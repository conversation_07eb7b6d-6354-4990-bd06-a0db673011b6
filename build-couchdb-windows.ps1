# PowerShell script to build CouchDB from source on Windows
# ⚠️ Some steps may require manual intervention (e.g., MSYS2 shell, Visual Studio Build Tools)

$SRC_DIR = "C:\Users\<USER>\Desktop\rest\shop\here\apache-couchdb-3.5.0"
$DEST_DIR = "C:\Users\<USER>\Desktop\rest\shop\resources\couchdb-windows"

Write-Host "🚀 Starting CouchDB build from source at $SRC_DIR"

# 1️⃣ Install Chocolatey if missing
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "🔧 Installing Chocolatey..."
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
}

# 2️⃣ Install dependencies
Write-Host "📦 Installing dependencies..."
choco install -y git erlang python openssl autoconf automake libtool pkgconfig help2man make visualstudio2019buildtools msys2

# 3️⃣ Install SpiderMonkey (manual step if not available via choco)
Write-Host "⚠️ SpiderMonkey is not available via Chocolatey. Please download and build SpiderMonkey manually if required by your CouchDB version."

# 4️⃣ Set up MSYS2 shell for Unix tools
Write-Host "⚠️ Please open an MSYS2 shell (from Start Menu) and run the following commands manually:"
Write-Host "cd $SRC_DIR"
Write-Host "./configure"
Write-Host "make release"
Write-Host "After build, copy rel/couchdb to $DEST_DIR"

# 5️⃣ Copy built binaries (if build succeeded)
if (Test-Path "$SRC_DIR\rel\couchdb") {
    Write-Host "📦 Copying built CouchDB to $DEST_DIR..."
    Remove-Item -Recurse -Force $DEST_DIR -ErrorAction SilentlyContinue
    New-Item -ItemType Directory -Force -Path $DEST_DIR | Out-Null
    Copy-Item -Recurse -Force "$SRC_DIR\rel\couchdb\*" $DEST_DIR
    Write-Host "🎉 CouchDB build complete! Binaries are in $DEST_DIR"
} else {
    Write-Host "❌ Build output not found. Please check the build steps above."
} 