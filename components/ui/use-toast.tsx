'use client';

import { useState, useEffect, createContext, useContext } from "react"

type ToastProps = {
  title?: string
  description?: string
  variant?: "default" | "destructive"
  duration?: number
}

type ToastContextType = {
  toast: (props: ToastProps) => void
  dismiss: (id?: string) => void
  toasts: Array<ToastProps & { id: string }>
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Array<ToastProps & { id: string }>>([])

  const toast = (props: ToastProps) => {
    const id = Math.random().toString(36).substring(2, 9)
    setToasts((prev) => [...prev, { ...props, id }])

    // Auto dismiss after duration
    if (props.duration !== 0) {
      setTimeout(() => {
        dismiss(id)
      }, props.duration || 5000)
    }
  }

  const dismiss = (id?: string) => {
    if (id) {
      setToasts((prev) => prev.filter((toast) => toast.id !== id))
    } else {
      setToasts([])
    }
  }

  // Clean up toasts when component unmounts
  useEffect(() => {
    return () => {
      setToasts([])
    }
  }, [])

  return (
    <ToastContext.Provider value={{ toast, dismiss, toasts }}>
      {children}
      {toasts.length > 0 && (
        <div className="fixed bottom-0 right-0 z-50 flex flex-col gap-2 p-4 max-w-md">
          {toasts.map((toast) => (
            <div
              key={toast.id}
              className={`rounded-md border p-4 shadow-md transition-all ${
                toast.variant === "destructive"
                  ? "bg-destructive text-destructive-foreground border-destructive"
                  : "bg-background border-border"
              }`}
              role="alert"
            >
              {toast.title && (
                <div className="font-medium">{toast.title}</div>
              )}
              {toast.description && (
                <div className="text-sm mt-1">{toast.description}</div>
              )}
              <button
                onClick={() => dismiss(toast.id)}
                className="absolute top-2 right-2 text-sm opacity-70 hover:opacity-100"
                aria-label="Close"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
    </ToastContext.Provider>
  )
}

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
} 