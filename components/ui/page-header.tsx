import React from "react";

interface PageHeaderProps {
  heading: string;
  description?: string;
  icon?: React.ReactNode;
}

export const PageHeader: React.FC<PageHeaderProps> = ({ heading, description, icon }) => {
  return (
    <div className="flex items-center space-x-4 mb-4">
      {icon && <div className="text-primary">{icon}</div>}
      <div>
        <h1 className="text-2xl font-bold leading-tight">{heading}</h1>
        {description && <p className="text-muted-foreground text-sm mt-1">{description}</p>}
      </div>
    </div>
  );
};
