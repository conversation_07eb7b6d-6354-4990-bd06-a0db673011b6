'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
// import { parseQRString, connectUsingMetadata } from '@/lib/network/offline-connection';
// import { useLan } from '@/lib/context/lan-context';
import { Camera, CheckCircle2, QrCode, RefreshCw, Smartphone, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface OfflineConnectionScannerProps {
  className?: string;
  onSuccess?: () => void;
  onClose?: () => void;
}

enum ScannerState {
  READY = 'ready',
  SCANNING = 'scanning',
  SUCCESS = 'success',
  ERROR = 'error',
}

export function OfflineConnectionScanner({ className = '', onSuccess, onClose }: OfflineConnectionScannerProps) {
  const [state, setState] = useState<ScannerState>(ScannerState.READY);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [manualInputValue, setManualInputValue] = useState<string>('');
  const [connecting, setConnecting] = useState(false);
  // const { refreshNetwork } = useLan();
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Clean up resources when component unmounts
  useEffect(() => {
    return () => {
      stopScanner();
    };
  }, []);

  const startScanner = async () => {
    try {
      setState(ScannerState.SCANNING);
      setErrorMessage('');
      
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported in this browser');
      }

      const constraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        
        // Start scanning the video for QR codes
        startScanning();
      }
    } catch (error) {
      console.error('Error starting scanner:', error);
      setState(ScannerState.ERROR);
      setErrorMessage((error as Error).message || 'Failed to access camera');
    }
  };

  const stopScanner = () => {
    if (streamRef.current) {
      const tracks = streamRef.current.getTracks();
      tracks.forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const startScanning = () => {
    // Note: This is a placeholder for a real QR scanning implementation.
    // In a real app, you would use a library like jsQR, zxing-js, or a React QR scanner component.
    // Since we can't add new dependencies here, we'll just simulate QR scanning with manual input.
    
    console.log('QR scanner activated - in a real app, this would scan the video stream');
  };

  const handleManualInput = () => {
    if (!manualInputValue) return;
    
    try {
      setConnecting(true);
      // const metadata = parseQRString(manualInputValue);
      
      // connectUsingMetadata(metadata).then(success => {
      //   if (success) {
      //     setState(ScannerState.SUCCESS);
      //     refreshNetwork();
      //     if (onSuccess) {
      //       setTimeout(onSuccess, 1500);
      //     }
      //   } else {
      //     setState(ScannerState.ERROR);
      //     setErrorMessage('Failed to connect to peer device');
      //   }
      // }).catch(error => {
      //   setState(ScannerState.ERROR);
      //   setErrorMessage((error as Error).message || 'Connection error');
      // }).finally(() => {
      //   setConnecting(false);
      // });
    } catch (error) {
      console.error('Error processing QR data:', error);
      setState(ScannerState.ERROR);
      setErrorMessage((error as Error).message || 'Failed to process QR code');
      setConnecting(false);
    }
  };

  const resetScanner = () => {
    stopScanner();
    setState(ScannerState.READY);
    setErrorMessage('');
    setManualInputValue('');
  };

  return (
    <Card className={`w-full max-w-md mx-auto overflow-hidden ${className}`}>
      <CardHeader className="space-y-1">
        <CardTitle className="text-xl flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          Scan Device QR Code
        </CardTitle>
        <CardDescription>
          Scan a QR code from another device to connect on LAN
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {state === ScannerState.READY && (
          <div className="flex flex-col items-center justify-center gap-4">
            <Button 
              onClick={startScanner} 
              className="w-full"
            >
              <Camera className="h-4 w-4 mr-2" />
              Open Camera
            </Button>

            <div className="relative w-full flex flex-col gap-2 mt-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Or paste connection data:</span>
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Paste QR code data here..."
                  value={manualInputValue}
                  onChange={(e) => setManualInputValue(e.target.value)}
                  className="flex-1"
                />
                <Button 
                  onClick={handleManualInput} 
                  variant="outline"
                  disabled={!manualInputValue}
                >
                  Connect
                </Button>
              </div>
            </div>
          </div>
        )}

        {state === ScannerState.SCANNING && (
          <div className="overflow-hidden rounded-lg h-[300px] relative">
            <video 
              ref={videoRef} 
              className="w-full h-full object-cover"
              autoPlay 
              playsInline 
              muted
            ></video>
            <div className="absolute inset-0 pointer-events-none border-4 border-dashed border-primary/50 rounded-lg"></div>
          </div>
        )}

        {state === ScannerState.SUCCESS && (
          <Alert variant="default" className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-900">
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle>Connection Successful</AlertTitle>
            <AlertDescription>
              Successfully connected to peer device. P2P sync will start automatically.
            </AlertDescription>
          </Alert>
        )}

        {state === ScannerState.ERROR && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertTitle>Connection Failed</AlertTitle>
            <AlertDescription>
              {errorMessage || 'Failed to connect to peer device. Please try again.'}
            </AlertDescription>
          </Alert>
        )}

        {connecting && (
          <div className="flex items-center justify-center gap-2 text-primary">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Connecting to peer device...</span>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex gap-2">
        {state !== ScannerState.READY && (
          <Button 
            onClick={resetScanner} 
            variant="outline"
            className="flex-1"
          >
            Reset
          </Button>
        )}
        <Button 
          onClick={onClose} 
          variant="ghost"
          className="flex-1"
        >
          Close
        </Button>
      </CardFooter>
    </Card>
  );
} 