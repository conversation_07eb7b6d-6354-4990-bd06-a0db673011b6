"use client";

import { useState } from "react";
import { useSyncService } from "@/lib/hooks/useSyncService";
import { Button } from "./button";
// import { restaurantDb } from "@/lib/db/v3";
import { Badge } from "./badge";

interface SyncStatusProps {
  showControls?: boolean;
}

export function SyncStatus({ showControls = true }: SyncStatusProps) {
  const { syncState, refreshSync, forceSyncOnce, forceFullPush, testAndRepairConnection } = useSyncService();
  const [repairing, setRepairing] = useState(false);
  const [result, setResult] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'synced':
        return 'bg-green-500/10 text-green-500 border-green-200';
      case 'connected':
        return syncState.pendingChanges
          ? 'bg-blue-500/10 text-blue-500 border-blue-200'
          : 'bg-green-500/10 text-green-500 border-green-200';
      case 'connecting':
        return 'bg-blue-500/10 text-blue-500 border-blue-200';
      case 'paused':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-200';
      case 'error':
        return 'bg-red-500/10 text-red-500 border-red-200';
      case 'disconnected':
        return 'bg-red-500/10 text-red-500 border-red-200';
      case 'lan_synced':
        return 'bg-purple-500/10 text-purple-500 border-purple-200';
      case 'lan_connected':
        return syncState.pendingChanges
          ? 'bg-purple-500/10 text-purple-500 border-purple-200'
          : 'bg-purple-500/10 text-purple-500 border-purple-200';
      case 'lan_paused':
        return 'bg-amber-500/10 text-amber-500 border-amber-200';
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'synced':
        return 'Synced';
      case 'connected':
        return syncState.pendingChanges ? 'Syncing...' : 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'paused':
        return syncState.pendingChanges ? 'Paused (pending)' : 'Paused';
      case 'error':
        return 'Error';
      case 'disconnected':
        return 'Offline';
      case 'lan_synced':
        return 'LAN Synced';
      case 'lan_connected':
        return syncState.pendingChanges ? 'LAN Syncing...' : 'LAN Connected';
      case 'lan_paused':
        return syncState.pendingChanges ? 'LAN Paused (pending)' : 'LAN Paused';
      default:
        return status;
    }
  };

  const handleRefresh = async () => {
    try {
      await refreshSync();
      setResult("Sync refresh triggered");
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleForceSyncOnce = async () => {
    try {
      const result = await forceSyncOnce();
      setResult(result.message);
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleForceFullPush = async () => {
    try {
      const result = await forceFullPush();
      setResult(result.message);
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleRepairConnection = async () => {
    setRepairing(true);
    setResult(null);

    try {
      const result = await testAndRepairConnection();
      setResult(result.message);
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setRepairing(false);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <Badge variant="outline" className={getStatusColor(syncState.status)}>
          Sync: {getStatusText(syncState.status)}
        </Badge>
        {syncState.pendingChanges ? (
          <Badge variant="outline" className="bg-blue-500/10 text-blue-500 border-blue-200">
            {syncState.pendingChanges} pending
          </Badge>
        ) : null}
      </div>

      {syncState.error && (
        <div className="text-xs text-red-500 mt-1">
          {syncState.error.message || "Unknown error"}
        </div>
      )}

      {result && (
        <div className="text-xs text-blue-500 mt-1">
          {result}
        </div>
      )}

      {showControls && (
        <div className="flex flex-wrap gap-2 mt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleRefresh}
            disabled={repairing}
          >
            Refresh
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleForceSyncOnce}
            disabled={repairing}
          >
            Sync Once
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleForceFullPush}
            disabled={repairing}
          >
            Full Push
          </Button>
          <Button
            size="sm"
            variant={syncState.status === 'error' ? 'destructive' : 'outline'}
            onClick={handleRepairConnection}
            disabled={repairing}
          >
            {repairing ? "Repairing..." : "Repair Connection"}
          </Button>
        </div>
      )}
    </div>
  );
}