"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Menu, X } from "lucide-react"
import { cn } from "@/lib/utils"

export default function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <>
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
        className={cn(
          "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
          isScrolled ? "bg-[#0a0807]/90 backdrop-blur-md py-3 shadow-lg shadow-amber-900/10" : "bg-transparent py-5",
        )}
      >
        <div className="container mx-auto px-4 flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 mr-2 relative flex items-center justify-center">
              <svg viewBox="0 0 24 24" fill="none" className="w-full h-full text-amber-400">
                <path
                  d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M19.5 12C19.5 16.1421 16.1421 19.5 12 19.5C7.85786 19.5 4.5 16.1421 4.5 12C4.5 7.85786 7.85786 4.5 12 4.5C16.1421 4.5 19.5 7.85786 19.5 12Z"
                  stroke="currentColor"
                  strokeWidth="2"
                />
                <path d="M12 2V4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                <path d="M12 20V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                <path d="M4 12L2 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                <path d="M22 12L20 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              </svg>
            </div>
            <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-red-500">
              TableTech
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse" dir="rtl" lang="ar">
            <a href="#features" className="text-white/70 hover:text-white transition-colors">المميزات</a>
            <a href="#how-it-works" className="text-white/70 hover:text-white transition-colors">كيف يعمل</a>
            <a href="#testimonials" className="text-white/70 hover:text-white transition-colors">آراء العملاء</a>
            <a href="#pricing" className="text-white/70 hover:text-white transition-colors">الأسعار</a>
            <a href="#faq" className="text-white/70 hover:text-white transition-colors">الأسئلة الشائعة</a>
          </nav>

          <div className="hidden md:flex items-center space-x-4 rtl:space-x-reverse" dir="rtl">
            <a href="/auth?page=signin" className="px-4 py-2 text-white/80 hover:text-white transition-colors">تسجيل الدخول</a>
            <a href="/auth?page=register" className="px-4 py-2 rounded-lg bg-gradient-to-r from-amber-500 to-red-600 text-white font-medium hover:opacity-90 transition-opacity">ابدأ الآن</a>
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden text-white" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </motion.header>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="fixed top-[60px] left-0 right-0 bg-[#0a0807]/95 backdrop-blur-lg z-40 border-t border-white/10 md:hidden"
        >
          <div className="container mx-auto px-4 py-4 flex flex-col space-y-4" dir="rtl">
            <a href="#features" className="text-white/70 hover:text-white transition-colors py-2" onClick={() => setMobileMenuOpen(false)}>المميزات</a>
            <a href="#how-it-works" className="text-white/70 hover:text-white transition-colors py-2" onClick={() => setMobileMenuOpen(false)}>كيف يعمل</a>
            <a href="#testimonials" className="text-white/70 hover:text-white transition-colors py-2" onClick={() => setMobileMenuOpen(false)}>آراء العملاء</a>
            <a href="#pricing" className="text-white/70 hover:text-white transition-colors py-2" onClick={() => setMobileMenuOpen(false)}>الأسعار</a>
            <a href="#faq" className="text-white/70 hover:text-white transition-colors py-2" onClick={() => setMobileMenuOpen(false)}>الأسئلة الشائعة</a>
            <div className="flex flex-col space-y-2 pt-2 border-t border-white/10">
              <a href="/auth?page=signin" className="px-4 py-2 text-white/80 hover:text-white transition-colors">تسجيل الدخول</a>
              <a href="/auth?page=register" className="px-4 py-2 rounded-lg bg-gradient-to-r from-amber-500 to-red-600 text-white font-medium hover:opacity-90 transition-opacity">ابدأ الآن</a>
            </div>
          </div>
        </motion.div>
      )}
    </>
  )
}
