"use client"

import { motion } from "framer-motion"
import { Playfair_Display } from "next/font/google"
import { cn } from "@/lib/utils"

const playfair = Playfair_Display({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-playfair",
})

export default function SectionHeading({
  title,
  subtitle,
  center = true,
  className,
}: {
  title: string
  subtitle: string
  center?: boolean
  className?: string
}) {
  return (
    <div className={cn("mb-12 md:mb-16", center && "text-center", className)}>
      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
        className={cn(
          "text-3xl md:text-4xl lg:text-5xl font-bold mb-4",
          "bg-clip-text text-transparent bg-gradient-to-r from-amber-300 via-red-400 to-orange-300",
          playfair.className,
        )}
      >
        {title}
      </motion.h2>
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="text-lg text-white/50 max-w-2xl mx-auto"
      >
        {subtitle}
      </motion.p>
    </div>
  )
}
