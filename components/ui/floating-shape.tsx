"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { 
  UtensilsCrossed, 
  Coffee, 
  ChefHat, 
  Wine, 
  Soup, 
  Pizza, 
  Sandwich,
  Cake,
  Croissant,
  Apple,
  Beef,
  Utensils
} from "lucide-react"

// Restaurant icon components with consistent styling
const IconWrapper = ({ children }: { children: React.ReactNode }) => (
  <div className="absolute inset-0 flex items-center justify-center opacity-40">
    {children}
  </div>
)

export default function FloatingShape({
  className,
  delay = 0,
  width = 400,
  height = 100,
  rotate = 0,
  gradient = "from-white/[0.08]",
  icon = "utensils",
}: {
  className?: string
  delay?: number
  width?: number
  height?: number
  rotate?: number
  gradient?: string
  icon?: "utensils" | "utensilsCrossed" | "coffee" | "chef" | "wine" | "soup" | "pizza" | "sandwich" | "cake" | "croissant" | "apple" | "beef"
}) {
  // Get the icon component based on the icon prop
  const getIcon = () => {
    switch (icon) {
      case "utensilsCrossed": return <UtensilsCrossed />
      case "coffee": return <Coffee />
      case "chef": return <ChefHat />
      case "wine": return <Wine />
      case "soup": return <Soup />
      case "pizza": return <Pizza />
      case "sandwich": return <Sandwich />
      case "cake": return <Cake />
      case "croissant": return <Croissant />
      case "apple": return <Apple />
      case "beef": return <Beef />
      default: return <Utensils />
    }
  }

  return (
    <motion.div
      initial={{
        opacity: 0,
        y: -150,
        rotate: rotate - 15,
      }}
      animate={{
        opacity: 1,
        y: 0,
        rotate: rotate,
      }}
      transition={{
        duration: 2.4,
        delay,
        ease: [0.23, 0.86, 0.39, 0.96],
        opacity: { duration: 1.2 },
      }}
      className={cn("absolute", className)}
    >
      <motion.div
        animate={{
          y: [0, 15, 0],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
        style={{
          width,
          height,
        }}
        className="relative flex items-center justify-center"
      >
        <div className={cn(
          "text-white/70", 
          "drop-shadow-[0_0_8px_rgba(255,255,255,0.2)]"
        )}>
          {getIcon()}
        </div>
      </motion.div>
    </motion.div>
  )
}
