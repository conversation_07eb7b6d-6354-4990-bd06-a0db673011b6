'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
// import { OfflineConnectionQR } from './offline-connection-qr';
import { OfflineConnectionScanner } from './offline-connection-scanner';
import { Smartphone, Wifi, QrCode } from 'lucide-react';

interface OfflineConnectionDialogProps {
  trigger?: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
}

export function OfflineConnectionDialog({ 
  trigger, 
  defaultOpen = false,
  className = '' 
}: OfflineConnectionDialogProps) {
  const [open, setOpen] = useState(defaultOpen);

  const handleSuccess = () => {
    // Close after successful connection with a small delay
    setTimeout(() => setOpen(false), 2000);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger ? (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      ) : (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Wifi className="h-4 w-4" />
            <span>Connect Offline</span>
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className={`sm:max-w-md max-h-[90vh] overflow-auto ${className}`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Offline Device Connection
          </DialogTitle>
          <DialogDescription>
            Connect devices directly on LAN without internet
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="generate" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              <span>Show QR Code</span>
            </TabsTrigger>
            <TabsTrigger value="scan" className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <span>Scan QR Code</span>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="generate" className="mt-4">
            {/* <OfflineConnectionQR /> */}
          </TabsContent>
          <TabsContent value="scan" className="mt-4">
            <OfflineConnectionScanner 
              onSuccess={handleSuccess}
              onClose={() => setOpen(false)}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
} 