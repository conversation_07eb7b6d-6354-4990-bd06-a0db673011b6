'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Users,
  UserPlus,
  LogOut,
  Trash2,
  Crown,
  Shield,
  User,
  Wifi,
  WifiOff,
  ChevronDown,
  Settings,
  RefreshCw
} from 'lucide-react';
import { useMultiUserAuth } from '@/lib/hooks/use-multi-user-auth';
import { UserSession } from '@/lib/auth/multi-user-session-manager';
import { toast } from 'sonner';

interface MultiUserSwitcherProps {
  className?: string;
  showStats?: boolean;
}

export function MultiUserSwitcher({ className, showStats = false }: MultiUserSwitcherProps) {
  const {
    currentUser,
    availableUsers,
    isOfflineMode,
    switchToUser,
    addUserSession,
    removeUserSession,
    logout,
    logoutAll,
    getSessionStats,
    loading
  } = useMultiUserAuth();

  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [addUserForm, setAddUserForm] = useState({
    identifier: '',
    password: '',
    isStaffLogin: false
  });
  const [isAddingUser, setIsAddingUser] = useState(false);

  // Get session statistics
  const stats = getSessionStats();

  // Helper functions
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'admin':
        return <Shield className="h-3 w-3 text-blue-500" />;
      case 'manager':
        return <Shield className="h-3 w-3 text-green-500" />;
      default:
        return <User className="h-3 w-3 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'manager':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Handle user switching
  const handleSwitchUser = async (userId: string) => {
    const success = switchToUser(userId);
    if (success) {
      const user = availableUsers.find(u => u.user.id === userId);
      toast.success(`🔄 Switched to ${user?.user.name || 'user'}`);
    } else {
      toast.error('❌ Failed to switch user');
    }
  };

  // Handle adding new user
  const handleAddUser = async () => {
    if (!addUserForm.identifier || !addUserForm.password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsAddingUser(true);
    try {
      const success = await addUserSession({
        identifier: addUserForm.identifier,
        password: addUserForm.password,
        isStaffLogin: addUserForm.isStaffLogin
      });

      if (success) {
        toast.success('✅ User added successfully!');
        setIsAddUserOpen(false);
        setAddUserForm({ identifier: '', password: '', isStaffLogin: false });
      } else {
        toast.error('❌ Failed to add user. Check credentials.');
      }
    } catch (error) {
      toast.error('❌ Error adding user');
    } finally {
      setIsAddingUser(false);
    }
  };

  // Handle removing user
  const handleRemoveUser = (userId: string, userName: string) => {
    removeUserSession(userId);
    toast.success(`🗑️ Removed ${userName} from device`);
  };

  // Handle logout current user
  const handleLogout = () => {
    logout();
    toast.success('👋 Logged out current user');
  };

  // Handle logout all users
  const handleLogoutAll = () => {
    logoutAll();
    toast.success('🧹 Logged out all users');
  };

  if (loading) {
    return (
      <Button variant="ghost" disabled className={className}>
        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
        Loading...
      </Button>
    );
  }

  if (!currentUser) {
    return null;
  }

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex items-center gap-2 h-auto p-2 hover:bg-accent"
          >
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs font-medium">
                  {getInitials(currentUser.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-start">
                <span className="text-sm font-medium">{currentUser.name}</span>
                <div className="flex items-center gap-1">
                  {getRoleIcon(currentUser.role)}
                  <span className="text-xs text-muted-foreground capitalize">
                    {currentUser.role}
                  </span>
                  {isOfflineMode ? (
                    <WifiOff className="h-3 w-3 text-orange-500" />
                  ) : (
                    <Wifi className="h-3 w-3 text-green-500" />
                  )}
                </div>
              </div>
            </div>
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-80">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span>👥 User Accounts ({availableUsers.length})</span>
            {isOfflineMode && (
              <Badge variant="outline" className="text-orange-600 border-orange-600">
                Offline
              </Badge>
            )}
          </DropdownMenuLabel>

          {showStats && stats.totalSessions > 1 && (
            <>
              <DropdownMenuSeparator />
              <div className="px-2 py-1 text-xs text-muted-foreground">
                📊 {stats.onlineSessions} online • {stats.offlineSessions} offline
              </div>
            </>
          )}

          <DropdownMenuSeparator />

          {/* Current User */}
          <DropdownMenuGroup>
            <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
              Current User
            </div>
            <DropdownMenuItem className="flex items-center gap-3 p-3 bg-accent/50">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs">
                  {getInitials(currentUser.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="font-medium">{currentUser.name}</div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  {getRoleIcon(currentUser.role)}
                  <span className="capitalize">{currentUser.role}</span>
                  {currentUser.email && (
                    <span>• {currentUser.email}</span>
                  )}
                </div>
              </div>
              <Badge className={getRoleBadgeColor(currentUser.role)}>
                Active
              </Badge>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          {/* Other Available Users */}
          {availableUsers.filter(session => session.user.id !== currentUser.id).length > 0 && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                  Switch to User
                </div>
                {availableUsers
                  .filter(session => session.user.id !== currentUser.id)
                  .map((session) => (
                    <DropdownMenuItem
                      key={session.id}
                      className="flex items-center gap-3 p-3 cursor-pointer"
                      onClick={() => handleSwitchUser(session.user.id)}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-xs">
                          {getInitials(session.user.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">{session.user.name}</div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {getRoleIcon(session.user.role)}
                          <span className="capitalize">{session.user.role}</span>
                          {session.user.email && (
                            <span>• {session.user.email}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {session.isOnline ? (
                          <Wifi className="h-3 w-3 text-green-500" />
                        ) : (
                          <WifiOff className="h-3 w-3 text-orange-500" />
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveUser(session.user.id, session.user.name);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuGroup>
            </>
          )}

          <DropdownMenuSeparator />

          {/* Actions */}
          <DropdownMenuGroup>
            <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
              <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add User Account
                </DropdownMenuItem>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>➕ Add User Account</DialogTitle>
                  <DialogDescription>
                    Add another user to this device. They will share the same restaurant database.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="identifier">Email/Username</Label>
                    <Input
                      id="identifier"
                      placeholder="Enter email or username"
                      value={addUserForm.identifier}
                      onChange={(e) => setAddUserForm(prev => ({ ...prev, identifier: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter password"
                      value={addUserForm.password}
                      onChange={(e) => setAddUserForm(prev => ({ ...prev, password: e.target.value }))}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isStaffLogin"
                      checked={addUserForm.isStaffLogin}
                      onChange={(e) => setAddUserForm(prev => ({ ...prev, isStaffLogin: e.target.checked }))}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="isStaffLogin" className="text-sm">
                      Staff login (use username instead of email)
                    </Label>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsAddUserOpen(false)}
                    disabled={isAddingUser}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddUser}
                    disabled={isAddingUser}
                  >
                    {isAddingUser ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Adding...
                      </>
                    ) : (
                      <>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Add User
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout Current User
            </DropdownMenuItem>

            {availableUsers.length > 1 && (
              <DropdownMenuItem
                onClick={handleLogoutAll}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Logout All Users
              </DropdownMenuItem>
            )}
          </DropdownMenuGroup>

          {/* Restaurant Info */}
          <DropdownMenuSeparator />
          <div className="px-2 py-2 text-xs text-muted-foreground">
            🏪 Restaurant: {currentUser.restaurantId}
            {isOfflineMode && (
              <div className="flex items-center gap-1 mt-1">
                <WifiOff className="h-3 w-3" />
                <span>Offline Mode Active</span>
              </div>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
} 