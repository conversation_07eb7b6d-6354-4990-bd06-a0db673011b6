'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { User } from 'lucide-react';
import { UserSwitchDialog } from './UserSwitchDialog';

interface SwitchUserButtonProps {
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

export function SwitchUserButton({ 
  variant = 'ghost', 
  size = 'default', 
  className,
  children 
}: SwitchUserButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsDialogOpen(true)}
      >
        {children || (
          <>
            <User className="h-4 w-4 mr-2" />
            Switch User
          </>
        )}
      </Button>

      <UserSwitchDialog 
        open={isDialogOpen} 
        onOpenChange={setIsDialogOpen} 
      />
    </>
  );
} 