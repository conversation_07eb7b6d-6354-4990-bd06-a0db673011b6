# 🔄 Multi-User Switching System

A secure and seamless user switching system for the restaurant management app that allows multiple users to be logged in on the same device with proper authentication.

## 🌟 Features

- **🔐 Secure Password Verification**: Requires password confirmation when switching to online users
- **📱 Offline Mode Support**: Seamless switching for offline users without password requirement
- **🎨 Clean UI/UX**: Minimalistic and space-efficient design using shadcn/ui components
- **🔄 Seamless Flow**: Handles cases when no other users are available by redirecting to auth page
- **👥 Multi-User Management**: View all available users with their roles and online status
- **🏪 Restaurant Database Sharing**: All users share the same restaurant database
- **✨ Smart Add User Flow**: Prevents refresh loops when adding new users to the device

## 📁 Components

### `UserSwitchDialog.tsx`
The main dialog component that handles the user switching flow:
- **User Selection**: Shows available users with their roles and status
- **Password Verification**: Requires password for online users
- **Add New User**: Smart redirect to auth page with context preservation
- **Offline Support**: Direct switching for offline users
- **Context Management**: Uses sessionStorage to track add user operations

### `SwitchUserButton.tsx`
A simple button component that opens the UserSwitchDialog:
- **Customizable**: Supports different variants, sizes, and custom children
- **Easy Integration**: Can be dropped into any dropdown menu or navigation

### `MultiUserSwitcher.tsx`
Enhanced dropdown component for advanced multi-user management:
- **Current User Display**: Shows active user with role and online status
- **Quick Switching**: Direct switching between users (existing functionality)
- **User Management**: Add/remove users, logout options
- **Session Statistics**: Shows online/offline user counts

## 🚀 Usage

### Basic Switch User Button
```tsx
import { SwitchUserButton } from '@/components/multi-user';

// Simple usage
<SwitchUserButton />

// Custom styling
<SwitchUserButton 
  variant="outline" 
  size="sm"
  className="w-full"
>
  <User className="h-4 w-4 mr-2" />
  Change User
</SwitchUserButton>
```

### In Dropdown Menus
```tsx
import { SwitchUserButton } from '@/components/multi-user';

<DropdownMenuItem onSelect={(e) => e.preventDefault()}>
  <SwitchUserButton 
    variant="ghost" 
    className="w-full justify-start p-0 h-auto font-normal"
  />
</DropdownMenuItem>
```

### Advanced Multi-User Management
```tsx
import { MultiUserSwitcher } from '@/components/multi-user';

<MultiUserSwitcher 
  className="w-full" 
  showStats={true}
/>
```

## 🔒 Security Features

1. **Password Verification**: Online users require password confirmation
2. **Offline Mode**: Offline users can switch without password (secure for local-only data)
3. **Session Management**: Proper token handling and session cleanup
4. **Role-Based Access**: Maintains user roles and permissions after switching

## 🎯 User Flow

### When Other Users Exist:
1. Click "Switch User" button
2. Select user from list
3. Enter password (if user is online)
4. Successfully switch to selected user

### When No Other Users Exist:
1. Click "Switch User" button
2. See "No other users available" message
3. Click "Add New User" to go to auth page with special context
4. Login with new credentials
5. System detects new user and redirects to main app
6. New user is now available for switching

## 🔧 Add User Flow Implementation

The system uses a sophisticated approach to prevent refresh loops when adding new users:

### Context Preservation
- When "Add New User" is clicked, the system stores context in `sessionStorage`
- Context includes: action type, current user ID, and timestamp
- Auth page receives `mode=add_user` parameter

### Smart Redirect Logic
- Auth page checks for `mode=add_user` parameter
- If authenticated user is the same as context user, stays on auth page
- If authenticated user is different (new user), redirects to main app
- Clears context after successful new user addition

### Visual Feedback
- Auth page shows "Adding New User to Device" indicator
- Toast notifications inform user of the process
- Loading states provide clear feedback

## 🔧 Integration

The system is integrated into:
- **`nav-user.tsx`**: Sidebar user navigation
- **`app/components/UserSwitcher.tsx`**: Main user switcher component
- **`main-nav.tsx`**: Top navigation bar
- **`app/auth/page.tsx`**: Enhanced with add user mode detection

## 📱 Responsive Design

- **Mobile-First**: Works seamlessly on mobile devices
- **Compact Design**: Space-efficient for small screens
- **Touch-Friendly**: Large touch targets for mobile interaction

## 🎨 Design Principles

- **Minimalistic**: Clean and uncluttered interface
- **Consistent**: Uses shadcn/ui components for consistency
- **Accessible**: Proper ARIA labels and keyboard navigation
- **Intuitive**: Clear visual hierarchy and user feedback

## 🔄 State Management

The system uses the existing multi-user authentication context:
- **`useMultiUserAuth`**: Hook for authentication state
- **`MultiUserSessionManager`**: Singleton for session management
- **Local Storage**: Persistent session storage
- **Session Storage**: Temporary context for add user flow
- **Cookies**: Middleware compatibility

## 🚨 Error Handling

- **Invalid Credentials**: Clear error messages for wrong passwords
- **Network Issues**: Graceful fallback to offline mode
- **Session Errors**: Automatic cleanup of invalid sessions
- **User Feedback**: Toast notifications for all actions
- **Refresh Loop Prevention**: Smart context management prevents infinite redirects

## 🐛 Troubleshooting

### Add User Button Causes Refresh
**Fixed**: The system now uses context preservation and smart redirect logic to prevent refresh loops.

### User Not Switching
- Check if password is correct for online users
- Verify user session exists in available users list
- Check console for authentication errors

### Offline Mode Issues
- Ensure offline mode is properly detected
- Check local storage for offline session data
- Verify offline user creation logic 