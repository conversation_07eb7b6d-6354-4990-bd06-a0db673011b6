"use client"

import React from "react"
import { type LucideIcon } from "lucide-react"
import Link from "next/link"
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation"
import { useMobileLayout } from "@/hooks/use-mobile-layout"
import { cn } from "@/lib/utils"

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

export type NavItem = {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
};

export function NavMain({
  items,
}: {
  items: NavItem[];
}) {
  const { navigate } = useStaticNavigation()

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault()
      const cleanPath = href.replace(/^\//, '')
      navigate(cleanPath)
    }
    // In dynamic mode, let Link handle it normally
  }

  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton 
              asChild 
              tooltip={item.title}
              isActive={item.isActive}
            >
              <Link
                href={item.url}
                onClick={(e) => handleNavClick(item.url, e)}
              >
                {item.icon && <item.icon />}
                <span>{item.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
