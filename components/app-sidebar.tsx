"use client"

import {
  BanknoteIcon,
  BarChart4Icon,
  ChefHat,
  ClipboardList,
  Home,
  Menu,
  Package,
  Package2,
  Settings,
  Truck,
  User,
  UserCog,
  Users,
  WifiOff,
  Crown,
  Server,
  Database,
  Network,
  Smartphone,
  TestTube,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar"

import { useAuth } from "@/lib/context/multi-user-auth-provider"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { useSettings } from '@/lib/context/settings-context'
import { isAdmin as checkIsAdmin } from "@/lib/auth/role-utils"
import { usePathname } from "next/navigation"
import { useMobileLayout, useTouchOptimization } from '@/hooks/use-mobile-layout'
import { useOSDetection } from '@/hooks/use-os-detection'
import { NavMain } from "./nav-main"
import { NavUser } from "./nav-user"
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { getCurrentRestaurantId as getCurrentRestaurantIdUtil } from '@/lib/db/v4/utils/restaurant-id';
import { NavItem } from './nav-main';
import { RefreshCw } from "lucide-react"; // Import RefreshCw for loading indicator
import { SimpleNetworkIndicator } from './simple-network-indicator';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const currentPathname = usePathname() || ""
  const { user } = useAuth()
  const { hasPageAccess, isOwner, isLoading } = usePermissions()
  const { isCogsEnabled } = useSettings()
  const { isMobile: isSidebarMobile } = useSidebar()
  const { isMobile: isMobileLayout } = useMobileLayout()
  const { isMobile: isMobileOS, platform, isCapacitor } = useOSDetection()
  
  // Use hybrid approach: OS detection for true mobile platforms, but fallback to screen size for browsers
  // Electron should always be treated as desktop regardless of platform detection
  const { isElectron } = useOSDetection()
  const isMobile = !isElectron && (isCapacitor || platform === 'ios' || platform === 'android' || (platform === 'unknown' && isMobileLayout))

  // Wait for permissions to be ready
  if (isLoading) {
    return (
      <Sidebar collapsible={isMobile ? "offcanvas" : "icon"} {...props}>
        <SidebarHeader />
        <SidebarContent>
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
          </div>
        </SidebarContent>
        <SidebarFooter />
        <SidebarRail />
      </Sidebar>
    );
  }

  // Use the utility functions directly with the user object
  const isAdmin = checkIsAdmin(user)

  // 👤 User data for NavUser component
  const userData = {
    name: user?.name || 'Unknown User',
    email: user?.email || '',
    avatar: '' // Required by NavUser component
  };

  // Create navigation items
  const createNavItems = () => {
    const items = []

    // Menu - Production
    if (hasPageAccess('menu')) {
      items.push({
        title: "Menu",
        url: "/menu",
        icon: Package,
        isActive: currentPathname === "/menu",
      })
    }

    // Orders - Production
    if (hasPageAccess('orders')) {
      items.push({
        title: "Orders",
        url: "/ordering",
        icon: ClipboardList,
        isActive: currentPathname === "/ordering",
      });
    }

    // Inventory - Production
    if (hasPageAccess('inventory')) {
      items.push({
        title: "Inventory",
        url: "/inventory",
        icon: Package2,
        isActive: currentPathname === "/inventory",
      })
    }

    // Suppliers - Production
    if (hasPageAccess('suppliers')) {
      items.push({
        title: "Suppliers",
        url: "/suppliers",
        icon: Truck,
        isActive: currentPathname === "/suppliers",
      })
    }

    // Staff - Production
    if (hasPageAccess('staff')) {
      items.push({
        title: "Staff",
        url: "/staff",
        icon: UserCog,
        isActive: currentPathname === "/staff",
      })
    }

    // Finance - Production
    if (hasPageAccess('finance')) {
      items.push({
        title: "Finance",
        url: "/finance",
        icon: BanknoteIcon,
        isActive: currentPathname === "/finance" || currentPathname.startsWith("/finance/"),
      })
    }

    // Analytics - Production
    if (hasPageAccess('analytics')) {
      items.push({
        title: "Analytics",
        url: "/analytics",
        icon: BarChart4Icon,
        isActive: currentPathname === "/analytics" || currentPathname.startsWith("/analytics/"),
      })
    }

    // Waiter - Production
    if (hasPageAccess('orders')) {
      items.push({
        title: "Waiter",
        url: "/waiter",
        icon: User,
        isActive: currentPathname === "/waiter",
      })
    }

    // Settings - Production
    if (hasPageAccess('settings')) {
      items.push({
        title: "Settings",
        url: "/settings",
        icon: Settings,
        isActive: currentPathname === "/settings",
      })
    }

    // Admin routes - Production
    if (isAdmin) {
      items.push({
        title: "Users",
        url: "/admin/users",
        icon: Users,
        isActive: currentPathname === "/admin/users",
      })
    }

    // Development routes - only in development mode
    if (process.env.NODE_ENV === 'development') {
      items.push({
        title: "Mobile Ordering",
        url: "/mobile-ordering",
        icon: Smartphone,
        isActive: currentPathname === "/mobile-ordering",
      })

      if (hasPageAccess('settings')) {
        items.push({
          title: "Offline Test",
          url: "/offline-test",
          icon: WifiOff,
          isActive: currentPathname === "/offline-test",
        })
      }

      items.push({
        title: "HTTP Sync Monitor",
        url: "/p2p-sync",
        icon: Network,
        isActive: currentPathname === "/p2p-sync",
      })
    }

    return items
  }

  const navItems = createNavItems();

  return (
    <Sidebar collapsible={isMobile ? "offcanvas" : "icon"} {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 px-4 py-2 group-data-[collapsible=icon]:hidden">
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <ChefHat className="size-4" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">Bistro</span>
              <span className="truncate text-xs">Restaurant Manager</span>
            </div>
          </div>
          <div className="flex items-center justify-center group-data-[collapsible=icon]:w-full group-data-[collapsible=icon]:px-0 px-4">
            <SidebarTrigger className="group-data-[collapsible=icon]:size-8 group-data-[collapsible=icon]:rounded-md group-data-[collapsible=icon]:hover:bg-sidebar-accent" />
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navItems} />
      </SidebarContent>
      <SidebarFooter>
        <div className="p-1">
          <SimpleNetworkIndicator />
        </div>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}