"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

export function CacheCleaner() {
  const [isClearing, setIsClearing] = useState(false);

  const clearCache = async () => {
    setIsClearing(true);
    try {
      // Get all database names from IndexedDB
      const databases = await indexedDB.databases();

      // Filter for PouchDB databases
      const pouchDBs = databases.filter(db =>
        db.name?.startsWith('menu_db_') ||
        db.name?.startsWith('restaurant_') ||
        db.name === 'menu_db'
      );

      if (pouchDBs.length === 0) {
        toast.info('No PouchDB databases found to clear');
        return;
      }

      // Delete each PouchDB database
      const deletePromises = pouchDBs.map(db => {
        return new Promise<void>((resolve, reject) => {
          if (!db.name) {
            resolve();
            return;
          }

          const request = indexedDB.deleteDatabase(db.name);

          request.onsuccess = () => {
            resolve();
          };

          request.onerror = () => {
            reject(new Error(`Failed to delete database: ${db.name}`));
          };
        });
      });

      // Wait for all deletions to complete
      await Promise.all(deletePromises);

      toast.success(`Cleared ${pouchDBs.length} PouchDB databases. Refreshing...`);

      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1500);

    } catch (error) {
      console.error('Error clearing cache:', error);
      toast.error('Failed to clear cache. See console for details.');
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <button
      onClick={clearCache}
      disabled={isClearing}
      className="w-full text-left text-sm flex items-center"
    >
      {isClearing ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Clearing...
        </>
      ) : (
        'Clear Cache'
      )}
    </button>
  );
}