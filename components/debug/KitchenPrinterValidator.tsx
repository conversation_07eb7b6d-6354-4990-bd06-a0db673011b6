"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw, 
  TestTube,
  Shield,
  Target,
  Zap
} from "lucide-react";
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { getMenu } from '@/lib/db/v4/operations/menu-ops';

interface ValidationResult {
  isValid: boolean;
  unassignedCategories: string[];
  assignmentReport: { categoryId: string; categoryName: string; printerId: string; printerName: string }[];
  warnings: string[];
}

interface RoutingResult {
  success: boolean;
  routingReport: { itemName: string; categoryId?: string; assignedPrinter?: string; error?: string }[];
  errors: string[];
}

interface SystemTestResult {
  system: string;
  success: boolean;
  printJobCount: number;
  error?: string;
  validationPassed: boolean;
  routingPassed: boolean;
}

export default function KitchenPrinterValidator() {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [routingResult, setRoutingResult] = useState<RoutingResult | null>(null);
  const [systemTests, setSystemTests] = useState<SystemTestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testOrder, setTestOrder] = useState<Order | null>(null);

  useEffect(() => {
    generateTestOrder();
  }, []);

  const generateTestOrder = async () => {
    try {
      const menu = await getMenu();
      const items = menu.categories?.slice(0, 4).map((category, index) => ({
        id: `test-item-${index}`,
        menuItemId: category.id,
        name: `Test ${category.name} Item ${index + 1}`,
        price: 1000 + (index * 300),
        quantity: Math.floor(Math.random() * 3) + 1,
        categoryId: category.id,
        notes: index % 2 === 0 ? `Special instructions for ${category.name}` : undefined
      })) || [];

      const orderId = `validation-test-${Date.now()}`;
      const order: Order = {
        _id: orderId,
        id: orderId,
        type: "order_document",
        schemaVersion: "v4.0",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tableId: "VALIDATION-TABLE",
        status: "pending",
        orderType: "dine-in",
        items,
        total: items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        notes: "🧪 Validation test order with diverse category items",
        paymentStatus: "pending",
        paymentMethod: "cash"
      };

      setTestOrder(order);
    } catch (error) {
      console.error('Error generating test order:', error);
    }
  };

  const runValidation = async () => {
    setIsLoading(true);
    try {
      const result = await kitchenPrintService.validateCategoryAssignments();
      setValidationResult(result);
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runRoutingTest = async () => {
    if (!testOrder) return;
    
    setIsLoading(true);
    try {
      const result = await kitchenPrintService.testOrderRouting(testOrder);
      setRoutingResult(result);
    } catch (error) {
      console.error('Routing test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runComprehensiveTest = async () => {
    if (!testOrder) return;

    setIsLoading(true);
    const results: SystemTestResult[] = [];

    try {
      // First validate assignments
      const validation = await kitchenPrintService.validateCategoryAssignments();
      const routing = await kitchenPrintService.testOrderRouting(testOrder);

      // Test each system
      for (const system of ['single', 'multi-station', 'multi-barcode'] as const) {
        try {
          kitchenPrintService.setSystem(system);
          const printResult = await kitchenPrintService.printKitchenOrder(testOrder, testOrder.tableId, { fontSize: 'medium' });
          
          results.push({
            system,
            success: printResult.success,
            printJobCount: printResult.printJobs?.length || (printResult.printJob ? 1 : 0),
            error: printResult.error,
            validationPassed: validation.isValid,
            routingPassed: routing.success
          });
        } catch (error) {
          results.push({
            system,
            success: false,
            printJobCount: 0,
            error: error instanceof Error ? error.message : 'Unknown error',
            validationPassed: validation.isValid,
            routingPassed: routing.success
          });
        }
      }

      setSystemTests(results);
      setValidationResult(validation);
      setRoutingResult(routing);
    } catch (error) {
      console.error('Comprehensive test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Kitchen Printer Validation Suite
        </h2>
        <div className="flex gap-2">
          <Button onClick={generateTestOrder} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            New Test Order
          </Button>
          <Button onClick={runComprehensiveTest} disabled={isLoading}>
            <TestTube className="h-4 w-4 mr-2" />
            Run Full Test
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Target className="h-4 w-4" />
              Category Validation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={runValidation} disabled={isLoading} className="w-full" size="sm">
              Validate Assignments
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Routing Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={runRoutingTest} disabled={isLoading || !testOrder} className="w-full" size="sm">
              Test Item Routing
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              System Tests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={runComprehensiveTest} disabled={isLoading || !testOrder} className="w-full" size="sm">
              Test All Systems
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Validation Results */}
      {validationResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(validationResult.isValid)}
              Category Assignment Validation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Badge className={getStatusColor(validationResult.isValid)}>
              {validationResult.isValid ? 'All Categories Assigned' : 'Issues Found'}
            </Badge>

            {validationResult.unassignedCategories.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Unassigned Categories:</strong> {validationResult.unassignedCategories.join(', ')}
                </AlertDescription>
              </Alert>
            )}

            {validationResult.warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Warnings:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {validationResult.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {validationResult.assignmentReport.map((assignment, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded text-sm">
                  <span>{assignment.categoryName}</span>
                  <Badge variant="outline">{assignment.printerName}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Routing Results */}
      {routingResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(routingResult.success)}
              Order Routing Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Badge className={getStatusColor(routingResult.success)}>
              {routingResult.success ? 'All Items Routed' : 'Routing Issues'}
            </Badge>

            {routingResult.errors.length > 0 && (
              <Alert>
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Routing Errors:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {routingResult.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              {routingResult.routingReport.map((report, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded text-sm">
                  <span>{report.itemName}</span>
                  {report.assignedPrinter ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      {report.assignedPrinter}
                    </Badge>
                  ) : (
                    <Badge className="bg-red-100 text-red-800 border-red-200">
                      No Printer
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* System Test Results */}
      {systemTests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>System Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {systemTests.map((test, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">{test.system.toUpperCase()}</h3>
                    {getStatusIcon(test.success)}
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <div>Print Jobs: {test.printJobCount}</div>
                    <div>Validation: {test.validationPassed ? '✅' : '❌'}</div>
                    <div>Routing: {test.routingPassed ? '✅' : '❌'}</div>
                  </div>
                  
                  {test.error && (
                    <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
                      {test.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
