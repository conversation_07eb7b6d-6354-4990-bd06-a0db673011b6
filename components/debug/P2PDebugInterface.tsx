'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Server,
  CheckCircle,
  XCircle,
  Search,
  Database,
  Network,
  Activity,
  AlertCircle,
  RefreshCw,
  WifiOff,
  Smartphone,
  Monitor,
  Wifi,
  Play,
  Pause
} from 'lucide-react';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';
import { useSync } from '@/lib/hooks/use-sync';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';

interface P2PDebugInterfaceProps {
  className?: string;
}

export function P2PDebugInterface({ className }: P2PDebugInterfaceProps) {
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  // Database status
  const [dbStatus, setDbStatus] = useState<{
    isReady: boolean;
    restaurantId: string | null;
    hasDatabase: boolean;
    dbName: string | null;
  }>({ isReady: false, restaurantId: null, hasDatabase: false, dbName: null });

  // Use the working sync hook
  const {
    servers,
    discovering,
    connected,
    syncing,
    status,
    currentServer,
    discover,
    connect,
    disconnect,
    error
  } = useSync();

  // Use autonomous sync hook
  const autonomousSync = useAutonomousSync();

  // Initialize platform detection
  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
  }, []);

  // Check database status
  useEffect(() => {
    const checkDatabase = async () => {
      try {
        const isReady = mainDbInstance.isInitialized;
        const restaurantId = mainDbInstance.getCurrentRestaurantId();
        const db = mainDbInstance.getDatabase();
        const hasDatabase = Boolean(db);

        let dbName = null;
        if (restaurantId) {
          const { getRestaurantDbName } = await import('@/lib/db/db-utils');
          dbName = getRestaurantDbName(restaurantId);
        }

        setDbStatus({
          isReady,
          restaurantId,
          hasDatabase,
          dbName
        });
      } catch (error) {
        console.error('Database status check failed:', error);
        setDbStatus({
          isReady: false,
          restaurantId: null,
          hasDatabase: false,
          dbName: null
        });
      }
    };

    checkDatabase();
    const interval = setInterval(checkDatabase, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleConnect = async (server: any) => {
    try {
      await connect(server);
    } catch (err) {
      console.error('Connection failed:', err);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (err) {
      console.error('Disconnect failed:', err);
    }
  };

  const getStatusIcon = (isConnected: boolean, isSyncing: boolean, hasError: boolean) => {
    if (hasError) return <XCircle className="h-4 w-4 text-red-500" />;
    if (isSyncing) return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
    if (isConnected) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <WifiOff className="h-4 w-4 text-gray-500" />;
  };

  const getStatusColor = (isConnected: boolean, isSyncing: boolean, hasError: boolean) => {
    if (hasError) return 'bg-red-100 text-red-800';
    if (isSyncing) return 'bg-blue-100 text-blue-800';
    if (isConnected) return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">P2P Sync Debug Interface</h2>
          <p className="text-muted-foreground">
            Native PouchDB ↔ CouchDB synchronization
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {isMobile && <Smartphone className="h-5 w-5 text-blue-500" />}
          {isDesktop && <Monitor className="h-5 w-5 text-purple-500" />}
          <Badge variant="outline">{platform}</Badge>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="autonomous">Autonomous</TabsTrigger>
          <TabsTrigger value="discovery">Discovery</TabsTrigger>
          <TabsTrigger value="sync">Sync Status</TabsTrigger>
          <TabsTrigger value="auth">Auth Debug</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Autonomous Sync Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Autonomous Sync Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${autonomousSync.isRunning ? 'text-green-600' : 'text-gray-400'}`}>
                    {autonomousSync.isRunning ? 'ON' : 'OFF'}
                  </div>
                  <div className="text-sm text-gray-600">Autonomous Mode</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {autonomousSync.discoveredServers}
                  </div>
                  <div className="text-sm text-gray-600">Auto-Discovered</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {autonomousSync.connectedServers}
                  </div>
                  <div className="text-sm text-gray-600">Auto-Connected</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">Last Discovery</div>
                  <div className="text-sm font-medium">
                    {autonomousSync.lastDiscovery
                      ? autonomousSync.lastDiscovery.toLocaleTimeString()
                      : 'Never'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Platform Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Platform</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  {isMobile ? <Smartphone className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
                  <span className="font-medium">{platform}</span>
                </div>
              </CardContent>
            </Card>

            {/* Database Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Database</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  {dbStatus.isReady ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="font-medium">
                    {dbStatus.isReady ? 'Ready' : 'Not Ready'}
                  </span>
                </div>
                {dbStatus.dbName && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {dbStatus.dbName}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Sync Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Sync Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  {getStatusIcon(connected, syncing, Boolean(error))}
                  <span className="font-medium">
                    {error ? 'Error' : syncing ? 'Syncing' : connected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                {currentServer && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {currentServer.url}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Sync Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {status.docsReceived}
                  </div>
                  <div className="text-sm text-gray-600">Docs Received</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {status.docsSent}
                  </div>
                  <div className="text-sm text-gray-600">Docs Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {servers.length}
                  </div>
                  <div className="text-sm text-gray-600">Servers Found</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">Last Sync</div>
                  <div className="text-sm font-medium">
                    {status.lastSync
                      ? new Date(status.lastSync).toLocaleTimeString()
                      : 'Never'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-red-600 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Sync Error
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-red-600 font-medium">{error}</p>

                  {/* Enhanced error troubleshooting */}
                  <div className="bg-red-50 p-3 rounded-lg">
                    <h4 className="font-medium text-red-800 mb-2">🔧 Troubleshooting Steps:</h4>
                    <div className="text-xs text-red-700 space-y-1">
                      {error.toLowerCase().includes('unauthorized') || error.toLowerCase().includes('authentication') ? (
                        <>
                          <p>• <strong>Authentication Issue:</strong> CouchDB credentials are incorrect</p>
                          <p>• <strong>Expected:</strong> Desktop CouchDB should use admin:admin credentials</p>
                          <p>• <strong>Check:</strong> Desktop CouchDB configuration in electron/src/p2p-sync.ts</p>
                          <p>• <strong>Fix:</strong> Restart desktop app to regenerate CouchDB config</p>
                        </>
                      ) : error.toLowerCase().includes('timeout') ? (
                        <>
                          <p>• <strong>Network Issue:</strong> Cannot reach CouchDB server</p>
                          <p>• <strong>Check:</strong> Desktop app is running and CouchDB is started</p>
                          <p>• <strong>Check:</strong> Both devices are on the same network</p>
                          <p>• <strong>Check:</strong> Firewall is not blocking port 5984</p>
                        </>
                      ) : error.toLowerCase().includes('not found') ? (
                        <>
                          <p>• <strong>Database Issue:</strong> Restaurant database not found</p>
                          <p>• <strong>Check:</strong> Same restaurant ID on both devices</p>
                          <p>• <strong>Check:</strong> Database was created on desktop first</p>
                        </>
                      ) : (
                        <>
                          <p>• Check browser console for detailed error information</p>
                          <p>• Verify network connectivity between devices</p>
                          <p>• Ensure CouchDB authentication is properly configured</p>
                          <p>• Check database permissions and restaurant ID matching</p>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Current sync configuration */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-medium text-gray-800 mb-2">🔍 Current Configuration:</h4>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>• <strong>Platform:</strong> {platform}</p>
                      <p>• <strong>Restaurant ID:</strong> {dbStatus.restaurantId || 'Not set'}</p>
                      <p>• <strong>Database Name:</strong> {dbStatus.dbName || 'Not available'}</p>
                      <p>• <strong>Expected Auth:</strong> admin:admin</p>
                      {currentServer && (
                        <p>• <strong>Target Server:</strong> {currentServer.ip}:{currentServer.port}</p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Autonomous Tab */}
        <TabsContent value="autonomous" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Autonomous Sync System
              </CardTitle>
              <CardDescription>
                Fully automated background sync that runs continuously
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Status Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg text-center">
                  <div className={`text-3xl font-bold mb-2 ${autonomousSync.isRunning ? 'text-green-600' : 'text-gray-400'}`}>
                    {autonomousSync.isRunning ? 'ACTIVE' : 'STOPPED'}
                  </div>
                  <div className="text-sm text-gray-600">System Status</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-3xl font-bold mb-2 text-blue-600">
                    {autonomousSync.discoveredServers}
                  </div>
                  <div className="text-sm text-gray-600">Servers Discovered</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-3xl font-bold mb-2 text-green-600">
                    {autonomousSync.connectedServers}
                  </div>
                  <div className="text-sm text-gray-600">Active Connections</div>
                </div>
              </div>

              {/* Control Panel */}
              <div className="flex gap-2">
                {autonomousSync.isRunning ? (
                  <Button onClick={autonomousSync.stop} variant="outline">
                    <Pause className="h-4 w-4 mr-2" />
                    Stop Autonomous Sync
                  </Button>
                ) : (
                  <Button onClick={autonomousSync.start}>
                    <Play className="h-4 w-4 mr-2" />
                    Start Autonomous Sync
                  </Button>
                )}
                <Button onClick={autonomousSync.discover} variant="outline">
                  <Search className="h-4 w-4 mr-2" />
                  Manual Discovery
                </Button>
              </div>

              {/* Autonomous Features */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-3">🤖 Autonomous Features</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-700">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Auto-starts on app launch</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Continuous server discovery</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Auto-connects to found servers</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Auto-reconnects on failures</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Live continuous sync</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>Background operation</span>
                  </div>
                </div>
              </div>

              {/* Error Display */}
              {autonomousSync.error && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">❌ Autonomous Sync Error</h4>
                  <p className="text-red-700 text-sm">{autonomousSync.error}</p>
                </div>
              )}

              {/* Last Discovery */}
              {autonomousSync.lastDiscovery && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm font-medium text-gray-800">Last Discovery</div>
                  <div className="text-sm text-gray-600">
                    {autonomousSync.lastDiscovery.toLocaleString()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Discovery Tab */}
        <TabsContent value="discovery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Server Discovery
              </CardTitle>
              <CardDescription>
                Scan for CouchDB servers on the local network
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button
                  onClick={discover}
                  disabled={discovering}
                  className="flex items-center gap-2"
                >
                  {discovering ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  {discovering ? 'Scanning...' : 'Scan Network'}
                </Button>
              </div>

              {/* Discovered Servers */}
              {servers.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Discovered Servers ({servers.length})</h4>
                  <div className="space-y-2">
                    {servers.map((server, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Server className="h-4 w-4 text-blue-500" />
                          <div>
                            <div className="font-medium">{server.ip}:{server.port}</div>
                            <div className="text-sm text-gray-600">{server.url}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {currentServer?.url === server.url ? (
                            <Badge className="bg-green-100 text-green-800">Connected</Badge>
                          ) : (
                            <Button
                              size="sm"
                              onClick={() => handleConnect(server)}
                              disabled={connected}
                            >
                              Connect
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const testUrl = `http://${server.ip}:${server.port}`;
                              window.open(testUrl, '_blank');
                            }}
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            Test
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {servers.length === 0 && !discovering && (
                <div className="text-center py-8 text-gray-500">
                  <WifiOff className="h-8 w-8 mx-auto mb-2" />
                  <p>No servers discovered</p>
                  <p className="text-sm">Click "Scan Network" to search for CouchDB servers</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sync Status Tab */}
        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Sync Status
              </CardTitle>
              <CardDescription>
                Real-time synchronization status and controls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Connection Status */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(connected, syncing, Boolean(error))}
                  <div>
                    <div className="font-medium">
                      {error ? 'Error' : syncing ? 'Syncing' : connected ? 'Connected' : 'Disconnected'}
                    </div>
                    {currentServer && (
                      <div className="text-sm text-gray-600">{currentServer.url}</div>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  {connected && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleDisconnect}
                    >
                      Disconnect
                    </Button>
                  )}
                  <Badge className={getStatusColor(connected, syncing, Boolean(error))}>
                    {error ? 'Error' : syncing ? 'Syncing' : connected ? 'Connected' : 'Disconnected'}
                  </Badge>
                </div>
              </div>

              {/* Sync Statistics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">{status.docsReceived}</div>
                  <div className="text-sm text-gray-600">Documents Received</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">{status.docsSent}</div>
                  <div className="text-sm text-gray-600">Documents Sent</div>
                </div>
              </div>

              {/* Last Sync Time */}
              {status.lastSync && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm font-medium text-blue-800">Last Sync</div>
                  <div className="text-sm text-blue-600">
                    {new Date(status.lastSync).toLocaleString()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Auth Debug Tab */}
        <TabsContent value="auth" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Authentication Debug
              </CardTitle>
              <CardDescription>
                CouchDB authentication and connection diagnostics
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Authentication Status */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-3">🔐 Authentication Configuration</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-blue-700">Expected Credentials:</p>
                    <p className="text-blue-600">Username: admin</p>
                    <p className="text-blue-600">Password: admin</p>
                  </div>
                  <div>
                    <p className="font-medium text-blue-700">Connection Format:</p>
                    <p className="text-blue-600 font-mono text-xs">*********************:PORT/DB</p>
                  </div>
                </div>
              </div>

              {/* Current Connection Details */}
              {currentServer && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-800 mb-3">🔗 Current Connection</h4>
                  <div className="space-y-2 text-sm">
                    <p><strong>Server IP:</strong> {currentServer.ip}</p>
                    <p><strong>Server Port:</strong> {currentServer.port}</p>
                    <p><strong>Database:</strong> {dbStatus.dbName || 'Not available'}</p>
                    <p><strong>Full URL:</strong> <code className="bg-gray-200 px-1 rounded">http://admin:***@{currentServer.ip}:{currentServer.port}/{dbStatus.dbName || 'resto-{id}'}</code></p>
                  </div>
                </div>
              )}

              {/* Authentication Test */}
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-3">🧪 Authentication Test</h4>
                <div className="space-y-2 text-sm text-yellow-700">
                  <p>To test authentication manually:</p>
                  <ol className="list-decimal list-inside space-y-1 ml-2">
                    <li>Open browser on mobile device</li>
                    <li>Navigate to: <code className="bg-yellow-200 px-1 rounded">http://{currentServer?.ip || 'DESKTOP_IP'}:{currentServer?.port || '5984'}</code></li>
                    <li>Enter credentials: admin / admin</li>
                    <li>You should see CouchDB welcome page</li>
                  </ol>
                </div>
              </div>

              {/* Error Analysis */}
              {error && (
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-3">❌ Error Analysis</h4>
                  <div className="space-y-2 text-sm text-red-700">
                    <p><strong>Error:</strong> {error}</p>
                    {error.toLowerCase().includes('unauthorized') && (
                      <div className="bg-red-100 p-2 rounded">
                        <p className="font-medium">🔐 Authentication Failure Detected</p>
                        <p>This error indicates the CouchDB server is rejecting the admin:admin credentials.</p>
                        <p><strong>Solution:</strong> Restart the desktop app to regenerate CouchDB configuration.</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Database Tab */}
        <TabsContent value="database" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Status
              </CardTitle>
              <CardDescription>
                Local PouchDB database information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <Badge variant={dbStatus.isReady ? "default" : "destructive"}>
                      {dbStatus.isReady ? 'Ready' : 'Not Ready'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Restaurant ID</span>
                    <span className="text-sm text-gray-600">
                      {dbStatus.restaurantId || 'Not set'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Database Name</span>
                    <span className="text-sm text-gray-600">
                      {dbStatus.dbName || 'Not available'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Has Database</span>
                    <Badge variant={dbStatus.hasDatabase ? "default" : "secondary"}>
                      {dbStatus.hasDatabase ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}