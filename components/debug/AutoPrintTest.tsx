"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Printer, Smartphone, Monitor, TestTube, CheckCircle, XCircle } from 'lucide-react';
import { useAutoPrint } from '@/components/providers/AutoPrintProvider';
import { autoPrintService } from '@/lib/services/auto-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { v4 as uuidv4 } from 'uuid';

export function AutoPrintTest() {
  const { config, isReady } = useAutoPrint();
  const { toast } = useToast();
  const [isTestingCreated, setIsTestingCreated] = useState(false);
  const [isTestingSynced, setIsTestingSynced] = useState(false);

  const createTestOrder = (): Order => {
    const now = new Date().toISOString();
    const dateStr = now.slice(0, 10).replace(/-/g, '');
    const orderId = `order:${dateStr}-999`; // Test order ID

    return {
      _id: orderId,
      id: orderId,
      type: 'order_document',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      tableId: 'TEST-TABLE',
      status: 'pending',
      orderType: 'dine-in',
      items: [
        {
          id: uuidv4(),
          menuItemId: 'test-item-1',
          name: 'Test Pizza Margherita',
          quantity: 1,
          price: 1200,
          size: 'Medium',
          notes: 'Test order for auto-print',
          categoryId: 'pizzas',
          addons: []
        },
        {
          id: uuidv4(),
          menuItemId: 'test-item-2',
          name: 'Test Coca Cola',
          quantity: 2,
          price: 300,
          size: 'Regular',
          notes: '',
          categoryId: 'beverages',
          addons: []
        }
      ],
      total: 1800,
      notes: 'This is a test order for auto-print functionality',
      paymentStatus: 'unpaid'
    };
  };

  const testOrderCreatedEvent = async () => {
    setIsTestingCreated(true);
    try {
      const testOrder = createTestOrder();
      
      // Simulate order-created event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('order-created', {
          detail: { order: testOrder }
        }));
      }

      toast({
        title: "🧪 Test Event Dispatched",
        description: "order-created event sent - check for auto-print",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "❌ Test Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    } finally {
      setIsTestingCreated(false);
    }
  };

  const testOrderSyncedEvent = async () => {
    setIsTestingSynced(true);
    try {
      const testOrder = createTestOrder();
      
      // Simulate order-synced event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('order-synced', {
          detail: { 
            order: testOrder,
            source: 'sync'
          }
        }));
      }

      toast({
        title: "🧪 Test Event Dispatched",
        description: "order-synced event sent - check for auto-print",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "❌ Test Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    } finally {
      setIsTestingSynced(false);
    }
  };

  const testDirectPrint = async () => {
    try {
      const testOrder = createTestOrder();
      await autoPrintService.printOrder(testOrder, 'created');
      
      toast({
        title: "🧪 Direct Print Test",
        description: "Direct print method called - check printer output",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "❌ Direct Print Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Auto-Print Testing
          {isReady ? (
            <Badge variant="default" className="bg-green-600">
              <Monitor className="h-3 w-3 mr-1" />
              Ready
            </Badge>
          ) : (
            <Badge variant="secondary">
              <Smartphone className="h-3 w-3 mr-1" />
              Not Ready
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Display */}
        <div className="grid grid-cols-2 gap-4 p-3 bg-muted/50 rounded-lg text-sm">
          <div className="space-y-1">
            <div className="font-medium">Service Status</div>
            <div className="flex items-center gap-2">
              {isReady ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <span>{isReady ? 'Active' : 'Inactive'}</span>
            </div>
          </div>
          <div className="space-y-1">
            <div className="font-medium">Configuration</div>
            <div className="text-xs space-y-0.5">
              <div>Enabled: {config.enabled ? '✅' : '❌'}</div>
              <div>Print Created: {config.printOnOrderCreated ? '✅' : '❌'}</div>
              <div>Print Synced: {config.printOnOrderSynced ? '✅' : '❌'}</div>
              <div>Delay: {config.delayMs}ms</div>
            </div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="space-y-3">
          <div>
            <h4 className="font-medium mb-2">Event-Based Tests</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <Button
                onClick={testOrderCreatedEvent}
                disabled={isTestingCreated || !config.printOnOrderCreated}
                variant="outline"
                size="sm"
              >
                <Printer className="h-4 w-4 mr-2" />
                {isTestingCreated ? 'Testing...' : 'Test Order Created'}
              </Button>
              
              <Button
                onClick={testOrderSyncedEvent}
                disabled={isTestingSynced || !config.printOnOrderSynced}
                variant="outline"
                size="sm"
              >
                <Smartphone className="h-4 w-4 mr-2" />
                {isTestingSynced ? 'Testing...' : 'Test Order Synced'}
              </Button>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Direct Test</h4>
            <Button
              onClick={testDirectPrint}
              disabled={!isReady}
              variant="default"
              size="sm"
              className="w-full"
            >
              <TestTube className="h-4 w-4 mr-2" />
              Test Direct Print
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-3 bg-blue-50/50 border border-blue-200 rounded-lg">
          <div className="text-xs text-blue-900 space-y-1">
            <div className="font-medium">Testing Instructions:</div>
            <div>1. Ensure you're on desktop with configured printers</div>
            <div>2. Enable auto-print in settings above</div>
            <div>3. Click test buttons to simulate mobile orders</div>
            <div>4. Check kitchen printers for output</div>
            <div>5. Watch for toast notifications</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}