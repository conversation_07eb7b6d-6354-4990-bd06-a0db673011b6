"use client"

import React, { use<PERSON>emo, useState, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { format, parseISO, getDay, getHours, addDays, startOfWeek, endOfWeek, addWeeks, subWeeks, isWithinInterval, startOfMonth, endOfMonth, eachWeekOfInterval, getMonth, getYear, isEqual, subMonths, isSameMonth } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ClockIcon, TrendingUpIcon, ArrowUpIcon, Users2Icon, InfoIcon, ChevronLeftIcon, ChevronRightIcon, CheckIcon, LayersIcon, PlusCircleIcon, MinusCircleIcon, XCircleIcon, CalendarDaysIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/lib/utils/currency';
import { 
  Popover,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";

interface PeakHoursHeatmapProps {
  orders: any[]; // Replace with proper type
  className?: string;
}

export default function PeakHoursHeatmap({
  orders,
  className
}: PeakHoursHeatmapProps) {
  const [hoveredCell, setHoveredCell] = useState<{ day: number; hour: number } | null>(null);
  const [currentWeekOffset, setCurrentWeekOffset] = useState(0);
  
  const dayNamesInFrench = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
  
  const hourlyData = useMemo(() => {
    const days = dayNamesInFrench;
    const hours = Array.from({ length: 24 }, (_, i) => i);
    const processedData: { count: number, total: number }[][] = Array(7).fill(0).map(() => 
      Array(24).fill(0).map(() => ({ count: 0, total: 0 }))
    );

    const weekStart = startOfWeek(addWeeks(new Date(), currentWeekOffset), { weekStartsOn: 0 });
    const weekEnd = endOfWeek(addWeeks(new Date(), currentWeekOffset), { weekStartsOn: 0 });

    const filteredOrders = orders.filter(order => {
      if (!order.createdAt || order.status !== 'completed') return false;
      try {
        const orderDate = parseISO(order.createdAt);
        return isWithinInterval(orderDate, { start: weekStart, end: weekEnd });
      } catch (error) {
        console.error('Error parsing order date for filtering:', error, order.createdAt);
        return false;
      }
    });
    
    filteredOrders.forEach(order => {
      try {
        const orderDate = parseISO(order.createdAt);
        const dayIndex = getDay(orderDate); 
        const hourIndex = getHours(orderDate);
        
        processedData[dayIndex][hourIndex].count += 1;
        processedData[dayIndex][hourIndex].total += order.total || 0;
      } catch (error) {
        console.error('Error parsing date within hourlyData calculation:', error);
      }
    });
    
    return { days, hours, data: processedData };
  }, [orders, currentWeekOffset]);

  const { maxCount, maxTotal, totalOrders, totalSales, nonZeroValues } = useMemo(() => {
    let maxCount = 0;
    let maxTotal = 0;
    let totalOrders = 0;
    let totalSales = 0;
    let nonZeroValues = 0;
    
    for (let i = 0; i < 7; i++) {
      for (let j = 0; j < 24; j++) {
        maxCount = Math.max(maxCount, hourlyData.data[i][j].count);
        maxTotal = Math.max(maxTotal, hourlyData.data[i][j].total);
        totalOrders += hourlyData.data[i][j].count;
        totalSales += hourlyData.data[i][j].total;
        if (hourlyData.data[i][j].count > 0) {
          nonZeroValues++;
        }
      }
    }
    return { maxCount, maxTotal, totalOrders, totalSales, nonZeroValues };
  }, [hourlyData]);

  const dynamicColors = useMemo(() => {
    const colorMap = new Map<number, string>();
    colorMap.set(0, 'bg-muted/10 dark:bg-muted/20');
    
    if (maxCount <= 1) {
      colorMap.set(1, 'bg-blue-400 dark:bg-blue-600/70');
      return colorMap;
    }
    
    const getColor = (value: number) => {
      const intensity = maxCount > 0 ? Math.floor((value / maxCount) * 100) : 0;

      if (intensity === 0) return 'bg-muted/10 dark:bg-muted/20'; 
      if (intensity < 20) return 'bg-blue-100 dark:bg-blue-900/30';
      if (intensity < 40) return 'bg-blue-300 dark:bg-blue-800/40';
      if (intensity < 60) return 'bg-blue-500 dark:bg-blue-700/60';
      if (intensity < 80) return 'bg-blue-600 dark:bg-blue-500/80';
      return 'bg-blue-700 dark:bg-blue-500'; 
    };
    
    for (let count = 1; count <= maxCount; count++) {
      colorMap.set(count, getColor(count));
    }
    
    return colorMap;
  }, [maxCount]);

  const getColorForCount = (count: number) => {
    return dynamicColors.get(count) || 'bg-muted/10';
  };

  const formatHour = (hour: number) => {
    return `${hour}:00`;
  };

  const businessHours = hourlyData.hours.filter(hour => hour >= 8 && hour <= 23);

  const dayTotals = useMemo(() => {
    return hourlyData.data.map(day => 
      day.reduce((acc, hour) => ({ 
        count: acc.count + hour.count, 
        total: acc.total + hour.total 
      }), { count: 0, total: 0 })
    );
  }, [hourlyData.data]);

  const hourTotals = useMemo(() => {
    const totals = Array(24).fill(0).map(() => ({ count: 0, total: 0 }));
    
    for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
      for (let hourIndex = 0; hourIndex < 24; hourIndex++) {
        totals[hourIndex].count += hourlyData.data[dayIndex][hourIndex].count;
        totals[hourIndex].total += hourlyData.data[dayIndex][hourIndex].total;
      }
    }
    
    return totals;
  }, [hourlyData.data]);

  const busiestDayIndex = useMemo(() => {
    return dayTotals.reduce((maxIndex, current, index, array) => 
      current.count > array[maxIndex].count ? index : maxIndex, 0);
  }, [dayTotals]);

  const busiestHour = useMemo(() => {
    let maxCount = 0;
    let busiestHour = 0;
    let busiestDay = 0;

    for (let dayIndex = 0; dayIndex < hourlyData.data.length; dayIndex++) {
      for (let hourIndex = 0; hourIndex < 24; hourIndex++) {
        const count = hourlyData.data[dayIndex][hourIndex].count;
        if (count > maxCount) {
          maxCount = count;
          busiestHour = hourIndex;
          busiestDay = dayIndex;
        }
      }
    }

    return {
      dayIndex: busiestDay,
      hourIndex: busiestHour,
      count: maxCount,
      total: hourlyData.data[busiestDay][busiestHour].total
    };
  }, [hourlyData.data]);
  
  const busiestHourOfDay = useMemo(() => {
    const maxHour = hourTotals.reduce((maxIdx, curr, idx, arr) => 
      curr.count > arr[maxIdx].count ? idx : maxIdx, 0);
    
    return {
      hour: maxHour,
      count: hourTotals[maxHour].count,
      total: hourTotals[maxHour].total
    };
  }, [hourTotals]);

  const handleCellHover = (dayIndex: number, hourIndex: number) => {
    setHoveredCell({ day: dayIndex, hour: hourIndex });
  };

  const handleCellLeave = () => {
    setHoveredCell(null);
  };

  const getPercentage = (count: number) => {
    if (totalOrders === 0) return '0%';
    return `${((count / totalOrders) * 100).toFixed(1)}%`;
  };

  const formatDateForDayLabel = (dayIndex: number) => {
    const targetWeekStart = startOfWeek(addWeeks(new Date(), currentWeekOffset), { weekStartsOn: 0 });
    const date = addDays(targetWeekStart, dayIndex);
    return format(date, 'd MMM', { locale: fr });
  };

  const getCurrentWeekDisplayString = (offset?: number) => {
    const targetOffset = offset === undefined ? currentWeekOffset : offset;

    if (targetOffset === 0 && offset === undefined) return "Cette semaine";
    if (targetOffset === -1 && offset === undefined) return "Semaine dernière";

    const weekStart = startOfWeek(addWeeks(new Date(), targetOffset), { weekStartsOn: 0 });
    const weekEnd = endOfWeek(addWeeks(new Date(), targetOffset), { weekStartsOn: 0 });
    
    if (weekStart.getMonth() !== weekEnd.getMonth()) {
      return `${format(weekStart, 'd MMM', { locale: fr })} - ${format(weekEnd, 'd MMM', { locale: fr })}`;
    }
    return `${format(weekStart, 'd', { locale: fr })} - ${format(weekEnd, 'd MMM', { locale: fr })}`;
  };
  
  const legendColors = useMemo(() => {
    const samples = [
      { count: 0, color: getColorForCount(0) },
      { count: Math.floor(maxCount * 0.2), color: getColorForCount(Math.floor(maxCount * 0.2)) },
      { count: Math.floor(maxCount * 0.5), color: getColorForCount(Math.floor(maxCount * 0.5)) },
      { count: Math.floor(maxCount * 0.8), color: getColorForCount(Math.floor(maxCount * 0.8)) },
      { count: maxCount, color: getColorForCount(maxCount) },
    ];
    const uniqueSamples = samples.reduce((acc, current) => {
      if (!acc.find(item => item.count === current.count)) {
        acc.push(current);
      }
      return acc;
    }, [] as {count: number, color: string}[]);
    if (maxCount === 0) return [{ count: 0, color: getColorForCount(0) }];
    return uniqueSamples.sort((a,b) => a.count - b.count);
  }, [maxCount, getColorForCount]);

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pt-4 pb-3 px-3 sm:pt-6 sm:pb-4 sm:px-6">
        <div className="flex flex-col sm:flex-row items-start sm:justify-between gap-3 sm:gap-0">
          <div>
            <CardTitle className="text-base sm:text-lg font-medium flex items-center gap-2">
              <ClockIcon className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              Heures de Pointe
            </CardTitle>
            <CardDescription className="text-sm">
              Distribution des commandes par jour et heure
            </CardDescription>
          </div>

          <div className="flex flex-row sm:flex-col gap-4 sm:gap-1 sm:items-end">
            <div>
              <p className="text-xs text-muted-foreground sm:text-right">Total Commandes</p>
              <p className="text-base sm:text-lg font-semibold sm:text-right">{totalOrders}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground sm:text-right">Ventes Totales</p>
              <p className="text-base sm:text-lg font-semibold sm:text-right">{formatCurrency(totalSales)}</p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-2 sm:pt-4 px-2 sm:px-6 overflow-x-auto">
        <div className="mb-3 sm:mb-4 flex justify-center">
          <div className="inline-flex items-center bg-muted p-0.5 sm:p-1 rounded-lg shadow-sm border">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setCurrentWeekOffset(prev => prev - 1)
                    }}
                    className="h-6 w-6 sm:h-8 sm:w-8"
                  >
                    <ChevronLeftIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="sr-only">Précédente</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom"><p>Semaine précédente</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className={cn(
                      "px-2 py-1 sm:px-3 sm:py-1.5 h-6 sm:h-8 text-xs sm:text-sm font-medium tabular-nums min-w-[120px] sm:min-w-[140px]",
                    )}
                  >
                     {getCurrentWeekDisplayString()}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{getCurrentWeekDisplayString()}</p> 
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      if (currentWeekOffset < 0) setCurrentWeekOffset(prev => prev + 1)
                    }}
                    disabled={currentWeekOffset === 0}
                    className="h-6 w-6 sm:h-8 sm:w-8"
                  >
                    <ChevronRightIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="sr-only">Suivante</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom"><p>Semaine suivante</p></TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="relative min-w-[650px] sm:min-w-[700px]">
          <div className="flex border-b pb-1 mb-1.5 sm:mb-2">
            <div className="w-20 sm:w-28 flex-shrink-0"></div>
            <div className="flex-1 flex">
              {businessHours.map(hour => {
                const isMaxHour = hour === busiestHourOfDay.hour;
                return (
                  <div key={hour} className="flex-1 flex flex-col items-center">
                    <div className={cn(
                      "text-[10px] sm:text-xs font-medium mb-0.5 sm:mb-1",
                      isMaxHour ? "text-primary" : "text-muted-foreground"
                    )}>
                      {formatHour(hour)}
                      {isMaxHour && <ArrowUpIcon className="inline h-2 w-2 sm:h-3 sm:w-3 ml-0.5" />}
                    </div>
                    <div className={cn(
                      "text-[9px] sm:text-xs px-0.5 rounded",
                      isMaxHour ? "bg-primary/10 text-primary font-medium px-1" : ""
                    )}>
                      {hourTotals[hour].count}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {hourlyData.days.map((day, dayIndex) => (
            <div 
              key={day} 
              className={cn(
                "flex mb-1.5 group",
                dayIndex === busiestDayIndex && "bg-muted/5 rounded"
              )}
            >
              <div className={cn(
                "w-20 sm:w-28 flex-shrink-0 text-xs sm:text-sm py-1 px-1 sm:px-2 flex flex-col justify-center",
                dayIndex === busiestDayIndex && "text-primary font-medium"
              )}>
                <div className="flex items-center gap-1">
                  {dayIndex === busiestDayIndex && <div className="h-1 w-1 sm:h-1.5 sm:w-1.5 rounded-full bg-primary" />}
                  <span className="truncate">{day.slice(0, 3)}</span>
                </div>
                <div className="text-[10px] sm:text-xs text-muted-foreground mt-0.5">
                  {formatDateForDayLabel(dayIndex)}
                  <span className="ml-1">({dayTotals[dayIndex].count})</span>
                </div>
              </div>
              
              <div className="flex-1 flex">
                {businessHours.map(hour => {
                  const value = hourlyData.data[dayIndex][hour];
                  const isBusiestCell = dayIndex === busiestHour.dayIndex && hour === busiestHour.hourIndex && value.count > 0;
                  const isHovered = hoveredCell?.day === dayIndex && hoveredCell?.hour === hour;
                  
                  return (
                    <Popover key={`${day}-${hour}`} open={isHovered}>
                      <PopoverTrigger asChild>
                        <motion.div 
                          className={cn(
                            "flex-1 h-8 sm:h-12 m-px rounded-sm sm:rounded-md flex items-center justify-center transition-all",
                            getColorForCount(value.count),
                            isBusiestCell && "ring-1 sm:ring-2 ring-primary/70 shadow-sm",
                            value.count > 0 && "cursor-pointer hover:z-10 hover:shadow-md"
                          )}
                          onHoverStart={() => handleCellHover(dayIndex, hour)}
                          onHoverEnd={handleCellLeave}
                          initial={{ scale: 1 }}
                          whileHover={{ scale: 1.05 }}
                          transition={{ duration: 0.15 }}
                        >
                          {value.count > 0 && (
                            <span className={cn(
                              "text-[9px] sm:text-xs font-medium",
                              value.count >= maxCount * 0.7 ? "text-white drop-shadow-sm" : ""
                            )}>
                              {value.count}
                            </span>
                          )}
                        </motion.div>
                      </PopoverTrigger>
                      
                      <PopoverContent 
                        side="top" 
                        className="w-56 p-0 border-primary/20 shadow-md" 
                        sideOffset={5}
                      >
                        <div className="bg-primary/10 p-2 rounded-t-md border-b border-primary/20">
                          <div className="font-medium text-sm flex justify-between items-center">
                            <span>{day}</span>
                            <span>{formatHour(hour)}</span>
                          </div>
                          <div className="text-xs text-muted-foreground mt-0.5">
                            {formatDateForDayLabel(dayIndex)}
                          </div>
                        </div>
                        
                        <div className="p-3">
                          <div className="grid grid-cols-2 gap-y-2 gap-x-3 text-sm">
                            <div>
                              <p className="text-xs text-muted-foreground">Commandes</p>
                              <p className="font-medium">{value.count}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">% du Total</p>
                              <p className="font-medium">{getPercentage(value.count)}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Ventes</p>
                              <p className="font-medium">{formatCurrency(value.total)}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Ticket Moyen</p>
                              <p className="font-medium">
                                {value.count > 0 ? formatCurrency(value.total / value.count) : "-"}
                              </p>
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-0 mt-4 sm:mt-6 border-t pt-3 sm:pt-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1 text-xs text-muted-foreground cursor-help">
                  <InfoIcon className="h-3 w-3" />
                  <span>Échelle adaptative</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="p-2 w-60">
                <p className="text-xs">
                  L'intensité des couleurs s'adapte au volume maximum de commandes ({maxCount} commandes max / créneau).
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex items-center gap-0.5">
            <div className="text-[10px] sm:text-xs text-muted-foreground mr-1">0</div>
            {legendColors.filter(c => c.count > 0 || maxCount === 0).map((colorItem, i) => (
              <TooltipProvider key={`legend-${i}`}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className={cn(
                      "h-3 w-3 sm:h-5 sm:w-5 rounded-sm", 
                      colorItem.color, 
                      maxCount === 0 && "w-6 sm:w-10"
                    )}></div>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="p-1.5">
                    <p className="text-xs">{colorItem.count} cmd.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
            {maxCount > 0 && <div className="text-[10px] sm:text-xs text-muted-foreground ml-1">{maxCount}</div>}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
