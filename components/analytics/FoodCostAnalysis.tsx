"use client"

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from '@/lib/utils/currency';
import { DollarSignIcon, AlertCircleIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FoodCostAnalysisProps {
  orders: any[]; // Replace with proper type
  menuItemRecipes: any[]; // Replace with proper type
  className?: string;
}

export default function FoodCostAnalysis({
  orders,
  menuItemRecipes,
  className
}: FoodCostAnalysisProps) {
  // Process orders and recipes to calculate food cost percentages
  const foodCostData = useMemo(() => {
    // Map to store aggregated data by menu item
    const itemMap = new Map();

    // Process all orders
    orders.forEach(order => {
      if (order.status !== 'completed') return;

      order.items.forEach((item: any) => {
        const itemId = item.id || item._id;
        const existingItem = itemMap.get(itemId);

        if (existingItem) {
          existingItem.quantity += item.quantity;
          existingItem.revenue += (item.price * item.quantity);
        } else {
          itemMap.set(itemId, {
            id: itemId,
            name: item.name,
            quantity: item.quantity,
            revenue: item.price * item.quantity,
            price: item.price,
            cost: 0, // Will be calculated using recipes
            costPercentage: 0
          });
        }
      });
    });

    // Calculate costs using recipes with new cost logic
    menuItemRecipes.forEach(recipe => {
      const menuItemId = recipe.menuItemId;
      const item = itemMap.get(menuItemId);

      if (item) {
        // Use effective cost (fixed or calculated from recipe)
        const effectiveCost = recipe.costingMethod === 'fixed' && recipe.fixedCost 
          ? recipe.fixedCost 
          : (recipe.costPerUnit || 0);
        
        item.cost = effectiveCost;

        // Calculate cost percentage
        if (item.price > 0) {
          item.costPercentage = (effectiveCost / item.price) * 100;
        }
      }
    });

    return Array.from(itemMap.values())
      .filter(item => item.cost > 0) // Only include items with cost data
      .sort((a, b) => b.revenue - a.revenue) // Sort by revenue
      .slice(0, 10); // Top 10 items
  }, [orders, menuItemRecipes]);

  // Calculate overall food cost percentage
  const overallFoodCost = useMemo(() => {
    const totalRevenue = foodCostData.reduce((sum, item) => sum + item.revenue, 0);
    const totalCost = foodCostData.reduce((sum, item) => sum + (item.cost * item.quantity), 0);

    return totalRevenue > 0 ? (totalCost / totalRevenue) * 100 : 0;
  }, [foodCostData]);

  // Get status color based on cost percentage
  const getStatusColor = (percentage: number) => {
    if (percentage <= 25) return "text-green-600";
    if (percentage <= 35) return "text-amber-600";
    return "text-red-600";
  };

  // Get progress color based on cost percentage
  const getProgressColor = (percentage: number) => {
    if (percentage <= 25) return "bg-green-600";
    if (percentage <= 35) return "bg-amber-600";
    return "bg-red-600";
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <DollarSignIcon className="h-5 w-5 text-primary" />
              Food Cost Analysis
            </CardTitle>
            <CardDescription>
              Cost of goods sold (COGS) breakdown by menu item
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Overall Food Cost */}
        <div className="mb-6 p-4 bg-muted/20 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium">Overall Food Cost Percentage</h3>
            <span className={cn("text-lg font-bold", getStatusColor(overallFoodCost))}>
              {overallFoodCost.toFixed(1)}%
            </span>
          </div>
          <Progress
            value={overallFoodCost}
            max={50}
            className={cn("h-2 bg-muted", getProgressColor(overallFoodCost))}
          />
          <div className="flex justify-between mt-1 text-xs text-muted-foreground">
            <span>Good (&le;25%)</span>
            <span>Acceptable (&le;35%)</span>
            <span>High (&gt;35%)</span>
          </div>
        </div>

        {/* Food Cost by Item */}
        {foodCostData.length > 0 ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Menu Item</TableHead>
                  <TableHead className="text-right">Unit Cost</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">Cost %</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {foodCostData.map(item => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="font-medium">{item.name}</div>
                    </TableCell>
                    <TableCell className="text-right">{formatCurrency(item.cost)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(item.price)}</TableCell>
                    <TableCell className="text-right">
                      <Badge
                        variant={
                          item.costPercentage <= 25 ? "default" :
                          item.costPercentage <= 35 ? "secondary" :
                          "destructive"
                        }
                      >
                        {item.costPercentage.toFixed(1)}%
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircleIcon className="h-10 w-10 text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium">No COGS Data Available</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Enable COGS tracking and create menu item recipes to see food cost analysis.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
