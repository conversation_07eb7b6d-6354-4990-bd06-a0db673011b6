"use client"

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircleIcon, InfoIcon, ArrowRightIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Alert {
  type: 'warning' | 'info' | 'success';
  message: string;
  action?: string;
  link?: string;
}

interface AlertBannerProps {
  alerts: Alert[];
  className?: string;
}

export default function AlertBanner({
  alerts,
  className
}: AlertBannerProps) {
  if (alerts.length === 0) return null;

  return (
    <Card className={cn("border-l-4 border-l-amber-500", className)}>
      <CardContent className="p-4">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-3">
          <div className="flex items-center gap-2">
            <AlertCircleIcon className="h-5 w-5 text-amber-500" />
            <h3 className="text-sm font-medium">Smart Alerts & Action Tips</h3>
          </div>
          
          <div className="flex-1 flex flex-col md:flex-row items-start md:items-center gap-3 md:gap-6">
            {alerts.map((alert, index) => (
              <div key={index} className="flex items-center gap-2">
                <Badge 
                  variant={alert.type === 'warning' ? "destructive" : 
                          alert.type === 'success' ? "default" : "secondary"}
                  className="h-5 px-1.5"
                >
                  {alert.type === 'warning' ? '⚠️' : 
                   alert.type === 'success' ? '✓' : 'ℹ️'}
                </Badge>
                <span className="text-sm">{alert.message}</span>
                {alert.action && alert.link && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="p-0 h-auto text-xs text-primary"
                    onClick={() => {
                      if (alert.link?.startsWith('#')) {
                        document.querySelector(alert.link)?.scrollIntoView({ behavior: 'smooth' });
                      } else if (alert.link) {
                        window.location.href = alert.link;
                      }
                    }}
                  >
                    {alert.action}
                    <ArrowRightIcon className="ml-1 h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
