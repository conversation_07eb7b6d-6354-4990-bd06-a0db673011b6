"use client"

import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { format, addWeeks, subWeeks, startOfWeek, endOfWeek, addDays, isSameMonth } from 'date-fns';
import { fr } from 'date-fns/locale';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon, 
  CalendarIcon, 
  PackageIcon, 
  TrendingUpIcon, 
  PlusIcon, 
  InfoIcon 
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from '@/lib/utils/currency';

// Interface for production batch data
interface ProductionBatch {
  id: string;
  subRecipeId: string;
  subRecipeName: string;
  batchSize: number;
  producedQuantity: number;
  date: string;
  yieldPercentage: number;
  costPerUnit: number;
  totalCost: number;
  performedBy: string;
}

interface WeeklyProductionHeatmapProps {
  batches: ProductionBatch[];
  className?: string;
  onDateClick?: (date: Date) => void;
}

export default function WeeklyProductionHeatmap({
  batches,
  className,
  onDateClick
}: WeeklyProductionHeatmapProps) {
  // State for current week
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    const today = new Date();
    return startOfWeek(today, { weekStartsOn: 1 }); // Start week on Monday
  });
  
  // Navigate to previous week
  const navigatePreviousWeek = () => {
    setCurrentWeekStart(prev => subWeeks(prev, 1));
  };
  
  // Navigate to next week
  const navigateNextWeek = () => {
    setCurrentWeekStart(prev => addWeeks(prev, 1));
  };
  
  // Reset to current week
  const resetToCurrentWeek = () => {
    const today = new Date();
    setCurrentWeekStart(startOfWeek(today, { weekStartsOn: 1 }));
  };
  
  // Calculate current week range
  const weekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
  
  // Format week display
  const weekRangeDisplay = (() => {
    const formattedStart = format(currentWeekStart, 'd MMM', { locale: fr });
    const formattedEnd = format(weekEnd, 'd MMM', { locale: fr });
    
    // If same month, don't repeat month
    if (isSameMonth(currentWeekStart, weekEnd)) {
      return `${format(currentWeekStart, 'd')} - ${formattedEnd} ${format(currentWeekStart, 'yyyy')}`;
    }
    
    // Different months
    return `${formattedStart} - ${formattedEnd} ${format(currentWeekStart, 'yyyy')}`;
  })();
  
  // Generate days of the week
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const date = addDays(currentWeekStart, i);
    return {
      date,
      dayName: format(date, 'EEE', { locale: fr }),
      dayNumber: format(date, 'd'),
      isToday: format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
    };
  });
  
  // Process batches to group by day
  const batchesByDay = weekDays.map(day => {
    const dayStr = format(day.date, 'yyyy-MM-dd');
    const dayBatches = batches.filter(batch => 
      format(new Date(batch.date), 'yyyy-MM-dd') === dayStr
    );
    
    // Aggregate by recipe
    const recipeMap: Record<string, { 
      count: number; 
      totalQuantity: number; 
      totalCost: number;
      avgYield: number;
    }> = {};
    
    dayBatches.forEach(batch => {
      if (!recipeMap[batch.subRecipeName]) {
        recipeMap[batch.subRecipeName] = {
          count: 0,
          totalQuantity: 0,
          totalCost: 0,
          avgYield: 0
        };
      }
      
      recipeMap[batch.subRecipeName].count += 1;
      recipeMap[batch.subRecipeName].totalQuantity += batch.producedQuantity;
      recipeMap[batch.subRecipeName].totalCost += batch.totalCost;
      recipeMap[batch.subRecipeName].avgYield += batch.yieldPercentage;
    });
    
    // Calculate averages for yields
    Object.keys(recipeMap).forEach(key => {
      if (recipeMap[key].count > 0) {
        recipeMap[key].avgYield = recipeMap[key].avgYield / recipeMap[key].count;
      }
    });
    
    // Sort recipes by count
    const recipesSortedByCost = Object.entries(recipeMap).map(([name, data]) => ({
      name,
      ...data
    })).sort((a, b) => b.totalCost - a.totalCost); // Sort by totalCost to find the most significant
    
    return {
      date: day.date,
      dayName: day.dayName,
      dayNumber: day.dayNumber,
      isToday: day.isToday,
      batches: dayBatches,
      batchCount: dayBatches.length,
      totalProduced: dayBatches.reduce((sum, b) => sum + b.producedQuantity, 0),
      totalCost: dayBatches.reduce((sum, b) => sum + b.totalCost, 0),
      recipes: recipesSortedByCost, // Use recipes sorted by cost for tooltip, and can pick [0] for cell display
      topRecipeName: recipesSortedByCost.length > 0 ? recipesSortedByCost[0].name : null
    };
  });
  
  // Find max totalCost for week for intensity scaling
  const maxTotalCostForWeek = Math.max(...batchesByDay.map(d => d.totalCost), 1); // Use totalCost for intensity
  
  return (
    <div className={cn("space-y-3", className)}> {/* Reduced space-y a bit */}
      {/* Header with navigation */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <TrendingUpIcon className="h-6 w-6 text-primary" /> {/* Changed icon */}
          <div>
            <h3 className="text-lg font-semibold">Activité de Production Hebdomadaire</h3>
            <p className="text-xs text-muted-foreground">Visualisez les jours les plus productifs par coût total.</p>
          </div>
        </div>
        
        <div className="flex items-center gap-1.5">
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0" 
            onClick={navigatePreviousWeek}
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={resetToCurrentWeek}
            className="text-xs h-8 px-2"
          >
            {weekRangeDisplay}
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0" 
            onClick={navigateNextWeek}
          >
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Weekly heatmap */}
      <div className="grid grid-cols-7 gap-2.5"> {/* Slightly reduced gap */}
        {batchesByDay.map((day, index) => {
          const intensity = maxTotalCostForWeek > 0 ? day.totalCost / maxTotalCostForWeek : 0;
          let bgClass = 'bg-gray-100 dark:bg-gray-800/50'; // Default for no/low activity
          
          if (day.batchCount > 0) { // Only apply activity colors if there are batches
            if (intensity <= 0.1) bgClass = 'bg-green-100 dark:bg-green-900/40';
            else if (intensity <= 0.25) bgClass = 'bg-green-200 dark:bg-green-800/50';
            else if (intensity <= 0.5) bgClass = 'bg-green-300 dark:bg-green-700/60';
            else if (intensity <= 0.75) bgClass = 'bg-green-400 dark:bg-green-600/70';
            else bgClass = 'bg-green-500 dark:bg-green-500/80 text-white'; // Darkest shade with white text
          }
          
          return (
            <TooltipProvider key={index} delayDuration={200}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className={cn(
                      "rounded-lg border p-2.5 flex flex-col items-center justify-start aspect-[3/4] transition-all", // Aspect ratio for a taller cell
                      bgClass,
                      day.batchCount > 0 
                        ? "hover:shadow-lg hover:ring-2 hover:ring-primary/70 cursor-pointer" 
                        : "border-dashed",
                      day.isToday && "ring-2 ring-blue-500 dark:ring-blue-400"
                    )}
                    onClick={() => day.batchCount > 0 && onDateClick && onDateClick(day.date)}
                  >
                    <div 
                      className={cn(
                        "text-xs font-semibold mb-0.5",
                        day.isToday ? (intensity > 0.75 ? "text-white" : "text-blue-600 dark:text-blue-300") : (intensity > 0.75 ? "text-white" : "text-gray-700 dark:text-gray-300")
                      )}
                    >
                      {day.dayName.toUpperCase()}
                    </div>
                    <div 
                      className={cn(
                        "text-lg font-bold mb-1",
                        day.isToday ? (intensity > 0.75 ? "text-white" : "text-blue-700 dark:text-blue-200") : (intensity > 0.75 ? "text-white" : "text-gray-800 dark:text-gray-100")
                      )}
                    >
                      {day.dayNumber}
                    </div>
                    
                    {day.batchCount > 0 ? (
                      <>
                        {/* Display Top Recipe Name instead of generic PackageIcon */}
                        {day.topRecipeName ? (
                          <div 
                            title={day.topRecipeName} // Full name on hover via browser default tooltip
                            className={cn(
                              "text-xs font-medium truncate leading-tight text-center max-w-full px-0.5 mb-1",
                              intensity > 0.75 ? "text-white/80" : "text-green-700 dark:text-green-200/80"
                            )}
                            style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden' }} // For 2-line clamp
                          >
                            {day.topRecipeName}
                          </div>
                        ) : (
                          <PackageIcon className={cn("h-5 w-5 mb-1", intensity > 0.75 ? "text-white/90" : "text-green-700 dark:text-green-300/90")} />
                        )}
                        <div 
                          className={cn(
                            "text-sm font-semibold", // Slightly smaller font for cost to give space to recipe name
                            intensity > 0.75 ? "text-white/90" : "text-green-800 dark:text-green-200/90"
                          )}
                        >
                          {formatCurrency(day.totalCost)}
                        </div>
                      </>
                    ) : (
                      <div className="text-xs text-gray-400 dark:text-gray-600 mt-2">Aucune</div>
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs p-3 bg-background text-foreground border shadow-xl rounded-lg">
                  <h4 className="font-semibold text-base mb-2">
                    {format(day.date, 'EEEE d MMMM yyyy', { locale: fr })}
                  </h4>
                  {day.batchCount > 0 ? (
                    <div className="space-y-2.5 text-sm">
                      <div className="flex justify-between">
                        <span>Lots Produits:</span> 
                        <span className="font-medium">{day.batchCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Quantité Totale:</span> 
                        <span className="font-medium">{day.totalProduced} unités</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Coût Total:</span> 
                        <span className="font-medium">{formatCurrency(day.totalCost)}</span>
                      </div>
                      {day.recipes.length > 0 && (
                        <div>
                          <h5 className="font-medium mt-2 mb-1">Top Recettes:</h5>
                          <ul className="list-disc list-inside space-y-1 text-xs">
                            {day.recipes.slice(0, 3).map(recipe => (
                              <li key={recipe.name}>
                                {recipe.name} ({recipe.count}x) - {formatCurrency(recipe.totalCost)}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p>Aucune production enregistrée pour ce jour.</p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>

      {/* Legend */}
      <div className="flex items-center justify-end gap-4 text-xs text-muted-foreground mt-2 pr-1">
        <div className="flex items-center gap-1.5">
          <div className="w-3 h-3 rounded-sm bg-green-100 dark:bg-green-900/40 border border-gray-300 dark:border-gray-600"></div>
          <span>Faible</span>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="w-3 h-3 rounded-sm bg-green-300 dark:bg-green-700/60 border border-gray-300 dark:border-gray-600"></div>
          <span>Moyen</span>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="w-3 h-3 rounded-sm bg-green-500 dark:bg-green-500/80 border border-gray-300 dark:border-gray-600"></div>
          <span>Élevé</span>
        </div>
         <div className="flex items-center gap-1.5">
          <div className="w-3 h-3 rounded-sm bg-gray-100 dark:bg-gray-800/50 border border-gray-300 dark:border-gray-600"></div>
          <span>Nul/Très Faible</span>
        </div>
      </div>
    </div>
  );
} 