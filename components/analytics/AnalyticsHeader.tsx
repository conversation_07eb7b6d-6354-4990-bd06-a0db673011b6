"use client"

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  BarChart4Icon,
  RefreshCwIcon,
  CalendarIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnalyticsHeaderProps {
  title: string;
  subtitle: string;
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  activeTab: string;
  onTabChange: (value: string) => void;
  onRefresh: () => void;
  isLoading: boolean;
}

export default function AnalyticsHeader({
  title = "Analytics & KPIs",
  subtitle = "Comprehensive business insights and performance metrics",
  dateRange,
  onDateRangeChange,
  activeTab,
  onTabChange,
  onRefresh,
  isLoading
}: AnalyticsHeaderProps) {
  return (
    <div className="space-y-4">
      {/* Header Title */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-full">
            <BarChart4Icon className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">{title}</h1>
            <p className="text-sm text-muted-foreground">{subtitle}</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          disabled={isLoading}
          className="w-full md:w-auto"
        >
          <RefreshCwIcon className={cn(
            "mr-2 h-4 w-4",
            isLoading && "animate-spin"
          )} />
          {isLoading ? "Refreshing..." : "Refresh Data"}
        </Button>
      </div>

      {/* Date Range Controls */}
      <Card className="border shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            {/* Date Range Tabs */}
            <Tabs value={activeTab} onValueChange={onTabChange} className="w-full md:w-auto">
              <TabsList className="w-full md:w-auto grid grid-cols-5">
                <TabsTrigger value="today">Aujourd'hui</TabsTrigger>
                <TabsTrigger value="yesterday">Hier</TabsTrigger>
                <TabsTrigger value="week">Cette Semaine</TabsTrigger>
                <TabsTrigger value="month">Ce Mois</TabsTrigger>
                <TabsTrigger value="custom">Perso</TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Custom Date Range Picker */}
            {activeTab === 'custom' && (
              <div className="w-full md:w-auto">
                <DateRangePicker
                  value={dateRange}
                  onChange={onDateRangeChange}
                  locale={fr}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
