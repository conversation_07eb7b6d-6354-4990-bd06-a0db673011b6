"use client";

import React, { useState } from 'react';
import { formatCurrency } from '@/lib/utils/currency';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PlusIcon, SearchIcon, CheckIcon, AlertCircleIcon, ClockIcon, PencilIcon, TrashIcon, RepeatIcon, UsersIcon, PackageIcon, HomeIcon, ZapIcon, TagIcon } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import ExpenseForm from '@/app/components/finance/ExpenseForm';
import { cn } from '@/lib/utils';

const EXPENSE_CATEGORIES = {
  STAFF: {
    id: 'staff',
    name: 'Personnel',
    color: 'hsl(230, 85%, 55%)',
    icon: UsersIcon
  },
  SUPPLIERS: {
    id: 'suppliers',
    name: 'Fournisseurs',
    color: 'hsl(160, 85%, 45%)',
    icon: PackageIcon
  },
  RENT: {
    id: 'rent',
    name: 'Loyer',
    color: 'hsl(340, 85%, 55%)',
    icon: HomeIcon
  },
  UTILITIES: {
    id: 'utilities',
    name: 'Services',
    color: 'hsl(45, 85%, 55%)',
    icon: ZapIcon
  },
  OTHER: {
    id: 'other',
    name: 'Autres',
    color: 'hsl(270, 85%, 55%)',
    icon: TagIcon
  }
};

const now = new Date();
const currentYear = now.getFullYear();
const currentMonth = now.getMonth();

interface ManualExpense {
  id: string;
  date: string;
  category: string;
  description: string;
  amount: number;
  paymentMethod: 'cash' | 'bank';
  source: 'manual';
  isRecurring: boolean;
  frequency?: 'monthly' | 'quarterly' | 'yearly';
  nextDueDate?: string;
  isPaid: boolean;
  paymentDate?: string;
  notes?: string;
}

const getMockManualExpenses = (): ManualExpense[] => [
  {
    id: 'm1',
    date: new Date(currentYear, currentMonth, 3).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.RENT.id,
    description: 'Loyer mensuel',
    amount: 120000,
    paymentMethod: 'bank',
    source: 'manual',
    isRecurring: true,
    frequency: 'monthly',
    nextDueDate: new Date(currentYear, currentMonth + 1, 3).toISOString().slice(0, 10),
    isPaid: true,
    paymentDate: new Date(currentYear, currentMonth, 3).toISOString().slice(0, 10),
    notes: 'Paiement par virement bancaire'
  },
  {
    id: 'm2',
    date: new Date(currentYear, currentMonth, 8).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.UTILITIES.id,
    description: 'Facture Électricité',
    amount: 24800,
    paymentMethod: 'bank',
    source: 'manual',
    isRecurring: true,
    frequency: 'monthly',
    nextDueDate: new Date(currentYear, currentMonth + 1, 8).toISOString().slice(0, 10),
    isPaid: true,
    paymentDate: new Date(currentYear, currentMonth, 9).toISOString().slice(0, 10)
  },
  {
    id: 'm3',
    date: new Date(currentYear, currentMonth, 10).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.UTILITIES.id,
    description: 'Internet',
    amount: 8500,
    paymentMethod: 'cash',
    source: 'manual',
    isRecurring: true,
    frequency: 'monthly',
    nextDueDate: new Date(currentYear, currentMonth + 1, 10).toISOString().slice(0, 10),
    isPaid: false
  },
  {
    id: 'm4',
    date: new Date(currentYear, currentMonth, 15).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.OTHER.id,
    description: 'Assurance restaurant',
    amount: 45000,
    paymentMethod: 'bank',
    source: 'manual',
    isRecurring: true,
    frequency: 'quarterly',
    nextDueDate: new Date(currentYear, currentMonth + 3, 15).toISOString().slice(0, 10),
    isPaid: true,
    paymentDate: new Date(currentYear, currentMonth, 14).toISOString().slice(0, 10)
  },
  {
    id: 'm5',
    date: new Date(currentYear, currentMonth, 18).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.OTHER.id,
    description: 'Réparation climatisation',
    amount: 12000,
    paymentMethod: 'cash',
    source: 'manual',
    isRecurring: false,
    isPaid: true,
    paymentDate: new Date(currentYear, currentMonth, 18).toISOString().slice(0, 10)
  },
  {
    id: 'm6',
    date: new Date(currentYear, currentMonth, 20).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.OTHER.id,
    description: 'Publicité Facebook',
    amount: 7500,
    paymentMethod: 'bank',
    source: 'manual',
    isRecurring: true,
    frequency: 'monthly',
    nextDueDate: new Date(currentYear, currentMonth + 1, 20).toISOString().slice(0, 10),
    isPaid: false
  },
  {
    id: 'm7',
    date: new Date(currentYear, currentMonth - 1, 25).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.OTHER.id,
    description: 'Entretien matériel de cuisine',
    amount: 18000,
    paymentMethod: 'bank',
    source: 'manual',
    isRecurring: true,
    frequency: 'quarterly',
    nextDueDate: new Date(currentYear, currentMonth + 2, 25).toISOString().slice(0, 10),
    isPaid: true,
    paymentDate: new Date(currentYear, currentMonth - 1, 25).toISOString().slice(0, 10)
  }
];

export default function ManualExpensesTab() {
  const [manualExpenses, setManualExpenses] = useState<ManualExpense[]>(getMockManualExpenses());
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [filterPaidStatus, setFilterPaidStatus] = useState<'all' | 'paid' | 'unpaid'>('all');
  const [filterRecurring, setFilterRecurring] = useState<'all' | 'one-time' | 'recurring'>('all');
  const [editingExpense, setEditingExpense] = useState<ManualExpense | null>(null);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [showExpenseForm, setShowExpenseForm] = useState(false);

  // Filtering logic
  const filteredManualExpenses = manualExpenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !categoryFilter || expense.category === categoryFilter;
    const matchesPaidStatus = 
      filterPaidStatus === 'all' || 
      (filterPaidStatus === 'paid' && expense.isPaid) || 
      (filterPaidStatus === 'unpaid' && !expense.isPaid);
    const matchesRecurring = 
      filterRecurring === 'all' || 
      (filterRecurring === 'recurring' && expense.isRecurring) || 
      (filterRecurring === 'one-time' && !expense.isRecurring);
    return matchesSearch && matchesCategory && matchesPaidStatus && matchesRecurring;
  });

  // Format frequency for display
  const formatFrequency = (frequency?: string) => {
    if (!frequency) return '-';
    switch (frequency) {
      case 'monthly': return 'Mensuel';
      case 'quarterly': return 'Trimestriel';
      case 'yearly': return 'Annuel';
      default: return frequency;
    }
  };

  // Check if an expense is due soon (within 7 days)
  const isDueSoon = (expense: ManualExpense) => {
    if (!expense.nextDueDate || expense.isPaid) return false;
    const dueDate = new Date(expense.nextDueDate);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays >= 0 && diffDays <= 7;
  };

  // Check if an expense is overdue
  const isOverdue = (expense: ManualExpense) => {
    if (!expense.nextDueDate || expense.isPaid) return false;
    const dueDate = new Date(expense.nextDueDate);
    const today = new Date();
    return dueDate < today;
  };

  // Handle marking an expense as paid
  const handleMarkAsPaid = (expenseId: string) => {
    setManualExpenses(prev => prev.map(exp => exp.id === expenseId ? { ...exp, isPaid: true, paymentDate: new Date().toISOString().slice(0, 10) } : exp));
  };

  // Handle expense edit
  const handleEditExpense = (expense: ManualExpense) => {
    setEditingExpense(expense);
    setOpenEditDialog(true);
  };

  // Handle expense delete
  const handleDeleteExpense = (expenseId: string) => {
    setManualExpenses(prev => prev.filter(exp => exp.id !== expenseId));
    setShowDeleteConfirm(null);
  };

  // Handle adding a new expense
  const handleAddExpense = () => {
    setEditingExpense(null);
    setShowExpenseForm(true);
  };

  // Get status badge for an expense
  const getStatusBadge = (expense: ManualExpense) => {
    if (expense.isPaid) {
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs font-normal">
          <CheckIcon className="h-3 w-3 mr-1" />
          Payé
        </Badge>
      );
    }
    if (isOverdue(expense)) {
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 text-xs font-normal">
          <AlertCircleIcon className="h-3 w-3 mr-1" />
          En retard
        </Badge>
      );
    }
    if (isDueSoon(expense)) {
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-xs font-normal">
          <ClockIcon className="h-3 w-3 mr-1" />
          Bientôt dû
        </Badge>
      );
    }
    return (
      <Badge variant="outline" className="text-xs font-normal">
        Non payé
      </Badge>
    );
  };

  // Render
  return (
    <div className="space-y-6">
      {/* Header Section - 72px height */}
      <div className="h-[72px] flex flex-col justify-center">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-semibold tracking-tight">Dépenses Manuelles et Récurrentes</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Gérez vos dépenses manuelles et récurrentes, comme le loyer, les factures, et autres frais généraux
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs font-normal">
              {filteredManualExpenses.length} dépenses
            </Badge>
            
            <Button className="h-9 px-3" onClick={handleAddExpense}>
              <PlusIcon className="h-3.5 w-3.5 mr-2" />
              <span className="text-sm">Nouvelle dépense</span>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Filter Bar - 60px height */}
      <div className="h-[60px] flex items-center">
        <div className="flex-1 border rounded-lg px-4 py-3">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <SearchIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Filtres</span>
            </div>
            
            <div className="flex items-center gap-3 flex-1">
              <div className="relative flex-1 max-w-xs">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Rechercher..."
                  className="pl-8 h-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={categoryFilter || 'all'} onValueChange={(value) => setCategoryFilter(value === 'all' ? null : value)}>
                <SelectTrigger className="h-9 w-[120px]">
                  <SelectValue placeholder="Catégorie" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes</SelectItem>
                  {Object.values(EXPENSE_CATEGORIES).map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filterPaidStatus} onValueChange={(value) => setFilterPaidStatus(value as 'all' | 'paid' | 'unpaid')}>
                <SelectTrigger className="h-9 w-[120px]">
                  <SelectValue placeholder="Statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous</SelectItem>
                  <SelectItem value="paid">
                    <div className="flex items-center gap-2">
                      <CheckIcon className="h-3 w-3 text-green-600" />
                      Payés
                    </div>
                  </SelectItem>
                  <SelectItem value="unpaid">
                    <div className="flex items-center gap-2">
                      <ClockIcon className="h-3 w-3 text-amber-600" />
                      Non payés
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterRecurring} onValueChange={(value) => setFilterRecurring(value as 'all' | 'one-time' | 'recurring')}>
                <SelectTrigger className="h-9 w-[120px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous</SelectItem>
                  <SelectItem value="recurring">
                    <div className="flex items-center gap-2">
                      <RepeatIcon className="h-3 w-3 text-blue-600" />
                      Récurrents
                    </div>
                  </SelectItem>
                  <SelectItem value="one-time">
                    <div className="flex items-center gap-2">
                      <TagIcon className="h-3 w-3 text-gray-600" />
                      Ponctuels
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="border rounded-lg">
        <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent border-b">
                <TableHead className="w-[14%] font-medium">Description</TableHead>
                <TableHead className="w-[12%] font-medium">Montant</TableHead>
                <TableHead className="w-[12%] font-medium">Catégorie</TableHead>
                <TableHead className="w-[12%] font-medium">Fréquence</TableHead>
                <TableHead className="w-[12%] font-medium">Prochaine échéance</TableHead>
                <TableHead className="w-[12%] font-medium">Mode</TableHead>
                <TableHead className="w-[12%] font-medium">Statut</TableHead>
                <TableHead className="w-[14%] font-medium">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredManualExpenses.length > 0 ? (
                filteredManualExpenses.map((expense, index) => {
                  const category = Object.values(EXPENSE_CATEGORIES).find(c => c.id === expense.category);
                  return (
                    <TableRow key={expense.id} className={cn(
                      "group transition-colors",
                      index !== filteredManualExpenses.length - 1 && "border-b",
                      isOverdue(expense) && !expense.isPaid && "bg-red-50/30 hover:bg-red-50/50",
                      isDueSoon(expense) && !expense.isPaid && "bg-amber-50/30 hover:bg-amber-50/50"
                    )}>
                      <TableCell className="py-3">
                        <div className="font-medium">{expense.description}</div>
                        {expense.notes && (
                          <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                            {expense.notes}
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium py-3">
                        {formatCurrency(expense.amount)}
                      </TableCell>
                      <TableCell className="py-3">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: category?.color }}
                          />
                          <span className="text-sm">{category?.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="py-3">
                        {expense.isRecurring ? (
                          <div className="flex items-center gap-1">
                            <RepeatIcon className="h-3 w-3 text-muted-foreground" />
                            <span className="text-sm">{formatFrequency(expense.frequency)}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">Ponctuel</span>
                        )}
                      </TableCell>
                      <TableCell className="py-3">
                        {expense.nextDueDate ? (
                          <span className="text-sm">
                            {new Date(expense.nextDueDate).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' })}
                          </span>
                        ) : (
                          <span className="text-muted-foreground text-sm">-</span>
                        )}
                      </TableCell>
                      <TableCell className="py-3">
                        <Badge variant={expense.paymentMethod === 'cash' ? 'secondary' : 'outline'} className="text-xs font-normal">
                          {expense.paymentMethod === 'cash' ? 'Caisse' : 'Banque'}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-3">
                        {getStatusBadge(expense)}
                      </TableCell>
                      <TableCell className="py-3">
                        <div className="flex items-center gap-1">
                          {!expense.isPaid && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="h-7 w-7 text-green-600 hover:text-green-700 hover:bg-green-100"
                                    onClick={() => handleMarkAsPaid(expense.id)}
                                  >
                                    <CheckIcon className="h-3.5 w-3.5" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="text-xs">Marquer comme payé</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-7 w-7"
                                  onClick={() => handleEditExpense(expense)}
                                >
                                  <PencilIcon className="h-3.5 w-3.5" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-xs">Modifier</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-7 w-7 text-red-600 hover:text-red-700 hover:bg-red-100"
                                  onClick={() => setShowDeleteConfirm(expense.id)}
                                >
                                  <TrashIcon className="h-3.5 w-3.5" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-xs">Supprimer</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-10 text-muted-foreground border-0">
                    <div className="flex flex-col items-center gap-3">
                      <div className="p-4 border-2 border-dashed border-muted rounded-lg">
                        <SearchIcon className="h-6 w-6 text-muted-foreground opacity-40" />
                      </div>
                      <div className="space-y-1 text-center">
                        <div className="font-medium">Aucune dépense trouvée</div>
                        <div className="text-xs">Essayez de modifier vos filtres ou ajoutez une nouvelle dépense</div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
      </div>
      
      {/* Add Expense Dialog */}
      <Dialog open={showExpenseForm} onOpenChange={setShowExpenseForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Nouvelle dépense</DialogTitle>
          </DialogHeader>
          <ExpenseForm
            onSubmit={(data) => {
              setManualExpenses(prev => [
                ...prev,
                {
                  ...data,
                  id: `m${Date.now()}`,
                  date: data.date.toISOString().slice(0, 10),
                  source: 'manual',
                  isPaid: data.isPaid ?? false,
                  paymentDate: data.isPaid ? data.date.toISOString().slice(0, 10) : undefined,
                  nextDueDate: data.isRecurring ? (() => {
                    const d = new Date(data.date);
                    if (data.frequency === 'monthly') d.setMonth(d.getMonth() + 1);
                    if (data.frequency === 'quarterly') d.setMonth(d.getMonth() + 3);
                    if (data.frequency === 'yearly') d.setFullYear(d.getFullYear() + 1);
                    return d.toISOString().slice(0, 10);
                  })() : undefined,
                  paymentMethod: data.paymentMethod === 'bank' ? 'bank' : 'cash',
                }
              ]);
              setShowExpenseForm(false);
            }}
            onCancel={() => setShowExpenseForm(false)}
            initialData={undefined}
            title="Nouvelle dépense"
            submitLabel="Ajouter"
          />
        </DialogContent>
      </Dialog>
      
      {/* Edit Expense Dialog */}
      <Dialog open={openEditDialog} onOpenChange={setOpenEditDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Modifier la dépense</DialogTitle>
          </DialogHeader>
          {editingExpense && (
            <ExpenseForm
              onSubmit={(data) => {
                setManualExpenses(prev => prev.map(exp =>
                  exp.id === editingExpense.id
                    ? {
                        ...exp,
                        ...data,
                        date: data.date.toISOString().slice(0, 10),
                        nextDueDate: data.isRecurring ? (() => {
                          const d = new Date(data.date);
                          if (data.frequency === 'monthly') d.setMonth(d.getMonth() + 1);
                          if (data.frequency === 'quarterly') d.setMonth(d.getMonth() + 3);
                          if (data.frequency === 'yearly') d.setFullYear(d.getFullYear() + 1);
                          return d.toISOString().slice(0, 10);
                        })() : undefined,
                        paymentMethod: data.paymentMethod === 'bank' ? 'bank' : 'cash',
                      }
                    : exp
                ));
                setOpenEditDialog(false);
              }}
              onCancel={() => setOpenEditDialog(false)}
              initialData={{
                ...editingExpense,
                date: new Date(editingExpense.date),
              }}
              title="Modifier la dépense"
              submitLabel="Enregistrer"
            />
          )}
        </DialogContent>
      </Dialog>
      
      {/* Dialog for expense deletion confirmation */}
      <Dialog open={!!showDeleteConfirm} onOpenChange={() => setShowDeleteConfirm(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Êtes-vous sûr de vouloir supprimer cette dépense ? Cette action est irréversible.</p>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                Annuler
              </Button>
              <Button 
                variant="destructive" 
                onClick={() => showDeleteConfirm && handleDeleteExpense(showDeleteConfirm)}
              >
                Supprimer
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 