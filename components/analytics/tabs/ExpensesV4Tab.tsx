"use client"

import React, { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { formatCurrency } from '@/lib/utils/currency';
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  CircleDollarSignIcon,
  SearchIcon,
  BarChart3Icon,
  CalendarIcon,
  PlusIcon,
  PackageIcon,
  HomeIcon,
  ZapIcon,
  UsersIcon,
  TagIcon,
  FilterIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  DollarSignIcon,
  ChevronsUpDownIcon,
  CheckIcon,
  InfoIcon,
  ClockIcon,
  PencilIcon,
  XIcon,
  AlertCircleIcon,
  RepeatIcon,
  TrashIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';
import ExpenseForm from '@/app/components/finance/ExpenseForm';
import { Tabs as SubTabs, TabsContent as SubTabsContent, TabsList as SubTabsList, TabsTrigger as SubTabsTrigger } from "@/components/ui/tabs";
import ManualExpensesTab from './ManualExpensesTab';

// Define interfaces
interface Expense {
  id: string;
  date: string;
  category: string;
  description: string;
  amount: number;
  paymentMethod: 'cash' | 'bank';
  source: 'staff' | 'supplier' | 'expense' | 'manual';
}

interface CategoryData {
  id: string;
  name: string;
  color: string;
  icon: React.ElementType;
}

interface ExpenseCategory {
  id: string;
  name: string;
  amount: number;
  percentage: number;
  color: string;
  Icon: React.ElementType;
}

// Mock data for the MVP
const EXPENSE_CATEGORIES: Record<string, CategoryData> = {
  STAFF: {
    id: 'staff',
    name: 'Personnel',
    color: 'hsl(230, 85%, 55%)',
    icon: UsersIcon
  },
  SUPPLIERS: {
    id: 'suppliers',
    name: 'Fournisseurs',
    color: 'hsl(160, 85%, 45%)',
    icon: PackageIcon
  },
  RENT: {
    id: 'rent',
    name: 'Loyer',
    color: 'hsl(340, 85%, 55%)',
    icon: HomeIcon
  },
  UTILITIES: {
    id: 'utilities',
    name: 'Services',
    color: 'hsl(45, 85%, 55%)',
    icon: ZapIcon
  },
  OTHER: {
    id: 'other',
    name: 'Autres',
    color: 'hsl(270, 85%, 55%)',
    icon: TagIcon
  }
};

// Get the current year and month
const now = new Date();
const currentYear = now.getFullYear();
const currentMonth = now.getMonth();

// Mock function to get expenses (dates set to current month)
const getMockExpenses = (): Expense[] => [
  {
    id: '1',
    date: new Date(currentYear, currentMonth, 1).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.STAFF.id,
    description: 'Salaire Ahmed',
    amount: 45000,
    paymentMethod: 'bank',
    source: 'staff'
  },
  {
    id: '2',
    date: new Date(currentYear, currentMonth, 2).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.STAFF.id,
    description: 'Salaire Mohamed',
    amount: 42000,
    paymentMethod: 'bank',
    source: 'staff'
  },
  {
    id: '3',
    date: new Date(currentYear, currentMonth, 3).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.SUPPLIERS.id,
    description: 'Fournitures Alimco',
    amount: 35000,
    paymentMethod: 'bank',
    source: 'supplier'
  },
  {
    id: '4',
    date: new Date(currentYear, currentMonth, 5).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.RENT.id,
    description: 'Loyer',
    amount: 120000,
    paymentMethod: 'bank',
    source: 'expense'
  },
  {
    id: '5',
    date: new Date(currentYear, currentMonth, 8).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.UTILITIES.id,
    description: 'Facture Électricité',
    amount: 24800,
    paymentMethod: 'bank',
    source: 'expense'
  },
  {
    id: '6',
    date: new Date(currentYear, currentMonth, 10).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.UTILITIES.id,
    description: 'Internet',
    amount: 8500,
    paymentMethod: 'cash',
    source: 'expense'
  },
  {
    id: '7',
    date: new Date(currentYear, currentMonth, 15).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.SUPPLIERS.id,
    description: 'Viande El Azhar',
    amount: 48000,
    paymentMethod: 'cash',
    source: 'supplier'
  },
  {
    id: '8',
    date: new Date(currentYear, currentMonth, 18).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.OTHER.id,
    description: 'Réparation climatisation',
    amount: 12000,
    paymentMethod: 'cash',
    source: 'expense'
  },
  {
    id: '9',
    date: new Date(currentYear, currentMonth, 22).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.STAFF.id,
    description: 'Avance Karim',
    amount: 5000,
    paymentMethod: 'cash',
    source: 'staff'
  },
  {
    id: '10',
    date: new Date(currentYear, currentMonth, 25).toISOString().slice(0, 10),
    category: EXPENSE_CATEGORIES.SUPPLIERS.id,
    description: 'Produits de nettoyage',
    amount: 6500,
    paymentMethod: 'cash',
    source: 'supplier'
  }
];

// Get expenses by category function
const getExpensesByCategory = (expenses: Expense[]): ExpenseCategory[] => {
  return Object.values(EXPENSE_CATEGORIES).map(category => {
    const categoryExpenses = expenses.filter(expense => expense.category === category.id);
    const total = categoryExpenses.reduce((sum: number, expense: Expense) => sum + expense.amount, 0);
    const percentage = expenses.length > 0 
      ? (total / expenses.reduce((sum: number, expense: Expense) => sum + expense.amount, 0)) * 100 
      : 0;

    return {
      id: category.id,
      name: category.name,
      amount: total,
      percentage,
      color: category.color,
      Icon: category.icon
    };
  }).sort((a, b) => b.amount - a.amount);
};

interface DateSelector {
  from: Date;
  to?: Date;
}

interface ExpensesV4TabProps {
  dateRange?: DateRange;
}

export default function ExpensesV4Tab({ dateRange }: ExpensesV4TabProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [expenses] = useState<Expense[]>(getMockExpenses());
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [dateSelector, setDateSelector] = useState<DateSelector>({
    from: new Date(currentYear, currentMonth, 1),
    to: new Date(currentYear, currentMonth + 1, 0)
  });
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [activeSubTab, setActiveSubTab] = useState('analytics');
  const [currentPage, setCurrentPage] = useState(1);

  // Filter expenses based on search, date and category
  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchQuery.toLowerCase());
    const expenseDate = new Date(expense.date);
    const matchesDate = (!dateSelector.from || expenseDate >= dateSelector.from) && 
                           (!dateSelector.to || expenseDate <= dateSelector.to);
    const matchesCategory = !categoryFilter || expense.category === categoryFilter;
    
    return matchesSearch && matchesDate && matchesCategory;
  });

  // Calculate total expenses and by category
  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  const expensesByCategory = getExpensesByCategory(filteredExpenses);

  // Date formatter
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
  };

  // Format date range for display
  const formatDateRange = () => {
    if (!dateSelector.from) return 'Sélectionner une période';
    
    const from = dateSelector.from.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' });
    const to = dateSelector.to 
      ? dateSelector.to.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })
      : 'Aujourd\'hui';
      
    return `${from} - ${to}`;
  };

  const handleExpenseAdded = (newExpense: any) => {
    // This would call the API to add the expense
    console.log('Adding expense:', newExpense);
    setShowExpenseForm(false);
    // Would refresh expenses here
  };

  // Get a trend indicator (just for visual enhancement)
  const getTrendIndicator = () => {
    return (
      <div className="flex items-center gap-1 text-xs font-medium">
        <ArrowUpIcon className="h-3 w-3 text-emerald-500" />
        <span className="text-emerald-600">3.2%</span>
        <span className="text-muted-foreground">vs. période préc.</span>
      </div>
    );
  };

  // Calculate category indicators
  const getCategoryIcon = (category: ExpenseCategory) => {
    return (
      <div 
        className="p-1.5 rounded-md"
        style={{ 
          backgroundColor: `${category.color}15`
        }}
      >
        <category.Icon
          className="h-3.5 w-3.5"
          style={{ color: category.color }}
        />
      </div>
    );
  };

  // Pagination logic
  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredExpenses.length / itemsPerPage);
  const paginatedExpenses = filteredExpenses.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, categoryFilter, dateSelector]);

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h2 className="text-xl font-semibold tracking-tight">Dépenses</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Gestion unifiée des dépenses - Personnel, Fournisseurs et charges opérationnelles
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button 
                variant="outline" 
                className="h-9 px-3"
              >
                <CalendarIcon className="h-3.5 w-3.5 mr-2" />
                <span className="text-sm">{formatDateRange()}</span>
                <ChevronsUpDownIcon className="ml-2 h-3.5 w-3.5 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateSelector.from}
                selected={{ from: dateSelector.from, to: dateSelector.to }}
                onSelect={(range) => {
                  if (range && range.from) {
                    setDateSelector({ 
                      from: range.from, 
                      to: range.to 
                    });
                    setIsCalendarOpen(false);
                  }
                }}
              />
            </PopoverContent>
          </Popover>
          
          <Dialog open={!!showExpenseForm} onOpenChange={setShowExpenseForm}>
            <DialogTrigger asChild>
              <Button className="h-9 px-3">
                <PlusIcon className="h-3.5 w-3.5 mr-2" />
                <span className="text-sm">Ajouter</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl">
              <DialogHeader>
                <DialogTitle>Nouvelle dépense</DialogTitle>
              </DialogHeader>
              <ExpenseForm 
                onSubmit={handleExpenseAdded} 
                onCancel={() => setShowExpenseForm(false)}
                initialData={undefined}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Sub-tabs */}
      <SubTabs defaultValue="analytics" value={activeSubTab} onValueChange={setActiveSubTab} className="space-y-6">
        <SubTabsList className="grid w-full grid-cols-2">
          <SubTabsTrigger value="analytics" className="flex items-center gap-2 h-9">
            <BarChart3Icon className="h-4 w-4" />
            <span>Analytiques</span>
          </SubTabsTrigger>
          <SubTabsTrigger value="manual-recurring" className="flex items-center gap-2 h-9">
            <ClockIcon className="h-4 w-4" />
            <span>Dépenses Manuelles / Récurrentes</span>
          </SubTabsTrigger>
        </SubTabsList>
        
        {/* Analytics Sub-tab Content */}
        <SubTabsContent value="analytics" className="space-y-6">
          {/* Top Summary Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Total Expenses Card */}
            <div className="lg:col-span-2 border rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <CircleDollarSignIcon className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Total des Dépenses</h3>
                  <div className="text-2xl font-bold">{formatCurrency(totalExpenses)}</div>
                </div>
              </div>
              <div className="mt-4">{getTrendIndicator()}</div>
            </div>

            {/* Category Breakdown */}
            <div className="lg:col-span-3 border rounded-lg p-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-4">Répartition par Catégorie</h3>
              <div className="grid grid-cols-2 gap-4">
                {expensesByCategory.slice(0, 4).map((category) => (
                  <div key={category.id} className="flex items-center gap-3">
                    <div 
                      className="p-1.5 rounded-md"
                      style={{ backgroundColor: `${category.color}15` }}
                    >
                      <category.Icon
                        className="h-4 w-4"
                        style={{ color: category.color }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium truncate">{category.name}</p>
                        <p className="text-sm font-semibold">{formatCurrency(category.amount)}</p>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="w-full bg-muted h-1 rounded-full overflow-hidden flex-1">
                          <div 
                            className="h-full rounded-full transition-all" 
                            style={{ 
                              width: `${Math.max(category.percentage, 2)}%`,
                              backgroundColor: category.color
                            }}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground w-8 text-right">
                          {category.percentage.toFixed(0)}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Filter Bar */}
          <div className="flex items-center justify-between gap-4 p-4 border rounded-lg">
            <div className="flex items-center gap-4 flex-1">
              <div className="flex items-center gap-2">
                <FilterIcon className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Filtres</span>
              </div>
              
              <div className="flex items-center gap-3 flex-1">
                <div className="relative flex-1 max-w-xs">
                  <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Rechercher..."
                    className="pl-8 h-9"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={categoryFilter || 'all'} onValueChange={(value) => setCategoryFilter(value === 'all' ? null : value)}>
                  <SelectTrigger className="h-9 w-[140px]">
                    <SelectValue placeholder="Catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    {Object.values(EXPENSE_CATEGORIES).map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: category.color }}
                          />
                          {category.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Badge variant="outline" className="text-xs font-normal">
              {filteredExpenses.length} dépenses
            </Badge>
          </div>
          
          {/* Expenses History Table */}
          <div className="border rounded-lg">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-muted-foreground" />
                  <h3 className="text-sm font-medium text-muted-foreground">Historique des Dépenses</h3>
                </div>
                
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>Page {currentPage} sur {totalPages}</span>
                </div>
              </div>
            </div>
            
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent border-b">
                  <TableHead className="w-24 font-medium">Date</TableHead>
                  <TableHead className="font-medium">Description</TableHead>
                  <TableHead className="w-32 font-medium">Catégorie</TableHead>
                  <TableHead className="text-right w-28 font-medium">Montant</TableHead>
                  <TableHead className="w-24 font-medium">Mode</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedExpenses.length > 0 ? (
                  paginatedExpenses.map((expense, index) => {
                    const category = Object.values(EXPENSE_CATEGORIES).find(c => c.id === expense.category);
                    return (
                      <TableRow 
                        key={expense.id} 
                        className={cn(
                          "group transition-colors",
                          index !== paginatedExpenses.length - 1 && "border-b"
                        )}
                      >
                        <TableCell className="py-3 text-xs font-medium">
                          {formatDate(expense.date)}
                        </TableCell>
                        <TableCell className="py-3">
                          <div className="font-medium text-sm">{expense.description}</div>
                          <div className="text-xs text-muted-foreground capitalize">
                            {expense.source}
                          </div>
                        </TableCell>
                        <TableCell className="py-3">
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: category?.color }}
                            />
                            <span className="text-xs">{category?.name}</span>
                          </div>
                        </TableCell>
                        <TableCell className="py-3 text-right">
                          <span className="font-medium text-sm">
                            {formatCurrency(expense.amount)}
                          </span>
                        </TableCell>
                        <TableCell className="py-3">
                          <Badge 
                            variant={expense.paymentMethod === 'cash' ? 'secondary' : 'outline'} 
                            className="text-xs font-normal"
                          >
                            {expense.paymentMethod === 'cash' ? 'Caisse' : 'Banque'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-10 text-muted-foreground border-0">
                      <div className="flex flex-col items-center gap-3">
                        <div className="p-4 border-2 border-dashed border-muted rounded-lg">
                          <SearchIcon className="h-6 w-6 text-muted-foreground opacity-40" />
                        </div>
                        <div className="space-y-1 text-center">
                          <div className="font-medium">Aucune dépense trouvée</div>
                          <div className="text-xs">Essayez de modifier vos filtres ou ajoutez une nouvelle dépense</div>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="p-4 border-t">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    Affichage de {((currentPage - 1) * itemsPerPage) + 1} à {Math.min(currentPage * itemsPerPage, filteredExpenses.length)} sur {filteredExpenses.length} dépenses
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="h-8 px-3"
                    >
                      <ArrowDownIcon className="h-3 w-3 rotate-90" />
                      Précédent
                    </Button>
                    
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNum)}
                            className="h-8 w-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="h-8 px-3"
                    >
                      Suivant
                      <ArrowUpIcon className="h-3 w-3 rotate-90" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </SubTabsContent>
        
        <SubTabsContent value="manual-recurring" className="space-y-6">
          <ManualExpensesTab />
        </SubTabsContent>
      </SubTabs>
    </div>
  );
} 