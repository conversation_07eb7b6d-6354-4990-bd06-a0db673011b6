"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  BarChart4Icon, 
  RefreshCwIcon,
  AlertCircleIcon,
  ClockIcon 
} from 'lucide-react';

interface TabFallbackProps {
  tabName: string;
  error?: string;
  onRetry?: () => void;
  isComingSoon?: boolean;
}

export default function TabFallback({ tabName, error, onRetry, isComingSoon = false }: TabFallbackProps) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="text-center py-8">
          <div className="flex flex-col items-center gap-4">
            <div className={`p-3 rounded-full ${
              isComingSoon 
                ? "bg-blue-50 dark:bg-blue-900/20" 
                : "bg-amber-50 dark:bg-amber-900/20"
            }`}>
              {isComingSoon ? (
                <ClockIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              ) : (
                <AlertCircleIcon className="h-8 w-8 text-amber-600 dark:text-amber-400" />
              )}
            </div>
            <div>
              <CardTitle className="text-lg">
                {isComingSoon ? `${tabName} - Bientôt disponible` : `${tabName} - Fonctionnalité en développement`}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-2">
                {isComingSoon 
                  ? "Cette fonctionnalité arrive bientôt. Restez connecté pour les mises à jour!"
                  : "Cette section est actuellement en cours de développement et sera bientôt disponible."
                }
              </p>
              {error && (
                <p className="text-xs text-red-600 mt-2 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                  Erreur technique: {error}
                </p>
              )}
            </div>
            {onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="mt-2"
              >
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Réessayer
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Mock preview cards to show what's coming */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="opacity-50 pointer-events-none">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <BarChart4Icon className="h-4 w-4" />
              {isComingSoon ? "Aperçu à venir" : "Aperçu des données"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded animate-pulse w-3/4" />
            </div>
          </CardContent>
        </Card>

        <Card className="opacity-50 pointer-events-none">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <BarChart4Icon className="h-4 w-4" />
              {isComingSoon ? "Métriques à venir" : "Métriques clés"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
            </div>
          </CardContent>
        </Card>

        <Card className="opacity-50 pointer-events-none">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-muted-foreground flex items-center gap-2">
              <BarChart4Icon className="h-4 w-4" />
              {isComingSoon ? "Analyses à venir" : "Analyses détaillées"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded animate-pulse w-4/5" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}