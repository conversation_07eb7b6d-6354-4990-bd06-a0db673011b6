"use client"

// Implementation using real data from v4 database
// - Uses useCOGSV4() for recipes, menu items, and production batches
// - Uses useStockV4() for inventory, stock counts, and waste logs
// - Calculates COGS KPIs from real production and waste data
// - Processes and displays real-time inventory alerts
// - Shows real stock count accuracy and wastage breakdown

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DateRange } from 'react-day-picker';
import { formatCurrency } from '@/lib/utils/currency';
import { 
  AreaChartIcon, 
  ShoppingBagIcon, 
  AlertCircleIcon, 
  ClockIcon, 
  PackageIcon,
  InfoIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  TimerIcon,
  Package2Icon,
  AlertTriangleIcon,
  CircleDollarSignIcon,
  Trash2Icon,
  ClipboardCheckIcon,
  RefreshCcwIcon,
  CheckIcon,
  Loader2Icon
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import KpiMetricCard from "@/components/analytics/KpiMetricCard";
import PeakHoursHeatmap from "@/components/analytics/PeakHoursHeatmap";
import WeeklyProductionHeatmap from "@/components/analytics/WeeklyProductionHeatmap";
// Import hooks for real data
import { useCOGSV4 } from '@/lib/hooks/useCOGSV4';
import { useStockV4 } from '@/lib/hooks/useStockV4';

// Types for inventory/COGS data
interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  minLevel: number;
  costPerUnit: number;
  expiryDate?: string;
  totalValue: number;
  daysLeft?: number;
  lastPurchaseDate?: string;
  turnoverRate?: number; // New field for turnover rate
  daysOfInventory?: number; // New field for days of inventory
}

// Adapt to work with actual WasteLog from the database
interface WasteLog {
  id: string;
  stockItemId: string;
  quantity: number;
  reason: 'expired' | 'damaged' | 'spilled' | 'contaminated' | 'cooking_error' | 'returned' | 'other';
  notes?: string;
  date: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

interface ProductionBatch {
  _id: string;
  id?: string; // Added for backward compatibility with WeeklyProductionHeatmap
  type: 'production-batch';
  subRecipeId: string;
  batchSize: number;
  producedQuantity: number;
  date: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
  costPerUnit?: number;
  subRecipeName?: string; // For UI display
  yieldPercentage?: number; 
  totalCost?: number;
}

// New interfaces for stock count
interface StockCount {
  id: string;
  name: string;
  date: string;
  status: 'draft' | 'in_progress' | 'completed';
  countType: 'full' | 'partial' | 'cycle';
  countArea: 'all' | 'kitchen' | 'bar' | 'storage';
  notes?: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
  accuracy: number; // Calculated field
}

interface StockCountItem {
  id: string;
  stockCountId: string;
  stockItemId: string;
  itemName: string; // For UI display
  theoreticalQuantity: number;
  countedQuantity: number;
  variance: number;
  varianceValue: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Helper to generate random dates within range
const getRandomDate = (daysBack: number): string => {
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * daysBack));
  return date.toISOString();
};

// Helper functions for working with real data
const calculateDaysLeft = (item: any, wasteLogsList: any[]): number | undefined => {
  // Skip if no waste logs or item doesn't exist
  if (!wasteLogsList || wasteLogsList.length === 0 || !item) {
    // Without waste data, provide simple estimate based on min level
    return item.quantity > 0 ? 
      (item.minLevel > 0 ? 
        Math.round(item.quantity / (item.minLevel * 0.1)) : 
        30) : 
      0;
  }
  
  // Look at waste logs to estimate daily usage
  const itemWasteLogs = wasteLogsList.filter(log => log.stockItemId === item.id);
  
  if (itemWasteLogs.length === 0) {
    // No waste logs for this specific item
    return item.quantity > 0 ? 
      (item.minLevel > 0 ? 
        Math.round(item.quantity / (item.minLevel * 0.1)) : 
        30) : 
      0;
  }
  
  // Calculate average daily usage based on waste logs
  const oldestLog = new Date(Math.min(...itemWasteLogs.map(log => new Date(log.date).getTime())));
  const today = new Date();
  const daysSinceOldest = Math.max(1, Math.round((today.getTime() - oldestLog.getTime()) / (1000 * 60 * 60 * 24)));
  
  const totalUsage = itemWasteLogs.reduce((sum: number, log) => sum + (log.quantity || 0), 0);
  const dailyUsage = totalUsage / daysSinceOldest;
  
  // Return days left based on current quantity and daily usage
  return dailyUsage > 0 ? Math.round(item.quantity / dailyUsage) : undefined;
};

const getLastPurchaseDate = (stockItemId: string): string | undefined => {
  // We would normally get this from purchase logs
  // For now, generate a random date or use today
  return stockItemId ? getRandomDate(30) : undefined;
};

const calculateTurnoverRate = (item: any): number => {
  if (!item || !item.category) return 1.0; // Default turnover rate for unknown items
  
  // This would normally be calculated from sales data
  // For now, use a category-based estimate
  const isPerishable = 
    item.category === 'Viandes' || 
    item.category === 'Légumes' || 
    item.category === 'Produits laitiers' || 
    item.category === 'Fruits';
    
  return isPerishable ? 
    2 + Math.random() * 3 : // Perishables turn over 2-5 times per month
    0.5 + Math.random() * 1.5; // Non-perishables turn over 0.5-2 times per month
};

const calculateDaysOfInventory = (item: any): number => {
  if (!item) return 30; // Default 30 days for unknown items
  const turnoverRate = calculateTurnoverRate(item);
  return Math.round(30 / turnoverRate);
};

// Mock data for inventory items
const getMockInventoryItems = (): InventoryItem[] => {
  const categories = ['Viandes', 'Légumes', 'Produits laitiers', 'Épices', 'Fruits', 'Produits secs', 'Boissons'];
  const names = [
    'Poulet', 'Boeuf haché', 'Agneau', 'Tomates', 'Oignons', 'Pommes de terre', 
    'Fromage', 'Lait', 'Crème', 'Paprika', 'Cumin', 'Sel', 'Poivre', 
    'Pommes', 'Oranges', 'Bananes', 'Riz', 'Pâtes', 'Farine', 'Huile d\'olive',
    'Coca Cola', 'Eau minérale', 'Jus d\'orange'
  ];
  
  return names.map((name, index) => {
    const category = categories[Math.floor(index / 4)];
    const quantity = Math.floor(Math.random() * 200) + 20;
    const minLevel = Math.floor(Math.random() * 50) + 10;
    const costPerUnit = Math.floor(Math.random() * 500) + 50;
    const totalValue = quantity * costPerUnit;
    
    // Calculate days until expiry for some items
    const needsExpiry = category === 'Viandes' || category === 'Légumes' || category === 'Produits laitiers' || category === 'Fruits';
    
    // Generate expiry date for perishables
    let expiryDate;
    if (needsExpiry) {
      const date = new Date();
      // Random days in the future (1-30)
      date.setDate(date.getDate() + Math.floor(Math.random() * 30) + 1);
      expiryDate = date.toISOString();
    }
    
    // Calculate days of inventory left based on usage rate
    const usageRate = Math.random() * 5 + 0.5; // Random usage per day
    const daysLeft = Math.floor(quantity / usageRate);
    
    // Last purchase date
    const lastPurchaseDate = getRandomDate(30);
    
    return {
      id: `item-${index}`,
      name,
      category,
      quantity,
      minLevel,
      costPerUnit,
      totalValue,
      daysLeft,
      expiryDate,
      lastPurchaseDate
    };
  });
};

// Mock data for wastage logs
const getMockWasteLogs = (inventoryItems: InventoryItem[]): WasteLog[] => {
  const reasons: ('expired' | 'damaged' | 'spilled' | 'contaminated' | 'cooking_error' | 'returned' | 'other')[] = 
    ['expired', 'damaged', 'spilled', 'contaminated', 'cooking_error', 'returned', 'other'];
  
  const logs: WasteLog[] = [];
  
  // Generate 30-50 waste logs
  const logCount = Math.floor(Math.random() * 20) + 30;
  
  for (let i = 0; i < logCount; i++) {
    // Pick a random inventory item
    const randomItemIndex = Math.floor(Math.random() * inventoryItems.length);
    const item = inventoryItems[randomItemIndex];
    
    // Random quantity wasted (smaller than current quantity)
    const quantity = Math.floor(Math.random() * Math.min(10, item.quantity / 2)) + 1;
    
    // Random reason
    const reason = reasons[Math.floor(Math.random() * reasons.length)];
    
    // Date in the last 30 days
    const date = getRandomDate(30);
    
    // Cost of waste
    const cost = quantity * item.costPerUnit;
    
    logs.push({
      id: `waste-${i}`,
      stockItemId: item.id,
      quantity,
      reason,
      date,
      performedBy: 'Employee ' + Math.floor(Math.random() * 5 + 1),
      createdAt: date,
      updatedAt: date
    });
  }
  
  return logs;
};

// Mock data for production batches
const getMockProductionBatches = (): ProductionBatch[] => {
  const subRecipes = [
    'Sauce Tomate', 'Sauce Béchamel', 'Pâte à Pizza', 'Marinade Poulet', 
    'Crème Pâtissière', 'Bouillon de Légumes', 'Sauce Algérienne', 'Sauce Barbecue'
  ];
  
  const batches: ProductionBatch[] = [];
  
  // Generate 20-30 production batches
  const batchCount = Math.floor(Math.random() * 10) + 20;
  
  for (let i = 0; i < batchCount; i++) {
    // Pick a random sub-recipe
    const subRecipeName = subRecipes[Math.floor(Math.random() * subRecipes.length)];
    
    // Random batch size
    const batchSize = Math.floor(Math.random() * 50) + 10;
    
    // Random yield percentage (80-100%)
    const yieldPercentage = 80 + Math.floor(Math.random() * 21);
    
    // Produced quantity based on yield
    const producedQuantity = Math.floor(batchSize * (yieldPercentage / 100));
    
    // Random cost per unit
    const costPerUnit = Math.floor(Math.random() * 100) + 20;
    
    // Total cost
    const totalCost = producedQuantity * costPerUnit;
    
    // Date in the last 30 days
    const date = getRandomDate(30);
    
    batches.push({
      _id: `batch-${i}`,
      type: 'production-batch',
      subRecipeId: `sr-${i}`,
      batchSize,
      producedQuantity,
      date,
      performedBy: 'Chef ' + Math.floor(Math.random() * 3 + 1),
      createdAt: date,
      updatedAt: date,
      costPerUnit
    });
  }
  
  return batches;
};

// Mock data for heatmap (wastage by day/hour)
const getMockWastageHeatmapData = (realWasteLogs: WasteLog[], stockItemsList: any[]) => {
  // Convert waste logs into format suitable for heatmap
  return realWasteLogs.map(log => {
    const date = new Date(log.date);
    // Calculate cost
    const item = stockItemsList.find(i => i.id === log.stockItemId);
    const cost = item && item.costPerUnit ? item.costPerUnit * log.quantity : 0;
    
    return {
      id: log.id,
      createdAt: log.date,
      status: 'completed',
      total: cost,
      orderType: log.reason // We'll use orderType to store the waste reason
    };
  });
};

// Mock data for stock counts
const getMockStockCounts = (): StockCount[] => {
  const countAreas: ('all' | 'kitchen' | 'bar' | 'storage')[] = ['all', 'kitchen', 'bar', 'storage'];
  const countTypes: ('full' | 'partial' | 'cycle')[] = ['full', 'partial', 'cycle'];
  
  // Generate 3 stock counts
  return Array.from({ length: 3 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i * 10); // One count every 10 days
    
    return {
      id: `sc-${i}`,
      name: `Stock Count ${i + 1}`,
      date: date.toISOString(),
      status: 'completed',
      countType: countTypes[i % countTypes.length],
      countArea: countAreas[i % countAreas.length],
      notes: i === 0 ? 'Monthly full count' : undefined,
      performedBy: `Employee ${i + 1}`,
      createdAt: date.toISOString(),
      updatedAt: date.toISOString(),
      accuracy: 95 - (i * 2) // Mock accuracy decreasing for older counts
    };
  });
};

// Mock data for stock count items
const getMockStockCountItems = (inventoryItems: InventoryItem[], stockCounts: StockCount[]): StockCountItem[] => {
  const countItems: StockCountItem[] = [];
  
  stockCounts.forEach(count => {
    // For each count, create items for a subset of inventory
    const itemsToCount = inventoryItems.slice(0, 10 + Math.floor(Math.random() * 5));
    
    itemsToCount.forEach(item => {
      // Generate random variance (usually small, occasionally larger)
      const variancePercentage = Math.random() < 0.7 
        ? (Math.random() * 0.1) // 70% chance of small variance (0-10%)
        : (Math.random() * 0.3); // 30% chance of larger variance (0-30%)
        
      const variance = Math.floor(item.quantity * variancePercentage);
      const direction = Math.random() > 0.5 ? 1 : -1; // positive or negative variance
      const actualVariance = variance * direction;
      
      countItems.push({
        id: `sci-${count.id}-${item.id}`,
        stockCountId: count.id,
        stockItemId: item.id,
        itemName: item.name,
        theoreticalQuantity: item.quantity,
        countedQuantity: item.quantity + actualVariance,
        variance: actualVariance,
        varianceValue: actualVariance * item.costPerUnit,
        notes: actualVariance !== 0 ? 'Discrepancy found' : undefined,
        createdAt: count.date,
        updatedAt: count.date
      });
    });
  });
  
  return countItems;
};

// Enhanced mock data for inventory items with turnover information
const getEnhancedInventoryItems = (items: InventoryItem[]): InventoryItem[] => {
  return items.map(item => {
    // Calculate mock turnover rate (times per month)
    const turnoverRate = 
      item.category === 'Viandes' || item.category === 'Légumes' || item.category === 'Produits laitiers' || item.category === 'Fruits'
        ? 2 + Math.random() * 3 // Perishables turn over 2-5 times per month
        : 0.5 + Math.random() * 1.5; // Non-perishables turn over 0.5-2 times per month
    
    // Calculate days of inventory
    const daysOfInventory = Math.round(30 / turnoverRate);
    
    return {
      ...item,
      turnoverRate,
      daysOfInventory
    };
  });
};

// ADDED: Helper function for Stock Value by Category using real data
const getStockValueByCategory = (items: any[]) => {
  return items.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = 0;
    }
    acc[item.category] += item.totalValue;
    return acc;
  }, {} as Record<string, number>);
};

// ADDED: Helper function for Mock Recent Purchases
const getMockRecentPurchases = (items: any[]): any[] => {
  const purchases: any[] = [];
  const suppliers = ['Metro Alger', 'SuperGrossiste Oran', 'Ferme Locale Annaba', 'Import Export Sétif'];
  
  // Create up to 5 mock purchases
  const numPurchases = Math.min(items.length, 5);
  const shuffledItems = [...items].sort(() => 0.5 - Math.random()); // Shuffle to get varied items

  for (let i = 0; i < numPurchases; i++) {
    const item = shuffledItems[i];
    const quantityPurchased = Math.floor(Math.random() * (item.minLevel > 0 ? item.minLevel * 0.8 : 10)) + (item.minLevel > 0 ? item.minLevel : 5) ; // Purchase around min level or a base amount
    const purchaseDate = getRandomDate(20); // Within last 20 days
    
    purchases.push({
      id: `purchase-${i}-${item.id}`,
      itemName: item.name,
      category: item.category,
      quantityPurchased,
      costPerUnit: item.costPerUnit, // Assuming current costPerUnit reflects recent purchase price
      totalCost: quantityPurchased * item.costPerUnit,
      purchaseDate,
      supplier: suppliers[i % suppliers.length]
    });
  }
  return purchases.sort((a,b) => new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime());
};

interface CogsTabProps {
  dateRange?: DateRange;
}

export default function CogsTab({ dateRange }: CogsTabProps) {
  // State for current tab
  const [activeTab, setActiveTab] = useState<string>("stock-performance"); // default to stock performance
  // State for selected production batch date (for future feature)
  const [selectedProductionDate, setSelectedProductionDate] = useState<Date | null>(null);
  
  // Get real data from hooks
  const { subRecipes, menuItemRecipes, productionBatches, loading: cogsLoading, error: cogsError } = useCOGSV4();
  const { 
    stockItems, 
    wasteLogs, 
    stockCounts, 
    getStockCountItems, // This is the function from the hook
    isLoading: stockLoading, 
    error: stockError 
  } = useStockV4();
  
  // State for stock count items
  const [stockCountItems, setStockCountItems] = useState<any[]>([]);
  const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
  
  // Check if there's a database error
  const hasError = Boolean(stockError || cogsError);

  // Create a ref for the getStockCountItems function
  const getStockCountItemsRef = useRef(getStockCountItems);

  // Update the ref if getStockCountItems changes
  useEffect(() => {
    getStockCountItemsRef.current = getStockCountItems;
  }, [getStockCountItems]);
  
  // Fetch waste logs and stock counts on component mount
  useEffect(() => {
    // Instead of calling refreshWasteLogs and refreshStockCounts which are creating
    // an infinite loop, we'll just manage our local loading state based on stockLoading
    const handleInitialDataLoading = () => {
      if (!stockLoading) {
        // When stockLoading becomes false, we can also update our local loading state
        console.log("Stock data loaded, setting isDataLoading to false");
        console.log("Data state:", {
          stockItemsCount: stockItems.length,
          wasteLogsCount: wasteLogs ? wasteLogs.length : 0,
          stockCountsCount: stockCounts ? stockCounts.length : 0,
          hasError: hasError
        });
        setIsDataLoading(false);
      } else {
        console.log("Stock is still loading...");
        setIsDataLoading(true);
      }
    };
    
    handleInitialDataLoading();
  }, [stockLoading]); // Only depend on stockLoading, remove other dependencies
  
  // Fetch stock count items when stock counts change
  useEffect(() => {
    const fetchStockCountItems = async () => {
      if (stockCounts && stockCounts.length > 0) {
        // Get the most recent stock count
        const latestCount = [...stockCounts].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )[0];
        
        try {
          // Use the ref'd version of the function
          const items = await getStockCountItemsRef.current(latestCount.id);
          setStockCountItems(items);
        } catch (error) {
          console.error('Error fetching stock count items:', error);
          setStockCountItems([]);
        }
      } else {
        // No stock counts available
        setStockCountItems([]);
      }
    };
    
    if (!stockLoading && stockCounts) {
      fetchStockCountItems();
    }
    // Remove getStockCountItems from dependencies, rely on the ref for its latest version.
    // The effect now only re-runs if stockCounts or stockLoading changes.
  }, [stockCounts, stockLoading]);
  
  // Convert real data to component-compatible format with safely handled empty cases
  const enhancedInventoryItems = stockItems && stockItems.length > 0 
    ? stockItems.map(item => ({
        id: item.id,
        name: item.name,
        category: item.category || 'Non catégorisé',
        quantity: item.quantity || 0,
        minLevel: item.minLevel || 0,
        costPerUnit: item.costPerUnit || 0,
        expiryDate: item.expiryDate,
        totalValue: (item.quantity || 0) * (item.costPerUnit || 0),
        // Calculate estimated days left based on usage from waste logs
        daysLeft: calculateDaysLeft(item, wasteLogs || []),
        lastPurchaseDate: getLastPurchaseDate(item.id),
        turnoverRate: calculateTurnoverRate(item),
        daysOfInventory: calculateDaysOfInventory(item)
      }))
    : [];
    
  // Calculate KPIs
  const calculateKPIs = () => {
    if (!stockItems || stockItems.length === 0) {
      // Return default values for empty data
      return {
        totalInventoryValue: 0,
        totalWasteCost: 0,
        wastePercentage: 0,
        totalProductionCost: 0,
        cogsPercentage: 0,
        itemsBelowMinimumCount: 0,
        itemsExpiringSoonCount: 0,
        avgProductionYield: 95 // Default value
      };
    }
    
    // Total inventory value
    const totalInventoryValue = enhancedInventoryItems.reduce((sum, item) => sum + item.totalValue, 0);
    
    // Total waste cost
    const totalWasteCost = (wasteLogs || []).reduce((sum, log) => {
      // Calculate cost based on the item's cost per unit
      const item = stockItems.find(i => i.id === log.stockItemId);
      const cost = item && item.costPerUnit ? item.costPerUnit * (log.quantity || 0) : 0;
      return sum + cost;
    }, 0);
    
    // Total production cost - calculate based on batch size and cost per unit
    const totalProductionCost = (productionBatches || []).reduce((sum, batch) => {
      // Type assertion to access optional property
      const costPerUnit = (batch as any).costPerUnit || 0;
      const cost = costPerUnit * (batch.producedQuantity || 0);
      return sum + cost;
    }, 0);
    
    // COGS percentage (using mock sales of 1,000,000)
    const mockSales = 1000000;
    const totalCOGS = totalWasteCost + totalProductionCost;
    const cogsPercentage = (totalCOGS / mockSales) * 100;
    
    // Items below minimum level
    const itemsBelowMinimum = enhancedInventoryItems.filter(item => 
      typeof item.minLevel === 'number' && 
      typeof item.quantity === 'number' && 
      item.quantity < item.minLevel
    );
    
    // Items expiring soon (in the next 7 days)
    const today = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);
    
    const itemsExpiringSoon = enhancedInventoryItems.filter(item => {
      if (!item.expiryDate) return false;
      const expiryDate = new Date(item.expiryDate);
      return expiryDate <= nextWeek;
    });
    
    // Production yield average
    const avgProductionYield = productionBatches && productionBatches.length > 0 ?
      productionBatches.reduce((sum, batch) => {
        // Calculate yield percentage based on produced vs batch size
        const yieldPercentage = batch.batchSize > 0 ? ((batch.producedQuantity || 0) / batch.batchSize) * 100 : 100;
        return sum + yieldPercentage;
      }, 0) / productionBatches.length :
      95; // Default if no batches
    
    return {
      totalInventoryValue,
      totalWasteCost,
      wastePercentage: totalCOGS > 0 ? (totalWasteCost / totalCOGS) * 100 : 0,
      totalProductionCost,
      cogsPercentage,
      itemsBelowMinimumCount: itemsBelowMinimum.length,
      itemsExpiringSoonCount: itemsExpiringSoon.length,
      avgProductionYield
    };
  };
  
  // Get wastage by reason for the summarized table
  const getWastageByReason = () => {
    if (!wasteLogs || wasteLogs.length === 0) {
      // Return empty data for initial state
      return [
        { reason: 'expired', count: 0, cost: 0 },
        { reason: 'damaged', count: 0, cost: 0 },
        { reason: 'other', count: 0, cost: 0 }
      ];
    }
    
    const wastageByReason = wasteLogs.reduce((acc, log) => {
      if (!acc[log.reason]) {
        acc[log.reason] = { count: 0, cost: 0 };
      }
      // Calculate cost
      const item = stockItems.find(i => i.id === log.stockItemId);
      const cost = item && item.costPerUnit ? item.costPerUnit * (log.quantity || 0) : 0;
      
      acc[log.reason].count += 1;
      acc[log.reason].cost += cost;
      return acc;
    }, {} as Record<string, { count: number, cost: number }>);
    
    // Convert to array for easier rendering
    return Object.entries(wastageByReason).map(([reason, data]) => ({
      reason,
      count: data.count,
      cost: data.cost
    }));
  };
  
  // Get top wasted items
  const getTopWastedItems = (limit = 5) => {
    if (!wasteLogs || wasteLogs.length === 0 || !stockItems || stockItems.length === 0) {
      return [];
    }
    
    // Aggregate by item
    const wasteByItem = wasteLogs.reduce((acc, log) => {
      // Find the item name
      const stockItem = stockItems.find(i => i.id === log.stockItemId);
      const itemName = stockItem ? stockItem.name : 'Article Inconnu';
      const itemCost = stockItem && stockItem.costPerUnit ? stockItem.costPerUnit * (log.quantity || 0) : 0;
      
      if (!acc[log.stockItemId]) {
        acc[log.stockItemId] = { 
          id: log.stockItemId,
          name: itemName,
          count: 0, 
          quantity: 0,
          cost: 0,
          reasons: {} as Record<string, number>
        };
      }
      
      acc[log.stockItemId].count += 1;
      acc[log.stockItemId].quantity += (log.quantity || 0);
      acc[log.stockItemId].cost += itemCost;
      
      // Count reasons
      if (!acc[log.stockItemId].reasons[log.reason]) {
        acc[log.stockItemId].reasons[log.reason] = 0;
      }
      acc[log.stockItemId].reasons[log.reason] += 1;
      
      return acc;
    }, {} as Record<string, { 
      id: string; 
      name: string; 
      count: number; 
      quantity: number; 
      cost: number; 
      reasons: Record<string, number> 
    }>);
    
    // Convert to array and sort by cost
    return Object.values(wasteByItem)
      .sort((a, b) => b.cost - a.cost)
      .slice(0, limit);
  };
  
  // Get items for inventory alert table (combining low stock and expiring soon)
  const getInventoryAlerts = () => {
    const alerts: {
      id: string;
      name: string;
      category: string;
      alertType: 'low_stock' | 'expiring_soon' | 'both';
      quantity: number;
      minLevel?: number;
      expiryDate?: string;
      daysUntilExpiry?: number;
      value: number;
    }[] = [];
    
    const today = new Date();
    
    enhancedInventoryItems.forEach(item => {
      let alertType: 'low_stock' | 'expiring_soon' | 'both' | null = null;
      let daysUntilExpiry: number | undefined = undefined;
      
      // Check for low stock
      const isLowStock = typeof item.minLevel === 'number' && 
                        typeof item.quantity === 'number' && 
                        item.quantity < item.minLevel;
      
      // Check for expiring soon
      let isExpiringSoon = false;
      if (item.expiryDate) {
        const expiryDate = new Date(item.expiryDate);
        const diffTime = expiryDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays <= 7) {
          isExpiringSoon = true;
          daysUntilExpiry = diffDays;
        }
      }
      
      // Determine alert type
      if (isLowStock && isExpiringSoon) {
        alertType = 'both';
      } else if (isLowStock) {
        alertType = 'low_stock';
      } else if (isExpiringSoon) {
        alertType = 'expiring_soon';
      }
      
      if (alertType) {
        alerts.push({
          id: item.id,
          name: item.name,
          category: item.category,
          alertType,
          quantity: item.quantity,
          minLevel: isLowStock ? item.minLevel : undefined,
          expiryDate: isExpiringSoon ? item.expiryDate : undefined,
          daysUntilExpiry,
          value: item.totalValue
        });
      }
    });
    
    // Sort: first by alert type (both first, then expiring, then low stock)
    // Then by days until expiry or value
    return alerts.sort((a, b) => {
      // First sort by alert type
      const typeOrder = { both: 0, expiring_soon: 1, low_stock: 2 };
      if (a.alertType !== b.alertType) {
        return typeOrder[a.alertType] - typeOrder[b.alertType];
      }
      
      // Then by urgency
      if (a.daysUntilExpiry !== undefined && b.daysUntilExpiry !== undefined) {
        return a.daysUntilExpiry - b.daysUntilExpiry;
      }
      
      // Then by value (highest first)
      return b.value - a.value;
    });
  };
  
  // Get overall stock count accuracy
  const getStockCountAccuracy = () => {
    if (!stockCounts || stockCounts.length === 0) return 100;
    
    // Use the most recent stock count
    const latestCount = [...stockCounts].sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    )[0];
    
    if (!stockCountItems || stockCountItems.length === 0) return 100;
    
    // Calculate accuracy
    const countItems = stockCountItems.filter(item => item.stockCountId === latestCount.id);
    if (countItems.length === 0) return 100;
    
    const totalTheoreticalQuantity = countItems.reduce((sum, item) => sum + Math.abs(item.theoreticalQuantity || 0), 0);
    const totalVariance = countItems.reduce((sum, item) => sum + Math.abs(item.variance || 0), 0);
    
    if (totalTheoreticalQuantity === 0) return 100;
    
    return 100 - ((totalVariance / totalTheoreticalQuantity) * 100);
  };
  
  // Get stock count variance items for display
  const getStockCountVarianceItems = () => {
    if (!stockCounts || stockCounts.length === 0) return [];
    
    // Use the most recent stock count
    const latestCount = [...stockCounts].sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    )[0];
    
    // Filter items for this count
    const items = stockCountItems.filter(item => item.stockCountId === latestCount.id);
    
    // Sort by absolute variance value (highest first)
    return items.sort((a, b) => Math.abs((b.varianceValue || 0)) - Math.abs((a.varianceValue || 0)));
  };
  
  // Get inventory turnover data for display
  const getInventoryTurnoverData = () => {
    // Sort by days of inventory (highest first)
    return [...enhancedInventoryItems].sort((a, b) => 
      (b.daysOfInventory || 0) - (a.daysOfInventory || 0)
    );
  };
  
  // Calculate average inventory turnover
  const getAverageInventoryTurnover = () => {
    const totalValue = enhancedInventoryItems.reduce((sum, item) => sum + item.totalValue, 0);
    
    if (totalValue === 0) return 0;
    
    // Weighted average based on item value
    const weightedSum = enhancedInventoryItems.reduce((sum, item) => {
      return sum + (item.daysOfInventory || 0) * (item.totalValue / totalValue);
    }, 0);
    
    return weightedSum;
  };
  
  // Debug log
  console.log('CogsTab render state:', {
    isDataLoading,
    stockLoading,
    cogsLoading,
    hasError,
    stockItemsCount: stockItems?.length || 0,
    wasteLogsCount: wasteLogs?.length || 0,
    stockCountsCount: stockCounts?.length || 0,
    kpisCalculated: true,
    activeTab
  });
  
  // Now add all useMemo hooks here after all functions are defined
  const kpis = useMemo(() => calculateKPIs(), [stockItems, enhancedInventoryItems, wasteLogs, productionBatches]);
  const wastageByReason = useMemo(() => getWastageByReason(), [wasteLogs, stockItems]);
  const topWastedItems = useMemo(() => getTopWastedItems(), [wasteLogs, stockItems]);
  const inventoryAlerts = useMemo(() => getInventoryAlerts(), [enhancedInventoryItems]);
  const stockCountAccuracy = useMemo(() => getStockCountAccuracy(), [stockCounts, stockCountItems]);
  const stockCountVarianceItems = useMemo(() => getStockCountVarianceItems(), [stockCounts, stockCountItems]);
  const inventoryTurnoverData = useMemo(() => getInventoryTurnoverData(), [enhancedInventoryItems]);
  const averageInventoryTurnover = useMemo(() => getAverageInventoryTurnover(), [enhancedInventoryItems]);
  
  // Generate wastage heatmap data from real waste logs
  const wastageHeatmapData = useMemo(() => getMockWastageHeatmapData(wasteLogs, stockItems), [wasteLogs, stockItems]);
  
  // Enrich production batches with sub-recipe data
  const enrichedProductionBatches = useMemo(() => {
    return productionBatches.map(batch => {
      const subRecipe = subRecipes.find(sr => sr._id === batch.subRecipeId);
      // Type assertion to access optional property
      const costPerUnit = (batch as any).costPerUnit || 0;
      return {
        ...batch,
        id: batch._id, // Add id for compatibility with WeeklyProductionHeatmap
        subRecipeName: subRecipe ? subRecipe.name : 'Unknown Recipe',
        yieldPercentage: (batch.producedQuantity / batch.batchSize) * 100,
        totalCost: costPerUnit * batch.producedQuantity,
        costPerUnit: costPerUnit // Explicitly include costPerUnit
      };
    });
  }, [productionBatches, subRecipes]);
  
  // ADDED: Call data getters for Analyse Coûts tab
  const stockValueByCategory = useMemo(() => getStockValueByCategory(enhancedInventoryItems), [enhancedInventoryItems]);
  const recentPurchases = useMemo(() => getMockRecentPurchases(enhancedInventoryItems), [enhancedInventoryItems]);
  const totalCogsValue = useMemo(() => kpis.totalProductionCost + kpis.totalWasteCost, [kpis]);
  
  const renderAlertBadge = (alertType: 'low_stock' | 'expiring_soon' | 'both') => {
    if (alertType === 'low_stock') {
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
          Stock Bas
        </Badge>
      );
    } else if (alertType === 'expiring_soon') {
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          Expiration Proche
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
          Double Alerte
        </Badge>
      );
    }
  };
  
  // Handler for when a date is clicked in the production heatmap
  const handleProductionDateClick = (date: Date) => {
    setSelectedProductionDate(date);
    // In the future, this could open a modal with detailed production info for that day
    console.log("Production date clicked:", date);
  };

  return (
    <div className="space-y-6">
      {/* KPI Cards - Now 2 rows of 2 */}
      {hasError ? (
        <div className="p-6 bg-red-50 rounded-lg border border-red-200">
          <div className="flex items-start gap-3">
            <AlertCircleIcon className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800">Erreur de chargement des données</h3>
              <p className="text-sm text-red-700 mt-1">
                Impossible d'accéder aux données d'inventaire et de gaspillage. Veuillez vérifier votre connexion à la base de données.
              </p>
              <p className="text-xs text-red-700 mt-2">
                {stockError?.message || cogsError?.message || "Erreur non spécifiée"}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4"> {/* Updated grid for a 2x2 layout on larger screens */}
            <KpiMetricCard
              title="Coût des Marchandises Vendues"
              value={kpis.cogsPercentage}
              icon={<CircleDollarSignIcon className="h-5 w-5 text-primary" />}
              isPercentage={true}
              tooltipText="Pourcentage du coût des marchandises par rapport aux ventes (COGS)"
              loading={isDataLoading || stockLoading || cogsLoading}
            />
            <KpiMetricCard
              title="Valeur Totale des Stocks"
              value={kpis.totalInventoryValue}
              icon={<PackageIcon className="h-5 w-5 text-primary" />}
              tooltipText="Valeur totale de tous les articles en stock"
              loading={isDataLoading || stockLoading}
            />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4"> {/* Updated grid for a 2x2 layout on larger screens */}
            <KpiMetricCard
              title="Coût du Gaspillage"
              value={kpis.wastePercentage}
              icon={<Trash2Icon className="h-5 w-5 text-red-500" />}
              isPercentage={true}
              trend={kpis.wastePercentage > 5 ? 'down' : 'up'}
              tooltipText="Pourcentage du coût total perdu en gaspillage"
              loading={isDataLoading || stockLoading}
            />
            <KpiMetricCard
              title="Rendement Production"
              value={kpis.avgProductionYield}
              icon={<Package2Icon className="h-5 w-5 text-green-500" />}
              isPercentage={true}
              trend={kpis.avgProductionYield > 90 ? 'up' : 'down'}
              tooltipText="Rendement moyen des lots de production (quantité réelle/attendue)"
              loading={isDataLoading || stockLoading || cogsLoading}
            />
          </div>
        </>
      )}
      {/* Main content tabs - now 5 main tabs */}
      <Tabs 
        defaultValue="stock-performance"
        value={activeTab} 
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="stock-performance" className="flex items-center gap-2">
            <PackageIcon className="h-4 w-4" />
            <span>Inventaire & Gaspillage</span>
          </TabsTrigger>
          <TabsTrigger value="production-yields" className="flex items-center gap-2">
            <ClipboardCheckIcon className="h-4 w-4" />
            <span>Rendement Production</span>
          </TabsTrigger>
          <TabsTrigger value="cost-profitability" className="flex items-center gap-2">
            <CircleDollarSignIcon className="h-4 w-4" />
            <span>Coûts & Rentabilité</span>
          </TabsTrigger>
        </TabsList>

        {/* Stock Performance & Wastage Tab (Merged) */}
        <TabsContent value="stock-performance" className="space-y-4">
          {hasError ? (
            <Card>
              <CardContent className="py-10">
                <div className="flex flex-col items-center justify-center text-center gap-3">
                  <AlertCircleIcon className="h-10 w-10 text-red-500" />
                  <h3 className="text-lg font-medium text-red-800">Données indisponibles</h3>
                  <p className="text-sm text-red-700 max-w-lg">
                    Nous ne pouvons pas afficher les informations d'inventaire et de gaspillage car la base de données est inaccessible.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl font-bold">Performance Inventaire</CardTitle>
                  <div className="flex gap-2">
                    {isDataLoading || stockLoading ? (
                      <Badge variant="outline" className="font-normal flex items-center gap-1">
                        <Loader2Icon className="h-3 w-3 animate-spin" />
                        <span>Chargement...</span>
                      </Badge>
                    ) : (
                      <>
                        <Badge variant="secondary" className="font-normal">
                          {inventoryAlerts.length} alertes
                        </Badge>
                        {stockCounts && stockCounts.length > 0 && (
                          <Badge variant={stockCountAccuracy >= 98 ? "success" : stockCountAccuracy >= 95 ? "outline" : "destructive"} className="font-normal">
                            {stockCountAccuracy.toFixed(1)}% précision
                          </Badge>
                        )}
                        <Badge variant={kpis.totalWasteCost > 0 ? "destructive" : "outline"} className="font-normal">
                          {formatCurrency(kpis.totalWasteCost)} gaspillage
                        </Badge>
                      </>
                    )}
                  </div>
                </div>
                <CardDescription>
                  Gestion de stock, comptage d'inventaire et contrôle du gaspillage
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* View selector tabs */}
                <Tabs defaultValue="alertes" className="w-full mb-6">
                  <TabsList className="w-full grid grid-cols-3">
                    <TabsTrigger value="alertes" className="text-sm">Alertes de Stock</TabsTrigger>
                    <TabsTrigger value="comptage" className="text-sm">Précision Comptage</TabsTrigger> 
                    <TabsTrigger value="gaspillage" className="text-sm">Gaspillage & Pertes</TabsTrigger>
                  </TabsList>
                  
                  {/* Alertes View */}
                  <TabsContent value="alertes" className="mt-4 space-y-4">
                    {isDataLoading || stockLoading ? (
                      <div className="flex justify-center items-center py-12">
                        <div className="flex flex-col items-center gap-2">
                          <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
                          <p className="text-sm text-muted-foreground">Chargement des alertes de stock...</p>
                        </div>
                      </div>
                    ) : (
                      <div className="rounded-md border overflow-hidden">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-muted/50">
                              <TableHead>Nom</TableHead>
                              <TableHead>Catégorie</TableHead>
                              <TableHead>Alerte</TableHead>
                              <TableHead>Quantité</TableHead>
                              <TableHead>
                                <Tooltip>
                                  <TooltipTrigger className="cursor-help flex items-center">
                                    <span>Stock (Jours)</span>
                                    <InfoIcon className="ml-1 h-3 w-3 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p className="text-xs">Jours estimés avant l'épuisement du stock selon la consommation récente</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TableHead>
                              <TableHead>Détails</TableHead>
                              <TableHead>Valeur</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {inventoryAlerts.length === 0 ? (
                              <TableRow>
                                <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                                  <div className="flex flex-col items-center gap-2">
                                    <CheckIcon className="h-5 w-5 text-green-500" />
                                    <span>Aucune alerte de stock à afficher</span>
                                    <span className="text-xs">Tous les articles sont à des niveaux satisfaisants</span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ) : (
                              inventoryAlerts.slice(0, 8).map((alert) => {
                                // Find full item data to get daysLeft
                                const fullItem = enhancedInventoryItems.find(item => item.id === alert.id);
                                return (
                                  <TableRow key={alert.id} className="hover:bg-muted/20">
                                    <TableCell className="font-medium">{alert.name}</TableCell>
                                    <TableCell>{alert.category}</TableCell>
                                    <TableCell>{renderAlertBadge(alert.alertType)}</TableCell>
                                    <TableCell>
                                      <div className="flex items-center gap-2">
                                        <span>{alert.quantity}</span>
                                        {alert.minLevel && alert.quantity < alert.minLevel && (
                                          <TooltipProvider>
                                            <Tooltip>
                                              <TooltipTrigger asChild>
                                                <div className="rounded-full p-1 bg-amber-100">
                                                  <AlertCircleIcon className="h-3 w-3 text-amber-700" />
                                                </div>
                                              </TooltipTrigger>
                                              <TooltipContent>
                                                <p className="text-xs">
                                                  Niveau minimum: {alert.minLevel}<br />
                                                  Déficit: {alert.minLevel - alert.quantity}
                                                </p>
                                              </TooltipContent>
                                            </Tooltip>
                                          </TooltipProvider>
                                        )}
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <div className={fullItem?.daysLeft ? (fullItem.daysLeft <= 3 ? 'text-red-600 font-medium' : fullItem.daysLeft <= 7 ? 'text-amber-600' : '') : ''}>
                                        {fullItem?.daysLeft ? fullItem.daysLeft.toFixed(0) : 'N/A'}
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <span className="ml-1 inline-block cursor-help">
                                                <InfoIcon className="h-3 w-3 text-muted-foreground" />
                                              </span>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                              <p className="text-xs">Basé sur le taux de consommation récent</p>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      {alert.expiryDate && (
                                        <div className="flex items-center gap-2">
                                          <ClockIcon className="h-3 w-3 text-red-500" />
                                          <span className={`text-sm ${alert.daysUntilExpiry && alert.daysUntilExpiry <= 3 ? 'text-red-600 font-medium' : ''}`}>
                                            {alert.daysUntilExpiry} jour{alert.daysUntilExpiry !== 1 ? 's' : ''}
                                          </span>
                                        </div>
                                      )}
                                      {alert.alertType === 'low_stock' && (
                                        <div className="flex items-center gap-2">
                                          <ArrowDownIcon className="h-3 w-3 text-amber-500" />
                                          <span className="text-sm">
                                            Stock bas
                                          </span>
                                        </div>
                                      )}
                                    </TableCell>
                                    <TableCell>{formatCurrency(alert.value)}</TableCell>
                                  </TableRow>
                                );
                              })
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                    {!isDataLoading && !stockLoading && inventoryAlerts.length > 8 && (
                      <div className="mt-3 text-sm text-center">
                        <span className="text-muted-foreground">
                          + {inventoryAlerts.length - 8} autres alertes
                        </span>
                      </div>
                    )}
                  </TabsContent>
                  
                  {/* Comptage View */}
                  <TabsContent value="comptage" className="mt-4 space-y-4">
                    {isDataLoading || stockLoading ? (
                      <div className="flex justify-center items-center py-12">
                        <div className="flex flex-col items-center gap-2">
                          <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
                          <p className="text-sm text-muted-foreground">Chargement des données de comptage...</p>
                        </div>
                      </div>
                    ) : (
                      <div className="rounded-md border overflow-hidden">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-muted/50">
                              <TableHead>Article</TableHead>
                              <TableHead>Théorique</TableHead>
                              <TableHead>Comptage</TableHead>
                              <TableHead className="text-center">Écart</TableHead>
                              <TableHead>Valeur Écart</TableHead>
                              <TableHead>Date Comptage</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {stockCountVarianceItems.length === 0 ? (
                              <TableRow>
                                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                                  <div className="flex flex-col items-center gap-2">
                                    {!stockCounts || stockCounts.length === 0 ? (
                                      <>
                                        <InfoIcon className="h-5 w-5 text-muted-foreground" />
                                        <span>Aucun comptage d'inventaire disponible</span>
                                        <span className="text-xs">Effectuez un comptage pour afficher des données ici</span>
                                      </>
                                    ) : (
                                      <>
                                        <CheckIcon className="h-5 w-5 text-green-500" />
                                        <span>Aucun écart détecté dans le dernier comptage</span>
                                        <span className="text-xs">Inventaire physique correspond à l'inventaire théorique</span>
                                      </>
                                    )}
                                  </div>
                                </TableCell>
                              </TableRow>
                            ) : (
                              stockCountVarianceItems.slice(0, 8).map((item) => (
                                <TableRow key={item.id} className="hover:bg-muted/20">
                                  <TableCell className="font-medium">{item.itemName}</TableCell>
                                  <TableCell>{item.theoreticalQuantity}</TableCell>
                                  <TableCell>{item.countedQuantity}</TableCell>
                                  <TableCell className="text-center">
                                    <div className={`flex items-center justify-center gap-1 font-medium 
                                      ${item.variance > 0 ? 'text-red-600' : item.variance < 0 ? 'text-green-600' : 'text-muted-foreground'}
                                    `}>
                                      {item.variance !== 0 && (
                                        item.variance > 0 
                                          ? <ArrowUpIcon className="h-4 w-4" /> 
                                          : <ArrowDownIcon className="h-4 w-4" />
                                      )}
                                      <span>{item.variance > 0 ? '+' : ''}{item.variance !== 0 ? item.variance : '-'}</span>
                                    </div>
                                  </TableCell>
                                  <TableCell className={`${item.varianceValue !== 0 ? (item.varianceValue > 0 ? 'text-red-600' : 'text-green-600') : 'text-muted-foreground'}`}>
                                    {item.varianceValue !== 0 ? formatCurrency(item.varianceValue) : '-'}
                                  </TableCell>
                                  <TableCell className="text-xs text-muted-foreground">
                                    {stockCounts.find(sc => sc.id === item.stockCountId)?.date 
                                      ? new Date(stockCounts.find(sc => sc.id === item.stockCountId)!.date).toLocaleDateString('fr-FR', {day: 'numeric', month: 'short'})
                                      : '-'}
                                  </TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                    {!isDataLoading && !stockLoading && (
                      <div className="text-sm text-center mt-2">
                        <span className="font-medium">Dernier comptage:</span> {stockCounts && stockCounts.length > 0 ? new Date(stockCounts[0].date).toLocaleDateString('fr-FR') : 'Aucun comptage disponible'}
                      </div>
                    )}
                  </TabsContent>
                  
                  {/* Gaspillage View */}
                  <TabsContent value="gaspillage" className="mt-4 space-y-4">
                    {isDataLoading || stockLoading ? (
                      <div className="flex justify-center items-center py-12">
                        <div className="flex flex-col items-center gap-2">
                          <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
                          <p className="text-sm text-muted-foreground">Chargement des données de gaspillage...</p>
                        </div>
                      </div>
                    ) : (
                      <>
                        {/* Top wasted items table */}
                        <div className="rounded-md border overflow-hidden">
                          <Table>
                            <TableHeader>
                              <TableRow className="bg-muted/50">
                                <TableHead>Article</TableHead>
                                <TableHead>Quantité</TableHead>
                                <TableHead>Coût</TableHead>
                                <TableHead>% du Gaspillage</TableHead>
                                <TableHead>Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {topWastedItems.length === 0 ? (
                                <TableRow>
                                  <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                                    <div className="flex flex-col items-center gap-2">
                                      <CheckIcon className="h-5 w-5 text-green-500" />
                                      <span>Aucun gaspillage enregistré</span>
                                      <span className="text-xs">Période actuelle sans pertes documentées</span>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ) : (
                                topWastedItems.map((item) => {
                                  const percentOfTotalWaste = kpis.totalWasteCost > 0 ? (item.cost / kpis.totalWasteCost) * 100 : 0;
                                  // Get top reasons
                                  const reasonEntries = Object.entries(item.reasons).sort((a, b) => b[1] - a[1]);
                                  const topReason = reasonEntries.length > 0 ? reasonEntries[0][0] : 'other';
                                  const secondReason = reasonEntries.length > 1 ? reasonEntries[1][0] : null;
                                  
                                  // Map reason to human-readable text
                                  const reasonMap: Record<string, string> = {
                                    'expired': 'Expiré',
                                    'damaged': 'Endommagé',
                                    'spilled': 'Renversé',
                                    'contaminated': 'Contaminé',
                                    'cooking_error': 'Erreur de Cuisine',
                                    'returned': 'Retourné',
                                    'other': 'Autre'
                                  };
                                  
                                  // Reason icon based on top reason
                                  const getReasonIcon = (reason: string) => {
                                    switch(reason) {
                                      case 'expired':
                                        return <ClockIcon className="h-4 w-4 text-red-500" />;
                                      case 'damaged':
                                        return <AlertTriangleIcon className="h-4 w-4 text-amber-500" />;
                                      case 'cooking_error':
                                        return <AlertCircleIcon className="h-4 w-4 text-orange-500" />;
                                      default:
                                        return <InfoIcon className="h-4 w-4 text-gray-500" />;
                                    }
                                  };
                                  
                                  return (
                                    <TableRow key={item.id} className="hover:bg-muted/20">
                                      <TableCell className="font-medium">{item.name}</TableCell>
                                      <TableCell>{item.quantity}</TableCell>
                                      <TableCell>{formatCurrency(item.cost)}</TableCell>
                                      <TableCell>
                                        <div className="flex flex-col gap-1">
                                          <div className="flex justify-between text-sm">
                                            <span>{percentOfTotalWaste.toFixed(1)}%</span>
                                          </div>
                                          <Progress value={percentOfTotalWaste} className="h-2" />
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <div className="flex gap-2 items-center cursor-help">
                                                {getReasonIcon(topReason)}
                                                <span className="text-sm">{reasonMap[topReason]}</span>
                                                {secondReason && (
                                                  <Badge variant="outline" className="text-xs py-0 h-5">
                                                    +{reasonEntries.length - 1}
                                                  </Badge>
                                                )}
                                              </div>
                                            </TooltipTrigger>
                                            <TooltipContent className="w-64">
                                              <div className="space-y-2">
                                                <p className="text-sm font-medium">Motifs de gaspillage</p>
                                                <div className="space-y-1">
                                                  {reasonEntries.map(([reason, count]) => (
                                                    <div key={reason} className="flex justify-between text-xs">
                                                      <span>{reasonMap[reason]}</span>
                                                      <span className="font-medium">{count} occurrence{count > 1 ? 's' : ''}</span>
                                                    </div>
                                                  ))}
                                                </div>
                                              </div>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      </TableCell>
                                    </TableRow>
                                  );
                                })
                              )}
                            </TableBody>
                          </Table>
                        </div>
                        
                        {/* Wastage by reason summary */}
                        {wastageByReason.length > 0 && (
                          <div className="mt-4">
                            <h4 className="font-medium mb-2 text-sm">Résumé par Motif:</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                              {wastageByReason.sort((a,b) => b.cost - a.cost).map((entry) => {
                                const reasonMap: Record<string, string> = {
                                  'expired': 'Expiré',
                                  'damaged': 'Endommagé',
                                  'spilled': 'Renversé',
                                  'contaminated': 'Contaminé',
                                  'cooking_error': 'Erreur de Cuisine',
                                  'returned': 'Retourné',
                                  'other': 'Autre'
                                };
                                
                                return (
                                  <div key={entry.reason} className="rounded-md border p-2">
                                    <p className="text-xs text-muted-foreground">{reasonMap[entry.reason] || entry.reason}</p>
                                    <p className="font-medium">{formatCurrency(entry.cost)}</p>
                                    <p className="text-xs">{entry.count} incidents</p>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Production Tab - Keep as is */}
        <TabsContent value="production-yields" className="space-y-4">
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl font-bold">Production en Lots</CardTitle>
                <Badge variant="outline" className="font-normal bg-primary/10 text-primary">
                  {productionBatches.length} lots
                </Badge>
              </div>
              <CardDescription>
                Rendement et coûts des productions récentes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Recent Production Batches Table */}
              <div className="rounded-md border overflow-hidden mb-6">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead>Sous-Recette</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Taille du Lot</TableHead>
                      <TableHead>Quantité Produite</TableHead>
                      <TableHead>Rendement</TableHead>
                      <TableHead>Coût Unitaire</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {enrichedProductionBatches.slice(0, 5).map((batch) => {
                      // Format date
                      const date = new Date(batch.date);
                      const formattedDate = new Intl.DateTimeFormat('fr-FR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit'
                      }).format(date);
                      
                      // Determine color based on yield percentage
                      const getYieldColor = () => {
                        if (batch.yieldPercentage >= 95) return "text-green-600";
                        if (batch.yieldPercentage >= 90) return "text-amber-600";
                        return "text-red-600";
                      };
                      
                      return (
                        <TableRow key={batch._id} className="hover:bg-muted/20">
                          <TableCell className="font-medium">{batch.subRecipeName}</TableCell>
                          <TableCell>{formattedDate}</TableCell>
                          <TableCell>{batch.batchSize}</TableCell>
                          <TableCell>{batch.producedQuantity}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <span className={getYieldColor()}>{batch.yieldPercentage.toFixed(1)}%</span>
                              {batch.yieldPercentage < 90 && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <div className="rounded-full p-1 bg-red-50">
                                        <AlertCircleIcon className="h-3 w-3 text-red-500" />
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p className="text-xs">
                                        Rendement faible<br />
                                        Perte: {batch.batchSize - batch.producedQuantity} unités
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{formatCurrency((batch as any).costPerUnit || 0)}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              
              {/* Weekly Production Heatmap */}
              <WeeklyProductionHeatmap 
                batches={enrichedProductionBatches as any} 
                onDateClick={handleProductionDateClick}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analyse des Coûts Tab */}
        <TabsContent value="cost-profitability" className="space-y-4">
          {/* Main COGS Variance Analysis Card - Keep and Enhance */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="text-xl font-bold">Analyse de Rentabilité & Variance</CardTitle>
              <CardDescription>Analyse des écarts entre les coûts prévus et réels par menu</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* COGS Variance Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Mock Theoretical COGS */}
                <div className="rounded-md border p-4">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">CMV Théorique Total</h3>
                  <p className="text-2xl font-bold">{formatCurrency(totalCogsValue * 0.95)}</p>
                  <p className="text-xs text-muted-foreground mt-1">Basé sur les coûts standards des recettes</p>
                </div>
                
                {/* Actual COGS */}
                <div className="rounded-md border p-4">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">CMV Réel Total</h3>
                  <p className="text-2xl font-bold">{formatCurrency(totalCogsValue)}</p>
                  <p className="text-xs text-muted-foreground mt-1">Coûts réels basés sur la consommation</p>
                </div>
                
                {/* Variance */}
                <div className="rounded-md border p-4 bg-red-50">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Variance</h3>
                  <div className="flex items-center">
                    <p className="text-2xl font-bold text-red-600">+5.0%</p>
                    <span className="text-red-600 ml-2">{formatCurrency(totalCogsValue - (totalCogsValue * 0.95))}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">Un écart positif signifie des coûts réels plus élevés que prévu</p>
                </div>
              </div>

              {/* Theoretical vs. Actual by Menu Item */}
              <h3 className="font-medium text-base mt-6 mb-2">Variance par Menu Item (Top 5)</h3>
              <div className="rounded-md border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead>Item Menu</TableHead>
                      <TableHead className="text-right">Qté Vendue</TableHead>
                      <TableHead className="text-right">CMV Théo/Unité</TableHead>
                      <TableHead className="text-right">CMV Théo Total</TableHead>
                      <TableHead className="text-right">CMV Réel/Unité</TableHead>
                      <TableHead className="text-right">CMV Réel Total</TableHead>
                      <TableHead className="text-right">Variance %</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* Mock data for menu items with various variances */}
                    <TableRow>
                      <TableCell className="font-medium">Burger Deluxe</TableCell>
                      <TableCell className="text-right">120</TableCell>
                      <TableCell className="text-right">{formatCurrency(250)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(250 * 120)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(265)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(265 * 120)}</TableCell>
                      <TableCell className="text-right text-red-600">****%</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Pizza Margherita</TableCell>
                      <TableCell className="text-right">85</TableCell>
                      <TableCell className="text-right">{formatCurrency(180)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(180 * 85)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(175)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(175 * 85)}</TableCell>
                      <TableCell className="text-right text-green-600">-2.8%</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Salade César</TableCell>
                      <TableCell className="text-right">65</TableCell>
                      <TableCell className="text-right">{formatCurrency(150)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(150 * 65)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(162)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(162 * 65)}</TableCell>
                      <TableCell className="text-right text-red-600">****%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          {/* New Card: COGS Composition */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="text-xl font-bold">Composition du CMV</CardTitle>
              <CardDescription>Répartition des coûts de marchandises vendues par catégorie</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <p className="text-sm text-muted-foreground">CMV Total Estimé</p>
                  <p className="text-3xl font-bold">{formatCurrency(totalCogsValue)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">% des Ventes</p>
                  <p className="text-3xl font-bold">{kpis.cogsPercentage.toFixed(1)}%</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* COGS by Category */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Répartition des Coûts</h3>
                  <div className="rounded-md border p-3 flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span>Coût de Production</span>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="font-medium">{formatCurrency(kpis.totalProductionCost)}</span>
                      <span className="text-xs text-muted-foreground">{((kpis.totalProductionCost / totalCogsValue) * 100).toFixed(1)}%</span>
                    </div>
                  </div>

                  <div className="rounded-md border p-3 flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span>Coût du Gaspillage</span>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="font-medium">{formatCurrency(kpis.totalWasteCost)}</span>
                      <span className="text-xs text-muted-foreground">{((kpis.totalWasteCost / totalCogsValue) * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>

                {/* COGS Success Metrics */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Indicateurs de Performance</h3>
                  
                  <div className="rounded-md border p-3">
                    <p className="text-xs text-muted-foreground">Rendement Production Moyenne</p>
                    <div className="flex items-center justify-between">
                      <p className={`text-lg font-semibold ${kpis.avgProductionYield >= 95 ? 'text-green-600' : kpis.avgProductionYield >= 90 ? 'text-amber-600' : 'text-red-600'}`}>
                        {kpis.avgProductionYield.toFixed(1)}%
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {kpis.avgProductionYield >= 95 ? 'Excellent' : kpis.avgProductionYield >= 90 ? 'Acceptable' : 'Amélioration nécessaire'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="rounded-md border p-3">
                    <p className="text-xs text-muted-foreground">Précision des Comptages</p>
                    <div className="flex items-center justify-between">
                      <p className={`text-lg font-semibold ${stockCountAccuracy >= 98 ? 'text-green-600' : stockCountAccuracy >= 95 ? 'text-amber-600' : 'text-red-600'}`}>
                        {stockCountAccuracy.toFixed(1)}%
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {stockCountAccuracy >= 98 ? 'Excellent' : stockCountAccuracy >= 95 ? 'Acceptable' : 'Amélioration nécessaire'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Purchases - Move to inventaire tab if needed later */}
        </TabsContent>
      </Tabs>
    </div>
  );
} 