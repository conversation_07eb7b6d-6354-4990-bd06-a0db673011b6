"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { 
  Package, 
  Beaker, 
  Save, 
  X 
} from 'lucide-react';
import { MenuItem } from '@/lib/db/v4/schemas/menu-schema';
import { StockItem } from '@/types/stock';
import { SimplifiedStockConsumption } from '@/lib/db/v4/operations/supplement-ops';
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { cn } from '@/lib/utils';

// Form schema
const supplementSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  prices: z.record(z.string(), z.number().min(0, 'Price must be positive')),
  stockConsumption: z.object({
    stockItemId: z.string().min(1, 'Stock item is required'),
    quantities: z.record(z.string(), z.number().min(0, 'Quantity must be positive')),
  }).optional(),
});

type SupplementFormData = z.infer<typeof supplementSchema>;

interface SupplementFormProps {
  initialData?: MenuItem | null;
  categoryId: string;
  categorySizes: string[];
  onSubmit: (data: SupplementFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export function SupplementForm({
  initialData,
  categoryId,
  categorySizes,
  onSubmit,
  onCancel,
  isSubmitting = false
}: SupplementFormProps) {
  const { stockItems } = useStockV4();

  // Initialize form
  const form = useForm<SupplementFormData>({
    resolver: zodResolver(supplementSchema),
    defaultValues: {
      name: initialData?.name || '',
      prices: initialData?.prices || categorySizes.reduce((acc, size) => ({ ...acc, [size]: 0 }), {}),
      stockConsumption: initialData?.stockConsumption || { stockItemId: '', quantities: {} },
    }
  });

  // Update stock item selection for the supplement
  const updateSupplementStockItem = (stockItemId: string) => {
    const currentStockConsumption = form.getValues('stockConsumption') || { stockItemId: '', quantities: {} };
    form.setValue('stockConsumption.stockItemId', stockItemId);
  };

  // Update quantity for a specific size
  const updateQuantityForSize = (size: string, quantity: number) => {
    const currentStockConsumption = form.getValues('stockConsumption') || { stockItemId: '', quantities: {} };
    const newQuantities = { ...currentStockConsumption.quantities, [size]: quantity };
    form.setValue('stockConsumption.quantities', newQuantities);
  };

  // Get stock item name
  const getStockItemName = (stockItemId: string) => {
    const stockItem = stockItems.find(item => item.id === stockItemId);
    return stockItem ? stockItem.name : 'Unknown Item';
  };

  // Get stock item unit
  const getStockItemUnit = (stockItemId: string) => {
    const stockItem = stockItems.find(item => item.id === stockItemId);
    return stockItem ? stockItem.unit : '';
  };

  const handleSubmit = async (data: SupplementFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Error submitting supplement:', error);
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Beaker className="h-5 w-5" />
                Supplement Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Supplement name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Pricing per Size */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing by Size</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {categorySizes.map((size) => (
                  <FormField
                    key={size}
                    control={form.control}
                    name={`prices.${size}`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{size}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              placeholder="0.00"
                              className="pr-12"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                            <span className="absolute right-3 top-2.5 text-sm text-muted-foreground">
                              DA
                            </span>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Stock Consumption per Size */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Stock Consumption by Size
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="stockConsumption.stockItemId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stock Item</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        updateSupplementStockItem(value);
                        field.onChange(value); // Update react-hook-form
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a stock item for this supplement" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {stockItems.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.name} ({item.unit})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Separator className="my-4" />

              <Label>Quantities per Size</Label>
              <div className="grid gap-4 md:grid-cols-2">
                {categorySizes.map((size) => (
                  <FormField
                    key={size}
                    control={form.control}
                    name={`stockConsumption.quantities.${size}`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{size} Quantity</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            value={field.value !== undefined ? field.value : ''} // Handle undefined initial values
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              field.onChange(isNaN(value) ? 0 : value); // Ensure it's a number
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </div>
              {form.watch('stockConsumption.stockItemId') && (
                <p className="text-sm text-muted-foreground mt-4">
                  Using: {getStockItemName(form.watch('stockConsumption.stockItemId'))} ({getStockItemUnit(form.watch('stockConsumption.stockItemId'))})
                </p>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Supplement'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
} 