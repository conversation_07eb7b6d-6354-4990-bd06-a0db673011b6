"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Save, Beaker } from 'lucide-react';
import { 
  getCategorySupplementConfig, 
  updateCategorySupplementConfig,
  type CategorySupplementConfig 
} from '@/lib/db/v4/operations/supplement-ops';
import { useToast } from '@/components/ui/use-toast';

interface CategorySupplementConfigProps {
  categoryId: string;
  availableSizes: string[];
  onConfigUpdate?: () => void;
}

export function CategorySupplementConfig({ 
  categoryId,
  availableSizes, 
  onConfigUpdate 
}: CategorySupplementConfigProps) {
  const { toast } = useToast();
  const [config, setConfig] = useState<CategorySupplementConfig>({
    globalPricing: {},
    isEnabled: true // Always enabled
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load current configuration
  useEffect(() => {
    const loadConfig = async () => {
      if (!categoryId) return;
      
      try {
        const currentConfig = await getCategorySupplementConfig(categoryId);
        if (currentConfig) {
          setConfig({
            ...currentConfig,
            isEnabled: true // Always enabled
          });
        } else {
          // Initialize with default pricing for available sizes
          const defaultPricing = availableSizes.reduce((acc, size) => {
            acc[size] = 0;
            return acc;
          }, {} as { [key: string]: number });
          
          setConfig({
            globalPricing: defaultPricing,
            isEnabled: true // Always enabled
          });
        }
      } catch (error) {
        console.error('Error loading supplement config:', error);
        toast({
          title: "Erreur",
          description: "Échec du chargement de la configuration des suppléments",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, [categoryId, availableSizes, toast]);

  // Update price for a specific size
  const updatePrice = (size: string, price: number) => {
    setConfig(prev => ({
      ...prev,
      globalPricing: {
        ...prev.globalPricing,
        [size]: price
      }
    }));
  };

  // Save configuration
  const saveConfig = async () => {
    if (!categoryId) return;
    
    setIsSaving(true);
    try {
      await updateCategorySupplementConfig(categoryId, {
        ...config,
        isEnabled: true // Always enabled
      });
      toast({
        title: "Succès",
        description: "Prix des suppléments de cette catégorie mis à jour",
      });
      onConfigUpdate?.();
    } catch (error) {
      console.error('Error saving supplement config:', error);
      toast({
        title: "Erreur",
        description: "Échec de la sauvegarde de la configuration",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-2">
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="border rounded-lg p-2.5 bg-muted/20 space-y-2">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Beaker className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium">Prix Suppléments (Catégorie)</span>
      </div>

      {/* Pricing grid */}
      <div className="space-y-2">
        <div className="grid grid-cols-3 gap-2">
          {availableSizes.map((size) => (
            <div key={size} className="space-y-1">
              <Label className="text-xs text-muted-foreground">{size}</Label>
              <div className="relative">
                <Input
                  type="number"
                  min="0"
                  step="1"
                  placeholder="0"
                  className="h-6 text-xs pr-8"
                  value={config.globalPricing[size] || ''}
                  onChange={(e) => updatePrice(size, parseFloat(e.target.value) || 0)}
                />
                <span className="absolute right-2 top-1 text-xs text-muted-foreground">
                  DA
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Save button */}
        <div className="flex justify-end">
          <Button
            size="sm"
            onClick={saveConfig}
            disabled={isSaving}
            className="h-6 px-2 text-xs"
          >
            <Save className="h-3 w-3 mr-1" />
            {isSaving ? 'Sauvegarde...' : 'Sauvegarder'}
          </Button>
        </div>
      </div>
    </div>
  );
} 