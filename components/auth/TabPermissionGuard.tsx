'use client';

import React from 'react';
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { usePermissions } from "@/lib/hooks/use-permissions";

interface TabPermissionGuardProps {
  page: string;
  tab: string;
  children: React.ReactNode;
}

// Extended User interface with permissions
interface UserWithPermissions {
  id: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
  permissions?: {
    tabs?: {
      [key: string]: {
        [key: string]: boolean;
      };
    };
  };
}

/**
 * TabPermissionGuard Component
 * Controls access to specific tabs within pages based on user permissions
 */
export function TabPermissionGuard({ page, tab, children }: TabPermissionGuardProps) {
  const auth = useAuth();
  const { user, isAuthenticated } = auth;
  const { hasTabAccess, isOwner, isLoading } = usePermissions();

  // No auth, no content - we still check authentication
  if (!isAuthenticated || !user) {
    return null;
  }

  // Wait for permissions to be ready
  if (isLoading) {
    return null;
  }

  // Owners always have access
  if (isOwner) {
    return <>{children}</>;
  }

  // Check if user has access to this specific tab
  if (!hasTabAccess(page, tab)) {
    return null;
  }

  // User has access, render children
  return <>{children}</>;
}
