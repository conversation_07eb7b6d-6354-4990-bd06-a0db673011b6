'use client';

import React from 'react';
import { usePermissions } from '@/lib/hooks/use-permissions';

interface SimpleTabPermissionGuardProps {
  page: string;
  tab: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function SimpleTabPermissionGuard({
  page,
  tab,
  children,
  fallback = null
}: SimpleTabPermissionGuardProps) {
  const { hasTabAccess, isLoading, isOwner } = usePermissions();

  // 🔄 Show loading state (or nothing to avoid layout shift)
  if (isLoading) {
    return null;
  }

  // 👑 Owners bypass all tab restrictions
  if (isOwner) {
    return <>{children}</>;
  }

  // 🔐 Check tab permissions for non-owners
  if (!hasTabAccess(page, tab)) {
    return <>{fallback}</>;
  }

  // ✅ User has access, render children
  return <>{children}</>;
} 