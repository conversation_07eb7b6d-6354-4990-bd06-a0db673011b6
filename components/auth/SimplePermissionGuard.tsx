'use client';

import React from 'react';
import { usePermissions, PagePermissions } from '@/lib/hooks/use-permissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SimplePermissionGuardProps {
  page: keyof PagePermissions;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function SimplePermissionGuard({
  page,
  children,
  fallback
}: SimplePermissionGuardProps) {
  const { hasPageAccess, isLoading, error, isOwner, refresh } = usePermissions();

  // 🔄 Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-lg">Loading permissions...</p>
        </div>
      </div>
    );
  }

  // ❌ Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Permission Error
            </CardTitle>
            <CardDescription>
              Failed to load permissions from local database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <Button onClick={refresh} variant="outline" className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 🔐 Check permissions
  if (!hasPageAccess(page)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-600">
              <AlertCircle className="h-5 w-5" />
              Access Restricted
            </CardTitle>
            <CardDescription>
              You don't have permission to access this page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              {isOwner 
                ? "This page is temporarily unavailable."
                : `You need permission to access the "${page}" section. Please contact your administrator.`
              }
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // ✅ User has access, render children
  return <>{children}</>;
} 