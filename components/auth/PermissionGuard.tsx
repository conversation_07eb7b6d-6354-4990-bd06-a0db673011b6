'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { usePermissions } from '@/lib/context/permissions';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { permissionSyncService } from '@/lib/services/permission-sync-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Wifi } from 'lucide-react';

// Define extended user type that includes permissions
interface ExtendedUser extends Record<string, any> {
  id?: string;
  name?: string;
  email?: string;
  role?: string;
  permissions?: {
    pages?: Record<string, boolean>;
  };
}

// Import or define the PagePermissions type to match what's in the context
type PagePermissions = {
  menu: boolean;
  orders: boolean;
  finance: boolean;
  analytics: boolean;
  inventory: boolean;
  staff: boolean;
  settings: boolean;
  suppliers: boolean;
};

// Define component props
interface PermissionGuardProps {
  page: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PermissionGuard({
  page,
  children,
  fallback
}: PermissionGuardProps) {
  const { hasPageAccess, isReady, isOwner, isAwaitingPermissions, permissions } = usePermissions();
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const { navigate } = useStaticNavigation();

  // Cast user to the ExtendedUser type to access permissions
  const extendedUser = user as ExtendedUser | null;

  // Log auth state for debugging
  useEffect(() => {
    console.log('PERMISSION GUARD - Auth state:', {
      isAuthenticated,
      userRole: extendedUser?.role,
      hasUser: !!extendedUser,
      userId: extendedUser?.id,
      userName: extendedUser?.name,
      userHasPermissions: !!extendedUser?.permissions,
      userPermissionsStructure: extendedUser?.permissions ? JSON.stringify(extendedUser.permissions) : 'none',
      permissionsContext: JSON.stringify(permissions),
      isOwner: isOwner,
      page: page,
      hasPageAccess: hasPageAccess(page as keyof typeof permissions.pages),
      isAwaitingPermissions: isAwaitingPermissions
    });
  }, [isAuthenticated, extendedUser, permissions, isOwner, page, hasPageAccess, isAwaitingPermissions]);

  // Redirect if staff user has no permissions yet and needs to sync
  useEffect(() => {
    if (isReady && isAuthenticated && !isOwner && isAwaitingPermissions) {
      console.log('PERMISSION GUARD - User needs to sync, redirecting to waiting-for-sync page');
      navigate('staff/waiting-for-sync');
    }
  }, [isReady, isAuthenticated, isOwner, isAwaitingPermissions, navigate]);

  // Show loading state while checking permissions
  if (!isReady || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-lg">Loading permissions...</p>
        </div>
      </div>
    );
  }

  // If user is awaiting permissions, show sync guidance instead of access denied
  if (isAwaitingPermissions && !isOwner) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-amber-600" />
            </div>
            <CardTitle className="text-xl">Sync Required</CardTitle>
            <CardDescription>
              Your device needs to sync with the main terminal to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <p className="text-sm text-amber-800">
                <strong>Fresh Device Detected:</strong> This device hasn't synced with the restaurant's main system yet.
              </p>
            </div>
            
            <div className="space-y-3">
              <Button 
                onClick={() => navigate('staff/waiting-for-sync')}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                <Wifi className="h-4 w-4 mr-2" />
                Go to Sync Page
              </Button>
              
              <Button 
                onClick={() => navigate('menu')}
                variant="outline"
                className="w-full"
              >
                Continue with Limited Access
              </Button>
            </div>
            
            <div className="text-xs text-gray-500 text-center">
              Ask your manager for help with syncing if needed.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if user has access to the page
  if (!hasPageAccess(page as keyof typeof permissions.pages)) {
    // Use custom fallback if provided, otherwise show default
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-[50vh] p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-800">
                <strong>Page:</strong> {page}
              </p>
              <p className="text-sm text-red-800 mt-1">
                Contact your manager if you believe you should have access to this feature.
              </p>
            </div>
            
            <Button 
              onClick={() => navigate('menu')}
              variant="outline"
              className="w-full"
            >
              Return to Menu
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User has access, render children
  return <>{children}</>;
}