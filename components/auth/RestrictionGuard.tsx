'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRestrictionMonitor } from '@/lib/hooks/use-restriction-monitor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, LogOut } from 'lucide-react';

interface RestrictionGuardProps {
  children: React.ReactNode;
}

export function RestrictionGuard({ children }: RestrictionGuardProps) {
  const { isRestricted, checkRestrictionStatus, logout, user, isOfflineMode } = useAuth();
  const [isChecking, setIsChecking] = useState(false);
  
  // Use the restriction monitor hook for background checking (very conservative)
  const { lastCheck } = useRestrictionMonitor({
    interval: 5 * 60 * 1000, // Check every 5 minutes (very conservative)
    checkOnMount: false, // Don't check on mount to avoid login interference
    onlineOnly: true
  });

  // Only check restriction status after user has been authenticated for a while
  useEffect(() => {
    if (user && checkRestrictionStatus && !isOfflineMode) {
      // Add a longer delay to ensure login process is completely finished
      const timeoutId = setTimeout(() => {
        console.log('🔒 [RestrictionGuard] Performing delayed restriction check');
        checkRestrictionStatus().catch(error => {
          console.error('Failed delayed restriction check:', error);
        });
      }, 5000); // 5 second delay to avoid login interference

      return () => clearTimeout(timeoutId);
    }
  }, [user?.id, checkRestrictionStatus, isOfflineMode]); // Only depend on user ID to avoid excessive calls

  // Manual refresh function
  const handleRefresh = async () => {
    if (!checkRestrictionStatus) {
      console.warn('checkRestrictionStatus is not available');
      return;
    }
    
    setIsChecking(true);
    try {
      await checkRestrictionStatus();
    } catch (error) {
      console.error('Failed to refresh restriction status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  // If user is restricted, show restriction screen
  if (isRestricted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-destructive" />
            </div>
            <CardTitle className="text-xl text-destructive">Account Restricted</CardTitle>
            <CardDescription>
              {user?.role === 'owner' 
                ? "Your restaurant account has been temporarily restricted. Please contact support for assistance."
                : "Your restaurant's account has been restricted. Please contact your restaurant owner or support for assistance."
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground text-center">
              <p><strong>Account:</strong> {user?.name || 'Unknown'}</p>
              <p><strong>Email:</strong> {user?.email || 'Unknown'}</p>
              {lastCheck && (
                <p><strong>Last checked:</strong> {lastCheck.toLocaleTimeString()}</p>
              )}
            </div>
            
            <div className="flex flex-col gap-2">
              {!isOfflineMode && (
                <Button 
                  onClick={handleRefresh} 
                  disabled={isChecking}
                  variant="outline"
                  className="w-full"
                >
                  {isChecking ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Check Status
                    </>
                  )}
                </Button>
              )}
              
              <Button 
                onClick={logout} 
                variant="destructive"
                className="w-full"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>

            {isOfflineMode && (
              <div className="text-xs text-muted-foreground text-center p-2 bg-muted rounded">
                <p>You're in offline mode. Connect to the internet to check your account status.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // If not restricted, render children normally
  return <>{children}</>;
}