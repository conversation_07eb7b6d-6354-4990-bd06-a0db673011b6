'use client';

import { PropsWithChildren } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useStaticNavigation } from '@/lib/utils/navigation';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export function ProtectedRoute({ children, adminOnly = false }: ProtectedRouteProps) {
  const { isAuthenticated, user, loading } = useAuth();
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const isAdmin = user?.role === 'admin';

  useEffect(() => {
    // Only redirect after authentication status is determined
    if (!loading) {
      if (!isAuthenticated) {
        // Check if we're already on auth page to prevent loops
        const currentPath = window.location.pathname;
        const isOnAuthPage = currentPath.includes('/auth') || currentPath.endsWith('/auth/index.html');
        
        if (!isOnAuthPage && !window.isRedirecting) {
          console.log('🔒 [ProtectedRoute] Redirecting to auth');
          window.isRedirecting = true;
          navigate('auth');
        }
      } else if (adminOnly && !isAdmin) {
        // Redirect non-admin users from admin-only pages to menu instead of root
        const currentPath = window.location.pathname;
        const isOnMenuPage = currentPath.includes('/menu') || currentPath.endsWith('/menu/index.html');
        
        if (!isOnMenuPage && !window.isRedirecting) {
          console.log('🔒 [ProtectedRoute] Redirecting non-admin to menu');
          window.isRedirecting = true;
          navigate('menu');
        }
      }
    }
  }, [isAuthenticated, isAdmin, loading, navigate, adminOnly]);

  // Show nothing while loading or if not authenticated
  if (loading || !isAuthenticated || (adminOnly && !isAdmin)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  // Show the protected content
  return <>{children}</>;
} 