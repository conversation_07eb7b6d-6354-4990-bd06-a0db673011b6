'use client';

import React, { useState, useEffect } from 'react';
import { isElectronEnvironment } from '@/lib/db/electron-db';
import { Loader2, Database, CheckCircle2 } from 'lucide-react';

interface DatabaseLoadingOverlayProps {
  children: React.ReactNode;
}

export function DatabaseLoadingOverlay({ children }: DatabaseLoadingOverlayProps) {
  const [isReady, setIsReady] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [attempts, setAttempts] = useState(0);

  useEffect(() => {
    if (!isElectronEnvironment()) {
      // Web/mobile - always ready
      setIsReady(true);
      return;
    }

    // Electron - show loading and start checking
    setShowOverlay(true);
    
    // 🔍 All possible CouchDB ports to check
    const POSSIBLE_PORTS = [5984, 5985, 5986, 5987, 6984, 6985];
    
    const checkDatabase = async () => {
      try {
        // 🎯 Use Electron IPC to check if CouchDB is ready (avoids CORS issues)
        if (typeof window !== 'undefined' && (window as any).electronAPI?.getCouchDBPort) {
          try {
            const actualPort = await (window as any).electronAPI.getCouchDBPort();
            console.log(`🔍 Electron reports CouchDB port: ${actualPort}`);
            
            // If we get a valid port, assume CouchDB is ready
            if (actualPort && actualPort > 0) {
              console.log(`✅ Database is ready on port ${actualPort}!`);
              setIsReady(true);
              // Brief success animation
              setTimeout(() => setShowOverlay(false), 1500);
              return true;
            }
          } catch (error) {
            console.log('📍 Could not get port from Electron, database not ready yet');
          }
        }
      } catch (error) {
        console.log('⏳ Database still starting up...');
      }
      return false;
    };

    // Check immediately
    checkDatabase();

    // Then check every 2 seconds
    const interval = setInterval(async () => {
      setAttempts(prev => {
        const newAttempts = prev + 1;
        
        // 🚨 Safety net: If we've been trying for 2 minutes, something is wrong
        if (newAttempts > 60) { // 60 attempts = 2 minutes
          console.warn('⚠️ Database check timed out after 2 minutes');
          clearInterval(interval);
          setIsReady(true); // Let user proceed anyway
          setTimeout(() => setShowOverlay(false), 500);
        }
        
        return newAttempts;
      });
      
      const ready = await checkDatabase();
      if (ready) {
        clearInterval(interval);
      }
    }, 2000);

    // Cleanup
    return () => clearInterval(interval);
  }, []);

  // Don't show overlay if not Electron or if ready
  if (!showOverlay || isReady) {
    return <>{children}</>;
  }

  return (
    <div className="fixed inset-0 bg-background z-50 flex items-center justify-center">
      <div className="text-center space-y-6 max-w-sm mx-auto p-8">
        {/* Logo */}
        <div className="flex items-center justify-center space-x-3 mb-8">
          <Database className="w-10 h-10 text-primary" />
          <h1 className="text-xl font-bold">Bistro</h1>
        </div>

        {/* Loading Spinner */}
        <div className="flex justify-center">
          {isReady ? (
            <CheckCircle2 className="w-12 h-12 text-green-500" />
          ) : (
            <Loader2 className="w-12 h-12 animate-spin text-blue-500" />
          )}
        </div>

        {/* Simple Message */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-foreground">
            {isReady ? 'Ready!' : 'Starting up...'}
          </h2>
          <p className="text-sm text-muted-foreground">
            {isReady 
              ? 'Opening your restaurant app'
              : 'Please wait while we start the database'
            }
          </p>
        </div>

        {/* Simple Progress */}
        {!isReady && (
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-blue-500 h-1.5 rounded-full transition-all duration-500" 
              style={{ width: `${Math.min(90, attempts * 1.5)}%` }}
            />
          </div>
        )}

        {/* Simple help text */}
        {!isReady && attempts > 20 && (
          <div className="text-xs text-muted-foreground">
            <p>First time startup takes a moment...</p>
          </div>
        )}
        
        {/* Extended wait message */}
        {!isReady && attempts > 45 && (
          <div className="text-xs text-muted-foreground">
            <p>Still starting up... This can take up to 2 minutes on slower systems.</p>
          </div>
        )}
      </div>
    </div>
  );
} 