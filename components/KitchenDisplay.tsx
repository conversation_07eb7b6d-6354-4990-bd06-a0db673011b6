"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import {
  ChefHat,
  Bell,
  Clock,
  CheckCircle,
  RefreshCw,
  Filter,
  Search,
  AlertCircle,
  XCircle,
  Printer,
  Settings,
  MoreVertical
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { kitchenPrintService } from "@/lib/services/kitchen-print-service";
import { extractDailySequence } from "@/lib/db/v4/operations/order-ops";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { differenceInSeconds, differenceInMinutes, formatDistance } from 'date-fns';
import { useStaticNavigation, isStaticMode } from "@/lib/utils/navigation";

// Define Order status types
type OrderStatus = 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';

// Add these types
type ItemStatus = 'ok' | 'damaged' | 'remade';

type ItemStatusUpdate = {
  orderId: string;
  itemId: string;
  status: ItemStatus;
  quantity: number;
  timestamp: string;
  notes?: string;
};

export function KitchenDisplay() {
  const { toast } = useToast();
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const {
    activeOrders,
    orders,
    isLoading,
    error,
    refreshOrders,
    updateOrder
  } = useOrderV4();
  const { navigate } = useStaticNavigation();

  // States for filtering and organization
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("active");
  const [sortOption, setSortOption] = useState<string>("newest");
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);
  const [pendingNotificationCount, setPendingNotificationCount] = useState(0);
  const [lastRefreshTime, setLastRefreshTime] = useState(new Date());
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [kitchenMode, setKitchenMode] = useState<string>("display");

  // Add new state variables for item status tracking
  const [selectedOrderForItemStatus, setSelectedOrderForItemStatus] = useState<any>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [itemStatusDialogOpen, setItemStatusDialogOpen] = useState(false);
  const [itemStatusQuantity, setItemStatusQuantity] = useState(1);
  const [itemStatus, setItemStatus] = useState<ItemStatus>('damaged');
  const [itemStatusNotes, setItemStatusNotes] = useState('');
  const [statusTimestamps, setStatusTimestamps] = useState<{[key: string]: {[status: string]: string}}>({});
  const [itemStatusHistory, setItemStatusHistory] = useState<ItemStatusUpdate[]>([]);

  // Load kitchen mode preference
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedKitchenMode = localStorage.getItem('kitchen_order_mode');
      if (savedKitchenMode) {
        setKitchenMode(savedKitchenMode);
      }
    }
  }, []);

  // Filter and sort orders
  const filteredOrders = useCallback(() => {
    let filtered = [...orders];

    // Apply status filter
    if (statusFilter === "active") {
      filtered = filtered.filter(order =>
        order.status === 'pending' ||
        order.status === 'preparing' ||
        order.status === 'served'
      );
    } else if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(order => {
        // Search by order ID
        if (order.id.toLowerCase().includes(searchLower)) return true;

        // Search by table ID
        if (order.tableId?.toLowerCase().includes(searchLower)) return true;

        // Search by item names
        if (order.items.some(item => item.name.toLowerCase().includes(searchLower))) return true;

        // Search by customer info
        if (order.customer?.name.toLowerCase().includes(searchLower)) return true;
        if (order.customer?.phone.toLowerCase().includes(searchLower)) return true;

        return false;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();

      if (sortOption === "newest") {
        return dateB - dateA;
      } else if (sortOption === "oldest") {
        return dateA - dateB;
      } else if (sortOption === "priority") {
        // Prioritize pending orders first, then preparing, then served
        const statusPriority = {
          pending: 3,
          preparing: 2,
          served: 1,
          completed: 0,
          cancelled: 0
        };

        const priorityA = statusPriority[a.status as OrderStatus] || 0;
        const priorityB = statusPriority[b.status as OrderStatus] || 0;

        if (priorityA !== priorityB) {
          return priorityB - priorityA;
        }

        // If same status, sort by creation time (newest first)
        return dateB - dateA;
      }

      return 0;
    });

    return filtered;
  }, [orders, statusFilter, searchTerm, sortOption]);

  // Handle manual refresh
  const handleRefresh = async () => {
    setRefreshLoading(true);
    try {
      await refreshOrders();
      setLastRefreshTime(new Date());
      toast({
        title: "Refreshed",
        description: "Order list has been updated.",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: "Could not refresh orders. Try again.",
        variant: "destructive",
      });
    } finally {
      setRefreshLoading(false);
    }
  };

  // Automatically refresh orders at interval
  useEffect(() => {
    if (!autoRefreshEnabled) return;

    const intervalId = setInterval(() => {
      refreshOrders().then(() => {
        setLastRefreshTime(new Date());
      });
    }, 30000); // Every 30 seconds

    return () => clearInterval(intervalId);
  }, [autoRefreshEnabled, refreshOrders]);

  // Handle order status updates
  const handleUpdateOrderStatus = async (orderId: string, newStatus: OrderStatus) => {
    try {
      await updateOrder(orderId, { status: newStatus });
      // Track the status change timestamp
      trackStatusChange(orderId, newStatus);

      toast({
        title: "Status Updated",
        description: `Order ${orderId.substring(6)} marked as ${newStatus}`,
        duration: 3000,
      });
      await refreshOrders();
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update order status. Try again.",
        variant: "destructive",
      });
    }
  };

  // Function to display timing information
  const getTimingInfo = (orderId: string) => {
    const timestamps = statusTimestamps[orderId] || {};

    // Format time elapsed for different statuses
    const formatTimeElapsed = (startStatus: string, endStatus?: string) => {
      if (!timestamps[startStatus]) return null;

      const startTime = new Date(timestamps[startStatus]);
      const endTime = endStatus && timestamps[endStatus]
        ? new Date(timestamps[endStatus])
        : new Date();

      const seconds = differenceInSeconds(endTime, startTime);

      if (seconds < 60) {
        return `${seconds}s`;
      } else {
        const minutes = differenceInMinutes(endTime, startTime);
        return `${minutes}m`;
      }
    };

    return {
      pendingTime: formatTimeElapsed('pending', 'preparing'),
      preparingTime: formatTimeElapsed('preparing', 'served'),
      servedTime: formatTimeElapsed('served', 'completed'),
      totalTime: formatTimeElapsed('pending')
    };
  };

  // Update the useEffect for pending orders to also track initial status
  useEffect(() => {
    const pendingOrders = orders.filter(order => order.status === 'pending');

    // Track timestamp for new pending orders
    pendingOrders.forEach(order => {
      if (!statusTimestamps[order.id] || !statusTimestamps[order.id].pending) {
        setStatusTimestamps(prev => ({
          ...prev,
          [order.id]: {
            ...(prev[order.id] || {}),
            pending: order.createdAt
          }
        }));
      }
    });

    // Rest of the existing notification code...
    if (pendingOrders.length > pendingNotificationCount && pendingNotificationCount > 0) {
      // Play sound for new orders
      const audio = new Audio('/sounds/notification.mp3');
      audio.play().catch(e => console.log('Audio playback error:', e));

      // Show notification
      toast({
        title: "New Order!",
        description: `New order received: ${pendingOrders[0].id.substring(6)}`,
        variant: "default",
      });
    }
    setPendingNotificationCount(pendingOrders.length);
  }, [orders, pendingNotificationCount, toast, statusTimestamps]);

  // Add a function to handle item status updates
  const handleItemStatusUpdate = async () => {
    if (!selectedOrderForItemStatus || !selectedItem) return;

    try {
      // Create a new status update record
      const statusUpdate: ItemStatusUpdate = {
        orderId: selectedOrderForItemStatus.id,
        itemId: selectedItem.id,
        status: itemStatus,
        quantity: itemStatusQuantity,
        timestamp: new Date().toISOString(),
        notes: itemStatusNotes
      };

      // Add to status history
      setItemStatusHistory(prev => [...prev, statusUpdate]);

      // Show success notification
      toast({
        title: `Item ${itemStatus === 'damaged' ? 'marked as damaged' : 'being remade'}`,
        description: `${itemStatusQuantity}× ${selectedItem.name} has been ${itemStatus === 'damaged' ? 'marked as damaged' : 'sent for remaking'}`,
        duration: 3000,
      });

      // Close the dialog
      setItemStatusDialogOpen(false);

      // Reset form
      setItemStatusNotes('');
      setItemStatus('damaged');
      setItemStatusQuantity(1);

      // Refresh orders to update UI
      await refreshOrders();
    } catch (error) {
      console.error('Error updating item status:', error);
      toast({
        title: "Update Failed",
        description: "Could not update item status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Log status change timestamps
  const trackStatusChange = useCallback((orderId: string, newStatus: OrderStatus) => {
    setStatusTimestamps(prev => ({
      ...prev,
      [orderId]: {
        ...(prev[orderId] || {}),
        [newStatus]: new Date().toISOString()
      }
    }));
  }, []);

  // Handle navigation click
  const handleNavClick = (href: string, e: React.MouseEvent) => {
    if (isStaticMode()) {
      e.preventDefault();
      const cleanPath = href.replace(/^\//, '');
      navigate(cleanPath);
    }
    // In dynamic mode, let the browser handle it normally
  };

  // Loading states
  if (authLoading || isLoading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Loading Kitchen Display</CardTitle>
            <CardDescription>Please wait while we retrieve the latest orders...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-8 gap-4">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
              <p className="text-sm text-muted-foreground">
                Loading order data...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-destructive flex items-center">
              <AlertCircle className="mr-2 h-5 w-5" />
              Error Loading Orders
            </CardTitle>
            <CardDescription>
              There was a problem retrieving the orders.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {error.message || "Unknown error occurred. Please try refreshing the page."}
            </p>
            <Button onClick={() => window.location.reload()}>Refresh Page</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access the kitchen display system.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <a href="/auth/signin" onClick={(e) => handleNavClick("/auth/signin", e)}>Sign In</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const filteredOrdersList = filteredOrders();

  return (
    <div className="container-fluid max-w-[1800px] px-4 py-2">
      {/* Mode selection notice */}
      {kitchenMode === "printer" && (
        <Card className="mb-4 border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Printer className="h-5 w-5 text-blue-500" />
                <div>
                  <h3 className="font-medium">Kitchen Printer Mode Active</h3>
                  <p className="text-sm text-muted-foreground">
                    Orders will be printed for kitchen staff. Use this screen to manage your order status.
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm" asChild>
                <a href="/settings" onClick={(e) => handleNavClick("/settings", e)}>
                  <Settings className="h-4 w-4 mr-1" />
                  Change Settings
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Print mode order section */}
      {kitchenMode === "printer" && (
        <Card className="mb-4 border-b-4 border-b-violet-500">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Printer className="h-6 w-6 text-violet-500" />
                <CardTitle>Print Kitchen Orders</CardTitle>
              </div>
            </div>
            <CardDescription>
              Print new orders for the kitchen staff
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-sm font-medium mb-2">New Orders to Print</h3>
                <div className="border rounded-md p-3 bg-muted/50 min-h-[100px]">
                  {filteredOrdersList.filter(o => o.status === 'pending').length > 0 ? (
                    <div className="space-y-2">
                      {filteredOrdersList
                        .filter(o => o.status === 'pending')
                        .map(order => (
                          <div
                            key={order.id}
                            className="flex justify-between items-center p-2 border rounded-md bg-card"
                          >
                            <div>
                              <span className="font-medium">Order #{extractDailySequence(order.id)}</span>
                              {order.tableId && (
                                <Badge variant="outline" className="ml-2">
                                  Table {order.tableId}
                                </Badge>
                              )}
                            </div>
                            <Button
                              size="sm"
                              onClick={async () => {
                                // Create print service and print order
                                try {
                                  // Generate the print job
                                  const printResult = await kitchenPrintService.printKitchenOrder(order, order.tableId);
                                  const printJob = printResult?.printJob;
                                  if (!printJob) {
                                    throw new Error("Print job not generated.");
                                  }

                                  // Open print dialog
                                  const printWindow = window.open('', '_blank');
                                  if (printWindow) {
                                    printWindow.document.write(`
                                      <html>
                                        <head>
                                          <title>${printJob.title}</title>
                                          <style>
                                            @media print {
                                              body { margin: 0; padding: 0; }
                                            }
                                          </style>
                                        </head>
                                        <body>
                                          ${printJob.content}
                                          <script>
                                            window.onload = function() {
                                              window.print();
                                              setTimeout(function() { window.close(); }, 500);
                                            };
                                          </script>
                                        </body>
                                      </html>
                                    `);
                                    printWindow.document.close();

                                    // Update order status after successful print
                                    setTimeout(() => {
                                      handleUpdateOrderStatus(order.id, 'preparing');
                                    }, 1000);

                                    toast({
                                      title: "Print Sent",
                                      description: `Order #${extractDailySequence(order.id)} sent to printer`,
                                    });
                                  }
                                } catch (error) {
                                  console.error("Error printing order:", error);
                                  toast({
                                    title: "Print Failed",
                                    description: "Could not print the order. Please try again.",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Printer className="h-4 w-4 mr-1" />
                              Print Order
                            </Button>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-[80px] text-muted-foreground">
                      <Printer className="h-6 w-6 mb-1.5 text-muted-foreground/50" />
                      <p className="text-xs text-center">No new orders to print</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Print History</h3>
                <div className="border rounded-md p-3 bg-muted/50 min-h-[100px] max-h-[300px] overflow-y-auto">
                  {filteredOrdersList.filter(o => o.status === 'preparing' || o.status === 'served').length > 0 ? (
                    <div className="space-y-2">
                      {filteredOrdersList
                        .filter(o => o.status === 'preparing' || o.status === 'served')
                        .map(order => (
                          <div
                            key={order.id}
                            className="flex justify-between items-center p-2 border rounded-md bg-card"
                          >
                            <div>
                              <span className="font-medium">Order #{extractDailySequence(order.id)}</span>
                              {order.tableId && (
                                <Badge variant="outline" className="ml-2">
                                  Table {order.tableId}
                                </Badge>
                              )}
                              <Badge
                                variant={order.status === 'preparing' ? 'default' : 'secondary'}
                                className="ml-2"
                              >
                                {order.status === 'preparing' ? 'In Progress' : 'Served'}
                              </Badge>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={async () => {
                                // Create print service and print order again
                                try {
                                  const printResult = await kitchenPrintService.printKitchenOrder(order, order.tableId);
                                  const printJob = printResult?.printJob;
                                  if (!printJob) {
                                    throw new Error("Print job not generated.");
                                  }

                                  const printWindow = window.open('', '_blank');
                                  if (printWindow) {
                                    printWindow.document.write(`
                                      <html>
                                        <head>
                                          <title>${printJob.title}</title>
                                          <style>
                                            @media print {
                                              body { margin: 0; padding: 0; }
                                            }
                                          </style>
                                        </head>
                                        <body>
                                          ${printJob.content}
                                          <script>
                                            window.onload = function() {
                                              window.print();
                                              setTimeout(function() { window.close(); }, 500);
                                            };
                                          </script>
                                        </body>
                                      </html>
                                    `);
                                    printWindow.document.close();

                                    toast({
                                      title: "Reprint Sent",
                                      description: `Order #${extractDailySequence(order.id)} sent to printer again`,
                                    });
                                  }
                                } catch (error) {
                                  console.error("Error printing order:", error);
                                  toast({
                                    title: "Print Failed",
                                    description: "Could not print the order. Please try again.",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Printer className="h-4 w-4 mr-1" />
                              Reprint
                            </Button>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-[80px] text-muted-foreground">
                      <Clock className="h-6 w-6 mb-1.5 text-muted-foreground/50" />
                      <p className="text-xs text-center">No print history</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Kitchen display header - streamlined and minimalistic */}
      <Card className="border-b-2 border-b-orange-500 shadow-sm">
        <CardHeader className="py-3 px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <ChefHat className="h-5 w-5 text-orange-500" />
              <CardTitle className="text-base">Kitchen Display</CardTitle>
              <Badge variant="outline" className="ml-2 h-6 px-2 text-xs font-normal flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{lastRefreshTime.toLocaleTimeString()}</span>
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="ghost"
                className={cn("h-8 px-2", autoRefreshEnabled && "text-green-600")}
                onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
              >
                <RefreshCw className={cn("h-4 w-4", autoRefreshEnabled && "text-green-600")} />
                <span className="ml-1 text-xs">{autoRefreshEnabled ? "Auto" : "Manual"}</span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="h-8 px-3"
                onClick={handleRefresh}
                disabled={refreshLoading}
              >
                {refreshLoading ?
                  <RefreshCw className="h-4 w-4 animate-spin" /> :
                  <RefreshCw className="h-4 w-4" />
                }
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {/* Compact Filters and Controls */}
          <div className="flex items-center gap-2 p-2 border-b bg-muted/30">
            <div className="relative flex-1 max-w-xs">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search orders or items..."
                className="pl-7 h-8 text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex gap-2 items-center">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status-filter" className="h-8 text-xs w-[110px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Orders</SelectItem>
                  <SelectItem value="active">Active Orders</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="preparing">Preparing</SelectItem>
                  <SelectItem value="served">Served</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortOption} onValueChange={setSortOption}>
                <SelectTrigger id="sort-option" className="h-8 text-xs w-[110px]">
                  <SelectValue placeholder="Sort" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="priority">By Priority</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Badge variant="outline" className="ml-auto h-6 px-2 flex items-center gap-1">
              <span className="font-semibold">{filteredOrdersList.length}</span> Orders
            </Badge>
          </div>

          {/* Main Kitchen Display Layout - Improved grid */}
          <div className="min-h-[calc(100vh-280px)] grid grid-cols-12 gap-0">
            {/* Pending Orders Column */}
            <div className="col-span-4 border-r">
              <div className="py-2 px-3 font-medium text-sm bg-muted/20 border-b flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <Bell className="h-3.5 w-3.5 text-yellow-500" />
                  <span>Pending</span>
                </div>
                <Badge className="bg-yellow-500 h-5 px-1.5 text-xs">
                  {filteredOrdersList.filter(o => o.status === 'pending').length}
                </Badge>
              </div>

              <ScrollArea className="h-[calc(100vh-280px)]">
                <div className="space-y-2 p-2">
                  {filteredOrdersList
                    .filter(order => order.status === 'pending')
                    .map(order => (
                      <Card key={order.id} className="border-l-2 border-l-yellow-500 hover:bg-accent/10 shadow-sm">
                        <div className="p-2">
                          {/* Order header - more compact */}
                          <div className="flex justify-between items-center mb-1.5">
                            <div className="flex items-center gap-1.5">
                              <span className="font-medium text-sm">#{extractDailySequence(order.id)}</span>
                              {order.tableId && (
                                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                                  T{order.tableId}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1.5">
                              <div className="flex items-center text-xs text-muted-foreground">
                                <Clock className="h-3 w-3 mr-0.5" />
                                {new Date(order.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </div>
                              <Badge variant="outline" className="h-5 px-1.5 text-xs">
                                {order.orderType === 'dine-in' ? 'Dine-in' : order.orderType || 'Unknown'}
                              </Badge>
                            </div>
                          </div>

                          {/* Order items - more compact */}
                          <div className="border-t border-b py-1.5 my-1.5 text-sm space-y-1">
                            {order.items.map(item => (
                              <div key={item.id} className="flex justify-between items-center">
                                <div className="flex items-center gap-1 flex-1 min-w-0">
                                  <span className="font-mono text-xs bg-muted/50 px-1 rounded inline-flex items-center justify-center min-w-[18px]">
                                    {item.quantity}
                                  </span>
                                  <span className="truncate">{item.name}</span>
                                  {item.size && item.size !== 'default' && (
                                    <Badge variant="outline" className="h-4 text-xs font-normal px-1">
                                      {item.size}
                                    </Badge>
                                  )}

                                  {/* Compact status indicators */}
                                  {itemStatusHistory.some(s =>
                                    s.orderId === order.id &&
                                    s.itemId === item.id &&
                                    s.status === 'damaged'
                                  ) && (
                                    <Badge variant="destructive" className="h-4 text-xs font-normal px-1">
                                      !
                                    </Badge>
                                  )}

                                  {itemStatusHistory.some(s =>
                                    s.orderId === order.id &&
                                    s.itemId === item.id &&
                                    s.status === 'remade'
                                  ) && (
                                    <Badge variant="secondary" className="h-4 text-xs font-normal px-1">
                                      R
                                    </Badge>
                                  )}
                                </div>

                                {/* More compact item status button */}
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-4 w-4 ml-1 p-0"
                                  onClick={() => {
                                    setSelectedOrderForItemStatus(order);
                                    setSelectedItem(item);
                                    setItemStatusQuantity(1);
                                    setItemStatusDialogOpen(true);
                                  }}
                                >
                                  <MoreVertical className="h-2.5 w-2.5" />
                                </Button>
                              </div>
                            ))}
                          </div>

                          {/* Action buttons - more touch friendly */}
                          <div className="flex gap-1 mt-1.5">
                            <Button
                              size="sm"
                              className="flex-1 h-8 bg-orange-500 hover:bg-orange-600 text-xs font-medium"
                              onClick={() => handleUpdateOrderStatus(order.id, 'preparing')}
                            >
                              <ChefHat className="h-3.5 w-3.5 mr-1" />
                              Start Preparing
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="w-8 h-8 p-0 hover:bg-red-50 hover:text-red-600 hover:border-red-200"
                              onClick={() => handleUpdateOrderStatus(order.id, 'cancelled')}
                            >
                              <XCircle className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}

                  {filteredOrdersList.filter(o => o.status === 'pending').length === 0 && (
                    <div className="flex flex-col items-center justify-center py-6 text-muted-foreground">
                      <Bell className="h-6 w-6 mb-1.5 text-muted-foreground/50" />
                      <p className="text-xs text-center">No pending orders</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Preparing Orders Column */}
            <div className="col-span-4 border-r">
              <div className="py-2 px-3 font-medium text-sm bg-muted/20 border-b flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <ChefHat className="h-3.5 w-3.5 text-orange-500" />
                  <span>Preparing</span>
                </div>
                <Badge className="bg-orange-500 h-5 px-1.5 text-xs">
                  {filteredOrdersList.filter(o => o.status === 'preparing').length}
                </Badge>
              </div>

              <ScrollArea className="h-[calc(100vh-280px)]">
                <div className="space-y-2 p-2">
                  {filteredOrdersList
                    .filter(order => order.status === 'preparing')
                    .map(order => (
                      <Card key={order.id} className="border-l-2 border-l-orange-500 hover:bg-accent/10 shadow-sm">
                        <div className="p-2">
                          {/* Order header - more compact */}
                          <div className="flex justify-between items-center mb-1.5">
                            <div className="flex items-center gap-1.5">
                              <span className="font-medium text-sm">#{extractDailySequence(order.id)}</span>
                              {order.tableId && (
                                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                                  T{order.tableId}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1.5">
                              <div className="flex items-center text-xs text-muted-foreground">
                                <Clock className="h-3 w-3 mr-0.5" />
                                {new Date(order.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </div>
                              <Badge variant="outline" className="h-5 px-1.5 text-xs">
                                {order.orderType === 'dine-in' ? 'Dine-in' : order.orderType || 'Unknown'}
                              </Badge>
                            </div>
                          </div>

                          {/* Timing info */}
                          {statusTimestamps[order.id] && (
                            <div className="flex gap-1.5 mb-1.5 text-[10px]">
                              <span className="bg-muted/70 px-1.5 py-0.5 rounded-sm flex items-center">
                                Total: {getTimingInfo(order.id).totalTime}
                              </span>
                              {getTimingInfo(order.id).pendingTime && (
                                <span className="bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-sm flex items-center">
                                  Wait: {getTimingInfo(order.id).pendingTime}
                                </span>
                              )}
                            </div>
                          )}

                          {/* Order items - more compact */}
                          <div className="border-t border-b py-1.5 my-1.5 text-sm space-y-1">
                            {order.items.map(item => (
                              <div key={item.id} className="flex justify-between items-center">
                                <div className="flex items-center gap-1 flex-1 min-w-0">
                                  <span className="font-mono text-xs bg-muted/50 px-1 rounded inline-flex items-center justify-center min-w-[18px]">
                                    {item.quantity}
                                  </span>
                                  <span className="truncate">{item.name}</span>
                                  {item.size && item.size !== 'default' && (
                                    <Badge variant="outline" className="h-4 text-xs font-normal px-1">
                                      {item.size}
                                    </Badge>
                                  )}

                                  {/* Compact status indicators */}
                                  {itemStatusHistory.some(s =>
                                    s.orderId === order.id &&
                                    s.itemId === item.id &&
                                    s.status === 'damaged'
                                  ) && (
                                    <Badge variant="destructive" className="h-4 text-xs font-normal px-1">
                                      !
                                    </Badge>
                                  )}

                                  {itemStatusHistory.some(s =>
                                    s.orderId === order.id &&
                                    s.itemId === item.id &&
                                    s.status === 'remade'
                                  ) && (
                                    <Badge variant="secondary" className="h-4 text-xs font-normal px-1">
                                      R
                                    </Badge>
                                  )}
                                </div>

                                {/* More compact item status button */}
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-4 w-4 ml-1 p-0"
                                  onClick={() => {
                                    setSelectedOrderForItemStatus(order);
                                    setSelectedItem(item);
                                    setItemStatusQuantity(1);
                                    setItemStatusDialogOpen(true);
                                  }}
                                >
                                  <MoreVertical className="h-2.5 w-2.5" />
                                </Button>
                              </div>
                            ))}
                          </div>

                          {/* Action buttons - more touch friendly */}
                          <div className="flex gap-1 mt-1.5">
                            <Button
                              size="sm"
                              className="flex-1 h-8 bg-green-500 hover:bg-green-600 text-xs font-medium"
                              onClick={() => handleUpdateOrderStatus(order.id, 'served')}
                            >
                              <CheckCircle className="h-3.5 w-3.5 mr-1" />
                              Ready to Serve
                            </Button>

                            {/* Back to pending button */}
                            <Button
                              size="sm"
                              variant="outline"
                              className="w-8 h-8 p-0 border-yellow-200 hover:bg-yellow-50 hover:text-yellow-700"
                              onClick={() => handleUpdateOrderStatus(order.id, 'pending')}
                            >
                              <Bell className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}

                  {filteredOrdersList.filter(o => o.status === 'preparing').length === 0 && (
                    <div className="flex flex-col items-center justify-center py-6 text-muted-foreground">
                      <ChefHat className="h-6 w-6 mb-1.5 text-muted-foreground/50" />
                      <p className="text-xs text-center">No orders in preparation</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Served & Ready Orders Column */}
            <div className="col-span-4">
              <div className="py-2 px-3 font-medium text-sm bg-muted/20 border-b flex items-center justify-between">
                <div className="flex items-center gap-1.5">
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  <span>Ready</span>
                </div>
                <Badge className="bg-green-500 h-5 px-1.5 text-xs">
                  {filteredOrdersList.filter(o => o.status === 'served').length}
                </Badge>
              </div>

              <ScrollArea className="h-[calc(100vh-280px)]">
                <div className="space-y-2 p-2">
                  {filteredOrdersList
                    .filter(order => order.status === 'served')
                    .map(order => (
                      <Card key={order.id} className="border-l-2 border-l-green-500 hover:bg-accent/10 shadow-sm">
                        <div className="p-2">
                          {/* Order header - more compact */}
                          <div className="flex justify-between items-center mb-1.5">
                            <div className="flex items-center gap-1.5">
                              <span className="font-medium text-sm">#{extractDailySequence(order.id)}</span>
                              {order.tableId && (
                                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                                  T{order.tableId}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1.5">
                              <div className="flex items-center text-xs text-muted-foreground">
                                <Clock className="h-3 w-3 mr-0.5" />
                                {new Date(order.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                              </div>
                              <Badge variant="outline" className="h-5 px-1.5 text-xs">
                                {order.orderType === 'dine-in' ? 'Dine-in' : order.orderType || 'Unknown'}
                              </Badge>
                            </div>
                          </div>

                          {/* Timing info */}
                          {statusTimestamps[order.id] && (
                            <div className="flex gap-1.5 mb-1.5 text-[10px]">
                              <span className="bg-muted/70 px-1.5 py-0.5 rounded-sm flex items-center">
                                Total: {getTimingInfo(order.id).totalTime}
                              </span>
                              {getTimingInfo(order.id).preparingTime && (
                                <span className="bg-orange-100 text-orange-800 px-1.5 py-0.5 rounded-sm flex items-center">
                                  Prep: {getTimingInfo(order.id).preparingTime}
                                </span>
                              )}
                            </div>
                          )}

                          {/* Order items - more compact */}
                          <div className="border-t border-b py-1.5 my-1.5 text-sm space-y-1">
                            {order.items.map(item => (
                              <div key={item.id} className="flex justify-between items-center">
                                <div className="flex items-center gap-1 flex-1 min-w-0">
                                  <span className="font-mono text-xs bg-muted/50 px-1 rounded inline-flex items-center justify-center min-w-[18px]">
                                    {item.quantity}
                                  </span>
                                  <span className="truncate">{item.name}</span>
                                  {item.size && item.size !== 'default' && (
                                    <Badge variant="outline" className="h-4 text-xs font-normal px-1">
                                      {item.size}
                                    </Badge>
                                  )}

                                  {/* Compact status indicators */}
                                  {itemStatusHistory.some(s =>
                                    s.orderId === order.id &&
                                    s.itemId === item.id &&
                                    s.status === 'damaged'
                                  ) && (
                                    <Badge variant="destructive" className="h-4 text-xs font-normal px-1">
                                      !
                                    </Badge>
                                  )}

                                  {itemStatusHistory.some(s =>
                                    s.orderId === order.id &&
                                    s.itemId === item.id &&
                                    s.status === 'remade'
                                  ) && (
                                    <Badge variant="secondary" className="h-4 text-xs font-normal px-1">
                                      R
                                    </Badge>
                                  )}
                                </div>

                                {/* More compact item status button */}
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-4 w-4 ml-1 p-0"
                                  onClick={() => {
                                    setSelectedOrderForItemStatus(order);
                                    setSelectedItem(item);
                                    setItemStatusQuantity(1);
                                    setItemStatusDialogOpen(true);
                                  }}
                                >
                                  <MoreVertical className="h-2.5 w-2.5" />
                                </Button>
                              </div>
                            ))}
                          </div>

                          {/* Action buttons - more touch friendly */}
                          <div className="flex gap-1 mt-1.5">
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1 h-8 text-green-700 border-green-200 bg-green-50 hover:bg-green-100 text-xs font-medium"
                              onClick={() => handleUpdateOrderStatus(order.id, 'completed')}
                            >
                              <CheckCircle className="h-3.5 w-3.5 mr-1" />
                              Mark Delivered
                            </Button>

                            {/* Back to preparing button */}
                            <Button
                              size="sm"
                              variant="outline"
                              className="w-8 h-8 p-0 border-orange-200 hover:bg-orange-50 hover:text-orange-700"
                              onClick={() => handleUpdateOrderStatus(order.id, 'preparing')}
                            >
                              <ChefHat className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}

                  {filteredOrdersList.filter(o => o.status === 'served').length === 0 && (
                    <div className="flex flex-col items-center justify-center py-6 text-muted-foreground">
                      <CheckCircle className="h-6 w-6 mb-1.5 text-muted-foreground/50" />
                      <p className="text-xs text-center">No orders ready for service</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        </CardContent>

        <CardFooter className="py-2 px-3 flex justify-between items-center bg-muted/10 border-t">
          <span className="text-xs text-muted-foreground">
            {filteredOrdersList.length} orders displayed
          </span>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSortOption("priority")}
              className="text-xs h-7 px-2"
            >
              Sort by Priority
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setStatusFilter("all")}
              className="text-xs h-7 px-2"
            >
              View All Orders
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Item Status Dialog - Improved */}
      <Dialog open={itemStatusDialogOpen} onOpenChange={setItemStatusDialogOpen}>
        <DialogContent className="sm:max-w-[400px] p-5">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-base">Update Item Status</DialogTitle>
            <DialogDescription className="text-xs">
              Mark item as damaged or being remade
            </DialogDescription>
          </DialogHeader>

          {selectedItem && (
            <div className="space-y-3 py-2">
              <div className="flex items-center gap-1.5 bg-muted/50 p-2 rounded-md border">
                <div className="font-medium text-sm">{selectedItem.name}</div>
                {selectedItem.size && selectedItem.size !== 'default' && (
                  <Badge variant="outline" className="h-5 text-xs">{selectedItem.size}</Badge>
                )}
              </div>

              <div className="grid gap-1.5">
                <Label htmlFor="status" className="text-xs font-medium">Status</Label>
                <Select value={itemStatus} onValueChange={(val: any) => setItemStatus(val)}>
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="damaged">Damaged/Spoiled</SelectItem>
                    <SelectItem value="remade">Being Remade</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-1.5">
                <Label htmlFor="quantity" className="text-xs font-medium">Quantity</Label>
                <div className="flex items-center gap-1.5">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => setItemStatusQuantity(Math.max(1, itemStatusQuantity - 1))}
                    disabled={itemStatusQuantity <= 1}
                  >
                    -
                  </Button>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    max={selectedItem.quantity || 1}
                    value={itemStatusQuantity}
                    onChange={(e) => setItemStatusQuantity(parseInt(e.target.value) || 1)}
                    className="w-14 h-7 text-center text-sm"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => setItemStatusQuantity(Math.min(selectedItem.quantity || 1, itemStatusQuantity + 1))}
                    disabled={itemStatusQuantity >= (selectedItem.quantity || 1)}
                  >
                    +
                  </Button>
                </div>
              </div>

              <div className="grid gap-1.5">
                <Label htmlFor="notes" className="text-xs font-medium">Notes (optional)</Label>
                <Input
                  id="notes"
                  placeholder="Reason or additional details"
                  value={itemStatusNotes}
                  onChange={(e) => setItemStatusNotes(e.target.value)}
                  className="h-8 text-sm"
                />
              </div>
            </div>
          )}

          <DialogFooter className="pt-2">
            <Button variant="outline" size="sm" onClick={() => setItemStatusDialogOpen(false)} className="h-8">
              Cancel
            </Button>
            <Button size="sm" onClick={handleItemStatusUpdate} className="h-8">
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}