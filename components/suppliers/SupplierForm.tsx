"use client";

import React from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Supplier } from '@/types/suppliers';
import { StockItem } from '@/types/stock';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from "@/components/ui/textarea";

// Schema for supplier validation
const supplierSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit comporter au moins 2 caractères." }),
  phoneNumber: z.string().optional(),
  category: z.string().optional(),
  items: z.array(z.string()).optional(),
  notes: z.string().optional(),
  balance: z.coerce.number().default(0),
  isActive: z.boolean().default(true),
});

// Supplier categories
const supplierCategories = [
  "no-category",
  "Ingrédients",
  "Boissons",
  "Emballages",
  "Fournitures de nettoyage",
  "Fournitures de bureau",
  "Autre"
];

// Component props
interface SupplierFormProps {
  onSubmit: (data: z.infer<typeof supplierSchema>) => Promise<boolean>;
  initialData?: Partial<Supplier>;
  onCancel: () => void;
  stockItems?: StockItem[];
  compact?: boolean;
}

export function SupplierForm({
  onSubmit,
  initialData,
  onCancel,
  stockItems = [],
  compact = false
}: SupplierFormProps) {
  const form = useForm<z.infer<typeof supplierSchema>>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: initialData?.name || "",
      phoneNumber: initialData?.phoneNumber || "",
      category: initialData?.category || undefined,
      items: initialData?.items || [],
      notes: initialData?.notes || "",
      balance: initialData?.balance || 0,
      isActive: initialData?.isActive ?? true,
    },
  });

  // Watch values for conditional display
  const selectedCategory = form.watch("category");
  const selectedItems = form.watch("items");

  // Get inventory items by category
  const itemsByCategory = selectedCategory && stockItems.length > 0
    ? stockItems.filter(item => item.category === selectedCategory)
    : [];

  // Handle form submission
  const handleSubmit = async (formData: z.infer<typeof supplierSchema>) => {
    try {
      const result = await onSubmit(formData);
      return result;
    } catch (error) {
      console.error("Error submitting supplier form:", error);
      return false;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom du fournisseur</FormLabel>
                  <FormControl>
                    <Input {...field} autoFocus={!initialData} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Numéro de téléphone</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {!compact && (
              <>
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Catégorie principale</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionnez une catégorie" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {supplierCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category === "no-category" ? "Aucune catégorie" : category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Catégorie principale des produits fournis
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Notes supplémentaires sur ce fournisseur"
                          className="resize-none h-20"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="balance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Solde initial</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        Positif: vous leur devez. Négatif: ils vous doivent.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Fournisseur actif</FormLabel>
                        <FormDescription>
                          Les fournisseurs inactifs n'apparaîtront pas dans les listes déroulantes.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Annuler
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? "Enregistrement..." : initialData?.id ? "Mettre à jour" : "Ajouter"}
          </Button>
        </div>
      </form>
    </Form>
  );
} 