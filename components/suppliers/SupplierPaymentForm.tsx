"use client";

import { useState, useEffect } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Supplier } from '@/types/suppliers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { CoinsIcon } from 'lucide-react';

// Schema for payment validation
const paymentSchema = z.object({
  amount: z.coerce.number().min(0.01, { message: "Amount must be greater than zero." }),
  notes: z.string().optional(),
});

interface SupplierPaymentFormProps {
  onSubmit: (data: z.infer<typeof paymentSchema>) => Promise<void>;
  supplier: Supplier;
}

export function SupplierPaymentForm({ onSubmit, supplier }: SupplierPaymentFormProps) {
  const [newBalance, setNewBalance] = useState<number>(supplier.balance);
  
  // Initialize form with default values
  const form = useForm<z.infer<typeof paymentSchema>>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      amount: 0,
      notes: ''
    },
  });

  // Watch for changes to calculate new balance
  const amount = form.watch('amount');
  
  // Calculate new balance when amount changes
  useEffect(() => {
    // When making a payment, the balance decreases
    setNewBalance(supplier.balance - amount);
  }, [amount, supplier.balance]);
  
  const handleFormSubmit = async (data: z.infer<typeof paymentSchema>) => {
    try {
      // Add calculated fields for payment transaction
      const enrichedData = {
        ...data,
        supplierId: supplier.id,
        transactionType: 'payment' as const,
        totalAmount: data.amount,
        amountPaid: data.amount,
        date: new Date().toISOString()
      };
      
      await onSubmit(enrichedData);
      form.reset();
    } catch (error) {
      console.error('Error submitting payment:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        <Card className="bg-muted/50">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>Solde actuel&nbsp;:</Label>
                <span className={supplier.balance === 0 ? "" : 
                      supplier.balance > 0 ? "text-red-600 font-medium" : "text-green-600 font-medium"}>
                  ${Math.abs(supplier.balance).toFixed(2)} 
                  {supplier.balance > 0 ? " (vous devez)" : supplier.balance < 0 ? " (crédit)" : ""}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Montant du paiement</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="0,00" 
                  min="0.01" 
                  step="0.01" 
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Entrez le montant que vous payez à ce fournisseur
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Updated balance preview */}
        <div className="p-3 border rounded-md bg-muted/30">
          <div className="flex items-start gap-2 text-sm">
            <CoinsIcon className="h-4 w-4 mt-0.5 text-blue-500" />
            <div>
              <p className="font-medium">Nouveau solde après paiement&nbsp;:</p>
              <p className={newBalance === 0 ? "" : 
                    newBalance > 0 ? "text-red-600 font-medium" : "text-green-600 font-medium"}>
                ${Math.abs(newBalance).toFixed(2)} 
                {newBalance > 0 ? " (vous devrez encore)" : newBalance < 0 ? " (crédit)" : ""}
              </p>
            </div>
          </div>
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Input placeholder="Notes supplémentaires" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit">
            Effectuer le paiement
          </Button>
        </div>
      </form>
    </Form>
  );
} 