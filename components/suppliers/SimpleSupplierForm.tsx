"use client";

import React from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Supplier } from '@/types/suppliers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from "@/components/ui/textarea";

// Schema for supplier validation
const supplierSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit comporter au moins 2 caractères." }),
  phoneNumber: z.string().min(8, { message: "Phone number must be at least 8 characters." }),
  notes: z.string().optional(),
  balance: z.coerce.number().default(0),
  isActive: z.boolean().default(true),
});

// Component props
interface SimpleSupplierFormProps {
  onSubmit: (data: z.infer<typeof supplierSchema>) => Promise<boolean | void>;
  initialData?: Partial<Supplier> | null;
  onCancel: () => void;
  compact?: boolean;
}

export function SimpleSupplierForm({
  onSubmit,
  initialData,
  onCancel,
  compact = false
}: SimpleSupplierFormProps) {
  const form = useForm<z.infer<typeof supplierSchema>>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: initialData?.name || "",
      phoneNumber: initialData?.phoneNumber || "",
      notes: initialData?.notes || "",
      balance: initialData?.balance || 0,
      isActive: initialData?.isActive ?? true,
    },
  });

  // Handle form submission
  const handleSubmit = async (formData: z.infer<typeof supplierSchema>) => {
    try {
      const result = await onSubmit(formData);
      return result;
    } catch (error) {
      console.error("Error submitting supplier form:", error);
      return false;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`space-y-${compact ? '3' : '4'}`}>
        <div className="grid grid-cols-1 gap-3">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={compact ? "text-sm" : ""}>Nom du fournisseur</FormLabel>
                <FormControl>
                  <Input {...field} autoFocus={!initialData} className={compact ? "h-9" : ""} />
                </FormControl>
                <FormMessage className={compact ? "text-xs" : ""} />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={compact ? "text-sm" : ""}>Numéro de téléphone</FormLabel>
                <FormControl>
                  <Input {...field} className={compact ? "h-9" : ""} />
                </FormControl>
                <FormMessage className={compact ? "text-xs" : ""} />
              </FormItem>
            )}
          />

          {!compact && (
            <>
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Notes supplémentaires sur ce fournisseur"
                        className="resize-none h-20"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="balance"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Balance initiale</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Positif: vous leur devez. Négatif: ils vous doivent.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Fournisseur actif</FormLabel>
                      <FormDescription>
                        Les fournisseurs inactifs n'apparaîtront pas dans les listes déroulantes.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-2">
          <Button type="button" variant="outline" onClick={onCancel} className={compact ? "h-9 text-sm" : ""}>
            Annuler
          </Button>
          <Button type="submit" className={compact ? "h-9 text-sm" : ""}>
            {initialData ? "Modifier" : "Ajouter"}
          </Button>
        </div>
      </form>
    </Form>
  );
} 