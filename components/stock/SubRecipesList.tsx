"use client";

import { useState, useEffect } from 'react';
import { useCOGSV4 } from '@/lib/hooks/useCOGSV4';
import { useStockV4 } from '@/lib/hooks/useStockV4';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import {
  ChefHat,
  Clock,
  Search,
  Eye,
  ArrowUpDown,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Package
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format, formatDistanceToNow } from 'date-fns';
import type { SubRecipe, RecipeIngredient } from '@/types/cogs';

export function SubRecipesList() {
  const { subRecipes, productionBatches, isCogsEnabled } = useCOGSV4();
  const { stockItems } = useStockV4();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubRecipe, setSelectedSubRecipe] = useState<SubRecipe | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [sortField, setSortField] = useState<'name' | 'yield' | 'lastProduced' | 'costPerUnit' | 'currentStock'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Filter sub-recipes based on search term
  const filteredSubRecipes = subRecipes.filter(subRecipe =>
    subRecipe.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort sub-recipes
  const sortedSubRecipes = [...filteredSubRecipes].sort((a, b) => {
    if (sortField === 'name') {
      return sortDirection === 'asc'
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortField === 'yield') {
      return sortDirection === 'asc'
        ? a.yield.quantity - b.yield.quantity
        : b.yield.quantity - a.yield.quantity;
    } else if (sortField === 'lastProduced') {
      const dateA = a.lastProduced ? new Date(a.lastProduced).getTime() : 0;
      const dateB = b.lastProduced ? new Date(b.lastProduced).getTime() : 0;
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
    } else if (sortField === 'costPerUnit') {
      const costA = a.costPerUnit || 0;
      const costB = b.costPerUnit || 0;
      return sortDirection === 'asc' ? costA - costB : costB - costA;
    } else if (sortField === 'currentStock') {
      const stockA = typeof a.currentStock === 'number' ? a.currentStock : 0;
      const stockB = typeof b.currentStock === 'number' ? b.currentStock : 0;
      return sortDirection === 'asc' ? stockA - stockB : stockB - stockA;
    }
    return 0;
  });

  // Toggle sort
  const toggleSort = (field: 'name' | 'yield' | 'lastProduced' | 'costPerUnit' | 'currentStock') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Open details dialog
  const openDetailsDialog = (subRecipe: SubRecipe) => {
    setSelectedSubRecipe(subRecipe);
    setIsDetailsDialogOpen(true);
  };

  // Get stock item name by ID
  const getStockItemName = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem ? stockItem.name : 'Unknown Item';
  };

  // Get stock item unit by ID
  const getStockItemUnit = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem ? stockItem.unit : '';
  };

  // Get stock item cost by ID
  const getStockItemCost = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem && stockItem.costPerUnit ? stockItem.costPerUnit : 0;
  };

  // Calculate total cost for an ingredient
  const calculateIngredientCost = (stockItemId: string, quantity: number) => {
    const costPerUnit = getStockItemCost(stockItemId);
    return costPerUnit * quantity;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Sous-recettes</h2>
          <p className="text-sm text-muted-foreground">
            Visualisez et gérez votre inventaire de sous-recettes
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Rechercher des sous-recettes..."
          className="pl-8"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Sub-recipes list */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="cursor-pointer" onClick={() => toggleSort('name')}>
                  <div className="flex items-center">
                    Nom
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => toggleSort('yield')}>
                  <div className="flex items-center">
                    Rendement
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => toggleSort('currentStock')}>
                  <div className="flex items-center">
                    Stock actuel
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => toggleSort('lastProduced')}>
                  <div className="flex items-center">
                    Dernière production
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer text-right" onClick={() => toggleSort('costPerUnit')}>
                  <div className="flex items-center justify-end">
                    Coût par unité
                    <ArrowUpDown className="ml-1 h-3 w-3" />
                  </div>
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedSubRecipes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    {searchTerm
                      ? 'Aucune sous-recette ne correspond à votre recherche'
                      : 'Aucune sous-recette trouvée. Créez-en dans la section Production !'}
                  </TableCell>
                </TableRow>
              ) : (
                sortedSubRecipes.map((subRecipe) => (
                  <TableRow key={subRecipe._id} className="cursor-pointer hover:bg-muted/50" onClick={() => openDetailsDialog(subRecipe)}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <ChefHat className="h-4 w-4 mr-2" />
                        <span>{subRecipe.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {subRecipe.yield.quantity} {subRecipe.yield.unit}
                    </TableCell>
                    <TableCell>
                      {typeof subRecipe.currentStock === 'number' ? subRecipe.currentStock.toFixed(2) : '0.00'} {subRecipe.yield.unit}
                    </TableCell>
                    <TableCell>
                      {subRecipe.lastProduced ? (
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                          <span title={format(new Date(subRecipe.lastProduced), 'PPP')}>
                            {formatDistanceToNow(new Date(subRecipe.lastProduced), { addSuffix: true })}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Jamais</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {subRecipe.costPerUnit ? formatCurrency(subRecipe.costPerUnit) : 'Not calculated'}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon" onClick={(e) => {
                        e.stopPropagation();
                        openDetailsDialog(subRecipe);
                      }}>
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Sub-recipe details dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          {selectedSubRecipe && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <ChefHat className="h-5 w-5" />
                  {selectedSubRecipe.name}
                </DialogTitle>
                <DialogDescription>
                  <div className="space-y-1">
                    <div>Yields: {selectedSubRecipe.yield.quantity} {selectedSubRecipe.yield.unit}</div>
                    <div className="flex items-center">
                      <Package className="h-4 w-4 mr-1" />
                      Current Stock: {typeof selectedSubRecipe.currentStock === 'number' ? selectedSubRecipe.currentStock.toFixed(2) : '0.00'} {selectedSubRecipe.yield.unit}
                    </div>
                    {selectedSubRecipe.lastProduced && (
                      <div>
                        <Clock className="h-4 w-4 inline mr-1" />
                        Last produced: {format(new Date(selectedSubRecipe.lastProduced), 'PPP')}
                      </div>
                    )}
                  </div>
                </DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <h3 className="text-sm font-medium mb-2">Ingredients</h3>
                <ScrollArea className="h-[300px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Ingredient</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead className="text-right">Cost Per Unit</TableHead>
                        <TableHead className="text-right">Total Cost</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedSubRecipe.ingredients.map((ingredient: RecipeIngredient, index: number) => {
                        const stockItemName = getStockItemName(ingredient.stockItemId);
                        const stockItemUnit = getStockItemUnit(ingredient.stockItemId);
                        const costPerUnit = getStockItemCost(ingredient.stockItemId);
                        const totalCost = calculateIngredientCost(ingredient.stockItemId, ingredient.quantity);

                        return (
                          <TableRow key={index}>
                            <TableCell>{stockItemName}</TableCell>
                            <TableCell>{ingredient.quantity} {stockItemUnit}</TableCell>
                            <TableCell className="text-right">{formatCurrency(costPerUnit)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(totalCost)}</TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-medium">Total Cost:</TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(selectedSubRecipe.ingredients.reduce(
                            (sum, ingredient) => sum + calculateIngredientCost(ingredient.stockItemId, ingredient.quantity),
                            0
                          ))}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-medium">Cost Per Unit:</TableCell>
                        <TableCell className="text-right font-medium">
                          {selectedSubRecipe.costPerUnit
                            ? formatCurrency(selectedSubRecipe.costPerUnit)
                            : 'Not calculated'}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
