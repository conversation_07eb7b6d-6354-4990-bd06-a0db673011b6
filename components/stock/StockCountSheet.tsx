"use client";

import { useState, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { StockItem } from '@/types/stock';
import { StockCount } from '@/types/stockCount';
import { Printer, Download } from 'lucide-react';
import { format } from 'date-fns';
import { useReactToPrint } from 'react-to-print';

interface StockCountSheetProps {
  stockCount: StockCount;
  stockItems: StockItem[];
  onPrint?: () => void;
}

export function StockCountSheet({ stockCount, stockItems, onPrint }: StockCountSheetProps) {
  const [isPrinting, setIsPrinting] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    onBeforePrint: () => {
      return new Promise<void>((resolve) => {
        setIsPrinting(true);
        if (onPrint) onPrint();
        resolve();
      });
    },
    onAfterPrint: () => {
      setIsPrinting(false);
    },
  });

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Filter items by count area
  const filteredItems = stockItems.filter(item => {
    if (!stockCount.countArea || stockCount.countArea === 'all') return true;

    const areaMap: Record<string, string[]> = {
      'kitchen': ['ingredients', 'produce', 'meat', 'dairy', 'spices'],
      'bar': ['beverages', 'alcohol', 'wine', 'beer', 'spirits'],      'storage': ['dry goods', 'paper goods', 'cleaning', 'supplies']
    };

    return areaMap[stockCount.countArea]?.includes(item.category.toLowerCase()) || false;
  });

  // Calculate space needed for each item based on purchase units
  const getItemHeight = (item: StockItem) => {
    const allUnits = [
      { id: 'base', name: item.unit, conversionToBase: 1 },
      ...(item.purchaseUnits || [])
    ];
    // Hyper-tight space calculation: each unit line is 13px tall
    return allUnits.length * 13;
  };

  // Maximize space usage with better pagination
  const getPageBreaks = () => {
    const pages: StockItem[][] = [];
    let currentPage: StockItem[] = [];
    
    // Adjusted available height for max items on page, considering header/footer and tighter margins
    const AVAILABLE_HEIGHT = 277 * 3.78; // A4 height (297mm) - total vertical padding (2 * 10mm) - header/footer height (approx 10mm)
    const COLUMN_HEIGHT = AVAILABLE_HEIGHT;
    
    const columns: StockItem[][] = [[], [], []];
    const columnHeights = [0, 0, 0];

    for (const item of filteredItems) {
      const itemHeight = getItemHeight(item);
      
      // Find the shortest column
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
      
      // Check if item fits in shortest column
      if (columnHeights[shortestColumnIndex] + itemHeight <= COLUMN_HEIGHT) {
        // Add to current page
        columns[shortestColumnIndex].push(item);
        columnHeights[shortestColumnIndex] += itemHeight;
      } else {
        // Start new page only if current page has items
        if (columns[0].length > 0 || columns[1].length > 0 || columns[2].length > 0) {
          // Flatten current page items and add to pages
          currentPage = [...columns[0], ...columns[1], ...columns[2]];
          pages.push(currentPage);
          
          // Reset for new page
          columns[0] = [];
          columns[1] = [];
          columns[2] = [];
          columnHeights[0] = 0;
          columnHeights[1] = 0;
          columnHeights[2] = 0;
        }
        
        // Add item to first column of new page
        columns[0].push(item);
        columnHeights[0] = itemHeight;
      }
    }
    
    // Add remaining items as final page
    if (columns[0].length > 0 || columns[1].length > 0 || columns[2].length > 0) {
      currentPage = [...columns[0], ...columns[1], ...columns[2]];
      pages.push(currentPage);
    }
    
    return pages;
  };

  const pages = getPageBreaks();
  const totalPages = pages.length;

  // Improved column distribution for maximum density
  const getColumnItems = (pageItems: StockItem[], columnIndex: number) => {
    const columns: StockItem[][] = [[], [], []];
    const columnHeights = [0, 0, 0];
    
    // Sort items by height (shortest first for better packing)
    const sortedItems = [...pageItems].sort((a, b) => getItemHeight(a) - getItemHeight(b));
    
    // Pack items greedily into shortest column
    for (const item of sortedItems) {
      const shortestColumn = columnHeights.indexOf(Math.min(...columnHeights));
      columns[shortestColumn].push(item);
      columnHeights[shortestColumn] += getItemHeight(item);
    }
    
    return columns[columnIndex] || [];
  };

  // Render single item row - fixed column layout
  const renderItemRow = (item: StockItem) => {
    const allUnits = [
      { id: 'base', name: item.unit, conversionToBase: 1 },
      ...(item.purchaseUnits || [])
    ];

    return (
      <div key={item.id} className="flex items-stretch border-b border-dashed border-gray-300 last:border-b-0"
           style={{ height: `${getItemHeight(item)}px`, minHeight: `${getItemHeight(item)}px` }}>
        {/* Item name - fixed width, ellipsis for overflow, vertically centered */}
        <div className="w-[100px] flex-shrink-0 text-gray-900 font-medium text-[9px] flex items-center pr-1 overflow-hidden whitespace-nowrap text-ellipsis">
          {item.name}
        </div>
        
        {/* Units and input lines - fixed width, stacked vertically, no spacing between units */}
        <div className="w-[80px] flex-shrink-0 ml-1 flex flex-col justify-center">
          {allUnits.map((unit) => (
            <div key={unit.id} className="flex items-center gap-[4px] text-[8px]"
                 style={{ height: '13px', lineHeight: '13px' }}>
              <span className="w-8 text-right text-gray-700 font-normal">
                {unit.name}
              </span>
              <div className="border-b border-black w-12 h-2 flex-shrink-0"></div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* Toolbar (hidden in print) */}
      <div className="flex justify-between items-center mb-4 no-print">
        <h2 className="text-2xl font-bold">Feuille de comptage de stock</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => handlePrint()}>
            <Printer className="h-4 w-4 mr-2" />
            Imprimer
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exporter CSV
          </Button>
        </div>
      </div>

      {/* Print container */}
      <div ref={printRef}>
        {pages.map((pageItems, pageIndex) => {
          return (
            <div
              key={pageIndex}
              className="print-page"
              style={{
                width: '210mm',
                height: '297mm',
                padding: '10mm',
                backgroundColor: 'white',
                marginBottom: pageIndex < totalPages - 1 ? '5mm' : '0',
                pageBreakAfter: pageIndex < totalPages - 1 ? 'always' : 'auto',
                position: 'relative'
              }}
            >
              {/* Header */}
              <div className="flex justify-between items-center mb-4 pb-2 border-b-2 border-black">
                <div>
                  <h1 className="text-lg font-bold">{stockCount.name}</h1>
                  <div className="text-sm mt-1">
                    Date: <span className="border-b border-black inline-block w-32 ml-2"></span>
                  </div>
                </div>
                <div className="text-sm font-medium">Page {pageIndex + 1} / {totalPages}</div>
              </div>

              {/* Content area - height adjusted for new padding */}
              <div className="h-[275mm] overflow-hidden">
                {/* 3 Column Layout */}
                <div className="grid grid-cols-3 gap-3 h-full">
                  {[0, 1, 2].map(columnIndex => {
                    const columnItems = getColumnItems(pageItems, columnIndex);
                    
                    return (
                      <div key={columnIndex} className="h-full">
                        {/* Column header */}
                        <div className="text-center text-sm font-bold pb-1 mb-2 border-b border-gray-600">
                          Articles
                        </div>
                        
                        {/* Items list */}
                        <div className="space-y-0">
                          {columnItems.map(renderItemRow)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Signatures (only on last page) - adjusted position */}
              {pageIndex === totalPages - 1 && (
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="grid grid-cols-2 gap-10 text-sm border-t border-gray-600 pt-3">
                    <div>
                      <span className="font-medium">Comptage effectué par:</span>
                      <div className="border-b border-gray-600 h-6 mt-1"></div>
                    </div>
                    <div>
                      <span className="font-medium">Vérifié par:</span>
                      <div className="border-b border-gray-600 h-6 mt-1"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Print CSS */}
      <style jsx>{`
        @media print {
          .no-print {
            display: none !important;
          }
          
          @page {
            size: A4 portrait;
            margin: 0;
            padding: 0;
          }
          
          .print-page {
            page-break-after: always;
            box-sizing: border-box;
          }
          
          .print-page:last-child {
            page-break-after: auto;
          }
          
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            box-sizing: border-box;
          }
        }
        
        @media screen {
          .print-page {
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
          }
        }
      `}</style>
    </div>
  );
}

