"use client";

import React, { useState } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StockItem, PurchaseUnit } from '@/types/stock';
import { useCOGSV4 } from '@/lib/hooks/useCOGSV4';
import { Supplier } from '@/types/suppliers';
import { PurchaseUnitsManager } from './PurchaseUnitsManager';
import { Separator } from "@/components/ui/separator";
import { v4 as uuidv4 } from 'uuid';
import { useIsMobile } from '@/hooks/use-mobile';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Package, ShoppingBag, Truck, DollarSign, ClipboardList, TrendingDown } from 'lucide-react';

// Schema for stock item validation
const createStockItemSchema = () => {
  // Base schema without quantity tracking
  const baseSchema = {
    name: z.string().min(2, { message: "Nom doit être au moins 2 caractères." }),
    category: z.string().min(1, { message: "Veuillez sélectionner une catégorie." }),
    unit: z.enum(["kg", "L", "pcs", "g", "ml"], {
      required_error: "Veuillez sélectionner une unité de mesure.",
    }),
    supplierId: z.string().optional()
      .transform(val => val === "none" ? undefined : val),
  };

  // Extended schema with quantity tracking for COGS
  const cogsSchema = {
    ...baseSchema,
    quantity: z.coerce.number().min(0, { message: "La quantité ne peut pas être négative." }).optional(),
    minLevel: z.coerce.number().min(0, { message: "Le niveau minimum ne peut pas être négatif." }).optional(),
    costPerUnit: z.coerce.number().min(0, { message: "Le coût ne peut pas être négatif." }).optional(),
  };

  return z.object(cogsSchema);
};

interface StockItemFormProps {
  onSubmit: (data: any) => void;
  initialData?: Partial<StockItem>;
  suppliers?: Supplier[];
  compact?: boolean;
}

export function StockItemForm({ onSubmit, initialData, suppliers = [], compact = false }: StockItemFormProps) {
  // Get COGS settings to check if it's enabled
  const { isCogsEnabled } = useCOGSV4();

  // State for purchase units
  const [purchaseUnits, setPurchaseUnits] = useState<PurchaseUnit[]>(
    initialData?.purchaseUnits || []
  );

  // Stock categories
  const categories = [
    "Ingrédients",
    "Boissons",
    "Emballages",
    "Fournitures de nettoyage",
    "Fournitures de bureau",
    "Autres"
  ];

  // Create the schema based on whether COGS is enabled
  const stockItemSchema = createStockItemSchema();

  // Initialize form with default values or initial data
  const form = useForm({
    resolver: zodResolver(stockItemSchema),
    defaultValues: {
      name: initialData?.name || "",
      category: initialData?.category || "Ingrédients",
      unit: (initialData?.unit as "kg" | "L" | "pcs" | "g" | "ml") || "kg",
      supplierId: initialData?.supplierId || "none",
      quantity: typeof initialData?.quantity === 'number' ? initialData.quantity : 0,
      minLevel: typeof initialData?.minLevel === 'number' ? initialData.minLevel : 0,
      costPerUnit: typeof initialData?.costPerUnit === 'number' ? initialData.costPerUnit : 0,
    },
  });

  // Handle adding purchase unit
  const handleAddPurchaseUnit = (unit: Omit<PurchaseUnit, 'id' | 'createdAt' | 'updatedAt'>) => {
    const now = new Date().toISOString();
    
    // If this is set as default, remove default from others
    let updatedUnits = purchaseUnits;
    if (unit.isDefault) {
      updatedUnits = purchaseUnits.map(u => ({ ...u, isDefault: false }));
    }

    const newUnit: PurchaseUnit = {
      id: uuidv4(),
      ...unit,
      createdAt: now,
      updatedAt: now,
    };

    setPurchaseUnits([...updatedUnits, newUnit]);
  };

  // Handle removing purchase unit
  const handleRemovePurchaseUnit = (unitId: string) => {
    setPurchaseUnits(purchaseUnits.filter(u => u.id !== unitId));
  };

  // Handle setting default purchase unit
  const handleSetDefault = (unitId: string) => {
    setPurchaseUnits(purchaseUnits.map(u => ({
      ...u,
      isDefault: u.id === unitId
    })));
  };

  // Handle form submission
  const handleSubmit = (values: any) => {
    // Include purchase units in the submission
    onSubmit({
      ...values,
      purchaseUnits: purchaseUnits
    });
  };

  const isMobile = useIsMobile() || compact;
  const [showAdvanced, setShowAdvanced] = useState(false);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={isMobile ? 'space-y-2' : `space-y-${compact ? '3' : '4'}`}>
        <div className={isMobile ? 'grid grid-cols-1 gap-2' : 'grid grid-cols-2 gap-4'}>
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1 text-xs">
                  <Package className="h-4 w-4" /> Nom
                </FormLabel>
                <FormControl>
                  <Input autoFocus placeholder="Nom de l'article" {...field} className={isMobile ? 'h-9 text-sm' : ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1 text-xs">
                  <ShoppingBag className="h-4 w-4" /> Catégorie
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className={isMobile ? 'h-9 text-sm' : ''}>
                      <SelectValue placeholder="Catégorie" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category} className="text-xs">
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="unit"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1 text-xs">
                  <Package className="h-4 w-4" /> Unité
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className={isMobile ? 'h-9 text-sm' : ''}>
                      <SelectValue placeholder="Unité" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="kg" className="text-xs">kg</SelectItem>
                    <SelectItem value="g" className="text-xs">g</SelectItem>
                    <SelectItem value="L" className="text-xs">L</SelectItem>
                    <SelectItem value="ml" className="text-xs">ml</SelectItem>
                    <SelectItem value="pcs" className="text-xs">pcs</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {suppliers && suppliers.length > 0 && (
            <FormField
              control={form.control}
              name="supplierId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1 text-xs">
                    <Truck className="h-4 w-4" /> Fournisseur
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className={isMobile ? 'h-9 text-sm' : ''}>
                        <SelectValue placeholder="Fournisseur" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none" className="text-xs">Aucun</SelectItem>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id} className="text-xs">
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        {/* Advanced fields hidden by default on mobile/compact */}
        {(isCogsEnabled && (showAdvanced || !isMobile)) && (
          <div className={isMobile ? 'grid grid-cols-1 gap-2' : 'grid grid-cols-2 gap-4'}>
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1 text-xs">
                    <ClipboardList className="h-4 w-4" /> Quantité
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" placeholder="0" {...field} className={isMobile ? 'h-9 text-sm' : ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="minLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1 text-xs">
                    <TrendingDown className="h-4 w-4" /> Min
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" placeholder="0" {...field} className={isMobile ? 'h-9 text-sm' : ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="costPerUnit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1 text-xs">
                    <DollarSign className="h-4 w-4" /> Valeur totale
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" placeholder="Valeur totale de la quantité" {...field} className={isMobile ? 'h-9 text-sm' : ''} />
                  </FormControl>
                  <FormDescription className="text-xs">
                    Valeur totale de <b>{form.watch('quantity') || 0} {form.watch('unit')}</b> que vous entrez. Le système calculera le coût par unité automatiquement.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Toggle for advanced fields on mobile/compact */}
        {isCogsEnabled && isMobile && (
          <div className="flex justify-end mb-2">
            <Button type="button" variant="ghost" size="sm" className="text-xs px-2 py-1" onClick={() => setShowAdvanced(v => !v)}>
              {showAdvanced ? <ChevronUp className="h-4 w-4 mr-1" /> : <ChevronDown className="h-4 w-4 mr-1" />}
              {showAdvanced ? 'Masquer les champs avancés' : 'Champs avancés'}
            </Button>
          </div>
        )}

        {/* Purchase Units Section */}
        {isCogsEnabled && (
          <>
            <Separator className="my-2" />
            <PurchaseUnitsManager
              baseUnit={form.watch('unit')}
              purchaseUnits={purchaseUnits}
              onAddPurchaseUnit={handleAddPurchaseUnit}
              onRemovePurchaseUnit={handleRemovePurchaseUnit}
              onSetDefault={handleSetDefault}
              compact={true}
            />
          </>
        )}

        {/* Sticky Save Button on Mobile */}
        <div className={isMobile ? 'sticky bottom-0 left-0 right-0 bg-background p-2 z-10 border-t flex justify-end' : 'flex justify-end'}>
          <Button type="submit" className={isMobile ? 'w-full' : ''}>Enregistrer</Button>
        </div>
      </form>
    </Form>
  );
}