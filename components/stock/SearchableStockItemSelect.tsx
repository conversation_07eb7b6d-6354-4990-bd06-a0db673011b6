"use client";

import React, { useState, useMemo } from 'react';
import { StockItem, PurchaseUnit } from '@/types/stock';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Check, ChevronDown, Plus, Package } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { StockItemForm } from './StockItemForm';
import { Supplier } from '@/types/suppliers';

// Enhanced selection item that can represent either a base item or a purchase unit
interface SelectionItem {
  id: string; // Format: "itemId" or "itemId:purchaseUnitId"
  stockItemId: string;
  purchaseUnitId?: string;
  displayName: string;
  subtitle: string;
  stockItem: StockItem;
  purchaseUnit?: PurchaseUnit;
}

interface SearchableStockItemSelectProps {
  stockItems: StockItem[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  suppliers?: Supplier[];
  onCreateStockItem?: (data: any) => Promise<StockItem>;
  compact?: boolean;
  // New prop to enable enhanced mode
  showPurchaseUnits?: boolean;
}

export function SearchableStockItemSelect({
  stockItems,
  value,
  onChange,
  placeholder = "Sélectionnez un article",
  suppliers = [],
  onCreateStockItem,
  compact = false,
  showPurchaseUnits = false
}: SearchableStockItemSelectProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false);
  
  // Create enhanced selection items when showPurchaseUnits is enabled
  const selectionItems = useMemo(() => {
    if (!showPurchaseUnits) {
      // Original behavior - just show stock items
      const uniqueItemsMap = new Map();
      stockItems.forEach(item => {
        if (!uniqueItemsMap.has(item.id)) {
          uniqueItemsMap.set(item.id, item);
        }
      });
      return Array.from(uniqueItemsMap.values())
        .map(item => ({
          id: item.id,
          stockItemId: item.id,
          displayName: item.name,
          subtitle: `${item.category} • ${item.unit}`,
          stockItem: item
        }))
        .sort((a, b) => a.displayName.localeCompare(b.displayName));
    }

    // Enhanced behavior - show purchase units when available
    const items: SelectionItem[] = [];
    
    // Remove duplicates by id
    const uniqueItemsMap = new Map();
    stockItems.forEach(item => {
      if (!uniqueItemsMap.has(item.id)) {
        uniqueItemsMap.set(item.id, item);
      }
    });
    const uniqueItems = Array.from(uniqueItemsMap.values());
    
    uniqueItems.forEach(stockItem => {
      // If item has purchase units, show them instead of the base item
      if (stockItem.purchaseUnits && stockItem.purchaseUnits.length > 0) {
        console.log('🔍 Processing item with purchase units:', stockItem.name, stockItem.purchaseUnits);
        stockItem.purchaseUnits.forEach((purchaseUnit: PurchaseUnit) => {
          const selectionItem = {
            id: `${stockItem.id}:${purchaseUnit.id}`,
            stockItemId: stockItem.id,
            purchaseUnitId: purchaseUnit.id,
            displayName: `${stockItem.name} (${purchaseUnit.name})`,
            subtitle: `${stockItem.category} • ${purchaseUnit.conversionToBase !== 1 ? `1 = ${purchaseUnit.conversionToBase} ${stockItem.unit}` : stockItem.unit}`,
            stockItem,
            purchaseUnit
          };
          console.log('➕ Adding enhanced selection item:', selectionItem);
          items.push(selectionItem);
        });
      } else {
        // No purchase units, show the base item
        const selectionItem = {
          id: stockItem.id,
          stockItemId: stockItem.id,
          displayName: stockItem.name,
          subtitle: `${stockItem.category} • ${stockItem.unit}`,
          stockItem
        };
        console.log('➕ Adding base selection item:', selectionItem);
        items.push(selectionItem);
      }
    });
    
    console.log('📋 Total selection items generated:', items.length);
    return items.sort((a, b) => a.displayName.localeCompare(b.displayName));
  }, [stockItems, showPurchaseUnits]);

  // Filter items based on search
  const filteredItems = selectionItems.filter(item => 
    item.displayName.toLowerCase().includes(search.toLowerCase()) ||
    item.stockItem.category.toLowerCase().includes(search.toLowerCase()) ||
    item.stockItem.name.toLowerCase().includes(search.toLowerCase())
  );

  // Get the selected item's name for display
  const selectedItem = selectionItems.find(item => item.id === value);
  const selectedItemName = selectedItem ? selectedItem.displayName : placeholder;

  // Handle search not found - offer to create a new item
  const handleSearchNotFound = () => {
    setIsAddItemDialogOpen(true);
  };

  // Handle creating a new item
  const handleCreateStockItem = async (data: any) => {
    if (!onCreateStockItem) return false;
    
    try {
      const newItem = await onCreateStockItem(data);
      onChange(newItem.id);
      setIsAddItemDialogOpen(false);
      setOpen(false);
      return true;
    } catch (error) {
      console.error("Failed to create stock item:", error);
      return false;
    }
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              compact && "h-8 text-sm"
            )}
          >
            <span className="truncate">{selectedItemName}</span>
            <ChevronDown className={cn("ml-2 h-4 w-4 shrink-0 opacity-50", compact && "h-3.5 w-3.5")} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command>
            <CommandInput 
              placeholder="Rechercher un article..." 
              value={search}
              onValueChange={setSearch}
              className={cn("h-9", compact && "h-8 text-sm")}
            />
            <CommandEmpty>
              <div className="px-2 py-3 text-xs text-center space-y-2">
                <p>Aucun article trouvé.</p>
                {onCreateStockItem && (
                  <Button 
                    variant="secondary" 
                    size="sm" 
                    onClick={handleSearchNotFound}
                    className="w-full h-8 text-xs"
                  >
                    <Plus className="mr-2 h-3.5 w-3.5" />
                    Créer un nouvel article
                  </Button>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-60">
                {filteredItems.map((item) => (
                  <CommandItem
                    key={item.id}
                    value={item.displayName}
                    onSelect={() => {
                      onChange(item.id);
                      setOpen(false);
                    }}
                    className="text-sm"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === item.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <Package className="h-3.5 w-3.5 text-muted-foreground shrink-0" />
                      <div className="flex flex-col min-w-0 flex-1">
                        <span className="truncate">{item.displayName}</span>
                        <span className="text-xs text-muted-foreground truncate">
                          {item.subtitle}
                        </span>
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* New Stock Item Dialog */}
      {onCreateStockItem && (
        <Dialog open={isAddItemDialogOpen} onOpenChange={setIsAddItemDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Ajouter un nouvel article</DialogTitle>
            </DialogHeader>
            <StockItemForm 
              onSubmit={handleCreateStockItem}
              initialData={{ name: search }}
              suppliers={suppliers}
              compact={true}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
} 