"use client";

import React, { useState } from 'react';
import { Supplier } from '@/types/suppliers';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Check, ChevronDown, Plus } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

// Import SimpleSupplierForm
import { SimpleSupplierForm } from '@/components/suppliers/SimpleSupplierForm';

interface SearchableSupplierSelectProps {
  suppliers: Supplier[];
  value: string;
  onChange: (value: string) => void;
  onCreateSupplier: (supplier: Partial<Supplier>) => Promise<Supplier>;
  compact?: boolean;
}

export function SearchableSupplierSelect({
  suppliers,
  value,
  onChange,
  onCreateSupplier,
  compact = false
}: SearchableSupplierSelectProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const [isAddSupplierDialogOpen, setIsAddSupplierDialogOpen] = useState(false);
  
  // Get the selected supplier's name for display
  const selectedSupplierName = value === "none" 
    ? "Sélectionnez un fournisseur" 
    : suppliers.find(s => s.id === value)?.name || "Sélectionnez un fournisseur";

  // Filter suppliers based on search term
  const filteredSuppliers = suppliers
    .filter(supplier => 
      supplier.name.toLowerCase().includes(search.toLowerCase())
    );

  // Handle creating a new supplier using SimpleSupplierForm
  const handleCreateSupplier = async (supplierData: any) => {
    try {
      const newSupplier = await onCreateSupplier(supplierData);
      onChange(newSupplier.id);
      setIsAddSupplierDialogOpen(false);
      setOpen(false);
      return true;
    } catch (error) {
      console.error("Failed to create supplier:", error);
      return false;
    }
  };

  // Handle search not found - offer to create a new supplier
  const handleSearchNotFound = () => {
    setIsAddSupplierDialogOpen(true);
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              compact && "h-8 text-sm"
            )}
          >
            {selectedSupplierName}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command>
            <CommandInput 
              placeholder="Rechercher un fournisseur..." 
              value={search}
              onValueChange={setSearch}
              className={cn("h-9", compact && "h-8 text-sm")}
            />
            <CommandEmpty>
              <div className="px-2 py-3 text-sm text-center space-y-2">
                <p>Aucun fournisseur trouvé.</p>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  onClick={handleSearchNotFound}
                  className="w-full"
                >
                  <Plus className="mr-2 h-3.5 w-3.5" />
                  Créer un nouveau fournisseur
                </Button>
              </div>
            </CommandEmpty>
            <CommandGroup className="max-h-60 overflow-auto">
              <CommandItem
                value="none"
                onSelect={() => {
                  onChange("none");
                  setOpen(false);
                }}
                className="text-sm"
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value === "none" ? "opacity-100" : "opacity-0"
                  )}
                />
                <span>Aucun fournisseur</span>
              </CommandItem>
              
              {filteredSuppliers.map((supplier) => (
                <CommandItem
                  key={supplier.id}
                  value={supplier.name}
                  onSelect={() => {
                    onChange(supplier.id);
                    setOpen(false);
                  }}
                  className="text-sm"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === supplier.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {supplier.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Supplier Creation Dialog using SimpleSupplierForm */}
      <Dialog open={isAddSupplierDialogOpen} onOpenChange={setIsAddSupplierDialogOpen}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
            <DialogTitle>Ajouter un nouveau fournisseur</DialogTitle>
          </DialogHeader>
          
          <SimpleSupplierForm 
            onSubmit={handleCreateSupplier}
            onCancel={() => setIsAddSupplierDialogOpen(false)}
            initialData={{ name: search }}
            compact={true}
          />
        </DialogContent>
      </Dialog>
    </>
  );
} 