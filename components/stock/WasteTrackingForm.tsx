"use client";

import { useState } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { StockItem } from '@/types/stock';
import { Trash2 } from 'lucide-react';
import { cn } from "@/lib/utils";

// Schema for waste log validation
const wasteLogSchema = z.object({
  stockItemId: z.string().min(1, { message: "Please select a stock item." }),
  quantity: z.coerce.number().positive({ message: "Quantity must be greater than zero." }),
  reason: z.enum(['expired', 'damaged', 'spilled', 'contaminated', 'cooking_error', 'returned', 'other'], {
    required_error: "Please select a reason.",
  }),
  notes: z.string().optional(),
});

interface WasteTrackingFormProps {
  onSubmit: (data: z.infer<typeof wasteLogSchema>) => Promise<void>;
  stockItems: StockItem[];
  selectedItemId?: string;
}

export function WasteTrackingForm({
  onSubmit,
  stockItems,
  selectedItemId
}: WasteTrackingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get the selected stock item
  const selectedItem = selectedItemId
    ? stockItems.find(item => item.id === selectedItemId)
    : null;

  // Initialize form with default values
  const form = useForm<z.infer<typeof wasteLogSchema>>({
    resolver: zodResolver(wasteLogSchema),
    defaultValues: {
      stockItemId: selectedItemId || "",
      quantity: 0,
      reason: 'expired',
      notes: ""
    }
  });

  // Handle form submission
  const handleSubmit = async (values: z.infer<typeof wasteLogSchema>) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
      form.reset({
        stockItemId: selectedItemId || "",
        quantity: 0,
        reason: 'expired',
        notes: ""
      });
    } catch (error) {
      console.error("Error submitting waste log:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get reason label and icon
  const getReasonInfo = (reason: string) => {
    switch (reason) {
      case 'expired':
        return {
          label: 'Périmé',
          description: 'Articles dont la date limite est dépassée',
          color: 'text-amber-600',
          bgColor: 'bg-amber-100'
        };
      case 'damaged':
        return {
          label: 'Endommagé',
          description: 'Articles physiquement endommagés lors de la manipulation ou du stockage',
          color: 'text-red-600',
          bgColor: 'bg-red-100'
        };
      case 'spilled':
        return {
          label: 'Renversé',
          description: 'Articles accidentellement renversés lors de la préparation ou du service',
          color: 'text-blue-600',
          bgColor: 'bg-blue-100'
        };
      case 'contaminated':
        return {
          label: 'Contaminé',
          description: 'Articles contaminés ou devenus impropres à la consommation',
          color: 'text-purple-600',
          bgColor: 'bg-purple-100'
        };
      case 'cooking_error':
        return {
          label: 'Erreur de cuisson',
          description: 'Articles gaspillés à cause d’erreurs de préparation',
          color: 'text-orange-600',
          bgColor: 'bg-orange-100'
        };
      case 'returned':
        return {
          label: 'Retourné par le client',
          description: 'Articles retournés par les clients et ne pouvant être réutilisés',
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-100'
        };
      case 'other':
        return {
          label: 'Autre',
          description: 'Autres raisons non couvertes par les catégories standards',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100'
        };
      default:
        return {
          label: reason,
          description: '',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100'
        };
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-5">
        <FormField
          control={form.control}
          name="stockItemId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Article du stock</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={!!selectedItemId}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un article du stock" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {stockItems.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name} {typeof item.quantity === 'number' ? `(${item.quantity} ${item.unit})` : `(${item.unit})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                Sélectionnez l'article qui a été gaspillé
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Quantité</FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0.01"
                    className="pr-16"
                    {...field}
                  />
                </FormControl>
                {selectedItem && (
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-muted-foreground">
                    {selectedItem.unit}
                  </div>
                )}
              </div>
              <FormDescription>
                Entrez la quantité gaspillée
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Raison</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez une raison" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {['expired', 'damaged', 'spilled', 'contaminated', 'cooking_error', 'returned', 'other'].map(reason => {
                    const info = getReasonInfo(reason);
                    return (
                      <SelectItem key={reason} value={reason}>
                        <div className="flex items-center gap-2">
                          <div className={`${info.bgColor} p-1 rounded-full`}>
                            <div className={`h-2 w-2 rounded-full ${info.color.replace('text-', 'bg-')}`}></div>
                          </div>
                          <span>{info.label}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              <FormDescription>
                Sélectionnez la raison du gaspillage
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (optionnel)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Ajoutez des détails supplémentaires sur ce gaspillage"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Ajoutez toute information supplémentaire sur ce gaspillage
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-3 pt-2">
          <div className="flex flex-col gap-2 w-full">
            {form.watch('reason') && (
              <div className={`p-2 rounded-md ${getReasonInfo(form.watch('reason')).bgColor} mb-2`}>
                <p className={`text-sm ${getReasonInfo(form.watch('reason')).color}`}>
                  <strong>{getReasonInfo(form.watch('reason')).label}:</strong> {getReasonInfo(form.watch('reason')).description}
                </p>
              </div>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700 w-full"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              {isSubmitting ? "Enregistrement..." : "Enregistrer le gaspillage"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
