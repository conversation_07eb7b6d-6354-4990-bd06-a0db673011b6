"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useForm, useFieldA<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { PlusCircle, Trash2, Truck, Package, DollarSign, FileText, Calendar as CalendarIcon, Check, ChevronsUpDown, X } from 'lucide-react';
import { Supplier } from '@/types/suppliers';
import { StockItem } from '@/types/stock';
import { useSuppliersV4 } from '@/lib/hooks/useSuppliersV4';
import { useToast } from "@/components/ui/use-toast";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from 'date-fns';
import { cn } from "@/lib/utils";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { SimpleSupplierForm } from '@/components/suppliers/SimpleSupplierForm';

// --- Schemas ---

const purchasedItemSchema = z.object({
  stockItemId: z.string().min(1, "Please select an item."),
  itemName: z.string().optional(), // Store name for display
  quantity: z.coerce.number().min(0.01, "Quantity must be positive."),
  costPerUnit: z.coerce.number().min(0, "Cost must be positive or zero."),
  lineTotal: z.number().default(0), // Calculated
});

const bulkPurchaseSchema = z.object({
  supplierId: z.string().min(1, "Please select a supplier.").transform(val => val === "none" ? "" : val),
  invoiceNumber: z.string().optional(),
  invoiceDate: z.date().optional(),
  items: z.array(purchasedItemSchema).min(1, "Please add at least one item."),
  amountPaid: z.coerce.number().min(0, "Amount paid cannot be negative.").default(0),
  notes: z.string().optional(),
  // Calculated fields (not part of the direct form input, but useful)
  grandTotal: z.number().default(0), 
});

// --- Component Props ---

interface BulkPurchaseFormProps {
  suppliers: Supplier[];
  stockItems: StockItem[];
  onSubmit: (data: z.infer<typeof bulkPurchaseSchema>) => Promise<boolean>; // Returns true on success
  onCancel: () => void;
  onSupplierAdded?: () => void; // Optional callback after adding supplier
}

// --- Component ---

export function BulkPurchaseForm({ 
  suppliers, 
  stockItems, 
  onSubmit, 
  onCancel,
  onSupplierAdded
}: BulkPurchaseFormProps) {
  
  const { toast } = useToast();
  const [grandTotal, setGrandTotal] = useState(0);
  const [isAddSupplierDialogOpen, setIsAddSupplierDialogOpen] = useState(false);
  const { createSupplier } = useSuppliersV4();
  const [isComboboxOpen, setIsComboboxOpen] = useState(false);
  const [comboboxValue, setComboboxValue] = useState("");
  const firstInputRef = useRef<HTMLInputElement>(null);

  // Initialize form for bulk purchase
  const form = useForm<z.infer<typeof bulkPurchaseSchema>>({
    resolver: zodResolver(bulkPurchaseSchema),
    defaultValues: {
      supplierId: '', // Initially empty
      invoiceNumber: '',
      invoiceDate: new Date(),
      items: [],
      amountPaid: 0,
      notes: ''
    }
  });

  // --- Field Array for Items ---
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // --- Watch Fields for Calculation ---
  const watchedItems = form.watch("items");
  const amountPaid = form.watch("amountPaid");

  // --- Calculate Totals ---
  useEffect(() => {
    let currentTotal = 0;
    watchedItems.forEach((item, index) => {
      const lineTotal = (item.quantity || 0) * (item.costPerUnit || 0);
      // Use update to set the calculated lineTotal without causing infinite loops if possible
      // This might need refinement depending on react-hook-form behavior
      if (form.getValues(`items.${index}.lineTotal`) !== lineTotal) { // Check current form value before setting
         form.setValue(`items.${index}.lineTotal`, lineTotal, { shouldValidate: false, shouldDirty: false });
      }
      currentTotal += lineTotal;
    });
    setGrandTotal(currentTotal);
    // Only update grandTotal if it actually changed to avoid potential loops
    if (form.getValues('grandTotal') !== currentTotal) {
      form.setValue('grandTotal', currentTotal, { shouldValidate: true, shouldDirty: true });
    }
  }, [watchedItems, form]);

  // --- Add New Item Line (via Combobox selection) ---
  const handleAddItemFromCombobox = (selectedItemId: string) => {
    const selectedItem = stockItems.find(item => item.id === selectedItemId);
    if (!selectedItem) return;

    // Check if item already exists in the list
    const existingItemIndex = fields.findIndex(field => field.stockItemId === selectedItemId);

    if (existingItemIndex !== -1) {
      // If item exists, maybe focus its quantity input?
      // Or show a toast message?
      toast({ title: "Item Already Added", description: `${selectedItem.name} is already in the list.`, variant: "default" });
      // Focus existing quantity input (requires refs on dynamic fields - complex, skip for now)
    } else {
      // Add new item row
      append({ 
        stockItemId: selectedItem.id,
        itemName: selectedItem.name,
        quantity: 1, 
        costPerUnit: selectedItem.costPerUnit || 0, // Pre-fill cost
        lineTotal: (1 * (selectedItem.costPerUnit || 0)), // Calculate initial line total
      });
      // Focus the quantity input of the newly added row shortly after appending
      setTimeout(() => {
         const inputs = document.querySelectorAll<HTMLInputElement>(`input[name^="items.${fields.length}"]`); // Find inputs in the new last row
         if (inputs[0]) { // Focus the quantity input (first input in the row)
             inputs[0].focus();
             inputs[0].select();
         }
       }, 50); 
    }

    // Reset combobox state
    setComboboxValue(""); 
    setIsComboboxOpen(false);
  };

  // Update handleAddNewSupplier to use our SimpleSupplierForm
  const handleAddNewSupplier = async (data: any) => {
    try {
      const newSupplier = await createSupplier(data);
      // Ensure newSupplier has an 'id' property (for v4 compatibility)
      const supplierWithId = {
        ...newSupplier,
        id: newSupplier.id,
      };
      if (supplierWithId && supplierWithId.id) {
        if (onSupplierAdded) {
          await onSupplierAdded();
        }
        setTimeout(() => {
          form.setValue('supplierId', supplierWithId.id, { shouldValidate: true });
        }, 100);
        setIsAddSupplierDialogOpen(false);
        toast({ title: 'Success', description: `${data.name} added.` });
        return true;
      } else {
        throw new Error('Failed to get new supplier details.');
      }
    } catch (error) {
      console.error('Error adding new supplier:', error);
      toast({ title: 'Error', description: 'Could not add supplier.', variant: 'destructive' });
      return false;
    }
  };
  
  // --- Form Submission ---
  const handleFormSubmit = async (data: z.infer<typeof bulkPurchaseSchema>) => {
    // Ensure grandTotal is included correctly
    const finalData = { ...data, grandTotal }; 
    
    console.log("Submitting Bulk Purchase:", finalData); // Debug log
    
    const success = await onSubmit(finalData);
    if (success) {
      toast({ title: "Success", description: "Bulk purchase recorded." });
      form.reset(); // Reset form on successful submission
      // onCancel(); // Optionally close the form/dialog
    } else {
       toast({ title: "Error", description: "Failed to record purchase.", variant: "destructive" });
    }
  };

  // --- Render ---
  return (
    <Form {...form}>
      <Card className="overflow-hidden border-none shadow-none p-0">
        <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-3 flex flex-col h-full">
            <CardContent className="space-y-3 px-1 py-0 flex-grow min-h-0">
                {/* --- Invoice Details Section --- */} 
                <div className="grid grid-cols-3 gap-2 items-end">
                   {/* Supplier Selection with Add New Button */} 
                   <FormField
                     control={form.control}
                     name="supplierId"
                     render={({ field }) => (
                       <FormItem className="col-span-2">
                           <div className="flex items-center justify-between gap-2 mb-1">
                              <FormLabel className="text-xs text-muted-foreground">Fournisseur</FormLabel>
                              {/* Add New Supplier Dialog Trigger */} 
                              <Dialog open={isAddSupplierDialogOpen} onOpenChange={setIsAddSupplierDialogOpen}>
                                 <DialogTrigger asChild>
                                   <Button type="button" variant="ghost" size="sm" className="text-[10px] h-5 px-1.5">
                                     <PlusCircle className="h-2.5 w-2.5 mr-0.5" /> Nouveau
                                   </Button>
                                 </DialogTrigger>
                                 {/* Add New Supplier Dialog Content */} 
                                 <DialogContent className="sm:max-w-[450px]">
                                    <DialogHeader>
                                         <DialogTitle>Add New Supplier</DialogTitle>
                                         <DialogDescription>Quickly add a new supplier.</DialogDescription>
                                       </DialogHeader>
                                       <SimpleSupplierForm
                                         onSubmit={handleAddNewSupplier}
                                         onCancel={() => setIsAddSupplierDialogOpen(false)}
                                         compact={true}
                                       />
                                 </DialogContent>
                               </Dialog>
                           </div>
                           <Select onValueChange={field.onChange} value={field.value}>
                             <FormControl>
                               <SelectTrigger className="h-7 text-xs border-0 bg-muted/30">
                                 <SelectValue placeholder="Sélectionner fournisseur" />
                               </SelectTrigger>
                             </FormControl>
                             <SelectContent>
                               <SelectItem value="none" disabled>Select a supplier</SelectItem>
                               {suppliers.map((supplier) => (
                                 <SelectItem key={supplier.id} value={supplier.id}>
                                   {supplier.name}
                                 </SelectItem>
                               ))}
                             </SelectContent>
                           </Select>
                           <FormMessage />
                       </FormItem>
                     )}
                   />
                   
                   {/* Invoice Date - Compact */} 
                   <FormField control={form.control} name="invoiceDate" render={({ field }) => (
                     <FormItem className="flex flex-col">
                       <FormLabel className="text-xs text-muted-foreground mb-1">Date</FormLabel>
                       <Popover>
                         <PopoverTrigger asChild>
                           <FormControl>
                             <Button variant={"outline"} className={cn("h-7 text-xs border-0 bg-muted/30 pl-2 text-left font-normal", !field.value && "text-muted-foreground")} >
                               {field.value ? format(field.value, "dd/MM") : <span>Date</span>}
                               <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                             </Button>
                           </FormControl>
                         </PopoverTrigger>
                         <PopoverContent className="w-auto p-0" align="start">
                           <Calendar mode="single" selected={field.value} onSelect={field.onChange} disabled={(date) => date > new Date() || date < new Date("1900-01-01")} initialFocus />
                         </PopoverContent>
                       </Popover>
                       <FormMessage />
                     </FormItem>
                   )} />
                </div> 

                {/* Invoice Number - Single Row */}
                <FormField control={form.control} name="invoiceNumber" render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-2">
                      <FormLabel className="text-xs text-muted-foreground min-w-0 flex-shrink-0">N° Facture</FormLabel>
                      <FormControl>
                        <Input placeholder="INV-12345" {...field} className="h-6 text-xs border-0 bg-muted/30 flex-1" />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )} />

                 {/* --- Add Item Combobox - Compact --- */} 
                 <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">Ajouter Article</Label>
                    <Popover open={isComboboxOpen} onOpenChange={setIsComboboxOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={isComboboxOpen}
                          className="w-full justify-between font-normal h-7 text-xs border-0 bg-muted/30"
                        >
                          {comboboxValue
                            ? stockItems.find((item) => item.id === comboboxValue)?.name
                            : "Rechercher article..."}
                          <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[--radix-popover-trigger-width] p-0 max-h-[--radix-popover-content-available-height]">
                        <Command>
                          <CommandInput placeholder="Rechercher..." />
                          <CommandList>
                            <CommandEmpty>Aucun article trouvé.</CommandEmpty>
                            <CommandGroup>
                               <ScrollArea className="h-48"> {/* Scroll long lists */}
                                {stockItems.map((item) => (
                                  <CommandItem
                                    key={item.id}
                                    value={item.name} // Use name for searching
                                    onSelect={(currentValue) => {
                                      const selectedId = stockItems.find(si => si.name.toLowerCase() === currentValue.toLowerCase())?.id;
                                      if (selectedId) {
                                        handleAddItemFromCombobox(selectedId);
                                      }
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        comboboxValue === item.id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    {item.name} ({item.unit})
                                  </CommandItem>
                                ))}
                              </ScrollArea>
                            </CommandGroup>
                           </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                 </div> 

                 {/* --- Excel-like Items Grid --- */} 
                 <div className="space-y-1">
                   <div className="text-xs font-medium text-muted-foreground mb-1">Articles</div>
                   
                   {/* Header Row - Excel-like */}
                   <div className="grid grid-cols-12 gap-0 bg-muted/20 py-1 px-1 text-[10px] font-medium text-muted-foreground">
                     <div className="col-span-6 pl-1">Article</div>
                     <div className="col-span-2 text-center">Qté</div>
                     <div className="col-span-2 text-center">Prix</div>
                     <div className="col-span-2 text-right pr-1">Total</div>
                   </div>
                   
                   {/* Data Rows - Excel-like Grid */}
                   <div className="space-y-0">
                     {fields.map((field, index) => (
                       <div key={field.id} className="grid grid-cols-12 gap-0 hover:bg-muted/10 group border-b border-muted/20">
                         {/* Item Name */}
                         <div className="col-span-6 flex items-center py-1 px-1 text-xs font-medium">
                           <span className="truncate">{field.itemName || 'Article...'}</span>
                           <Button 
                             type="button" 
                             variant="ghost" 
                             size="icon" 
                             className="h-4 w-4 ml-auto opacity-0 group-hover:opacity-100 text-destructive"
                             onClick={() => remove(index)}
                           >
                             <X className="h-2.5 w-2.5" />
                           </Button>
                         </div>
                         
                         {/* Quantity */}
                         <div className="col-span-2 p-0">
                           <FormField 
                             control={form.control} 
                             name={`items.${index}.quantity`} 
                             render={({ field: qtyField }) => (
                               <Input 
                                 type="number" 
                                 step="0.01" 
                                 placeholder="1" 
                                 {...qtyField} 
                                 className="h-6 text-xs text-center border-0 bg-transparent rounded-none focus:bg-background" 
                               />
                             )} 
                           />
                         </div>
                         
                         {/* Cost Per Unit */}
                         <div className="col-span-2 p-0">
                           <FormField 
                             control={form.control} 
                             name={`items.${index}.costPerUnit`} 
                             render={({ field: costField }) => (
                               <Input 
                                 type="number" 
                                 step="0.01" 
                                 placeholder="0.00" 
                                 {...costField} 
                                 className="h-6 text-xs text-center border-0 bg-transparent rounded-none focus:bg-background" 
                               />
                             )} 
                           />
                         </div>
                         
                         {/* Line Total */}
                         <div className="col-span-2 flex items-center justify-end py-1 px-1 text-xs font-medium">
                           {watchedItems[index]?.lineTotal?.toFixed(2) || '0.00'}
                         </div>
                       </div>
                     ))}
                     
                     {fields.length === 0 && (
                       <div className="grid grid-cols-12 gap-0 py-6">
                         <div className="col-span-12 text-center text-muted-foreground text-xs">
                           Utilisez la recherche ci-dessus pour ajouter des articles
                         </div>
                       </div>
                     )}
                   </div>
                   
                   <FormMessage>{form.formState.errors.items?.message}</FormMessage>
                </div> 

                 {/* --- Summary and Payment Section - Compact --- */} 
                <div className="grid grid-cols-2 gap-3 pt-2 border-t border-muted/30">
                    {/* Grand Total */}
                    <div>
                       <div className="text-xs text-muted-foreground mb-1">Total Général</div>
                       <div className="text-lg font-bold">{grandTotal.toFixed(2)} DA</div>
                    </div>
                    
                    {/* Amount Paid */}
                    <FormField control={form.control} name="amountPaid" render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs text-muted-foreground">Montant Payé</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" placeholder="0.00" {...field} className="h-7 text-xs border-0 bg-muted/30" />
                        </FormControl>
                        <div className="text-[10px] text-muted-foreground">
                          Reste: {(grandTotal - (amountPaid || 0)).toFixed(2)} DA
                        </div>
                        <FormMessage />
                      </FormItem>
                    )} />
                </div> 
                
                {/* Notes - Compact */}
                <FormField control={form.control} name="notes" render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center gap-2">
                      <FormLabel className="text-xs text-muted-foreground min-w-0 flex-shrink-0">Notes</FormLabel>
                      <FormControl>
                        <Input placeholder="Remarques..." {...field} className="h-6 text-xs border-0 bg-muted/30 flex-1" />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )} />
            </CardContent>
            
            <CardFooter className="flex justify-end gap-2 bg-muted/20 px-4 py-2 border-t flex-shrink-0">
               <Button type="button" variant="outline" onClick={onCancel} className="h-7 text-xs">Annuler</Button>
               <Button type="submit" disabled={form.formState.isSubmitting || !form.formState.isValid} className="h-7 text-xs">
                 {form.formState.isSubmitting ? "Enregistrement..." : "Enregistrer Facture"}
               </Button>
            </CardFooter>
        </form>
      </Card>
    </Form>
  );
} 