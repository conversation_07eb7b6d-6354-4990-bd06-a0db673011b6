"use client";

import React, { useState, useEffect } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { StockItem } from '@/types/stock';
import { Supplier } from '@/types/suppliers';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, ShoppingBag } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

// Schema for purchase logging validation
const purchaseSchema = z.object({
  stockItemId: z.string().min(1, { message: "Please select an item." }),
  supplierId: z.string().optional()
    .transform(val => val === "none" ? undefined : val),
  quantity: z.coerce.number().min(0.01, { message: "Quantity must be greater than zero." }),
  costPerUnit: z.coerce.number().optional(),
  totalCost: z.coerce.number().optional(),
  date: z.date({
    required_error: "Please select a date for this purchase.",
  }),
  notes: z.string().optional(),
}).refine((data) => {
  // Either costPerUnit or totalCost must be provided
  return data.costPerUnit !== undefined || data.totalCost !== undefined;
}, {
  message: "You must provide either cost per unit or total cost.",
  path: ["costPerUnit"]
});

interface PurchaseLogFormProps {
  onSubmit: (data: any) => Promise<void>;
  stockItems: StockItem[];
  suppliers: Supplier[];
  selectedItemId?: string;
  suggestedSupplierId?: string;
  linkWithSupplier?: boolean; // Whether to link with supplier transaction
}

export function PurchaseLogForm({ 
  onSubmit, 
  stockItems, 
  suppliers,
  selectedItemId,
  suggestedSupplierId,
  linkWithSupplier = true // Default to true
}: PurchaseLogFormProps) {
  const [calculatedTotal, setCalculatedTotal] = useState<number | null>(null);
  const [calculatedUnit, setCalculatedUnit] = useState<number | null>(null);
  const [selectedItem, setSelectedItem] = useState<StockItem | null>(
    selectedItemId ? stockItems.find(item => item.id === selectedItemId) || null : null
  );
  
  // Add state for amount paid
  const [payFullAmount, setPayFullAmount] = useState(true);

  // Initialize form
  const form = useForm<z.infer<typeof purchaseSchema>>({
    resolver: zodResolver(purchaseSchema),
    defaultValues: {
      stockItemId: selectedItemId || '',
      supplierId: suggestedSupplierId || 'none',
      quantity: 1,
      costPerUnit: undefined,
      totalCost: undefined,
      date: new Date(),
      notes: '',
    },
  });

  // Watch for changes to calculate derived values
  const quantity = form.watch('quantity');
  const totalCost = form.watch('totalCost');
  const costPerUnit = form.watch('costPerUnit');
  const watchedStockItemId = form.watch('stockItemId');
  
  // Update selected item when stockItemId changes
  useEffect(() => {
    if (watchedStockItemId) {
      const item = stockItems.find(item => item.id === watchedStockItemId);
      setSelectedItem(item || null);
    } else {
      setSelectedItem(null);
    }
  }, [watchedStockItemId, stockItems]);

  // Auto-calculate values when inputs change
  useEffect(() => {
    // Calculate unit cost when quantity and total cost are provided
    if (quantity > 0 && totalCost !== undefined && totalCost > 0) {
      setCalculatedUnit(totalCost / quantity);
      // Update the form field if it's not manually set
      if (costPerUnit === undefined) {
        form.setValue('costPerUnit', totalCost / quantity);
      }
    } 
    
    // Calculate total cost when quantity and unit cost are provided
    if (quantity > 0 && costPerUnit !== undefined && costPerUnit > 0) {
      setCalculatedTotal(quantity * costPerUnit);
      // Update the form field if it's not manually set
      if (totalCost === undefined) {
        form.setValue('totalCost', quantity * costPerUnit);
      }
    }
  }, [quantity, totalCost, costPerUnit, form]);

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    try {
      const totalCostFinal = formData.totalCost || calculatedTotal || 0;
      
      // Set default paid amount based on payFullAmount toggle
      const amountPaid = payFullAmount ? totalCostFinal : 0;
      
      // Ensure both costPerUnit and totalCost are set
      const purchaseData = {
        ...formData,
        totalCost: totalCostFinal,
        costPerUnit: formData.costPerUnit || calculatedUnit,
        unit: selectedItem?.unit || 'pcs',
        // Include data for supplier transaction if linking is enabled
        linkToSupplier: linkWithSupplier && formData.supplierId && formData.supplierId !== 'none',
        amountPaid: amountPaid
      };
      
      await onSubmit(purchaseData);
      form.reset({
        stockItemId: selectedItemId || '',
        supplierId: suggestedSupplierId || 'none',
        quantity: 1,
        costPerUnit: undefined,
        totalCost: undefined,
        date: new Date(),
        notes: '',
      });
      setPayFullAmount(true);
    } catch (error) {
      console.error('Error submitting purchase log:', error);
    }
  };

  return (
    <div className="space-y-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Stock Item Selection */}
          <FormField
            control={form.control}
            name="stockItemId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Item</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an item" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {stockItems.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        {item.name} ({item.category})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Purchase Date */}
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Purchase Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Purchase Details Group */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {/* Quantity */}
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity{selectedItem && ` (${selectedItem.unit})`}</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.01" 
                      min="0.01" 
                      {...field} 
                      onChange={(e) => {
                        field.onChange(e);
                        // Clear calculated values when quantity changes
                        setCalculatedTotal(null);
                        setCalculatedUnit(null);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cost Per Unit */}
            <FormField
              control={form.control}
              name="costPerUnit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cost Per Unit</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      placeholder={calculatedUnit !== null ? calculatedUnit.toFixed(2) : "Enter cost per unit"}
                      {...field}
                      value={field.value || ''}
                      onChange={(e) => {
                        // When typing in cost per unit, clear total cost to recalculate
                        if (e.target.value) {
                          form.setValue('totalCost', undefined);
                        }
                        field.onChange(e);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                  {calculatedUnit !== null && !field.value && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Calculated: {calculatedUnit.toFixed(2)}
                    </p>
                  )}
                </FormItem>
              )}
            />

            {/* Total Cost */}
            <FormField
              control={form.control}
              name="totalCost"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Cost</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      placeholder={calculatedTotal !== null ? calculatedTotal.toFixed(2) : "Enter total cost"}
                      {...field}
                      value={field.value || ''}
                      onChange={(e) => {
                        // When typing in total cost, clear cost per unit to recalculate
                        if (e.target.value) {
                          form.setValue('costPerUnit', undefined);
                        }
                        field.onChange(e);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                  {calculatedTotal !== null && !field.value && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Calculated: {calculatedTotal.toFixed(2)}
                    </p>
                  )}
                </FormItem>
              )}
            />
          </div>

          {/* Supplier Selection */}
          <FormField
            control={form.control}
            name="supplierId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Supplier</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || 'none'}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a supplier (optional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  {linkWithSupplier 
                    ? "Select a supplier to link this purchase with supplier transactions" 
                    : "Select a supplier for reference only"}
                </FormDescription>
              </FormItem>
            )}
          />

          {/* Payment handling - only show if linking to supplier and a supplier is selected */}
          {linkWithSupplier && form.watch('supplierId') && form.watch('supplierId') !== 'none' && (
            <div className="bg-muted/30 p-4 rounded-md mt-4">
              <h4 className="text-sm font-semibold mb-2">Payment Details</h4>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="payFullAmount"
                  checked={payFullAmount}
                  onCheckedChange={(checked) => setPayFullAmount(checked as boolean)}
                />
                <Label htmlFor="payFullAmount">
                  Pay full amount now
                </Label>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {payFullAmount 
                  ? "The full amount will be marked as paid in the supplier account" 
                  : "The full amount will be added to the supplier's balance"}
              </p>
            </div>
          )}

          {/* Notes (Optional) */}
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Any additional notes about this purchase" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full">
            <ShoppingBag className="mr-2 h-4 w-4" />
            Log Purchase
          </Button>
        </form>
      </Form>
    </div>
  );
} 