"use client";

import { useState, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { StockItem } from '@/types/stock';
import { StockCount, StockCountItem } from '@/types/stockCount';
import { Printer, Download, ArrowUp, ArrowDown, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { useReactToPrint } from 'react-to-print';
import { formatCurrency } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StockCountVarianceReportProps {
  stockCount: StockCount;
  countItems: StockCountItem[];
  stockItems: Record<string, StockItem>;
  onPrint?: () => void;
}

type SortField = 'name' | 'category' | 'variance' | 'varianceValue';
type SortDirection = 'asc' | 'desc';
type FilterType = 'all' | 'positive' | 'negative' | 'zero';

export function StockCountVarianceReport({
  stockCount,
  countItems,
  stockItems,
  onPrint
}: StockCountVarianceReportProps) {
  const [isPrinting, setIsPrinting] = useState(false);
  const [sortField, setSortField] = useState<SortField>('varianceValue');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterType, setFilterType] = useState<FilterType>('all');
  const printRef = useRef<HTMLDivElement>(null);

  // Handle print
  const handlePrint = useReactToPrint({
    contentRef: printRef,
    onBeforePrint: async () => {
      setIsPrinting(true);
      if (onPrint) onPrint();
    },
    onAfterPrint: async () => {
      setIsPrinting(false);
    },
  });

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Filter and sort items
  const getFilteredAndSortedItems = () => {
    // Filter items
    const filtered = countItems.filter(item => {
      // Only include items with counted quantity
      if (item.countedQuantity === undefined || item.variance === undefined) return false;

      // Apply filter
      switch (filterType) {
        case 'positive':
          return item.variance > 0;
        case 'negative':
          return item.variance < 0;
        case 'zero':
          return item.variance === 0;
        default:
          return true;
      }
    });

    // Sort items
    return filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortField) {
        case 'name':
          comparison = (stockItems[a.stockItemId]?.name || '').localeCompare(stockItems[b.stockItemId]?.name || '');
          break;
        case 'category':
          comparison = (stockItems[a.stockItemId]?.category || '').localeCompare(stockItems[b.stockItemId]?.category || '');
          break;
        case 'variance':
          comparison = (a.variance || 0) - (b.variance || 0);
          break;
        case 'varianceValue':
          comparison = (a.varianceValue || 0) - (b.varianceValue || 0);
          break;
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });
  };

  // Calculate totals
  const calculateTotals = () => {
    let totalVarianceValue = 0;
    let positiveVarianceValue = 0;
    let negativeVarianceValue = 0;

    countItems.forEach(item => {
      if (item.varianceValue !== undefined) {
        totalVarianceValue += item.varianceValue;

        if (item.varianceValue > 0) {
          positiveVarianceValue += item.varianceValue;
        } else if (item.varianceValue < 0) {
          negativeVarianceValue += Math.abs(item.varianceValue);
        }
      }
    });

    return {
      totalVarianceValue,
      positiveVarianceValue,
      negativeVarianceValue
    };
  };

  const totals = calculateTotals();
  const filteredItems = getFilteredAndSortedItems();

  // Toggle sort
  const toggleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Variance Report</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => handlePrint()}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={filterType} onValueChange={(value) => setFilterType(value as FilterType)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by variance" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Items</SelectItem>
              <SelectItem value="positive">Positive Variance</SelectItem>
              <SelectItem value="negative">Negative Variance</SelectItem>
              <SelectItem value="zero">Zero Variance</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div ref={printRef} className={`p-4 ${isPrinting ? 'bg-white text-black' : ''}`}>
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">Variance Report: {stockCount.name}</h1>
          <div className="flex justify-center gap-4 mt-2">
            <p className="text-muted-foreground">Date: {formatDate(stockCount.date)}</p>
            <p className="text-muted-foreground">Type: {stockCount.countType ? stockCount.countType.replace('_', ' ').toUpperCase() : 'FULL'}</p>
            <p className="text-muted-foreground">Area: {stockCount.countArea ? stockCount.countArea.replace('_', ' ').toUpperCase() : 'ALL'}</p>
          </div>
          {stockCount.notes && <p className="mt-2 italic">{stockCount.notes}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Total Variance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className={`text-2xl font-bold ${totals.totalVarianceValue < 0 ? 'text-red-500' : totals.totalVarianceValue > 0 ? 'text-green-500' : ''}`}>
                {formatCurrency(totals.totalVarianceValue)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Positive Variance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-green-500">
                {formatCurrency(totals.positiveVarianceValue)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Negative Variance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-red-500">
                {formatCurrency(totals.negativeVarianceValue)}
              </p>
            </CardContent>
          </Card>
        </div>

        <Table className="border">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">#</TableHead>
              <TableHead className="cursor-pointer" onClick={() => toggleSort('name')}>
                Item Name
                {sortField === 'name' && (
                  sortDirection === 'asc' ? <ArrowUp className="inline h-4 w-4 ml-1" /> : <ArrowDown className="inline h-4 w-4 ml-1" />
                )}
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => toggleSort('category')}>
                Category
                {sortField === 'category' && (
                  sortDirection === 'asc' ? <ArrowUp className="inline h-4 w-4 ml-1" /> : <ArrowDown className="inline h-4 w-4 ml-1" />
                )}
              </TableHead>
              <TableHead>Unit</TableHead>
              <TableHead>System Qty</TableHead>
              <TableHead>Counted Qty</TableHead>
              <TableHead className="cursor-pointer" onClick={() => toggleSort('variance')}>
                Variance Qty
                {sortField === 'variance' && (
                  sortDirection === 'asc' ? <ArrowUp className="inline h-4 w-4 ml-1" /> : <ArrowDown className="inline h-4 w-4 ml-1" />
                )}
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => toggleSort('varianceValue')}>
                Variance Value
                {sortField === 'varianceValue' && (
                  sortDirection === 'asc' ? <ArrowUp className="inline h-4 w-4 ml-1" /> : <ArrowDown className="inline h-4 w-4 ml-1" />
                )}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No variance data found
                </TableCell>
              </TableRow>
            ) : (
              filteredItems.map((item, index) => {
                const stockItem = stockItems[item.stockItemId];
                if (!stockItem) return null;

                // Filter items by count area if not 'all'
                if (stockCount.countArea && stockCount.countArea !== 'all') {
                  // Map categories to areas (customize based on your category structure)
                  const areaMap: Record<string, string[]> = {
                    'kitchen': ['ingredients', 'produce', 'meat', 'dairy', 'spices'],
                    'bar': ['beverages', 'alcohol', 'wine', 'beer', 'spirits'],
                    'storage': ['dry goods', 'paper goods', 'cleaning', 'supplies']
                  };

                  if (!areaMap[stockCount.countArea]?.includes(stockItem.category.toLowerCase())) {
                    return null;
                  }
                }

                return (
                  <TableRow key={item.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell className="font-medium">{stockItem.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span>{stockItem.category}</span>
                        {stockCount.countArea && stockCount.countArea !== 'all' && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full">
                            {stockCount.countArea}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{stockItem.unit}</TableCell>
                    <TableCell>{item.theoreticalQuantity}</TableCell>
                    <TableCell>{item.countedQuantity}</TableCell>
                    <TableCell className={`font-medium ${item.variance && item.variance < 0 ? 'text-red-500' : item.variance && item.variance > 0 ? 'text-green-500' : ''}`}>
                      {item.variance !== undefined ? (
                        item.variance > 0 ? `+${item.variance}` : item.variance
                      ) : 'N/A'}
                    </TableCell>
                    <TableCell className={`font-medium ${item.varianceValue && item.varianceValue < 0 ? 'text-red-500' : item.varianceValue && item.varianceValue > 0 ? 'text-green-500' : ''}`}>
                      {item.varianceValue !== undefined ? formatCurrency(item.varianceValue) : 'N/A'}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
