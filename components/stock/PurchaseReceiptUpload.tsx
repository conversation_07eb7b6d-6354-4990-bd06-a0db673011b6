"use client";

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { Camera, Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useCamera } from '@/hooks/use-camera';
import { Capacitor } from '@capacitor/core';
import { CameraResultType } from '@capacitor/camera';

interface PurchaseReceiptUploadProps {
  onImageUploaded: (imageData: { fileId: string; localPath?: string; previewUrl: string; file?: File }) => void;
  onImageRemoved: () => void;
  currentImage?: { fileId: string; localPath?: string; previewUrl: string; file?: File } | null;
  disabled?: boolean;
  isMobile?: boolean;
}

// Image compression utility
const compressImage = (file: File, maxWidth: number = 1024, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/webp',
            lastModified: Date.now(),
          });
          resolve(compressedFile);
        } else {
          resolve(file);
        }
      }, 'image/webp', quality);
    };
    
    img.src = URL.createObjectURL(file);
  });
};

// Check if we're in Electron environment
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

export function PurchaseReceiptUpload({
  onImageUploaded,
  onImageRemoved,
  currentImage,
  disabled = false,
  isMobile = false
}: PurchaseReceiptUploadProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const [uploadProgress, setUploadProgress] = useState({ isUploading: false, progress: 0 });
  const [isUploading, setIsUploading] = useState(false);
  
  const { takePhoto, pickPhoto, showImagePicker, isNative } = useCamera();

  // Generate file path for local storage (Electron only)
  const generateLocalPath = useCallback((filename: string, purchaseId?: string) => {
    if (!isElectron || !user?.restaurantId) return undefined;
    
    const sanitizedRestaurantId = user.restaurantId.replace(/[^a-zA-Z0-9]/g, '_');
    const sanitizedPurchaseId = purchaseId || `temp_${Date.now()}`;
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    
    return `/userData/images/${sanitizedRestaurantId}/purchases/${sanitizedPurchaseId}/${sanitizedFilename}`;
  }, [user?.restaurantId]);

  // Handle file processing (prepare for later upload)
  const processFile = useCallback(async (file: File, purchaseId?: string) => {
    if (!user?.restaurantId) {
      toast({
        title: '❌ Error',
        description: 'Restaurant ID not found. Please log in again.',
        variant: 'destructive'
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      // Compress image
      const compressedFile = await compressImage(file);
      
      const timestamp = Date.now();
      const fileExtension = compressedFile.name.split('.').pop() || 'webp';
      const filename = `receipt_${timestamp}.${fileExtension}`;
      
      setUploadProgress({ isUploading: true, progress: 100 });
      
      // Generate local path for reference
      const localPath = generateLocalPath(filename, purchaseId);
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(compressedFile);
      
      // Pass the actual File object instead of just the filename
      // This way the form can pass the File to addPurchaseLog
      onImageUploaded({
        fileId: filename,
        localPath,
        previewUrl,
        file: compressedFile // Add the actual file for later upload
      });
      
      toast({
        title: '📎 Image Ready',
        description: 'Image will be attached when transaction is saved'
      });
      
      setTimeout(() => {
        setUploadProgress({ isUploading: false, progress: 0 });
        setIsUploading(false);
      }, 1000);
    } catch (error) {
      console.error('Error processing image:', error);
      toast({
        title: '❌ Upload Error',
        description: error instanceof Error ? error.message : 'Failed to process image',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
      setUploadProgress({ isUploading: false, progress: 0 });
      setIsUploading(false);
    }
  }, [user?.restaurantId, generateLocalPath, onImageUploaded, toast]);

  // Handle native camera/photo picker
  const handleNativeImagePicker = useCallback(async () => {
    if (!isNative) return;
    
    try {
      const photo = await showImagePicker({
        quality: 80,
        resultType: CameraResultType.Base64,
      });
      
      if (photo.base64String) {
        // Convert base64 to File object
        const byteCharacters = atob(photo.base64String);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const file = new File([byteArray], `receipt_${Date.now()}.${photo.format}`, { 
          type: `image/${photo.format}` 
        });
        
        processFile(file);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      toast({
        title: '❌ Error',
        description: 'Failed to capture photo. Please try again.',
        variant: 'destructive'
      });
    }
  }, [isNative, showImagePicker, processFile, toast]);

  // Handle file input change
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
    // Reset input
    event.target.value = '';
  }, [processFile]);

  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
    
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      processFile(file);
    } else {
      toast({
        title: '❌ Invalid File',
        description: 'Please select an image file',
        variant: 'destructive'
      });
    }
  }, [processFile, toast]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  }, []);

  // Handle remove image
  const handleRemoveImage = useCallback(() => {
    if (currentImage?.previewUrl) {
      URL.revokeObjectURL(currentImage.previewUrl);
    }
    onImageRemoved();
  }, [currentImage, onImageRemoved]);

  const isLoading = isUploading || isProcessing;

  return (
    <div className="space-y-2">
      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || isLoading}
      />
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || isLoading}
      />

      {currentImage?.previewUrl ? (
        // Image preview
        <Card className="relative">
          <CardContent className="p-2">
            <div className="relative">
              <img
                src={currentImage.previewUrl}
                alt="Receipt preview"
                className={cn(
                  "w-full rounded-md object-cover",
                  isMobile ? "h-32" : "h-40"
                )}
              />
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-1 right-1 h-6 w-6"
                onClick={handleRemoveImage}
                disabled={disabled}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-1 text-center">
              Receipt stored (viewing coming soon)
            </p>
          </CardContent>
        </Card>
      ) : (
        // Upload area
        <div className="space-y-2">
          {/* Camera button for mobile */}
          {isNative && (
            <Button
              type="button"
              variant="outline"
              className="w-full"
              onClick={handleNativeImagePicker}
              disabled={true}
            >
              <Camera className="h-4 w-4 mr-2" />
              Coming soon
            </Button>
          )}
          
          {/* File upload area */}
          <div
            className={cn(
              "relative group flex flex-col items-center justify-center border-2 border-dashed rounded-md cursor-pointer transition-colors hover:border-primary/50 hover:bg-muted/50",
              isDragging && "border-primary bg-primary/10",
              disabled && "cursor-not-allowed opacity-50",
              isMobile ? "min-h-[80px] p-2" : "min-h-[80px] p-3"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => false} // Disabled for coming soon
          >
            {isLoading ? (
              <div className="flex flex-col items-center gap-1 text-center">
                <Loader2 className="h-5 w-5 text-primary animate-spin" />
                <p className="text-[11px] text-muted-foreground">Traitement en cours...</p>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-1 text-center">
                <Upload className="h-5 w-5 text-muted-foreground" />
                <p className="text-[11px] text-muted-foreground">
                  Coming soon
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 