"use client";

import React, { useState } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { StockItem } from '@/types/stock';
import { PlusCircle, MinusCircle, ArrowUp, ArrowDown } from 'lucide-react';
import { cn } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

// Schema for stock adjustment validation
const stockAdjustmentSchema = z.object({
  stockItemId: z.string().min(1, { message: "Please select a stock item." })
    .transform(val => val === "none" ? "" : val),
  adjustmentType: z.enum(["addition", "reduction"], {
    required_error: "Please select an adjustment type.",
  }),
  quantity: z.coerce.number().min(0.01, { message: "Quantity must be greater than zero." }),
  reason: z.string().optional(),
});

interface StockAdjustmentFormProps {
  onSubmit: (data: z.infer<typeof stockAdjustmentSchema>) => void;
  stockItems: StockItem[];
  selectedItemId?: string;
  compact?: boolean;
}

export function StockAdjustmentForm({
  onSubmit,
  stockItems,
  selectedItemId,
  compact = false
}: StockAdjustmentFormProps) {
  const [quickAdjustValue, setQuickAdjustValue] = useState<number | null>(null);

  // Get the selected stock item
  const selectedItem = selectedItemId
    ? stockItems.find(item => item.id === selectedItemId)
    : null;

  // Initialize form with default values
  const form = useForm<z.infer<typeof stockAdjustmentSchema>>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      stockItemId: selectedItemId || "none",
      adjustmentType: "addition",
      quantity: 0,
      reason: ""
    }
  });

  // Get current values from the form
  const adjustmentType = form.watch('adjustmentType');

  // Handle quick adjust clicks
  const handleQuickAdjust = (value: number) => {
    setQuickAdjustValue(value);
    form.setValue('quantity', value);
  };

  // Handle form submission
  const handleSubmit = (values: z.infer<typeof stockAdjustmentSchema>) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-5">
        <FormField
          control={form.control}
          name="stockItemId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Stock Item</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={!!selectedItemId}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select stock item" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {!selectedItemId && <SelectItem value="none">Select an item</SelectItem>}
                  {stockItems.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name} {typeof item.quantity === 'number' ? `(${item.quantity} ${item.unit})` : `(${item.unit})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                {selectedItem && (
                  <span>Current stock: <strong>
                    {typeof selectedItem.quantity === 'number'
                      ? `${selectedItem.quantity} ${selectedItem.unit}`
                      : 'Not tracked'}
                  </strong></span>
                )}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="adjustmentType"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Adjustment Type</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="addition" id="addition" className="border-green-400" />
                    <label
                      htmlFor="addition"
                      className="flex items-center gap-2 cursor-pointer rounded-md p-2 px-3 hover:bg-green-50 transition-colors"
                    >
                      <div className="bg-green-100 p-1 rounded-full">
                        <ArrowUp className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="space-y-0.5">
                        <div className="text-sm font-medium">Add Stock</div>
                        <div className="text-xs text-muted-foreground">
                          Increase available quantity
                        </div>
                      </div>
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="reduction" id="reduction" className="border-amber-400" />
                    <label
                      htmlFor="reduction"
                      className="flex items-center gap-2 cursor-pointer rounded-md p-2 px-3 hover:bg-amber-50 transition-colors"
                    >
                      <div className="bg-amber-100 p-1 rounded-full">
                        <ArrowDown className="h-4 w-4 text-amber-600" />
                      </div>
                      <div className="space-y-0.5">
                        <div className="text-sm font-medium">Remove Stock</div>
                        <div className="text-xs text-muted-foreground">
                          Decrease available quantity
                        </div>
                      </div>
                    </label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Quantity to {adjustmentType === 'addition' ? 'Add' : 'Remove'}</FormLabel>

              {/* Quick adjust section */}
              {selectedItem && typeof selectedItem.quantity === 'number' && (
                <div className="grid grid-cols-4 gap-2 mb-3">
                  {[1, 5, 10, (selectedItem.minLevel || 10) / 2].map((value, index) => (
                    <Button
                      key={index}
                      type="button"
                      variant="outline"
                      size="sm"
                      className={cn(
                        "text-xs h-8",
                        quickAdjustValue === value && adjustmentType === 'addition' && "bg-green-50 border-green-200",
                        quickAdjustValue === value && adjustmentType === 'reduction' && "bg-amber-50 border-amber-200"
                      )}
                      onClick={() => handleQuickAdjust(value)}
                    >
                      {value === (selectedItem.minLevel || 10) / 2 ? `${Math.round(value)} (½ min)` : value}
                    </Button>
                  ))}
                </div>
              )}

              <div className="relative">
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    min="0.01"
                    className={cn(
                      "pr-16",
                      adjustmentType === 'addition' && "focus-visible:ring-green-400",
                      adjustmentType === 'reduction' && "focus-visible:ring-amber-400"
                    )}
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      setQuickAdjustValue(null);
                    }}
                  />
                </FormControl>
                {selectedItem && (
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-muted-foreground">
                    {selectedItem.unit}
                  </div>
                )}
              </div>
              <FormDescription>
                {adjustmentType === 'addition'
                  ? "Enter the amount to add to inventory"
                  : "Enter the amount to remove from inventory"}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Reason (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={adjustmentType === 'addition'
                    ? "Why are you adding stock? (e.g., purchase, inventory correction)"
                    : "Why are you removing stock? (e.g., usage, spoilage, damage)"
                  }
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Adding a reason helps with inventory tracking and auditing
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-3 pt-2">
          <Button
            type="submit"
            className={cn(
              adjustmentType === 'addition' && "bg-green-600 hover:bg-green-700",
              adjustmentType === 'reduction' && "bg-amber-600 hover:bg-amber-700"
            )}
          >
            {adjustmentType === 'addition' ? (
              <PlusCircle className="mr-2 h-4 w-4" />
            ) : (
              <MinusCircle className="mr-2 h-4 w-4" />
            )}
            {adjustmentType === 'addition' ? 'Add Stock' : 'Remove Stock'}
          </Button>
        </div>
      </form>
    </Form>
  );
}