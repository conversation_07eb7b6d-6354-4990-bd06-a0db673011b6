"use client";

import { useState } from 'react';
import { StockItem, StockAdjustment } from '@/types/stock';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  PlusCircle,
  MinusCircle,
  Clock,
  AlertTriangle,
  CheckCircle2,
  RefreshCw,
  ShoppingCart,
  Package,
  BarChart3,
  History,
  AlertCircle,
  Calendar,
  ArrowDown,
  ArrowUp,
  DollarSign,
  ChefHat
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { StockAdjustmentForm } from './StockAdjustmentForm';
import { PurchaseForm } from '@/components/stock/PurchaseForm';
import { Supplier } from '@/types/suppliers';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { useCOGSV4 } from '@/lib/hooks/useCOGSV4';

interface StockItemDetailsProps {
  item: StockItem;
  adjustments: StockAdjustment[];
  onAdjustStock: (adjustment: Omit<StockAdjustment, '_id' | 'date' | 'performedBy'>) => Promise<void>;
  onRefreshAdjustments: () => Promise<void>;
  isLoadingAdjustments?: boolean;
  suppliers?: Array<Supplier>;
  onPurchaseFromSupplier?: (data: any) => Promise<void>;
}

export function StockItemDetails({
  item,
  adjustments,
  onAdjustStock,
  onRefreshAdjustments,
  isLoadingAdjustments = false,
  suppliers = [],
  onPurchaseFromSupplier
}: StockItemDetailsProps) {
  const [isAdjustmentDialogOpen, setIsAdjustmentDialogOpen] = useState(false);
  const [isPurchaseDialogOpen, setIsPurchaseDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // Get COGS settings to check if it's enabled
  const { isCogsEnabled } = useCOGSV4();

  // Calculate stock status
  const getStockStatus = () => {
    // If COGS is not enabled or quantity is undefined, show a neutral status
    if (!isCogsEnabled || item.quantity === undefined) {
      return { status: 'unknown', label: /* knowledge */'Quantité non suivie'/* endknowledge */, color: 'secondary', icon: Package };
    }

    if (item.quantity <= 0) {
      return { status: 'out-of-stock', label: /* knowledge */'Rupture de stock'/* endknowledge */, color: 'destructive', icon: AlertCircle };
    } else if (item.quantity <= (item.minLevel || 0)) {
      return { status: 'low', label: /* knowledge */'Stock faible'/* endknowledge */, color: 'warning', icon: AlertTriangle };
    } else {
      return { status: 'in-stock', label: /* knowledge */'En stock'/* endknowledge */, color: 'success', icon: CheckCircle2 };
    }
  };

  const stockStatus = getStockStatus();

  // Get supplier name if available
  const getSupplierName = () => {
    if (!item.supplierId) return /* knowledge */'Non assigné'/* endknowledge */;
    const supplier = suppliers.find(s => s.id === item.supplierId);
    return supplier ? supplier.name : /* knowledge */'Inconnu'/* endknowledge */;
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), /* knowledge */'d MMM yyyy HH:mm'/* endknowledge */);
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Handle adjustment submission
  const handleAdjustment = async (data: any) => {
    await onAdjustStock({
      id: item.id,
      stockItemId: item.id,
      adjustmentType: data.adjustmentType,
      quantity: data.quantity,
      reason: data.reason
    });
    setIsAdjustmentDialogOpen(false);
  };

  // Handle purchase submission
  const handlePurchase = async (data: any) => {
    if (onPurchaseFromSupplier) {
      await onPurchaseFromSupplier({
        ...data,
        stockItemId: item.id
      });
      setIsPurchaseDialogOpen(false);
    }
  };

  // Calculate stock level as percentage
  const getStockLevelPercentage = () => {
    if (!isCogsEnabled || item.quantity === undefined) return 0;
    if ((item.minLevel || 0) === 0) return 100;
    const targetLevel = (item.minLevel || 10) * 2; // Assuming "full" is double the min level
    return Math.min(Math.round((item.quantity / targetLevel) * 100), 100);
  };

  // Get stock level color
  const getStockLevelColor = () => {
    if (!isCogsEnabled || item.quantity === undefined) return "bg-gray-300";
    const percentage = getStockLevelPercentage();
    if (percentage <= 25) return "bg-red-500";
    if (percentage <= 50) return "bg-amber-500";
    return "bg-emerald-500";
  };

  // Filter recent purchases from adjustments
  const purchases = adjustments.filter(adj =>
    adj.adjustmentType === 'addition' &&
    adj.reason?.toLowerCase().includes('purchase')
  );

  return (
    <div className="space-y-6">
      {/* Quick action bar */}
      <div className="flex flex-wrap gap-2 justify-end">
        <Button
          variant="outline"
          onClick={() => {
            setActiveTab("adjustments");
            setIsAdjustmentDialogOpen(true);
          }}
          className="bg-blue-50 border-blue-200 hover:bg-blue-100 text-blue-700"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Adjust Stock
        </Button>

        <Button
          variant="outline"
          onClick={() => {
            setActiveTab("purchases");
            setIsPurchaseDialogOpen(true);
          }}
          className="bg-green-50 border-green-200 hover:bg-green-100 text-green-700"
        >
          <ShoppingCart className="mr-2 h-4 w-4" />
          Purchase
        </Button>
      </div>

      {/* Item header card */}
      <Card className="border-t-4" style={{ borderTopColor: `var(--${stockStatus.color})` }}>
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-2">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full bg-${stockStatus.color}/20`}>
                <stockStatus.icon className={`h-6 w-6 text-${stockStatus.color}`} />
              </div>
              <div>
                <CardTitle className="text-2xl">{item.name}</CardTitle>
                <CardDescription>
                  {/* knowledge: fallback for missing category v4 */}
                  <span className="font-medium text-muted-foreground">Category:</span>{' '}
                  {item.category ? <span>{item.category}</span> : <span>/* knowledge */'Non catégorisé'/* endknowledge */</span>}
                  {/* endknowledge */}
                  {isCogsEnabled && (
                    <span className="ml-2 inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700">
                      <ChefHat className="mr-1 h-3 w-3" />
                      COGS Enabled
                    </span>
                  )}
                </CardDescription>
              </div>
            </div>
            <Badge variant={stockStatus.color as any} className="h-6 px-3 py-1">
              {stockStatus.label}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pb-3">
          {isCogsEnabled && item.quantity !== undefined ? (
            <div className="flex flex-col space-y-1 mb-2">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-sm">Stock Level</span>
                <span className="text-sm">{typeof item.quantity === 'number' ? item.quantity : 0} sur {(typeof item.minLevel === 'number' ? item.minLevel : 10) * 2} {item.unit}</span>
              </div>
              <Progress value={getStockLevelPercentage()} className={cn("h-2", getStockLevelColor())} />
            </div>
          ) : (
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Unit of Measurement:</span>
              <span className="font-medium">{item.unit}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main content tabs */}
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="adjustments" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            <span>Adjustments</span>
          </TabsTrigger>
          <TabsTrigger value="purchases" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            <span>Purchases</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Item Details</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="grid grid-cols-2 items-center gap-y-2">
                  {isCogsEnabled && (
                    <>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Package className="h-4 w-4" />
                        <span>Quantity</span>
                      </div>
                      <span className="font-medium text-right">{item.quantity !== undefined ? `${item.quantity} ${item.unit}` : 'Not tracked'}</span>

                      <div className="flex items-center gap-2 text-muted-foreground">
                        <AlertTriangle className="h-4 w-4" />
                        <span>Min Level</span>
                      </div>
                      <span className="text-right">{item.minLevel !== undefined ? `${item.minLevel} ${item.unit}` : 'Not set'}</span>
                    </>
                  )}

                  <div className="flex items-center gap-2 text-muted-foreground">
                    <DollarSign className="h-4 w-4" />
                    <span>Cost per Unit</span>
                  </div>
                  <span className="text-right">{item.costPerUnit !== undefined ? `$${item.costPerUnit.toFixed(2)}` : 'Not set'}</span>

                  <div className="flex items-center gap-2 text-muted-foreground">
                    <ShoppingCart className="h-4 w-4" />
                    <span>Supplier</span>
                  </div>
                  <span className="text-right">{getSupplierName()}</span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="grid grid-cols-2 items-center gap-y-2">
                  {isCogsEnabled && item.quantity !== undefined && item.costPerUnit !== undefined && (
                    <>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <DollarSign className="h-4 w-4" />
                        <span>Total Value</span>
                      </div>
                      <span className="font-medium text-right">${(item.quantity * (item.costPerUnit ?? 0)).toFixed(2)}</span>
                    </>
                  )}

                  {item.expiryDate && (
                    <>
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>Expires</span>
                      </div>
                      <div className="text-right">
                        {format(new Date(item.expiryDate), 'MMM d, yyyy')}
                        <div className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(item.expiryDate), { addSuffix: true })}
                        </div>
                      </div>
                    </>
                  )}

                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>Last Updated</span>
                  </div>
                  <div className="text-right">
                    {format(new Date(item.updatedAt), 'MMM d, yyyy')}
                    <div className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(item.updatedAt), { addSuffix: true })}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Recent Adjustments Summary */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {adjustments.length === 0 ? (
                  <div className="py-6 text-center text-muted-foreground">
                    No activity recorded yet
                  </div>
                ) : (
                  <div className="space-y-3">
                    {adjustments.slice(0, 3).map(adjustment => (
                      <div key={adjustment.id} className="flex items-start gap-3 p-2 rounded-md border border-muted">
                        <div className={`p-1.5 rounded-full ${adjustment.adjustmentType === 'addition' ? 'bg-green-100' : 'bg-amber-100'}`}>
                          {adjustment.adjustmentType === 'addition' ? (
                            <ArrowUp className="h-4 w-4 text-green-600" />
                          ) : (
                            <ArrowDown className="h-4 w-4 text-amber-600" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <div className="text-sm font-medium">
                              {adjustment.adjustmentType === 'addition' ? 'Added' : 'Removed'} {adjustment.quantity} {item.unit}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {format(new Date(adjustment.date), 'MMM d')}
                            </div>
                          </div>
                          {adjustment.reason && (
                            <div className="text-xs text-muted-foreground truncate">
                              {adjustment.reason}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    {adjustments.length > 3 && (
                      <Button variant="ghost" className="w-full text-xs" onClick={() => setActiveTab("adjustments")}>
                        View all {adjustments.length} adjustments
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Purchase Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Quick Restock</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {isCogsEnabled && item.quantity !== undefined && (item.minLevel !== undefined) && (item.quantity <= item.minLevel) ? (
                  <>
                  <div className={`flex p-3 rounded-md ${item.quantity <= 0 ? 'bg-red-50 text-red-800' : 'bg-amber-50 text-amber-800'}`}>
                    <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                    <div className="text-sm">
                      {item.quantity <= 0 ? (
                        <span>This item is <strong>out of stock</strong>. Consider purchasing more immediately.</span>
                      ) : (
                        <span>Stock level is <strong>below minimum</strong>. Consider restocking soon.</span>
                      )}
                    </div>
                  </div>
                  </>
                ) : isCogsEnabled && item.quantity !== undefined ? (
                  <>
                  <div className="flex p-3 rounded-md bg-green-50 text-green-800">
                    <CheckCircle2 className="h-5 w-5 mr-2 flex-shrink-0" />
                    <div className="text-sm">
                      Current stock level is <strong>sufficient</strong>.
                    </div>
                  </div>
                  </>
                ) : null}

                <div className="pt-2">
                  <Button
                    className="w-full"
                    onClick={() => {
                      setIsPurchaseDialogOpen(true);
                      setActiveTab("purchases");
                    }}
                    variant={isCogsEnabled && item.quantity !== undefined && (item.minLevel !== undefined) && (item.quantity <= item.minLevel) ? "default" : "outline"}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Purchase More Stock
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Adjustments Tab */}
        <TabsContent value="adjustments" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Adjustment History</CardTitle>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => setIsAdjustmentDialogOpen(true)}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Adjust Stock
                  </Button>
                  <Button variant="outline" size="icon" onClick={onRefreshAdjustments} disabled={isLoadingAdjustments}>
                    <RefreshCw className={`h-4 w-4 ${isLoadingAdjustments ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </div>
              <CardDescription>Complete history of stock adjustments for this item</CardDescription>
            </CardHeader>
            <CardContent>
              {adjustments.length === 0 ? (
                <div className="py-8 text-center">
                  <div className="mx-auto bg-muted w-12 h-12 rounded-full flex items-center justify-center mb-3">
                    <History className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-sm font-medium">No Adjustment History</h3>
                  <p className="text-sm text-muted-foreground mt-1 max-w-md mx-auto">
                    There is no record of stock adjustments for this item yet. Use the "Adjust Stock" button to add or remove stock.
                  </p>
                </div>
              ) : (
                <div className="relative overflow-x-auto rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Performed By</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {adjustments.map(adjustment => (
                        <TableRow key={adjustment.id}>
                          <TableCell className="whitespace-nowrap">
                            <div className="text-sm">
                              {format(new Date(adjustment.date), 'MMM d, yyyy')}
                              <div className="text-xs text-muted-foreground">
                                {format(new Date(adjustment.date), 'h:mm a')}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              {adjustment.adjustmentType === 'addition' ? (
                                <div className="bg-green-100 p-1 rounded-full mr-2">
                                  <PlusCircle className="h-4 w-4 text-green-600" />
                                </div>
                              ) : (
                                <div className="bg-amber-100 p-1 rounded-full mr-2">
                                  <MinusCircle className="h-4 w-4 text-amber-600" />
                                </div>
                              )}
                              <span className="capitalize">
                                {adjustment.adjustmentType === 'addition' ? 'Added' : 'Removed'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {adjustment.quantity} {item.unit}
                          </TableCell>
                          <TableCell>
                            {adjustment.reason || 'No reason provided'}
                          </TableCell>
                          <TableCell className="whitespace-nowrap">
                            {adjustment.performedBy || 'System'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Purchases Tab */}
        <TabsContent value="purchases" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Purchase History</CardTitle>
                <Button variant="default" onClick={() => setIsPurchaseDialogOpen(true)}>
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Purchase From Supplier
                </Button>
              </div>
              <CardDescription>History of purchases for this item</CardDescription>
            </CardHeader>
            <CardContent>
              {purchases.length === 0 ? (
                <div className="py-8 text-center">
                  <div className="mx-auto bg-muted w-12 h-12 rounded-full flex items-center justify-center mb-3">
                    <ShoppingCart className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-sm font-medium">No Purchase History</h3>
                  <p className="text-sm text-muted-foreground mt-1 max-w-md mx-auto">
                    There is no record of purchases for this item yet. Use the "Purchase From Supplier" button to record a new purchase.
                  </p>
                </div>
              ) : (
                <div className="relative overflow-x-auto rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Supplier</TableHead>
                        <TableHead>Cost</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {purchases.map(purchase => (
                        <TableRow key={purchase.id}>
                          <TableCell className="whitespace-nowrap">
                            <div className="text-sm">
                              {format(new Date(purchase.date), 'MMM d, yyyy')}
                              <div className="text-xs text-muted-foreground">
                                {format(new Date(purchase.date), 'h:mm a')}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {purchase.quantity} {item.unit}
                          </TableCell>
                          <TableCell>
                            {getSupplierName()}
                          </TableCell>
                          <TableCell>
                            ${(purchase.quantity * (item.costPerUnit ?? 0)).toFixed(2)}
                          </TableCell>
                          <TableCell>
                            {purchase.reason?.replace(/purchase/i, '').trim() || 'No notes'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Purchase from Supplier Dialog */}
      <Dialog open={isPurchaseDialogOpen} onOpenChange={setIsPurchaseDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Purchase {item.name} From Supplier</DialogTitle>
          </DialogHeader>
          {suppliers && (
            <PurchaseForm
              onSubmit={handlePurchase}
              stockItem={item}
              suppliers={suppliers.filter(s => s.isActive)}
              suggestedSupplierId={item.supplierId || undefined}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Adjust Stock Dialog */}
      <Dialog open={isAdjustmentDialogOpen} onOpenChange={setIsAdjustmentDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Adjust Stock for {item.name}</DialogTitle>
          </DialogHeader>
          <StockAdjustmentForm
            onSubmit={handleAdjustment}
            stockItems={[item]}
            selectedItemId={item.id}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}