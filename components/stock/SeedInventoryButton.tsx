"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Sprout } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { runUniversalSeedV4 } from '@/lib/seed-data/universal-seed';

export function SeedInventoryButton() {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSeed = async () => {
    setLoading(true);
    try {
      await runUniversalSeedV4();
      toast({
        title: '🌱 Seed réussi !',
        description: 'Toutes les données de base (menu, inventaire, fournisseurs, recettes) ont été créées.',
      });
      // Optionally: refresh inventory/menu/suppliers here if hooks available
    } catch (err) {
      toast({
        title: '❌ Erreur lors du seed',
        description: 'Un problème est survenu. Vérifiez la console.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button variant="outline" size="sm" onClick={handleSeed} disabled={loading}>
      {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Sprout className="h-4 w-4 mr-2" />}
      Seed universel v4
    </Button>
  );
}
