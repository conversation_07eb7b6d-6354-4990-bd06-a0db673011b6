"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Image as ImageIcon, Eye, Download, X, Loader2, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ReceiptImageViewerProps {
  receiptImage?: string; // PouchDB attachment filename
  receiptImageLocal?: string; // Local path for Electron (deprecated)
  transactionId?: string; // Transaction ID for PouchDB attachment lookup
  purchaseId?: string; // Legacy support - maps to transactionId
  className?: string;
  showBadge?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isMobile?: boolean;
}

// Check if we're in Electron environment
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

export function ReceiptImageViewer({
  receiptImage,
  receiptImageLocal,
  transactionId,
  purchaseId, // Legacy support
  className,
  showBadge = false,
  size = 'md',
  isMobile = false
}: ReceiptImageViewerProps) {
  // Use transactionId or fall back to purchaseId for legacy support
  const actualTransactionId = transactionId || purchaseId;
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const loadingRef = useRef<string | null>(null);

  // Size configurations
  const sizeConfig = {
    sm: { container: 'h-8 w-8', icon: 'h-4 w-4', badge: 'text-xs' },
    md: { container: 'h-12 w-12', icon: 'h-6 w-6', badge: 'text-sm' },
    lg: { container: 'h-16 w-16', icon: 'h-8 w-8', badge: 'text-base' },
  };

  // Load image URL using new receipt system
  useEffect(() => {
    const loadImage = async () => {
      if (!receiptImage && !actualTransactionId) {
        console.log('🖼️ No image data provided');
        setImageUrl(null);
        setIsLoading(false);
        setError(null);
        loadingRef.current = null;
        return;
      }

      // Prevent loading the same image multiple times
      const currentKey = `${actualTransactionId}-${receiptImage}`;
      if (loadingRef.current === currentKey) {
        console.log('🖼️ Already loading this image, skipping');
        return;
      }

      console.log('🖼️ Loading receipt image...', { receiptImage, transactionId: actualTransactionId });
      setIsLoading(true);
      setError(null);
      loadingRef.current = currentKey;

      try {
        // Use new receipt operations
        if (actualTransactionId) {
          const { getReceiptFromTransaction } = await import('@/lib/db/v4/operations/purchase-receipt-ops');
          
          const receiptData = await getReceiptFromTransaction(actualTransactionId, receiptImage);
          
          if (receiptData && loadingRef.current === currentKey) {
            setImageUrl(receiptData.dataUrl);
            setIsLoading(false);
            console.log('🖼️ Successfully loaded receipt image');
            return;
          }
        }

        // No image available
        if (loadingRef.current === currentKey) {
          console.log('🖼️ No receipt image could be loaded');
          setImageUrl(null);
          setError('Receipt not available');
        }
      } catch (error) {
        console.error('🖼️ Error loading receipt image:', error);
        if (loadingRef.current === currentKey) {
          setError('Failed to load receipt');
          setImageUrl(null);
        }
      } finally {
        if (loadingRef.current === currentKey) {
          setIsLoading(false);
          loadingRef.current = null;
        }
      }
    };

    loadImage();
  }, [receiptImage, actualTransactionId]);

  // Don't render anything if no image data
  if (!receiptImage && !actualTransactionId) {
    return null;
  }

  const config = sizeConfig[size];

  // Download image handler
  const handleDownload = async () => {
    if (!imageUrl || imageUrl.startsWith('data:')) {
      // For data URLs, create a download link directly
      if (imageUrl) {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = `receipt-${actualTransactionId || receiptImage || 'image'}.webp`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      return;
    }
    
    try {
      console.error('Download not implemented for this image type');
    } catch (error) {
      console.error('Failed to download image:', error);
    }
  };

  return (
    <>
      <div className={cn("relative", className)}>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "border-dashed transition-all duration-200",
            config.container,
            isLoading && "animate-pulse",
            error && "border-red-300 bg-red-50",
            imageUrl && !error && "border-green-300 bg-green-50 hover:bg-green-100"
          )}
          onClick={() => setIsDialogOpen(true)}
          disabled={isLoading}
        >
          <ImageIcon className={cn("text-muted-foreground", config.icon)} />
        </Button>

        {showBadge && (
          <Badge 
            variant="secondary"
            className={cn("absolute -top-2 -right-2", config.badge)}
          >
            Soon
          </Badge>
        )}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Receipt Image</span>
              <div className="flex gap-2">
                {imageUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDialogOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex flex-col items-center justify-center py-12 space-y-3">
              <ImageIcon className="h-8 w-8 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">Receipt viewing coming soon</p>
              <p className="text-xs text-muted-foreground">Image storage is working, display feature in development</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
} 