"use client";

import React, { useEffect, useState } from 'react';
import { format, subDays, isWithinInterval } from 'date-fns';
import { PurchaseTransaction } from '@/lib/db/v4/schemas/purchase-transaction-schema';
import { StockItem } from '@/types/stock';
import { Supplier } from '@/types/suppliers';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ClipboardList, TrendingDown, TrendingUp, Calendar, Package, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ReceiptImageViewer } from './ReceiptImageViewer';

interface PurchaseHistoryViewProps {
  stockItem: StockItem | null;
  purchases: PurchaseTransaction[];
  suppliers: Supplier[];
  isLoading?: boolean;
  isMobile?: boolean;
}

export function PurchaseHistoryView({ stockItem, purchases, suppliers, isLoading = false, isMobile = false }: PurchaseHistoryViewProps) {
  const [sortedPurchases, setSortedPurchases] = useState<PurchaseTransaction[]>([]);
  const [lastSevenDaysSpend, setLastSevenDaysSpend] = useState<number>(0);
  const [lastThirtyDaysSpend, setLastThirtyDaysSpend] = useState<number>(0);
  const [averageUnitCost, setAverageUnitCost] = useState<number | null>(null);

  // Debug: Log purchases with images
  useEffect(() => {
    const purchasesWithImages = purchases.filter(p => p.hasReceiptImage);
    console.log('📈 PurchaseHistoryView Debug:', {
      stockItemId: stockItem?.id,
      stockItemName: stockItem?.name,
      totalPurchases: purchases.length,
      purchasesWithImages: purchasesWithImages.length,
      imageData: purchasesWithImages.map(p => ({
        id: p._id,
        hasReceiptImage: p.hasReceiptImage,
        receiptImage: p.receiptImage
      }))
    });
  }, [purchases, stockItem]);

  useEffect(() => {
    // Sort purchases by date (most recent first)
    const sorted = [...purchases].sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
    setSortedPurchases(sorted);

    // Calculate metrics
    const now = new Date();
    const sevenDaysAgo = subDays(now, 7);
    const thirtyDaysAgo = subDays(now, 30);

    // Last 7 days spend (only for transactions with items for this stock item)
    const sevenDaysSpend = purchases
      .filter(p => isWithinInterval(new Date(p.date), { start: sevenDaysAgo, end: now }))
      .filter(p => stockItem ? p.items.some(item => item.stockItemId === stockItem.id) : true)
      .reduce((total, p) => {
        if (!stockItem) return total + p.totalCost;
        // Calculate cost only for relevant items
        const relevantItems = p.items.filter(item => item.stockItemId === stockItem.id);
        return total + relevantItems.reduce((sum, item) => sum + item.totalCost, 0);
      }, 0);
    setLastSevenDaysSpend(sevenDaysSpend);

    // Last 30 days spend
    const thirtyDaysSpend = purchases
      .filter(p => isWithinInterval(new Date(p.date), { start: thirtyDaysAgo, end: now }))
      .filter(p => stockItem ? p.items.some(item => item.stockItemId === stockItem.id) : true)
      .reduce((total, p) => {
        if (!stockItem) return total + p.totalCost;
        // Calculate cost only for relevant items
        const relevantItems = p.items.filter(item => item.stockItemId === stockItem.id);
        return total + relevantItems.reduce((sum, item) => sum + item.totalCost, 0);
      }, 0);
    setLastThirtyDaysSpend(thirtyDaysSpend);

    // Calculate average unit cost from transaction items
    if (purchases.length > 0 && stockItem) {
      const relevantItems = purchases.flatMap(p => 
        p.items.filter(item => item.stockItemId === stockItem.id)
      );
      
      if (relevantItems.length > 0) {
        const totalUnitCost = relevantItems.reduce((sum, item) => sum + item.costPerBaseUnit, 0);
        setAverageUnitCost(totalUnitCost / relevantItems.length);
      } else {
        setAverageUnitCost(null);
      }
    } else {
      setAverageUnitCost(null);
    }
  }, [purchases]);

  // Helper to get supplier name from ID
  const getSupplierName = (supplierId?: string) => {
    if (!supplierId) return 'N/A';
    const supplier = suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : 'Unknown';
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className={`grid grid-cols-1 gap-2 ${!isMobile ? 'md:grid-cols-3' : ''}`}>
          <Skeleton className="h-24" />
          <Skeleton className="h-24" />
          <Skeleton className="h-24" />
        </div>
        <Skeleton className="h-64" />
      </div>
    );
  }

  // Mobile card for purchase history
  const MobilePurchaseCard = ({ purchase }: { purchase: PurchaseTransaction }) => (
    <Card className="mb-2">
      <CardContent className="p-3">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-full bg-muted/60 flex items-center justify-center">
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="text-sm font-medium">
              {format(new Date(purchase.date), 'MMM d, yyyy')}
            </div>
          </div>
          <Badge variant="outline">
            {purchase.totalCost.toFixed(2)}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Package className="h-3 w-3 text-muted-foreground" />
            <span className="text-muted-foreground">Items:</span>
            <span className="font-medium">{purchase.items.length}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-muted-foreground" />
            <span className="text-muted-foreground">Amount Paid:</span>
            <span className="font-medium">
              {purchase.amountPaid.toFixed(2)}
            </span>
          </div>
          
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-muted-foreground" />
            <span className="text-muted-foreground">Supplier:</span>
            <span className="font-medium">{getSupplierName(purchase.supplierId)}</span>
          </div>
          
          {purchase.hasReceiptImage && (
            <div className="flex items-center gap-1">
              <span className="text-muted-foreground text-xs">Receipt:</span>
              <ReceiptImageViewer
                receiptImage={purchase.receiptImage}
                transactionId={purchase._id}
                size="sm"
                isMobile={true}
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={isMobile ? "space-y-2" : "space-y-4"}>
      {/* Metrics Cards */}
      <div className={`grid grid-cols-1 gap-2 ${!isMobile ? 'md:grid-cols-3 gap-4' : ''}`}>
        {/* Recent Spend */}
        <Card>
          <CardHeader className={isMobile ? "p-3" : "pb-2"}>
            <CardDescription>Last 7 Days Spend</CardDescription>
            <CardTitle className={`flex items-center ${isMobile ? 'text-xl' : 'text-2xl'}`}>
              <TrendingDown className="mr-2 h-5 w-5 text-muted-foreground" />
              {lastSevenDaysSpend.toFixed(2)}
            </CardTitle>
          </CardHeader>
        </Card>

        {/* 30 Days Spend */}
        <Card>
          <CardHeader className={isMobile ? "p-3" : "pb-2"}>
            <CardDescription>Last 30 Days Spend</CardDescription>
            <CardTitle className={`flex items-center ${isMobile ? 'text-xl' : 'text-2xl'}`}>
              <TrendingUp className="mr-2 h-5 w-5 text-muted-foreground" />
              {lastThirtyDaysSpend.toFixed(2)}
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Average Unit Cost */}
        <Card>
          <CardHeader className={isMobile ? "p-3" : "pb-2"}>
            <CardDescription>Average Cost Per {stockItem?.unit}</CardDescription>
            <CardTitle className={isMobile ? 'text-xl' : 'text-2xl'}>
              {averageUnitCost !== null ? averageUnitCost.toFixed(2) : 'N/A'}
            </CardTitle>
          </CardHeader>
        </Card>
      </div>

      {/* Purchases Table/Cards */}
      <Card>
        <CardHeader className={isMobile ? "p-3 pb-2" : "pb-2"}>
          <div className="flex items-center justify-between">
            <CardTitle className={isMobile ? "text-base" : "text-lg"}>Purchase History</CardTitle>
            <Badge variant="outline" className="text-xs">
              {purchases.length} {purchases.length === 1 ? 'record' : 'records'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className={isMobile ? "p-3 pt-0" : ""}>
          {sortedPurchases.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
              <ClipboardList className="h-12 w-12 mb-2" />
              <p>No purchase records found for this item.</p>
            </div>
          ) : isMobile ? (
            <div className="space-y-2">
              {sortedPurchases.map((purchase) => (
                <MobilePurchaseCard key={purchase._id} purchase={purchase} />
              ))}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Cost/Unit</TableHead>
                    <TableHead>Total Cost</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead className="text-center">Receipt</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedPurchases.map((purchase) => {
                    // Find items for this specific stock item
                    const relevantItems = purchase.items.filter(item => 
                      stockItem ? item.stockItemId === stockItem.id : true
                    );
                    
                    if (stockItem && relevantItems.length === 0) return null;
                    
                    // For each relevant item, show a row
                    return relevantItems.map((item, index) => (
                      <TableRow key={`${purchase._id}-${index}`}>
                        <TableCell>{format(new Date(purchase.date), 'MMM d, yyyy')}</TableCell>
                        <TableCell>
                          {item.quantity} {item.unit}
                        </TableCell>
                        <TableCell>
                          {item.costPerUnit.toFixed(2)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {item.totalCost.toFixed(2)}
                        </TableCell>
                        <TableCell>
                          {getSupplierName(purchase.supplierId)}
                        </TableCell>
                        <TableCell className="text-center">
                          {purchase.hasReceiptImage && (
                            <ReceiptImageViewer
                              receiptImage={purchase.receiptImage}
                              transactionId={purchase._id}
                              size="sm"
                              isMobile={false}
                            />
                          )}
                        </TableCell>
                      </TableRow>
                    ));
                  }).flat()}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 