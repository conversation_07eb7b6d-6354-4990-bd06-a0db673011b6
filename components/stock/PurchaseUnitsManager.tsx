"use client";

import React, { useState } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { PurchaseUnit } from '@/types/stock';
import { Plus, Trash2, Package, Calculator } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";

const purchaseUnitSchema = z.object({
  name: z.string().min(1, { message: "Le nom est requis." }),
  conversionToBase: z.coerce.number().min(0.001, { message: "La conversion doit être supérieure à 0." }),
  isDefault: z.boolean().optional(),
});

interface PurchaseUnitsManagerProps {
  baseUnit: string;
  purchaseUnits: PurchaseUnit[];
  onAddPurchaseUnit: (unit: Omit<PurchaseUnit, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onRemovePurchaseUnit: (unitId: string) => void;
  onSetDefault: (unitId: string) => void;
  compact?: boolean;
}

export function PurchaseUnitsManager({ 
  baseUnit, 
  purchaseUnits, 
  onAddPurchaseUnit, 
  onRemovePurchaseUnit, 
  onSetDefault,
  compact = false 
}: PurchaseUnitsManagerProps) {
  const { toast } = useToast();
  const [isAddingUnit, setIsAddingUnit] = useState(false);

  const form = useForm({
    resolver: zodResolver(purchaseUnitSchema),
    defaultValues: {
      name: "",
      conversionToBase: 1,
      isDefault: false,
    },
  });

  const handleSubmit = (values: any) => {
    // Check if this is the first unit and should be default
    const shouldBeDefault = purchaseUnits.length === 0 || values.isDefault;
    
    onAddPurchaseUnit({
      name: values.name,
      conversionToBase: values.conversionToBase,
      isDefault: shouldBeDefault,
    });

    form.reset();
    setIsAddingUnit(false);
    
    toast({
      title: "📦 Unité ajoutée",
      description: `${values.name} (1 = ${values.conversionToBase} ${baseUnit}) ajoutée avec succès.`,
      duration: 2000,
    });
  };

  const handleSetDefault = (unitId: string) => {
    onSetDefault(unitId);
    toast({
      title: "✅ Unité par défaut",
      description: "Unité par défaut mise à jour.",
      duration: 2000,
    });
  };

  const handleRemove = (unitId: string, unitName: string) => {
    onRemovePurchaseUnit(unitId);
    toast({
      title: "🗑️ Unité supprimée",
      description: `${unitName} supprimée avec succès.`,
      duration: 2000,
    });
  };

  return (
    <Card className={compact ? "border-0 shadow-none" : ""}>
      <CardHeader className={compact ? "px-0 py-2" : ""}>
        <CardTitle className={`flex items-center gap-2 ${compact ? 'text-sm' : 'text-base'}`}>
          <Package className="h-4 w-4" />
          Unités d'achat
        </CardTitle>
        <FormDescription className={compact ? "text-xs" : "text-sm"}>
          Définissez les différentes unités d'achat et leurs conversions vers l'unité de base ({baseUnit}).
        </FormDescription>
      </CardHeader>
      
      <CardContent className={compact ? "px-0 py-2" : ""}>
        {/* Existing Purchase Units */}
        {purchaseUnits.length > 0 && (
          <div className="mb-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className={compact ? "text-xs" : "text-sm"}>Unité</TableHead>
                  <TableHead className={compact ? "text-xs" : "text-sm"}>Conversion</TableHead>
                  <TableHead className={compact ? "text-xs" : "text-sm"}>Statut</TableHead>
                  <TableHead className={compact ? "text-xs" : "text-sm"}>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {purchaseUnits.map((unit) => (
                  <TableRow key={unit.id}>
                    <TableCell className={compact ? "text-xs" : "text-sm"}>
                      {unit.name}
                    </TableCell>
                    <TableCell className={compact ? "text-xs" : "text-sm"}>
                      <div className="flex items-center gap-1">
                        <Calculator className="h-3 w-3 text-muted-foreground" />
                        1 = {unit.conversionToBase} {baseUnit}
                      </div>
                    </TableCell>
                    <TableCell>
                      {unit.isDefault && (
                        <Badge variant="secondary" className={compact ? "text-xs" : ""}>
                          Par défaut
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {!unit.isDefault && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSetDefault(unit.id)}
                            className={compact ? "h-6 px-2 text-xs" : ""}
                          >
                            Défaut
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemove(unit.id, unit.name)}
                          className={compact ? "h-6 px-2 text-xs" : ""}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Add New Unit Form */}
        {isAddingUnit ? (
          <div className={`space-y-${compact ? '2' : '3'} border rounded-lg p-3`}>
            <div className={`grid grid-cols-1 ${compact ? 'gap-2' : 'gap-3'} md:grid-cols-2`}>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={compact ? "text-xs" : "text-sm"}>Nom de l'unité</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="ex: Plateau, Carton, Boîte" 
                        className={compact ? "h-8 text-xs" : ""}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="conversionToBase"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className={compact ? "text-xs" : "text-sm"}>
                      Conversion (1 unité = ? {baseUnit})
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.001" 
                        min="0.001"
                        placeholder="ex: 30 pour 1 plateau = 30 pièces"
                        className={compact ? "h-8 text-xs" : ""}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="isDefault"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border border-input"
                    />
                  </FormControl>
                  <FormLabel className={`${compact ? 'text-xs' : 'text-sm'} font-normal`}>
                    Définir comme unité par défaut
                  </FormLabel>
                </FormItem>
              )}
            />

            <div className="flex gap-2">
              <Button 
                type="button"
                size={compact ? "sm" : "default"}
                className={compact ? "text-xs" : ""}
                onClick={form.handleSubmit(handleSubmit)}
              >
                Ajouter
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                size={compact ? "sm" : "default"}
                className={compact ? "text-xs" : ""}
                onClick={() => {
                  setIsAddingUnit(false);
                  form.reset();
                }}
              >
                Annuler
              </Button>
            </div>
          </div>
        ) : (
          <Button 
            onClick={() => setIsAddingUnit(true)}
            variant="outline"
            size={compact ? "sm" : "default"}
            className={`w-full ${compact ? 'text-xs' : ''}`}
          >
            <Plus className="h-4 w-4 mr-2" />
            Ajouter une unité d'achat
          </Button>
        )}
      </CardContent>
    </Card>
  );
} 