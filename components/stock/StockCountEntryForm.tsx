"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { StockItem } from '@/types/stock';
import { StockCount, StockCountItem } from '@/types/stockCount';
import { Search, Save, CheckCircle2, Eye, EyeOff } from 'lucide-react';
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { format } from 'date-fns';

interface CountedItem {
  quantity: number;
  notes: string;
  purchaseUnitQuantities: Record<string, number>;
}

interface StockCountEntryFormProps {
  stockCount: StockCount;
  stockItems: StockItem[];
  countItems: StockCountItem[];
  onSaveItems: (items: Array<{ stockItemId: string; countedQuantity: number; notes?: string }>) => Promise<void>;
  onComplete: () => Promise<void>;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function StockCountEntryForm({
  stockCount,
  stockItems,
  countItems,
  onSaveItems,
  onComplete
}: StockCountEntryFormProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSystemQty, setShowSystemQty] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [countedItems, setCountedItems] = useState<Record<string, CountedItem>>({});

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const stockItemsMap = useMemo(() => {
    const map = new Map<string, StockItem>();
    stockItems.forEach(item => map.set(item.id, item));
    return map;
  }, [stockItems]);

  const purchaseUnitsCache = useMemo(() => {
    const cache = new Map<string, Array<{ id: string; name: string; conversionToBase: number; isDefault?: boolean }>>();
    stockItems.forEach(item => {
      cache.set(item.id, [
        { id: 'base', name: item.unit, conversionToBase: 1, isDefault: true },
        ...(item.purchaseUnits || [])
      ]);
    });
    return cache;
  }, [stockItems]);

  useEffect(() => {
    const initialCountedItems: Record<string, CountedItem> = {};
    countItems.forEach(item => {
      const stockItem = stockItemsMap.get(item.stockItemId);
      if (stockItem) {
        initialCountedItems[stockItem.id] = {
          quantity: item.countedQuantity || 0,
          notes: item.notes || '',
          purchaseUnitQuantities: {}
        };
      }
    });
    setCountedItems(initialCountedItems);
  }, [countItems, stockItemsMap]);

  const filteredItems = useMemo(() => {
    return stockItems.filter(item => {
      const matchesSearch = !debouncedSearchQuery || 
        item.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(debouncedSearchQuery.toLowerCase());
      
      return matchesSearch;
    });
  }, [stockItems, debouncedSearchQuery]);

  const progress = useMemo(() => {
    const totalItems = filteredItems.length;
    const countedItemsCount = filteredItems.filter(item => (countedItems[item.id]?.quantity || 0) > 0).length;
    return totalItems > 0 ? (countedItemsCount / totalItems) * 100 : 0;
  }, [filteredItems, countedItems]);

  // Function to save all counted items when user confirms
  const handleConfirmCount = useCallback(async () => {
    const itemsToSave: Array<{ stockItemId: string; countedQuantity: number; notes?: string }> = [];
    
    // Collect all items that have been counted
    Object.entries(countedItems).forEach(([stockItemId, item]) => {
      if (item.quantity > 0) {
        itemsToSave.push({
          stockItemId,
          countedQuantity: item.quantity,
          notes: item.notes || undefined
        });
      }
    });

    try {
      // Save all items
      await onSaveItems(itemsToSave);
      // Complete the count only if it's not already completed
      if (stockCount.status !== 'completed') {
        await onComplete();
      }
    } catch (error) {
      console.error('Error confirming count:', error);
    }
  }, [countedItems, onSaveItems, onComplete]);

  const handlePurchaseUnitQuantityChange = useCallback((stockItemId: string, purchaseUnitId: string, value: string) => {
    const quantity = value === '' ? 0 : parseFloat(value) || 0;
    
    setCountedItems(prev => {
      const currentItem = prev[stockItemId] || { quantity: 0, notes: '', purchaseUnitQuantities: {} };
      const updatedUnitQuantities = {
        ...currentItem.purchaseUnitQuantities,
        [purchaseUnitId]: quantity
      };
      
      const purchaseUnits = purchaseUnitsCache.get(stockItemId) || [];
      const totalBaseQuantity = purchaseUnits.reduce((total, unit) => {
        const unitQuantity = updatedUnitQuantities[unit.id] || 0;
        return total + (unitQuantity * unit.conversionToBase);
      }, 0);
      
      const updatedItem = {
        ...currentItem,
        quantity: totalBaseQuantity,
        purchaseUnitQuantities: updatedUnitQuantities
      };
      
      return {
        ...prev,
        [stockItemId]: updatedItem
      };
    });
  }, [purchaseUnitsCache]);

  const handleNotesChange = useCallback((stockItemId: string, notes: string) => {
    setCountedItems(prev => {
      const currentItem = prev[stockItemId] || { quantity: 0, notes: '', purchaseUnitQuantities: {} };
      const updatedItem = { ...currentItem, notes };
      
      return {
        ...prev,
        [stockItemId]: updatedItem
      };
    });
  }, []);

  const lastCountMap = useMemo(() => {
    const map = new Map<string, string>();
    countItems.forEach(item => {
      map.set(item.stockItemId, item.updatedAt);
    });
    return map;
  }, [countItems]);

  return (
    <div className="space-y-4">
      <Card className="border-none shadow-none">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold">{stockCount.name}</CardTitle>
              <CardDescription className="text-sm">
                {format(new Date(stockCount.date), 'PPP')} • {Math.round(progress)}% terminé
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSystemQty(!showSystemQty)}
                className="h-8"
              >
                {showSystemQty ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showSystemQty ? 'Masquer' : 'Voir'} système
              </Button>
              <Badge variant={stockCount.status === 'completed' ? 'success' : 'secondary'}>
                {stockCount.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </div>
          
          <div className="flex flex-wrap items-center justify-between gap-2 pt-2 border-t">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher des articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 h-8 text-sm"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={handleConfirmCount}
                className="h-8 bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircle2 className="mr-1 h-4 w-4" />
                {stockCount.status === 'completed' ? 'Modifier comptage' : 'Confirmer comptage'}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          <div className="h-[500px] overflow-auto border rounded-lg">
            <Table className="text-sm">
              <TableHeader className="sticky top-0 bg-background z-10">
                <TableRow>
                  <TableHead className="w-10 p-2"></TableHead>
                  <TableHead className="p-2 min-w-[200px]">Article</TableHead>
                  <TableHead className="p-2 w-32">Dernier comptage</TableHead>
                  <TableHead className="p-2 w-48">Unités d'achat</TableHead>
                  {showSystemQty && (
                    <TableHead className="p-2 w-24 text-muted-foreground">Qté système</TableHead>
                  )}
                  <TableHead className="p-2 w-32">Total (base)</TableHead>
                  <TableHead className="p-2 min-w-[150px]">Notes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={showSystemQty ? 7 : 6} className="h-32 text-center text-muted-foreground">
                      Aucun article trouvé.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredItems.map((item) => {
                    const purchaseUnits = purchaseUnitsCache.get(item.id) || [];
                    const unitQuantities = countedItems[item.id]?.purchaseUnitQuantities || {};
                    const totalBaseQuantity = countedItems[item.id]?.quantity || 0;

                    return (
                      <TableRow 
                        key={item.id} 
                        className="transition-colors hover:bg-muted/50"
                      >
                        <TableCell className="p-2">
                          <Checkbox
                            checked={totalBaseQuantity > 0}
                            disabled
                          />
                        </TableCell>
                        <TableCell className="p-2 font-medium">{item.name}</TableCell>
                        <TableCell className="p-2 text-muted-foreground">
                          {lastCountMap.get(item.id)
                            ? format(new Date(lastCountMap.get(item.id)!), 'PPP')
                            : '—'}
                        </TableCell>
                        <TableCell className="p-2">
                          <div className="space-y-1">
                            {purchaseUnits.map(unit => (
                              <div key={unit.id} className="flex items-center gap-2">
                                <span className="text-xs w-12 text-right">{unit.name}:</span>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  value={unitQuantities[unit.id] || ''}
                                  onChange={(e) => handlePurchaseUnitQuantityChange(item.id, unit.id, e.target.value)}
                                  className="w-16 h-6 text-xs"
                                  placeholder="0"
                                />
                                {unit.conversionToBase !== 1 && (
                                  <span className="text-[10px] text-muted-foreground">
                                    (1={unit.conversionToBase}{item.unit})
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </TableCell>
                        {showSystemQty && (
                          <TableCell className="p-2 text-xs text-muted-foreground">
                            {typeof item.quantity === 'number' ? item.quantity.toFixed(2) : 'N/A'} {item.unit}
                          </TableCell>
                        )}
                        <TableCell className="p-2">
                          {totalBaseQuantity > 0 ? (
                            <div className="space-y-1">
                              <div className="text-sm font-medium">
                                {totalBaseQuantity.toFixed(2)} {item.unit}
                              </div>
                              {(() => {
                                const purchaseUnits = purchaseUnitsCache.get(item.id) || [];
                                const filledUnits = purchaseUnits.filter(unit => 
                                  (unitQuantities[unit.id] || 0) > 0
                                ).length;
                                const totalUnits = purchaseUnits.length;
                                
                                if (filledUnits > 0 && filledUnits < totalUnits) {
                                  return (
                                    <div className="text-xs text-amber-600 flex items-center gap-1">
                                      ⚠️ Partiel ({filledUnits}/{totalUnits} unités)
                                    </div>
                                  );
                                }
                                return null;
                              })()}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground">
                              — Non compté
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="p-2">
                          <Input
                            value={countedItems[item.id]?.notes || ''}
                            onChange={(e) => handleNotesChange(item.id, e.target.value)}
                            className="w-full h-8 text-sm"
                            placeholder="Notes..."
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
