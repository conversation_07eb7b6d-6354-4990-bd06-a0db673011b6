'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Clock, 
  Scan, 
  AlertCircle, 
  Package,
  Printer,
  RefreshCw
} from 'lucide-react';
import { 
  markItemComplete, 
  parseBarcodeData, 
  getOrdersReadyForExpo,
  markExpoNotified,
  getPendingOrders
} from '@/lib/db/v4/operations/order-completion-ops';
import { getOrder } from '@/lib/db/v4/operations/order-ops';
import { kitchenPrintService } from '@/lib/services/kitchen-print-service';
import { toast } from 'sonner';

interface PendingOrder {
  orderId: string;
  orderNumber: string;
  stationItems: {
    [stationId: string]: {
      [itemIndex: string]: {
        completed: boolean;
        completedAt?: string;
        scannedBy?: string;
        barcode: string;
      }
    }
  };
  allItemsComplete: boolean;
  createdAt: string;
}

interface ReadyOrder {
  orderId: string;
  orderNumber: string;
  completedStations: string[];
  completedAt: string;
}

export default function BarcodeScannerInterface() {
  const [barcodeInput, setBarcodeInput] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [pendingOrders, setPendingOrders] = useState<PendingOrder[]>([]);
  const [readyOrders, setReadyOrders] = useState<ReadyOrder[]>([]);
  const [lastScannedItem, setLastScannedItem] = useState<string | null>(null);
  const [scanHistory, setScanHistory] = useState<Array<{
    barcode: string;
    timestamp: string;
    success: boolean;
    message: string;
  }>>([]);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const refreshInterval = useRef<NodeJS.Timeout | null>(null);

  // Auto-focus input and setup refresh interval
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
    
    // Refresh data every 30 seconds
    refreshInterval.current = setInterval(() => {
      loadOrderData();
    }, 30000);
    
    // Initial load
    loadOrderData();
    
    return () => {
      if (refreshInterval.current) {
        clearInterval(refreshInterval.current);
      }
    };
  }, []);

  // Load pending and ready orders
  const loadOrderData = async () => {
    try {
      const [pending, ready] = await Promise.all([
        getPendingOrders(),
        getOrdersReadyForExpo()
      ]);
      
      // Transform pending orders
      const transformedPending: PendingOrder[] = pending.map(order => ({
        orderId: order.orderId,
        orderNumber: order.orderId.substring(order.orderId.length - 6),
        stationItems: order.stationItems,
        allItemsComplete: order.allItemsComplete,
        createdAt: order.createdAt
      }));
      
      // Transform ready orders
      const transformedReady: ReadyOrder[] = ready.map(order => {
        const completedStations = Object.keys(order.stationItems);
        return {
          orderId: order.orderId,
          orderNumber: order.orderId.substring(order.orderId.length - 6),
          completedStations,
          completedAt: order.updatedAt
        };
      });
      
      setPendingOrders(transformedPending);
      setReadyOrders(transformedReady);
      
    } catch (error) {
      console.error('Error loading order data:', error);
      toast.error('Failed to load order data');
    }
  };

  // Handle barcode scan
  const handleBarcodeScan = async (barcode: string) => {
    if (!barcode.trim()) return;
    
    setIsScanning(true);
    
    try {
      // Parse barcode data
      const barcodeData = parseBarcodeData(barcode);
      
      // Mark item as complete
      const result = await markItemComplete(
        barcodeData.orderId,
        barcodeData.stationId,
        barcodeData.itemIndex,
        'scanner-user' // Could be replaced with actual user ID
      );
      
      setLastScannedItem(`${barcodeData.orderId}-${barcodeData.stationId}-${barcodeData.itemIndex}`);
      
      // Add to scan history
      setScanHistory(prev => [{
        barcode,
        timestamp: new Date().toISOString(),
        success: true,
        message: `Item completed for order #${barcodeData.orderId.substring(barcodeData.orderId.length - 6)}`
      }, ...prev.slice(0, 9)]); // Keep last 10 scans
      
      // Check if order is now complete and ready for expo
      if (result.allItemsComplete && !result.expoNotified) {
        await handleOrderReadyForExpo(result.orderId);
      }
      
      // Refresh data
      await loadOrderData();
      
      toast.success(`Item completed for order #${barcodeData.orderId.substring(barcodeData.orderId.length - 6)}`);
      
    } catch (error: any) {
      console.error('Error processing barcode:', error);
      
      // Add to scan history
      setScanHistory(prev => [{
        barcode,
        timestamp: new Date().toISOString(),
        success: false,
        message: error.message || 'Invalid barcode'
      }, ...prev.slice(0, 9)]);
      
      toast.error(error.message || 'Failed to process barcode');
    } finally {
      setIsScanning(false);
      setBarcodeInput('');
      
      // Refocus input
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  };

  // Handle order ready for expo
  const handleOrderReadyForExpo = async (orderId: string) => {
    try {
      // Get order details
      const order = await getOrder(orderId);
      if (!order) {
        throw new Error('Order not found');
      }
      
      // Get completed stations
      const pendingOrder = pendingOrders.find(p => p.orderId === orderId);
      const completedStations = pendingOrder ? Object.keys(pendingOrder.stationItems) : [];
      
      // Generate expo ticket
      const expoTicket = kitchenPrintService.generateExpoTicket(order._id);
      
      // Print expo ticket (show preview)
      if (expoTicket) {
        const printWindow = window.open('', '_blank', 'width=600,height=800');
        if (printWindow) {
          printWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>${expoTicket.title}</title>
                <style>
                  body { font-family: 'Courier New', monospace; margin: 0; padding: 0; background: #fff; }
                  .preview-area { width: 80mm; margin: 0 auto; padding: 10px; }
                  @media print { 
                    body { width: 80mm; margin: 0; } 
                    @page { size: 80mm auto; margin: 0; } 
                  }
                </style>
              </head>
              <body>
                <div class="preview-area">${expoTicket.content}</div>
                <script>
                  window.onload = function() { 
                    setTimeout(function() { 
                      window.print(); 
                      setTimeout(function() { window.close(); }, 500);
                    }, 300); 
                  };
                </script>
              </body>
            </html>
          `);
          printWindow.document.close();
        }
      }
      
      // Mark as expo notified
      await markExpoNotified(orderId);
      
      toast.success(`Expo ticket printed for order #${orderId.substring(orderId.length - 6)}`);
      
    } catch (error) {
      console.error('Error handling expo ticket:', error);
      toast.error('Failed to print expo ticket');
    }
  };

  // Handle form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleBarcodeScan(barcodeInput);
  };

  // Manual expo print
  const handleManualExpoPrint = async (orderId: string) => {
    await handleOrderReadyForExpo(orderId);
    await loadOrderData();
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Barcode Scanner</h1>
          <p className="text-muted-foreground">Scan item barcodes to mark them as complete</p>
        </div>
        <Button onClick={loadOrderData} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Scanner Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scan className="h-5 w-5" />
            Barcode Scanner
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex gap-2">
              <Input
                ref={inputRef}
                value={barcodeInput}
                onChange={(e) => setBarcodeInput(e.target.value)}
                placeholder="Scan or enter barcode..."
                className="flex-1"
                disabled={isScanning}
              />
              <Button type="submit" disabled={isScanning || !barcodeInput.trim()}>
                {isScanning ? 'Processing...' : 'Process'}
              </Button>
            </div>
            
            {lastScannedItem && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Last scanned: {lastScannedItem}
                </AlertDescription>
              </Alert>
            )}
          </form>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Orders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Pending Orders ({pendingOrders.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingOrders.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No pending orders
                </p>
              ) : (
                pendingOrders.map((order) => (
                  <div key={order.orderId} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">Order #{order.orderNumber}</h3>
                      <Badge variant="secondary">
                        {Object.keys(order.stationItems).length} stations
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      {Object.entries(order.stationItems).map(([stationId, items]) => {
                        const completedItems = Object.values(items).filter(item => item.completed).length;
                        const totalItems = Object.keys(items).length;
                        const isStationComplete = completedItems === totalItems;
                        
                        return (
                          <div key={stationId} className="flex items-center justify-between text-sm">
                            <span className="capitalize">{stationId}</span>
                            <div className="flex items-center gap-2">
                              <span>{completedItems}/{totalItems}</span>
                              {isStationComplete ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <Clock className="h-4 w-4 text-yellow-500" />
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Ready for Expo */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Ready for Expo ({readyOrders.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {readyOrders.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No orders ready for expo
                </p>
              ) : (
                readyOrders.map((order) => (
                  <div key={order.orderId} className="border rounded-lg p-4 bg-green-50">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">Order #{order.orderNumber}</h3>
                      <Badge variant="default" className="bg-green-500">
                        Ready
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">
                        Completed stations: {order.completedStations.join(', ')}
                      </div>
                      
                      <Button 
                        onClick={() => handleManualExpoPrint(order.orderId)}
                        size="sm"
                        className="w-full"
                      >
                        <Printer className="h-4 w-4 mr-2" />
                        Print Expo Ticket
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scan History */}
      {scanHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Scans</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {scanHistory.map((scan, index) => (
                <div key={index} className="flex items-center justify-between text-sm border-b pb-2">
                  <div className="flex items-center gap-2">
                    {scan.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="font-mono">{scan.barcode}</span>
                  </div>
                  <div className="text-right">
                    <div className={scan.success ? 'text-green-600' : 'text-red-600'}>
                      {scan.message}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {new Date(scan.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}