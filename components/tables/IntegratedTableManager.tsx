"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Package, 
  Users, 
  Check, 
  X, 
  ChevronDown,
  ChevronRight,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { updateTablePackaging, getTablePackaging } from '@/lib/db/v4';
import { useToast } from '@/components/ui/use-toast';
import type { Table } from '@/lib/db/v4';
import type { PackagingItem } from '@/lib/db/v4/schemas/table-schema';
import {
  Command,
  CommandInput,
  CommandList,
  CommandItem,
  CommandEmpty,
  CommandGroup
} from '@/components/ui/command';
import { SearchableStockItemSelect } from '@/components/stock/SearchableStockItemSelect';

interface IntegratedTableManagerProps {
  className?: string;
}

export function IntegratedTableManager({ className }: IntegratedTableManagerProps) {
  const { tables, isLoading, addTable, updateTable, deleteTable, refreshTables } = useTableDB();
  const { stockItems } = useStockV4();
  const { toast } = useToast();

  // State management
  const [expandedTableId, setExpandedTableId] = useState<string | null>(null);
  const [editingTableId, setEditingTableId] = useState<string | null>(null);
  const [isAddingTable, setIsAddingTable] = useState(false);
  const [packagingConfigs, setPackagingConfigs] = useState<Record<string, PackagingItem[]>>({});

  // Form states
  const [newTable, setNewTable] = useState({ name: '', seats: 4 });
  const [editedTable, setEditedTable] = useState<Partial<Table>>({});
  const [newPackagingItem, setNewPackagingItem] = useState({ stockItemId: '', quantity: 1 });

  // Add state for new table packaging
  const [newTablePackaging, setNewTablePackaging] = useState<PackagingItem[]>([]);
  const [newTableSelectedStockItemId, setNewTableSelectedStockItemId] = useState('');
  const [newTablePackagingItem, setNewTablePackagingItem] = useState({ stockItemId: '', quantity: 1 });

  // Show all stock items for packaging selection (no filter)
  const packagingStockItems = stockItems;

  // Remove Command search state
  // const [search, setSearch] = useState('');
  // const [selectedSearchItem, setSelectedSearchItem] = useState<null | { id: string; name: string }>(null);
  const [selectedStockItemId, setSelectedStockItemId] = useState('');

  // Load packaging configurations for all tables
  useEffect(() => {
    const loadAllPackagingConfigs = async () => {
      const configs: Record<string, PackagingItem[]> = {};
      for (const table of tables) {
        try {
          configs[table.id] = await getTablePackaging(table.id);
        } catch (error) {
          console.error(`Error loading packaging for table ${table.id}:`, error);
          configs[table.id] = [];
        }
      }
      setPackagingConfigs(configs);
    };

    if (tables.length > 0) {
      loadAllPackagingConfigs();
    }
  }, [tables]);

  // Helper functions
  const getStockItemName = (stockItemId: string) => {
    const item = stockItems.find(item => item.id === stockItemId);
    return item ? `${item.name}` : 'Unknown';
  };

  const getPackagingCount = (tableId: string) => {
    return packagingConfigs[tableId]?.length || 0;
  };

  // Table operations
  const handleAddTable = async () => {
    try {
      const tableData = {
        name: newTable.name || `Table ${tables.length + 1}`,
        seats: newTable.seats,
        position: { x: 0, y: 0 },
        status: 'free' as const,
        packaging: newTablePackaging
      };
      await addTable(tableData);
      setNewTable({ name: '', seats: 4 });
      setNewTablePackaging([]);
      setNewTableSelectedStockItemId('');
      setNewTablePackagingItem({ stockItemId: '', quantity: 1 });
      setIsAddingTable(false);
      toast({ title: "✅ Table created successfully" });
    } catch (error) {
      toast({ title: "❌ Failed to create table", variant: "destructive" });
    }
  };

  const handleEditTable = (table: Table) => {
    setEditingTableId(table.id);
    setEditedTable({ ...table });
  };

  const handleSaveEdit = async () => {
    if (!editingTableId || !editedTable) return;
    
    try {
      await updateTable(editingTableId, editedTable);
      setEditingTableId(null);
      setEditedTable({});
      toast({ title: "✅ Table updated successfully" });
    } catch (error) {
      toast({ title: "❌ Failed to update table", variant: "destructive" });
    }
  };

  const handleDeleteTable = async (tableId: string) => {
    try {
      await deleteTable(tableId);
      toast({ title: "✅ Table deleted successfully" });
    } catch (error) {
      toast({ title: "❌ Failed to delete table", variant: "destructive" });
    }
  };

  // Packaging operations
  const handleAddPackagingItem = async (tableId: string) => {
    if (!newPackagingItem.stockItemId || newPackagingItem.quantity <= 0) return;

    const currentPackaging = packagingConfigs[tableId] || [];
    const existingIndex = currentPackaging.findIndex(p => p.stockItemId === newPackagingItem.stockItemId);
    
    let updatedPackaging;
    if (existingIndex >= 0) {
      updatedPackaging = [...currentPackaging];
      updatedPackaging[existingIndex].quantity += newPackagingItem.quantity;
    } else {
      updatedPackaging = [...currentPackaging, { ...newPackagingItem }];
    }

    try {
      await updateTablePackaging(tableId, updatedPackaging);
      setPackagingConfigs(prev => ({ ...prev, [tableId]: updatedPackaging }));
      setNewPackagingItem({ stockItemId: '', quantity: 1 });
      toast({ title: "✅ Packaging item added" });
    } catch (error) {
      toast({ title: "❌ Failed to add packaging item", variant: "destructive" });
    }
  };

  const handleRemovePackagingItem = async (tableId: string, index: number) => {
    const currentPackaging = packagingConfigs[tableId] || [];
    const updatedPackaging = currentPackaging.filter((_, i) => i !== index);

    try {
      await updateTablePackaging(tableId, updatedPackaging);
      setPackagingConfigs(prev => ({ ...prev, [tableId]: updatedPackaging }));
      toast({ title: "✅ Packaging item removed" });
    } catch (error) {
      toast({ title: "❌ Failed to remove packaging item", variant: "destructive" });
    }
  };

  const handleUpdatePackagingQuantity = async (tableId: string, index: number, quantity: number) => {
    if (quantity <= 0) {
      handleRemovePackagingItem(tableId, index);
      return;
    }

    const currentPackaging = packagingConfigs[tableId] || [];
    const updatedPackaging = [...currentPackaging];
    updatedPackaging[index].quantity = quantity;

    try {
      await updateTablePackaging(tableId, updatedPackaging);
      setPackagingConfigs(prev => ({ ...prev, [tableId]: updatedPackaging }));
    } catch (error) {
      toast({ title: "❌ Failed to update quantity", variant: "destructive" });
    }
  };

  if (isLoading) {
    return (
      <Card className={cn("border-0 shadow-none", className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-muted-foreground border-t-transparent" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("border-0 shadow-none bg-background", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium text-foreground">Tables</CardTitle>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setIsAddingTable(true)}
            className="h-7 px-2 text-xs border-muted-foreground/20 hover:border-muted-foreground/40"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Table
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-2 pt-0">
        {/* Add New Table Form */}
        {isAddingTable && (
          <Card className="border-dashed border-muted-foreground/30 bg-muted/20">
            <CardContent className="p-2 space-y-2">
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground">Name</Label>
                  <Input
                    value={newTable.name}
                    onChange={(e) => setNewTable({ ...newTable, name: e.target.value })}
                    placeholder="Table name"
                    className="h-7 text-xs border-muted-foreground/20"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground">Seats</Label>
                  <Input
                    type="number"
                    value={newTable.seats}
                    onChange={(e) => setNewTable({ ...newTable, seats: parseInt(e.target.value) || 4 })}
                    className="h-7 text-xs border-muted-foreground/20"
                  />
                </div>
              </div>
              {/* Packaging UI for new table */}
              <div className="space-y-1 pt-1 border-t border-muted-foreground/10">
                <Label className="text-xs font-medium text-muted-foreground">Packaging</Label>
                {/* List of added packaging items */}
                {newTablePackaging.length > 0 && (
                  <div className="space-y-1">
                    {newTablePackaging.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 rounded border border-muted-foreground/10 bg-muted/30">
                        <div className="flex items-center gap-1">
                          <Package className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs font-medium text-foreground">
                            {getStockItemName(item.stockItemId)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Input
                            type="number"
                            min="0.1"
                            step="0.1"
                            inputMode="decimal"
                            className="w-14 h-7 text-xs text-center border-muted-foreground/20 appearance-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            value={item.quantity}
                            onChange={(e) => {
                              const updated = [...newTablePackaging];
                              updated[index].quantity = parseFloat(e.target.value) || 1;
                              setNewTablePackaging(updated);
                            }}
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setNewTablePackaging(newTablePackaging.filter((_, i) => i !== index))}
                            className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                {/* Add packaging item row */}
                <div className="flex items-center gap-2 mt-1">
                  <div className="max-w-[160px] flex-1">
                    <SearchableStockItemSelect
                      stockItems={packagingStockItems}
                      value={newTableSelectedStockItemId}
                      onChange={id => {
                        setNewTableSelectedStockItemId(id);
                        setNewTablePackagingItem({ stockItemId: id, quantity: 1 });
                      }}
                      placeholder="Search stock item..."
                      compact={true}
                    />
                  </div>
                  <Input
                    type="number"
                    min="0.1"
                    step="0.1"
                    inputMode="decimal"
                    className="w-14 h-7 text-xs text-center border-muted-foreground/20 appearance-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                    placeholder="Qty"
                    value={newTableSelectedStockItemId === newTablePackagingItem.stockItemId ? newTablePackagingItem.quantity : 1}
                    onChange={(e) => {
                      setNewTablePackagingItem({ stockItemId: newTableSelectedStockItemId, quantity: parseFloat(e.target.value) || 1 });
                    }}
                    disabled={!newTableSelectedStockItemId}
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-7 w-7 p-0 flex items-center justify-center"
                    disabled={!newTableSelectedStockItemId || newTablePackagingItem.quantity <= 0}
                    onClick={() => {
                      if (newTableSelectedStockItemId && newTablePackagingItem.quantity > 0) {
                        // If item already exists, update quantity
                        const idx = newTablePackaging.findIndex(p => p.stockItemId === newTableSelectedStockItemId);
                        let updated;
                        if (idx >= 0) {
                          updated = [...newTablePackaging];
                          updated[idx].quantity += newTablePackagingItem.quantity;
                        } else {
                          updated = [...newTablePackaging, { ...newTablePackagingItem }];
                        }
                        setNewTablePackaging(updated);
                        setNewTableSelectedStockItemId('');
                        setNewTablePackagingItem({ stockItemId: '', quantity: 1 });
                      }
                    }}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
                {/* Empty state */}
                {newTablePackaging.length === 0 && (
                  <div className="text-center py-2 text-muted-foreground">
                    <Package className="h-6 w-6 mx-auto mb-1 opacity-50" />
                    <p className="text-xs">No packaging configured</p>
                    <p className="text-xs">Add items that will be consumed automatically for dine-in orders</p>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-1">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setIsAddingTable(false)}
                  className="h-7 px-2 text-xs"
                >
                  <X className="h-3 w-3 mr-1" />
                  Cancel
                </Button>
                <Button 
                  size="sm" 
                  onClick={handleAddTable}
                  className="h-7 px-2 text-xs bg-foreground text-background hover:bg-foreground/90"
                >
                  <Check className="h-3 w-3 mr-1" />
                  Create
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Tables Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
          {tables.map((table) => {
            const isEditing = editingTableId === table.id;
            const isExpanded = expandedTableId === table.id;
            const packagingCount = getPackagingCount(table.id);
            return (
              <Card key={table.id} className="border-muted-foreground/10 bg-card">
                <CardContent className="p-0">
                  {/* Table Header */}
                  <div className="p-2 pb-1">
                    {isEditing ? (
                      <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label className="text-xs font-medium text-muted-foreground">Name</Label>
                            <Input
                              value={editedTable.name || ''}
                              onChange={(e) => setEditedTable({ ...editedTable, name: e.target.value })}
                              className="h-7 text-xs border-muted-foreground/20"
                            />
                          </div>
                          <div className="space-y-1">
                            <Label className="text-xs font-medium text-muted-foreground">Seats</Label>
                            <Input
                              type="number"
                              value={editedTable.seats || ''}
                              onChange={(e) => setEditedTable({ ...editedTable, seats: parseInt(e.target.value) || 4 })}
                              className="h-7 text-xs border-muted-foreground/20"
                            />
                          </div>
                        </div>
                        <div className="flex justify-end gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => setEditingTableId(null)}
                            className="h-7 px-2 text-xs"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Cancel
                          </Button>
                          <Button 
                            size="sm" 
                            onClick={handleSaveEdit}
                            className="h-7 px-2 text-xs bg-foreground text-background hover:bg-foreground/90"
                          >
                            <Check className="h-3 w-3 mr-1" />
                            Save
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-xs text-foreground">{table.name}</div>
                          <div className="flex items-center gap-2 mt-0.5">
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Users className="h-3 w-3" />
                              {table.seats} seats
                            </div>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Package className="h-3 w-3" />
                              {packagingCount} items
                            </div>
                            <Badge 
                              variant={table.status === 'free' ? 'secondary' : 'default'}
                              className="text-xs h-4 px-1"
                            >
                              {table.status}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setExpandedTableId(isExpanded ? null : table.id)}
                            className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                          >
                            {isExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditTable(table)}
                            className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTable(table.id)}
                            className="h-7 w-7 p-0 text-muted-foreground hover:text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Expanded Packaging Configuration */}
                  {isExpanded && !isEditing && (
                    <>
                      <Separator className="bg-muted-foreground/10" />
                      <div className="p-2 pt-1 space-y-2">
                        <div className="flex items-center gap-1 mb-1">
                          <Settings className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs font-medium text-foreground">Packaging</span>
                        </div>
                        {/* Current Packaging Items */}
                        {packagingConfigs[table.id]?.length > 0 && (
                          <div className="space-y-1">
                            {packagingConfigs[table.id].map((item, index) => (
                              <div key={index} className="flex items-center justify-between p-2 rounded border border-muted-foreground/10 bg-muted/30">
                                <div className="flex items-center gap-1">
                                  <Package className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-xs font-medium text-foreground">
                                    {getStockItemName(item.stockItemId)}
                                  </span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Input
                                    type="number"
                                    min="0.1"
                                    step="0.1"
                                    value={item.quantity}
                                    onChange={(e) => handleUpdatePackagingQuantity(table.id, index, parseFloat(e.target.value) || 0)}
                                    className="w-12 h-6 text-xs text-center border-muted-foreground/20"
                                  />
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemovePackagingItem(table.id, index)}
                                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                        {/* Add New Packaging Item - dropdown search, compact row */}
                        <div className="space-y-1 pt-1 border-t border-muted-foreground/10">
                          <Label className="text-xs font-medium text-muted-foreground">Add Packaging Item</Label>
                          <div className="flex items-center gap-2">
                            <div className="max-w-[160px] flex-1">
                              <SearchableStockItemSelect
                                stockItems={packagingStockItems}
                                value={selectedStockItemId}
                                onChange={id => {
                                  setSelectedStockItemId(id);
                                  setNewPackagingItem({ stockItemId: id, quantity: 1 });
                                }}
                                placeholder="Search stock item..."
                                compact={true}
                              />
                            </div>
                            <Input
                              type="number"
                              min="0.1"
                              step="0.1"
                              inputMode="decimal"
                              className="w-14 h-7 text-xs text-center border-muted-foreground/20 appearance-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                              placeholder="Qty"
                              value={selectedStockItemId === newPackagingItem.stockItemId ? newPackagingItem.quantity : 1}
                              onChange={(e) => {
                                setNewPackagingItem({ stockItemId: selectedStockItemId, quantity: parseFloat(e.target.value) || 1 });
                              }}
                              disabled={!selectedStockItemId}
                            />
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-7 w-7 p-0 flex items-center justify-center"
                              disabled={!selectedStockItemId || newPackagingItem.quantity <= 0}
                              onClick={() => {
                                if (selectedStockItemId && newPackagingItem.quantity > 0) {
                                  handleAddPackagingItem(table.id);
                                  setSelectedStockItemId('');
                                  setNewPackagingItem({ stockItemId: '', quantity: 1 });
                                }
                              }}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        {/* Empty State */}
                        {(!packagingConfigs[table.id] || packagingConfigs[table.id].length === 0) && (
                          <div className="text-center py-3 text-muted-foreground">
                            <Package className="h-6 w-6 mx-auto mb-1 opacity-50" />
                            <p className="text-xs">No packaging configured</p>
                            <p className="text-xs">Add items that will be consumed automatically for dine-in orders</p>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
        {/* Empty State */}
        {tables.length === 0 && !isAddingTable && (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <h3 className="text-base font-medium mb-1">No tables yet</h3>
            <p className="text-xs mb-2">Create your first table to get started</p>
            <Button 
              variant="outline" 
              onClick={() => setIsAddingTable(true)}
              className="border-muted-foreground/20 hover:border-muted-foreground/40 h-7 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add First Table
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 