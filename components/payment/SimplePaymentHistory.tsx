'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { 
  Download, 
  Eye,
  EyeOff,
  Calendar
} from 'lucide-react';

// Import NEW payment system functions
import {
  getPaymentHistory
} from '@/lib/services/new-staff-balance-service';

import type { PaymentSnapshotDocument } from '@/lib/db/v4/schemas/new-payment-schemas';

interface SimplePaymentHistoryProps {
  staffId: string;
  className?: string;
  maxItems?: number;
  showExport?: boolean;
}

export default function SimplePaymentHistory({ 
  staffId, 
  className = '',
  maxItems = 10,
  showExport = true
}: SimplePaymentHistoryProps) {
  const { toast } = useToast();
  const [payments, setPayments] = useState<PaymentSnapshotDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    if (staffId) {
      loadPaymentHistory();
    }
  }, [staffId]);

  const loadPaymentHistory = async () => {
    setLoading(true);
    try {
      const history = await getPaymentHistory(staffId);
      setPayments(history);
    } catch (error) {
      console.error('Error loading payment history:', error);
      toast({
        title: "❌ Error Loading History",
        description: "Failed to load payment history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    const csvContent = [
      ['Date', 'Base Salary', 'Bonus', 'Deduction', 'Advance', 'Net Amount', 'Notes'],
      ...payments.map(payment => [
        new Date(payment.paymentDate).toLocaleDateString(),
        payment.baseSalary.toString(),
        payment.bonusAmount.toString(),
        payment.deductionAmount.toString(),
        payment.advanceAmount.toString(),
        payment.netAmount.toString(),
        payment.notes || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payment-history-${staffId}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    toast({
      title: "✅ Export Complete",
      description: "Payment history exported to CSV",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('DZD', 'DA');
  };

  const displayedPayments = showAll ? payments : payments.slice(0, maxItems);

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium">Payment History</h4>
          <p className="text-sm text-muted-foreground">
            {payments.length} payment snapshots
          </p>
        </div>
        
        {showExport && payments.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={exportToCSV}
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        )}
      </div>

      {/* Payment List */}
      <div className="space-y-2">
        {loading ? (
          <div className="text-center py-6 text-muted-foreground text-sm">
            Loading payment history...
          </div>
        ) : payments.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground text-sm">
            No payment snapshots found
          </div>
        ) : (
          <>
            {displayedPayments.map((payment) => (
              <SimplePaymentCard key={payment._id} payment={payment} formatCurrency={formatCurrency} />
            ))}
            
            {payments.length > maxItems && (
              <div className="text-center pt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAll(!showAll)}
                >
                  {showAll ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      Show Less
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Show All ({payments.length})
                    </>
                  )}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

// Simple Payment Card Component
function SimplePaymentCard({ 
  payment, 
  formatCurrency 
}: { 
  payment: PaymentSnapshotDocument; 
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);

  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'bg-green-100 text-green-800 border-green-200';
    if (netAmount > 20000) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (netAmount > 0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="border rounded-md p-3 hover:bg-muted/50 transition-colors">
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">
            {new Date(payment.paymentDate).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            })}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className={`text-xs ${getPaymentStatusColor(payment.netAmount)}`}>
            {formatCurrency(payment.netAmount)}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      {/* Quick Summary */}
      <div className="grid grid-cols-4 gap-2 text-xs">
        <div className="text-center">
          <div className="text-muted-foreground">Base</div>
          <div className="font-medium">{formatCurrency(payment.baseSalary)}</div>
        </div>
        <div className="text-center">
          <div className="text-green-600">Bonus</div>
          <div className="font-medium text-green-700">+{formatCurrency(payment.bonusAmount)}</div>
        </div>
        <div className="text-center">
          <div className="text-red-600">Deduction</div>
          <div className="font-medium text-red-700">-{formatCurrency(payment.deductionAmount)}</div>
        </div>
        <div className="text-center">
          <div className="text-orange-600">Advance</div>
          <div className="font-medium text-orange-700">-{formatCurrency(payment.advanceAmount)}</div>
        </div>
      </div>

      {/* Detailed View */}
      {showDetails && (
        <>
          <Separator className="my-2" />
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Gross Amount:</span>
              <span className="font-medium">{formatCurrency(payment.grossAmount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Deductions:</span>
              <span className="font-medium text-red-600">-{formatCurrency(payment.totalDeductions)}</span>
            </div>
            <div className="flex justify-between font-medium border-t pt-1">
              <span>Net Amount:</span>
              <span className="text-green-600">{formatCurrency(payment.netAmount)}</span>
            </div>
            {payment.notes && (
              <div className="mt-1 p-1 bg-muted/50 rounded text-xs">
                <span className="font-medium">Notes: </span>
                {payment.notes}
              </div>
            )}
            {payment.usedBalanceIds && payment.usedBalanceIds.length > 0 && (
              <div className="text-xs text-muted-foreground mt-1">
                Used {payment.usedBalanceIds.length} balance entries
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
