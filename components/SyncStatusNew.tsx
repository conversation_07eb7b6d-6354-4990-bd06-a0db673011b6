"use client";

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Cloud, CloudOff, AlertCircle, Loader2, CheckCircle2 } from 'lucide-react';

interface SyncStatusNewProps {
  syncState: {
    status: 'idle' | 'syncing' | 'error' | 'paused' | 'complete';
    error?: Error;
    lastSync?: string;
    progress?: {
      docs_read: number;
      docs_written: number;
      pending: number;
    };
  };
  className?: string;
}

export function SyncStatusNew({ syncState, className = '' }: SyncStatusNewProps) {
  const [timeAgo, setTimeAgo] = useState<string>('');

  // Update time ago every 10 seconds
  useEffect(() => {
    const updateTimeAgo = () => {
      if (!syncState.lastSync) {
        setTimeAgo('Never');
        return;
      }

      const now = new Date();
      const syncDate = new Date(syncState.lastSync);
      const diff = now.getTime() - syncDate.getTime();
      
      if (diff < 1000) {
        setTimeAgo('Just now');
      } else if (diff < 60000) {
        setTimeAgo(`${Math.floor(diff / 1000)}s ago`);
      } else if (diff < 3600000) {
        setTimeAgo(`${Math.floor(diff / 60000)}m ago`);
      } else if (diff < 86400000) {
        setTimeAgo(`${Math.floor(diff / 3600000)}h ago`);
      } else {
        setTimeAgo(`${Math.floor(diff / 86400000)}d ago`);
      }
    };

    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 10000);
    
    return () => clearInterval(interval);
  }, [syncState.lastSync]);

  // Render different status badges based on sync state
  const renderStatusBadge = () => {
    const pendingChanges = syncState.progress?.pending || 0;
    
    switch (syncState.status) {
      case 'complete':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
            <CheckCircle2 className="h-3.5 w-3.5" />
            <span>Synced</span>
          </Badge>
        );
      case 'syncing':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1">
            <Loader2 className="h-3.5 w-3.5 animate-spin" />
            <span>Syncing</span>
          </Badge>
        );
      case 'paused':
        return (
          <Badge variant="outline" className={pendingChanges > 0 ? "bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1" : "bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"}>
            {pendingChanges > 0 ? <AlertCircle className="h-3.5 w-3.5" /> : <Cloud className="h-3.5 w-3.5" />}
            <span>{pendingChanges > 0 ? "Pending" : "Paused"}</span>
            {pendingChanges > 0 && (
              <span className="ml-1 text-xs bg-amber-200 text-amber-800 px-1.5 py-0.5 rounded-full">
                {pendingChanges}
              </span>
            )}
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
            <AlertCircle className="h-3.5 w-3.5" />
            <span>Error</span>
          </Badge>
        );
      case 'idle':
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 flex items-center gap-1">
            <CloudOff className="h-3.5 w-3.5" />
            <span>Offline</span>
          </Badge>
        );
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-2 ${className}`}>
            {renderStatusBadge()}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <p className="font-semibold">Sync Status: {syncState.status}</p>
            <p>Last synced: {timeAgo}</p>
            {syncState.error && (
              <p className="text-red-500 mt-1">
                Error: {syncState.error.message}
              </p>
            )}
            {syncState.progress && (
              <div className="mt-1 text-xs">
                <p>Read: {syncState.progress.docs_read}</p>
                <p>Written: {syncState.progress.docs_written}</p>
                <p>Pending: {syncState.progress.pending}</p>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
} 