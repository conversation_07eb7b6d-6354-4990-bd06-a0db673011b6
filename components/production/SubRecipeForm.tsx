"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StockItem } from "@/types/stock";
import { SubRecipe } from "@/lib/db/v4/schemas/sub-recipe-schema";
import { Loader2, Plus, X } from "lucide-react";

const subRecipeSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit comporter au moins 2 caractères." }),
  ingredients: z.array(
    z.object({
      stockItemId: z.string().min(1, { message: "Veuillez sélectionner un ingrédient." }),
      quantity: z.coerce.number().positive({ message: "La quantité doit être positive." }),
    })
  ).min(1, { message: "Au moins un ingrédient est requis." }),
  yield: z.object({
    quantity: z.coerce.number().positive({ message: "La quantité produite doit être positive." }),
    unit: z.enum(["kg", "L", "pcs", "g", "ml"], {
      required_error: "Veuillez sélectionner une unité de mesure.",
    }),
  }),
});

interface SubRecipeFormProps {
  onSubmit: (data: z.infer<typeof subRecipeSchema>) => void;
  onCancel: () => void;
  initialData?: SubRecipe | null;
  stockItems: StockItem[];
  isMobile?: boolean;
}

export function SubRecipeForm({ 
  onSubmit, 
  onCancel, 
  initialData, 
  stockItems, 
  isMobile = false 
}: SubRecipeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof subRecipeSchema>>({
    resolver: zodResolver(subRecipeSchema),
    defaultValues: initialData
      ? {
          name: initialData.name,
          ingredients: initialData.ingredients,
          yield: initialData.yield,
        }
      : {
          name: "",
          ingredients: [{ stockItemId: "", quantity: 1 }],
          yield: { quantity: 1, unit: "kg" },
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "ingredients",
  });

  const handleSubmit = async (values: z.infer<typeof subRecipeSchema>) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-3">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3">
          {/* Recipe Name - Compact */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs font-medium">Nom de la sous-recette</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Pâte à Pizza"
                    className="h-8 text-xs"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Yield Information - Ultra Compact */}
          <div className="space-y-1">
            <FormLabel className="text-xs font-medium">Rendement</FormLabel>
            <div className="grid grid-cols-2 gap-2">
              <FormField
                control={form.control}
                name="yield.quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="number"
                        min="0.01"
                        step="0.01"
                        placeholder="1,2"
                        className="h-8 text-xs"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="yield.unit"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="kg" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="kg" className="text-xs">kg</SelectItem>
                        <SelectItem value="g" className="text-xs">g</SelectItem>
                        <SelectItem value="L" className="text-xs">L</SelectItem>
                        <SelectItem value="ml" className="text-xs">ml</SelectItem>
                        <SelectItem value="pcs" className="text-xs">pcs</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Ingredients - Ultra Compact */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <FormLabel className="text-xs font-medium">Ingrédients</FormLabel>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => append({ stockItemId: "", quantity: 1 })}
                className="h-6 px-2 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Ajouter
              </Button>
            </div>

            <div className="space-y-1 max-h-[250px] overflow-y-auto">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-2 p-2 border rounded-md bg-muted/20">
                  <div className="flex-1">
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.stockItemId`}
                      render={({ field }) => (
                        <FormItem>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="h-7 text-xs">
                                <SelectValue placeholder="Sélectionner un ingrédient" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {stockItems.map((item) => (
                                <SelectItem key={item.id} value={item.id} className="text-xs">
                                  {item.name} ({item.unit})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="w-16">
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              min="0.01"
                              step="0.01"
                              placeholder="1"
                              className="h-7 text-xs text-center"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-muted-foreground hover:text-destructive"
                    onClick={() => remove(index)}
                    disabled={fields.length === 1}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>

            {form.formState.errors.ingredients?.message && (
              <p className="text-xs font-medium text-destructive">
                {form.formState.errors.ingredients.message}
              </p>
            )}
          </div>

          {/* Actions - Compact */}
          <div className="flex gap-2 pt-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              className="flex-1 h-8 text-xs"
            >
              Annuler
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="flex-1 h-8 text-xs"
            >
              {isSubmitting && <Loader2 className="mr-1 h-3 w-3 animate-spin" />}
              Créer
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
