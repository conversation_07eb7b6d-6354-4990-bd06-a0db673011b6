"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { RefreshCw, ChefHat, Calendar, Info } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { formatCurrency } from "@/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import type { SubRecipe, ProductionBatch, RecipeIngredient } from '@/types/cogs';

// Schema for batch production validation
const batchProductionSchema = z.object({
  subRecipeId: z.string().min(1, { message: "Veuillez sélectionner une sous-recette." }),
  batchSize: z.coerce.number().positive({ message: "La taille du lot doit être positive." }),
});

export function BatchProduction() {
  const { toast } = useToast();
  const { subRecipes, productionBatches, createProductionBatch, refreshData } = useCOGSV4();
  const { stockItems } = useStockV4();
  const { user } = useAuth();
  const [selectedSubRecipe, setSelectedSubRecipe] = useState<SubRecipe | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Initialize form
  const form = useForm<z.infer<typeof batchProductionSchema>>({
    resolver: zodResolver(batchProductionSchema),
    defaultValues: {
      subRecipeId: "",
      batchSize: 1,
    },
  });

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshData();
    setIsRefreshing(false);
  };

  // Handle sub-recipe selection
  const handleSubRecipeChange = (subRecipeId: string) => {
    const subRecipe = subRecipes.find((sr) => sr._id === subRecipeId);
    setSelectedSubRecipe(subRecipe || null);
  };

  // Handle form submission
  const handleSubmit = async (values: z.infer<typeof batchProductionSchema>) => {
    if (!selectedSubRecipe) return;

    setIsSubmitting(true);
    try {
      // Calculate produced quantity based on batch size and yield
      const producedQuantity = selectedSubRecipe.yield.quantity * values.batchSize;

      // Create production batch
      await createProductionBatch({
        subRecipeId: values.subRecipeId,
        batchSize: values.batchSize,
        producedQuantity,
        date: new Date().toISOString(),
        performedBy: user?.name || (user as any)?.username || 'unknown',
      });

      toast({
        title: "Lot enregistré",
        description: `Production de ${values.batchSize} lot(s) de ${selectedSubRecipe.name} enregistrée avec succès.`,
        duration: 3000,
      });

      // Reset form
      form.reset({
        subRecipeId: "",
        batchSize: 1,
      });
      setSelectedSubRecipe(null);
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Échec de l'enregistrement de la production. Veuillez réessayer.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sort batches by date (most recent first)
  const sortedBatches = [...productionBatches].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Get recent batches (last 10)
  const recentBatches = sortedBatches.slice(0, 10);

  return (
    <div className="max-w-5xl mx-auto space-y-8">
      {/* knowledge: Wide, two-column Record Production UI/UX */}
      <Card className="shadow-sm border border-muted-foreground/10">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl">Enregistrer une production</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              {/* knowledge: Three-column layout: left (dropdown+button), middle (summary), right (ingredients) */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                {/* Left column: dropdown + button */}
                <div className="flex flex-col gap-3">
                  <FormField
                    control={form.control}
                    name="subRecipeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">Sous-recette</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            handleSubRecipeChange(value);
                          }}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="h-9 text-base">
                              <SelectValue placeholder="Sélectionnez une sous-recette" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {subRecipes.map((subRecipe) => (
                              <SelectItem key={subRecipe._id} value={subRecipe._id} className="text-base">
                                {subRecipe.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="batchSize"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">Taille du lot</FormLabel>
                        <FormControl>
                          <Input type="number" min="0.1" step="0.1" {...field} className="h-9 text-base" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full h-9 text-base mt-2" disabled={isSubmitting}>
                    {isSubmitting ? "Enregistrement..." : "Enregistrer"}
                  </Button>
                </div>
                {/* Middle column: summary */}
                {selectedSubRecipe && (
                  <div className="border rounded bg-muted/10 p-4 text-sm flex flex-col gap-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Rendement :</span>
                      <span>{selectedSubRecipe.yield.quantity} {selectedSubRecipe.yield.unit}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Taille du lot :</span>
                      <span>{form.watch("batchSize")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Rendement total :</span>
                      <span>{(selectedSubRecipe.yield.quantity * form.watch("batchSize")).toFixed(2)} {selectedSubRecipe.yield.unit}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Coût/unité :</span>
                      <span>{formatCurrency(selectedSubRecipe.costPerUnit || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Coût total :</span>
                      <span>{formatCurrency((selectedSubRecipe.costPerUnit || 0) * selectedSubRecipe.yield.quantity * form.watch("batchSize"))}</span>
                    </div>
                  </div>
                )}
                {/* Right column: ingredients */}
                {selectedSubRecipe && (
                  <div className="border rounded bg-muted/10 p-4 text-sm">
                    <div className="font-medium mb-1">Ingrédients</div>
                    <ul className="space-y-0.5">
                      {selectedSubRecipe.ingredients.map((ingredient: RecipeIngredient) => {
                        // knowledge: use id instead of _id for v4 StockItem
                        const stockItem = stockItems.find(item => item.id === ingredient.stockItemId);
                        return (
                          <li key={ingredient.stockItemId} className="flex justify-between">
                            <span>{stockItem ? `${stockItem.name} (${stockItem.unit})` : ingredient.stockItemId}</span>
                            <span>{(ingredient.quantity * form.watch("batchSize")).toFixed(2)}</span>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}
              </div>
              {/* end knowledge */}
            </form>
          </Form>
        </CardContent>
      </Card>
      {/* end knowledge */}

      {/* knowledge: Recent Production below Record Production */}
      <Card className="shadow-sm border border-muted-foreground/10">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Productions récentes</CardTitle>
            <CardDescription>
              Lots de production récemment enregistrés
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
            Rafraîchir
          </Button>
        </CardHeader>
        <CardContent>
          {recentBatches.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Aucun lot de production enregistré pour le moment.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Sous-recette</TableHead>
                    <TableHead>Taille du lot</TableHead>
                    <TableHead>Produit</TableHead>
                    <TableHead>Enregistré par</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentBatches.map((batch) => {
                    const subRecipe = subRecipes.find(
                      (sr) => sr._id === batch.subRecipeId
                    );
                    return (
                      <TableRow key={batch._id}>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            {new Date(batch.date).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <ChefHat className="h-4 w-4 mr-2 text-muted-foreground" />
                            {subRecipe?.name || "Unknown"}
                          </div>
                        </TableCell>
                        <TableCell>{batch.batchSize}</TableCell>
                        <TableCell>
                          {batch.producedQuantity} {subRecipe?.yield.unit || "units"}
                        </TableCell>
                        <TableCell>{batch.performedBy}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      {/* end knowledge */}
    </div>
  );
}
