"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useIsMobile } from "@/hooks/use-mobile";
import { Plus, Edit, Trash2, RefreshCw, ChefHat, MoreHorizontal, AlertTriangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { SubRecipeForm } from "./SubRecipeForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatCurrency } from "@/lib/utils";
import { SubRecipe } from '@/types/cogs';

export function SubRecipesList() {
  // Enhanced minimalistic version - no current stock column
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { subRecipes, createSubRecipe, updateSubRecipe, deleteSubRecipe, loading, error } = useCOGSV4();
  const { stockItems } = useStockV4();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedSubRecipe, setSelectedSubRecipe] = useState<SubRecipe | null>(null);

  const handleSubRecipeSubmit = async (data: any) => {
    try {
      if (selectedSubRecipe) {
        await updateSubRecipe(selectedSubRecipe._id, data);
        toast({
          title: "✅ Updated",
          description: `${data.name} updated successfully.`,
          duration: 2000,
        });
      } else {
        await createSubRecipe(data);
        toast({
          title: "✅ Created",
          description: `${data.name} created successfully.`,
          duration: 2000,
        });
      }
      setIsFormOpen(false);
      setSelectedSubRecipe(null);
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to save sub-recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  const handleDeleteSubRecipe = async (subRecipe: SubRecipe) => {
    try {
      await deleteSubRecipe(subRecipe._id);
      toast({
        title: "🗑️ Deleted",
        description: `${subRecipe.name} deleted successfully.`,
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to delete sub-recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  const handleEditSubRecipe = (subRecipe: SubRecipe) => {
    setSelectedSubRecipe(subRecipe);
    setIsFormOpen(true);
  };

  const handleNewSubRecipe = () => {
    setSelectedSubRecipe(null);
    setIsFormOpen(true);
  };

  const getStockItemName = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem ? stockItem.name : 'Unknown Item';
  };

  const getStockItemCost = (stockItemId: string) => {
    const stockItem = stockItems.find((item) => item.id === stockItemId);
    return stockItem && stockItem.costPerUnit ? stockItem.costPerUnit : 0;
  };

  const calculateIngredientCost = (stockItemId: string, quantity: number) => {
    const costPerUnit = getStockItemCost(stockItemId);
    return costPerUnit * quantity;
  };

  // Mobile card component - more compact
  const MobileSubRecipeCard = ({ subRecipe }: { subRecipe: SubRecipe }) => {
    const totalCost = subRecipe.ingredients.reduce(
      (sum, ingredient) => sum + calculateIngredientCost(ingredient.stockItemId, ingredient.quantity),
      0
    );

    return (
      <Card className="group cursor-pointer transition-all duration-200 hover:shadow-sm hover:border-primary/20">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <div className="p-1.5 rounded-md bg-orange-500/10 shrink-0">
                <ChefHat className="h-4 w-4 text-orange-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-medium text-sm truncate">{subRecipe.name}</h3>
                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-0.5">
                  <span>{subRecipe.yield.quantity} {subRecipe.yield.unit}</span>
                  <span>•</span>
                  <span>{formatCurrency(subRecipe.costPerUnit || 0)}</span>
                </div>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleEditSubRecipe(subRecipe)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDeleteSubRecipe(subRecipe)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {/* Header - Compact */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Sous-recettes</h2>
          <p className="text-sm text-muted-foreground">
            Visualisez et gérez votre inventaire de sous-recettes
          </p>
        </div>
        <Button size="sm" onClick={handleNewSubRecipe}>
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle sous-recette
        </Button>
      </div>

      {/* Content */}
      {loading ? (
        <Card className="border-dashed">
          <CardContent className="p-8 text-center">
            <RefreshCw className="h-12 w-12 mx-auto mb-3 text-muted-foreground/50 animate-spin" />
            <h3 className="text-base font-medium mb-2">
              Chargement des sous-recettes...
            </h3>
            <p className="text-sm text-muted-foreground">
              Veuillez patienter
            </p>
          </CardContent>
        </Card>
      ) : error ? (
        <Card className="border-dashed border-red-200">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto mb-3 text-red-400" />
            <h3 className="text-base font-medium mb-2 text-red-600">
              Erreur de chargement
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              {error.message || 'Une erreur est survenue lors du chargement des données'}
            </p>
            <Button size="sm" variant="outline" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Recharger
            </Button>
          </CardContent>
        </Card>
      ) : subRecipes.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="p-8 text-center">
            <ChefHat className="h-12 w-12 mx-auto mb-3 text-muted-foreground/50" />
            <h3 className="text-base font-medium mb-2">
              Aucune sous-recette trouvée
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              Créez-en dans la section Production !
            </p>
            <Button size="sm" onClick={handleNewSubRecipe}>
              <Plus className="h-4 w-4 mr-2" />
              Créer la première
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        <div className="space-y-2">
          {subRecipes.map((subRecipe) => (
            <MobileSubRecipeCard key={subRecipe._id} subRecipe={subRecipe} />
          ))}
        </div>
      ) : (
        <Card className="border-0 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="font-semibold">Nom</TableHead>
                  <TableHead className="font-semibold">Rendement</TableHead>
                  <TableHead className="font-semibold">Coût par unité</TableHead>
                  <TableHead className="font-semibold">Dernière production</TableHead>
                  <TableHead className="font-semibold text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {subRecipes.map((subRecipe) => (
                  <TableRow key={subRecipe._id} className="cursor-pointer hover:bg-muted/30 transition-colors">
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <div className="p-1.5 rounded-md bg-orange-500/10">
                          <ChefHat className="h-4 w-4 text-orange-600" />
                        </div>
                        <span>{subRecipe.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {subRecipe.yield.quantity} {subRecipe.yield.unit}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(subRecipe.costPerUnit || 0)} / {subRecipe.yield.unit}
                    </TableCell>
                    <TableCell>
                      {subRecipe.lastProduced ? (
                        <span className="text-sm text-muted-foreground">
                          {new Date(subRecipe.lastProduced).toLocaleDateString()}
                        </span>
                      ) : (
                        <span className="text-muted-foreground text-sm">Jamais</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditSubRecipe(subRecipe)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteSubRecipe(subRecipe)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              {selectedSubRecipe ? "Modifier la sous-recette" : "Créer une sous-recette"}
            </DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto flex-1">
            <SubRecipeForm
              onSubmit={handleSubRecipeSubmit}
              initialData={selectedSubRecipe}
              stockItems={stockItems}
              onCancel={() => setIsFormOpen(false)}
              isMobile={isMobile}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
