"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useIsMobile } from "@/hooks/use-mobile";
import { Plus, Edit, Trash2, ChefHat, MoreHorizontal, DollarSign, TrendingUp } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { MenuItemRecipeForm } from "./MenuItemRecipeForm";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatCurrency } from "@/lib/utils";
import type { MenuItemRecipe } from '@/types/cogs';
import type { Category, MenuItem } from '@/lib/db/v4-menu-service';

export function MenuRecipesList() {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { menuItemRecipes, createMenuItemRecipe, updateMenuItemRecipe, deleteMenuItemRecipe, subRecipes, loading, error } = useCOGSV4();
  const { categories } = useMenuV4();
  const { stockItems } = useStockV4();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedRecipe, setSelectedRecipe] = useState<MenuItemRecipe | null>(null);

  // Get all menu items from all categories
  const menuItems = categories.flatMap((category: Category) =>
    category.items.map((item: MenuItem) => ({
      id: item.id,
      name: item.name,
      categoryName: category.name,
      sizes: category.sizes || [],
      prices: item.prices
    }))
  );

  // Helper functions
  const getMenuItemName = (menuItemId: string) => {
    const menuItem = menuItems.find((item) => item.id === menuItemId);
    return menuItem ? menuItem.name : 'Unknown Item';
  };

  const getMenuItemCategory = (menuItemId: string) => {
    const menuItem = menuItems.find((item) => item.id === menuItemId);
    return menuItem ? menuItem.categoryName : '';
  };

  const calculateRecipeCost = (recipe: MenuItemRecipe) => {
    return recipe.ingredients.reduce((sum: number, ingredient: any) => {
      if ('stockItemId' in ingredient) {
        const stockItem = stockItems.find(item => item.id === ingredient.stockItemId);
        const costPerUnit = stockItem?.costPerUnit || 0;
        return sum + (costPerUnit * ingredient.quantity);
      } else if ('subRecipeId' in ingredient) {
        const subRecipe = subRecipes.find(sr => sr._id === ingredient.subRecipeId);
        const costPerUnit = subRecipe?.costPerUnit || 0;
        return sum + (costPerUnit * ingredient.quantity);
      }
      return sum;
    }, 0);
  };

  const calculateProfitMargin = (recipe: MenuItemRecipe) => {
    const menuItem = menuItems.find((item: MenuItem) => item.id === recipe.menuItemId);
    if (!menuItem || !recipe.costPerUnit) return null;

    const price = recipe.size ? menuItem.prices[recipe.size] : Object.values(menuItem.prices)[0];
    if (!price) return null;

    const margin = ((price - recipe.costPerUnit) / price) * 100;
    return { margin, price, profit: price - recipe.costPerUnit };
  };

  const handleRecipeSubmit = async (data: any) => {
    try {
      if (selectedRecipe) {
        await updateMenuItemRecipe(selectedRecipe._id, data);
        toast({
          title: "✅ Updated",
          description: "Recipe updated successfully.",
          duration: 2000,
        });
      } else {
        await createMenuItemRecipe(data);
        toast({
          title: "✅ Created",
          description: "Recipe created successfully.",
          duration: 2000,
        });
      }
      setIsFormOpen(false);
      setSelectedRecipe(null);
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to save recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  const handleDeleteRecipe = async (recipe: MenuItemRecipe) => {
    try {
      await deleteMenuItemRecipe(recipe._id);
      toast({
        title: "🗑️ Deleted",
        description: "Recipe deleted successfully.",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "Failed to delete recipe.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  const handleEditRecipe = (recipe: MenuItemRecipe) => {
    setSelectedRecipe(recipe);
    setIsFormOpen(true);
  };

  const handleNewRecipe = () => {
    setSelectedRecipe(null);
    setIsFormOpen(true);
  };

  // Mobile card component
  const MobileRecipeCard = ({ recipe }: { recipe: MenuItemRecipe }) => {
    const menuItem = menuItems.find((item: MenuItem) => item.id === recipe.menuItemId);
    const profitData = calculateProfitMargin(recipe);

    return (
      <Card className="group transition-all duration-200 hover:shadow-md hover:border-blue-500/20">
        <CardContent className="p-3">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/5 shrink-0">
                <ChefHat className="h-4 w-4 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm truncate">
                  {recipe.menuItemName || menuItem?.name || "Unknown Item"}
                </h3>
                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-0.5">
                  <span>{getMenuItemCategory(recipe.menuItemId)}</span>
                  {recipe.size && (
                    <>
                      <span>•</span>
                      <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                        {recipe.size}
                      </Badge>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => handleEditRecipe(recipe)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="grid grid-cols-3 gap-3 text-xs">
            <div>
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <DollarSign className="h-3 w-3" />
                <span>Coût</span>
              </div>
              <span className="font-medium">{formatCurrency(recipe.costPerUnit || 0)}</span>
            </div>
            <div>
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <DollarSign className="h-3 w-3" />
                <span>Prix</span>
              </div>
              <span className="font-medium">{formatCurrency(profitData?.price || 0)}</span>
            </div>
            <div>
              <div className="flex items-center gap-1 text-muted-foreground mb-1">
                <TrendingUp className="h-3 w-3" />
                <span>Marge</span>
              </div>
              {profitData ? (
                <div className="flex flex-col gap-0.5">
                  <span className={`font-medium text-xs ${profitData.profit < 0 ? 'text-destructive' : 'text-green-600'}`}>
                    {profitData.profit > 0 ? "+" : ""}{formatCurrency(profitData.profit)}
                  </span>
                  <span className={`text-xs ${profitData.margin < 0 ? 'text-destructive' : 'text-green-600'}`}>
                    {profitData.margin > 0 ? "+" : ""}{profitData.margin.toFixed(1)}%
                  </span>
                </div>
              ) : (
                <span className="text-muted-foreground">N/A</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-3">
      {/* Header */}
      <div className={isMobile ? "space-y-2" : "flex justify-between items-center"}>
        <div>
          <h2 className="text-lg font-semibold">Recettes des articles du menu</h2>
          {!isMobile && (
            <p className="text-sm text-muted-foreground">
              Gérez les recettes de vos articles du menu
            </p>
          )}
        </div>
        <Button size="sm" onClick={handleNewRecipe} className={isMobile ? "w-full" : ""}>
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle recette
        </Button>
      </div>

      {/* Content */}
      {menuItemRecipes.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="p-6 text-center">
            <ChefHat className="h-10 w-10 mx-auto mb-3 text-muted-foreground/50" />
            <h3 className="text-sm font-medium mb-2">
              Aucune recette trouvée
            </h3>
            <p className="text-xs text-muted-foreground mb-4">
              Créez votre première recette pour commencer.
            </p>
            <Button size="sm" onClick={handleNewRecipe}>
              <Plus className="h-4 w-4 mr-2" />
              Créer la première
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        <div className="space-y-2">
          {menuItemRecipes.map((recipe) => (
            <MobileRecipeCard key={recipe._id} recipe={recipe} />
          ))}
        </div>
      ) : (
        <Card className="border-0 shadow-sm">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="font-semibold">Menu Item</TableHead>
                  <TableHead className="font-semibold">Taille</TableHead>
                  <TableHead className="font-semibold">Coût</TableHead>
                  <TableHead className="font-semibold">Prix de vente</TableHead>
                  <TableHead className="font-semibold">Marge bénéficiaire</TableHead>
                  <TableHead className="font-semibold text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {menuItemRecipes.map((recipe) => {
                  const menuItem = menuItems.find((item: MenuItem) => item.id === recipe.menuItemId);
                  const price = menuItem && recipe.size
                    ? menuItem.prices[recipe.size]
                    : menuItem
                      ? Object.values(menuItem.prices)[0]
                      : 0;
                  const profitData = calculateProfitMargin(recipe);

                  return (
                    <TableRow key={recipe._id} className="hover:bg-muted/50 transition-colors">
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/5">
                            <ChefHat className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <span>{recipe.menuItemName || menuItem?.name || "Unknown Item"}</span>
                            <div className="text-xs text-muted-foreground">{getMenuItemCategory(recipe.menuItemId)}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {recipe.size ? (
                          <Badge variant="outline" className="text-xs">
                            {recipe.size}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground text-sm">Default</span>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(recipe.costPerUnit || 0)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(price)}
                      </TableCell>
                      <TableCell>
                        {profitData ? (
                          <div className="flex items-center gap-2">
                            <span className={`text-sm font-medium ${profitData.profit < 0 ? "text-destructive" : "text-green-600"}`}>
                              {profitData.profit > 0 ? "+" : ""}{formatCurrency(profitData.profit)}
                            </span>
                            <Badge
                              variant={profitData.margin < 0 ? "destructive" : profitData.margin < 65 ? "secondary" : "default"}
                              className="text-xs"
                            >
                              {profitData.margin > 0 ? "+" : ""}{profitData.margin.toFixed(1)}%
                            </Badge>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">N/A</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditRecipe(recipe)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteRecipe(recipe)}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className={isMobile ? "sm:max-w-full max-h-[90vh] overflow-hidden" : "max-w-3xl max-h-[90vh] overflow-hidden"}>
          <DialogHeader className={isMobile ? "pb-2" : ""}>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5" />
              {selectedRecipe ? "Modifier la recette" : "Créer une recette"}
            </DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto flex-1">
            <MenuItemRecipeForm
              onSubmit={handleRecipeSubmit}
              initialData={selectedRecipe}
              menuItems={menuItems}
              subRecipes={subRecipes}
              onCancel={() => setIsFormOpen(false)}
              isMobile={isMobile}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}