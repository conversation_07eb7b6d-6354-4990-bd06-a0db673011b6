"use client";

import { useState, useEffect } from "react";
import { WifiOff, Wifi } from "lucide-react";
import { cn } from "@/lib/utils";

interface SimpleNetworkIndicatorProps {
  className?: string;
}

/**
 * Simple, clean network status indicator for the app sidebar
 * Shows a minimal dot indicator with online/offline status
 */
export function SimpleNetworkIndicator({ className = '' }: SimpleNetworkIndicatorProps) {
  const [online, setOnline] = useState(true);
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    // Only run on client-side
    setMounted(true);
    
    // Initial check
    setOnline(navigator.onLine);
    
    // Register listeners
    const handleOnline = () => setOnline(true);
    const handleOffline = () => setOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Don't render anything during SSR to prevent hydration mismatch
  if (!mounted) return null;
  
  return (
    <div className={cn(
      "flex items-center gap-2 px-2 py-1 text-xs",
      className
    )}>
      {online ? (
        <>
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          <span className="text-muted-foreground group-data-[collapsible=icon]:hidden">Online</span>
        </>
      ) : (
        <>
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          <span className="text-muted-foreground group-data-[collapsible=icon]:hidden">Offline</span>
        </>
      )}
    </div>
  );
} 