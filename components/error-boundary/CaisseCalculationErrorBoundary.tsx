'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>tor, AlertTriangle, Refresh<PERSON><PERSON>, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  calculationType?: string; // e.g., 'current_period', 'calculation_history', 'session_totals'
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error Boundary for Caisse Calculation components
 * Catches JavaScript errors in cash register calculations and financial summaries
 */
export class CaisseCalculationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[CaisseCalculationErrorBoundary] Calculation error:', error);
    console.error('[CaisseCalculationErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log calculation errors with context
    if (process.env.NODE_ENV === 'production') {
      console.error('Caisse calculation error in production:', {
        calculationType: this.props.calculationType || 'unknown',
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const calculationType = this.props.calculationType || 'calculation';

      return (
        <Card className="w-full max-w-md mx-auto mt-8 border-orange-200">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Calculator className="h-12 w-12 text-orange-500" />
                <AlertTriangle className="h-6 w-6 text-red-600 absolute -top-1 -right-1" />
              </div>
            </div>
            <CardTitle className="text-orange-600">Calculation Error</CardTitle>
            <CardDescription>
              An error occurred during {calculationType}. The calculation could not be completed.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded p-3">
              <div className="flex items-start gap-2">
                <TrendingUp className="h-4 w-4 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Data Integrity Protected</p>
                  <p className="text-xs text-blue-600 mt-1">
                    Your financial data remains safe. This is a display issue only.
                  </p>
                </div>
              </div>
            </div>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <p className="text-sm font-medium text-red-800 mb-2">Error Details:</p>
                <p className="text-xs text-red-600 font-mono">
                  {this.state.error.message}
                </p>
                {this.state.errorInfo && (
                  <details className="mt-2">
                    <summary className="text-xs text-red-600 cursor-pointer">
                      Component Stack
                    </summary>
                    <pre className="text-xs text-red-600 mt-1 whitespace-pre-wrap">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            )}
            
            <div className="flex gap-2">
              <Button 
                onClick={this.handleRetry}
                className="flex-1"
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Recalculate
              </Button>
              <Button 
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                Reload Page
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for caisse calculations
 */
export function withCaisseCalculationErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  calculationType?: string,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <CaisseCalculationErrorBoundary calculationType={calculationType} fallback={fallback}>
      <Component {...props} />
    </CaisseCalculationErrorBoundary>
  );

  WrappedComponent.displayName = `withCaisseCalculationErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}