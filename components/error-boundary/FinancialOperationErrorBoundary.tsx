'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { DollarSign, AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  operation?: string; // e.g., 'payment_processing', 'cash_transaction', 'expense_recording'
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error Boundary for Financial Operation components
 * Catches JavaScript errors in payment processing, cash transactions, and expense operations
 */
export class FinancialOperationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[FinancialOperationErrorBoundary] Financial operation error:', error);
    console.error('[FinancialOperationErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log financial errors with high priority
    if (process.env.NODE_ENV === 'production') {
      console.error('CRITICAL: Financial operation error in production:', {
        operation: this.props.operation || 'unknown',
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      });
      
      // TODO: Send to error tracking service with high priority
      // This should trigger immediate alerts for financial errors
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const operationName = this.props.operation || 'financial operation';

      return (
        <Card className="w-full max-w-md mx-auto mt-8 border-red-200">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <DollarSign className="h-12 w-12 text-red-500" />
                <AlertTriangle className="h-6 w-6 text-red-600 absolute -top-1 -right-1" />
              </div>
            </div>
            <CardTitle className="text-red-600">Financial Operation Failed</CardTitle>
            <CardDescription>
              An error occurred during {operationName}. Your data has been protected and no partial transactions were recorded.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
              <p className="text-sm text-yellow-800">
                <strong>Important:</strong> No financial data has been corrupted. 
                Please retry the operation or contact support if the issue persists.
              </p>
            </div>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <p className="text-sm font-medium text-red-800 mb-2">Error Details:</p>
                <p className="text-xs text-red-600 font-mono">
                  {this.state.error.message}
                </p>
                {this.state.errorInfo && (
                  <details className="mt-2">
                    <summary className="text-xs text-red-600 cursor-pointer">
                      Component Stack
                    </summary>
                    <pre className="text-xs text-red-600 mt-1 whitespace-pre-wrap">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            )}
            
            <div className="flex gap-2">
              <Button 
                onClick={this.handleRetry}
                className="flex-1"
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Operation
              </Button>
              <Button 
                onClick={() => window.location.reload()}
                className="flex-1"
                variant="destructive"
              >
                Reload Page
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for financial operations
 */
export function withFinancialErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  operation?: string,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <FinancialOperationErrorBoundary operation={operation} fallback={fallback}>
      <Component {...props} />
    </FinancialOperationErrorBoundary>
  );

  WrappedComponent.displayName = `withFinancialErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}