/**
 * Autonomous Sync Status Component
 * 
 * Shows the status of the autonomous sync system with:
 * - Real-time connection status
 * - Discovery information
 * - Control buttons
 * - Configuration options
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Activity,
  CheckCircle,
  XCircle,
  Search,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Server,
  Wifi,
  WifiOff,
  Clock,
  AlertCircle
} from 'lucide-react';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface AutonomousSyncStatusProps {
  className?: string;
}

export function AutonomousSyncStatus({ className }: AutonomousSyncStatusProps) {
  const {
    status,
    isRunning,
    isConnected,
    isSyncing,
    discoveredServers,
    connectedServers,
    lastDiscovery,
    error,
    start,
    stop,
    discover,
    updateConfig
  } = useAutonomousSync({
    autoStart: true,
    discoveryInterval: 30000,
    reconnectInterval: 60000
  });

  const [showSettings, setShowSettings] = useState(false);
  const [discoveryInterval, setDiscoveryInterval] = useState(30);
  const [reconnectInterval, setReconnectInterval] = useState(60);

  const handleConfigUpdate = () => {
    updateConfig({
      discoveryInterval: discoveryInterval * 1000,
      reconnectInterval: reconnectInterval * 1000
    });
  };

  const getStatusIcon = () => {
    if (error) return <XCircle className="h-4 w-4 text-red-500" />;
    if (isSyncing) return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
    if (isConnected) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (isRunning) return <Search className="h-4 w-4 text-yellow-500 animate-pulse" />;
    return <WifiOff className="h-4 w-4 text-gray-500" />;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isSyncing) return 'Syncing';
    if (isConnected) return 'Connected';
    if (isRunning) return 'Discovering';
    return 'Stopped';
  };

  const getStatusColor = () => {
    if (error) return 'bg-red-100 text-red-800';
    if (isSyncing) return 'bg-blue-100 text-blue-800';
    if (isConnected) return 'bg-green-100 text-green-800';
    if (isRunning) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                Autonomous Sync
              </CardTitle>
              <CardDescription>
                Automatic background synchronization with CouchDB servers
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <Badge className={getStatusColor()}>
                {getStatusText()}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {discoveredServers}
              </div>
              <div className="text-sm text-gray-600">Servers Found</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {connectedServers}
              </div>
              <div className="text-sm text-gray-600">Connected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {status.syncStatus.docsReceived}
              </div>
              <div className="text-sm text-gray-600">Docs Received</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {status.syncStatus.docsSent}
              </div>
              <div className="text-sm text-gray-600">Docs Sent</div>
            </div>
          </div>

          {/* Last Discovery */}
          {lastDiscovery && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              <span>Last discovery: {lastDiscovery.toLocaleTimeString()}</span>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex gap-2">
            {isRunning ? (
              <Button onClick={stop} variant="outline" size="sm">
                <Pause className="h-4 w-4 mr-2" />
                Stop
              </Button>
            ) : (
              <Button onClick={start} size="sm">
                <Play className="h-4 w-4 mr-2" />
                Start
              </Button>
            )}
            
            <Button onClick={discover} variant="outline" size="sm" disabled={status.isDiscovering}>
              {status.isDiscovering ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Discover
            </Button>

            <Button
              onClick={() => setShowSettings(!showSettings)}
              variant="outline"
              size="sm"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Discovered Servers */}
      {status.discoveredServers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Discovered Servers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {status.discoveredServers.map((server, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center gap-2">
                    <Server className="h-4 w-4 text-blue-500" />
                    <span className="font-mono text-sm">{server.ip}:{server.port}</span>
                  </div>
                  <Badge variant={status.connectedServers.some(c => c.url === server.url) ? "default" : "secondary"}>
                    {status.connectedServers.some(c => c.url === server.url) ? "Connected" : "Available"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Failed Servers */}
      {status.failedServers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm text-red-600">Failed Connections</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {status.failedServers.map((failed, index) => (
                <div key={index} className="flex items-center justify-between p-2 border border-red-200 rounded">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="font-mono text-sm">{failed.server.ip}:{failed.server.port}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-red-600">
                      {failed.attempts} attempts
                    </div>
                    <div className="text-xs text-gray-500">
                      {failed.lastAttempt.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Settings Panel */}
      <Collapsible open={showSettings} onOpenChange={setShowSettings}>
        <CollapsibleContent>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="discovery-interval">Discovery Interval (seconds)</Label>
                  <Input
                    id="discovery-interval"
                    type="number"
                    value={discoveryInterval}
                    onChange={(e) => setDiscoveryInterval(Number(e.target.value))}
                    min={10}
                    max={300}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reconnect-interval">Reconnect Interval (seconds)</Label>
                  <Input
                    id="reconnect-interval"
                    type="number"
                    value={reconnectInterval}
                    onChange={(e) => setReconnectInterval(Number(e.target.value))}
                    min={30}
                    max={600}
                  />
                </div>
              </div>
              <Button onClick={handleConfigUpdate} size="sm">
                Update Configuration
              </Button>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}