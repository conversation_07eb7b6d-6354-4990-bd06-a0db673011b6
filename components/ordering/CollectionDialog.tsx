"use client";

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Package, X } from "lucide-react";
import EnhancedCollectionTable from '@/app/components/caisse/EnhancedCollectionTable';

interface CollectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function CollectionDialog({ open, onOpenChange }: CollectionDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-primary" />
              <DialogTitle>Collectes Livraisons</DialogTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription>
            Validation des collectes d'argent des livreurs
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-auto">
          <EnhancedCollectionTable />
        </div>
      </DialogContent>
    </Dialog>
  );
}