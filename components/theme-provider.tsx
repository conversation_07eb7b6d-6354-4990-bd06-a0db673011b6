"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  themes: Theme[];
  resolvedTheme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  attribute?: 'class' | 'data-theme';
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
  enableColorScheme?: boolean;
  nonce?: string;
}

export function ThemeProvider({
  children,
  defaultTheme = 'light',
  storageKey = 'theme',
  attribute = 'class',
  enableSystem = false,
  disableTransitionOnChange = false,
  enableColorScheme = true,
  ...props
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [mounted, setMounted] = useState(false);

  const applyTheme = React.useCallback((newTheme: Theme) => {
    if (typeof document === 'undefined') return;
    
    const root = document.documentElement;
    
    if (attribute === 'class') {
      // Remove old theme classes
      root.classList.remove('light', 'dark');
      // Add new theme class
      root.classList.add(newTheme);
    } else {
      root.setAttribute(attribute, newTheme);
    }

    // Set color scheme if enabled
    if (enableColorScheme && root.style) {
      root.style.colorScheme = newTheme;
    }
  }, [attribute, enableColorScheme]);

  const setTheme = React.useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    applyTheme(newTheme);
    
    // Save to localStorage if available
    if (typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(storageKey, newTheme);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }
  }, [applyTheme, storageKey]);

  // Initialize theme on mount
  useEffect(() => {
    let savedTheme = defaultTheme;
    
    // Try to get saved theme from localStorage
    if (typeof localStorage !== 'undefined') {
      try {
        const stored = localStorage.getItem(storageKey);
        if (stored === 'light' || stored === 'dark') {
          savedTheme = stored;
        }
      } catch (error) {
        console.warn('Failed to read theme from localStorage:', error);
      }
    }
    
    setThemeState(savedTheme);
    applyTheme(savedTheme);
    setMounted(true);
  }, [defaultTheme, storageKey, applyTheme]);

  const contextValue: ThemeContextType = React.useMemo(
    () => ({
      theme,
      setTheme,
      themes: ['light', 'dark'],
      resolvedTheme: theme,
    }),
    [theme, setTheme]
  );

  // Don't render theme content until mounted to prevent hydration mismatch
  if (!mounted) {
    return <div style={{ display: 'contents' }}>{children}</div>;
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}