"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { autoPrintService, AutoPrintConfig } from '@/lib/services/auto-print-service';
import { useToast } from '@/components/ui/use-toast';

interface AutoPrintContextType {
  config: AutoPrintConfig;
  isReady: boolean;
  updateConfig: (config: Partial<AutoPrintConfig>) => void;
}

const AutoPrintContext = createContext<AutoPrintContextType | undefined>(undefined);

export function useAutoPrint() {
  const context = useContext(AutoPrintContext);
  if (context === undefined) {
    throw new Error('useAutoPrint must be used within an AutoPrintProvider');
  }
  return context;
}

interface AutoPrintProviderProps {
  children: React.ReactNode;
}

export function AutoPrintProvider({ children }: AutoPrintProviderProps) {
  const { toast } = useToast();
  const [config, setConfig] = useState<AutoPrintConfig>(autoPrintService.getConfig());
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Initialize the auto-print service
    autoPrintService.initialize();
    setIsReady(autoPrintService.isReady());
    setConfig(autoPrintService.getConfig());

    // Listen for auto-print events
    const handleAutoPrintSuccess = (event: CustomEvent) => {
      const { orderId, source } = event.detail;
      toast({
        title: "🖨️ Order Printed",
        description: `Order #${orderId.split('-').pop()} automatically printed to kitchen (${source})`,
        duration: 3000,
      });
    };

    const handleAutoPrintError = (event: CustomEvent) => {
      const { orderId, error, source } = event.detail;
      toast({
        title: "❌ Auto-Print Failed",
        description: `Failed to print order #${orderId.split('-').pop()}: ${error}`,
        variant: "destructive",
        duration: 5000,
      });
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('auto-print-success', handleAutoPrintSuccess as EventListener);
      window.addEventListener('auto-print-error', handleAutoPrintError as EventListener);
    }

    // Cleanup on unmount
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('auto-print-success', handleAutoPrintSuccess as EventListener);
        window.removeEventListener('auto-print-error', handleAutoPrintError as EventListener);
      }
      autoPrintService.destroy();
    };
  }, [toast]);

  const updateConfig = (newConfig: Partial<AutoPrintConfig>) => {
    autoPrintService.updateConfig(newConfig);
    setConfig(autoPrintService.getConfig());
    setIsReady(autoPrintService.isReady());
  };

  return (
    <AutoPrintContext.Provider value={{
      config,
      isReady,
      updateConfig
    }}>
      {children}
    </AutoPrintContext.Provider>
  );
}