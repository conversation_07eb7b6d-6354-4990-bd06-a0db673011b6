"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useSyncService } from "@/lib/hooks/useSyncService";
import { AlertCircle, CheckCircle, Database, RefreshCcw, Wifi, WifiOff } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "./ui/card";

export function CorsFixHelper() {
  const { syncState, dbName, testAndRepairConnection, fixCorsIssues, refreshSync } = useSyncService();
  const [isFixing, setIsFixing] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info' | null, text: string }>({ 
    type: null, 
    text: '' 
  });

  const handleConfigureCors = async () => {
    setIsFixing(true);
    setMessage({ type: 'info', text: 'Configuring CORS settings on the CouchDB server...' });
    
    try {
      const result = await fixCorsIssues();
      
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: 'CORS settings updated successfully! Remote sync should now work properly.' 
        });
      } else {
        setMessage({ 
          type: 'error', 
          text: `Failed to update CORS settings: ${result.message}` 
        });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: `Error fixing CORS: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    } finally {
      setIsFixing(false);
    }
  };

  const handleRepairConnection = async () => {
    setIsFixing(true);
    setMessage({ type: 'info', text: 'Testing and repairing database connection...' });
    
    try {
      const result = await testAndRepairConnection();
      
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: 'Connection tested and repaired successfully!' 
        });
      } else {
        setMessage({ 
          type: 'error', 
          text: `Connection repair failed: ${result.message}` 
        });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: `Error testing connection: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    } finally {
      setIsFixing(false);
    }
  };

  const handleRefreshStatus = async () => {
    setIsFixing(true);
    setMessage({ type: 'info', text: 'Refreshing sync status...' });
    
    try {
      await refreshSync();
      setMessage({ 
        type: 'success', 
        text: 'Sync status refreshed successfully!' 
      });
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: `Error refreshing status: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Database Connection
        </CardTitle>
        <CardDescription>
          Fix CORS and connection issues with the remote database
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="font-medium">Connection Status:</span>
            {syncState.status === 'synced' ? (
              <span className="flex items-center text-green-600">
                <CheckCircle className="h-4 w-4 mr-1" />
                Synced
              </span>
            ) : syncState.status === 'connected' ? (
              <span className="flex items-center text-blue-600">
                <Wifi className="h-4 w-4 mr-1" />
                Connected {syncState.pendingChanges ? `(Syncing...)` : ''}
              </span>
            ) : syncState.status === 'paused' ? (
              <span className="flex items-center text-amber-600">
                <Wifi className="h-4 w-4 mr-1" />
                Paused {syncState.pendingChanges ? `(${syncState.pendingChanges} pending)` : ''}
              </span>
            ) : (
              <span className="flex items-center text-orange-600">
                <WifiOff className="h-4 w-4 mr-1" />
                {syncState.status === 'error' ? 'Error' : 'Disconnected'}
              </span>
            )}
          </div>
          
          {dbName && (
            <div className="text-sm text-muted-foreground">
              Database: <span className="font-mono">{dbName}</span>
            </div>
          )}
        </div>
        
        {message.type && (
          <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
            {message.type === 'error' && <AlertCircle className="h-4 w-4" />}
            {message.type === 'success' && <CheckCircle className="h-4 w-4" />}
            {message.type === 'info' && <RefreshCcw className="h-4 w-4" />}
            <AlertTitle>
              {message.type === 'error' && 'Error'}
              {message.type === 'success' && 'Success'}
              {message.type === 'info' && 'Working...'}
            </AlertTitle>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}
        
        {syncState.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Sync Error</AlertTitle>
            <AlertDescription>
              {syncState.error.message}
              {syncState.error.reason && ` - ${syncState.error.reason}`}
              {syncState.error.status && ` (Status ${syncState.error.status})`}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between gap-2">
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleRepairConnection} 
            disabled={isFixing}
          >
            <RefreshCcw className="h-4 w-4 mr-2" />
            Test Connection
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleRefreshStatus} 
            disabled={isFixing}
          >
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
        </div>
        
        <Button 
          onClick={handleConfigureCors} 
          disabled={isFixing}
        >
          {isFixing ? (
            <>
              <RefreshCcw className="h-4 w-4 mr-2 animate-spin" />
              Fixing...
            </>
          ) : (
            <>
              <Database className="h-4 w-4 mr-2" />
              Fix CORS Issues
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
} 