'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Camera, CheckCircle2, QrCode, RefreshCw, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Capacitor } from '@capacitor/core';
import { CapacitorBarcodeScanner } from '@capacitor/barcode-scanner';
import { toast } from 'sonner';
import { useP2PSync } from '@/hooks/use-p2p-sync';

interface QRCodeScannerProps {
  onSuccess?: (data: string) => void;
  onClose?: () => void;
}

enum ScannerState {
  READY = 'ready',
  SCANNING = 'scanning',
  SUCCESS = 'success',
  ERROR = 'error',
}

export function QRCodeScanner({ onSuccess, onClose }: QRCodeScannerProps) {
  const [state, setState] = useState<ScannerState>(ScannerState.READY);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [scanResult, setScanResult] = useState<string>('');
  const { peers } = useP2PSync();

  // Cleanup when component unmounts
  useEffect(() => {
    return () => {
      if (state === ScannerState.SCANNING) {
        stopScan();
      }
    };
  }, [state]);

  const startScan = async () => {
    if (!Capacitor.isNativePlatform()) {
      setErrorMessage('QR scanning is only available on native platforms (Android/iOS)');
      setState(ScannerState.ERROR);
      return;
    }

    try {
      setState(ScannerState.SCANNING);
      setErrorMessage('');
      
      // Add body class for Ionic apps (to make background transparent)
      document.querySelector('body')?.classList.add('scanner-active');
      
      // Start scanning
      const result = await CapacitorBarcodeScanner.scanBarcode({
        hint: 0, // 0 is QR_CODE in Html5QrcodeSupportedFormats
      });
      
      // Reset background and body class
      document.querySelector('body')?.classList.remove('scanner-active');
      
      if (result.ScanResult) {
        setScanResult(result.ScanResult);
        setState(ScannerState.SUCCESS);
        
        try {
          // Assume the QR content is JSON with P2P connection details
          const connectionData = JSON.parse(result.ScanResult);
          
          if (onSuccess) {
            onSuccess(result.ScanResult);
          }
          
          toast.success('QR Code scanned successfully!');
        } catch (error) {
          console.error('Invalid QR code data format:', error);
          setErrorMessage('Invalid QR code format. Expected P2P connection data.');
          setState(ScannerState.ERROR);
        }
      } else {
        setErrorMessage('No content found in QR code');
        setState(ScannerState.ERROR);
      }
    } catch (error) {
      console.error('Scanning error:', error);
      document.querySelector('body')?.classList.remove('scanner-active');
      setErrorMessage(`Scanning failed: ${error}`);
      setState(ScannerState.ERROR);
    }
  };

  const stopScan = async () => {
    if (Capacitor.isNativePlatform()) {
      document.querySelector('body')?.classList.remove('scanner-active');
    }
    setState(ScannerState.READY);
  };

  const handleClose = () => {
    if (state === ScannerState.SCANNING) {
      stopScan();
    }
    if (onClose) {
      onClose();
    }
  };

  const renderContent = () => {
    switch (state) {
      case ScannerState.READY:
        return (
          <>
            <CardContent className="flex flex-col items-center justify-center p-6">
              <div className="bg-slate-100 h-64 w-64 rounded-lg flex items-center justify-center mb-4">
                <QrCode className="h-24 w-24 text-slate-400" />
              </div>
              <p className="text-sm text-slate-500 text-center">
                Scan a QR code to connect to a P2P device.
              </p>
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              <Button onClick={startScan} className="w-full" size="lg">
                <Camera className="mr-2 h-4 w-4" />
                Start Scanning
              </Button>
              <Button variant="outline" onClick={handleClose} className="w-full" size="lg">
                Cancel
              </Button>
            </CardFooter>
          </>
        );

      case ScannerState.SCANNING:
        return (
          <>
            {Capacitor.isNativePlatform() ? (
              <CardContent className="p-6 flex flex-col items-center">
                <p className="text-center text-sm text-slate-500 mb-8">
                  Position the QR code within the scanning area
                </p>
                <div className="scanner-overlay w-64 h-64 border-2 border-dashed rounded-lg border-primary flex items-center justify-center animate-pulse">
                  <QrCode className="h-16 w-16 text-primary opacity-50" />
                </div>
                <div className="mt-8">
                  <Button variant="outline" onClick={stopScan} size="lg">
                    Cancel Scan
                  </Button>
                </div>
              </CardContent>
            ) : (
              <CardContent className="p-6">
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertTitle>Not Supported</AlertTitle>
                  <AlertDescription>
                    QR scanning is only available on mobile devices.
                  </AlertDescription>
                </Alert>
                <div className="mt-4 flex justify-center">
                  <Button variant="outline" onClick={handleClose}>
                    Close
                  </Button>
                </div>
              </CardContent>
            )}
          </>
        );

      case ScannerState.SUCCESS:
        return (
          <>
            <CardContent className="p-6">
              <div className="flex flex-col items-center mb-4">
                <div className="rounded-full bg-green-100 p-3 mb-2">
                  <CheckCircle2 className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-green-700">Scan Successful!</h3>
              </div>
              
              <div className="bg-slate-50 p-3 rounded-md overflow-hidden mb-4">
                <p className="text-xs text-slate-600 break-all">
                  {scanResult || 'No content'}
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handleClose}>
                Close
              </Button>
              <Button onClick={startScan}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Scan Again
              </Button>
            </CardFooter>
          </>
        );

      case ScannerState.ERROR:
        return (
          <>
            <CardContent className="p-6">
              <Alert variant="destructive" className="mb-4">
                <XCircle className="h-4 w-4" />
                <AlertTitle>Scan Failed</AlertTitle>
                <AlertDescription>
                  {errorMessage || 'An unknown error occurred while scanning'}
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={handleClose}>
                Close
              </Button>
              <Button onClick={startScan}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </CardFooter>
          </>
        );
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <QrCode className="mr-2 h-5 w-5" />
          QR Code Scanner
        </CardTitle>
        <CardDescription>
          Scan a QR code to connect with another device
        </CardDescription>
      </CardHeader>
      {renderContent()}
    </Card>
  );
} 