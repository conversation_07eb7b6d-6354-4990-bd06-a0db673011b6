'use client'
import Link from 'next/link'
import { Logo } from './logo'
import { Menu, X } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import React from 'react'
import { cn } from '@/lib/utils'
import { ModeToggle } from '@/components/ui/mode-toggle'

export const HeroHeader = () => {
    const [menuState, setMenuState] = React.useState(false)
    const [isScrolled, setIsScrolled] = React.useState(false)

    React.useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 50)
        }
        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [])

    return (
        <header>
            <nav
                data-state={menuState && 'active'}
                className="fixed z-20 w-full px-2">
                <div className={cn(
                    'mx-auto mt-2 max-w-6xl px-6 transition-all duration-300 lg:px-12',
                    isScrolled && 'bg-background/60 max-w-4xl rounded-2xl border backdrop-blur-lg lg:px-5'
                )}>
                    <div className="relative flex items-center justify-between py-3 lg:py-4">
                        <div className="flex items-center">
                            <Link
                                href="/"
                                aria-label="home"
                                className="flex items-center">
                                <Logo />
                            </Link>
                        </div>

                        {/* Mobile menu button */}
                        <button
                            onClick={() => setMenuState(!menuState)}
                            aria-label={menuState ? 'Close Menu' : 'Open Menu'}
                            className="relative z-20 block p-2 lg:hidden">
                            <Menu className={cn("size-5 transition-all duration-200", menuState && "opacity-0 rotate-90 scale-0")} />
                            <X className={cn("absolute inset-0 m-auto size-5 transition-all duration-200", !menuState && "opacity-0 -rotate-90 scale-0")} />
                        </button>

                        {/* Desktop navigation */}
                        <div className="hidden lg:flex items-center space-x-6 rtl:space-x-reverse">
                            <div className="flex items-center gap-3">
                                <Button
                                    asChild
                                    variant="outline"
                                    size="sm"
                                    className="rounded-full">
                                    <Link href="/auth/signin">
                                        <span className="font-almarai font-bold">تسجيل الدخول</span>
                                    </Link>
                                </Button>
                                <ModeToggle />
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            {/* Mobile menu */}
            <div className={cn(
                "fixed inset-0 z-10 bg-background/95 backdrop-blur-md transition-all duration-300 flex flex-col items-center justify-center",
                menuState ? "opacity-100" : "opacity-0 pointer-events-none"
            )}>
                <div className="flex flex-col items-center space-y-6 p-8">
                    <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="w-full rounded-full">
                        <Link href="/auth/signin" onClick={() => setMenuState(false)}>
                            <span className="font-almarai font-bold">تسجيل الدخول</span>
                        </Link>
                    </Button>
                    <Button
                        asChild
                        size="lg"
                        className="w-full rounded-full">
                        <Link href="/auth/signin" onClick={() => setMenuState(false)}>
                            <span className="font-almarai font-bold">اشترك الآن</span>
                        </Link>
                    </Button>
                </div>
            </div>
        </header>
    )
}
