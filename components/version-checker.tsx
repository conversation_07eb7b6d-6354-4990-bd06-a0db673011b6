'use client';

import { useEffect, useState } from 'react';
import { APP_VERSION } from '@/lib/config/app-version';

/**
 * Component that checks if the app version has changed
 * If the version has changed, it will reload the page to ensure the latest version is used
 */
export function VersionChecker() {
  const [hasChecked, setHasChecked] = useState(false);
  
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;
    
    // Check if the app version has changed
    const checkVersion = () => {
      const storedVersion = localStorage.getItem('app_version');
      
      // If no stored version, store the current version
      if (!storedVersion) {
        localStorage.setItem('app_version', APP_VERSION);
        setHasChecked(true);
        return;
      }
      
      // If the version has changed, reload the page
      if (storedVersion !== APP_VERSION) {
        console.log(`App version changed from ${storedVersion} to ${APP_VERSION}, reloading...`);
        
        // Update the stored version
        localStorage.setItem('app_version', APP_VERSION);
        
        // Clear any caches if needed
        if ('caches' in window) {
          caches.keys().then(cacheNames => {
            cacheNames.forEach(cacheName => {
              caches.delete(cacheName);
            });
          });
        }
        
        // Reload the page
        window.location.reload();
      } else {
        setHasChecked(true);
      }
    };
    
    checkVersion();
  }, []);
  
  // This component doesn't render anything
  return null;
}
