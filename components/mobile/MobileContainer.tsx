"use client";

import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  safeArea?: boolean;
}

export function MobileContainer({ 
  children, 
  className, 
  padding = 'md',
  safeArea = true 
}: MobileContainerProps) {
  const isMobile = useIsMobile();
  
  const paddingClasses = {
    none: '',
    sm: isMobile ? 'p-2' : 'p-4',
    md: isMobile ? 'p-4' : 'p-6',
    lg: isMobile ? 'p-6' : 'p-8',
  };

  return (
    <div
      className={cn(
        'w-full',
        paddingClasses[padding],
        safeArea && 'safe-x',
        className
      )}
    >
      {children}
    </div>
  );
}

interface MobileGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

export function MobileGrid({ 
  children, 
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}: MobileGridProps) {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
  };

  return (
    <div
      className={cn(
        'grid',
        cols.mobile && gridCols[cols.mobile as keyof typeof gridCols],
        cols.tablet && `tablet:${gridCols[cols.tablet as keyof typeof gridCols]}`,
        cols.desktop && `desktop:${gridCols[cols.desktop as keyof typeof gridCols]}`,
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

interface MobileButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  touchOptimized?: boolean;
}

export function MobileButton({ 
  children, 
  className,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  touchOptimized = true,
  ...props 
}: MobileButtonProps) {
  const isMobile = useIsMobile();
  
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
  };

  const sizeClasses = {
    sm: isMobile ? 'h-10 px-3 text-sm' : 'h-9 px-3 text-sm',
    md: isMobile ? 'h-12 px-4' : 'h-10 px-4',
    lg: isMobile ? 'h-14 px-6 text-lg' : 'h-11 px-8',
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        touchOptimized && isMobile && 'min-h-touch min-w-touch',
        variantClasses[variant],
        sizeClasses[size],
        fullWidth && 'w-full',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  shadow?: boolean;
}

export function MobileCard({ 
  children, 
  className,
  padding = 'md',
  shadow = true 
}: MobileCardProps) {
  const isMobile = useIsMobile();
  
  const paddingClasses = {
    sm: isMobile ? 'p-3' : 'p-4',
    md: isMobile ? 'p-4' : 'p-6',
    lg: isMobile ? 'p-6' : 'p-8',
  };

  return (
    <div
      className={cn(
        'rounded-lg border bg-card text-card-foreground',
        shadow && 'shadow-sm',
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </div>
  );
}

interface MobileStackProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg';
  direction?: 'vertical' | 'horizontal';
}

export function MobileStack({ 
  children, 
  className,
  spacing = 'md',
  direction = 'vertical' 
}: MobileStackProps) {
  const spacingClasses = {
    sm: direction === 'vertical' ? 'space-y-2' : 'space-x-2',
    md: direction === 'vertical' ? 'space-y-4' : 'space-x-4',
    lg: direction === 'vertical' ? 'space-y-6' : 'space-x-6',
  };

  return (
    <div
      className={cn(
        'flex',
        direction === 'vertical' ? 'flex-col' : 'flex-row items-center',
        spacingClasses[spacing],
        className
      )}
    >
      {children}
    </div>
  );
}
