"use client";

import { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface MobileFormWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export function MobileFormWrapper({ children, className }: MobileFormWrapperProps) {
  const formRef = useRef<HTMLDivElement>(null);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [activeInput, setActiveInput] = useState<HTMLElement | null>(null);

  useEffect(() => {
    let initialViewportHeight = window.visualViewport?.height || window.innerHeight;
    
    const handleViewportChange = () => {
      if (!window.visualViewport) return;
      
      const currentHeight = window.visualViewport.height;
      const heightDifference = initialViewportHeight - currentHeight;
      
      // Consider keyboard open if viewport height decreased by more than 150px
      const keyboardOpen = heightDifference > 150;
      setIsKeyboardOpen(keyboardOpen);
      
      // Scroll active input into view when keyboard opens
      if (keyboardOpen && activeInput) {
        setTimeout(() => {
          activeInput.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }, 100);
      }
    };

    const handleFocusIn = (e: FocusEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        setActiveInput(target);
        
        // Prevent zoom on iOS
        if (target instanceof HTMLInputElement) {
          const fontSize = window.getComputedStyle(target).fontSize;
          if (parseFloat(fontSize) < 16) {
            target.style.fontSize = '16px';
          }
        }
      }
    };

    const handleFocusOut = () => {
      setActiveInput(null);
    };

    // Visual Viewport API support
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
    } else {
      // Fallback for browsers without Visual Viewport API
      window.addEventListener('resize', handleViewportChange);
    }

    document.addEventListener('focusin', handleFocusIn);
    document.addEventListener('focusout', handleFocusOut);

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange);
      } else {
        window.removeEventListener('resize', handleViewportChange);
      }
      document.removeEventListener('focusin', handleFocusIn);
      document.removeEventListener('focusout', handleFocusOut);
    };
  }, [activeInput]);

  return (
    <div
      ref={formRef}
      className={cn(
        'transition-all duration-300 ease-in-out',
        isKeyboardOpen && 'keyboard-open',
        className
      )}
      style={{
        paddingBottom: isKeyboardOpen ? '20px' : '0px',
      }}
    >
      {children}
      
      {/* Mobile keyboard spacer */}
      {isKeyboardOpen && (
        <div className="h-4 flex-shrink-0" />
      )}
    </div>
  );
}

// CSS to be added to globals.css
export const mobileFormStyles = `
  /* Mobile form keyboard handling */
  @media (max-width: 768px) {
    .keyboard-open {
      transform: translateY(-10px);
    }
    
    /* Prevent zoom on input focus */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="tel"],
    textarea,
    select {
      font-size: 16px !important;
      transform-origin: left top;
    }
    
    /* Better focus styles for mobile */
    input:focus,
    textarea:focus {
      outline: 2px solid hsl(var(--primary));
      outline-offset: 2px;
    }
  }
`;
