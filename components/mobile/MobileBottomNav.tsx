"use client";

import { cn } from '@/lib/utils';
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { Button } from '@/components/ui/button';

interface MobileBottomNavProps {
  children: React.ReactNode;
  className?: string;
  fixed?: boolean;
  blur?: boolean;
}

export function MobileBottomNav({ 
  children, 
  className,
  fixed = true,
  blur = true 
}: MobileBottomNavProps) {
  const { isMobile, safeAreaInsets } = useMobileLayout();

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div
      className={cn(
        'w-full border-t bg-background/95',
        fixed && 'fixed bottom-0 left-0 right-0 z-50',
        blur && 'backdrop-blur supports-[backdrop-filter]:bg-background/60',
        className
      )}
      style={{
        paddingBottom: `max(${safeAreaInsets.bottom}px, 8px)`,
        paddingLeft: `${safeAreaInsets.left}px`,
        paddingRight: `${safeAreaInsets.right}px`,
      }}
    >
      <div className="px-4 py-2">
        {children}
      </div>
    </div>
  );
}

interface MobileBottomNavItemProps {
  children: React.ReactNode;
  onClick?: () => void;
  active?: boolean;
  disabled?: boolean;
  className?: string;
}

export function MobileBottomNavItem({ 
  children, 
  onClick,
  active = false,
  disabled = false,
  className 
}: MobileBottomNavItemProps) {
  return (
    <Button
      variant={active ? "default" : "ghost"}
      size="sm"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'flex-1 h-12 min-w-touch',
        'flex flex-col items-center justify-center',
        'text-xs font-medium',
        'transition-colors duration-200',
        active && 'bg-primary text-primary-foreground',
        className
      )}
    >
      {children}
    </Button>
  );
}

interface MobileFloatingActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  size?: 'sm' | 'md' | 'lg';
}

export function MobileFloatingActionButton({ 
  children, 
  onClick,
  className,
  position = 'bottom-right',
  size = 'md'
}: MobileFloatingActionButtonProps) {
  const { isMobile, safeAreaInsets } = useMobileLayout();

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
  };

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-14 w-14',
    lg: 'h-16 w-16',
  };

  return (
    <Button
      onClick={onClick}
      className={cn(
        'fixed z-50 rounded-full shadow-lg',
        'bg-primary text-primary-foreground',
        'hover:bg-primary/90',
        'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        positionClasses[position],
        sizeClasses[size],
        className
      )}
      style={{
        bottom: `calc(1rem + ${safeAreaInsets.bottom}px)`,
        right: position === 'bottom-right' ? `calc(1rem + ${safeAreaInsets.right}px)` : undefined,
        left: position === 'bottom-left' ? `calc(1rem + ${safeAreaInsets.left}px)` : undefined,
      }}
    >
      {children}
    </Button>
  );
}

interface MobileFixedHeaderProps {
  children: React.ReactNode;
  className?: string;
  blur?: boolean;
}

export function MobileFixedHeader({ 
  children, 
  className,
  blur = true 
}: MobileFixedHeaderProps) {
  const { safeAreaInsets } = useMobileLayout();

  return (
    <div
      className={cn(
        'fixed top-0 left-0 right-0 z-40',
        'w-full border-b bg-background/95',
        blur && 'backdrop-blur supports-[backdrop-filter]:bg-background/60',
        className
      )}
      style={{
        paddingTop: `${safeAreaInsets.top}px`,
        paddingLeft: `${safeAreaInsets.left}px`,
        paddingRight: `${safeAreaInsets.right}px`,
      }}
    >
      <div className="px-4 py-3">
        {children}
      </div>
    </div>
  );
}

interface MobileStickyContainerProps {
  children: React.ReactNode;
  className?: string;
  hasFixedHeader?: boolean;
  hasFixedBottom?: boolean;
}

export function MobileStickyContainer({ 
  children, 
  className,
  hasFixedHeader = false,
  hasFixedBottom = false 
}: MobileStickyContainerProps) {
  const { safeAreaInsets } = useMobileLayout();

  return (
    <div
      className={cn(
        'flex flex-col h-screen-mobile',
        className
      )}
      style={{
        paddingTop: hasFixedHeader ? `calc(4rem + ${safeAreaInsets.top}px)` : `${safeAreaInsets.top}px`,
        paddingBottom: hasFixedBottom ? `calc(4rem + ${safeAreaInsets.bottom}px)` : `${safeAreaInsets.bottom}px`,
        paddingLeft: `${safeAreaInsets.left}px`,
        paddingRight: `${safeAreaInsets.right}px`,
      }}
    >
      {children}
    </div>
  );
}

// Utility component for handling keyboard adjustments
interface MobileKeyboardAdjustProps {
  children: React.ReactNode;
  className?: string;
}

export function MobileKeyboardAdjust({ children, className }: MobileKeyboardAdjustProps) {
  const { isKeyboardOpen, viewportHeight } = useMobileLayout();

  return (
    <div
      className={cn(
        'transition-all duration-300 ease-in-out',
        className
      )}
      style={{
        height: isKeyboardOpen ? `${viewportHeight}px` : '100vh',
      }}
    >
      {children}
    </div>
  );
}
