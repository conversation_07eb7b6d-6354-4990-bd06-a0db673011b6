'use client';

import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useStaff } from "@/lib/hooks/use-staff";
import { useUnifiedDB } from "@/lib/context/unified-db-provider";
import { SyncStatusNew } from "@/components/SyncStatusNew";
import { getStaffMember } from "@/lib/db/v4";

// Using inline interface instead of importing from staff-service
interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: string;
  position?: string;
  status: 'ACTIVE' | 'INACTIVE';
  userId?: string;
  hasUserAccount?: boolean;
}

interface StaffWithPermissions extends StaffMember {
  fetchedPermissions?: {
    pages: {
      menu: boolean;
      orders: boolean;
      finance: boolean;
      inventory: boolean;
      staff: boolean;
      settings: boolean;
      suppliers: boolean;
    }
  };
  permissionError?: string;
}

export function StaffManagement() {
  const { loading: authLoading } = useAuth();
  const {
    staff,
    isLoading: staffLoading,
    error: staffError,
    refreshStaff,
  } = useStaff();

  // Create a state to hold staff with permissions
  const [staffWithPermissions, setStaffWithPermissions] = useState<StaffWithPermissions[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);

  // Get sync status from the restaurant DB
  const { status: restaurantStatus } = useUnifiedDB();

  // Create a syncState object in the format expected by SyncStatusNew
  const syncState = {
    status: restaurantStatus.status,
    error: restaurantStatus.error,
    lastSync: new Date().toISOString(),
    progress: restaurantStatus.progress
  };

  const [successMessage, setSuccessMessage] = useState("");
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [permissionsFetchError, setPermissionsFetchError] = useState<string | null>(null);

  const isLoading = staffLoading || authLoading || permissionsLoading;
  const error = staffError;

  // Function to fetch staff permissions directly from PouchDB
  const fetchStaffPermissions = useCallback(async () => {
    if (!staff || staff.length === 0) return;

    setPermissionsLoading(true);
    setPermissionsFetchError(null);

    try {
      // Create a copy of the staff array to add permissions to
      const updatedStaff = [...staff] as StaffWithPermissions[];

      // Only fetch permissions for staff members that have user accounts
      const staffWithAccounts = updatedStaff.filter(member => member.hasUserAccount);

      // Store debug info for display
      const debug = {
        totalStaff: updatedStaff.length,
        staffWithAccounts: staffWithAccounts.length,
        accountDetails: staffWithAccounts.map(s => ({
          id: s.id,
          name: s.name,
          userId: s.userId,
          hasUserAccount: s.hasUserAccount
        }))
      };
      setDebugInfo(debug);

      console.log('🧑‍💼 Staff with accounts:', debug.accountDetails);

      if (staffWithAccounts.length === 0) {
        setSuccessMessage("No staff members with user accounts found.");
        setStaffWithPermissions(updatedStaff);
        setPermissionsLoading(false);
        return;
      }

      // Fetch permissions for each staff member with a user account directly from PouchDB
      for (const member of staffWithAccounts) {
        try {
          // Clear previous error
          const index = updatedStaff.findIndex(s => s.id === member.id);
          if (index !== -1) {
            updatedStaff[index] = {
              ...updatedStaff[index],
              permissionError: undefined
            };
          }

          // Fetch permissions directly from PouchDB using the staff ID
          console.log(`🔍 Fetching permissions for ${member.name} (ID: ${member.id}) directly from PouchDB`);
          
          const staffDocument = await getStaffMember(member.id);
          
          if (staffDocument?.permissions) {
            console.log(`📋 Found permissions for ${member.name}:`, staffDocument.permissions);

            // Find this staff member in our array and update permissions
            if (index !== -1) {
              updatedStaff[index] = {
                ...updatedStaff[index],
                fetchedPermissions: {
                  pages: {
                    menu: !!staffDocument.permissions.pages?.menu,
                    orders: !!staffDocument.permissions.pages?.orders,
                    finance: !!staffDocument.permissions.pages?.finance,
                    inventory: !!staffDocument.permissions.pages?.inventory,
                    staff: !!staffDocument.permissions.pages?.staff,
                    settings: !!staffDocument.permissions.pages?.settings,
                    suppliers: !!staffDocument.permissions.pages?.suppliers,
                  }
                }
              };
            }
          } else {
            console.log(`📋 No permissions found for ${member.name} in PouchDB`);
            // Set default empty permissions
            if (index !== -1) {
              updatedStaff[index] = {
                ...updatedStaff[index],
                fetchedPermissions: {
                  pages: {
                    menu: false,
                    orders: false,
                    finance: false,
                    inventory: false,
                    staff: false,
                    settings: false,
                    suppliers: false,
                  }
                }
              };
            }
          }
        } catch (error) {
          console.error(`Error fetching permissions for staff ${member.id}:`, error);

          // Store the error with the staff member
          const index = updatedStaff.findIndex(s => s.id === member.id);
          if (index !== -1) {
            updatedStaff[index] = {
              ...updatedStaff[index],
              permissionError: error instanceof Error ? error.message : String(error)
            };
          }
        }
      }

      setStaffWithPermissions(updatedStaff);
      setSuccessMessage("Staff permissions loaded successfully from PouchDB (client-side)");
    } catch (error) {
      console.error('Error fetching staff permissions:', error);
      setPermissionsFetchError(error instanceof Error ? error.message : String(error));
    } finally {
      setPermissionsLoading(false);
    }
  }, [staff]);

  // Fetch permissions when staff data is loaded
  useEffect(() => {
    if (staff && staff.length > 0 && !staffLoading) {
      fetchStaffPermissions();
    }
  }, [staff, staffLoading, fetchStaffPermissions]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <h2 className="text-lg font-medium mb-2">Loading Staff Data</h2>
        <p className="text-muted-foreground">Please wait...</p>
      </div>
    );
  }

  // Show error state with a more user-friendly message
  if (error) {
    return (
      <div className="space-y-6">
        <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div>
            <p className="font-medium">Error Loading Staff Data</p>
            <p className="text-sm">{error.message}</p>
            <button
              onClick={() => refreshStaff()}
              className="text-sm underline mt-2"
            >
              Try refreshing the data
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {successMessage && (
        <div className="p-3 mb-4 bg-green-100 border border-green-400 text-green-700 rounded">
          {successMessage}
        </div>
      )}

      {permissionsFetchError && (
        <div className="p-3 mb-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <p className="font-medium">Error fetching permissions:</p>
          <p className="text-sm">{permissionsFetchError}</p>
          <button
            onClick={fetchStaffPermissions}
            className="mt-2 px-4 py-1 text-sm bg-white border border-red-400 text-red-700 rounded hover:bg-red-50"
          >
            Retry loading permissions
          </button>
        </div>
      )}

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Staff Management</h2>
        <div className="flex space-x-2 items-center">
          <button
            onClick={fetchStaffPermissions}
            disabled={permissionsLoading}
            className="mr-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:bg-blue-300"
          >
            {permissionsLoading ? 'Refreshing...' : 'Refresh Permissions'}
          </button>
          <SyncStatusNew syncState={syncState} />
        </div>
      </div>

      {/* Debug information */}
      {debugInfo && (
        <div className="bg-blue-50 p-4 rounded-lg mb-4 text-sm text-blue-800">
          <h3 className="font-bold mb-2">Debug Info (Client-side PouchDB):</h3>
          <p>Total Staff: {debugInfo.totalStaff}</p>
          <p>Staff with Accounts: {debugInfo.staffWithAccounts}</p>
          {debugInfo.accountDetails?.length > 0 && (
            <div className="mt-2">
              <p className="font-semibold">Staff with user accounts:</p>
              <ul className="list-disc list-inside">
                {debugInfo.accountDetails.map((s: any) => (
                  <li key={s.id}>
                    {s.name} (ID: {s.id}, username: {s.username || s.name || 'missing'})
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <h2 className="text-lg font-medium mb-4">Staff Members</h2>

          {staffWithPermissions.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              <p>No staff members found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Permissions (PouchDB)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {staffWithPermissions.map((member: StaffWithPermissions) => (
                    <tr key={member.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{member.name}</div>
                        <div className="text-xs text-gray-500">ID: {member.id}</div>
                        {((member as any).username || member.name) && (
                          <div className="text-xs text-gray-500">Username: {(member as any).username || member.name}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{member.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{member.role}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{member.position || 'N/A'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          member.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {member.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {member.hasUserAccount ? (
                          <div className="space-y-1">
                            {member.permissionError ? (
                              <div className="text-xs text-red-500">
                                Error: {member.permissionError}
                              </div>
                            ) : member.fetchedPermissions ? (
                              <div className="text-xs space-x-1 flex flex-wrap">
                                {Object.entries(member.fetchedPermissions.pages).map(([page, enabled]) => (
                                  <span key={page} className={`px-2 py-1 rounded-lg m-1 ${
                                    enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-500'
                                  }`}>
                                    {page}
                                  </span>
                                ))}
                              </div>
                            ) : (
                              <div className="text-sm text-gray-500">Loading permissions...</div>
                            )}
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">No user account</div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <p className="text-sm text-gray-600 italic">
          ✅ Permissions are now loaded directly from PouchDB client-side (no API routes needed). 
          Staff credential management is not yet implemented. This feature will be added after further discussions about implementation approach.
        </p>
      </div>
    </div>
  );
}