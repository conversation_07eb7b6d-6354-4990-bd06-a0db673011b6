"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { Staff, DeliveryStaff, Shift } from "@/types/staff";
import { ShiftConfig, AttendanceRecord } from "@/lib/types/staff";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Clock, Calendar as CalendarIcon, PlusCircle, Pencil, Trash2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON><PERSON>it<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  DialogFooter,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { staffService } from "@/lib/services/staff-service";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useDatabase } from "@/lib/hooks/useDatabase";
import { UserIcon } from "lucide-react";
import { AttendanceCalendarAndList } from "./AttendanceCalendarAndList";

interface StaffPresenceProps {
  staffList: Staff[];
  deliveryStaffList: DeliveryStaff[];
  onShiftCreate: (shift: Shift) => void;
}

interface ShiftFormData {
  name: string;
  startTime: string;
  duration: number;
}

const SHIFT_COLORS = [
  "bg-blue-500/10 text-blue-500 border-blue-200",
  "bg-orange-500/10 text-orange-500 border-orange-200",
  "bg-purple-500/10 text-purple-500 border-purple-200",
  "bg-green-500/10 text-green-500 border-green-200",
  "bg-red-500/10 text-red-500 border-red-200",
  "bg-yellow-500/10 text-yellow-500 border-yellow-200",
];

// Helper function to convert ShiftConfig to Shift
const convertToShift = (shiftConfig: ShiftConfig): Shift => {
  return {
    ...shiftConfig,
    duration: shiftConfig.endTime 
      ? calculateDuration(shiftConfig.startTime, shiftConfig.endTime) 
      : 8, // Default duration
    isActive: true, // Default active state
  };
};

// Helper to calculate duration between start and end time
const calculateDuration = (startTime: string, endTime: string): number => {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  // Simple calculation (doesn't handle midnight crossing)
  const durationHours = endHour - startHour;
  const durationMinutes = (endMinute - startMinute) / 60;
  
  return Math.max(0, durationHours + durationMinutes);
};

const calculateEndTime = (startTime: string, durationHours: number): string => {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const totalMinutes = startHour * 60 + startMinute + durationHours * 60;
  const endHour = Math.floor(totalMinutes / 60) % 24; // Ensure it wraps around 24 hours
  const endMinute = totalMinutes % 60;
  return `${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}`;
};

export function StaffPresence({ staffList, deliveryStaffList, onShiftCreate }: StaffPresenceProps) {
  const [date, setDate] = useState<Date>(new Date());
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [selectedShift, setSelectedShift] = useState<Shift | null>(null);
  const [selectedStaff, setSelectedStaff] = useState<Staff | DeliveryStaff | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isAddShiftOpen, setIsAddShiftOpen] = useState(false);
  const [isEditShiftOpen, setIsEditShiftOpen] = useState(false);
  const [editingShift, setEditingShift] = useState<Shift | null>(null);
  const [newShift, setNewShift] = useState<ShiftFormData>({
    name: "",
    startTime: "",
    duration: 8,
  });
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();
  const { isInitialized } = useDatabase();

  // Load shifts
  useEffect(() => {
    const loadShifts = async () => {
      if (!isInitialized || !isAuthenticated) return;

      try {
        const allShiftConfigs = await staffService.getAllShifts();
        // Convert ShiftConfig[] to Shift[]
        const allShifts = allShiftConfigs.map(convertToShift);
        setShifts(allShifts);
        if (allShifts.length > 0 && !selectedShift) {
          setSelectedShift(allShifts[0]);
        }
      } catch (error) {
        console.error('Error loading shifts:', error);
        toast({
          title: "Error",
          description: "Failed to load shifts",
          variant: "destructive",
        });
      }
    };

    loadShifts();
  }, [isInitialized, isAuthenticated]);

  const handleAddShift = async () => {
    if (!isInitialized || !isAuthenticated) return;

    try {
      // Create new shift with missing fields for ShiftConfig
      const shiftData = {
        name: newShift.name,
        startTime: newShift.startTime,
        endTime: calculateEndTime(newShift.startTime, newShift.duration)
      };
      
      const createdShiftConfig = await staffService.createShift(shiftData);
      // Convert the created ShiftConfig to Shift before adding to state
      const createdShift = convertToShift(createdShiftConfig);
      
      setShifts(prev => [...prev, createdShift]);
      setIsAddShiftOpen(false);
      setNewShift({ name: "", startTime: "", duration: 8 });
      
      toast({
        title: "Success",
        description: "Shift created successfully",
      });
    } catch (error) {
      console.error('Error creating shift:', error);
      toast({
        title: "Error",
        description: "Failed to create shift",
        variant: "destructive",
      });
    }
  };

  const handleEditShift = async () => {
    if (!isInitialized || !isAuthenticated || !editingShift) return;

    try {
      // Prepare update data compatible with ShiftConfig
      const updateData = {
        name: newShift.name,
        startTime: newShift.startTime,
        endTime: calculateEndTime(newShift.startTime, newShift.duration)
      };
      
      const updatedShiftConfig = await staffService.updateShift(editingShift.id, updateData);
      // Convert the updated ShiftConfig to Shift before updating state
      const updatedShift = convertToShift(updatedShiftConfig);
      
      setShifts(prev => prev.map(shift => shift.id === editingShift.id ? updatedShift : shift));
      setIsEditShiftOpen(false);
      setEditingShift(null);
      setNewShift({ name: "", startTime: "", duration: 8 });
      
      toast({
        title: "Success",
        description: "Shift updated successfully",
      });
    } catch (error) {
      console.error('Error updating shift:', error);
      toast({
        title: "Error",
        description: "Failed to update shift",
        variant: "destructive",
      });
    }
  };

  const handleDeleteShift = async (shiftId: string) => {
    if (!isInitialized || !isAuthenticated) return;

    try {
      await staffService.deleteShift(shiftId);
      setShifts(prev => prev.filter(shift => shift.id !== shiftId));
      toast({
        title: "Success",
        description: "Shift deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting shift:', error);
      toast({
        title: "Error",
        description: "Failed to delete shift",
        variant: "destructive",
      });
    }
  };

  const openEditShift = (shift: Shift) => {
    setEditingShift(shift);
    setNewShift({ name: shift.name, startTime: shift.startTime, duration: shift.duration });
    setIsEditShiftOpen(true);
  };

  const getStaffById = (id: string) => {
    return [...staffList, ...deliveryStaffList].find(staff => staff.id === id);
  };

  const handleCheckIn = async () => {
    if (!selectedStaff || !selectedShift) {
      toast({
        title: "Error",
        description: "Please select a staff member and a shift.",
        variant: "destructive",
      });
      return;
    }
    try {
      const today = format(new Date(), 'yyyy-MM-dd');
      await staffService.recordShiftAttendance(
        selectedStaff.id,
        selectedShift.id,
        {
          date: today,
          status: "present",
          shiftName: selectedShift.name,
          notes: "Checked in via system",
        }
      );
      setRefreshTrigger(prev => prev + 1);
      toast({
        title: "Success",
        description: `${selectedStaff.name} checked in for ${selectedShift.name}.`,
      });
    } catch (error) {
      console.error('Error checking in:', error);
      toast({
        title: "Error",
        description: "Failed to check in staff member.",
        variant: "destructive",
      });
    }
  };

  const toggleAttendance = async (recordId: string, currentAttendedStatus: boolean) => {
    if (!isAuthenticated || !isInitialized) return;
  
    try {
      const [staffId, dateStr, shiftId] = recordId.split('_');
  
      let newStatus: "present" | "late" | "absent";
      if (currentAttendedStatus) {
        newStatus = "absent";
      } else {
        newStatus = "present";
      }
  
      await staffService.recordShiftAttendance(
        staffId,
        shiftId,
        {
          date: dateStr,
          status: newStatus,
          notes: `Status updated to ${newStatus} by system.`,
        }
      );
      setRefreshTrigger(prev => prev + 1);
      toast({
        title: "Success",
        description: `Attendance updated to ${newStatus}.`,
      });
    } catch (error) {
      console.error('Error toggling attendance:', error);
      toast({
        title: "Error",
        description: "Failed to update attendance.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-bold flex items-center">
            <UserIcon className="mr-2 h-5 w-5" /> Staff Presence Management
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="px-3 py-1 text-sm">
              <CalendarIcon className="mr-1 h-3 w-3" /> {format(date, "PPP")}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="staffSelect">Select Staff Member</Label>
              <Select
                value={selectedStaff?.id || ""}
                onValueChange={(value) => setSelectedStaff(getStaffById(value) || null)}
              >
                <SelectTrigger id="staffSelect">
                  <SelectValue placeholder="Select a staff member" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Staff</SelectLabel>
                    {staffList.map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel>Delivery Staff</SelectLabel>
                    {deliveryStaffList.map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col space-y-2">
              <Label htmlFor="shiftSelect">Select Shift</Label>
              <Select
                value={selectedShift?.id || ""}
                onValueChange={(value) => setSelectedShift(shifts.find(s => s.id === value) || null)}
              >
                <SelectTrigger id="shiftSelect">
                  <SelectValue placeholder="Select a shift" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Available Shifts</SelectLabel>
                    {shifts.map((shift) => (
                      <SelectItem key={shift.id} value={shift.id}>
                        {shift.name} ({shift.startTime} - {shift.endTime})
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button onClick={handleCheckIn} className="w-full">
                <Clock className="mr-2 h-4 w-4" /> Check In
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-bold flex items-center">
            <CalendarIcon className="mr-2 h-5 w-5" /> Attendance Overview
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Dialog open={isAddShiftOpen} onOpenChange={setIsAddShiftOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <PlusCircle className="mr-2 h-4 w-4" /> Add Shift
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add New Shift</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="shiftName" className="text-right">
                      Shift Name
                    </Label>
                    <Input
                      id="shiftName"
                      value={newShift.name}
                      onChange={(e) => setNewShift({ ...newShift, name: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="startTime" className="text-right">
                      Start Time
                    </Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={newShift.startTime}
                      onChange={(e) => setNewShift({ ...newShift, startTime: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="duration" className="text-right">
                      Duration (hours)
                    </Label>
                    <Input
                      id="duration"
                      type="number"
                      value={newShift.duration}
                      onChange={(e) => setNewShift({ ...newShift, duration: parseFloat(e.target.value) || 0 })}
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" onClick={handleAddShift}>
                    Add Shift
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isEditShiftOpen} onOpenChange={setIsEditShiftOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="hidden">
                  Edit Shift
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Edit Shift</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="editShiftName" className="text-right">
                      Shift Name
                    </Label>
                    <Input
                      id="editShiftName"
                      value={newShift.name}
                      onChange={(e) => setNewShift({ ...newShift, name: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="editStartTime" className="text-right">
                      Start Time
                    </Label>
                    <Input
                      id="editStartTime"
                      type="time"
                      value={newShift.startTime}
                      onChange={(e) => setNewShift({ ...newShift, startTime: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="editDuration" className="text-right">
                      Duration (hours)
                    </Label>
                    <Input
                      id="editDuration"
                      type="number"
                      value={newShift.duration}
                      onChange={(e) => setNewShift({ ...newShift, duration: parseFloat(e.target.value) || 0 })}
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" onClick={handleEditShift}>
                    Save Changes
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Manage Shifts</h3>
            <ScrollArea className="h-48 w-full rounded-md border p-4">
              {shifts.length > 0 ? (
                <ul className="space-y-2">
                  {shifts.map((shift, index) => (
                    <li
                      key={shift.id}
                      className={cn(
                        "flex items-center justify-between p-2 rounded-md",
                        SHIFT_COLORS[index % SHIFT_COLORS.length]
                      )}
                    >
                      <div>
                        <span className="font-medium">{shift.name}</span> (
                        {shift.startTime} - {shift.endTime}) {shift.duration}h
                      </div>
                      <div className="space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditShift(shift)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteShift(shift.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-center text-muted-foreground">
                  No shifts configured yet.
                </div>
              )}
            </ScrollArea>
          </div>
          <AttendanceCalendarAndList
            selectedStaff={selectedStaff}
            shifts={shifts}
            refreshTrigger={refreshTrigger}
            onToggleAttendance={toggleAttendance}
            onRefresh={() => setRefreshTrigger(prev => prev + 1)}
          />
        </CardContent>
      </Card>
    </div>
  );
} 