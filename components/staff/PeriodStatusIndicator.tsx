'use client';

import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle2, AlertCircle, Clock } from 'lucide-react';
import { PeriodStatus } from '@/lib/services/staff-payment-service';

interface PeriodStatusIndicatorProps {
  periodStatus: PeriodStatus;
  className?: string;
}

export function PeriodStatusIndicator({ periodStatus, className = "" }: PeriodStatusIndicatorProps) {
  const { paidPeriods, duePeriods, nextDue } = periodStatus;

  return (
    <Card className={`border-muted ${className}`}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-3.5 w-3.5 text-emerald-500" />
            <span className="text-muted-foreground">Payé:</span>
            <span className="font-medium">
              {paidPeriods.length > 0 
                ? paidPeriods.slice(-3).join(', ') + (paidPeriods.length > 3 ? '...' : '')
                : 'Aucun'
              }
            </span>
          </div>
          
          {duePeriods.length > 0 && (
            <div className="flex items-center gap-2">
              <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
              <span className="text-muted-foreground">En retard:</span>
              <Badge variant="destructive" className="text-xs">
                {duePeriods.length}
              </Badge>
            </div>
          )}
          
          {nextDue && (
            <div className="flex items-center gap-2">
              <Clock className="h-3.5 w-3.5 text-blue-500" />
              <span className="text-muted-foreground">Prochain:</span>
              <span className="font-medium">{nextDue}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 