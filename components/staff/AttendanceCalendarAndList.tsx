"use client";

import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  format, 
  isSameDay, 
  startOfWeek, 
  addDays, 
  eachDayOfInterval, 
  subDays, 
  isToday,
  startOfMonth,
  endOfMonth,
  isSameMonth,
  subMonths,
  addMonths,
  endOfWeek,
  subMonths as dateFnsSubMonths,
  addMonths as dateFnsAddMonths,
} from "date-fns";
import { Staff, DeliveryStaff, Shift } from "@/types/staff";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON>, Users, CheckSquare, Square, ChevronLeft, ChevronRight, CalendarIcon, RotateCcw, DollarSign } from "lucide-react";
import { staffService } from "@/lib/services/staff-service";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useDatabase } from "@/lib/hooks/useDatabase";
import { useToast } from "@/components/ui/use-toast";
import { AttendanceRecord } from "@/lib/types/staff";
import { Checkbox } from "@/components/ui/checkbox";

interface StaffPresenceRecord {
  id: string;
  staffId: string;
  staffName: string;
  status: "present" | "late" | "absent";
  shift: Shift;
  notes?: string;
  attended: boolean;
  date: string;
  isPaid?: boolean;
  rate?: number;
}

interface AttendanceCalendarAndListProps {
  selectedStaff: Staff | DeliveryStaff | null;
  shifts: Shift[];
  refreshTrigger: number;
  onToggleAttendance: (recordId: string, currentStatus: boolean) => Promise<void>;
  onRefresh: () => void;
  showPaymentFeatures?: boolean;
  onShiftSelect?: (shifts: StaffPresenceRecord[]) => void;
  selectedShifts?: string[];
  showPaidShifts?: boolean;
  enableBulkSelection?: boolean;
  viewMode?: 'calendar' | 'list';
}

export function AttendanceCalendarAndList({
  selectedStaff,
  shifts,
  refreshTrigger,
  onToggleAttendance,
  onRefresh,
  onShiftSelect,
  selectedShifts = [],
  showPaidShifts = true,
  enableBulkSelection = false,
  viewMode: externalViewMode,
}: AttendanceCalendarAndListProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [presenceRecords, setPresenceRecords] = useState<StaffPresenceRecord[]>([]);
  const [selectedRecords, setSelectedRecords] = useState<string[]>(selectedShifts);
  const { isInitialized } = useDatabase();
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState<string>(externalViewMode || "calendar");

  useEffect(() => {
    setSelectedRecords(selectedShifts);
  }, [selectedShifts]);

  useEffect(() => {
    if (externalViewMode) {
      setActiveTab(externalViewMode);
    }
  }, [externalViewMode]);

  useEffect(() => {
    const loadPresenceRecords = async () => {
      if (!isInitialized || !isAuthenticated || !selectedStaff) {
        setPresenceRecords([]);
        return;
      }

      try {
        let startDate, endDate;

        if (activeTab === "calendar") {
          startDate = startOfMonth(currentMonth);
          endDate = endOfMonth(currentMonth);
        } else {
          startDate = dateFnsSubMonths(new Date(), 3);
          endDate = new Date();
        }
        
        const startDateStr = format(startDate, "yyyy-MM-dd");
        const endDateStr = format(endDate, "yyyy-MM-dd");
        
        const fetchedRecords = await staffService.getStaffAttendance(selectedStaff.id, startDateStr, endDateStr);
        
        const records: StaffPresenceRecord[] = [];
        
        if (fetchedRecords && fetchedRecords.length > 0) {
          fetchedRecords.forEach((r: AttendanceRecord) => {
            const shift = shifts.find((s) => s.id === r.shiftId);
            if (shift) {
              const record: StaffPresenceRecord = {
                id: r.id, // ← USE THE REAL ATTENDANCE RECORD ID!
                staffId: selectedStaff.id,
                staffName: selectedStaff.name,
                status: r.status,
                shift,
                notes: r.notes || "",
                attended: r.status === "present" || r.status === "late",
                date: r.date,
                isPaid: r.isPaid || false,
                rate: (selectedStaff.paymentConfig as any)?.shiftRates?.[r.shiftId] || selectedStaff.paymentConfig?.shiftRate || 0,
              };
              
              if (showPaidShifts || !record.isPaid) {
                records.push(record);
              }
            }
          });
        }
        setPresenceRecords(records);
      } catch (e) {
        if (!(e as any)?.status || (e as any).status !== 404) {
          toast({
            title: "Error",
            description: "Failed to load presence records",
            variant: "destructive",
          });
        }
        setPresenceRecords([]);
      }
    };
    loadPresenceRecords();
  }, [currentMonth, activeTab, shifts, refreshTrigger, selectedStaff, isInitialized, isAuthenticated, showPaidShifts]);

  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 1 });
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 1 });

    return eachDayOfInterval({ start: startDate, end: endDate });
  }, [currentMonth]);
  
  const dayRecords = useMemo(() => 
    presenceRecords.filter((record) => isSameDay(new Date(record.date), selectedDate)),
    [presenceRecords, selectedDate]
  );

  const handleRecordSelection = (recordId: string, selected: boolean) => {
    if (!enableBulkSelection) return;
    
    let newSelection;
    if (selected) {
      newSelection = [...selectedRecords, recordId];
    } else {
      newSelection = selectedRecords.filter(id => id !== recordId);
    }
    
    setSelectedRecords(newSelection);
    
    if (onShiftSelect) {
      const selectedRecordObjects = presenceRecords.filter(r => newSelection.includes(r.id));
      onShiftSelect(selectedRecordObjects);
    }
  };

  const handleSelectAllInList = () => {
    if (!enableBulkSelection) return;
    
    const unpaidRecords = presenceRecords.filter(r => !r.isPaid);
    const allIds = unpaidRecords.map(r => r.id);
    const allSelected = allIds.every(id => selectedRecords.includes(id));
    
    let newSelection;
    if (allSelected) {
      newSelection = selectedRecords.filter(id => !allIds.includes(id));
    } else {
      newSelection = [...new Set([...selectedRecords, ...allIds])];
    }
    
    setSelectedRecords(newSelection);
    
    if (onShiftSelect) {
      const selectedRecordObjects = presenceRecords.filter(r => newSelection.includes(r.id));
      onShiftSelect(selectedRecordObjects);
    }
  };

  const handleSelectAllOnDay = () => {
    if (!enableBulkSelection) return;
    
    const unpaidRecordsOnDay = dayRecords.filter(r => !r.isPaid);
    const allIdsOnDay = unpaidRecordsOnDay.map(r => r.id);
    const allSelectedOnDay = allIdsOnDay.every(id => selectedRecords.includes(id));
    
    let newSelection;
    if (allSelectedOnDay) {
      newSelection = selectedRecords.filter(id => !allIdsOnDay.includes(id));
    } else {
      newSelection = [...new Set([...selectedRecords, ...allIdsOnDay])];
    }
    
    setSelectedRecords(newSelection);
    
    if (onShiftSelect) {
      const selectedRecordObjects = presenceRecords.filter(r => newSelection.includes(r.id));
      onShiftSelect(selectedRecordObjects);
    }
  };

  if (!selectedStaff) {
    return (
      <div className="flex items-center justify-center h-20 border border-dashed rounded-md bg-muted/20">
        <div className="flex flex-col items-center gap-1">
          <Users className="h-4 w-4 text-muted-foreground" />
          <p className="text-xs text-muted-foreground">Select staff to view attendance</p>
        </div>
      </div>
    );
  }

  const unpaidRecords = presenceRecords.filter(r => !r.isPaid);
  const allUnpaidSelected = unpaidRecords.length > 0 && unpaidRecords.every(r => selectedRecords.includes(r.id));
  const unpaidRecordsOnDay = dayRecords.filter(r => !r.isPaid);
  const allUnpaidSelectedOnDay = unpaidRecordsOnDay.length > 0 && unpaidRecordsOnDay.every(r => selectedRecords.includes(r.id));

  return (
    <div className="space-y-1">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 h-7">
          <TabsTrigger value="calendar" className="text-xs h-5">Calendar</TabsTrigger>
          <TabsTrigger value="list" className="text-xs h-5">List</TabsTrigger>
        </TabsList>
        
        <TabsContent value="calendar" className="mt-1 space-y-1">
          {/* Compact Calendar Header */}
          <div className="flex items-center justify-between px-1">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6" 
                onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
              >
                <ChevronLeft className="h-3 w-3" />
              </Button>
              <h2 className="text-xs font-semibold min-w-[70px] text-center">
                {format(currentMonth, "MMM yy")}
              </h2>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6" 
                onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
              >
                <ChevronRight className="h-3 w-3" />
              </Button>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-6 text-[0.65rem] px-2" 
              onClick={() => {
                setCurrentMonth(new Date());
                setSelectedDate(new Date());
              }}
            >
              Today
            </Button>
          </div>

          {/* Ultra-Compact Calendar */}
          <div className="border rounded-md overflow-hidden">
            {/* Days Header */}
            <div className="grid grid-cols-7 bg-muted/40 border-b">
              {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, i) => (
                <div key={day + i} className="py-1 text-center text-[0.6rem] font-medium text-muted-foreground border-r border-border/50 last:border-r-0">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7">
              {calendarDays.map((day, index) => {
                const recordsForDay = presenceRecords.filter(r => isSameDay(new Date(r.date), day));
                const isCurrentMonth = isSameMonth(day, currentMonth);
                const isSelected = isSameDay(day, selectedDate); 
                const isCurrentDay = isToday(day);
                
                // Status indicators: Green = Paid, Orange = Unpaid Present/Late, Red = Unpaid Absent
                const paidRecords = recordsForDay.filter(r => r.isPaid);
                const unpaidPresent = recordsForDay.filter(r => !r.isPaid && r.attended);
                const unpaidAbsent = recordsForDay.filter(r => !r.isPaid && !r.attended);
                
                return (
                  <div
                    key={day.toString()}
                    className={cn(
                      "h-14 border-r border-b border-border/30 last:border-r-0 cursor-pointer transition-all duration-150 p-1 flex flex-col justify-between relative group",
                      !isCurrentMonth && "bg-muted/20 text-muted-foreground",
                      isCurrentDay && "bg-primary/5 border-primary/40 ring-1 ring-primary/20",
                      isSelected && "bg-accent ring-2 ring-primary ring-inset shadow-md",
                      "hover:bg-accent/40 hover:shadow-sm"
                    )}
                    onClick={() => setSelectedDate(day)}
                  >
                    <span className={cn(
                      "text-xs font-medium leading-none self-start",
                      isCurrentDay && "text-primary font-bold",
                      !isCurrentMonth && "text-muted-foreground/60"
                    )}>
                      {format(day, 'd')}
                    </span>
                    
                    {recordsForDay.length > 0 && (
                      <div className="flex items-end justify-center gap-1 flex-wrap mt-auto mb-0.5">
                        {/* Paid Records - Green Circles */}
                        {paidRecords.slice(0, 3).map(record => (
                          <div
                            key={record.id}
                            className="h-2.5 w-2.5 rounded-full bg-emerald-500 border border-emerald-600 shadow-sm"
                            title={`${record.shift.name}: Paid ${record.status}`}
                          />
                        ))}
                        
                        {/* Unpaid Present/Late - Orange Circles */}
                        {unpaidPresent.slice(0, 3).map(record => (
                          <div
                            key={record.id}
                            className="h-2.5 w-2.5 rounded-full bg-amber-500 border border-amber-600 shadow-sm"
                            title={`${record.shift.name}: Unpaid ${record.status}`}
                          />
                        ))}
                        
                        {/* Unpaid Absent - Red Circles */}
                        {unpaidAbsent.slice(0, 3).map(record => (
                          <div
                            key={record.id}
                            className="h-2.5 w-2.5 rounded-full bg-red-500 border border-red-600 shadow-sm"
                            title={`${record.shift.name}: Absent (unpaid)`}
                          />
                        ))}
                        
                        {/* Overflow indicator */}
                        {recordsForDay.length > 6 && (
                          <span className="text-[0.55rem] font-medium text-muted-foreground bg-muted/80 px-1 rounded">
                            +{recordsForDay.length - 6}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
            
          {/* Selected Day Records - Ultra Compact */}
          {dayRecords.length > 0 && (
            <div className="border rounded-md p-2 bg-card">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-semibold">
                  {format(selectedDate, "MMM d")}
                </h3>
                {enableBulkSelection && unpaidRecordsOnDay.length > 0 && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleSelectAllOnDay} 
                    className="text-xs h-6 px-2"
                  >
                    {allUnpaidSelectedOnDay ? <CheckSquare className="h-3 w-3" /> : <Square className="h-3 w-3" />}
                  </Button>
                )}
              </div>
              
              <ScrollArea className="h-36">
                <div className="space-y-1">
                  {dayRecords.map((record) => (
                    <div
                      key={record.id}
                      className={cn(
                        "flex items-center justify-between p-2 rounded-md border text-xs transition-colors",
                        record.isPaid ? "bg-emerald-50 border-emerald-200 dark:bg-emerald-950/20" : "bg-background hover:bg-accent/30",
                        selectedRecords.includes(record.id) && "ring-2 ring-primary/50"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        {enableBulkSelection && !record.isPaid && (
                          <Checkbox
                            checked={selectedRecords.includes(record.id)}
                            onCheckedChange={(checked) => handleRecordSelection(record.id, !!checked)}
                            className="h-3 w-3"
                          />
                        )}
                        <div className="flex items-center gap-2">
                          {record.isPaid && <DollarSign className="h-3 w-3 text-emerald-600" />}
                          <div>
                            <span className="font-medium">{record.shift.name}</span>
                            <div className="text-[0.7rem] text-muted-foreground">
                              {record.shift.startTime} - {record.shift.endTime}
                            </div>
                          </div>
                          <Badge 
                            variant={record.status === "present" ? "default" : record.status === "late" ? "secondary" : "destructive"}
                            className="text-[0.65rem] px-2 py-0.5 h-5"
                          >
                            {record.status}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-[0.7rem] text-muted-foreground min-w-[2.5rem] text-right">
                          {record.rate?.toFixed(0)}
                        </span>
                        <Button 
                          size="sm" 
                          variant="ghost" 
                          className="h-6 px-2 text-[0.7rem] border"
                          onClick={() => onToggleAttendance(record.id, record.attended)}
                          disabled={record.isPaid}
                        >
                          {record.attended ? "Mark Absent" : "Mark Present"}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {dayRecords.length === 0 && (
            <div className="text-center text-sm text-muted-foreground py-6 border rounded-md bg-muted/10">
              No records for {format(selectedDate, "MMM d")}.
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="list" className="mt-1 space-y-1">
          <div className="flex justify-between items-center px-1">
            <h3 className="text-sm font-semibold">
              Attendance Records
            </h3>
            {enableBulkSelection && unpaidRecords.length > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleSelectAllInList} 
                className="text-xs h-6 px-2"
              >
                {allUnpaidSelected ? <CheckSquare className="h-3 w-3" /> : <Square className="h-3 w-3" />}
              </Button>
            )}
          </div>
          
          <div className="border rounded-md">
            <ScrollArea className="h-72">
              <Table>
                <TableHeader>
                  <TableRow className="h-8 border-b">
                    {enableBulkSelection && (
                      <TableHead className="w-8 p-1">
                        <Checkbox
                          checked={allUnpaidSelected}
                          onCheckedChange={handleSelectAllInList}
                          className="h-3 w-3"
                        />
                      </TableHead>
                    )}
                    <TableHead className="p-2 text-xs">Date</TableHead>
                    <TableHead className="p-2 text-xs">Shift</TableHead>
                    <TableHead className="p-2 text-xs">Status</TableHead>
                    <TableHead className="p-2 text-xs text-right">Rate</TableHead>
                    <TableHead className="p-2 text-xs text-right">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {presenceRecords.length > 0 ? (
                    presenceRecords.map((record) => (
                      <TableRow key={record.id} className={cn(
                        "h-8 border-b border-border/30",
                        record.isPaid && "bg-emerald-50 dark:bg-emerald-950/10"
                      )}>
                        {enableBulkSelection && (
                          <TableCell className="p-1">
                            {!record.isPaid && (
                              <Checkbox
                                checked={selectedRecords.includes(record.id)}
                                onCheckedChange={(checked) => handleRecordSelection(record.id, !!checked)}
                                className="h-3 w-3"
                              />
                            )}
                          </TableCell>
                        )}
                        <TableCell className="p-2 font-medium text-xs">
                          <div className="flex items-center gap-1">
                            {record.isPaid && <DollarSign className="h-3 w-3 text-emerald-600" />}
                            {format(new Date(record.date), "MMM d")}
                          </div>
                        </TableCell>
                        <TableCell className="p-2 text-xs">
                          <div>{record.shift.name}</div>
                          <div className="text-[0.65rem] text-muted-foreground">
                            {record.shift.startTime}-{record.shift.endTime}
                          </div>
                        </TableCell>
                        <TableCell className="p-2">
                          <Badge
                            variant={record.status === "present" ? "default" : record.status === "late" ? "secondary" : "destructive"}
                            className="text-[0.65rem] px-2 py-0.5 h-5"
                          >
                            {record.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="p-2 font-mono text-xs text-right text-muted-foreground">
                          {record.rate?.toFixed(0)}
                        </TableCell>
                        <TableCell className="p-2 text-right">
                          <Button
                            size="sm"
                            variant="ghost" 
                            className="h-6 px-2 text-[0.7rem] border"
                            onClick={() => onToggleAttendance(record.id, record.attended)}
                            disabled={record.isPaid}
                          >
                            {record.attended ? "Mark Absent" : "Mark Present"}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={enableBulkSelection ? 6 : 5} className="h-16 text-center">
                        <div className="flex flex-col items-center gap-2 text-muted-foreground">
                          <CalendarIcon className="h-4 w-4" />
                          <p className="text-xs">No records found.</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
          
          <Button onClick={onRefresh} variant="ghost" className="w-full h-7 text-xs border">
            <RotateCcw className="h-3 w-3 mr-1" />
            Refresh
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
}