'use client';

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { StaffMember } from '@/lib/types/staff';
import { Users } from 'lucide-react';

interface StaffSelectorProps {
  staffList: StaffMember[];
  selectedStaff: StaffMember | null;
  onStaffSelect: (staff: StaffMember | null) => void;
}

export function StaffSelector({ staffList, selectedStaff, onStaffSelect }: StaffSelectorProps) {
  const getStaffInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium flex items-center gap-2 text-muted-foreground">
        <Users className="h-4 w-4" />
        <span>Personnel</span>
      </label>
      <Select
        value={selectedStaff?.id || ''}
        onValueChange={(value) => {
          const staff = staffList.find(s => s.id === value);
          onStaffSelect(staff || null);
        }}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Choisir un membre..." />
        </SelectTrigger>
        <SelectContent>
          {staffList.map((staffMember) => (
            <SelectItem key={staffMember.id} value={staffMember.id}>
              <div className="flex items-center gap-3 py-1">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {getStaffInitials(staffMember.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{staffMember.name}</p>
                  <p className="text-xs text-muted-foreground">{staffMember.role}</p>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
} 