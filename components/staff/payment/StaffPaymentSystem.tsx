'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { StaffSelector } from '@/components/staff/payment/StaffSelector';
import { PaymentModeSelector } from '@/components/staff/payment/PaymentModeSelector';
import { PaymentForm } from '@/components/staff/payment/PaymentForm';
import NewPaymentHistory from '@/components/payment/NewPaymentHistory';
import { StaffMember } from '@/lib/types/staff';
import { StaffInfoHeader } from '@/components/staff/payment/StaffInfoHeader';
import { Separator } from '@/components/ui/separator';
// 🆕 Removed old payment service imports - NewPaymentHistory handles its own data loading
import { useToast } from '@/components/ui/use-toast';

export type PaymentMode = 'salary' | 'per-shift' | 'bonus' | 'advance' | 'deduction';

interface StaffPaymentSystemProps {
  staffList?: StaffMember[];
  selectedStaffId?: string | null;
}

export function StaffPaymentSystem({ staffList = [], selectedStaffId }: StaffPaymentSystemProps) {
  const { toast } = useToast();
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [paymentMode, setPaymentMode] = useState<PaymentMode>('salary');
  // 🆕 Removed payment history state and loading - NewPaymentHistory handles its own data

  useEffect(() => {
    if (selectedStaffId && staffList.length > 0) {
      const staff = staffList.find(s => s.id === selectedStaffId);
      if (staff) {
        setSelectedStaff(staff);
      }
    }
  }, [selectedStaffId, staffList]);

  useEffect(() => {
    if (selectedStaff) {
      const isPerShift = selectedStaff.paymentConfig.type === 'PER_SHIFT';
      setPaymentMode(isPerShift ? 'per-shift' : 'salary');
      // 🆕 Removed loadPaymentHistory call - NewPaymentHistory handles its own data loading
    }
  }, [selectedStaff]);

  const handleStaffSelect = (staff: StaffMember | null) => {
    setSelectedStaff(staff);
  };

  const handlePaymentSuccess = () => {
    // 🆕 NewPaymentHistory will automatically refresh when new payments are made
    // No need to manually reload payment history
  };

  return (
    <div className="space-y-6 py-4">
      <div className="space-y-4 px-4">
        <h2 className="text-2xl font-semibold leading-none tracking-tight">💰 Paiement du Personnel</h2>
        <StaffSelector
          staffList={staffList}
          selectedStaff={selectedStaff}
          onStaffSelect={handleStaffSelect}
        />
        {selectedStaff && (
          <div className="space-y-4">
            <Separator />
            <StaffInfoHeader staff={selectedStaff} />
            <PaymentModeSelector
              selectedMode={paymentMode}
              onModeSelect={setPaymentMode}
              selectedStaff={selectedStaff}
            />
            <PaymentForm
              staff={selectedStaff}
              paymentMode={paymentMode}
              onPaymentComplete={handlePaymentSuccess}
            />
          </div>
        )}
      </div>

      {selectedStaff && (
        <div className="space-y-4 px-4 pt-6">
          <h2 className="text-2xl font-semibold leading-none tracking-tight">📊 Historique des Paiements - {selectedStaff.name}</h2>
          <NewPaymentHistory
            staffId={selectedStaff.id}
            maxHeight="600px"
          />
        </div>
      )}
    </div>
  );
}