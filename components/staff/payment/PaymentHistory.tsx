'use client';

import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { History, Loader2, RefreshCw } from 'lucide-react';
import { PaymentSnapshot } from '@/lib/services/staff-payment-service';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from '@/lib/utils';

interface PaymentHistoryProps {
  payments: PaymentSnapshot[];
  isLoading: boolean;
  onRefresh: () => void;
}

const formatCurrency = (amount: number): string => {
  if (amount === 0) return '-';
  return amount.toLocaleString() + ' DZD';
};

export function PaymentHistory({ payments, isLoading, onRefresh }: PaymentHistoryProps) {
  return (
    <Card className="border shadow-sm">
      <div className="p-4 border-b flex items-center justify-between">
        <div className="flex items-center gap-2">
          <History className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-md font-semibold">Historique des Paiements</h3>
        </div>
        <Button variant="ghost" size="sm" onClick={onRefresh} disabled={isLoading}>
          <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
        </Button>
      </div>

      <div>
        {isLoading ? (
          <div className="p-8 text-center text-muted-foreground">
            <Loader2 className="h-6 w-6 animate-spin mx-auto" />
          </div>
        ) : payments.length === 0 ? (
          <div className="p-8 text-center text-muted-foreground">
            Aucun paiement trouvé
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Montant de Base</TableHead>
                <TableHead className="text-right">Bonus</TableHead>
                <TableHead className="text-right">Déduction</TableHead>
                <TableHead className="text-right font-semibold">Net Payé</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((p) => (
                <TableRow key={p.id}>
                  <TableCell>{format(new Date(p.date), 'd MMM yyyy', { locale: fr })}</TableCell>
                  <TableCell>{p.type}</TableCell>
                  <TableCell className="text-right">{formatCurrency(p.base)}</TableCell>
                  <TableCell className="text-right text-green-600">{p.bonus > 0 ? `+${formatCurrency(p.bonus)}` : '-'}</TableCell>
                  <TableCell className="text-right text-red-600">{p.deduction > 0 ? `-${formatCurrency(p.deduction)}` : '-'}</TableCell>
                  <TableCell className="text-right font-bold">{formatCurrency(p.netPaid)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    </Card>
  );
}