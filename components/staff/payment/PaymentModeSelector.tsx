'use client';

import React from 'react';
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { DollarSign, Gift, CreditCard, Minus, Calendar } from 'lucide-react';
import { PaymentMode } from './StaffPaymentSystem';
import { StaffMember } from '@/lib/types/staff';
import { Button } from '@/components/ui/button';

interface PaymentModeSelectorProps {
  selectedMode: PaymentMode;
  onModeSelect: (mode: PaymentMode) => void;
  selectedStaff?: StaffMember | null;
}

export function PaymentModeSelector({ selectedMode, onModeSelect, selectedStaff }: PaymentModeSelectorProps) {
  // Determine available payment modes based on staff payment type
  const getAvailableModes = (): PaymentMode[] => {
    if (!selectedStaff) return ['salary', 'per-shift', 'bonus', 'advance', 'deduction'];
    
    const isPerShift = selectedStaff.paymentConfig.type === 'PER_SHIFT';
    
    if (isPerShift) {
      // For per-shift staff: only per-shift, bonus, advance, deduction
      return ['per-shift', 'bonus', 'advance', 'deduction'];
    } else {
      // For salary staff: only salary, bonus, advance, deduction
      return ['salary', 'bonus', 'advance', 'deduction'];
    }
  };

  const availableModes = getAvailableModes();
  const gridCols = availableModes.length;

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">💳 Type de paiement</h3>
      <Tabs value={selectedMode} onValueChange={(value) => onModeSelect(value as PaymentMode)}>
        <TabsList className={`grid w-full h-10`} style={{ gridTemplateColumns: `repeat(${gridCols}, 1fr)` }}>
          {availableModes.includes('salary') && (
            <TabsTrigger value="salary" className="flex items-center gap-1 text-sm" asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <DollarSign className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Salaire</span>
              </Button>
            </TabsTrigger>
          )}
          {availableModes.includes('per-shift') && (
            <TabsTrigger value="per-shift" className="flex items-center gap-1 text-sm" asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Calendar className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Par Shift</span>
              </Button>
            </TabsTrigger>
          )}
          {availableModes.includes('bonus') && (
            <TabsTrigger value="bonus" className="flex items-center gap-1 text-sm" asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Gift className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Bonus</span>
              </Button>
            </TabsTrigger>
          )}
          {availableModes.includes('advance') && (
            <TabsTrigger value="advance" className="flex items-center gap-1 text-sm" asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <CreditCard className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Avance</span>
              </Button>
            </TabsTrigger>
          )}
          {availableModes.includes('deduction') && (
            <TabsTrigger value="deduction" className="flex items-center gap-1 text-sm" asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Minus className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Déduction</span>
              </Button>
            </TabsTrigger>
          )}
        </TabsList>
      </Tabs>
    </div>
  );
} 