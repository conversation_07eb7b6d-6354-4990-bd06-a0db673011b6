'use client';

import React, { useState, useEffect } from 'react';
import { format, addMonths } from 'date-fns';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  DollarSign, 
  Loader2,
  CheckIcon,
  Calendar,
  Edit3,
  X
} from 'lucide-react';
import { StaffMember } from '@/lib/types/staff';
import * as staffPaymentService from '@/lib/services/staff-payment-service';
// Import new balance system functions
import {
  getAllBalances,
  createPaymentSnapshot,
  getPaymentHistory
} from '@/lib/services/new-staff-balance-service';

interface MonthlySalaryFormProps {
  selectedStaff: StaffMember;
  onPaymentSuccess: () => void;
}

// Helper functions
const formatCurrency = (amount: number): string => {
  return amount.toLocaleString() + ' DZD';
};

const formatDate = (isoString: string): string => {
  try {
    const date = new Date(isoString);
    // Use UTC methods to avoid timezone shifts
    const year = date.getUTCFullYear();
    const month = date.getUTCMonth(); // 0-indexed
    const day = date.getUTCDate();
    
    // Format it manually to 'dd MMM yyyy'
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    return `${String(day).padStart(2, '0')} ${monthNames[month]} ${year}`;
  } catch {
    return 'Date invalide';
  }
};

export function MonthlySalaryForm({ selectedStaff, onPaymentSuccess }: MonthlySalaryFormProps) {
  const { toast } = useToast();
  
  // Simple form state
  const [baseSalaryInput, setBaseSalaryInput] = useState<number>(0); // 🆕 User input for base salary
  const [paymentNote, setPaymentNote] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  
  // Next payment due date state
  const [nextPaymentDueDate, setNextPaymentDueDate] = useState<string>('');
  const [editingDueDate, setEditingDueDate] = useState(false);
  const [tempDueDate, setTempDueDate] = useState<string>('');
  
  // Financial calculation state
  const [financialSummary, setFinancialSummary] = useState<{
    baseSalary: number;
    totalBonuses: number;
    totalDeductions: number;
    totalAdvances: number;
    suggestedAmount: number;
  }>({
    baseSalary: 0,
    totalBonuses: 0,
    totalDeductions: 0,
    totalAdvances: 0,
    suggestedAmount: 0
  });

  // Load financial data when component mounts
  useEffect(() => {
    loadFinancialSummary();
    loadNextPaymentDueDate();
  }, [selectedStaff]);

  const loadNextPaymentDueDate = async () => {
    if (!selectedStaff) return;
    
    console.log('🔍 Loading next payment due date for staff:', selectedStaff.id);
    
    try {
      const dueDate = await staffPaymentService.getNextPaymentDueDate(selectedStaff.id);
      console.log('📅 Fetched due date:', dueDate);
      
      if (dueDate) {
        setNextPaymentDueDate(dueDate);
        setTempDueDate(format(new Date(dueDate), 'yyyy-MM-dd'));
        console.log('✅ Due date set in state:', dueDate);
      } else {
        // No due date exists (staff has never been paid)
        setNextPaymentDueDate('');
        setTempDueDate('');
        console.log('❌ No due date found - staff has never been paid');
      }
    } catch (error) {
      console.error('Error loading next payment due date:', error);
      // Clear due date on error
      setNextPaymentDueDate('');
      setTempDueDate('');
    }
  };

  const handleSaveDueDate = async () => {
    if (!selectedStaff || !tempDueDate) return;
    
    try {
      // tempDueDate is 'YYYY-MM-DD'. The service now correctly handles this.
      await staffPaymentService.updateNextPaymentDueDate(selectedStaff.id, tempDueDate);
      
      // To properly display the new date without timezone shifts, create a UTC-based date
      const parts = tempDueDate.split('-');
      const utcDate = new Date(Date.UTC(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2])));
      
      setNextPaymentDueDate(utcDate.toISOString());
      setEditingDueDate(false);
      
      toast({
        title: "📅 Date d'échéance mise à jour",
        description: `Prochaine échéance: ${formatDate(utcDate.toISOString())}`,
      });
    } catch (error) {
      console.error('Error updating due date:', error);
      toast({
        title: "Erreur",
        description: "Impossible de mettre à jour la date d'échéance",
        variant: "destructive",
      });
    }
  };

  const loadFinancialSummary = async () => {
    if (!selectedStaff) return;
    
    try {
      // 🆕 Use new balance system to get current balances
      const balances = await getAllBalances(selectedStaff.id);
      const baseSalary = selectedStaff.paymentConfig.baseSalary || 0;

      console.log('📊 Current balances from new system:', balances);

      // Calculate suggested salary amount using new balance system
      const suggestedAmount = baseSalary + balances.bonuses - balances.deductions - balances.advances;

      setFinancialSummary({
        baseSalary,
        totalBonuses: balances.bonuses,
        totalDeductions: balances.deductions,
        totalAdvances: balances.advances,
        suggestedAmount: Math.max(0, suggestedAmount) // Don't suggest negative amounts
      });

      // Set the base salary input to the configured base salary
      setBaseSalaryInput(baseSalary);
      
    } catch (error) {
      console.error('Error loading financial summary:', error);
      // Fallback to base salary
      const baseSalary = selectedStaff.paymentConfig.baseSalary || 0;
      setFinancialSummary({
        baseSalary,
        totalBonuses: 0,
        totalDeductions: 0,
        totalAdvances: 0,
        suggestedAmount: baseSalary
      });
      setBaseSalaryInput(baseSalary);
    }
  };

  // 🆕 Calculate net amount based on user input
  const calculateNetAmount = () => {
    return baseSalaryInput + financialSummary.totalBonuses - financialSummary.totalDeductions - financialSummary.totalAdvances;
  };

  const handlePayment = () => {
    const netAmount = calculateNetAmount();
    if (!selectedStaff || baseSalaryInput <= 0) {
      toast({
        title: "Saisie invalide",
        description: "Veuillez saisir un salaire de base valide.",
        variant: "destructive",
      });
      return;
    }
    
    setShowConfirmDialog(true);
  };

  const confirmPayment = async () => {
    setShowConfirmDialog(false);
    setSubmitting(true);
    try {
      // 🆕 Use new payment snapshot system instead of consolidateAllPendingPayments
      const netAmount = calculateNetAmount();
      const paymentSnapshot = await createPaymentSnapshot({
        staffId: selectedStaff.id,
        baseSalary: baseSalaryInput,
        useAllBonuses: true,
        useAllDeductions: true,
        useAllAdvances: true,
        notes: paymentNote || `Monthly salary payment - ${format(new Date(), 'dd MMM yyyy')}`,
        periodStart: undefined, // Could be enhanced to include period
        periodEnd: undefined
      });

      console.log('✅ Payment snapshot created:', paymentSnapshot);

      // 🆕 Update next payment due date after successful payment
      try {
        if (nextPaymentDueDate) {
          // Staff already has a due date, update it by adding one month
          const baseDate = new Date(nextPaymentDueDate);
          const nextDueDate = addMonths(baseDate, 1);
          const nextDueDateString = format(nextDueDate, 'yyyy-MM-dd');

          await staffPaymentService.updateNextPaymentDueDate(selectedStaff.id, nextDueDateString);
          console.log('📅 Next payment due date updated to:', nextDueDate.toISOString());
        } else {
          // First salary payment - set initial due date to next month
          const firstDueDate = addMonths(new Date(), 1);
          const firstDueDateString = format(firstDueDate, 'yyyy-MM-dd');

          await staffPaymentService.updateNextPaymentDueDate(selectedStaff.id, firstDueDateString);
          console.log('📅 First payment due date set to:', firstDueDate.toISOString());
        }
      } catch (error) {
        console.warn('Failed to update next payment due date:', error);
        // Don't fail the payment if this update fails
      }

      toast({
        title: "🎯 Paiement Traité",
        description: `Net versé: ${formatCurrency(paymentSnapshot.netAmount)}. Balances consolidées automatiquement`,
      });

      // Reset form and reload data
      setBaseSalaryInput(0);
      setPaymentNote('');

      // Reload financial summary and next payment due date
      await loadFinancialSummary();
      await loadNextPaymentDueDate();

      // Trigger refresh
      onPaymentSuccess();

    } catch (error) {
      console.error('Error processing payment snapshot:', error);
      toast({
        title: "❌ Échec du paiement",
        description: error instanceof Error ? error.message : "Impossible de traiter le paiement",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-2">
      {/* Hyper-Compact, Refined Payment Interface */}
      <div className="bg-card rounded-lg border">
        {/* Simplified Header */}
        <div className="p-3 border-b">
          <div>
            <h3 className="font-semibold text-sm leading-tight">Calcul Salaire</h3>
            <p className="text-xs text-muted-foreground leading-tight">Période de paie actuelle</p>
          </div>
        </div>
        
        {/* Base Salary Input */}
        <div className="p-3 border-b">
          <div className="space-y-2">
            <label className="text-xs text-muted-foreground font-medium">Salaire de Base</label>
            <div className="relative">
              <DollarSign className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="number"
                className="pl-9 h-10 text-base font-semibold"
                value={baseSalaryInput}
                onChange={(e) => setBaseSalaryInput(parseFloat(e.target.value) || 0)}
                placeholder="0"
              />
            </div>
          </div>
        </div>

        {/* Financial Details Grid */}
        <div className="p-3 border-b">
          <div className="grid grid-cols-3 gap-3">
          <div className="text-center">
              <div className="text-xs text-muted-foreground mb-0.5">Primes</div>
              <div className="font-semibold text-sm tracking-tight text-emerald-600">+{formatCurrency(financialSummary.totalBonuses)}</div>
          </div>
          <div className="text-center">
              <div className="text-xs text-muted-foreground mb-0.5">Retenues</div>
              <div className="font-semibold text-sm tracking-tight text-red-500">-{formatCurrency(financialSummary.totalDeductions)}</div>
          </div>
          <div className="text-center">
              <div className="text-xs text-muted-foreground mb-0.5">Avances</div>
              <div className="font-semibold text-sm tracking-tight text-orange-500">-{formatCurrency(financialSummary.totalAdvances)}</div>
            </div>
          </div>
        </div>

        {/* Next Payment Due Date - Only show if staff has been paid before */}
        {nextPaymentDueDate && (
          <div className="p-3 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-1">
                <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                {editingDueDate ? (
                  <div className="flex gap-2 flex-1">
                    <Input
                      type="date"
                      value={tempDueDate}
                      onChange={(e) => setTempDueDate(e.target.value)}
                      className="h-8 text-sm flex-1"
                    />
                  </div>
                ) : (
                  <div className="flex-1">
                    <div className="text-xs text-muted-foreground">Prochaine Échéance</div>
                    <div className="text-sm font-semibold text-primary leading-tight">
                      {formatDate(nextPaymentDueDate)}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex gap-1 flex-shrink-0">
                {editingDueDate && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => {
                      setEditingDueDate(false);
                      setTempDueDate(format(new Date(nextPaymentDueDate), 'yyyy-MM-dd'));
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    if (editingDueDate) {
                      handleSaveDueDate();
                    } else {
                      setEditingDueDate(true);
                    }
                  }}
                  disabled={submitting}
                >
                  {submitting ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : editingDueDate ? (
                    <CheckIcon className="h-3 w-3 text-emerald-600" />
                  ) : (
                    <Edit3 className="h-3 w-3" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Payment Actions */}
        <div className="p-3 bg-muted/30">
          <div className="space-y-3">
            <Input
              placeholder="Note (optionnelle)..."
              value={paymentNote}
              onChange={(e) => setPaymentNote(e.target.value)}
              className="h-10 text-sm"
            />
            <div className="flex items-center justify-between gap-3">
              <div className="text-right">
                <div className="text-xs text-muted-foreground uppercase tracking-wider font-medium">Net à Payer</div>
                <div className="text-xl font-bold text-primary leading-tight">{formatCurrency(calculateNetAmount())}</div>
              </div>
              <Button 
                className="px-8 h-12 text-base font-medium" 
                onClick={handlePayment}
                disabled={baseSalaryInput <= 0 || submitting}
              >
                {submitting ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <CheckIcon className="h-5 w-5" />
                )}
                <span className="ml-2">Payer</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmer le paiement</DialogTitle>
            <DialogDescription>
              Veuillez vérifier les détails du paiement avant de confirmer.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Employé:</span>
                <div className="font-medium">{selectedStaff?.name}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Type:</span>
                <div className="font-medium">Salaire mensuel</div>
              </div>
            </div>
            
            <div className="border rounded-lg p-3 bg-muted/30">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Salaire de base:</span>
                  <span className="font-medium">{formatCurrency(baseSalaryInput)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Primes:</span>
                  <span className="font-medium text-emerald-600">+{formatCurrency(financialSummary.totalBonuses)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Retenues:</span>
                  <span className="font-medium text-red-500">-{formatCurrency(financialSummary.totalDeductions)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Avances:</span>
                  <span className="font-medium text-orange-500">-{formatCurrency(financialSummary.totalAdvances)}</span>
                </div>
              </div>
              <div className="border-t mt-2 pt-2">
                <div className="flex justify-between font-semibold">
                  <span>Montant à payer:</span>
                  <span className="text-primary">{formatCurrency(calculateNetAmount())}</span>
                </div>
              </div>
            </div>
            
            {paymentNote && (
              <div>
                <span className="text-sm text-muted-foreground">Note:</span>
                <div className="text-sm font-medium">{paymentNote}</div>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={submitting}
            >
              Annuler
            </Button>
            <Button
              onClick={confirmPayment}
              disabled={submitting}
            >
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Confirmer le paiement
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}