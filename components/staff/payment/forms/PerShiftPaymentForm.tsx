"use client";

import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval, 
  startOfWeek, 
  endOfWeek,
  isSameDay,
  isToday,
  isSameMonth 
} from "date-fns";
import { ChevronLeft, ChevronRight, TrendingUp, TrendingDown, DollarSign, Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { StaffMember } from "@/lib/types/staff";
import { getStaffAttendance } from "@/lib/services/staff-payment-service";
import { getAllBalances, createPerShiftPaymentSnapshot } from "@/lib/services/new-staff-balance-service";
import { useDatabase } from "@/lib/context/unified-db-provider";
import { cn } from "@/lib/utils";

interface PerShiftPaymentFormProps {
  selectedStaff: StaffMember;
  onPaymentSuccess: () => void;
}

interface ShiftBlock {
  id: string;
  date: string;
  shiftId: string;
  shiftName: string;
  rate: number;
  status: 'paid' | 'unpaid' | 'absent';
  isPaid: boolean;
  attended: boolean;
}

interface StaffBalances {
  advanceBalance: number;
  pendingDeductions: number;
  pendingBonuses: number;
}

interface AdjustmentToggles {
  bonusEnabled: boolean;
  deductionEnabled: boolean;
  advanceEnabled: boolean;
}

export function PerShiftPaymentForm({
  selectedStaff,
  onPaymentSuccess
}: PerShiftPaymentFormProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [shifts, setShifts] = useState<ShiftBlock[]>([]);
  const [selectedShifts, setSelectedShifts] = useState<string[]>([]);
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [balances, setBalances] = useState<StaffBalances>({ advanceBalance: 0, pendingDeductions: 0, pendingBonuses: 0 });
  const [adjustmentToggles, setAdjustmentToggles] = useState<AdjustmentToggles>({
    bonusEnabled: true,
    deductionEnabled: true,
    advanceEnabled: true
  });
  const [paymentNote, setPaymentNote] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { toast } = useToast();
  const { isInitialized } = useDatabase();

  // Load shifts and balances
  useEffect(() => {
    loadData();
  }, [currentMonth, selectedStaff]);

  const loadData = async () => {
    if (!selectedStaff) return;
    
    setIsLoading(true);
    try {
      await Promise.all([loadShifts(), loadBalances()]);
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadShifts = async () => {
    const startDate = format(startOfMonth(currentMonth), 'yyyy-MM-dd');
    const endDate = format(endOfMonth(currentMonth), 'yyyy-MM-dd');
    
    const attendanceRecords = await getStaffAttendance(
      selectedStaff.id,
      startDate,
      endDate
    );

    const shiftBlocks: ShiftBlock[] = attendanceRecords.map(record => {
      const rate = selectedStaff.paymentConfig.shiftRates?.[record.shiftId] || 
                  selectedStaff.paymentConfig.shiftRate || 0;
      
      return {
        id: record.id,
        date: record.date,
        shiftId: record.shiftId,
        shiftName: record.shiftName,
        rate,
        status: record.isPaid ? 'paid' : (record.attended ? 'unpaid' : 'absent'),
        isPaid: record.isPaid || false,
        attended: record.attended
      };
    });

    setShifts(shiftBlocks);
  };

  const loadBalances = async () => {
    try {
      console.log('🔄 Loading balances for staff:', selectedStaff.id);

      // Try new balance system first
      try {
        const balanceSummary = await getAllBalances(selectedStaff.id);
        console.log('📊 New system balance summary:', balanceSummary);

        const newBalances = {
          advanceBalance: balanceSummary.advances || 0,
          pendingDeductions: balanceSummary.deductions || 0,
          pendingBonuses: balanceSummary.bonuses || 0
        };

        console.log('💰 Setting balances from new system:', newBalances);
        setBalances(newBalances);
        return; // Success with new system

      } catch (newSystemError) {
        console.error('❌ New balance system failed:', newSystemError);

        // Set zero balances if new system fails
        setBalances({
          advanceBalance: 0,
          pendingDeductions: 0,
          pendingBonuses: 0
        });

        // Show error to user
        toast({
          title: "❌ Erreur de chargement des balances",
          description: "Impossible de charger les balances. Veuillez rafraîchir la page.",
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('❌ Both balance systems failed:', error);
      // Final fallback to zero balances
      setBalances({
        advanceBalance: 0,
        pendingDeductions: 0,
        pendingBonuses: 0
      });

      toast({
        title: "❌ Erreur de chargement",
        description: "Impossible de charger les balances. Veuillez rafraîchir la page.",
        variant: "destructive",
      });
    }
  };

  // Calculate calendar days
  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 1 });
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 1 });
    return eachDayOfInterval({ start: startDate, end: endDate });
  }, [currentMonth]);

  // Get unpaid shifts sorted by date (oldest first)
  const unpaidShifts = useMemo(() => {
    return shifts
      .filter(shift => !shift.isPaid && shift.attended)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [shifts]);

  // Calculate total unpaid amount
  const totalUnpaidAmount = useMemo(() => {
    return unpaidShifts.reduce((sum, shift) => sum + shift.rate, 0);
  }, [unpaidShifts]);

  // Auto-select shifts based on payment amount (pay oldest first)
  useEffect(() => {
    if (paymentAmount <= 0) {
      setSelectedShifts([]);
      return;
    }

    let remainingAmount = paymentAmount;
    const selectedIds: string[] = [];

    for (const shift of unpaidShifts) {
      if (remainingAmount >= shift.rate) {
        selectedIds.push(shift.id);
        remainingAmount -= shift.rate;
      } else {
        break;
      }
    }

    setSelectedShifts(selectedIds);
  }, [paymentAmount, unpaidShifts]);

  // Smart auto-enable/disable logic for adjustments based on sufficiency
  useEffect(() => {
    const selectedAmount = selectedShifts.reduce((sum, shiftId) => {
      const shift = shifts.find(s => s.id === shiftId);
      return sum + (shift?.rate || 0);
    }, 0);

    // Auto-enable/disable adjustments based on brut amount sufficiency
    setAdjustmentToggles({
      // Bonus: always enabled when available (adds money, no risk)
      bonusEnabled: (balances.pendingBonuses || 0) > 0,

      // Deduction: auto-enable only if brut amount can fully cover it
      deductionEnabled: (balances.pendingDeductions || 0) > 0 && selectedAmount >= (balances.pendingDeductions || 0),

      // Advance: auto-enable when there are selected shifts (partial repayment allowed)
      advanceEnabled: (balances.advanceBalance || 0) > 0 && selectedAmount > 0
    });
  }, [selectedShifts, shifts, balances]);

  // Calculate selected amount and final amount after all adjustments
  const { selectedAmount, finalAmount, advanceDeduction, netBonuses, netDeductions, canProcessPayment } = useMemo(() => {
    const selected = selectedShifts.reduce((sum, shiftId) => {
      const shift = shifts.find(s => s.id === shiftId);
      return sum + (shift?.rate || 0);
    }, 0);

    // Apply adjustments only if enabled (with safety checks for undefined values)
    const bonusAmount = adjustmentToggles.bonusEnabled ? (balances.pendingBonuses || 0) : 0;
    const deductionAmount = adjustmentToggles.deductionEnabled ? (balances.pendingDeductions || 0) : 0;
    const maxAdvanceDeduction = adjustmentToggles.advanceEnabled ? Math.min((balances.advanceBalance || 0), selected) : 0;

    // Final calculation: base + bonuses - deductions - advance repayment
    const final = selected + bonusAmount - deductionAmount - maxAdvanceDeduction;

    // Check if payment can be processed (final amount must be >= 0)
    const canProcess = final >= 0;

    return {
      selectedAmount: selected || 0,
      finalAmount: Math.max(0, final || 0),
      advanceDeduction: maxAdvanceDeduction || 0,
      netBonuses: bonusAmount || 0,
      netDeductions: deductionAmount || 0,
      canProcessPayment: canProcess
    };
  }, [selectedShifts, shifts, balances, adjustmentToggles]);

  // Handle payment amount input
  const handleAmountChange = (value: string) => {
    const amount = parseInt(value) || 0;
    setPaymentAmount(amount);
  };

  // Handle manual shift selection/deselection
  const handleShiftToggle = (shiftId: string) => {
    setSelectedShifts(prev => {
      if (prev.includes(shiftId)) {
        return prev.filter(id => id !== shiftId);
      } else {
        return [...prev, shiftId];
      }
    });
  };

  // Handle adjustment toggle changes
  const handleAdjustmentToggle = (type: keyof AdjustmentToggles, enabled: boolean) => {
    setAdjustmentToggles(prev => ({
      ...prev,
      [type]: enabled
    }));
  };

  // Process payment
  const handlePayment = () => {
    if (!isInitialized) {
      toast({
        title: "Base de données non initialisée",
        description: "Veuillez attendre que la base de données soit prête",
        variant: "destructive",
      });
      return;
    }

    if (selectedShifts.length === 0) {
      toast({
        title: "Aucun shift sélectionné",
        description: "Veuillez sélectionner des shifts à payer",
        variant: "destructive",
      });
      return;
    }

    setShowConfirmDialog(true);
  };

  const confirmPayment = async () => {
    setShowConfirmDialog(false);
    setIsProcessing(true);
    try {
      const shiftData = {
        attendanceIds: selectedShifts,
        shiftBreakdown: selectedShifts.map(shiftId => {
          const shift = shifts.find(s => s.id === shiftId)!;
          return {
            shiftId: shift.shiftId,
            shiftName: shift.shiftName,
            count: 1,
            rate: shift.rate,
            amount: shift.rate
          };
        })
      };

      // Create per-shift payment snapshot using new balance system
      const snapshot = await createPerShiftPaymentSnapshot({
        staffId: selectedStaff.id,
        shiftData,
        useAllBonuses: adjustmentToggles.bonusEnabled,
        useAllDeductions: adjustmentToggles.deductionEnabled,
        useAllAdvances: adjustmentToggles.advanceEnabled,
        notes: paymentNote || '' // Use manual note or empty string
      });

      // Build summary message
      const transactions = [`${selectedShifts.length} shifts: ${(selectedAmount || 0).toLocaleString()} DA`];
      if (adjustmentToggles.bonusEnabled && (netBonuses || 0) > 0) transactions.push(`Prime: +${(netBonuses || 0).toLocaleString()} DA`);
      if (adjustmentToggles.deductionEnabled && (netDeductions || 0) > 0) transactions.push(`Déduction: -${(netDeductions || 0).toLocaleString()} DA`);
      if (adjustmentToggles.advanceEnabled && (advanceDeduction || 0) > 0) transactions.push(`Avance remboursée: -${(advanceDeduction || 0).toLocaleString()} DA`);

      toast({
        title: "✅ Paiement traité",
        description: `Net versé: ${(snapshot.netAmount || 0).toLocaleString()} DA. Snapshot créé: ${(snapshot._id || 'unknown').slice(-8)}`,
      });

      // Reset and reload
      setSelectedShifts([]);
      setPaymentAmount(0);
      setPaymentNote('');
      loadData();
      onPaymentSuccess();
    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: "Échec du paiement",
        description: error instanceof Error ? error.message : "Impossible de traiter le paiement",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get shift for specific date
  const getShiftForDate = (date: Date) => {
    return shifts.find(shift => isSameDay(new Date(shift.date), date));
  };

  // Show database initialization message
  if (!isInitialized) {
    return (
      <Card className="p-4 space-y-4">
        <div className="text-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <h3 className="font-medium text-lg mb-2">🔄 Initialisation en cours...</h3>
          <p className="text-sm text-muted-foreground">
            La base de données se prépare. Veuillez patienter quelques instants.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 space-y-4">
      {/* Header with balances */}
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="font-medium">🗓️ Paiement par Shift - {selectedStaff.name}</h3>
            <p className="text-sm text-muted-foreground">
              Total impayé: {totalUnpaidAmount.toLocaleString()} DA ({unpaidShifts.length} shifts)
            </p>
          </div>
          
          {/* Compact status legend */}
          <div className="flex items-center gap-3 text-xs">
            <div className="flex items-center gap-1">
              <div className="h-2.5 w-2.5 bg-primary rounded-full"></div>
              <span>Sélectionné</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-2.5 w-2.5 bg-green-500 rounded-full"></div>
              <span>Payé</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-2.5 w-2.5 bg-amber-500 rounded-full"></div>
              <span>Impayé</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="h-2.5 w-2.5 bg-red-500 rounded-full"></div>
              <span>Absent</span>
            </div>
          </div>
        </div>


      </div>

      {/* Calendar Navigation */}
      <div className="flex items-center justify-between border-b pb-2">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={() => {
              const prevMonth = new Date(currentMonth);
              prevMonth.setMonth(prevMonth.getMonth() - 1);
              setCurrentMonth(prevMonth);
            }}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="font-medium min-w-[100px] text-center">
            {format(currentMonth, 'MMMM yyyy')}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={() => {
              const nextMonth = new Date(currentMonth);
              nextMonth.setMonth(nextMonth.getMonth() + 1);
              setCurrentMonth(nextMonth);
            }}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        
        {selectedShifts.length > 0 && (
          <Badge variant="secondary" className="text-xs">
            {selectedShifts.length} sélectionnés
          </Badge>
        )}
      </div>

      {/* Compact Calendar Grid */}
      <div className="space-y-1">
        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-1">
          {['L', 'M', 'M', 'J', 'V', 'S', 'D'].map((day, index) => (
            <div key={index} className="h-6 flex items-center justify-center text-xs font-medium text-muted-foreground">
              {day}
            </div>
          ))}
        </div>
        
        {/* Calendar days in rows */}
        {Array.from({ length: Math.ceil(calendarDays.length / 7) }, (_, weekIndex) => (
          <div key={weekIndex} className="grid grid-cols-7 gap-1">
            {calendarDays.slice(weekIndex * 7, (weekIndex + 1) * 7).map(day => {
              const shift = getShiftForDate(day);
              const isSelected = shift ? selectedShifts.includes(shift.id) : false;
              const isCurrentMonth = isSameMonth(day, currentMonth);
              const isTodayDate = isToday(day);
              
              return (
                <div
                  key={day.toISOString()}
                  className={cn(
                    "h-8 border rounded flex items-center justify-center text-xs cursor-pointer transition-all relative",
                    !isCurrentMonth && "opacity-30 text-muted-foreground",
                    isTodayDate && "ring-1 ring-primary font-semibold",
                    shift && shift.attended && !shift.isPaid && "hover:scale-105 hover:shadow-sm",
                    !shift && "cursor-default"
                  )}
                  onClick={() => shift && !shift.isPaid && shift.attended && handleShiftToggle(shift.id)}
                >
                  <span className="relative z-10">{format(day, 'd')}</span>
                  
                  {/* Enhanced status indicator */}
                  {shift && (
                    <div className={cn(
                      "absolute inset-0 rounded transition-all",
                      isSelected && "bg-primary/20 border-primary border-2",
                      !isSelected && shift.isPaid && "bg-green-500/20 border-green-500/30 border",
                      !isSelected && !shift.isPaid && shift.attended && "bg-amber-500/20 border-amber-500/30 border",
                      !isSelected && !shift.attended && "bg-red-500/20 border-red-500/30 border"
                    )}>
                      {/* Small indicator dot */}
                      <div className={cn(
                        "absolute top-0.5 right-0.5 h-1.5 w-1.5 rounded-full",
                        isSelected && "bg-primary",
                        !isSelected && shift.isPaid && "bg-green-500",
                        !isSelected && !shift.isPaid && shift.attended && "bg-amber-500",
                        !isSelected && !shift.attended && "bg-red-500"
                      )} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>

      {/* Payment Input */}
      <div className="space-y-3 pt-2">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="payment-amount" className="text-sm">Montant à payer (DA)</Label>
            <Input
              id="payment-amount"
              type="number"
              value={paymentAmount || ''}
              onChange={(e) => handleAmountChange(e.target.value)}
              placeholder="Entrez le montant"
              min="0"
              step="1"
              className="mt-1"
            />
          </div>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Shifts (brut):</span>
              <span>{(selectedAmount || 0).toLocaleString()} DA</span>
            </div>
            
            {/* Bonus with inline toggle - Always visible */}
            <div className={cn(
              "flex justify-between items-center",
              (balances.pendingBonuses || 0) > 0 ? "text-green-600" : "text-green-600/50"
            )}>
              <div className="flex items-center gap-2">
                <span>+ Prime:</span>
                <Switch
                  checked={adjustmentToggles.bonusEnabled}
                  onCheckedChange={(checked) => handleAdjustmentToggle('bonusEnabled', checked)}
                  disabled={(balances.pendingBonuses || 0) === 0}
                  className="scale-75 data-[state=checked]:bg-green-600"
                />
              </div>
              <span className={cn(
                (balances.pendingBonuses || 0) === 0 && "opacity-30",
                !adjustmentToggles.bonusEnabled && (balances.pendingBonuses || 0) > 0 && "line-through opacity-50"
              )}>
                +{(balances.pendingBonuses || 0).toLocaleString()} DA
              </span>
            </div>
            
            {/* Deduction with inline toggle - Always visible */}
            <div className={cn(
              "flex justify-between items-center",
              (balances.pendingDeductions || 0) > 0 ? "text-red-600" : "text-red-600/50"
            )}>
              <div className="flex items-center gap-2">
                <span>- Déduction:</span>
                <Switch
                  checked={adjustmentToggles.deductionEnabled}
                  onCheckedChange={(checked) => handleAdjustmentToggle('deductionEnabled', checked)}
                  disabled={(balances.pendingDeductions || 0) === 0 || (selectedAmount || 0) < (balances.pendingDeductions || 0)}
                  className="scale-75 data-[state=checked]:bg-red-600"
                />
                {(balances.pendingDeductions || 0) > 0 && (selectedAmount || 0) < (balances.pendingDeductions || 0) && (
                  <span className="text-xs opacity-60">Insuffisant</span>
                )}
              </div>
              <span className={cn(
                (balances.pendingDeductions || 0) === 0 && "opacity-30",
                !adjustmentToggles.deductionEnabled && (balances.pendingDeductions || 0) > 0 && "line-through opacity-50"
              )}>
                -{(balances.pendingDeductions || 0).toLocaleString()} DA
              </span>
            </div>
            
            {/* Advance with inline toggle - Always visible */}
            <div className={cn(
              "flex justify-between items-center",
              (balances.advanceBalance || 0) > 0 ? "text-orange-600" : "text-orange-600/50"
            )}>
              <div className="flex items-center gap-2">
                <span>- Avance:</span>
                <Switch
                  checked={adjustmentToggles.advanceEnabled}
                  onCheckedChange={(checked) => handleAdjustmentToggle('advanceEnabled', checked)}
                  disabled={(balances.advanceBalance || 0) === 0 || (selectedAmount || 0) === 0}
                  className="scale-75 data-[state=checked]:bg-orange-600"
                />
                {(balances.advanceBalance || 0) > 0 && (selectedAmount || 0) < (balances.advanceBalance || 0) && (selectedAmount || 0) > 0 && (
                  <span className="text-xs opacity-60">Partiel</span>
                )}
                {(balances.advanceBalance || 0) > 0 && (selectedAmount || 0) === 0 && (
                  <span className="text-xs opacity-60">Insuffisant</span>
                )}
              </div>
              <span className={cn(
                (balances.advanceBalance || 0) === 0 && "opacity-30",
                !adjustmentToggles.advanceEnabled && (balances.advanceBalance || 0) > 0 && "line-through opacity-50"
              )}>
                -{Math.min((selectedAmount || 0), (balances.advanceBalance || 0)).toLocaleString()} DA
              </span>
            </div>
            
            <div className="flex justify-between font-medium border-t pt-1">
              <span>Total Net:</span>
              <span className="text-primary">{(finalAmount || 0).toLocaleString()} DA</span>
            </div>
          </div>
        </div>

        {/* Quick amount buttons */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPaymentAmount(totalUnpaidAmount)}
            disabled={totalUnpaidAmount === 0}
          >
            Tout payer ({totalUnpaidAmount.toLocaleString()})
          </Button>
          {unpaidShifts.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPaymentAmount(unpaidShifts[0].rate)}
            >
              1 Shift ({unpaidShifts[0].rate.toLocaleString()})
            </Button>
          )}
        </div>

        {/* Payment warnings */}
        {!canProcessPayment && selectedShifts.length > 0 && (
          <div className="p-2 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700">
            ⚠️ Impossible de traiter le paiement: le montant net serait négatif.
            Désactivez certains ajustements ou sélectionnez plus de shifts.
          </div>
        )}

        {/* Payment Note */}
        <div className="space-y-1.5">
          <Label htmlFor="payment-note" className="text-xs">Note (Optionnel)</Label>
          <Textarea
            id="payment-note"
            value={paymentNote}
            onChange={(e) => setPaymentNote(e.target.value)}
            placeholder="Ajouter une note pour ce paiement..."
            rows={2}
            className="text-sm"
          />
        </div>

        {/* Payment Action */}
        <Button
          onClick={handlePayment}
          disabled={selectedShifts.length === 0 || isProcessing || !canProcessPayment || !isInitialized}
          className="w-full"
          variant={canProcessPayment && isInitialized ? "default" : "secondary"}
        >
          <DollarSign className="h-4 w-4 mr-2" />
          {!isInitialized ? "Base de données en cours d'initialisation..." :
           isProcessing ? "Traitement..." : 
           !canProcessPayment ? `Paiement impossible (${(finalAmount || 0).toLocaleString()} DA)` :
           `Payer ${selectedShifts.length} Shifts (${(finalAmount || 0).toLocaleString()} DA)`}
        </Button>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmer le paiement</DialogTitle>
            <DialogDescription>
              Veuillez vérifier les détails du paiement avant de confirmer.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-3 py-4">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Personnel:</span>
              <span className="text-sm font-medium">{selectedStaff?.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Type:</span>
              <span className="text-sm font-medium">Paiement par shift</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Shifts sélectionnés:</span>
              <span className="text-sm font-medium">{selectedShifts.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Montant brut:</span>
              <span className="text-sm font-medium">{(selectedAmount || 0).toLocaleString()} DA</span>
            </div>
            {adjustmentToggles.bonusEnabled && (balances.pendingBonuses || 0) > 0 && (
              <div className="flex justify-between text-green-600">
                <span className="text-sm">Prime incluse:</span>
                <span className="text-sm font-medium">+{(balances.pendingBonuses || 0).toLocaleString()} DA</span>
              </div>
            )}
            {adjustmentToggles.deductionEnabled && (balances.pendingDeductions || 0) > 0 && (
              <div className="flex justify-between text-red-600">
                <span className="text-sm">Déduction incluse:</span>
                <span className="text-sm font-medium">-{(balances.pendingDeductions || 0).toLocaleString()} DA</span>
              </div>
            )}
            {adjustmentToggles.advanceEnabled && (balances.advanceBalance || 0) > 0 && (
              <div className="flex justify-between text-orange-600">
                <span className="text-sm">Remboursement avance:</span>
                <span className="text-sm font-medium">-{Math.min((selectedAmount || 0), (balances.advanceBalance || 0)).toLocaleString()} DA</span>
              </div>
            )}
            <div className="flex justify-between border-t pt-2">
              <span className="text-sm font-semibold">Montant net:</span>
              <span className="text-sm font-semibold text-primary">{(finalAmount || 0).toLocaleString()} DA</span>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={isProcessing}
            >
              Annuler
            </Button>
            <Button
              onClick={confirmPayment}
              disabled={isProcessing}
              className="min-w-[120px]"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Traitement...
                </>
              ) : (
                "Confirmer le paiement"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}