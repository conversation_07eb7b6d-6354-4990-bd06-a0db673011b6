'use client';

import React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { StaffMember } from '@/lib/types/staff';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, Phone, Mail, DollarSign } from 'lucide-react';

interface StaffInfoHeaderProps {
  staff: StaffMember;
}

export function StaffInfoHeader({ staff }: StaffInfoHeaderProps) {
  const getStaffInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const formatPaymentInfo = (staff: StaffMember) => {
    const { paymentConfig } = staff;
    const type = paymentConfig.type;
    if (type === 'PER_SHIFT') {
      if (paymentConfig.shiftRates && Object.keys(paymentConfig.shiftRates).length > 0) {
        return `Taux variables / service`;
      } else {
        return `${paymentConfig.shiftRate?.toLocaleString() || 0} DA/service`;
      }
    } else if (type === 'WEEKLY') {
      return `${paymentConfig.baseSalary.toLocaleString()} DA/semaine`;
    } else {
      return `${paymentConfig.baseSalary.toLocaleString()} DA/mois`;
    }
  };

  const getWorkDuration = (staff: StaffMember) => {
    if (!staff.startDate && !(staff as any).createdAt) return 'N/A';
    const startDate = new Date(staff.startDate || (staff as any).createdAt || '');
    return formatDistanceToNow(startDate, { addSuffix: true, locale: fr });
  };

  return (
    <div className="flex items-start gap-3">
      <Avatar className="h-14 w-14 text-lg">
        <AvatarFallback>{getStaffInitials(staff.name)}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="text-lg font-bold">{staff.name}</h3>
          <Badge variant="outline" className="text-xs">{staff.role}</Badge>
          <Badge variant={staff.status === 'ACTIVE' ? 'default' : 'secondary'} className="text-xs">
            {staff.status === 'ACTIVE' ? 'Actif' : 'Inactif'}
          </Badge>
        </div>
        <div className="flex flex-wrap items-center gap-x-3 gap-y-0.5 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <DollarSign className="h-3.5 w-3.5" /> {formatPaymentInfo(staff)}
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-3.5 w-3.5" /> Travaille depuis {getWorkDuration(staff)}
          </div>
          {staff.phone && (
            <div className="flex items-center gap-1">
              <Phone className="h-3.5 w-3.5" /> {staff.phone}
            </div>
          )}
          {staff.email && (
            <div className="flex items-center gap-1">
              <Mail className="h-3.5 w-3.5" /> {staff.email}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 