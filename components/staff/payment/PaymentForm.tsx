'use client';

import React from 'react';
import { StaffMember } from '@/lib/types/staff';
import { PaymentMode } from './StaffPaymentSystem';
import { MonthlySalaryForm } from './forms/MonthlySalaryForm';
import { SimplePaymentForm } from './forms/SimplePaymentForm';
import { PerShiftPaymentForm } from './forms/PerShiftPaymentForm';

interface PaymentFormProps {
  staff: StaffMember;
  paymentMode: PaymentMode;
  onPaymentComplete: () => void;
}

export function PaymentForm({ staff, paymentMode, onPaymentComplete }: PaymentFormProps) {
  const renderPaymentForm = () => {
    switch (paymentMode) {
      case 'salary':
        return (
          <MonthlySalaryForm
            selectedStaff={staff}
            onPaymentSuccess={onPaymentComplete}
          />
        );
      case 'per-shift':
        return (
          <PerShiftPaymentForm
            selectedStaff={staff}
            onPaymentSuccess={onPaymentComplete}
          />
        );
      case 'bonus':
      case 'advance':
      case 'deduction':
        return (
          <SimplePaymentForm
            selectedStaff={staff}
            paymentMode={paymentMode}
            onPaymentSuccess={onPaymentComplete}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {renderPaymentForm()}
    </div>
  );
} 