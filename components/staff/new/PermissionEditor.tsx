'use client';

import React, { useState, useEffect } from 'react';
import { User, Permissions, PagePermission, ComponentPermission, ActionPermission } from '../../../lib/auth/types';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { usePermissions } from '@/lib/context/permissions';
import { Switch } from "../../ui/switch";
import { Button } from "../../ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../ui/tabs";

// Props for the component
interface PermissionEditorProps {
  user: User;
  onSave: (userId: string, permissions: Permissions) => Promise<void>;
}

export function PermissionEditor({ user, onSave }: PermissionEditorProps) {
  const { isOwner } = useAuth();
  const { isOwner: permissionIsOwner } = usePermissions();
  const [permissions, setPermissions] = useState<Permissions>(user.permissions);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  
  // Reset permissions when user changes
  useEffect(() => {
    setPermissions(user.permissions);
  }, [user]);
  
  // Check if current user can edit permissions (owners/admins only)
  const canEdit = isOwner || permissionIsOwner;
  
  // Handle permission toggles
  const handlePagePermissionToggle = (pageName: string, access: boolean) => {
    setPermissions(prev => ({
      ...prev,
      pages: prev.pages.map(p => 
        p.page === pageName ? { ...p, access } : p
      )
    }));
  };
  
  const handleComponentPermissionToggle = (pageName: string, componentName: string, access: boolean) => {
    setPermissions(prev => ({
      ...prev,
      components: prev.components.map(c => 
        c.page === pageName && c.component === componentName 
          ? { ...c, access } 
          : c
      )
    }));
  };
  
  const handleActionPermissionToggle = (actionName: string, resourceName: string, access: boolean) => {
    setPermissions(prev => ({
      ...prev,
      actions: prev.actions.map(a => 
        a.action === actionName && a.resource === resourceName 
          ? { ...a, access } 
          : a
      )
    }));
  };
  
  // Handle save
  const handleSave = async () => {
    if (!canEdit) return;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      await onSave(user.id, permissions);
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save permissions');
    } finally {
      setIsSaving(false);
    }
  };
  
  // Group components by page for better organization
  const componentsByPage: Record<string, ComponentPermission[]> = {};
  permissions.components.forEach(comp => {
    if (!componentsByPage[comp.page]) {
      componentsByPage[comp.page] = [];
    }
    componentsByPage[comp.page].push(comp);
  });
  
  // Group actions by resource for better organization
  const actionsByResource: Record<string, ActionPermission[]> = {};
  permissions.actions.forEach(action => {
    if (!actionsByResource[action.resource]) {
      actionsByResource[action.resource] = [];
    }
    actionsByResource[action.resource].push(action);
  });

  // Show message if user doesn't have permission to edit
  if (!canEdit) {
    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="text-center py-8">
          <h2 className="text-xl font-bold mb-4">Permission Editor</h2>
          <p className="text-gray-600">You don't have permission to edit staff permissions.</p>
          <p className="text-sm text-gray-500 mt-2">Only owners and administrators can modify permissions.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Permissions for {user.name}</h2>
        <Button 
          onClick={handleSave} 
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
      
      {saveError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {saveError}
        </div>
      )}
      
      <Tabs defaultValue="pages">
        <TabsList className="mb-4">
          <TabsTrigger value="pages">Pages</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>
        
        {/* Pages Tab */}
        <TabsContent value="pages">
          <div className="space-y-4">
            <p className="text-sm text-gray-500 mb-4">
              Control which pages this user can access
            </p>
            
            <div className="grid gap-4">
              {permissions.pages.map((page) => (
                <div key={page.page} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <span className="font-medium">{page.page}</span>
                  </div>
                  <Switch
                    checked={page.access}
                    onCheckedChange={(access) => handlePagePermissionToggle(page.page, access)}
                    disabled={!canEdit}
                  />
                </div>
              ))}
            </div>
          </div>
        </TabsContent>
        
        {/* Components Tab */}
        <TabsContent value="components">
          <div className="space-y-4">
            <p className="text-sm text-gray-500 mb-4">
              Control which UI components this user can access within each page
            </p>
            
            <Accordion type="multiple" className="w-full">
              {Object.entries(componentsByPage).map(([pageName, components]) => (
                <AccordionItem key={pageName} value={pageName}>
                  <AccordionTrigger className="text-lg font-medium">
                    {pageName} ({components.filter(c => c.access).length}/{components.length})
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="grid gap-2 pl-2">
                      {components.map((comp) => (
                        <div key={comp.component} className="flex items-center justify-between p-2 border-b">
                          <div>
                            <span>{comp.component}</span>
                          </div>
                          <Switch
                            checked={comp.access}
                            onCheckedChange={(access) => 
                              handleComponentPermissionToggle(pageName, comp.component, access)
                            }
                            disabled={!canEdit}
                          />
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </TabsContent>
        
        {/* Actions Tab */}
        <TabsContent value="actions">
          <div className="space-y-4">
            <p className="text-sm text-gray-500 mb-4">
              Control which actions this user can perform on different resources
            </p>
            
            <Accordion type="multiple" className="w-full">
              {Object.entries(actionsByResource).map(([resourceName, actions]) => (
                <AccordionItem key={resourceName} value={resourceName}>
                  <AccordionTrigger className="text-lg font-medium">
                    {resourceName} ({actions.filter(a => a.access).length}/{actions.length})
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="grid gap-2 pl-2">
                      {actions.map((action) => (
                        <div key={action.action} className="flex items-center justify-between p-2 border-b">
                          <div>
                            <span className="capitalize">{action.action}</span>
                          </div>
                          <Switch
                            checked={action.access}
                            onCheckedChange={(access) => 
                              handleActionPermissionToggle(action.action, resourceName, access)
                            }
                            disabled={!canEdit}
                          />
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 