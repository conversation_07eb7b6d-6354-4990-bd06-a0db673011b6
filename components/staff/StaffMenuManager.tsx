'use client';

import React, { useState, useEffect, useCallback, useImperativeHandle, forwardRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Users, X, CheckCircle, ShoppingCart, Receipt } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { createStaffOrder } from "@/lib/services/staff-menu-service";

// Types
interface StaffMember {
  id: string;
  name: string;
  role: string;
  currentShift: {
    id: string;
    name: string;
  };
  attendanceRecord: any;
}

interface StaffMenuItem {
  id: string;
  itemName: string;
  staffPrice: number;
  menuItemId: string;
  size?: string;
  originalPrice: number;
  categoryName: string;
}

interface StaffAllowance {
  used: number;
  remaining: number;
}

interface StaffMenuManagerProps {
  config?: { isEnabled: boolean; allowancePerShift: number };
  menuItems: StaffMenuItem[];
  currentUser: any;
  onStaffSelect?: (staffId: string | null) => void;
  onItemAdd?: (staffId: string, item: StaffMenuItem) => boolean;
}

interface StaffSelection {
  staff: StaffMember;
  items: (StaffMenuItem & { selectedId: string })[];
}

interface StaffMenuManagerRef {
  addItemToSelectedStaff: (item: StaffMenuItem) => boolean;
  getSelectedStaffId: () => string | null;
  canAddItemToStaff: (staffId: string) => boolean;
}

const StaffMenuManager = forwardRef<StaffMenuManagerRef, StaffMenuManagerProps>(
  ({ config, menuItems, currentUser, onStaffSelect, onItemAdd }, ref) => {
    
    // State
    const [selectedStaffId, setSelectedStaffId] = useState<string | null>(null);
    const [staffSelections, setStaffSelections] = useState<Record<string, StaffSelection>>({});
    const [presentStaff, setPresentStaff] = useState<StaffMember[]>([]);
    const [staffAllowances, setStaffAllowances] = useState<Record<string, StaffAllowance>>({});
    const [isLoading, setIsLoading] = useState(true);
    const { toast } = useToast();
    const [isProcessing, setIsProcessing] = useState(false);

    // Mock data for present staff (in real app, this would come from attendance/shift system)
    useEffect(() => {
      // Simulate loading staff data
      const mockPresentStaff: StaffMember[] = [
        {
          id: 'staff-1',
          name: 'daada',
          role: 'Serveur',
          currentShift: { id: 'shift-1', name: 'Matin' },
          attendanceRecord: { checkedIn: true }
        },
        {
          id: 'staff-2', 
          name: 'jjjj',
          role: 'Cuisinier',
          currentShift: { id: 'shift-1', name: 'Matin' },
          attendanceRecord: { checkedIn: true }
        }
      ];

      // Initialize allowances based on config
      const allowancePerShift = config?.allowancePerShift || 1;
      const allowances: Record<string, StaffAllowance> = {};
      
      mockPresentStaff.forEach(staff => {
        allowances[staff.id] = {
          used: 0,
          remaining: allowancePerShift
        };
      });

      setPresentStaff(mockPresentStaff);
      setStaffAllowances(allowances);
      setIsLoading(false);
    }, [config]);

    // Staff selection handler
    const handleStaffSelect = useCallback((staffId: string) => {
      setSelectedStaffId(staffId);
      if (onStaffSelect) {
        onStaffSelect(staffId);
      }
    }, [onStaffSelect]);

    // Check if staff can add more items
    const canAddItemToStaff = useCallback((staffId: string): boolean => {
      const allowance = staffAllowances[staffId];
      if (!allowance) return false;
      
      const currentItems = staffSelections[staffId]?.items || [];
      return currentItems.length < allowance.remaining;
    }, [staffAllowances, staffSelections]);

    // Add item to a specific staff member
    const addItemToStaff = useCallback((staffId: string, menuItem: StaffMenuItem): boolean => {
      // Check quota ONLY when trying to add
      if (!canAddItemToStaff(staffId)) {
        toast({
          title: "Quota atteint",
          description: `${presentStaff.find(s => s.id === staffId)?.name || 'Ce membre'} a déjà atteint sa limite d'articles.`,
          variant: "destructive"
        });
        return false;
      }

      const staff = presentStaff.find(s => s.id === staffId);
      if (!staff) return false;

      const selectedItem = {
        ...menuItem,
        selectedId: `${menuItem.id}-${Date.now()}`
      };

      setStaffSelections(prev => ({
        ...prev,
        [staffId]: {
          staff,
          items: [...(prev[staffId]?.items || []), selectedItem]
        }
      }));

      // Auto-select staff when item is added
      setSelectedStaffId(staffId);

      // Show success feedback
      toast({
        title: "Article ajouté",
        description: `${menuItem.itemName} ajouté pour ${staff.name}`,
      });

      // Notify parent component if callback provided
      if (onItemAdd) {
        onItemAdd(staffId, menuItem);
      }

      return true;
    }, [canAddItemToStaff, presentStaff, onItemAdd, staffSelections, staffAllowances, toast]);

    // Add item to currently selected staff or auto-select available staff
    const addItemToSelectedStaff = useCallback((item: StaffMenuItem): boolean => {
      // If staff is selected, add to them
      if (selectedStaffId) {
        return addItemToStaff(selectedStaffId, item);
      }
      
      // Auto-select first available staff member who can accept items
      const availableStaff = presentStaff.find(staff => canAddItemToStaff(staff.id));
      if (availableStaff) {
        return addItemToStaff(availableStaff.id, item);
      }
      
      // Show message if no staff can accept items
      toast({
        title: "Aucun personnel disponible",
        description: "Tous les membres du personnel ont atteint leur quota d'articles.",
        variant: "destructive"
      });
      return false;
    }, [selectedStaffId, addItemToStaff, presentStaff, canAddItemToStaff, toast]);

    // Remove item from staff
    const removeItemFromStaff = useCallback((staffId: string, itemIndex: number) => {
      setStaffSelections(prev => {
        const currentSelection = prev[staffId];
        if (!currentSelection) return prev;
        
        const newItems = currentSelection.items.filter((_, index) => index !== itemIndex);
        
        if (newItems.length === 0) {
          const { [staffId]: removed, ...rest } = prev;
          return rest;
        }
        
        return {
          ...prev,
          [staffId]: {
            ...currentSelection,
            items: newItems
          }
        };
      });
    }, []);

    // Get current staff ID
    const getSelectedStaffId = useCallback(() => selectedStaffId, [selectedStaffId]);

    // Add method to get best staff for item
    const getBestStaffForItem = useCallback((): string | null => {
      if (selectedStaffId && canAddItemToStaff(selectedStaffId)) {
        return selectedStaffId;
      }
      return presentStaff.find(staff => canAddItemToStaff(staff.id))?.id || null;
    }, [selectedStaffId, canAddItemToStaff, presentStaff]);

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      addItemToSelectedStaff,
      getSelectedStaffId,
      canAddItemToStaff,
      getBestStaffForItem,
      addItemToStaff,
    }));

    // Get total items across all staff
    const getTotalItems = useCallback(() => {
      return Object.values(staffSelections).reduce((total, selection) => 
        total + selection.items.length, 0
      );
    }, [staffSelections]);

    // Add confirmation handler for staff orders
    const confirmStaffOrders = useCallback(async () => {
      if (getTotalItems() === 0) return;
      
      setIsProcessing(true);
      try {
        // Collect all items from all staff
        const allStaffItems: Array<{
          staffMenuItem: import("@/lib/db/v4/schemas/staff-menu-schema").StaffMenuItem;
          quantity: number;
          addons?: import("@/lib/db/v4/schemas/order-schema").OrderAddon[];
          notes?: string;
        }> = [];
        
        const staffNames: string[] = [];
        
        Object.entries(staffSelections).forEach(([staffId, selection]) => {
          if (selection.items.length > 0) {
            staffNames.push(selection.staff.name);
            selection.items.forEach(item => {
              allStaffItems.push({
                staffMenuItem: {
                  id: item.id,
                  menuItemId: item.menuItemId,
                  itemName: item.itemName,
                  categoryName: item.categoryName,
                  size: item.size || 'default',
                  originalPrice: item.originalPrice,
                  staffPrice: 0, // Force 0 pricing for simplicity
                  isActive: true // Default to active
                },
                quantity: 1,
                addons: [],
                notes: `Pour ${selection.staff.name}`
              });
            });
          }
        });
        
        if (allStaffItems.length === 0) return;
        
        // Create one big staff order using the first staff member's info as representative
        const firstStaff = Object.values(staffSelections)[0].staff;
        const result = await createStaffOrder(
          'staff-collective', // Use collective ID
          `Commande Équipe (${staffNames.join(', ')})`, // Combined names
          firstStaff.currentShift.id,
          firstStaff.currentShift.name,
          allStaffItems,
          {
            notes: `Commande collective pour ${staffNames.length} membres du personnel: ${staffNames.join(', ')}`
          }
        );
        
        if (result.success) {
          toast({
            title: "✅ Commande équipe créée",
            description: `${allStaffItems.length} items commandés pour ${staffNames.length} membres du personnel`,
          });
          
          // Clear all selections
          setStaffSelections({});
          setSelectedStaffId(null);
          
        } else {
          toast({
            title: "❌ Erreur",
            description: result.error || "Impossible de créer la commande équipe",
            variant: "destructive"
          });
        }
        
      } catch (error: any) {
        console.error('Error confirming staff orders:', error);
        toast({
          title: "❌ Erreur",
          description: "Erreur lors de la création de la commande équipe",
          variant: "destructive"
        });
      } finally {
        setIsProcessing(false);
      }
    }, [staffSelections, getTotalItems, toast]);

    // Render loading state
    if (isLoading) {
      return (
        <div className="p-3 border border-dashed border-muted-foreground/30 rounded-lg">
          <div className="animate-pulse space-y-2">
            <div className="h-3 bg-muted rounded w-1/3"></div>
            <div className="h-12 bg-muted rounded"></div>
          </div>
        </div>
      );
    }

    // Render no staff state
    if (presentStaff.length === 0) {
      return (
        <div className="p-3 border border-dashed border-muted-foreground/30 rounded-lg text-center">
          <Users className="h-8 w-8 mx-auto mb-1 opacity-50" />
          <p className="text-sm text-muted-foreground">Aucun personnel présent</p>
        </div>
      );
    }

    return (
      <div className="p-3 space-y-2.5">
        {/* Header with instructions */}
        <div className="mb-2">
          <div className="flex items-center justify-between mb-1">
            <h4 className="text-xs font-medium flex items-center gap-1.5 text-gray-600">
              <Users className="h-3.5 w-3.5" />
              Menu Personnel
            </h4>
            {selectedStaffId && (
              <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
                {presentStaff.find(s => s.id === selectedStaffId)?.name} sélectionné
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-500 leading-relaxed">
            {selectedStaffId 
              ? "Sélectionnez des articles pour les ajouter automatiquement."
              : "Sélectionnez un membre ou ajoutez directement des articles."}
          </p>
        </div>

        {/* Staff Selection Grid */}
        <div className="space-y-1.5">
          {presentStaff.map((staff) => {
            const allowance = staffAllowances[staff.id] || { used: 0, remaining: 1 };
            const currentItems = staffSelections[staff.id]?.items || [];
            const isSelected = selectedStaffId === staff.id;
            const hasItems = currentItems.length > 0;
            
            return (
              <div
                key={staff.id}
                onClick={() => handleStaffSelect(staff.id)}
                className={cn(
                  "w-full p-2.5 border rounded-md cursor-pointer transition-all duration-150",
                  isSelected 
                    ? "border-blue-400 bg-blue-50 shadow-sm" 
                    : hasItems
                    ? "border-green-400 bg-green-50/50"
                    : "border-gray-200 hover:border-gray-300 hover:bg-gray-50/50"
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "h-1.5 w-1.5 rounded-full transition-colors",
                      isSelected ? "bg-blue-500" : hasItems ? "bg-green-500" : "bg-gray-300"
                    )}></div>
                    <span className={cn(
                      "text-sm font-medium transition-colors select-none",
                      isSelected ? "text-blue-700" : hasItems ? "text-green-700" : "text-gray-700"
                    )}>
                      {staff.name}
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Badge variant={hasItems ? "default" : "outline"} className={cn(
                      "text-xs px-1.5 py-0.5 h-5 font-medium pointer-events-none",
                      isSelected ? "bg-blue-100 text-blue-700 border-blue-200" :
                      hasItems ? "bg-green-100 text-green-700 border-green-200" : ""
                    )}>
                      {currentItems.length}/{allowance.remaining}
                    </Badge>
                    {isSelected && <CheckCircle className="h-3.5 w-3.5 text-blue-500" />}
                  </div>
                </div>
                
                {/* Current items - Compact inline display */}
                {hasItems && (
                  <div className="mt-2 space-y-1">
                    {currentItems.map((item, index) => (
                      <div key={item.selectedId} className="flex justify-between items-center text-xs bg-white/70 rounded-sm px-2 py-1 group">
                        <span className="text-gray-600 truncate select-none">• {item.itemName}</span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeItemFromStaff(staff.id, index);
                          }}
                          className="opacity-0 group-hover:opacity-100 h-4 w-4 text-red-400 hover:text-red-600 flex items-center justify-center transition-all duration-150"
                          aria-label="Remove item"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Summary with staff breakdown */}
        {getTotalItems() > 0 && (
          <div className="space-y-3 pt-3 mt-3 border-t border-gray-200 bg-gray-50/30 rounded-md p-3 -mx-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5 text-xs font-medium text-gray-700">
                <ShoppingCart className="h-3.5 w-3.5" />
                Résumé de la commande
              </div>
              <Badge variant="default" className="text-xs px-2 py-0.5 h-5 bg-blue-100 text-blue-700">
                {getTotalItems()} articles • {Object.keys(staffSelections).length} personnes
              </Badge>
            </div>
            
            {/* Staff breakdown */}
            <div className="space-y-1.5">
              {Object.entries(staffSelections).map(([staffId, selection]) => (
                <div key={staffId} className="flex justify-between items-center text-xs bg-white/60 rounded px-2 py-1">
                  <span className="font-medium text-gray-700">{selection.staff.name}</span>
                  <span className="text-gray-500">{selection.items.length} article{selection.items.length > 1 ? 's' : ''}</span>
                </div>
              ))}
            </div>
            
            {/* Confirmation Button */}
            <Button 
              onClick={confirmStaffOrders}
              className="w-full h-10 bg-green-600 hover:bg-green-700 text-white text-sm font-semibold rounded-md transition-all duration-150 shadow-sm hover:shadow-md"
              disabled={isProcessing || getTotalItems() === 0}
            >
              {isProcessing ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>Traitement en cours...</span>
                </div>
              ) : (
                <>
                  <Receipt className="h-4 w-4 mr-2" />
                  <span>Confirmer Commande Équipe ({getTotalItems()} articles)</span>
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    );
  }
);

StaffMenuManager.displayName = 'StaffMenuManager';

export default StaffMenuManager; 