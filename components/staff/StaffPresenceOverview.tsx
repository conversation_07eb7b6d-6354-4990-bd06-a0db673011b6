"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, UserCheck, UserX, Clock } from "lucide-react";

interface StaffPresenceStats {
  totalStaff: number;
  presentStaff: number;
  absentStaff: number;
  lateStaff: number;
  attendanceRate: number;
  shiftDistribution: {
    morning: number;
    afternoon: number;
    night: number;
  };
}

interface StaffMember {
  id: string;
  name: string;
  status: "present" | "absent" | "late";
  checkInTime?: string;
  shift?: string;
}

interface StaffPresenceOverviewProps {
  stats: StaffPresenceStats;
  presentStaff: StaffMember[];
  absentStaff: StaffMember[];
}

export function StaffPresenceOverview({
  stats,
  presentStaff,
  absentStaff,
}: StaffPresenceOverviewProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Staff Presence</CardTitle>
        </Card<PERSON>eader>
        <CardContent>
          <div className="grid gap-4">
            {presentStaff.map((staff) => (
              <div
                key={staff.id}
                className="flex items-center justify-between p-2 rounded-md bg-muted/50"
              >
                <div className="flex items-center gap-2">
                  <div className="font-medium">{staff.name}</div>
                  <Badge variant={staff.status === "present" ? "default" : "destructive"}>
                    {staff.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  {staff.shift && <span>{staff.shift}</span>}
                  {staff.checkInTime && <span>{staff.checkInTime}</span>}
                </div>
              </div>
            ))}
            {presentStaff.length === 0 && (
              <div className="text-sm text-muted-foreground text-center py-4">
                No staff present yet
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 