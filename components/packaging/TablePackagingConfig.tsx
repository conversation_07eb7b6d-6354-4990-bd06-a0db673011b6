"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Package } from 'lucide-react';
import { PackagingItem } from '@/lib/db/v4/schemas/table-schema';
import { updateTablePackaging, getTablePackaging } from '@/lib/db/v4';
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useToast } from '@/components/ui/use-toast';

interface TablePackagingConfigProps {
  tableId: string;
  tableName: string;
  onUpdate?: () => void;
}

export function TablePackagingConfig({ tableId, tableName, onUpdate }: TablePackagingConfigProps) {
  const [packaging, setPackaging] = useState<PackagingItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [newItem, setNewItem] = useState<{ stockItemId: string; quantity: number }>({
    stockItemId: '',
    quantity: 1
  });

  const { stockItems } = useStockV4();
  const { toast } = useToast();

  // Filter stock items to only show packaging items
  const packagingStockItems = stockItems.filter(item => 
    item.category.toLowerCase().includes('emballage') || 
    item.category.toLowerCase().includes('packaging') ||
    item.name.toLowerCase().includes('napkin') ||
    item.name.toLowerCase().includes('plate') ||
    item.name.toLowerCase().includes('cup') ||
    item.name.toLowerCase().includes('bag') ||
    item.name.toLowerCase().includes('box') ||
    item.name.toLowerCase().includes('container')
  );

  useEffect(() => {
    loadPackaging();
  }, [tableId]);

  const loadPackaging = async () => {
    try {
      setIsLoading(true);
      const tablePackaging = await getTablePackaging(tableId);
      setPackaging(tablePackaging);
    } catch (error) {
      console.error('Error loading table packaging:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger la configuration d'emballage",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddItem = () => {
    if (!newItem.stockItemId || newItem.quantity <= 0) {
      return;
    }

    // Check if item already exists
    const existingIndex = packaging.findIndex(p => p.stockItemId === newItem.stockItemId);
    if (existingIndex >= 0) {
      // Update existing item quantity
      const updatedPackaging = [...packaging];
      updatedPackaging[existingIndex].quantity += newItem.quantity;
      setPackaging(updatedPackaging);
    } else {
      // Add new item
      setPackaging([...packaging, { ...newItem }]);
    }

    // Reset form
    setNewItem({ stockItemId: '', quantity: 1 });
  };

  const handleRemoveItem = (index: number) => {
    setPackaging(packaging.filter((_, i) => i !== index));
  };

  const handleUpdateQuantity = (index: number, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveItem(index);
      return;
    }

    const updatedPackaging = [...packaging];
    updatedPackaging[index].quantity = quantity;
    setPackaging(updatedPackaging);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      await updateTablePackaging(tableId, packaging);
      
      toast({
        title: "✅ Succès",
        description: `Configuration d'emballage mise à jour pour ${tableName}`,
      });

      onUpdate?.();
    } catch (error) {
      console.error('Error saving table packaging:', error);
      toast({
        title: "Erreur",
        description: "Impossible de sauvegarder la configuration",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getStockItemName = (stockItemId: string) => {
    const item = stockItems.find(item => item.id === stockItemId);
    return item ? `${item.name} (${item.unit})` : 'Article inconnu';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Configuration d'emballage - {tableName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Configuration d'emballage - {tableName}
        </CardTitle>
        <div className="text-sm text-muted-foreground">
          Définissez les articles d'emballage consommés automatiquement lors des commandes sur place pour cette table.
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current packaging items */}
        {packaging.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Articles configurés</Label>
            {packaging.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg bg-muted/20">
                <div className="flex-1">
                  <span className="font-medium">{getStockItemName(item.stockItemId)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="0"
                    step="0.1"
                    value={item.quantity}
                    onChange={(e) => handleUpdateQuantity(index, parseFloat(e.target.value) || 0)}
                    className="w-20 h-8 text-center"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveItem(index)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add new item */}
        <div className="space-y-3 border-t pt-4">
          <Label className="text-sm font-medium">Ajouter un article</Label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="md:col-span-2">
              <Select
                value={newItem.stockItemId}
                onValueChange={(value) => setNewItem({ ...newItem, stockItemId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un article d'emballage" />
                </SelectTrigger>
                <SelectContent>
                  {packagingStockItems.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name} ({item.unit})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Input
                type="number"
                min="0.1"
                step="0.1"
                value={newItem.quantity}
                onChange={(e) => setNewItem({ ...newItem, quantity: parseFloat(e.target.value) || 1 })}
                placeholder="Quantité"
                className="flex-1"
              />
              <Button
                onClick={handleAddItem}
                disabled={!newItem.stockItemId || newItem.quantity <= 0}
                size="sm"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Save button */}
        <div className="flex justify-end pt-4 border-t">
          <Button 
            onClick={handleSave} 
            disabled={isSaving}
            className="flex items-center gap-2"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
            ) : (
              <Package className="h-4 w-4" />
            )}
            {isSaving ? 'Sauvegarde...' : 'Sauvegarder'}
          </Button>
        </div>

        {/* Info about when this applies */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="text-sm text-blue-800">
            <strong>ℹ️ Information :</strong> Cette configuration s'applique uniquement aux commandes 
            <Badge variant="outline" className="mx-1">sur place</Badge>
            pour cette table. Les articles seront automatiquement consommés lors du paiement de la commande.
          </div>
        </div>

        {/* Empty state */}
        {packaging.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">Aucun article d'emballage configuré</p>
            <p className="text-xs">Ajoutez des articles qui seront consommés automatiquement pour cette table</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 