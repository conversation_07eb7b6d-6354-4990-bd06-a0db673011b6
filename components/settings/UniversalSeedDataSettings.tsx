"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Loader2, 
  ChefHat, 
  Trash2, 
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { 
  initializeUniversalSeedData, 
  checkSeedDataExists, 
  clearSeedData,
  type SeedDataResult 
} from '@/lib/db/v4';

export function UniversalSeedDataSettings() {
  const [hasMenu, setHasMenu] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<SeedDataResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Confirmation states
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');

  // Check if menu exists on component mount
  useEffect(() => {
    checkMenuStatus();
  }, []);

  const checkMenuStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const status = await checkSeedDataExists();
      setHasMenu(status.menuDocument);
    } catch (err) {
      setError('Could not check menu status');
      console.error('Menu check error:', err);
    } finally {
      setLoading(false);
    }
  };

  const resetConfirmations = () => {
    setShowDeleteConfirm(false);
    setConfirmationText('');
  };

  const createStarterMenu = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      console.log('🌱 Creating starter menu...');
      
      const seedResult = await initializeUniversalSeedData({
        skipIfExists: true,
        overwrite: false
      });

      if (seedResult.success) {
        setResult({
          success: true,
          message: `Complete restaurant setup created successfully! You now have ${seedResult.details.stockItems} ingredients, ${seedResult.details.subRecipes} recipes, a full menu, and ${seedResult.details.menuItemRecipes} item recipes ready to use.`,
          details: seedResult.details
        });
        await checkMenuStatus();
      } else {
        throw new Error(seedResult.message || 'Failed to create restaurant setup');
      }
      
    } catch (err) {
      console.error('Restaurant setup creation error:', err);
      setError('Could not create complete restaurant setup. Please try again or contact support.');
    } finally {
      setLoading(false);
    }
  };

  const replaceWithStarterMenu = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      console.log('🔄 Replacing with starter menu...');
      
      const seedResult = await initializeUniversalSeedData({
        skipIfExists: false,
        overwrite: true
      });

      if (seedResult.success) {
        setResult({
          success: true,
          message: 'Restaurant system replaced successfully! Complete setup with menu, recipes, and ingredients is now ready.',
          details: seedResult.details
        });
        await checkMenuStatus();
        resetConfirmations();
      } else {
        throw new Error(seedResult.message || 'Failed to replace restaurant system');
      }
      
    } catch (err) {
      console.error('Menu replacement error:', err);
      setError('Could not replace menu. Please try again or contact support.');
    } finally {
      setLoading(false);
    }
  };

  const deleteAllMenuData = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      console.log('🗑️ Deleting all menu data...');
      
      await clearSeedData();
      
      setResult({
        success: true,
        message: 'All menu data has been deleted.',
        details: {
          stockItems: 0,
          subRecipes: 0,
          menuDocument: false,
          menuItemRecipes: 0
        }
      });
      
      await checkMenuStatus();
      resetConfirmations();
      
    } catch (err) {
      console.error('Menu deletion error:', err);
      setError('Could not delete menu data. Please try again or contact support.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Main Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChefHat className="w-5 h-5" />
            Complete Restaurant Setup
          </CardTitle>
          <CardDescription>
            Set up your entire restaurant with menu, recipes, ingredients, and pricing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {hasMenu === null ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              Checking menu...
            </div>
          ) : hasMenu ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-green-700 bg-green-50 p-3 rounded-lg">
                <CheckCircle className="w-4 h-4" />
                <span className="font-medium">Your restaurant system is set up</span>
              </div>
              
              <div className="pt-2 border-t">
                <p className="text-sm text-gray-600 mb-3">
                  Want to reset everything and start with our complete restaurant template?
                </p>
                <Button
                  onClick={() => setShowDeleteConfirm(true)}
                  variant="outline"
                  className="w-full border-orange-300 text-orange-700 hover:bg-orange-50"
                >
                  Replace with Complete Setup
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center py-4">
                <p className="text-gray-600 mb-4">
                  No restaurant data found. Set up your complete restaurant system with everything you need to start operating.
                </p>
                <Button
                  onClick={createStarterMenu}
                  disabled={loading}
                  className="w-full"
                  size="lg"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <ChefHat className="w-4 h-4 mr-2" />
                  )}
                  Set Up Complete Restaurant
                </Button>
              </div>
              
              <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded space-y-2">
                <p><strong>Complete restaurant setup includes:</strong></p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                  <div>
                    <strong>📋 Menu Items:</strong>
                    <br />• 6 Pizzas (500-2200 DA)
                    <br />• 5 Sandwiches (450-790 DA)
                    <br />• 4 Tacos (250-990 DA)
                    <br />• 6 Drinks (120-390 DA)
                  </div>
                  <div>
                    <strong>🧪 Complete System:</strong>
                    <br />• 50+ Stock ingredients
                    <br />• Recipe formulations
                    <br />• Cost calculations
                    <br />• Inventory tracking
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Section */}
      {result && (
        <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
          <AlertDescription>
            <p className={result.success ? "text-green-800" : "text-red-800"}>
              {result.message}
            </p>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Section */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-600">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Replace Menu Confirmation */}
      {showDeleteConfirm && (
        <Card className="border-orange-200">
          <CardHeader className="bg-orange-50">
            <CardTitle className="text-orange-700">Replace Current Menu?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            <Alert className="border-orange-300 bg-orange-100">
              <AlertTriangle className="w-4 h-4" />
              <AlertDescription className="text-orange-800">
                <strong>This will replace your current menu with the starter menu.</strong>
                <br />All devices will get the new menu. This cannot be undone.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-3">
              <div>
                <Label htmlFor="replace-confirm">Type "REPLACE" to confirm:</Label>
                <Input
                  id="replace-confirm"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  placeholder="REPLACE"
                  className="mt-1"
                />
              </div>
            </div>
            
            <div className="flex gap-3">
              <Button
                onClick={replaceWithStarterMenu}
                disabled={loading || confirmationText !== 'REPLACE'}
                variant="destructive"
                className="flex-1"
              >
                {loading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
                Replace Menu
              </Button>
              <Button
                variant="outline"
                onClick={resetConfirmations}
                disabled={loading}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}