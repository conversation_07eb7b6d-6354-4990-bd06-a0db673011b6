"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Cloud, 
  Check, 
  X, 
  Key, 
  Link, 
  Settings, 
  User, 
  ShieldCheck,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff,
  TestTube2,
  Trash2
} from "lucide-react";

interface GoogleDriveConfig {
  type: 'oauth' | 'service_account';
  // OAuth config
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  // Service Account config
  clientEmail?: string;
  privateKey?: string;
  projectId?: string;
}

interface ConnectionStatus {
  isConnected: boolean;
  type?: 'oauth' | 'service_account';
  user?: any;
  error?: string;
  lastTested?: string;
}

export function GoogleDriveSettings() {
  const { toast } = useToast();
  
  // State
  const [setupMode, setSetupMode] = useState<'easy' | 'pro'>('easy');
  const [activeTab, setActiveTab] = useState<'oauth' | 'service_account'>('oauth');
  const [isLoading, setIsLoading] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false
  });
  
  // OAuth form state (for pro mode)
  const [oauthConfig, setOauthConfig] = useState({
    clientId: '',
    clientSecret: '',
    refreshToken: ''
  });
  const [showOAuthSecrets, setShowOAuthSecrets] = useState({
    clientSecret: false,
    refreshToken: false
  });
  
  // Service Account form state (for pro mode)
  const [serviceAccountConfig, setServiceAccountConfig] = useState({
    clientEmail: '',
    privateKey: '',
    projectId: ''
  });
  const [showServiceAccountKey, setShowServiceAccountKey] = useState(false);

  // Load existing configuration on mount
  useEffect(() => {
    testConnection();
    loadSavedConfig();
  }, []);

  // One-click OAuth setup for easy mode
  const handleEasySetup = async () => {
    setIsLoading(true);
    try {
      // Create a popup window for OAuth
      const popup = window.open(
        '/api/google-drive/oauth/authorize',
        'google-oauth',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      // Listen for the OAuth completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          // Check if OAuth was successful
          testConnection();
          setIsLoading(false);
        }
      }, 1000);

      // Listen for messages from the popup
      const messageListener = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;
        
        if (event.data.type === 'GOOGLE_OAUTH_SUCCESS') {
          popup?.close();
          clearInterval(checkClosed);
          toast({
            title: "Success!",
            description: "Google Drive connected successfully.",
          });
          testConnection();
          setIsLoading(false);
          window.removeEventListener('message', messageListener);
        } else if (event.data.type === 'GOOGLE_OAUTH_ERROR') {
          popup?.close();
          clearInterval(checkClosed);
          toast({
            title: "Connection Failed",
            description: event.data.error || "Failed to connect to Google Drive.",
            variant: "destructive",
          });
          setIsLoading(false);
          window.removeEventListener('message', messageListener);
        }
      };

      window.addEventListener('message', messageListener);
      
    } catch (error) {
      console.error('Easy setup error:', error);
      toast({
        title: "Setup Failed",
        description: "Failed to start Google Drive setup. Please try again.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const loadSavedConfig = () => {
    // Load from localStorage (in a real app, this would be from your settings API)
    const savedConfig = localStorage.getItem('google_drive_config');
    if (savedConfig) {
      try {
        const config: GoogleDriveConfig = JSON.parse(savedConfig);
        if (config.type === 'oauth') {
          setOauthConfig({
            clientId: config.clientId || '',
            clientSecret: config.clientSecret || '',
            refreshToken: config.refreshToken || ''
          });
          setSetupMode('pro');
        } else if (config.type === 'service_account') {
          setServiceAccountConfig({
            clientEmail: config.clientEmail || '',
            privateKey: config.privateKey || '',
            projectId: config.projectId || ''
          });
          // setActiveTab('service_account');
        }
      } catch (error) {
        console.error('Failed to load saved config:', error);
      }
    }
  };

  const saveConfig = (config: GoogleDriveConfig) => {
    localStorage.setItem('google_drive_config', JSON.stringify(config));
  };

  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      const response = await fetch('/api/google-drive/test');
      const data = await response.json();
      
      setConnectionStatus({
        isConnected: data.success,
        type: data.config?.type,
        user: data.user,
        error: data.error,
        lastTested: new Date().toLocaleString()
      });
      
      if (data.success) {
        toast({
          title: "✅ Connection Successful",
          description: `Connected to Google Drive as ${data.user?.displayName || 'Unknown User'}`,
        });
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setConnectionStatus({
        isConnected: false,
        error: 'Failed to test connection',
        lastTested: new Date().toLocaleString()
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleOAuthSetup = async () => {
    if (!oauthConfig.clientId || !oauthConfig.clientSecret || !oauthConfig.refreshToken) {
      toast({
        title: "❌ Missing Information",
        description: "Please fill in all OAuth fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/google-drive/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'oauth',
          config: oauthConfig
        })
      });

      const data = await response.json();
      
      if (data.success) {
        const config: GoogleDriveConfig = {
          type: 'oauth',
          ...oauthConfig
        };
        saveConfig(config);
        
        toast({
          title: "🎉 OAuth Setup Complete!",
          description: "Google Drive OAuth connection established successfully",
        });
        
        await testConnection();
      } else {
        throw new Error(data.error || 'Setup failed');
      }
    } catch (error) {
      console.error('OAuth setup failed:', error);
      toast({
        title: "❌ OAuth Setup Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleServiceAccountSetup = async () => {
    if (!serviceAccountConfig.clientEmail || !serviceAccountConfig.privateKey) {
      toast({
        title: "❌ Missing Information",
        description: "Please fill in required Service Account fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/google-drive/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'service_account',
          config: serviceAccountConfig
        })
      });

      const data = await response.json();
      
      if (data.success) {
        const config: GoogleDriveConfig = {
          type: 'service_account',
          ...serviceAccountConfig
        };
        saveConfig(config);
        
        toast({
          title: "🎉 Service Account Setup Complete!",
          description: "Google Drive Service Account connection established successfully",
        });
        
        await testConnection();
      } else {
        throw new Error(data.error || 'Setup failed');
      }
    } catch (error) {
      console.error('Service Account setup failed:', error);
      toast({
        title: "❌ Service Account Setup Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearConfig = () => {
    localStorage.removeItem('google_drive_config');
    setOauthConfig({ clientId: '', clientSecret: '', refreshToken: '' });
    setServiceAccountConfig({ clientEmail: '', privateKey: '', projectId: '' });
    setConnectionStatus({ isConnected: false });
    
    toast({
      title: "🗑️ Configuration Cleared",
      description: "Google Drive configuration has been removed",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cloud className="h-5 w-5" />
          Google Drive Integration
        </CardTitle>
        <CardDescription>
          Connect to Google Drive for image storage. Choose between OAuth (easy setup) or Service Account (better for production).
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Connection Status */}
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-3">
            {connectionStatus.isConnected ? (
              <Check className="h-5 w-5 text-green-500" />
            ) : (
              <X className="h-5 w-5 text-red-500" />
            )}
            <div>
              <p className="font-medium">
                {connectionStatus.isConnected ? 'Connected' : 'Not Connected'}
              </p>
              {connectionStatus.type && (
                <Badge variant="outline" className="mt-1">
                  {connectionStatus.type === 'oauth' ? 'OAuth' : 'Service Account'}
                </Badge>
              )}
              {connectionStatus.user && (
                <p className="text-sm text-muted-foreground">
                  {connectionStatus.user.displayName || connectionStatus.user.emailAddress}
                </p>
              )}
              {connectionStatus.error && (
                <p className="text-sm text-red-500">{connectionStatus.error}</p>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={testConnection}
              disabled={isTestingConnection}
            >
              {isTestingConnection ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <TestTube2 className="h-4 w-4" />
              )}
              Test
            </Button>
            {connectionStatus.isConnected && (
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={handleClearConfig}
              >
                <Trash2 className="h-4 w-4" />
                Clear
              </Button>
            )}
          </div>
        </div>

        <Separator />

        {/* Setup Mode Selection */}
        <div className="flex items-center justify-center gap-4 mb-6">
          <Button
            variant={setupMode === 'easy' ? 'default' : 'outline'}
            onClick={() => setSetupMode('easy')}
            className="flex items-center gap-2"
          >
            <Cloud className="h-4 w-4" />
            Easy Setup
          </Button>
          <Button
            variant={setupMode === 'pro' ? 'default' : 'outline'}
            onClick={() => setSetupMode('pro')}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Pro Setup
          </Button>
        </div>

        {/* Easy Setup Mode */}
        {setupMode === 'easy' && (
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <Cloud className="h-6 w-6 text-blue-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">One-Click Google Drive Setup</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Connect your Google Drive in seconds! No technical setup required - just click the button below and authorize access.
                  </p>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Check className="h-3 w-3 text-green-500" />
                    <span>No credentials needed</span>
                    <Check className="h-3 w-3 text-green-500" />
                    <span>Secure OAuth flow</span>
                    <Check className="h-3 w-3 text-green-500" />
                    <span>Works instantly</span>
                  </div>
                </div>
              </div>
            </div>

            <Button 
              onClick={handleEasySetup} 
              disabled={isLoading}
              size="lg"
              className="w-full h-12 text-base font-medium bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Connecting to Google Drive...
                </>
              ) : (
                <>
                  <Cloud className="mr-2 h-5 w-5" />
                  Connect Google Drive Now
                </>
              )}
            </Button>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                Need advanced configuration? 
                <button 
                  onClick={() => setSetupMode('pro')}
                  className="text-blue-600 hover:text-blue-700 underline ml-1"
                >
                  Switch to Pro Setup
                </button>
              </p>
            </div>
          </div>
        )}

        {/* Pro Setup Mode */}
        {setupMode === 'pro' && (
          <div className="space-y-4">
            <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-amber-900">Advanced Configuration</p>
                  <p className="text-amber-700 mt-1">
                    For developers and advanced users who need custom OAuth credentials or Service Account setup.
                  </p>
                </div>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'oauth' | 'service_account')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="oauth" className="flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  Custom OAuth
                </TabsTrigger>
                <TabsTrigger value="service_account" className="flex items-center gap-2">
                  <ShieldCheck className="h-4 w-4" />
                  Service Account
                </TabsTrigger>
              </TabsList>

              {/* Custom OAuth Tab */}
              <TabsContent value="oauth" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="clientId">Client ID</Label>
                    <Input
                      id="clientId"
                      placeholder="your-client-id.googleusercontent.com"
                      value={oauthConfig.clientId}
                      onChange={(e) => setOauthConfig(prev => ({ ...prev, clientId: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="clientSecret">Client Secret</Label>
                    <div className="relative">
                      <Input
                        id="clientSecret"
                        type={showOAuthSecrets.clientSecret ? "text" : "password"}
                        placeholder="Your OAuth client secret"
                        value={oauthConfig.clientSecret}
                        onChange={(e) => setOauthConfig(prev => ({ ...prev, clientSecret: e.target.value }))}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowOAuthSecrets(prev => ({ 
                          ...prev, 
                          clientSecret: !prev.clientSecret 
                        }))}
                      >
                        {showOAuthSecrets.clientSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="refreshToken">Refresh Token</Label>
                    <div className="relative">
                      <Input
                        id="refreshToken"
                        type={showOAuthSecrets.refreshToken ? "text" : "password"}
                        placeholder="Your OAuth refresh token"
                        value={oauthConfig.refreshToken}
                        onChange={(e) => setOauthConfig(prev => ({ ...prev, refreshToken: e.target.value }))}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowOAuthSecrets(prev => ({ 
                          ...prev, 
                          refreshToken: !prev.refreshToken 
                        }))}
                      >
                        {showOAuthSecrets.refreshToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <Button 
                    onClick={handleOAuthSetup} 
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Setting up OAuth...
                      </>
                    ) : (
                      <>
                        <Link className="mr-2 h-4 w-4" />
                        Connect with Custom OAuth
                      </>
                    )}
                  </Button>
                </div>
              </TabsContent>

          {/* Service Account Tab */}
              <TabsContent value="service_account" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="clientEmail">Service Account Email</Label>
                    <Input
                      id="clientEmail"
                      placeholder="<EMAIL>"
                      value={serviceAccountConfig.clientEmail}
                      onChange={(e) => setServiceAccountConfig(prev => ({ ...prev, clientEmail: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="privateKey">Private Key</Label>
                    <div className="relative">
                      <Textarea
                        id="privateKey"
                        placeholder="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
                        value={serviceAccountConfig.privateKey}
                        onChange={(e) => setServiceAccountConfig(prev => ({ ...prev, privateKey: e.target.value }))}
                        rows={showServiceAccountKey ? 10 : 3}
                        className="font-mono text-xs"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-2"
                        onClick={() => setShowServiceAccountKey(!showServiceAccountKey)}
                      >
                        {showServiceAccountKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="projectId">Project ID (Optional)</Label>
                    <Input
                      id="projectId"
                      placeholder="your-gcp-project-id"
                      value={serviceAccountConfig.projectId}
                      onChange={(e) => setServiceAccountConfig(prev => ({ ...prev, projectId: e.target.value }))}
                    />
                  </div>

                  <Button 
                    onClick={handleServiceAccountSetup} 
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Setting up Service Account...
                      </>
                    ) : (
                      <>
                        <ShieldCheck className="mr-2 h-4 w-4" />
                        Connect with Service Account
                      </>
                    )}
                  </Button>
                </div>
              </TabsContent>
             </Tabs>

             <div className="text-center mt-4">
               <p className="text-xs text-gray-500">
                 Want simpler setup? 
                 <button 
                   onClick={() => setSetupMode('easy')}
                   className="text-blue-600 hover:text-blue-700 underline ml-1"
                 >
                   Switch to Easy Setup
                 </button>
               </p>
             </div>
           </div>
         )}

        {/* Setup Instructions */}
        <Separator />
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Setup Instructions
          </h4>
          <div className="text-xs text-muted-foreground space-y-2">
            <p>
              1. Go to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Cloud Console</a>
            </p>
            <p>
              2. Enable the Google Drive API for your project
            </p>
            <p>
              3. Create credentials (OAuth 2.0 or Service Account)
            </p>
            <p>
              4. Copy the credentials and paste them above
            </p>
            <p>
              5. Test the connection to ensure everything works
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}