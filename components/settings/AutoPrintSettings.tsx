"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Info, Printer, Smartphone, Monitor } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAutoPrint } from '@/components/providers/AutoPrintProvider';

export function AutoPrintSettings() {
  const { config, isReady, updateConfig } = useAutoPrint();

  const handleToggle = (key: keyof typeof config) => {
    updateConfig({ [key]: !config[key] });
  };

  const handleDelayChange = (value: string) => {
    const delayMs = parseInt(value) || 0;
    updateConfig({ delayMs: Math.max(0, Math.min(10000, delayMs)) }); // 0-10 seconds max
  };

  return (
    <TooltipProvider>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Printer className="h-5 w-5" />
            Auto-Print Settings
            {isReady ? (
              <Badge variant="default" className="bg-green-600">
                <Monitor className="h-3 w-3 mr-1" />
                Desktop Ready
              </Badge>
            ) : (
              <Badge variant="secondary">
                <Smartphone className="h-3 w-3 mr-1" />
                Mobile/Web
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Service Status */}
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <div className={`w-2 h-2 rounded-full ${isReady ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span className="font-medium">
                {isReady ? 'Auto-print active on desktop' : 'Auto-print only works on desktop app'}
              </span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <div className="space-y-1 text-xs">
                    <div className="font-medium">Auto-print requires:</div>
                    <div>• Desktop app (Electron)</div>
                    <div>• Configured kitchen printers</div>
                    <div>• Active sync connection</div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Main Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="auto-print-enabled" className="text-sm font-medium">
                Enable Auto-Print
              </Label>
              <p className="text-xs text-muted-foreground">
                Automatically print orders to kitchen when they arrive
              </p>
            </div>
            <Switch
              id="auto-print-enabled"
              checked={config.enabled}
              onCheckedChange={() => handleToggle('enabled')}
              disabled={!isReady}
            />
          </div>

          {config.enabled && (
            <>
              {/* Print on Order Created */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="print-on-created" className="text-sm font-medium">
                    Print Local Orders
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Print orders created on this device
                  </p>
                </div>
                <Switch
                  id="print-on-created"
                  checked={config.printOnOrderCreated}
                  onCheckedChange={() => handleToggle('printOnOrderCreated')}
                  disabled={!isReady}
                />
              </div>

              {/* Print on Order Synced */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="print-on-synced" className="text-sm font-medium">
                    Print Synced Orders
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Print orders received from mobile devices
                  </p>
                </div>
                <Switch
                  id="print-on-synced"
                  checked={config.printOnOrderSynced}
                  onCheckedChange={() => handleToggle('printOnOrderSynced')}
                  disabled={!isReady}
                />
              </div>

              {/* Print Delay */}
              <div className="space-y-2">
                <Label htmlFor="print-delay" className="text-sm font-medium">
                  Print Delay (milliseconds)
                </Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="print-delay"
                    type="number"
                    min="0"
                    max="10000"
                    step="100"
                    value={config.delayMs}
                    onChange={(e) => handleDelayChange(e.target.value)}
                    className="w-24"
                    disabled={!isReady}
                  />
                  <span className="text-xs text-muted-foreground">
                    Delay before printing (0-10000ms)
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Small delay allows UI updates to complete before printing
                </p>
              </div>

              {/* Desktop Only Setting */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="desktop-only" className="text-sm font-medium">
                    Desktop Only
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Only enable auto-print on desktop devices
                  </p>
                </div>
                <Switch
                  id="desktop-only"
                  checked={config.desktopOnly}
                  onCheckedChange={() => handleToggle('desktopOnly')}
                  disabled={!isReady}
                />
              </div>
            </>
          )}

          {/* Help Text */}
          <div className="p-3 bg-blue-50/50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-900 space-y-1">
                <div className="font-medium">How Auto-Print Works:</div>
                <div>1. Mobile device creates an order</div>
                <div>2. Order syncs to desktop via CouchDB</div>
                <div>3. Desktop automatically prints to configured kitchen printers</div>
                <div>4. Kitchen receives order immediately</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}