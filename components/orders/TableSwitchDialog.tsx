import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useToast } from '@/hooks/use-toast';
import { ArrowRight, Users, Clock } from 'lucide-react';
import { Order } from '@/lib/db/v4/schemas/order-schema';
import { format } from 'date-fns';

interface TableSwitchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: Order;
  onSuccess?: () => void;
}

export const TableSwitchDialog: React.FC<TableSwitchDialogProps> = ({
  open,
  onOpenChange,
  order,
  onSuccess
}) => {
  const { tables } = useTableDB();
  const { orders, updateOrder } = useOrderV4();
  const { toast } = useToast();
  const [selectedTableId, setSelectedTableId] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get table statuses - which tables have active orders
  const tableStatuses = tables.map(table => {
    const activeOrders = orders.filter(o => 
      o.tableId === table.id && 
      (o.status === 'pending' || o.status === 'preparing' || o.status === 'served')
    );
    
    return {
      tableId: table.id,
      isOccupied: activeOrders.length > 0,
      orderCount: activeOrders.length,
      orders: activeOrders
    };
  });

  const handleTableSwitch = async () => {
    if (!selectedTableId) return;
    
    setIsProcessing(true);
    try {
      await updateOrder(order.id, { tableId: selectedTableId });
      
      const newTable = tables.find(t => t.id === selectedTableId);
      const oldTable = tables.find(t => t.id === order.tableId);
      
      toast({
        title: "Table changée",
        description: `#${order.id.slice(-6)} → ${newTable?.name}`
      });
      
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error switching table:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de changer la table"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const currentTable = tables.find(t => t.id === order.tableId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Changer de table</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="p-2 bg-muted/30 rounded text-sm">
            <span className="font-medium">{currentTable?.name}</span>
            <ArrowRight className="h-4 w-4 inline mx-2" />
            <span className="text-muted-foreground">#{order.id.slice(-6)}</span>
          </div>

          <div>
            <ScrollArea className="h-64">
              <div className="grid grid-cols-2 gap-2">
                {tables.map(table => {
                  const status = tableStatuses.find(s => s.tableId === table.id);
                  const isCurrentTable = table.id === order.tableId;
                  const isSelected = selectedTableId === table.id;
                  
                  return (
                    <Button
                      key={table.id}
                      variant={isSelected ? "default" : "outline"}
                      className={`h-12 p-2 flex items-center ${
                        isCurrentTable ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                      onClick={() => !isCurrentTable && setSelectedTableId(table.id)}
                      disabled={isCurrentTable}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <div className={`w-2 h-2 rounded-full ${status?.isOccupied ? 'bg-amber-500' : 'bg-green-500'}`}></div>
                        <span className="font-medium text-sm">{table.name}</span>
                        {status?.isOccupied && status.orderCount > 0 && (
                          <Badge variant="secondary" className="text-xs bg-amber-100 text-amber-700 px-1.5 py-0 ml-auto">
                            {status.orderCount}
                          </Badge>
                        )}
                      </div>
                    </Button>
                  );
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => onOpenChange(false)}
            >
              Annuler
            </Button>
            <Button
              className="flex-1"
              onClick={handleTableSwitch}
              disabled={!selectedTableId || isProcessing}
            >
              {isProcessing ? 'Changement...' : 'Changer de table'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};