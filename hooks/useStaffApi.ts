import { useState, useCallback } from 'react';
import { StaffMember } from '@/lib/types/staff';
import { getApiUrl, checkRemoteConnectivity } from '@/lib/build-config';

export interface StaffApiError {
  code?: string;
  message: string;
}

export interface StaffCreationResult {
  success: boolean;
  staff: StaffMember;
  authLinked: boolean;
  authError?: {
    code: string;
    message: string;
  };
}

export interface UseStaffApiResult {
  loading: boolean;
  error: StaffApiError | null;
  createStaff: (
    staffData: any, 
    authData?: { username: string; password: string; } | undefined
  ) => Promise<StaffCreationResult | null>;
  addAuthToStaff: (
    staffId: string,
    authData: { username: string; password: string; }
  ) => Promise<StaffCreationResult | null>;
  clearError: () => void;
}

/**
 * Hook for interacting with the staff API.
 */
export default function useStaffApi(): UseStaffApiResult {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<StaffApiError | null>(null);

  /**
   * Clear any API errors
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // knowledge:start electron environment check simplification
  function isElectron() {
    return typeof window !== 'undefined' && window.IS_DESKTOP_APP === true;
  }
  // knowledge:end electron environment check simplification

  async function isServerReachable() {
    try {
      // Use the proper connectivity check that handles remote servers
      return await checkRemoteConnectivity();
    } catch {
      return false;
    }
  }

  let forcedOffline = false;
  if (typeof window !== 'undefined' && isElectron()) {
    isServerReachable().then(reachable => {
      if (!reachable) {
        forcedOffline = true;
      }
    });
  }
  // knowledge:end electron offline fallback

  /**
   * Create a new staff member with optional auth credentials
   */
  const createStaff = useCallback(async (
    staffData: any,
    authData?: { username: string; password: string; }
  ): Promise<StaffCreationResult | null> => {
    try {
      if (forcedOffline) {
        setError({ code: 'OFFLINE_MODE', message: 'You are offline. Staff features are disabled.' });
        return null;
      }
      setLoading(true);
      setError(null);
      
      console.log('useStaffApi: Creating staff', { name: staffData.name, role: staffData.role });
      
      // Get restaurantId from localStorage if available
      const restaurantId = localStorage.getItem('restaurantId') || undefined;
      
      // Call the API to create the staff member
      const response = await fetch('/api/staff', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          staffData,
          authData,
          restaurantId
        })
      });
      
      const result = await response.json();
      
      // Detect offline fallback response from Electron fetch override
      if (result && result.offline) {
        setError({
          code: 'OFFLINE_MODE',
          message: 'You are offline. Staff creation will be saved locally only. Use the offline staff management feature.'
        });
        return null;
      }
      
      if (!response.ok || !result.success) {
        console.error('useStaffApi: Error creating staff:', result.error);
        
        setError({
          code: result.error?.code || 'API_ERROR',
          message: result.error?.message || 'An error occurred while creating the staff member'
        });
        
        return null;
      }
      
      console.log('useStaffApi: Staff created successfully:', { 
        id: result.staff.id, 
        authLinked: result.authLinked 
      });
      
      return result;
    } catch (error: any) {
      console.error('useStaffApi: Exception creating staff:', error);
      
      setError({
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unknown error occurred'
      });
      
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Add authentication to an existing staff member
   */
  const addAuthToStaff = useCallback(async (
    staffId: string,
    authData: { username: string; password: string; }
  ): Promise<StaffCreationResult | null> => {
    try {
      if (forcedOffline) {
        setError({ code: 'OFFLINE_MODE', message: 'You are offline. Staff features are disabled.' });
        return null;
      }
      setLoading(true);
      setError(null);
      
      console.log('useStaffApi: Adding auth to staff', { staffId, username: authData.username });
      
      // Get restaurantId from localStorage if available
      const restaurantId = localStorage.getItem('restaurantId') || undefined;
      
      // Call the API to add auth to the staff member
      const response = await fetch('/api/staff', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          staffId,
          authData,
          restaurantId
        })
      });
      
      const result = await response.json();
      
      // Detect offline fallback response from Electron fetch override
      if (result && result.offline) {
        setError({
          code: 'OFFLINE_MODE',
          message: 'You are offline. Staff auth changes will be saved locally only. Use the offline staff management feature.'
        });
        return null;
      }
      
      if (!response.ok || !result.success) {
        console.error('useStaffApi: Error adding auth to staff:', result.error);
        
        setError({
          code: result.error?.code || 'API_ERROR',
          message: result.error?.message || 'An error occurred while adding authentication'
        });
        
        return null;
      }
      
      console.log('useStaffApi: Auth added successfully:', { 
        id: result.staff.id, 
        authLinked: result.authLinked 
      });
      
      return result;
    } catch (error: any) {
      console.error('useStaffApi: Exception adding auth to staff:', error);
      
      setError({
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unknown error occurred'
      });
      
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    createStaff,
    addAuthToStaff,
    clearError
  };
} 