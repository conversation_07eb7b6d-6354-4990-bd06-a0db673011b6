import { useState, useEffect, useCallback } from 'react';
import { PeerInfo, SyncStatus } from '../types/p2p-sync';

/**
 * Hook to interact with the P2P sync functionality in Electron
 */
export function useP2PSync() {
  const [isElectron, setIsElectron] = useState<boolean>(false);
  const [peers, setPeers] = useState<PeerInfo[]>([]);
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [mdnsStatus, setMdnsStatus] = useState<'not_running' | 'running' | 'error'>('not_running');
  const [logs, setLogs] = useState<string[]>([]);
  
  // Initialize the hook and detect Electron environment
  useEffect(() => {
    console.log('[P2PSync] useEffect initialization started');
    // Guard against SSR where window is undefined
    if (typeof window === 'undefined') return;
    
    // knowledge:start electron environment check simplification
    const isElectronEnv = typeof window !== 'undefined' && window.IS_DESKTOP_APP === true;
    console.log('[P2PSync] Detected Electron environment:', isElectronEnv);
    setIsElectron(isElectronEnv);
    // knowledge:end electron environment check simplification
    
    if (isElectronEnv && window.electronAPI && window.electronAPI.p2pSync) {
      // Set up event listeners
      console.log('[P2PSync] Setting up P2P event listeners');
      window.electronAPI.p2pSync.onPeerDiscovered((peerInfo: PeerInfo) => {
        console.log('[P2PSync] Peer discovered:', peerInfo);
        setPeers(currentPeers => {
          // Check if this peer already exists
          if (currentPeers.some(p => p.id === peerInfo.id)) {
            return currentPeers;
          }
          return [...currentPeers, peerInfo];
        });
      });
      
      window.electronAPI.p2pSync.onPeerLost((peerId: string) => {
        console.log('[P2PSync] Peer lost:', peerId);
        setPeers(currentPeers => currentPeers.filter(p => p.id !== peerId));
      });
      
      window.electronAPI.p2pSync.onSyncStatusUpdated((status: SyncStatus) => {
        console.log('[P2PSync] Sync status updated:', status);
        setSyncStatuses(currentStatuses => {
          // Find if we already have this sync in our list
          const index = currentStatuses.findIndex(
            s => s.peerId === status.peerId && s.dbName === status.dbName
          );
          
          if (index >= 0) {
            // Update existing status
            const newStatuses = [...currentStatuses];
            newStatuses[index] = status;
            return newStatuses;
          } else {
            // Add new status
            return [...currentStatuses, status];
          }
        });
      });
      
      // Subscribe to P2P logs for debugging
      if (window.p2pLog) {
        window.p2pLog.onLog((message: string) => {
          setLogs(prev => [message, ...prev].slice(0, 100));
        });
      }
      
      // Initial fetch of peers, sync statuses, and mDNS status
      console.log('[P2PSync] Fetching initial data');
      const fetchInitialData = async () => {
        try {
          if (window.electronAPI && window.electronAPI.p2pSync) {
            // Use a type assertion to fix TypeScript error since we know getMdnsStatus exists
            const api = window.electronAPI.p2pSync as any;
            
            const [peersResult, syncStatusesResult, mdnsStatusResult, systemId] = await Promise.all([
              api.getPeers(),
              api.getSyncStatus(),
              api.getMdnsStatus(),
              api.getSystemId().catch(() => 'unknown')
            ]);
            
            console.log('[P2PSync] Initial data fetched:', { 
              peersResult, 
              syncStatusesResult,
              mdnsStatusResult,
              systemId: systemId.substring(0, 8) + '...'
            });
            
            setPeers(peersResult);
            setSyncStatuses(syncStatusesResult);
            setMdnsStatus(mdnsStatusResult);
            setIsInitialized(true);
            
            // Add log entry for successful initialization
            const initLog = `${new Date().toISOString()} - 🖥️ Desktop mDNS initialized. Status: ${mdnsStatusResult}, Version: 1.0.0, SystemId: ${systemId}`;
            setLogs(prev => [initLog, ...prev].slice(0, 100));
            
            // Add a second log for clarity and to verify log rendering
            const secondLog = `${new Date().toISOString()} - 🧪 P2P system ready - Desktop is ${mdnsStatusResult === 'running' ? 'broadcasting' : 'not broadcasting'}`;
            setLogs(prev => [secondLog, ...prev].slice(0, 100));
            
            // Detailed logs parsing will use logs from main process; skip client-side os-based log injection
            
            // Add a special method to get the active server port
            try {
              if (api.getServerPort) {
                const serverPort = await api.getServerPort().catch(() => 8080);
                const portLog = `${new Date().toISOString()} - 📡 Service port: ${serverPort}`;
                setLogs(prev => [portLog, ...prev].slice(0, 100));
              }
            } catch (portError) {
              console.warn('Could not get server port, using fallback:', portError);
            }
            
            // Inject service name, TXT records, and local IP using getServiceInfo
            try {
              if (api.getServiceInfo) {
                const serviceInfo = await api.getServiceInfo().catch(() => null);
                if (serviceInfo && serviceInfo.name) {
                  const serviceNameLog = `${new Date().toISOString()} - 📡 Service name: ${serviceInfo.name}`;
                  setLogs(prev => [serviceNameLog, ...prev].slice(0, 100));
                }
                if (serviceInfo && serviceInfo.txt) {
                  const txtLog = `${new Date().toISOString()} - 📡 TXT records: ${JSON.stringify(serviceInfo.txt)}`;
                  setLogs(prev => [txtLog, ...prev].slice(0, 100));
                }
                if (serviceInfo && serviceInfo.ip) {
                  const ipLog = `${new Date().toISOString()} - 📡 Local IP: ${serviceInfo.ip}`;
                  setLogs(prev => [ipLog, ...prev].slice(0, 100));
                }
              }
            } catch (e) {
              console.warn('Could not get service info for logs injection:', e);
            }
            
            // Detailed TXT records logs come via p2pLog events from main process
          }
        } catch (error) {
          console.error('Error fetching initial P2P sync data:', error);
          setMdnsStatus('error');
          const errorMsg = error instanceof Error ? error.message : String(error);
          setLogs(prev => [`${new Date().toISOString()} - ❌ Error initializing: ${errorMsg}`, ...prev].slice(0, 100));
        }
      };
      
      fetchInitialData();
      
      // Poll for mDNS status every 2 seconds to keep UI updated
      const statusInterval = setInterval(async () => {
        try {
          if (window.electronAPI && window.electronAPI.p2pSync) {
            // Use a type assertion to fix TypeScript error
            const api = window.electronAPI.p2pSync as any;
            
            try {
              const status = await api.getMdnsStatus();
              console.log('[P2PSync] Got mDNS status:', status);
              setMdnsStatus(status);
            } catch (mdnsError) {
              console.error('[P2PSync] Error fetching mDNS status - setting to error state:', mdnsError);
              setMdnsStatus('error');
            }
            
            // Every 30 seconds, refresh detailed configuration for UI parsing
            const now = new Date();
            if (now.getSeconds() % 30 === 0) {
              try {
                const [peersResult, systemId] = await Promise.all([
                  api.getPeers(),
                  api.getSystemId().catch(() => 'unknown')
                ]);
                
                // Get the port from peers if available
                let portLog = '';
                if (peersResult && peersResult.length > 0) {
                  const port = peersResult[0].port || 0; 
                  portLog = `${now.toISOString()} - 📡 Service port: ${port}`;
                  setLogs(prev => [portLog, ...prev].slice(0, 100));
                }
                
                // Update IP address information if available
                if (peersResult && peersResult.length > 0) {
                  const peer = peersResult[0];
                  if (peer.ip) {
                    const ipLog = `${now.toISOString()} - 📡 Local IP: ${peer.ip}`;
                    setLogs(prev => [ipLog, ...prev].slice(0, 100));
                  }
                }
              } catch (configError) {
                console.error('Error updating mDNS configuration:', configError);
              }
            }
          }
        } catch (error) {
          console.error('Error fetching mDNS status:', error);
        }
      }, 2000);
      
      return () => {
        clearInterval(statusInterval);
      };
    }
  }, []);
  
  // Start syncing with a peer
  const startSync = useCallback(async (
    peerId: string,
    dbName: string,
    direction: 'push' | 'pull' | 'both' = 'both'
  ) => {
    if (typeof window === 'undefined' || !window.electronAPI?.p2pSync) {
      console.error('Electron P2P Sync API not available');
      return { success: false, error: 'Electron P2P Sync API not available' };
    }
    try {
      return await window.electronAPI.p2pSync.startSync(peerId, dbName, direction);
    } catch (error) {
      console.error('Error starting sync:', error);
      return { success: false, error: String(error) };
    }
  }, []);
  
  // Stop syncing with a peer for a specific database
  const stopSync = useCallback(async (peerId: string, dbName: string) => {
    if (typeof window === 'undefined' || !window.electronAPI?.p2pSync) {
      console.error('Electron P2P Sync API not available');
      return { success: false, error: 'Electron P2P Sync API not available' };
    }
    try {
      return await window.electronAPI.p2pSync.stopSync(peerId, dbName);
    } catch (error) {
      console.error('Error stopping sync:', error);
      return { success: false, error: String(error) };
    }
  }, []);
  
  // Stop all syncs with a peer
  const stopAllSyncsWithPeer = useCallback(async (peerId: string) => {
    if (typeof window === 'undefined' || !window.electronAPI?.p2pSync) {
      console.error('Electron P2P Sync API not available');
      return { success: false, error: 'Electron P2P Sync API not available' };
    }
    try {
      return await window.electronAPI.p2pSync.stopAllSyncsWithPeer(peerId);
    } catch (error) {
      console.error('Error stopping all syncs with peer:', error);
      return { success: false, error: String(error) };
    }
  }, []);
  
  // Get all syncs for a specific database
  const getSyncsForDatabase = useCallback((dbName: string) => {
    return syncStatuses.filter(status => status.dbName === dbName);
  }, [syncStatuses]);
  
  // Get all syncs with a specific peer
  const getSyncsWithPeer = useCallback((peerId: string) => {
    return syncStatuses.filter(status => status.peerId === peerId);
  }, [syncStatuses]);
  
  return {
    isElectron,
    isInitialized,
    peers,
    syncStatuses,
    mdnsStatus,
    logs,
    startSync,
    stopSync,
    stopAllSyncsWithPeer,
    getSyncsForDatabase,
    getSyncsWithPeer
  };
} 