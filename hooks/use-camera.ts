"use client";

import { useState, useCallback } from 'react';
import { Camera, CameraResultType, CameraSource, CameraDirection } from '@capacitor/camera';
import { Capacitor } from '@capacitor/core';

export interface CameraPhoto {
  webPath?: string;
  path?: string;
  base64String?: string;
  format: string;
}

export interface UseCameraOptions {
  quality?: number;
  allowEditing?: boolean;
  resultType?: CameraResultType;
  source?: CameraSource;
  correctOrientation?: boolean;
  width?: number;
  height?: number;
}

export function useCamera() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if we're on a native platform
  const isNative = Capacitor.isNativePlatform();
  const platform = Capacitor.getPlatform();

  // Request camera permissions
  const requestPermissions = useCallback(async () => {
    try {
      if (isNative) {
        const permissions = await Camera.requestPermissions({
          permissions: ['camera', 'photos']
        });
        
        if (permissions.camera === 'denied' || permissions.photos === 'denied') {
          throw new Error('Camera or photos permission denied');
        }
        
        return permissions;
      }
      return { camera: 'granted', photos: 'granted' };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to request permissions';
      setError(errorMessage);
      throw err;
    }
  }, [isNative]);

  // Check current permissions
  const checkPermissions = useCallback(async () => {
    try {
      if (isNative) {
        return await Camera.checkPermissions();
      }
      return { camera: 'granted', photos: 'granted' };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check permissions';
      setError(errorMessage);
      throw err;
    }
  }, [isNative]);

  // Take a photo with camera
  const takePhoto = useCallback(async (options: UseCameraOptions = {}): Promise<CameraPhoto> => {
    setIsLoading(true);
    setError(null);

    try {
      // Check permissions first
      const permissions = await checkPermissions();
      if (permissions.camera !== 'granted') {
        await requestPermissions();
      }

      const defaultOptions = {
        quality: 90,
        allowEditing: true,
        resultType: CameraResultType.Uri,
        source: CameraSource.Camera,
        correctOrientation: true,
        width: 1024,
        height: 1024,
        ...options
      };

      const image = await Camera.getPhoto(defaultOptions);
      
      return {
        webPath: image.webPath,
        path: image.path,
        base64String: image.base64String,
        format: image.format
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to take photo';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [checkPermissions, requestPermissions]);

  // Pick photo from gallery
  const pickPhoto = useCallback(async (options: UseCameraOptions = {}): Promise<CameraPhoto> => {
    setIsLoading(true);
    setError(null);

    try {
      // Check permissions first
      const permissions = await checkPermissions();
      if (permissions.photos !== 'granted') {
        await requestPermissions();
      }

      const defaultOptions = {
        quality: 90,
        allowEditing: true,
        resultType: CameraResultType.Uri,
        source: CameraSource.Photos,
        correctOrientation: true,
        width: 1024,
        height: 1024,
        ...options
      };

      const image = await Camera.getPhoto(defaultOptions);
      
      return {
        webPath: image.webPath,
        path: image.path,
        base64String: image.base64String,
        format: image.format
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pick photo';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [checkPermissions, requestPermissions]);

  // Pick multiple photos (iOS 14+, Android)
  const pickMultiplePhotos = useCallback(async (limit: number = 5) => {
    setIsLoading(true);
    setError(null);

    try {
      // Check permissions first
      const permissions = await checkPermissions();
      if (permissions.photos !== 'granted') {
        await requestPermissions();
      }

      if (isNative) {
        const images = await Camera.pickImages({
          quality: 90,
          limit: limit
        });
        
        return images.photos.map(photo => ({
          webPath: photo.webPath,
          path: photo.path,
          format: photo.format
        }));
      } else {
        // Fallback for web - single photo
        const photo = await pickPhoto();
        return [photo];
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pick photos';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isNative, checkPermissions, requestPermissions, pickPhoto]);

  // Show action sheet to choose camera or gallery
  const showImagePicker = useCallback(async (options: UseCameraOptions = {}): Promise<CameraPhoto> => {
    setIsLoading(true);
    setError(null);

    try {
      // Check permissions first
      const permissions = await checkPermissions();
      if (permissions.camera !== 'granted' || permissions.photos !== 'granted') {
        await requestPermissions();
      }

      const defaultOptions = {
        quality: 90,
        allowEditing: true,
        resultType: CameraResultType.Uri,
        source: CameraSource.Prompt, // This shows the action sheet
        correctOrientation: true,
        width: 1024,
        height: 1024,
        ...options
      };

      const image = await Camera.getPhoto(defaultOptions);
      
      return {
        webPath: image.webPath,
        path: image.path,
        base64String: image.base64String,
        format: image.format
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get photo';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [checkPermissions, requestPermissions]);

  return {
    // State
    isLoading,
    error,
    isNative,
    platform,
    
    // Methods
    requestPermissions,
    checkPermissions,
    takePhoto,
    pickPhoto,
    pickMultiplePhotos,
    showImagePicker,
    
    // Clear error
    clearError: () => setError(null)
  };
} 