"use client";

import { useState, useEffect } from 'react';

export interface OSDetectionState {
  isMobile: boolean;
  isDesktop: boolean;
  platform: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown';
  userAgent: string;
  isCapacitor: boolean;
  isElectron: boolean;
  isBrowser: boolean;
}

export function useOSDetection(): OSDetectionState {
  const [osState, setOSState] = useState<OSDetectionState>({
    isMobile: false,
    isDesktop: true,
    platform: 'unknown',
    userAgent: '',
    isCapacitor: false,
    isElectron: false,
    isBrowser: true,
  });

  useEffect(() => {
    const detectOS = () => {
      if (typeof window === 'undefined') return;

      const userAgent = navigator.userAgent.toLowerCase();
      
      // Check for Capacitor (mobile app)
      const isCapacitor = !!(window as any).Capacitor;
      
      // Check for Electron (desktop app)
      const isElectron = !!(window as any).electronAPI || 
                        userAgent.includes('electron') ||
                        !!(window as any).require;
      
      // Determine platform
      let platform: OSDetectionState['platform'] = 'unknown';
      let isMobile = false;
      let isDesktop = true;

      if (isCapacitor) {
        // Mobile app via Capacitor
        isMobile = true;
        isDesktop = false;
        
        if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ios')) {
          platform = 'ios';
        } else if (userAgent.includes('android')) {
          platform = 'android';
        }
      } else if (isElectron) {
        // Desktop app via Electron
        isMobile = false;
        isDesktop = true;
        
        if (userAgent.includes('mac') || userAgent.includes('darwin')) {
          platform = 'macos';
        } else if (userAgent.includes('win')) {
          platform = 'windows';
        } else if (userAgent.includes('linux')) {
          platform = 'linux';
        }
      } else {
        // Browser - detect based on user agent
        if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
          platform = 'ios';
          isMobile = true;
          isDesktop = false;
        } else if (userAgent.includes('android')) {
          platform = 'android';
          isMobile = true;
          isDesktop = false;
        } else if (userAgent.includes('mac') || userAgent.includes('darwin')) {
          platform = 'macos';
          isMobile = false;
          isDesktop = true;
        } else if (userAgent.includes('win')) {
          platform = 'windows';
          isMobile = false;
          isDesktop = true;
        } else if (userAgent.includes('linux')) {
          platform = 'linux';
          isMobile = false;
          isDesktop = true;
        }
      }

      setOSState({
        isMobile,
        isDesktop,
        platform,
        userAgent,
        isCapacitor,
        isElectron,
        isBrowser: !isCapacitor && !isElectron,
      });
    };

    detectOS();
  }, []);

  return osState;
}

// Utility hooks for specific OS detection needs
export function useIsMobileOS() {
  const { isMobile } = useOSDetection();
  return isMobile;
}

export function useIsDesktopOS() {
  const { isDesktop } = useOSDetection();
  return isDesktop;
}

export function usePlatform() {
  const { platform } = useOSDetection();
  return platform;
}

export function useIsCapacitor() {
  const { isCapacitor } = useOSDetection();
  return isCapacitor;
}

export function useIsElectron() {
  const { isElectron } = useOSDetection();
  return isElectron;
}

export function useIsBrowser() {
  const { isBrowser } = useOSDetection();
  return isBrowser;
}