import * as React from "react"

const MOBILE_BREAKPOINT = 768

// Create a context to store and share the mobile state
export const MobileContext = React.createContext<boolean>(false);

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(false)

  React.useEffect(() => {
    // Function to check if the device is mobile
    const checkMobile = () => {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') return false

      // Get device width
      const width = window.innerWidth
      return width < MOBILE_BREAKPOINT
    }

    // Set initial state
    setIsMobile(checkMobile())

    // Set up event listener for window resize
    const handleResize = () => {
      setIsMobile(checkMobile())
    }

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return isMobile
}

// Provider component to wrap the application
export function MobileProvider({ children }: { children: React.ReactNode }) {
  const isMobile = useIsMobile();
  
  return (
    <MobileContext.Provider value={isMobile}>
      {children}
    </MobileContext.Provider>
  );
}

// Simple hook to consume the context
export function useMobileContext() {
  return React.useContext(MobileContext);
}
