"use client";

import { useState, useEffect } from 'react';

export interface MobileLayoutState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  viewportHeight: number;
  viewportWidth: number;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  isKeyboardOpen: boolean;
}

export function useMobileLayout(): MobileLayoutState {
  const [layoutState, setLayoutState] = useState<MobileLayoutState>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    orientation: 'landscape',
    viewportHeight: 0,
    viewportWidth: 0,
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 },
    isKeyboardOpen: false,
  });

  useEffect(() => {
    const updateLayoutState = () => {
      if (typeof window === 'undefined') return;

      const width = window.innerWidth;
      const height = window.innerHeight;
      const visualViewport = window.visualViewport;
      
      // Determine device type
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      
      // Determine orientation
      const orientation = width > height ? 'landscape' : 'portrait';
      
      // Check if keyboard is open (significant viewport height reduction)
      const keyboardThreshold = 150;
      const isKeyboardOpen = visualViewport 
        ? (height - visualViewport.height) > keyboardThreshold
        : false;

      // Get safe area insets
      const safeAreaInsets = {
        top: getSafeAreaInset('top'),
        bottom: getSafeAreaInset('bottom'),
        left: getSafeAreaInset('left'),
        right: getSafeAreaInset('right'),
      };

      setLayoutState({
        isMobile,
        isTablet,
        isDesktop,
        orientation,
        viewportHeight: visualViewport?.height || height,
        viewportWidth: visualViewport?.width || width,
        safeAreaInsets,
        isKeyboardOpen,
      });
    };

    // Helper function to get safe area inset values
    const getSafeAreaInset = (side: 'top' | 'bottom' | 'left' | 'right'): number => {
      if (typeof window === 'undefined') return 0;
      
      const testElement = document.createElement('div');
      testElement.style.position = 'fixed';
      testElement.style.top = '0';
      testElement.style.left = '0';
      testElement.style.width = '1px';
      testElement.style.height = '1px';
      testElement.style.paddingTop = `env(safe-area-inset-${side})`;
      
      document.body.appendChild(testElement);
      const computedStyle = window.getComputedStyle(testElement);
      const insetValue = parseInt(computedStyle.paddingTop) || 0;
      document.body.removeChild(testElement);
      
      return insetValue;
    };

    // Initial update
    updateLayoutState();

    // Listen for resize events
    const handleResize = () => updateLayoutState();
    window.addEventListener('resize', handleResize);

    // Listen for visual viewport changes (keyboard open/close)
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateLayoutState);
    }

    // Listen for orientation changes
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateLayoutState, 100);
    };
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', updateLayoutState);
      }
    };
  }, []);

  return layoutState;
}

// Utility hooks for specific layout needs
export function useIsMobileLayout() {
  const { isMobile } = useMobileLayout();
  return isMobile;
}

export function useIsTabletLayout() {
  const { isTablet } = useMobileLayout();
  return isTablet;
}

export function useIsDesktopLayout() {
  const { isDesktop } = useMobileLayout();
  return isDesktop;
}

export function useOrientation() {
  const { orientation } = useMobileLayout();
  return orientation;
}

export function useKeyboardState() {
  const { isKeyboardOpen } = useMobileLayout();
  return isKeyboardOpen;
}

export function useSafeAreaInsets() {
  const { safeAreaInsets } = useMobileLayout();
  return safeAreaInsets;
}

// Responsive breakpoint utilities
export const breakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
} as const;

export function useBreakpoint() {
  const { viewportWidth } = useMobileLayout();
  
  return {
    isMobile: viewportWidth < breakpoints.mobile,
    isTablet: viewportWidth >= breakpoints.mobile && viewportWidth < breakpoints.tablet,
    isDesktop: viewportWidth >= breakpoints.desktop,
    current: viewportWidth < breakpoints.mobile 
      ? 'mobile' 
      : viewportWidth < breakpoints.tablet 
        ? 'tablet' 
        : 'desktop',
  };
}

// Touch-friendly utilities
export function useTouchOptimization() {
  const { isMobile } = useMobileLayout();
  
  return {
    minTouchTarget: isMobile ? 44 : 32, // px
    recommendedSpacing: isMobile ? 16 : 12, // px
    fontSize: {
      small: isMobile ? 14 : 12,
      medium: isMobile ? 16 : 14,
      large: isMobile ? 18 : 16,
    },
  };
}
