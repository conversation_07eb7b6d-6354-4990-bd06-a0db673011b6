const config = {
  "appId": "dev.bistro.app",
  "productName": "Bistro",
  "directories": {
    "output": "release/"
  },
  "files": [
    "dist/**/*",
    "app/**/*",
    "config.json",
    "capacitor.config.json",
    "node_modules/electron-updater/**/*",
    "node_modules/nano/**/*",
    "node_modules/bonjour/**/*",
    "node_modules/bonjour-service/**/*",
    "node_modules/multicast-dns/**/*",
    "node_modules/dns-packet/**/*",
    "node_modules/dns-txt/**/*",
    "node_modules/ip/**/*",
    "node_modules/pouchdb-browser/**/*",
    "node_modules/pouchdb-find/**/*"
  ],
  "asarUnpack": [
    "dist/preload.js",
    "resources/couchdb-macos/**/*",
    "resources/couchdb-windows/**/*"
  ],
  "extraResources": [
    {
      "from": "resources/couchdb-macos",
      "to": "couchdb-macos",
      "filter": [
        "**/*"
      ]
    },
    {
      "from": "resources/couchdb-windows",
      "to": "couchdb-windows",
      "filter": [
        "**/*"
      ]
    }
  ],
  "mac": {
    "category": "public.app-category.business",
    "target": [
      {
        "target": "dmg",
        "arch": [
          "x64",
          "arm64"
        ]
      }
    ],
    "icon": "resources/icon.icns",
    "identity": null
  },
  // No publish configuration - no auto-updater needed
  "publish": null
};

module.exports = config;