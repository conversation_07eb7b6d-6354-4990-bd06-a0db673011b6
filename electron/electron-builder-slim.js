const config = {
  "appId": "dev.bistro.app",
  "productName": "Bistro",
  "directories": {
    "output": "release/"
  },
  "files": [
    "dist/**/*",
    "app/**/*",
    "capacitor.config.json",
    "node_modules/electron-updater/**/*",
    "node_modules/pouchdb-node/**/*",
    "node_modules/pouchdb-find/**/*",
    "node_modules/nano/**/*"
  ],
  "asar": true,
  "asarUnpack": [
    "**/*.node",
    "resources/couchdb-windows/**/*"
  ],
  "extraResources": [
    {
      "from": "resources/couchdb-windows",
      "to": "couchdb-windows",
      "filter": [
        "**/*"
      ]
    }
  ],
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": [
          "x64"
        ]
      }
    ],
    "icon": "resources/icon.ico",
    "publisherName": "Bistro Restaurant POS",
    "requestedExecutionLevel": "asInvoker"
  },
  "nsis": {
    "oneClick": false,
    "perMachine": true,
    "allowToChangeInstallationDirectory": true,
    "deleteAppDataOnUninstall": true,
    "artifactName": "bistro-${version}.${ext}",
    "compression": "ultra",
    "runAfterFinish": false
  },
  "publish": [
    {
      "provider": "generic",
      "url": "https://pub-d1ae66d7e9a247a08a1ad96b22c13e10.r2.dev"
    }
  ],
  "afterPack": "./scripts/afterPack.js"
};

module.exports = config;