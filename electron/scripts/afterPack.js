const fs = require('fs-extra');
const path = require('path');

module.exports = async function afterPack(context) {
  try {
    const appPath = context.appOutDir;

    // Remove unnecessary Electron locales (keep only en, en-US, en_GB)
    const localeDir = path.join(appPath, 'locales');
    if (await fs.pathExists(localeDir)) {
      const files = await fs.readdir(localeDir);
      await Promise.all(
        files
          .filter((f) => !/^en(-|_)?/i.test(f))
          .map((f) => fs.remove(path.join(localeDir, f)))
      );
      console.log(`[afterPack] Removed ${files.length} locale files`);
    }

    // Remove ffmpeg binaries if video playback is not required
    const ffmpegFiles = ['ffmpeg.dll', 'ffmpeg', 'libffmpeg.so'];
    await Promise.all(
      ffmpegFiles.map(async (file) => {
        const fullPath = path.join(appPath, file);
        if (await fs.pathExists(fullPath)) {
          await fs.remove(fullPath);
          console.log(`[afterPack] Removed ${file}`);
        }
      })
    );
  } catch (err) {
    console.error('[afterPack] Error while slimming app package:', err);
  }
}; 