#!/usr/bin/env node

/**
 * 🔧 Before Build Script
 * 
 * <PERSON>les native dependency rebuilding for cross-platform Electron builds
 * Ensures native modules work correctly on target platforms
 */

const { execSync } = require('child_process');
const path = require('path');

function log(message, color = '\x1b[36m') {
  console.log(`${color}🔧 [Before Build] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [Before Build] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [Before Build] ${message}\x1b[0m`);
}

async function beforeBuild(context) {
  const { platform, arch } = context;
  
  log(`Starting pre-build for platform: ${platform}, arch: ${arch}`);
  
  try {
    // Clean any existing builds
    log('Cleaning previous native builds...');
    try {
      execSync('find node_modules -name "*.node" -delete', { 
        stdio: 'pipe',
        cwd: __dirname 
      });
    } catch (e) {
      // Ignore errors - files might not exist
    }
    
    // Rebuild native dependencies for target platform
    let rebuildCommand;
    
    if (platform === 'win32') {
      log('Rebuilding native dependencies for Windows...');
      rebuildCommand = `electron-rebuild --platform=win32 --arch=${arch}`;
    } else if (platform === 'darwin') {
      log('Rebuilding native dependencies for macOS...');
      rebuildCommand = `electron-rebuild --platform=darwin --arch=${arch}`;
    } else {
      log('Rebuilding native dependencies for Linux...');
      rebuildCommand = `electron-rebuild --platform=linux --arch=${arch}`;
    }
    
    log(`Running: ${rebuildCommand}`);
    execSync(rebuildCommand, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
      env: {
        ...process.env,
        npm_config_target_platform: platform,
        npm_config_target_arch: arch,
        npm_config_disturl: 'https://electronjs.org/headers',
        npm_config_runtime: 'electron',
        npm_config_cache: path.join(__dirname, '..', '.electron-gyp'),
        npm_config_build_from_source: 'true'
      }
    });
    
    logSuccess(`Native dependencies rebuilt successfully for ${platform}/${arch}`);
    
  } catch (error) {
    logError(`Failed to rebuild native dependencies: ${error.message}`);
    throw error;
  }
}

module.exports = beforeBuild; 