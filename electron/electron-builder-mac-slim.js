const config = {
  "appId": "dev.bistro.app",
  "productName": "Bistro",
  "directories": {
    "output": "release/"
  },
  "files": [
    "dist/**/*",
    "app/**/*",
    "capacitor.config.json",
    "node_modules/electron-updater/**/*",
    "node_modules/pouchdb-node/**/*",
    "node_modules/pouchdb-find/**/*",
    "node_modules/nano/**/*"
  ],
  "asarUnpack": [
    "node_modules/pouchdb-node/**/*",
    "resources/couchdb-macos/**/*"
  ],
  "extraResources": [
    {
      "from": "resources/couchdb-macos",
      "to": "couchdb-macos",
      "filter": [
        "**/*"
      ]
    }
  ],
  "mac": {
    "category": "public.app-category.business",
    "target": [
      {
        "target": "dmg",
        "arch": [
          "x64",
          "arm64"
        ]
      },
      {
        "target": "zip",
        "arch": [
          "x64",
          "arm64"
        ]
      }
    ],
    "icon": "resources/icon.icns"
  },
  "publish": [
    {
      "provider": "generic",
      "url": "https://bistro.icu/api/updates/check"
    }
  ]
};

module.exports = config;