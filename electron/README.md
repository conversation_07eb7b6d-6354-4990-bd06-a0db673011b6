# Bistro Electron App

This is the Electron desktop application for Bistro, built using Capacitor.

## Development

To run the app in development mode:

```bash
# From the root directory
npm run electron:dev
```

This will:
1. Start the Next.js development server
2. Watch for TypeScript changes in the Electron app
3. Run the Electron app connected to the dev server

## Building for Production

To build the app for production:

```bash
# From the root directory
npm run electron:dist
```

This creates distributable packages in the `electron/release` folder.

## Structure

- `src/` - TypeScript source code for the Electron app
  - `index.ts` - Main Electron process entry point
  - `preload.ts` - Preload script for secure communication
  - `setup.ts` - Capacitor plugin setup
  - `utils.ts` - Utility functions

## Customization

To customize the Electron app, edit the following:

- `capacitor.config.ts` - Main Capacitor configuration
- `electron/package.json` - Electron app metadata and dependencies
- `electron/src/index.ts` - Main window configuration

## Troubleshooting

If you encounter issues:

1. Make sure Next.js server is running correctly
2. Check Electron console for errors (View > Toggle Developer Tools)
3. Verify that capacitor.config.ts has the correct webDir setting 

## Updating the App (Offline/Manual)

Since this app is designed for offline/local use, updates are **manual**:

- When a new version is available, download the new `.dmg` or `.zip` from your distribution source.
- Close the Bistro app if it is running.
- Replace the old app in your `Applications` folder (or wherever you keep it) with the new one.
- Open the new version as usual.

There is **no automatic update**. If you need to update multiple devices, copy the new installer to each device and repeat the steps above.