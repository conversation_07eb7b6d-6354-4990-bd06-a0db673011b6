import * as dgram from 'dgram';
import * as os from 'os';

/**
 * IGMP Querier Implementation
 * 
 * Solves the common home network problem where mDNS devices "disappear"
 * after a while due to missing IGMP queriers in home routers.
 * 
 * Based on research from: https://github.com/machinekoder/querierd
 */
export class IGMPQuerier {
  private socket: dgram.Socket | null = null;
  private queryInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private readonly MULTICAST_ADDRESS = '*********'; // All systems multicast
  private readonly IGMP_QUERY_INTERVAL = 125000; // 125 seconds (RFC default)
  private readonly IGMP_QUERY_TYPE = 0x11; // IGMP v2 General Query
  private localIP: string = '';

  constructor(private logger?: (message: string) => void) {
    this.log = this.log.bind(this);
  }

  /**
   * Start the IGMP querier service
   */
  async start(): Promise<boolean> {
    if (this.isRunning) {
      this.log('IGMP Querier already running');
      return true;
    }

    try {
      // Get local IP address
      this.localIP = this.getLocalIPAddress();
      if (!this.localIP) {
        throw new Error('Could not determine local IP address');
      }

      this.log(`🔍 Starting IGMP Querier on ${this.localIP}`);

      // Create UDP socket
      this.socket = dgram.createSocket({ type: 'udp4', reuseAddr: true });

      // Set up socket options for multicast
      this.socket.bind(0, this.localIP, () => {
        if (!this.socket) return;

        try {
          // Set TTL for multicast packets
          this.socket.setMulticastTTL(1);
          
          // Set multicast interface
          this.socket.setMulticastInterface(this.localIP);
          
          this.log(`✅ IGMP Querier socket bound to ${this.localIP}`);
          
          // Start sending queries
          this.startQueryLoop();
          this.isRunning = true;
          
        } catch (error) {
          this.log(`❌ Error configuring multicast: ${error}`);
          this.stop();
        }
      });

      this.socket.on('error', (error) => {
        this.log(`❌ IGMP Querier socket error: ${error}`);
        this.stop();
      });

      return true;

    } catch (error) {
      this.log(`❌ Failed to start IGMP Querier: ${error}`);
      return false;
    }
  }

  /**
   * Stop the IGMP querier service
   */
  stop(): void {
    this.log('🛑 Stopping IGMP Querier');
    
    this.isRunning = false;

    if (this.queryInterval) {
      clearInterval(this.queryInterval);
      this.queryInterval = null;
    }

    if (this.socket) {
      try {
        this.socket.close();
      } catch (error) {
        this.log(`Warning: Error closing socket: ${error}`);
      }
      this.socket = null;
    }
  }

  /**
   * Start the query loop
   */
  private startQueryLoop(): void {
    // Send initial query immediately
    this.sendIGMPQuery();

    // Set up interval for regular queries
    this.queryInterval = setInterval(() => {
      this.sendIGMPQuery();
    }, this.IGMP_QUERY_INTERVAL);

    this.log(`🔄 IGMP Query loop started (interval: ${this.IGMP_QUERY_INTERVAL}ms)`);
  }

  /**
   * Send an IGMP General Query packet
   */
  private sendIGMPQuery(): void {
    if (!this.socket || !this.isRunning) {
      return;
    }

    try {
      // Create IGMP v2 General Query packet
      const packet = this.createIGMPQueryPacket();
      
      // Send to all-systems multicast address
      this.socket.send(packet as Uint8Array, 0, packet.length, 0, this.MULTICAST_ADDRESS, (error) => {
        if (error) {
          this.log(`❌ Error sending IGMP query: ${error}`);
        } else {
          this.log(`📡 IGMP Query sent to ${this.MULTICAST_ADDRESS}`);
        }
      });

    } catch (error) {
      this.log(`❌ Error creating IGMP query: ${error}`);
    }
  }

  /**
   * Create an IGMP v2 General Query packet
   */
  private createIGMPQueryPacket(): Buffer {
    // IGMP v2 General Query packet structure:
    // Type (1 byte): 0x11 (General Query)
    // Max Response Time (1 byte): 100 (10 seconds in 1/10 second units)
    // Checksum (2 bytes): calculated
    // Group Address (4 bytes): 0.0.0.0 for general query
    
    const packet = Buffer.alloc(8);
    
    packet[0] = this.IGMP_QUERY_TYPE; // Type: General Query
    packet[1] = 100; // Max Response Time: 10 seconds
    packet[2] = 0; // Checksum (will be calculated)
    packet[3] = 0; // Checksum
    packet[4] = 0; // Group Address: 0.0.0.0
    packet[5] = 0;
    packet[6] = 0;
    packet[7] = 0;

    // Calculate checksum
    const checksum = this.calculateChecksum(packet);
    packet[2] = (checksum >> 8) & 0xFF;
    packet[3] = checksum & 0xFF;

    return packet;
  }

  /**
   * Calculate Internet checksum for IGMP packet
   */
  private calculateChecksum(packet: Buffer): number {
    let sum = 0;
    
    // Sum all 16-bit words
    for (let i = 0; i < packet.length; i += 2) {
      if (i + 1 < packet.length) {
        sum += (packet[i] << 8) + packet[i + 1];
      } else {
        sum += packet[i] << 8;
      }
    }

    // Add carry bits
    while (sum >> 16) {
      sum = (sum & 0xFFFF) + (sum >> 16);
    }

    // One's complement
    return (~sum) & 0xFFFF;
  }

  /**
   * Get the local IP address for the default network interface
   */
  private getLocalIPAddress(): string {
    const interfaces = os.networkInterfaces();
    
    // Look for the first non-internal IPv4 address
    for (const interfaceName in interfaces) {
      const addresses = interfaces[interfaceName];
      if (!addresses) continue;

      for (const address of addresses) {
        if (address.family === 'IPv4' && !address.internal) {
          return address.address;
        }
      }
    }

    return '';
  }

  /**
   * Check if the querier is running
   */
  isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Get status information
   */
  getStatus(): {
    running: boolean;
    localIP: string;
    queryInterval: number;
  } {
    return {
      running: this.isRunning,
      localIP: this.localIP,
      queryInterval: this.IGMP_QUERY_INTERVAL
    };
  }

  /**
   * Log messages
   */
  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [IGMP Querier] ${message}`;
    
    if (this.logger) {
      this.logger(logMessage);
    } else {
      console.log(logMessage);
    }
  }
} 