/// <reference types="electron" />
/// <reference types="node" />

// This file contains type declarations for modules without proper TypeScript definitions

declare module 'pouchdb-find';
declare module 'pouchdb-server';
declare module 'get-port';
declare module 'bonjour';
declare module 'bonjour-service';

// Local type definitions instead of imports
interface PeerInfo {
  ip: string;
  port: number;
  hostname: string;
  id: string;
}

interface SyncStatus {
  peerId: string;
  dbName: string;
  status: 'active' | 'complete' | 'paused' | 'error';
  direction: 'push' | 'pull' | 'both';
  details?: any;
}

// ElectronAPI interface for the preload exposed methods
interface ElectronAPI {
  send: (channel: string, data: any) => void;
  receive: (channel: string, func: (...args: any[]) => void) => void;
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  getPouchData: (dbName: string, options?: any) => Promise<any>;
  savePouchData: (dbName: string, data: any) => Promise<any>;
  findPouchData: (dbName: string, query: any) => Promise<any>;
  database: {
    ensureDbOpened: (dbIdentifier: string) => Promise<any>;
    get: (dbIdentifier: string, docId: string, options?: any) => Promise<any>;
    put: (dbIdentifier: string, doc: any) => Promise<any>;
    remove: (dbIdentifier: string, docId: string, rev: string) => Promise<any>;
    bulkDocs: (dbIdentifier: string, docsParam: Array<any> | { docs: Array<any> }, options?: any) => Promise<any>;
    createIndex: (dbIdentifier: string, indexDefinition: any) => Promise<any>;
    find: (dbIdentifier: string, findRequest: any) => Promise<any>;
    close: (dbIdentifier: string) => Promise<any>;
    destroy: (dbIdentifier: string) => Promise<any>;
  };
  p2pSync: {
    getPeers: () => Promise<PeerInfo[]>;
    getSyncStatus: () => Promise<SyncStatus[]>;
    startSync: (peerId: string, dbName: string, direction?: 'push' | 'pull' | 'both') => Promise<any>;
    stopSync: (peerId: string, dbName: string) => Promise<any>;
    stopAllSyncsWithPeer: (peerId: string) => Promise<any>;
    onPeerDiscovered: (callback: (peerInfo: PeerInfo) => void) => void;
    onPeerLost: (callback: (peerId: string) => void) => void;
    onSyncStatusUpdated: (callback: (status: SyncStatus) => void) => void;
  };
}

// Augment window interface for use in renderer process
interface Window {
  electronAPI: ElectronAPI;
  IS_DESKTOP_APP: boolean;
}

// Fix missing types in PouchDB modules
declare namespace PouchDB {
  interface Database {
    // Keep existing methods, add any missing ones
  }
}

interface IpcMainEvent {
  // Add any properties needed from IpcMainEvent
}

// Add any other missing interfaces or types 