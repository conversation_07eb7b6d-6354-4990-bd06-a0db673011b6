// New mDNS discovery handlers
export function setupMdnsHandlers(ipcMain: Electron.IpcMain) {
  const discoveredServices = new Map();
  let bonjourInstance: any = null;
  let browsers: any[] = [];

  // Handler to start mDNS discovery for a specific service type
  ipcMain.handle('mdns:start-discovery', async (event, serviceType = '_http._tcp') => {
    try {
      console.log(`[mDNS] Starting discovery for service type: ${serviceType}`);
      
      // Clean up previous browsers for this service type
      browsers = browsers.filter(browser => {
        if (browser._type === serviceType) {
          try {
            browser.stop();
            return false;
          } catch (e) {
            console.error('[mDNS] Error stopping browser:', e);
          }
        }
        return true;
      });
      
      // Create a new Bonjour instance if needed
      if (!bonjourInstance) {
        const Bonjour = require('bonjour-service');
        bonjourInstance = new Bonjour();
        console.log('[mDNS] Created new Bonjour instance');
      }
      
      // Clear previously discovered services of this type
      discoveredServices.clear();
      
      // Create a new browser for this service type
      const browser = bonjourInstance.find({ type: serviceType });
      browsers.push(browser);
      
      // Handle service discovery
      browser.on('up', (service: any) => {
        console.log('[mDNS] Service up:', service.name);
        const serviceInfo = {
          name: service.name,
          host: service.host,
          port: service.port,
          type: service.type,
          addresses: service.addresses || [],
          txtRecord: service.txt || {},
          fullname: service.fqdn
        };
        
        // Store the service and notify the renderer
        discoveredServices.set(`${service.name}-${service.host}`, serviceInfo);
        event.sender.send('mdns:service-updated', Array.from(discoveredServices.values()));
      });
      
      // Handle service removal
      browser.on('down', (service: any) => {
        console.log('[mDNS] Service down:', service.name);
        discoveredServices.delete(`${service.name}-${service.host}`);
        event.sender.send('mdns:service-updated', Array.from(discoveredServices.values()));
      });
      
      return { success: true };
    } catch (error) {
      console.error('[mDNS] Error starting discovery:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  });
  
  // Handler to stop mDNS discovery
  ipcMain.handle('mdns:stop-discovery', async () => {
    try {
      console.log('[mDNS] Stopping all discovery browsers');
      
      // Stop all browsers
      browsers.forEach(browser => {
        try {
          browser.stop();
        } catch (e) {
          console.error('[mDNS] Error stopping browser:', e);
        }
      });
      browsers = [];
      
      // Destroy Bonjour instance if it exists
      if (bonjourInstance) {
        bonjourInstance.destroy();
        bonjourInstance = null;
      }
      
      discoveredServices.clear();
      return { success: true };
    } catch (error) {
      console.error('[mDNS] Error stopping discovery:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  });
  
  // Handler to get all currently discovered services
  ipcMain.handle('mdns:get-services', () => {
    return Array.from(discoveredServices.values());
  });
} 