import * as path from 'path';
import * as fs from 'fs';
import { app, BrowserWindow } from 'electron';
import * as chokidar from 'chokidar';
// Replace electron-is-dev with simple environment check
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Type definition for Capacitor config
export interface CapacitorConfig {
  appId: string;
  appName: string;
  webDir: string;
  server?: {
    androidScheme: string;
  };
  plugins?: {
    CapacitorCommunityElectron?: {
      electronOptions?: {
        title?: string;
        windowWidth?: number;
        windowHeight?: number;
        resizable?: boolean;
        singleInstance?: boolean;
      };
    };
  };
}

// Read and parse the Capacitor config file
export function getCapacitorConfig(): CapacitorConfig {
  const configPath = path.join(app.getAppPath(), 'capacitor.config.json');
  const tsConfigPath = path.join(app.getAppPath(), 'capacitor.config.ts');
  const jsConfigPath = path.join(app.getAppPath(), 'capacitor.config.js');

  let configData: CapacitorConfig = {
    appId: 'com.example.app',
    appName: 'Bistro',
    webDir: '.next',
  };

  try {
    if (fs.existsSync(configPath)) {
      const configFile = fs.readFileSync(configPath, 'utf8');
      configData = JSON.parse(configFile);
    } else if (fs.existsSync(jsConfigPath)) {
      // In production, we'll use the compiled JS version
      const config = require(jsConfigPath).default;
      configData = config;
    } else if (fs.existsSync(tsConfigPath)) {
      // For typescript config, we need to read it as a string
      // This is not ideal but works for basic extraction
      const configFile = fs.readFileSync(tsConfigPath, 'utf8');
      const appIdMatch = configFile.match(/appId:\s*['"]([^'"]+)['"]/);
      const appNameMatch = configFile.match(/appName:\s*['"]([^'"]+)['"]/);
      const webDirMatch = configFile.match(/webDir:\s*['"]([^'"]+)['"]/);

      if (appIdMatch && appIdMatch[1]) configData.appId = appIdMatch[1];
      if (appNameMatch && appNameMatch[1]) configData.appName = appNameMatch[1];
      if (webDirMatch && webDirMatch[1]) configData.webDir = webDirMatch[1];
    }
  } catch (error) {
    console.error('Error reading Capacitor config:', error);
  }

  return configData;
}

// Setup file watcher for development mode hot reload
export function setupReloadWatcher(window: BrowserWindow): void {
  if (!isDev) return;
  
  // In dev mode, we're watching the development server
  // The actual reload happens when the development server updates
  // This is just to prevent crashes if something changes locally
  
  window.webContents.on('did-fail-load', () => {
    console.log('Page failed to load, retrying in 1 second...');
    setTimeout(() => {
      if (!window.isDestroyed()) {
        window.webContents.reload();
      }
    }, 1000);
  });
}