#!/bin/bash

# 🍎 Fast DMG-Only macOS Deployment Script
# This script builds ONLY the DMG file for maximum speed
# No ZIP, no auto-updater metadata - just the DMG installer

set -e  # Exit on any error

echo "🍎 Starting FAST DMG-only macOS deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
RELEASE_DIR="electron/release"
LOCAL_RELEASES_DIR="releases/macos-dmg"

echo ""
echo -e "${CYAN}${BOLD}⚡ FAST DMG-ONLY DEPLOYMENT${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Release Directory: ${RELEASE_DIR}"
echo -e "  Local Output: ${LOCAL_RELEASES_DIR}"
echo -e "  Build Type: DMG-only (fastest)"
echo -e "  Skipping: ZIP archive, auto-updater metadata"
echo ""

# Step 1: Validate environment
echo -e "${YELLOW}🔍 Validating environment...${NC}"

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ This script must be run on macOS to build macOS apps!${NC}"
    echo -e "  Current OS: $OSTYPE"
    exit 1
fi

echo -e "${GREEN}  ✅ Running on macOS${NC}"

# Check for required tools
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found!${NC}"
    exit 1
fi

echo -e "${GREEN}  ✅ Node.js found: $(node --version)${NC}"
echo ""

# Step 2: Ensure directories exist (no cleaning)
echo -e "${YELLOW}📁 Ensuring necessary directories exist...${NC}"

# Create local releases directory
mkdir -p "$LOCAL_RELEASES_DIR"
echo -e "${GREEN}  ✅ Ensured local releases directory exists${NC}"
echo ""

# Step 3: Install dependencies (optimized)
echo -e "${YELLOW}📦 Installing dependencies (optimized)...${NC}"

# Use npm ci for faster installs, skip audit/fund for speed
echo -e "  ${BLUE}Installing root dependencies...${NC}"
if [ -f "package-lock.json" ]; then
    npm ci --no-audit --no-fund --silent || npm install --legacy-peer-deps --no-audit --no-fund --silent
else
    npm install --legacy-peer-deps --no-audit --no-fund --silent
fi

echo -e "  ${BLUE}Installing Electron dependencies...${NC}"
cd electron

if [ -f "package-lock.json" ]; then
    npm ci --no-audit --no-fund --silent || npm install --legacy-peer-deps --no-audit --no-fund --silent
else
    npm install --legacy-peer-deps --no-audit --no-fund --silent
fi

cd ..
echo -e "${GREEN}  ✅ Dependencies installed${NC}"
echo ""

# Step 4: Prepare CouchDB binaries
echo -e "${YELLOW}🗄️ Preparing CouchDB binaries...${NC}"

# Check if CouchDB binaries exist
COUCHDB_DIR="electron/resources/couchdb-macos"
if [ ! -d "$COUCHDB_DIR" ] || [ ! -f "$COUCHDB_DIR/bin/couchdb" ]; then
    echo -e "  ${BLUE}CouchDB binaries not found, downloading...${NC}"
    
    # Create resources directory
    mkdir -p electron/resources
    
    # Download and extract CouchDB for macOS
    TEMP_DIR=$(mktemp -d)
    echo -e "  ${BLUE}Downloading CouchDB 3.3.3 for macOS...${NC}"
    curl -L "https://archive.apache.org/dist/couchdb/binary/mac/3.3.3/Apache-CouchDB-3.3.3.zip" -o "$TEMP_DIR/couchdb.zip"
    
    echo -e "  ${BLUE}Extracting CouchDB...${NC}"
    cd "$TEMP_DIR"
    unzip -q couchdb.zip
    
    # Find extracted directory
    EXTRACTED_DIR=$(find . -name "Apache-CouchDB*" -type d | head -1)
    if [ -n "$EXTRACTED_DIR" ]; then
        cd "$OLDPWD"
        mv "$TEMP_DIR/$EXTRACTED_DIR" "$COUCHDB_DIR"
        chmod -R 755 "$COUCHDB_DIR"
        echo -e "${GREEN}  ✅ CouchDB binaries prepared${NC}"
    else
        echo -e "${RED}❌ Failed to extract CouchDB${NC}"
        exit 1
    fi
    
    # Cleanup
    rm -rf "$TEMP_DIR"
else
    echo -e "${GREEN}  ✅ CouchDB binaries already available${NC}"
fi
echo ""

# Step 5: Build ONLY the DMG (fastest)
echo -e "${YELLOW}⚡ Building DMG-only (fastest build)...${NC}"
echo -e "  ${BLUE}Building Next.js static files first...${NC}"
npm run build:electron
echo ""
echo -e "  ${BLUE}Building production-ready DMG installer...${NC}"
echo -e "  ${CYAN}Skipping ZIP and auto-updater for maximum speed${NC}"
echo ""

START_TIME=$(date +%s)

cd electron
npm run electron:build:mac:dmg
cd ..

END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))

echo ""
echo -e "${GREEN}  ✅ DMG build completed in ${BUILD_TIME} seconds${NC}"
echo ""

# Step 6: Find and organize the DMG file
echo -e "${YELLOW}🔍 Locating DMG file...${NC}"

# Find DMG file only
DMG_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.dmg" | head -1)

if [ -z "$DMG_FILE" ]; then
    echo -e "${RED}❌ No DMG file found in release directory!${NC}"
    echo -e "  Expected location: ${RELEASE_DIR}"
    echo -e "  Available files:"
    ls -la "$RELEASE_DIR" 2>/dev/null || echo "    (directory not found)"
    exit 1
fi

# Copy DMG to local releases directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_SUBDIR="${LOCAL_RELEASES_DIR}/${TIMESTAMP}"
mkdir -p "$RELEASE_SUBDIR"

echo -e "${GREEN}  ✅ Created release subdirectory: ${RELEASE_SUBDIR}${NC}"

# Copy DMG file
cp "$DMG_FILE" "$RELEASE_SUBDIR/"
DMG_SIZE=$(stat -f%z "$DMG_FILE" 2>/dev/null || echo "unknown")
if [ "$DMG_SIZE" != "unknown" ]; then
    SIZE_MB=$((DMG_SIZE / 1048576))
    echo -e "${GREEN}  ✅ Copied DMG: $(basename "$DMG_FILE") (${SIZE_MB} MB)${NC}"
else
    echo -e "${GREEN}  ✅ Copied DMG: $(basename "$DMG_FILE")${NC}"
fi

# Create a "latest" symlink
cd "$LOCAL_RELEASES_DIR"
rm -f latest
ln -s "$TIMESTAMP" latest
cd - > /dev/null

echo -e "${GREEN}  ✅ Created 'latest' symlink${NC}"
echo ""

# Step 7: Create minimal release info
echo -e "${YELLOW}📝 Creating release information...${NC}"

RELEASE_INFO_FILE="${RELEASE_SUBDIR}/release-info.txt"
cat > "$RELEASE_INFO_FILE" << EOF
🍎 Shop macOS DMG-Only Release
==============================

Build Date: $(date)
Build Time: ${BUILD_TIME} seconds
Build Host: $(hostname)
macOS Version: $(sw_vers -productVersion)
Node.js Version: $(node --version)

Files in this release:
  - $(basename "$DMG_FILE") (${SIZE_MB} MB)

Installation Instructions:
  1. Double-click the DMG file to mount it
  2. Drag Bistro.app to your Applications folder
  3. Eject the DMG when done

Note: This is a DMG-only build for fastest deployment.
No ZIP archive or auto-updater metadata included.

EOF

echo -e "${GREEN}  ✅ Created release information file${NC}"
echo ""

# Final success message
echo -e "${GREEN}${BOLD}🎉 FAST DMG-ONLY DEPLOYMENT COMPLETED!${NC}"
echo ""
echo -e "${CYAN}📋 Deployment Summary:${NC}"
echo -e "  ✅ Built macOS DMG installer in ${BUILD_TIME} seconds"
echo -e "  ✅ Skipped ZIP and auto-updater (faster build)"
echo -e "  ✅ Organized DMG in local releases directory"
echo -e "  ✅ Created 'latest' symlink for easy access"
echo ""
echo -e "${YELLOW}📁 Release Location:${NC}"
echo -e "  DMG File: ${PWD}/${RELEASE_SUBDIR}/$(basename "$DMG_FILE")"
echo -e "  Latest Link: ${PWD}/${LOCAL_RELEASES_DIR}/latest"
echo ""
echo -e "${YELLOW}⚡ Speed Optimizations Applied:${NC}"
echo -e "  ✅ DMG-only build (no ZIP generation)"
echo -e "  ✅ No auto-updater metadata"
echo -e "  ✅ Silent dependency installation"
echo -e "  ✅ Minimal file operations"
echo ""
echo -e "${YELLOW}🔗 What's Next:${NC}"
echo -e "  1. 📁 Navigate to: ${BOLD}${LOCAL_RELEASES_DIR}/latest${NC}"
echo -e "  2. 🍎 Test the DMG installer"
echo -e "  3. 📤 Distribute the DMG file to your users"
echo ""
echo -e "${GREEN}${BOLD}✅ Your fast macOS DMG is ready!${NC}"
echo ""