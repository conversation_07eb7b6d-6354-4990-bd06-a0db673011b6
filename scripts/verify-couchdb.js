#!/usr/bin/env node

/**
 * 🔍 CouchDB Verification Script
 * 
 * Verifies that CouchDB binaries are properly bundled and can launch
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

function log(message, color = '\x1b[36m') {
  console.log(`${color}🔍 [CouchDB Verify] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [CouchDB Verify] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [CouchDB Verify] ${message}\x1b[0m`);
}

async function verifyCouchDB() {
  log('Verifying CouchDB installation...');
  
  const platform = process.platform;
  const resourcesDir = path.join(__dirname, '..', 'electron', 'resources');
  
  let couchdbDir, binaryName;
  
  if (platform === 'darwin') {
    couchdbDir = path.join(resourcesDir, 'couchdb-macos');
    binaryName = 'couchdb';
  } else if (platform === 'win32') {
    couchdbDir = path.join(resourcesDir, 'couchdb-windows');
    binaryName = 'couchdb.cmd';
  } else {
    couchdbDir = path.join(resourcesDir, 'couchdb-linux');
    binaryName = 'couchdb';
  }
  
  // Check if directory exists
  if (!fs.existsSync(couchdbDir)) {
    logError(`CouchDB directory not found: ${couchdbDir}`);
    return false;
  }
  
  logSuccess(`CouchDB directory found: ${couchdbDir}`);
  
  // Check if binary exists
  const binaryPath = path.join(couchdbDir, 'bin', binaryName);
  if (!fs.existsSync(binaryPath)) {
    logError(`CouchDB binary not found: ${binaryPath}`);
    return false;
  }
  
  logSuccess(`CouchDB binary found: ${binaryPath}`);
  
  // Check if binary is executable
  try {
    const stats = fs.statSync(binaryPath);
    if (platform !== 'win32' && !(stats.mode & parseInt('111', 8))) {
      logError('CouchDB binary is not executable');
      return false;
    }
  } catch (error) {
    logError(`Error checking binary permissions: ${error.message}`);
    return false;
  }
  
  logSuccess('CouchDB binary has correct permissions');
  
  // Test CouchDB startup (quick test)
  log('Testing CouchDB startup...');
  
  return new Promise((resolve) => {
    const child = spawn(binaryPath, [], {
      cwd: couchdbDir,
      stdio: 'pipe',
      timeout: 10000
    });
    
    let output = '';
    let hasStarted = false;
    
    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      // Look for successful startup messages
      if (text.includes('Apache CouchDB has started') || 
          text.includes('Time to relax') ||
          text.includes('has started on http://')) {
        hasStarted = true;
        logSuccess('CouchDB started successfully!');
        child.kill('SIGTERM');
        resolve(true);
      }
    });
    
    child.stderr.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      // Also check stderr for startup messages
      if (text.includes('Apache CouchDB has started') || 
          text.includes('Time to relax') ||
          text.includes('has started on http://')) {
        hasStarted = true;
        logSuccess('CouchDB started successfully!');
        child.kill('SIGTERM');
        resolve(true);
      }
    });
    
    child.on('close', (code) => {
      if (hasStarted) {
        logSuccess('CouchDB verification completed successfully');
        resolve(true);
      } else {
        logError('CouchDB did not start properly');
        console.log('Output:', output.substring(0, 500) + '...');
        resolve(false);
      }
    });
    
    child.on('error', (error) => {
      logError(`Error testing CouchDB binary: ${error.message}`);
      resolve(false);
    });
    
    // Kill after timeout
    setTimeout(() => {
      if (!hasStarted) {
        child.kill('SIGTERM');
        logError('CouchDB startup test timed out');
        resolve(false);
      }
    }, 8000);
  });
}

async function main() {
  try {
    const success = await verifyCouchDB();
    if (success) {
      logSuccess('🎉 CouchDB verification passed! Ready for Electron packaging.');
      logSuccess('CouchDB will launch automatically when the Electron app starts.');
    } else {
      logError('❌ CouchDB verification failed!');
      process.exit(1);
    }
  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { verifyCouchDB }; 