#!/bin/bash

# 🚀 Enhanced Windows Deployment Script with CouchDB Auto-Setup
# This script builds Windows app with automatic CouchDB binary preparation
# Mirrors the successful macOS deployment process

set -e  # Exit on any error

echo "🚀 Starting enhanced Windows deployment with CouchDB auto-setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
RELEASE_DIR="electron/release"
LOCAL_RELEASES_DIR="releases/windows"

echo ""
echo -e "${CYAN}${BOLD}⚡ ENHANCED WINDOWS DEPLOYMENT${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Release Directory: ${RELEASE_DIR}"
echo -e "  Local Output: ${LOCAL_RELEASES_DIR}"
echo -e "  CouchDB: Auto-download and setup"
echo -e "  Build Type: Full production build"
echo ""

# Step 1: Validate environment
echo -e "${YELLOW}🔍 Validating environment...${NC}"

# Check for required tools
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found!${NC}"
    exit 1
fi

echo -e "${GREEN}  ✅ Node.js found: $(node --version)${NC}"
echo ""

# Step 2: Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
if [ -d "$RELEASE_DIR" ]; then
    rm -rf "$RELEASE_DIR"
    echo -e "${GREEN}  ✅ Cleaned release directory${NC}"
else
    echo -e "${BLUE}  ℹ️  No previous builds to clean${NC}"
fi

# Create local releases directory
mkdir -p "$LOCAL_RELEASES_DIR"
echo -e "${GREEN}  ✅ Ensured local releases directory exists${NC}"
echo ""

# Step 3: Install dependencies (optimized)
echo -e "${YELLOW}📦 Installing dependencies (optimized)...${NC}"

# Use npm ci for faster installs, skip audit/fund for speed
echo -e "  ${BLUE}Installing root dependencies...${NC}"
if [ -f "package-lock.json" ]; then
    npm ci --no-audit --no-fund --silent || npm install --legacy-peer-deps --no-audit --no-fund --silent
else
    npm install --legacy-peer-deps --no-audit --no-fund --silent
fi

echo -e "  ${BLUE}Installing Electron dependencies...${NC}"
cd electron

if [ -f "package-lock.json" ]; then
    npm ci --no-audit --no-fund --silent || npm install --legacy-peer-deps --no-audit --no-fund --silent
else
    npm install --legacy-peer-deps --no-audit --no-fund --silent
fi

cd ..
echo -e "${GREEN}  ✅ Dependencies installed${NC}"
echo ""

# Step 4: Prepare CouchDB binaries for Windows
echo -e "${YELLOW}🗄️ Preparing CouchDB binaries for Windows...${NC}"

# Check if CouchDB binaries exist
COUCHDB_DIR="electron/resources/couchdb-windows"
if [ ! -d "$COUCHDB_DIR" ] || [ ! -f "$COUCHDB_DIR/bin/couchdb.cmd" ]; then
    echo -e "  ${BLUE}CouchDB binaries not found, downloading...${NC}"
    
    # Create resources directory
    mkdir -p electron/resources
    
    # Download and extract CouchDB for Windows
    TEMP_DIR=$(mktemp -d)
    echo -e "  ${BLUE}Downloading CouchDB 3.3.3 for Windows...${NC}"
    
    # Use the official Windows binary
    curl -L "https://archive.apache.org/dist/couchdb/binary/win/3.3.3/apache-couchdb-3.3.3.msi" -o "$TEMP_DIR/couchdb.msi"
    
    echo -e "  ${BLUE}Extracting CouchDB from MSI...${NC}"
    cd "$TEMP_DIR"
    
    # Extract MSI using msiextract (if available) or 7zip
    if command -v msiextract &> /dev/null; then
        msiextract couchdb.msi
    elif command -v 7z &> /dev/null; then
        7z x couchdb.msi
    else
        echo -e "  ${YELLOW}⚠️  MSI extraction tools not found, trying alternative approach...${NC}"
        # Try to download the ZIP version instead
        curl -L "https://github.com/apache/couchdb/releases/download/3.3.3/apache-couchdb-3.3.3-win64.zip" -o couchdb.zip
        if [ -f "couchdb.zip" ]; then
            unzip -q couchdb.zip
            EXTRACTED_DIR=$(find . -name "*couchdb*" -type d | head -1)
        else
            echo -e "${RED}❌ Could not download CouchDB ZIP. Please manually place CouchDB binaries in ${COUCHDB_DIR}${NC}"
            echo -e "  Download from: https://couchdb.apache.org/"
            echo -e "  Extract to: ${COUCHDB_DIR}"
            echo -e "  Ensure: ${COUCHDB_DIR}/bin/couchdb.cmd exists"
            exit 1
        fi
    fi
    
    # Find extracted directory and move it
    if [ -z "$EXTRACTED_DIR" ]; then
        EXTRACTED_DIR=$(find . -name "*couchdb*" -type d | head -1)
    fi
    
    if [ -n "$EXTRACTED_DIR" ]; then
        cd "$OLDPWD"
        mv "$TEMP_DIR/$EXTRACTED_DIR" "$COUCHDB_DIR"
        chmod -R 755 "$COUCHDB_DIR" 2>/dev/null || true  # Ignore chmod errors on Windows
        echo -e "${GREEN}  ✅ CouchDB binaries prepared${NC}"
    else
        echo -e "${RED}❌ Failed to extract CouchDB${NC}"
        echo -e "  ${YELLOW}Manual setup required:${NC}"
        echo -e "  1. Download CouchDB from: https://couchdb.apache.org/"
        echo -e "  2. Extract to: ${COUCHDB_DIR}"
        echo -e "  3. Ensure: ${COUCHDB_DIR}/bin/couchdb.cmd exists"
        exit 1
    fi
    
    # Cleanup
    rm -rf "$TEMP_DIR"
else
    echo -e "${GREEN}  ✅ CouchDB binaries already available${NC}"
fi
echo ""

# Step 5: Build the Windows app
echo -e "${YELLOW}⚡ Building Windows app...${NC}"
echo -e "  ${BLUE}Building Next.js static files first...${NC}"
npm run build:electron
echo ""
echo -e "  ${BLUE}Building production-ready Windows installer...${NC}"
echo -e "  ${CYAN}Note: USB detection warnings are normal for cross-platform builds${NC}"
echo ""

START_TIME=$(date +%s)

cd electron
npm run electron:build:win
cd ..

END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))

echo ""
echo -e "${GREEN}  ✅ Windows build completed in ${BUILD_TIME} seconds${NC}"
echo ""

# Step 6: Find and organize all release files
echo -e "${YELLOW}🔍 Locating Windows release files...${NC}"

# Find .exe file
EXE_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.exe" | head -1)
# Find latest.yml file
YML_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "latest.yml" | head -1)
# Find .blockmap file
BLOCKMAP_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.blockmap" | head -1)

if [ -z "$EXE_FILE" ]; then
    echo -e "${RED}❌ No .exe file found in release directory!${NC}"
    echo -e "  Expected location: ${RELEASE_DIR}"
    echo -e "  Available files:"
    ls -la "$RELEASE_DIR" 2>/dev/null || echo "    (directory not found)"
    exit 1
fi

# Copy files to local releases directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_SUBDIR="${LOCAL_RELEASES_DIR}/${TIMESTAMP}"
mkdir -p "$RELEASE_SUBDIR"

echo -e "${GREEN}  ✅ Created release subdirectory: ${RELEASE_SUBDIR}${NC}"

# Copy .exe file
cp "$EXE_FILE" "$RELEASE_SUBDIR/"
EXE_SIZE=$(stat -f%z "$EXE_FILE" 2>/dev/null || stat -c%s "$EXE_FILE" 2>/dev/null || echo "unknown")
if [ "$EXE_SIZE" != "unknown" ]; then
    SIZE_MB=$((EXE_SIZE / 1048576))
    echo -e "${GREEN}  ✅ Copied executable: $(basename "$EXE_FILE") (${SIZE_MB} MB)${NC}"
else
    echo -e "${GREEN}  ✅ Copied executable: $(basename "$EXE_FILE")${NC}"
fi

# Copy latest.yml file (for auto-updater)
if [ -n "$YML_FILE" ]; then
    cp "$YML_FILE" "$RELEASE_SUBDIR/"
    echo -e "${GREEN}  ✅ Copied auto-updater metadata: $(basename "$YML_FILE")${NC}"
else
    echo -e "${YELLOW}  ⚠️  Auto-updater metadata file (latest.yml) not found${NC}"
fi

# Copy .blockmap file (for efficient updates)
if [ -n "$BLOCKMAP_FILE" ]; then
    cp "$BLOCKMAP_FILE" "$RELEASE_SUBDIR/"
    BLOCKMAP_SIZE=$(stat -f%z "$BLOCKMAP_FILE" 2>/dev/null || stat -c%s "$BLOCKMAP_FILE" 2>/dev/null || echo "unknown")
    if [ "$BLOCKMAP_SIZE" != "unknown" ]; then
        SIZE_KB=$((BLOCKMAP_SIZE / 1024))
        echo -e "${GREEN}  ✅ Copied blockmap file: $(basename "$BLOCKMAP_FILE") (${SIZE_KB} KB)${NC}"
    else
        echo -e "${GREEN}  ✅ Copied blockmap file: $(basename "$BLOCKMAP_FILE")${NC}"
    fi
else
    echo -e "${YELLOW}  ⚠️  Blockmap file not found${NC}"
fi

# Create a "latest" symlink
cd "$LOCAL_RELEASES_DIR"
rm -f latest
ln -s "$TIMESTAMP" latest
cd - > /dev/null

echo -e "${GREEN}  ✅ Created 'latest' symlink${NC}"
echo ""

# Step 7: Create release info
echo -e "${YELLOW}📝 Creating release information...${NC}"

RELEASE_INFO_FILE="${RELEASE_SUBDIR}/release-info.txt"
cat > "$RELEASE_INFO_FILE" << EOF
🪟 Shop Windows Enhanced Release
================================

Build Date: $(date)
Build Time: ${BUILD_TIME} seconds
Build Host: $(hostname)
Node.js Version: $(node --version)
CouchDB: Bundled (auto-downloaded)

Files in this release:
  - $(basename "$EXE_FILE") (${SIZE_MB} MB)

Installation Instructions:
  1. Double-click the .exe file to install
  2. Follow the installation wizard
  3. Launch Shop from Start Menu or Desktop

Features:
  ✅ Fully static app (connects to remote server)
  ✅ Bundled CouchDB server for offline data
  ✅ Production-ready build
  ✅ Auto-updater ready

Note: This build includes automatic CouchDB setup,
mirroring the successful macOS deployment process.

EOF

echo -e "${GREEN}  ✅ Created release information file${NC}"
echo ""

# Final success message
echo -e "${GREEN}${BOLD}🎉 ENHANCED WINDOWS DEPLOYMENT COMPLETED!${NC}"
echo ""
echo -e "${CYAN}📋 Deployment Summary:${NC}"
echo -e "  ✅ Auto-downloaded and prepared CouchDB binaries"
echo -e "  ✅ Built Windows installer in ${BUILD_TIME} seconds"
echo -e "  ✅ Organized executable in local releases directory"
echo -e "  ✅ Created 'latest' symlink for easy access"
echo -e "  ✅ Mirrors successful macOS deployment process"
echo ""
echo -e "${YELLOW}📁 Release Location:${NC}"
echo -e "  Executable: ${PWD}/${RELEASE_SUBDIR}/$(basename "$EXE_FILE")"
echo -e "  Latest Link: ${PWD}/${LOCAL_RELEASES_DIR}/latest"
echo ""
echo -e "${YELLOW}🔧 Key Improvements:${NC}"
echo -e "  ✅ Automatic CouchDB binary preparation"
echo -e "  ✅ Optimized dependency installation"
echo -e "  ✅ Enhanced error handling and feedback"
echo -e "  ✅ Consistent with macOS deployment process"
echo ""
echo -e "${YELLOW}🔗 What's Next:${NC}"
echo -e "  1. 📁 Navigate to: ${BOLD}${LOCAL_RELEASES_DIR}/latest${NC}"
echo -e "  2. 🪟 Test the Windows installer"
echo -e "  3. 📤 Distribute the .exe file to your users"
echo ""
echo -e "${GREEN}${BOLD}✅ Your enhanced Windows build is ready!${NC}"
echo "" 