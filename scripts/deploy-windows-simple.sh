#!/bin/bash

# 🚀 Simple Windows Deployment Script
# Uses existing bundled CouchDB binaries - NO DOWNLOADS
# Mirrors the successful macOS deployment process

set -e  # Exit on any error

echo "🚀 Starting Windows deployment with bundled CouchDB binaries..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
RELEASE_DIR="electron/release"
LOCAL_RELEASES_DIR="releases/windows"

echo ""
echo -e "${CYAN}${BOLD}⚡ SIMPLE WINDOWS DEPLOYMENT${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Release Directory: ${RELEASE_DIR}"
echo -e "  Local Output: ${LOCAL_RELEASES_DIR}"
echo -e "  CouchDB: Use existing bundled binaries"
echo -e "  Build Type: Full production build"
echo ""

# Step 1: Validate environment
echo -e "${YELLOW}🔍 Validating environment...${NC}"

# Check for required tools
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found!${NC}"
    exit 1
fi

echo -e "${GREEN}  ✅ Node.js found: $(node --version)${NC}"

# Check if CouchDB binaries exist
COUCHDB_DIR="electron/resources/couchdb-windows"
if [ ! -d "$COUCHDB_DIR" ]; then
    echo -e "${RED}❌ CouchDB Windows binaries not found at: ${COUCHDB_DIR}${NC}"
    echo -e "  Please ensure CouchDB binaries are bundled in the resources directory"
    exit 1
fi

if [ ! -f "$COUCHDB_DIR/bin/couchdb" ] && [ ! -f "$COUCHDB_DIR/bin/couchdb.cmd" ]; then
    echo -e "${RED}❌ CouchDB executable not found in: ${COUCHDB_DIR}/bin/${NC}"
    exit 1
fi

echo -e "${GREEN}  ✅ CouchDB Windows binaries found${NC}"
echo ""

# Step 2: Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
if [ -d "$RELEASE_DIR" ]; then
    rm -rf "$RELEASE_DIR"
    echo -e "${GREEN}  ✅ Cleaned release directory${NC}"
else
    echo -e "${BLUE}  ℹ️  No previous builds to clean${NC}"
fi

# Clean electron build directories
if [ -d "electron/dist" ]; then
    rm -rf "electron/dist"
    echo -e "${GREEN}  ✅ Cleaned electron/dist${NC}"
fi

if [ -d "electron/app" ]; then
    rm -rf "electron/app"
    echo -e "${GREEN}  ✅ Cleaned electron/app${NC}"
fi

# Create local releases directory
mkdir -p "$LOCAL_RELEASES_DIR"
echo -e "${GREEN}  ✅ Ensured local releases directory exists${NC}"
echo ""

# Step 3: Install dependencies (optimized)
echo -e "${YELLOW}📦 Installing dependencies (optimized)...${NC}"

# Use npm ci for faster installs, skip audit/fund for speed
echo -e "  ${BLUE}Installing root dependencies...${NC}"
if [ -f "package-lock.json" ]; then
    npm ci --no-audit --no-fund --silent || npm install --legacy-peer-deps --no-audit --no-fund --silent
else
    npm install --legacy-peer-deps --no-audit --no-fund --silent
fi

echo -e "  ${BLUE}Installing Electron dependencies...${NC}"
cd electron

if [ -f "package-lock.json" ]; then
    npm ci --no-audit --no-fund --silent || npm install --legacy-peer-deps --no-audit --no-fund --silent
else
    npm install --legacy-peer-deps --no-audit --no-fund --silent
fi

cd ..
echo -e "${GREEN}  ✅ Dependencies installed${NC}"
echo ""

# Step 4: Verify CouchDB binaries are properly packaged
echo -e "${YELLOW}🗄️ Verifying CouchDB binary packaging...${NC}"

# List key files to confirm they're there
echo -e "  ${BLUE}CouchDB Windows binary structure:${NC}"
if [ -f "$COUCHDB_DIR/bin/couchdb" ]; then
    echo -e "    ✅ couchdb (main executable)"
fi
if [ -f "$COUCHDB_DIR/bin/couchdb.cmd" ]; then
    echo -e "    ✅ couchdb.cmd (Windows batch file)"
fi

# Count total files for reference
TOTAL_FILES=$(find "$COUCHDB_DIR" -type f | wc -l | tr -d ' ')
echo -e "    📊 Total bundled files: ${TOTAL_FILES}"

# Get directory size
DIR_SIZE=$(du -sh "$COUCHDB_DIR" | cut -f1)
echo -e "    📦 Bundle size: ${DIR_SIZE}"

echo -e "${GREEN}  ✅ CouchDB binaries verified and ready${NC}"
echo ""

# Step 5: Build the Windows app
echo -e "${YELLOW}⚡ Building Windows app...${NC}"
echo -e "  ${BLUE}Building Next.js static files first...${NC}"
npm run build:electron
echo ""
echo -e "  ${BLUE}Building production-ready Windows installer...${NC}"
echo -e "  ${CYAN}Using bundled CouchDB binaries (no downloads)${NC}"
echo ""

START_TIME=$(date +%s)

cd electron
npm run electron:build:win
cd ..

END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))

echo ""
echo -e "${GREEN}  ✅ Windows build completed in ${BUILD_TIME} seconds${NC}"
echo ""

# Step 6: Find and organize all release files
echo -e "${YELLOW}🔍 Locating Windows release files...${NC}"

# Find .exe file
EXE_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.exe" | head -1)
# Find latest.yml file
YML_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "latest.yml" | head -1)
# Find .blockmap file
BLOCKMAP_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.blockmap" | head -1)

if [ -z "$EXE_FILE" ]; then
    echo -e "${RED}❌ No .exe file found in release directory!${NC}"
    echo -e "  Expected location: ${RELEASE_DIR}"
    echo -e "  Available files:"
    ls -la "$RELEASE_DIR" 2>/dev/null || echo "    (directory not found)"
    exit 1
fi

# Copy files to local releases directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_SUBDIR="${LOCAL_RELEASES_DIR}/${TIMESTAMP}"
mkdir -p "$RELEASE_SUBDIR"

echo -e "${GREEN}  ✅ Created release subdirectory: ${RELEASE_SUBDIR}${NC}"

# Copy .exe file
cp "$EXE_FILE" "$RELEASE_SUBDIR/"
EXE_SIZE=$(stat -f%z "$EXE_FILE" 2>/dev/null || stat -c%s "$EXE_FILE" 2>/dev/null || echo "unknown")
if [ "$EXE_SIZE" != "unknown" ]; then
    SIZE_MB=$((EXE_SIZE / 1048576))
    echo -e "${GREEN}  ✅ Copied executable: $(basename "$EXE_FILE") (${SIZE_MB} MB)${NC}"
else
    echo -e "${GREEN}  ✅ Copied executable: $(basename "$EXE_FILE")${NC}"
fi

# Copy latest.yml file (for auto-updater)
if [ -n "$YML_FILE" ]; then
    cp "$YML_FILE" "$RELEASE_SUBDIR/"
    echo -e "${GREEN}  ✅ Copied auto-updater metadata: $(basename "$YML_FILE")${NC}"
else
    echo -e "${YELLOW}  ⚠️  Auto-updater metadata file (latest.yml) not found${NC}"
fi

# Copy .blockmap file (for efficient updates)
if [ -n "$BLOCKMAP_FILE" ]; then
    cp "$BLOCKMAP_FILE" "$RELEASE_SUBDIR/"
    BLOCKMAP_SIZE=$(stat -f%z "$BLOCKMAP_FILE" 2>/dev/null || stat -c%s "$BLOCKMAP_FILE" 2>/dev/null || echo "unknown")
    if [ "$BLOCKMAP_SIZE" != "unknown" ]; then
        SIZE_KB=$((BLOCKMAP_SIZE / 1024))
        echo -e "${GREEN}  ✅ Copied blockmap file: $(basename "$BLOCKMAP_FILE") (${SIZE_KB} KB)${NC}"
    else
        echo -e "${GREEN}  ✅ Copied blockmap file: $(basename "$BLOCKMAP_FILE")${NC}"
    fi
else
    echo -e "${YELLOW}  ⚠️  Blockmap file not found${NC}"
fi

# Create a "latest" symlink
cd "$LOCAL_RELEASES_DIR"
rm -f latest
ln -s "$TIMESTAMP" latest
cd - > /dev/null

echo -e "${GREEN}  ✅ Created 'latest' symlink${NC}"
echo ""

# Step 7: Create release info
echo -e "${YELLOW}📝 Creating release information...${NC}"

RELEASE_INFO_FILE="${RELEASE_SUBDIR}/release-info.txt"
cat > "$RELEASE_INFO_FILE" << EOF
🪟 Shop Windows Simple Release
==============================

Build Date: $(date)
Build Time: ${BUILD_TIME} seconds
Build Host: $(hostname)
Node.js Version: $(node --version)
CouchDB: Bundled (existing binaries)

Files in this release:
  - $(basename "$EXE_FILE") (${SIZE_MB} MB) - Windows installer
  $([ -n "$YML_FILE" ] && echo "- $(basename "$YML_FILE") - Auto-updater metadata")
  $([ -n "$BLOCKMAP_FILE" ] && echo "- $(basename "$BLOCKMAP_FILE") - Blockmap for efficient updates")

CouchDB Bundle Info:
  - Location: electron/resources/couchdb-windows
  - Total files: ${TOTAL_FILES}
  - Bundle size: ${DIR_SIZE}
  - No downloads required

Installation Instructions:
  1. Double-click the .exe file to install
  2. Follow the installation wizard
  3. Launch Shop from Start Menu or Desktop

Features:
  ✅ Fully static app (connects to remote server)
  ✅ Bundled CouchDB server for offline data
  ✅ Production-ready build
  ✅ Uses existing binaries (no downloads)

Note: This build uses pre-bundled CouchDB binaries,
ensuring consistent and reliable deployments.

EOF

echo -e "${GREEN}  ✅ Created release information file${NC}"
echo ""

# Final success message
echo -e "${GREEN}${BOLD}🎉 SIMPLE WINDOWS DEPLOYMENT COMPLETED!${NC}"
echo ""
echo -e "${CYAN}📋 Deployment Summary:${NC}"
echo -e "  ✅ Used existing bundled CouchDB binaries"
echo -e "  ✅ Built Windows installer in ${BUILD_TIME} seconds"
echo -e "  ✅ Copied all release files (exe, yml, blockmap) to local directory"
echo -e "  ✅ Auto-updater files included for seamless updates"
echo -e "  ✅ Created 'latest' symlink for easy access"
echo -e "  ✅ No downloads required - fully self-contained"
echo ""
echo -e "${YELLOW}📁 Release Location:${NC}"
echo -e "  Executable: ${PWD}/${RELEASE_SUBDIR}/$(basename "$EXE_FILE")"
echo -e "  Latest Link: ${PWD}/${LOCAL_RELEASES_DIR}/latest"
echo ""
echo -e "${YELLOW}🔧 Key Features:${NC}"
echo -e "  ✅ Uses pre-bundled CouchDB binaries"
echo -e "  ✅ No internet downloads required"
echo -e "  ✅ Consistent with macOS deployment process"
echo -e "  ✅ Clean build with optimized dependencies"
echo ""
echo -e "${YELLOW}🔗 What's Next:${NC}"
echo -e "  1. 📁 Navigate to: ${BOLD}${LOCAL_RELEASES_DIR}/latest${NC}"
echo -e "  2. 🪟 Test the Windows installer"
echo -e "  3. 📤 Distribute the .exe file to your users"
echo ""
echo -e "${GREEN}${BOLD}✅ Your Windows build with bundled CouchDB is ready!${NC}"
echo "" 