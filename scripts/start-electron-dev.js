#!/usr/bin/env node

const { spawn } = require('child_process');
const axios = require('axios');
const { setTimeout } = require('timers/promises');

let nextProcess = null;
let electronWatchProcess = null;
let electronApp = null;

// Cleanup function
function cleanup() {
  console.log('\n🛑 Cleaning up processes...');
  
  if (electronApp) {
    console.log('🔄 Terminating Electron app...');
    electronApp.kill('SIGTERM');
    electronApp = null;
  }
  
  if (electronWatchProcess) {
    console.log('🔄 Terminating Electron watch process...');
    electronWatchProcess.kill('SIGTERM');
    electronWatchProcess = null;
  }
  
  if (nextProcess) {
    console.log('🔄 Terminating Next.js server...');
    nextProcess.kill('SIGTERM');
    nextProcess = null;
  }
  
  // Give processes time to clean up
  setTimeout(1000).then(() => {
    console.log('✅ Cleanup completed');
    process.exit(0);
  });
}

// Handle termination signals
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('exit', cleanup);

async function checkServerHealth(url, maxAttempts = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      console.log(`🔍 Attempt ${i + 1}/${maxAttempts}: Checking ${url}...`);
      const response = await axios.get(url, { timeout: 2000 });
      if (response.status === 200) {
        console.log('✅ Next.js server is ready!');
        return true;
      }
    } catch (error) {
      // Server not ready yet, wait and retry
      console.log(`⏳ Server not ready, waiting 2 seconds...`);
      await setTimeout(2000);
    }
  }
  
  console.error('❌ Next.js server failed to start after maximum attempts');
  return false;
}

async function killExistingElectronProcesses() {
  console.log('🔄 Checking for existing Electron processes...');
  
  try {
    // Kill any existing Electron processes for this project
    const { execSync } = require('child_process');
    execSync('pkill -f "Electron.*bistro/electron" || true', { stdio: 'ignore' });
    execSync('pkill -f "couchdb.*bistro/electron" || true', { stdio: 'ignore' });
    
    // Remove any stale lock files
    execSync('find electron/pouchdb-data -name "LOCK" -delete || true', { stdio: 'ignore' });
    
    console.log('✅ Cleaned up existing processes and lock files');
    await setTimeout(1000); // Give time for cleanup
  } catch (error) {
    console.log('⚠️ Note: No existing processes to clean up');
  }
}

async function startElectronDev() {
  console.log('🚀 Starting Electron Development Environment...\n');

  // Step 0: Clean up any existing processes
  await killExistingElectronProcesses();

  // Step 1: Start Next.js server
  console.log('📦 Starting Next.js development server...');
  nextProcess = spawn('npm', ['run', 'dev'], {
    stdio: ['ignore', 'pipe', 'pipe'],
    cwd: process.cwd()
  });

  nextProcess.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(`[Next.js] ${output.trim()}`);
  });

  nextProcess.stderr.on('data', (data) => {
    const output = data.toString();
    console.error(`[Next.js ERROR] ${output.trim()}`);
  });

  nextProcess.on('exit', (code) => {
    console.log(`Next.js server exited with code ${code}`);
    if (code !== 0) {
      console.error('❌ Next.js server failed to start');
      cleanup();
    }
  });

  // Step 2: Wait for server to be healthy
  const serverUrl = 'http://localhost:3000';
  const isHealthy = await checkServerHealth(serverUrl);
  
  if (!isHealthy) {
    console.error('❌ Could not connect to Next.js server');
    cleanup();
    return;
  }

  // Step 3: Build Electron in watch mode
  console.log('\n🖥️  Starting Electron TypeScript watch mode...');
  electronWatchProcess = spawn('npm', ['run', 'watch'], {
    stdio: ['ignore', 'pipe', 'pipe'],
    cwd: './electron'
  });

  electronWatchProcess.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(`[Electron Build] ${output.trim()}`);
  });

  electronWatchProcess.stderr.on('data', (data) => {
    const output = data.toString();
    console.error(`[Electron Build ERROR] ${output.trim()}`);
  });

  electronWatchProcess.on('exit', (code) => {
    console.log(`Electron watch process exited with code ${code}`);
    if (code !== 0 && electronWatchProcess) {
      console.error('❌ Electron build failed');
      cleanup();
    }
  });

  // Step 4: Wait for initial build to complete
  console.log('⏳ Waiting for initial Electron build...');
  await setTimeout(5000);

  // Step 5: Start Electron app
  console.log('🚀 Launching Electron app...');
  electronApp = spawn('npm', ['run', 'electron:start-live'], {
    stdio: 'inherit',
    cwd: './electron',
    env: { 
      ...process.env, 
      NODE_ENV: 'development',
      ELECTRON_START_URL: serverUrl 
    }
  });

  electronApp.on('exit', (code) => {
    console.log(`\n📱 Electron app exited with code ${code}`);
    cleanup();
  });

  electronApp.on('error', (error) => {
    console.error('❌ Failed to start Electron app:', error);
    cleanup();
  });

  console.log('\n✅ Electron development environment is running!');
  console.log('   • Next.js server: http://localhost:3000');
  console.log('   • Electron app: Connected to development server');
  console.log('   • TypeScript: Watching for changes');
  console.log('   • Press Ctrl+C to stop all processes\n');
}

// Check if axios is available
try {
  require.resolve('axios');
} catch (error) {
  console.error('❌ axios is required but not installed.');
  console.error('   Run: npm install --save-dev axios');
  process.exit(1);
}

startElectronDev().catch((error) => {
  console.error('❌ Failed to start Electron dev environment:', error);
  cleanup();
});