#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Find all API route files
function findApiRoutes(dir) {
  const apiRoutes = [];
  
  function scan(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        scan(itemPath);
      } else if (item === 'route.ts' || item === 'route.js') {
        apiRoutes.push(itemPath);
      }
    }
  }
  
  scan(dir);
  return apiRoutes;
}

// Add static export configuration to a route file
function addStaticExportConfig(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check if already has static export config
  if (content.includes('export const dynamic') || content.includes('export const revalidate')) {
    console.log(`  ✓ ${path.relative(process.cwd(), filePath)} - already configured`);
    return;
  }
  
  // Find the first import statement
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // Find the last import or the first non-import line
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('import ') || lines[i].startsWith('import{')) {
      insertIndex = i + 1;
    } else if (lines[i].trim() === '' && insertIndex > 0) {
      // Skip empty lines after imports
      continue;
    } else if (insertIndex > 0) {
      // Found first non-import line
      break;
    }
  }
  
  // Insert the static export configuration
  const staticConfig = [
    '',
    '// Static export configuration',
    'export const dynamic = \'force-static\';',
    'export const revalidate = false;',
    ''
  ];
  
  lines.splice(insertIndex, 0, ...staticConfig);
  
  const newContent = lines.join('\n');
  fs.writeFileSync(filePath, newContent, 'utf8');
  
  console.log(`  ✓ ${path.relative(process.cwd(), filePath)} - added static export config`);
}

// Main execution
console.log('🔧 Adding static export configuration to API routes...');

const apiDir = path.join(__dirname, '../app/api');

if (!fs.existsSync(apiDir)) {
  console.log('❌ API directory not found');
  process.exit(1);
}

const apiRoutes = findApiRoutes(apiDir);

if (apiRoutes.length === 0) {
  console.log('✅ No API routes found');
  process.exit(0);
}

console.log(`📁 Found ${apiRoutes.length} API routes:`);

for (const route of apiRoutes) {
  addStaticExportConfig(route);
}

console.log(`\n✅ Updated ${apiRoutes.length} API routes for static export`);
console.log('\n💡 Note: These API routes will return static responses in static builds.');
console.log('💡 For full functionality, use server-side deployment instead of static export.');