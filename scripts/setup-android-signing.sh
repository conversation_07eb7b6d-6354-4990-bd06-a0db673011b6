#!/bin/bash

# 🔐 Android APK Signing Setup Script
# This script generates a keystore for signing Android APKs and creates the keystore.properties file

set -e

echo "🔐 Setting up Android APK signing..."

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "⚠️  Java not found in PATH. Trying Android Studio's bundled Java..."
    export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
    export PATH="$JAVA_HOME/bin:$PATH"
    
    if ! command -v java &> /dev/null; then
        echo "❌ Java is required but not found. Please install Java or Android Studio."
        exit 1
    fi
fi

echo "✅ Java found: $(java -version 2>&1 | head -n 1)"

# Configuration
KEYSTORE_FILE="android/bistro-release-key.keystore"
KEY_ALIAS="bistro-key"
KEYSTORE_PROPERTIES="android/keystore.properties"

# Check if keystore already exists
if [ -f "$KEYSTORE_FILE" ]; then
    echo "⚠️  Keystore already exists at $KEYSTORE_FILE"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Aborted. Using existing keystore."
        exit 0
    fi
    rm -f "$KEYSTORE_FILE"
fi

# Generate keystore
echo "🔑 Generating new keystore..."
echo "📝 You will be prompted for keystore and key passwords, and certificate information."
echo "⚠️  IMPORTANT: Remember these passwords! You'll need them for future updates."
echo

keytool -genkey -v -keystore "$KEYSTORE_FILE" -alias "$KEY_ALIAS" -keyalg RSA -keysize 2048 -validity 10000

if [ ! -f "$KEYSTORE_FILE" ]; then
    echo "❌ Failed to generate keystore"
    exit 1
fi

echo "✅ Keystore generated successfully!"

# Get passwords for keystore.properties
echo
echo "🔧 Setting up keystore.properties file..."
read -s -p "Enter keystore password: " STORE_PASSWORD
echo
read -s -p "Enter key password: " KEY_PASSWORD
echo

# Create keystore.properties file
cat > "$KEYSTORE_PROPERTIES" << EOF
# 🔐 Android Keystore Configuration
# This file contains sensitive information and should NOT be committed to version control
# Add keystore.properties to your .gitignore file

storeFile=bistro-release-key.keystore
storePassword=$STORE_PASSWORD
keyAlias=$KEY_ALIAS
keyPassword=$KEY_PASSWORD
EOF

echo "✅ keystore.properties created!"

# Update .gitignore
GITIGNORE_FILE="android/.gitignore"
if [ -f "$GITIGNORE_FILE" ]; then
    if ! grep -q "keystore.properties" "$GITIGNORE_FILE"; then
        echo "" >> "$GITIGNORE_FILE"
        echo "# Keystore configuration (contains sensitive data)" >> "$GITIGNORE_FILE"
        echo "keystore.properties" >> "$GITIGNORE_FILE"
        echo "*.keystore" >> "$GITIGNORE_FILE"
        echo "✅ Updated android/.gitignore"
    fi
else
    echo "⚠️  android/.gitignore not found. Please manually add keystore.properties to .gitignore"
fi

# Update root .gitignore
ROOT_GITIGNORE=".gitignore"
if [ -f "$ROOT_GITIGNORE" ]; then
    if ! grep -q "android/keystore.properties" "$ROOT_GITIGNORE"; then
        echo "" >> "$ROOT_GITIGNORE"
        echo "# Android keystore configuration" >> "$ROOT_GITIGNORE"
        echo "android/keystore.properties" >> "$ROOT_GITIGNORE"
        echo "android/*.keystore" >> "$ROOT_GITIGNORE"
        echo "✅ Updated root .gitignore"
    fi
fi

echo
echo "🎉 Android signing setup complete!"
echo
echo "📋 Summary:"
echo "  ✅ Keystore: $KEYSTORE_FILE"
echo "  ✅ Configuration: $KEYSTORE_PROPERTIES"
echo "  ✅ Key alias: $KEY_ALIAS"
echo
echo "🔒 Security Notes:"
echo "  • keystore.properties is excluded from git"
echo "  • Back up your keystore file securely"
echo "  • Never lose your keystore - you can't update your app without it!"
echo
echo "🚀 Next steps:"
echo "  1. Test signed build: cd android && ./gradlew assembleRelease"
echo "  2. APK will be at: android/app/build/outputs/apk/release/app-release.apk"
