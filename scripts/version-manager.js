#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Automated Version Management Script
 * Updates version across all platform-specific configs
 */

const PROJECT_ROOT = path.resolve(__dirname, '..');

// Files that need version updates
const VERSION_FILES = [
  'package.json',
  'electron/package.json',
  'android/app/build.gradle',
  'ios/App/App.xcodeproj/project.pbxproj', // iOS project file
];

// Capacitor config files
const CAPACITOR_CONFIG = 'capacitor.config.ts';

function getCurrentVersion() {
  const packagePath = path.join(PROJECT_ROOT, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  return packageJson.version;
}

function updatePackageJson(filePath, newVersion) {
  const fullPath = path.join(PROJECT_ROOT, filePath);
  if (!fs.existsSync(fullPath)) {
    console.warn(`⚠️  File not found: ${filePath}`);
    return false;
  }

  const packageJson = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
  packageJson.version = newVersion;
  fs.writeFileSync(fullPath, JSON.stringify(packageJson, null, 2) + '\n');
  console.log(`✅ Updated ${filePath} to version ${newVersion}`);
  return true;
}

function updateAndroidVersion(newVersion) {
  const buildGradlePath = path.join(PROJECT_ROOT, 'android/app/build.gradle');
  if (!fs.existsSync(buildGradlePath)) {
    console.warn('⚠️  Android build.gradle not found');
    return false;
  }

  let content = fs.readFileSync(buildGradlePath, 'utf8');
  
  // Extract version parts (assuming semantic versioning)
  const versionParts = newVersion.split('.');
  const versionCode = parseInt(versionParts[0]) * 10000 + 
                     parseInt(versionParts[1]) * 100 + 
                     parseInt(versionParts[2] || 0);

  // Update versionName
  content = content.replace(
    /versionName\s+["'][\d.]+["']/,
    `versionName "${newVersion}"`
  );

  // Update versionCode
  content = content.replace(
    /versionCode\s+\d+/,
    `versionCode ${versionCode}`
  );

  fs.writeFileSync(buildGradlePath, content);
  console.log(`✅ Updated Android version to ${newVersion} (code: ${versionCode})`);
  return true;
}

function updateCapacitorConfig(newVersion) {
  const configPath = path.join(PROJECT_ROOT, CAPACITOR_CONFIG);
  if (!fs.existsSync(configPath)) {
    console.warn('⚠️  Capacitor config not found');
    return false;
  }

  let content = fs.readFileSync(configPath, 'utf8');
  
  // Update version in capacitor config (if it exists)
  if (content.includes('version:')) {
    content = content.replace(
      /version:\s*["'][\d.]+["']/,
      `version: "${newVersion}"`
    );
  }

  fs.writeFileSync(configPath, content);
  console.log(`✅ Updated Capacitor config version`);
  return true;
}

function incrementVersion(currentVersion, type = 'patch') {
  const parts = currentVersion.split('.').map(Number);
  const [major, minor, patch] = parts;

  switch (type) {
    case 'major':
      return `${major + 1}.0.0`;
    case 'minor':
      return `${major}.${minor + 1}.0`;
    case 'patch':
    default:
      return `${major}.${minor}.${patch + 1}`;
  }
}

function gitCommitVersion(newVersion) {
  try {
    execSync(`git add -A`, { cwd: PROJECT_ROOT });
    execSync(`git commit -m "chore: bump version to ${newVersion}"`, { cwd: PROJECT_ROOT });
    execSync(`git tag -a v${newVersion} -m "Version ${newVersion}"`, { cwd: PROJECT_ROOT });
    console.log(`✅ Created git commit and tag for version ${newVersion}`);
    return true;
  } catch (error) {
    console.warn('⚠️  Git operations failed (this is normal if git is not configured)');
    return false;
  }
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command || command === 'help') {
    console.log(`
📦 Version Manager

Usage:
  node scripts/version-manager.js <command> [options]

Commands:
  current                 Show current version
  patch                   Increment patch version (x.x.X)
  minor                   Increment minor version (x.X.0)
  major                   Increment major version (X.0.0)
  set <version>           Set specific version
  sync                    Sync version across all platform files

Examples:
  npm run version:patch   # 1.0.0 → 1.0.1
  npm run version:minor   # 1.0.1 → 1.1.0
  npm run version:major   # 1.1.0 → 2.0.0
  npm run version:set 2.1.0
    `);
    return;
  }

  const currentVersion = getCurrentVersion();
  console.log(`📦 Current version: ${currentVersion}`);

  let newVersion = currentVersion;

  switch (command) {
    case 'current':
      return;

    case 'patch':
    case 'minor':
    case 'major':
      newVersion = incrementVersion(currentVersion, command);
      break;

    case 'set':
      newVersion = args[1];
      if (!newVersion) {
        console.error('❌ Please provide a version number');
        process.exit(1);
      }
      break;

    case 'sync':
      // Just sync existing version across files
      break;

    default:
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
  }

  console.log(`🎯 Target version: ${newVersion}`);
  console.log('');

  // Update all version files
  let success = true;
  
  // Update package.json files
  success &= updatePackageJson('package.json', newVersion);
  success &= updatePackageJson('electron/package.json', newVersion);

  // Update platform-specific files
  success &= updateAndroidVersion(newVersion);
  success &= updateCapacitorConfig(newVersion);

  if (success && command !== 'sync') {
    // Create git commit and tag (optional)
    gitCommitVersion(newVersion);
  }

  console.log('');
  console.log(success ? '🎉 Version update completed!' : '⚠️  Some updates failed');
  console.log(`📦 New version: ${newVersion}`);

  // Show next steps
  console.log('');
  console.log('📋 Next steps:');
  console.log('  • Test builds: npm run build:electron && npm run build:mobile');
  console.log('  • Deploy desktop: npm run deploy:windows && npm run deploy:macos');
  console.log('  • Deploy mobile: npm run deploy:android && npm run deploy:ios');
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  getCurrentVersion,
  incrementVersion,
  updatePackageJson,
  updateAndroidVersion,
  updateCapacitorConfig,
};