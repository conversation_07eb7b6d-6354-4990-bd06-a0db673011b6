#!/usr/bin/env node

// Automate Capacitor production mode for Android: static build with remote API capability
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function main() {
  console.log('🚀 Building mobile app for production/offline mode...');
  
  // Set production environment variables
  const env = {
    ...process.env,
    NODE_ENV: 'production',
    BUILD_TARGET: 'static',
    // Set your remote server URL here - replace with your actual hosted app URL
    NEXT_PUBLIC_REMOTE_SERVER_URL: process.env.NEXT_PUBLIC_REMOTE_SERVER_URL || 'https://bistro.icu'
  };
  
  console.log(`🌐 Remote server URL: ${env.NEXT_PUBLIC_REMOTE_SERVER_URL}`);
  console.log('📦 Building static export for mobile...');
  
  // Use our unified build system for mobile production
  execSync('npm run build:mobile', { 
    stdio: 'inherit',
    env
  });
  
  // Sync Capacitor project with the static build
  console.log('📱 Syncing Capacitor project...');
  execSync('npx cap sync android', { 
    stdio: 'inherit',
    env: {
      ...env,
      CAP_MODE: 'prod'
    }
  });
  
  // Open Android Studio
  console.log('🔧 Opening Android Studio...');
  execSync('npx cap open android', { stdio: 'inherit' });
  
  console.log('✅ Production build ready!');
  console.log('📱 Mobile app features:');
  console.log('  • 🌐 Calls remote APIs when online');
  console.log('  • 💾 Falls back to PouchDB when offline');
  console.log('  • 🔄 Syncs with CouchDB when connection restored');
  console.log(`  • 🖥️  Remote server: ${env.NEXT_PUBLIC_REMOTE_SERVER_URL}`);
}

main();