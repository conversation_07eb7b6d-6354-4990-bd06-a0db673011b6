/**
 * Manual RSC Path Fixer
 * 
 * Fixes the RSC payload paths in the HTML file to use relative paths
 */

const fs = require('fs');

console.log('🔧 Fixing RSC payload paths manually...\n');

const htmlPath = 'electron/app/index.html';

if (!fs.existsSync(htmlPath)) {
  console.log('❌ HTML file not found:', htmlPath);
  process.exit(1);
}

let htmlContent = fs.readFileSync(htmlPath, 'utf8');

console.log('📄 Original file size:', (htmlContent.length / 1024).toFixed(2), 'KB');

// Fix all absolute paths in RSC payload to relative paths
let fixCount = 0;

// Fix HL (Head Link) references
htmlContent = htmlContent.replace(/:HL\["\/(_next\/[^"]+)"/g, (match, path) => {
  fixCount++;
  return `:HL["./${path}"`;
});

// Fix href references in RSC payload
htmlContent = htmlContent.replace(/"href":"\/(_next\/[^"]+)"/g, (match, path) => {
  fixCount++;
  return `"href":"./${path}"`;
});

// Fix src references in RSC payload
htmlContent = htmlContent.replace(/"src":"\/(_next\/[^"]+)"/g, (match, path) => {
  fixCount++;
  return `"src":"./${path}"`;
});

// Fix chunk references in RSC payload
htmlContent = htmlContent.replace(/"\\.\/(_next\/static\/chunks\/[^"]+)"/g, (match, path) => {
  fixCount++;
  return `"./${path}"`;
});

// Fix any remaining absolute _next paths
htmlContent = htmlContent.replace(/"\/_next\//g, '"./_next/');

console.log('🔧 Fixed', fixCount, 'path references');

// Write the fixed content back
fs.writeFileSync(htmlPath, htmlContent);

console.log('✅ RSC payload paths fixed successfully');
console.log('📄 Updated file size:', (htmlContent.length / 1024).toFixed(2), 'KB');

// Verify the fixes
const verifyContent = fs.readFileSync(htmlPath, 'utf8');
const remainingAbsolutePaths = (verifyContent.match(/"\/_next\//g) || []).length;
const relativePaths = (verifyContent.match(/"\.\/_next\//g) || []).length;

console.log('\n📊 Verification:');
console.log('✅ Relative paths (./_next/):', relativePaths);
console.log(remainingAbsolutePaths === 0 ? '✅' : '❌', 'Remaining absolute paths (/_next/):', remainingAbsolutePaths);

if (remainingAbsolutePaths === 0) {
  console.log('\n🎉 All RSC payload paths are now relative!');
  console.log('🚀 The app should now load without blank screen issues.');
} else {
  console.log('\n⚠️  Some absolute paths remain. Manual inspection may be needed.');
}

console.log('\n✅ RSC path fixing complete!');