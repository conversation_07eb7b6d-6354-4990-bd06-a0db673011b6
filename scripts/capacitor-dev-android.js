#!/usr/bin/env node

// Automate Capacitor dev mode for Android: set dev environment and start
const fs = require('fs');
const os = require('os');
const path = require('path');
const { execSync } = require('child_process');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  throw new Error('No local network IP found.');
}

function main() {
  const ip = getLocalIP();
  const devServerUrl = `http://${ip}:3000`;
  
  console.log(`🚀 Starting Capacitor development mode...`);
  console.log(`📱 Dev server: ${devServerUrl}`);
  
  // Set environment variables for development
  process.env.NODE_ENV = 'development';
  process.env.CAP_MODE = 'dev';
  process.env.CAPACITOR_DEV_SERVER_URL = devServerUrl;
  
  // Sync with development environment
  try {
    console.log('🔄 Syncing Capacitor project for development...');
    execSync('npx cap sync android', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development',
        CAP_MODE: 'dev',
        CAPACITOR_DEV_SERVER_URL: devServerUrl
      }
    });
  } catch (err) {
    console.warn('\n⚠️  cap sync failed (this might be normal for dev mode):', err.message);
  }
  
  // Open Android Studio
  try {
    console.log('📱 Opening Android Studio...');
    execSync('npx cap open android', { stdio: 'inherit' });
    console.log(`✅ Development mode ready!`);
    console.log(`🔧 Start your Next.js dev server: npm run dev`);
    console.log(`📱 The mobile app will connect to: ${devServerUrl}`);
  } catch (err) {
    console.error('❌ Failed to open Android Studio:', err.message);
  }
}

main(); 