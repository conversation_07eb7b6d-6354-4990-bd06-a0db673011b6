#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing static paths for Electron compatibility...');

const outDir = path.join(__dirname, '..', 'out');

if (!fs.existsSync(outDir)) {
  console.error('❌ Output directory not found');
  process.exit(1);
}

// Fix HTML files - convert absolute paths to relative paths
function fixHtmlFiles(dir) {
  const files = fs.readdirSync(dir);
  let filesFixed = 0;
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      filesFixed += fixHtmlFiles(filePath);
    } else if (file.endsWith('.html')) {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // Fix absolute paths in HTML attributes
      content = content.replace(/href="\/_next\//g, 'href="../_next/');
      content = content.replace(/src="\/_next\//g, 'src="../_next/');
      
      // Fix absolute paths in RSC payload
      content = content.replace(/"\/\_next\/static\//g, '"../_next/static/');
      content = content.replace(/'\/\_next\/static\//g, '"../_next/static/');
      
      // Fix JSON-encoded paths in RSC payloads
      content = content.replace(/\\"\\\/\\\_next\\\//g, '\\"../_next/');
      
      // Fix mixed relative/absolute paths in RSC chunks
      // Convert "static/chunks/" to "../_next/static/chunks/" when not already prefixed
      content = content.replace(/"static\/chunks\//g, '"../_next/static/chunks/');
      content = content.replace(/'static\/chunks\//g, '"../_next/static/chunks/');
      
      // Fix any remaining absolute _next references
      content = content.replace(/"\/_next\//g, '"../_next/');
      content = content.replace(/'\/_next\//g, '"../_next/');
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✓ Fixed paths in ${path.relative(outDir, filePath)}`);
        filesFixed++;
      }
    }
  }
  
  return filesFixed;
}

// Main execution
console.log(`📂 Processing: ${outDir}`);

const filesFixed = fixHtmlFiles(outDir);

if (filesFixed > 0) {
  console.log(`✅ Fixed static paths in ${filesFixed} files`);
  console.log('\n🎯 Path fixing summary:');
  console.log('  • Converted absolute /_next/ paths to relative ../_next/ paths');
  console.log('  • Fixed RSC chunk references for static builds');
  console.log('  • Ensured compatibility with electron-serve');
} else {
  console.log('✅ No files needed path fixing');
}

console.log('\n🚀 Static paths are now compatible with Electron!');