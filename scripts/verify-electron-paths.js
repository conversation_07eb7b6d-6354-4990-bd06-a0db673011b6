#!/usr/bin/env node

/**
 * Verify Electron Static Asset Paths
 * 
 * This script verifies that all asset paths in the Electron HTML files
 * are using relative paths (./_next/...) instead of absolute paths (/_next/...)
 */

const fs = require('fs');
const path = require('path');

const ELECTRON_APP_DIR = path.join(__dirname, '..', 'electron', 'app');

function verifyPathsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for problematic absolute paths
    const absolutePathPatterns = [
      /href="\/_next\//g,
      /src="\/_next\//g,
      /"\/_next\/static\//g,
    ];
    
    let hasAbsolutePaths = false;
    const issues = [];
    
    absolutePathPatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        hasAbsolutePaths = true;
        issues.push(`Pattern ${index + 1}: Found ${matches.length} absolute path(s)`);
      }
    });
    
    if (hasAbsolutePaths) {
      console.log(`❌ ${path.relative(ELECTRON_APP_DIR, filePath)}: Found absolute paths`);
      issues.forEach(issue => console.log(`   ${issue}`));
      return false;
    } else {
      console.log(`✅ ${path.relative(ELECTRON_APP_DIR, filePath)}: All paths are relative`);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error verifying paths in ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir) {
  let totalFiles = 0;
  let validFiles = 0;
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip _next directory to avoid checking source files
        if (item !== '_next') {
          const [subTotal, subValid] = walkDirectory(fullPath);
          totalFiles += subTotal;
          validFiles += subValid;
        }
      } else if (item.endsWith('.html')) {
        totalFiles++;
        if (verifyPathsInFile(fullPath)) {
          validFiles++;
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dir}:`, error.message);
  }
  
  return [totalFiles, validFiles];
}

function main() {
  console.log('🔍 Verifying Electron static asset paths...');
  console.log(`📁 Target directory: ${ELECTRON_APP_DIR}`);
  
  if (!fs.existsSync(ELECTRON_APP_DIR)) {
    console.error(`❌ Electron app directory not found: ${ELECTRON_APP_DIR}`);
    console.error('   Make sure to run the build process first.');
    process.exit(1);
  }
  
  const [totalFiles, validFiles] = walkDirectory(ELECTRON_APP_DIR);
  
  console.log(`\n📊 Verification Results:`);
  console.log(`   Total HTML files: ${totalFiles}`);
  console.log(`   Valid files: ${validFiles}`);
  console.log(`   Invalid files: ${totalFiles - validFiles}`);
  
  if (validFiles === totalFiles) {
    console.log(`\n🎉 All HTML files have correct relative paths!`);
    process.exit(0);
  } else {
    console.log(`\n❌ ${totalFiles - validFiles} files have absolute paths that need fixing.`);
    console.log(`   Run: node scripts/fix-electron-paths.js`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { verifyPathsInFile, walkDirectory };
