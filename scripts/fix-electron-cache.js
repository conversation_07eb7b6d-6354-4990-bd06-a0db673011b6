#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Electron webpack cache issues...');

// Clean Next.js cache
const nextCacheDir = path.join(process.cwd(), '.next');
if (fs.existsSync(nextCacheDir)) {
  console.log('🗑️  Removing .next cache directory...');
  fs.rmSync(nextCacheDir, { recursive: true, force: true });
}

// Clean Electron build cache
const electronDistDir = path.join(process.cwd(), 'electron', 'dist');
if (fs.existsSync(electronDistDir)) {
  console.log('🗑️  Removing electron/dist directory...');
  fs.rmSync(electronDistDir, { recursive: true, force: true });
}

// Clean Electron app cache
const electronAppDir = path.join(process.cwd(), 'electron', 'app');
if (fs.existsSync(electronAppDir)) {
  console.log('🗑️  Removing electron/app directory...');
  fs.rmSync(electronAppDir, { recursive: true, force: true });
}

// Clean out directory
const outDir = path.join(process.cwd(), 'out');
if (fs.existsSync(outDir)) {
  console.log('🗑️  Removing out directory...');
  fs.rmSync(outDir, { recursive: true, force: true });
}

console.log('✅ Cache cleanup complete!');
console.log('');
console.log('Next steps:');
console.log('1. Run: npm run build:electron');
console.log('2. Run: npm run electron:static');
console.log('');