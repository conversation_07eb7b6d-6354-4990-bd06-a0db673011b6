#!/bin/bash

# 🍎 Local macOS Deployment Script
# This script builds the macOS app locally and organizes the output files
# No cloud upload - purely local release generation

set -e  # Exit on any error

echo "🍎 Starting local macOS deployment process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
RELEASE_DIR="electron/release"
LOCAL_RELEASES_DIR="releases/macos"

echo ""
echo -e "${CYAN}${BOLD}🍎 LOCAL MACOS DEPLOYMENT${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Release Directory: ${RELEASE_DIR}"
echo -e "  Local Output: ${LOCAL_RELEASES_DIR}"
echo -e "  Build Type: Local (no cloud upload)"
echo ""

# Step 1: Validate environment
echo -e "${YELLOW}🔍 Validating environment...${NC}"

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ This script must be run on macOS to build macOS apps!${NC}"
    echo -e "  Current OS: $OSTYPE"
    echo -e "  Use a Mac or macOS virtual machine to build macOS releases."
    exit 1
fi

echo -e "${GREEN}  ✅ Running on macOS${NC}"

# Check for required tools
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found!${NC}"
    echo -e "  Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo -e "${GREEN}  ✅ Node.js found: $(node --version)${NC}"
echo ""

# Step 2: Ensure directories exist (no cleaning)
echo -e "${YELLOW}📁 Ensuring necessary directories exist...${NC}"

# Create local releases directory
mkdir -p "$LOCAL_RELEASES_DIR"
echo -e "${GREEN}  ✅ Ensured local releases directory exists${NC}"
echo ""

# Step 3: Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
echo -e "  Installing root dependencies..."

# Use npm ci for faster, reliable installs
echo -e "  ${BLUE}Installing root dependencies (this may take a few minutes)...${NC}"
if [ -f "package-lock.json" ]; then
    timeout 300 npm ci --no-audit --no-fund || {
        echo -e "  ${YELLOW}⚠️  npm ci failed, trying with legacy peer deps...${NC}"
        npm install --legacy-peer-deps --no-audit --no-fund
    }
else
    npm install --legacy-peer-deps --no-audit --no-fund
fi

echo -e "  ${BLUE}Installing Electron dependencies...${NC}"
cd electron

if [ -f "package-lock.json" ]; then
    timeout 300 npm ci --no-audit --no-fund || {
        echo -e "  ${YELLOW}⚠️  npm ci failed, trying with legacy peer deps...${NC}"
        npm install --legacy-peer-deps --no-audit --no-fund
    }
else
    npm install --legacy-peer-deps --no-audit --no-fund
fi

cd ..
echo -e "${GREEN}  ✅ Dependencies installed${NC}"
echo ""

# Step 4: Prepare CouchDB binaries
echo -e "${YELLOW}🗄️ Preparing CouchDB binaries...${NC}"

# Check if CouchDB binaries exist
COUCHDB_DIR="electron/resources/couchdb-macos"
if [ ! -d "$COUCHDB_DIR" ] || [ ! -f "$COUCHDB_DIR/bin/couchdb" ]; then
    echo -e "  ${BLUE}CouchDB binaries not found, downloading...${NC}"
    
    # Create resources directory
    mkdir -p electron/resources
    
    # Download and extract CouchDB for macOS
    TEMP_DIR=$(mktemp -d)
    echo -e "  ${BLUE}Downloading CouchDB 3.3.3 for macOS...${NC}"
    curl -L "https://archive.apache.org/dist/couchdb/binary/mac/3.3.3/Apache-CouchDB-3.3.3.zip" -o "$TEMP_DIR/couchdb.zip"
    
    echo -e "  ${BLUE}Extracting CouchDB...${NC}"
    cd "$TEMP_DIR"
    unzip -q couchdb.zip
    
    # Find extracted directory
    EXTRACTED_DIR=$(find . -name "Apache-CouchDB*" -type d | head -1)
    if [ -n "$EXTRACTED_DIR" ]; then
        cd "$OLDPWD"
        mv "$TEMP_DIR/$EXTRACTED_DIR" "$COUCHDB_DIR"
        chmod -R 755 "$COUCHDB_DIR"
        echo -e "${GREEN}  ✅ CouchDB binaries prepared${NC}"
    else
        echo -e "${RED}❌ Failed to extract CouchDB${NC}"
        exit 1
    fi
    
    # Cleanup
    rm -rf "$TEMP_DIR"
else
    echo -e "${GREEN}  ✅ CouchDB binaries already available${NC}"
fi
echo ""

# Step 5: Build the app
echo -e "${YELLOW}🏗️ Building Electron app for macOS...${NC}"
echo -e "  Building production-ready macOS installer..."
echo -e "  ${BLUE}Note: This will create both DMG and ZIP files${NC}"
echo ""

cd electron
npm run electron:build:mac
cd ..

echo ""
echo -e "${GREEN}  ✅ macOS build completed${NC}"
echo ""

# Step 6: Find and organize the built files
echo -e "${YELLOW}🔍 Locating and organizing built files...${NC}"

# Find DMG file
DMG_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.dmg" | head -1)
# Find ZIP file
ZIP_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.zip" | head -1)
# Find latest.yml
YML_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "latest*.yml" | head -1)

if [ -z "$DMG_FILE" ] && [ -z "$ZIP_FILE" ]; then
    echo -e "${RED}❌ No DMG or ZIP files found in release directory!${NC}"
    echo -e "  Expected location: ${RELEASE_DIR}"
    echo -e "  Available files:"
    ls -la "$RELEASE_DIR" 2>/dev/null || echo "    (directory not found)"
    exit 1
fi

# Copy files to local releases directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_SUBDIR="${LOCAL_RELEASES_DIR}/${TIMESTAMP}"
mkdir -p "$RELEASE_SUBDIR"

echo -e "${GREEN}  ✅ Created release subdirectory: ${RELEASE_SUBDIR}${NC}"

# Copy DMG file
if [ -n "$DMG_FILE" ]; then
    cp "$DMG_FILE" "$RELEASE_SUBDIR/"
    DMG_SIZE=$(stat -f%z "$DMG_FILE" 2>/dev/null || echo "unknown")
    if [ "$DMG_SIZE" != "unknown" ]; then
        SIZE_MB=$((DMG_SIZE / 1048576))
        echo -e "${GREEN}  ✅ Copied DMG: $(basename "$DMG_FILE") (${SIZE_MB} MB)${NC}"
    else
        echo -e "${GREEN}  ✅ Copied DMG: $(basename "$DMG_FILE")${NC}"
    fi
fi

# Copy ZIP file
if [ -n "$ZIP_FILE" ]; then
    cp "$ZIP_FILE" "$RELEASE_SUBDIR/"
    ZIP_SIZE=$(stat -f%z "$ZIP_FILE" 2>/dev/null || echo "unknown")
    if [ "$ZIP_SIZE" != "unknown" ]; then
        SIZE_MB=$((ZIP_SIZE / 1048576))
        echo -e "${GREEN}  ✅ Copied ZIP: $(basename "$ZIP_FILE") (${SIZE_MB} MB)${NC}"
    else
        echo -e "${GREEN}  ✅ Copied ZIP: $(basename "$ZIP_FILE")${NC}"
    fi
fi

# Copy YML file (for auto-updater)
if [ -n "$YML_FILE" ]; then
    cp "$YML_FILE" "$RELEASE_SUBDIR/"
    echo -e "${GREEN}  ✅ Copied update metadata: $(basename "$YML_FILE")${NC}"
fi

# Create a "latest" symlink
cd "$LOCAL_RELEASES_DIR"
rm -f latest
ln -s "$TIMESTAMP" latest
cd - > /dev/null

echo -e "${GREEN}  ✅ Created 'latest' symlink${NC}"
echo ""

# Step 7: Create release info file
echo -e "${YELLOW}📝 Creating release information...${NC}"

RELEASE_INFO_FILE="${RELEASE_SUBDIR}/release-info.txt"
cat > "$RELEASE_INFO_FILE" << EOF
🍎 Shop macOS Release Information
================================

Build Date: $(date)
Build Host: $(hostname)
macOS Version: $(sw_vers -productVersion)
Node.js Version: $(node --version)
Electron Version: $(cd electron && npm list electron --depth=0 2>/dev/null | grep electron || echo "Unknown")

Files in this release:
EOF

# List all files with sizes
for file in "$RELEASE_SUBDIR"/*; do
    if [ -f "$file" ] && [ "$(basename "$file")" != "release-info.txt" ]; then
        filename=$(basename "$file")
        filesize=$(stat -f%z "$file" 2>/dev/null || echo "unknown")
        if [ "$filesize" != "unknown" ]; then
            size_mb=$((filesize / 1048576))
            echo "  - $filename (${size_mb} MB)" >> "$RELEASE_INFO_FILE"
        else
            echo "  - $filename" >> "$RELEASE_INFO_FILE"
        fi
    fi
done

cat >> "$RELEASE_INFO_FILE" << EOF

Installation Instructions:
- DMG: Double-click to mount, then drag Bistro.app to Applications folder
- ZIP: Extract and move Bistro.app to Applications folder

Auto-Update: 
- The latest.yml file contains update metadata for the built-in updater
- Place this file on your update server if using auto-updates

EOF

echo -e "${GREEN}  ✅ Created release information file${NC}"
echo ""

# Final success message
echo -e "${GREEN}${BOLD}🎉 LOCAL MACOS DEPLOYMENT COMPLETED!${NC}"
echo ""
echo -e "${CYAN}📋 Deployment Summary:${NC}"
if [ -n "$DMG_FILE" ]; then
    echo -e "  ✅ Built macOS DMG installer"
fi
if [ -n "$ZIP_FILE" ]; then
    echo -e "  ✅ Built macOS ZIP archive"
fi
echo -e "  ✅ Organized files in local releases directory"
echo -e "  ✅ Created release information file"
echo -e "  ✅ Set up 'latest' symlink for easy access"
echo ""
echo -e "${YELLOW}📁 Release Location:${NC}"
echo -e "  Full Path: ${PWD}/${RELEASE_SUBDIR}"
echo -e "  Latest Link: ${PWD}/${LOCAL_RELEASES_DIR}/latest"
echo ""
echo -e "${YELLOW}🔗 What's Next:${NC}"
echo -e "  1. 📁 Navigate to: ${BOLD}${LOCAL_RELEASES_DIR}/latest${NC}"
echo -e "  2. 🍎 Test the DMG installer on a clean Mac"
echo -e "  3. 📤 Distribute the DMG file to your users"
echo -e "  4. 🔄 Use the ZIP for programmatic distribution"
echo ""
echo -e "${GREEN}${BOLD}✅ Your macOS app is ready for local distribution!${NC}"
echo ""