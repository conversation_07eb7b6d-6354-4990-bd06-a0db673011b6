#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Remove problematic theme scripts from all HTML files
 */
function removeThemeScripts() {
  const electronAppDir = path.join(__dirname, '..', 'electron', 'app');
  
  if (!fs.existsSync(electronAppDir)) {
    console.log('❌ Electron app directory not found:', electronAppDir);
    return;
  }

  console.log('🧹 Removing theme scripts from HTML files...');
  
  // Find all HTML files recursively
  function findHtmlFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...findHtmlFiles(fullPath));
      } else if (item.endsWith('.html')) {
        files.push(fullPath);
      }
    }
    
    return files;
  }
  
  const htmlFiles = findHtmlFiles(electronAppDir);
  console.log(`📄 Found ${htmlFiles.length} HTML files`);
  
  let processedCount = 0;
  let modifiedCount = 0;
  
  for (const filePath of htmlFiles) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // Remove the problematic theme script
      const themeScriptPattern = /<script>\(\(e,t,r,n,o,a,i,s\)=>\{[^}]+\}\)\("class","theme","light",null,\["light","dark"\],null,true,true\)<\/script>/g;
      content = content.replace(themeScriptPattern, '');
      
      // Also remove any other theme-related scripts that might cause forEach errors
      const forEachErrorPattern = /<script>[^<]*forEach[^<]*<\/script>/g;
      content = content.replace(forEachErrorPattern, '');
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ Cleaned: ${path.relative(electronAppDir, filePath)}`);
        modifiedCount++;
      }
      
      processedCount++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   Processed: ${processedCount} files`);
  console.log(`   Modified:  ${modifiedCount} files`);
  console.log(`✅ Theme script cleanup completed!`);
}

// Run the cleanup
removeThemeScripts();
