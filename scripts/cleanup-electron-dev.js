#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Cleaning up Electron development environment...');

try {
  // Kill Electron processes
  console.log('🔄 Stopping Electron processes...');
  try {
    execSync('pkill -f "Electron.*bistro/electron"', { stdio: 'ignore' });
    console.log('✅ Stopped Electron processes');
  } catch (error) {
    console.log('ℹ️ No Electron processes found');
  }

  // Kill CouchDB processes
  console.log('🔄 Stopping CouchDB processes...');
  try {
    execSync('pkill -f "couchdb.*bistro/electron"', { stdio: 'ignore' });
    execSync('pkill -f "beam.smp.*bistro/electron"', { stdio: 'ignore' });
    console.log('✅ Stopped CouchDB processes');
  } catch (error) {
    console.log('ℹ️ No CouchDB processes found');
  }

  // Remove lock files
  console.log('🔄 Removing database lock files...');
  const dbPath = path.join(__dirname, '..', 'electron', 'pouchdb-data');
  
  if (fs.existsSync(dbPath)) {
    try {
      execSync(`find "${dbPath}" -name "LOCK" -delete`, { stdio: 'ignore' });
      execSync(`find "${dbPath}" -name "LOG" -delete`, { stdio: 'ignore' });
      execSync(`find "${dbPath}" -name "LOG.old*" -delete`, { stdio: 'ignore' });
      console.log('✅ Removed database lock files');
    } catch (error) {
      console.log('ℹ️ No lock files found to remove');
    }
  } else {
    console.log('ℹ️ Database directory not found');
  }

  // Remove any stale PID files
  console.log('🔄 Removing CouchDB PID files...');
  try {
    const configPath = path.join(dbPath, 'couchdb-config');
    if (fs.existsSync(configPath)) {
      execSync(`find "${configPath}" -name "*.pid" -delete`, { stdio: 'ignore' });
      execSync(`find "${configPath}" -name "couch.uri" -delete`, { stdio: 'ignore' });
      console.log('✅ Removed CouchDB PID files');
    }
  } catch (error) {
    console.log('ℹ️ No PID files found');
  }

  console.log('\n✅ Cleanup completed successfully!');
  console.log('   You can now safely start the Electron dev environment.');

} catch (error) {
  console.error('❌ Error during cleanup:', error.message);
  process.exit(1);
}