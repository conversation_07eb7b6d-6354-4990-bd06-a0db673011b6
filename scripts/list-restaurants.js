// <PERSON>ript to list all PouchDB databases on the system
const fs = require('fs');
const path = require('path');
const os = require('os');

// Helper function to get PouchDB data directory
function getPouchDBDataDir() {
  // On MacOS, PouchDB stores data in the user's Library folder
  if (process.platform === 'darwin') {
    return path.join(os.homedir(), 'Library', 'Application Support', 'PouchDB');
  }
  
  // On Windows, it's in AppData/Local
  if (process.platform === 'win32') {
    return path.join(os.homedir(), 'AppData', 'Local', 'PouchDB');
  }
  
  // On Linux, it might be in .local/share
  return path.join(os.homedir(), '.local', 'share', 'PouchDB');
}

async function listDatabases() {
  const dataDir = getPouchDBDataDir();
  console.log(`Looking for PouchDB databases in: ${dataDir}`);
  
  try {
    // Check if the directory exists
    if (!fs.existsSync(dataDir)) {
      console.log('PouchDB data directory not found');
      
      // Try looking in the current directory
      const currentDir = process.cwd();
      console.log(`Looking for databases in current directory: ${currentDir}`);
      
      const files = fs.readdirSync(currentDir);
      const databases = files.filter(file => 
        file.startsWith('restaurant-') && 
        fs.statSync(path.join(currentDir, file)).isDirectory()
      );
      
      if (databases.length === 0) {
        console.log('No restaurant databases found in current directory');
      } else {
        console.log('\nFound restaurant databases:');
        databases.forEach(db => {
          console.log(`- ${db}`);
        });
        
        console.log('\nTo fix a specific database, run:');
        console.log(`node scripts/fix-categories.js [restaurant-id]`);
        console.log('\nWhere [restaurant-id] is the part after "restaurant-" in the database name');
      }
      return;
    }
    
    // Read the directory
    const files = fs.readdirSync(dataDir);
    
    // Filter for restaurant databases
    const restaurantDbs = files.filter(file => 
      file.startsWith('restaurant-') && 
      fs.statSync(path.join(dataDir, file)).isDirectory()
    );
    
    if (restaurantDbs.length === 0) {
      console.log('No restaurant databases found');
    } else {
      console.log('\nFound restaurant databases:');
      restaurantDbs.forEach(db => {
        const restaurantId = db.replace('restaurant-', '');
        console.log(`- ${db} (ID: ${restaurantId})`);
      });
      
      console.log('\nTo fix a specific database, run:');
      console.log(`node scripts/fix-categories.js [restaurant-id]`);
      console.log('\nWhere [restaurant-id] is the ID shown above');
    }
  } catch (error) {
    console.error('Error listing databases:', error);
  }
}

// Run the script
listDatabases().catch(err => {
  console.error('Script failed:', err);
  process.exit(1);
}); 