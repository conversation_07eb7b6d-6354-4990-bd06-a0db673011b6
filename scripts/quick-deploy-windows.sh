#!/bin/bash

# 🚀 Quick Windows Release & Upload Script
# Skips dependency installation, just builds and uploads

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo ""
echo -e "${CYAN}${BOLD}🚀 QUICK WINDOWS DEPLOYMENT${NC}"
echo ""

# Step 1: Validate environment
echo -e "${YELLOW}🔍 Validating environment...${NC}"
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found!${NC}"
    exit 1
fi

if [ ! -f "scripts/upload-to-r2.js" ]; then
    echo -e "${RED}❌ Upload script not found!${NC}"
    exit 1
fi
echo -e "${GREEN}  ✅ Environment validated${NC}"
echo ""

# Step 2: Build the app
echo -e "${YELLOW}🏗️ Building Windows app...${NC}"
echo -e "  ${BLUE}Note: USB detection warnings are normal${NC}"
echo ""

cd electron
npm run electron:build:win
cd ..

echo ""
echo -e "${GREEN}  ✅ Build completed${NC}"
echo ""

# Step 3: Find and upload the .exe file
echo -e "${YELLOW}🔍 Locating executable...${NC}"
EXE_FILE=$(find "electron/release" -maxdepth 1 -name "*.exe" | head -1)

if [ -z "$EXE_FILE" ]; then
    echo -e "${RED}❌ No .exe file found!${NC}"
    exit 1
fi

echo -e "${GREEN}  ✅ Found: $(basename "$EXE_FILE")${NC}"

# Get file size
FILE_SIZE=$(stat -f%z "$EXE_FILE" 2>/dev/null || stat -c%s "$EXE_FILE" 2>/dev/null || echo "unknown")
if [ "$FILE_SIZE" != "unknown" ]; then
    SIZE_MB=$((FILE_SIZE / 1048576))
    echo -e "  📊 Size: ${SIZE_MB} MB"
fi
echo ""

# Step 4: Upload to R2 (both versioned and latest)
echo -e "${YELLOW}📤 Uploading to R2 (dual upload)...${NC}"
echo ""

if node scripts/upload-both-to-r2.js "$EXE_FILE"; then
    echo ""
    echo -e "${GREEN}${BOLD}🎉 SUCCESS! Windows app deployed!${NC}"
    echo ""
    echo -e "${CYAN}Next steps:${NC}"
    echo -e "  1. 🌐 Start dev server: ${BOLD}npm run dev${NC}"
    echo -e "  2. 🖱️  Test download on landing page"
    echo ""
else
    echo ""
    echo -e "${RED}❌ Upload failed!${NC}"
    exit 1
fi