#!/bin/bash

# <PERSON>ript to safely build Next.js application without sourcemaps
# This addresses the Turbopack sourcemap issue

echo "🔒 Starting secure build process..."

# Clean the .next directory to ensure no cached sourcemaps remain
echo "🧹 Cleaning .next directory..."
rm -rf .next

# Set environment variables to disable sourcemaps
export GENERATE_SOURCEMAP=false
export NEXT_DISABLE_SOURCEMAPS=true
export NODE_ENV=production

# Run the build with Turbopack
echo "🏗️ Building with sourcemaps disabled..."
npx next build --turbopack

# Verify no sourcemap files exist
echo "🔍 Verifying no sourcemap files exist..."
MAPS=$(find .next -name "*.map" | wc -l)

if [ "$MAPS" -gt 0 ]; then
  echo "⚠️ Warning: $MAPS sourcemap files found. Removing them..."
  find .next -name "*.map" -delete
else
  echo "✅ No sourcemap files found. Build is secure!"
fi

echo "🚀 Build completed safely!" 