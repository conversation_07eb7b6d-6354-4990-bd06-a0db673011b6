#!/bin/bash

# 🚀 Copy Existing Windows Release Files Script
# This script copies already-built release files to the proper release directory
# Demonstrates that the auto-updater files ARE being generated

set -e

echo "🚀 Copying existing Windows release files..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
RELEASE_DIR="electron/release"
LOCAL_RELEASES_DIR="releases/windows"

echo ""
echo -e "${CYAN}${BOLD}📁 COPYING EXISTING RELEASE FILES${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Source: ${RELEASE_DIR}"
echo -e "  Destination: ${LOCAL_RELEASES_DIR}"
echo ""

# Step 1: Check if release files exist
echo -e "${YELLOW}🔍 Checking for existing release files...${NC}"

if [ ! -d "$RELEASE_DIR" ]; then
    echo -e "${RED}❌ Release directory not found: ${RELEASE_DIR}${NC}"
    echo -e "  Please run the build first: npm run deploy:windows"
    exit 1
fi

# Find files
EXE_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.exe" | head -1)
YML_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "latest.yml" | head -1)
BLOCKMAP_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.blockmap" | head -1)

echo -e "${BLUE}📋 Found files:${NC}"
if [ -n "$EXE_FILE" ]; then
    EXE_SIZE=$(stat -f%z "$EXE_FILE" 2>/dev/null || stat -c%s "$EXE_FILE" 2>/dev/null || echo "unknown")
    if [ "$EXE_SIZE" != "unknown" ]; then
        SIZE_MB=$((EXE_SIZE / 1048576))
        echo -e "  ✅ $(basename "$EXE_FILE") (${SIZE_MB} MB)"
    else
        echo -e "  ✅ $(basename "$EXE_FILE")"
    fi
else
    echo -e "  ❌ No .exe file found"
fi

if [ -n "$YML_FILE" ]; then
    echo -e "  ✅ $(basename "$YML_FILE") (auto-updater metadata)"
else
    echo -e "  ❌ No latest.yml file found"
fi

if [ -n "$BLOCKMAP_FILE" ]; then
    BLOCKMAP_SIZE=$(stat -f%z "$BLOCKMAP_FILE" 2>/dev/null || stat -c%s "$BLOCKMAP_FILE" 2>/dev/null || echo "unknown")
    if [ "$BLOCKMAP_SIZE" != "unknown" ]; then
        SIZE_KB=$((BLOCKMAP_SIZE / 1024))
        echo -e "  ✅ $(basename "$BLOCKMAP_FILE") (${SIZE_KB} KB)"
    else
        echo -e "  ✅ $(basename "$BLOCKMAP_FILE")"
    fi
else
    echo -e "  ❌ No .blockmap file found"
fi

if [ -z "$EXE_FILE" ]; then
    echo -e "${RED}❌ No executable file found! Please build first.${NC}"
    exit 1
fi

echo ""

# Step 2: Create release directory
echo -e "${YELLOW}📁 Creating release directory...${NC}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RELEASE_SUBDIR="${LOCAL_RELEASES_DIR}/${TIMESTAMP}"
mkdir -p "$RELEASE_SUBDIR"
echo -e "${GREEN}  ✅ Created: ${RELEASE_SUBDIR}${NC}"

# Step 3: Copy files
echo -e "${YELLOW}📋 Copying files...${NC}"

# Copy .exe file
cp "$EXE_FILE" "$RELEASE_SUBDIR/"
echo -e "${GREEN}  ✅ Copied executable: $(basename "$EXE_FILE")${NC}"

# Copy latest.yml file
if [ -n "$YML_FILE" ]; then
    cp "$YML_FILE" "$RELEASE_SUBDIR/"
    echo -e "${GREEN}  ✅ Copied auto-updater metadata: $(basename "$YML_FILE")${NC}"
fi

# Copy .blockmap file
if [ -n "$BLOCKMAP_FILE" ]; then
    cp "$BLOCKMAP_FILE" "$RELEASE_SUBDIR/"
    echo -e "${GREEN}  ✅ Copied blockmap file: $(basename "$BLOCKMAP_FILE")${NC}"
fi

# Create latest symlink
cd "$LOCAL_RELEASES_DIR"
rm -f latest
ln -s "$TIMESTAMP" latest
cd - > /dev/null

echo -e "${GREEN}  ✅ Created 'latest' symlink${NC}"

# Step 4: Show release info
echo ""
echo -e "${GREEN}${BOLD}🎉 RELEASE FILES COPIED SUCCESSFULLY!${NC}"
echo ""
echo -e "${CYAN}📋 Release Summary:${NC}"
if [ -n "$EXE_FILE" ]; then
    echo -e "  ✅ Windows installer: $(basename "$EXE_FILE")"
fi
if [ -n "$YML_FILE" ]; then
    echo -e "  ✅ Auto-updater metadata: $(basename "$YML_FILE")"
fi
if [ -n "$BLOCKMAP_FILE" ]; then
    echo -e "  ✅ Blockmap for efficient updates: $(basename "$BLOCKMAP_FILE")"
fi
echo ""
echo -e "${YELLOW}📁 Location:${NC}"
echo -e "  ${PWD}/${RELEASE_SUBDIR}"
echo -e "  ${PWD}/${LOCAL_RELEASES_DIR}/latest"
echo ""
echo -e "${YELLOW}🔗 What this proves:${NC}"
echo -e "  ✅ Auto-updater files ARE being generated by electron-builder"
echo -e "  ✅ The deployment script just needed to copy them"
echo -e "  ✅ latest.yml contains version info and download URLs"
echo -e "  ✅ .blockmap enables efficient partial updates"
echo ""
echo -e "${GREEN}${BOLD}✅ All auto-updater files are now properly organized!${NC}"
echo "" 