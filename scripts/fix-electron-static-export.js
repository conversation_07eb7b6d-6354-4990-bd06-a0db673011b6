#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Electron static export...');

// Step 1: Clean the out directory
const outDir = path.join(process.cwd(), 'out');
if (fs.existsSync(outDir)) {
  console.log('🧹 Cleaning out directory...');
  fs.rmSync(outDir, { recursive: true, force: true });
}

// Step 2: Clean the electron/app directory
const electronAppDir = path.join(process.cwd(), 'electron', 'app');
if (fs.existsSync(electronAppDir)) {
  console.log('🧹 Cleaning electron/app directory...');
  fs.rmSync(electronAppDir, { recursive: true, force: true });
}

// Step 3: Run the Next.js build
console.log('🏗️ Building Next.js static export...');
const { execSync } = require('child_process');

try {
  // Set environment variables for the build
  process.env.BUILD_TARGET = 'electron';
  process.env.NODE_ENV = 'production';
  
  // Run the build
  execSync('npm run build', { 
    stdio: 'inherit',
    env: { ...process.env }
  });
  
  console.log('✅ Next.js build completed');
  
  // Step 4: Copy the out directory to electron/app
  if (fs.existsSync(outDir)) {
    console.log('📁 Copying build to electron/app...');
    
    // Create electron/app directory
    fs.mkdirSync(electronAppDir, { recursive: true });
    
    // Copy all files from out to electron/app
    const copyRecursive = (src, dest) => {
      const stats = fs.statSync(src);
      if (stats.isDirectory()) {
        fs.mkdirSync(dest, { recursive: true });
        const files = fs.readdirSync(src);
        files.forEach(file => {
          copyRecursive(path.join(src, file), path.join(dest, file));
        });
      } else {
        fs.copyFileSync(src, dest);
      }
    };
    
    const files = fs.readdirSync(outDir);
    files.forEach(file => {
      copyRecursive(path.join(outDir, file), path.join(electronAppDir, file));
    });
    
    console.log('✅ Files copied to electron/app');
    
    // Step 5: Verify the main files exist
    const indexPath = path.join(electronAppDir, 'index.html');
    const nextDir = path.join(electronAppDir, '_next');
    
    if (fs.existsSync(indexPath) && fs.existsSync(nextDir)) {
      console.log('✅ Static export fixed successfully!');
      console.log(`📄 Index file: ${indexPath}`);
      console.log(`📁 Assets directory: ${nextDir}`);
      
      // List some key files to verify
      const chunksDir = path.join(nextDir, 'static', 'chunks');
      if (fs.existsSync(chunksDir)) {
        const chunks = fs.readdirSync(chunksDir).slice(0, 5);
        console.log(`🧩 Sample chunks: ${chunks.join(', ')}`);
      }
    } else {
      console.error('❌ Required files missing after copy');
      process.exit(1);
    }
    
  } else {
    console.error('❌ Build output directory not found');
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}