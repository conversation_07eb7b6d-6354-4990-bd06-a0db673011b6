#!/usr/bin/env node

// Automate Capacitor production mode for iOS: static build with remote API capability
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function main() {
  console.log('🚀 Building iOS app for production/offline mode...');
  
  // Set production environment variables
  const env = {
    ...process.env,
    NODE_ENV: 'production',
    BUILD_TARGET: 'static',
    // Set your remote server URL here - replace with your actual hosted app URL
    NEXT_PUBLIC_REMOTE_SERVER_URL: process.env.NEXT_PUBLIC_REMOTE_SERVER_URL || 'https://bistro.icu'
  };
  
  console.log(`🌐 Remote server URL: ${env.NEXT_PUBLIC_REMOTE_SERVER_URL}`);
  console.log('📦 Building static export for iOS...');
  
  // Use our unified build system for mobile production
  execSync('npm run build:mobile', { 
    stdio: 'inherit',
    env
  });
  
  // Sync Capacitor project with the static build
  console.log('📱 Syncing Capacitor iOS project...');
  execSync('npx cap sync ios', { 
    stdio: 'inherit',
    env: {
      ...env,
      CAP_MODE: 'prod'
    }
  });
  
  // Open Xcode
  console.log('🔧 Opening Xcode...');
  execSync('npx cap open ios', { stdio: 'inherit' });
  
  console.log('✅ iOS Production build ready!');
  console.log('📱 iOS app features:');
  console.log('  • 🌐 Calls remote APIs when online');
  console.log('  • 💾 Falls back to PouchDB when offline');
  console.log('  • 🔄 Syncs with CouchDB when connection restored');
  console.log(`  • 🖥️  Remote server: ${env.NEXT_PUBLIC_REMOTE_SERVER_URL}`);
  console.log('  • 📸 Camera and Photos access for inventory management');
}

main();