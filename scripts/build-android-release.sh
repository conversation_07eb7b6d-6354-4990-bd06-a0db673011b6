#!/bin/bash

# 📱 Android Release Build Script
# Builds a production-ready, signed APK for distribution

set -e

echo "📱 Building Android release APK..."

# Set Java environment for Android builds
if ! command -v java &> /dev/null; then
    echo "🔧 Setting up Java environment..."
    export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
    export PATH="$JAVA_HOME/bin:$PATH"
fi

# Check if keystore is configured
KEYSTORE_PROPERTIES="android/keystore.properties"
if [ ! -f "$KEYSTORE_PROPERTIES" ]; then
    echo "⚠️  No keystore configuration found!"
    echo "📝 Run: ./scripts/setup-android-signing.sh to set up APK signing"
    echo "🔄 Building unsigned APK for now..."
fi

# Step 1: Build static export
echo "🏗️  Step 1: Building static Next.js export..."
npm run build:mobile

# Step 2: Sync with Capacitor
echo "📱 Step 2: Syncing with Capacitor..."
npx cap sync android

# Step 3: Build APK
echo "🔨 Step 3: Building Android APK..."
cd android

# Set environment for Gradle
export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"

# Build the release APK
./gradlew assembleRelease

cd ..

# Step 4: Check results
APK_DIR="android/app/build/outputs/apk/release"
SIGNED_APK="$APK_DIR/app-release.apk"
UNSIGNED_APK="$APK_DIR/app-release-unsigned.apk"

echo
echo "🎉 Build completed!"
echo

if [ -f "$SIGNED_APK" ]; then
    echo "✅ Signed APK created: $SIGNED_APK"
    APK_SIZE=$(du -h "$SIGNED_APK" | cut -f1)
    echo "📦 APK size: $APK_SIZE"
    echo "🔐 Status: SIGNED (ready for distribution)"
    FINAL_APK="$SIGNED_APK"
elif [ -f "$UNSIGNED_APK" ]; then
    echo "⚠️  Unsigned APK created: $UNSIGNED_APK"
    APK_SIZE=$(du -h "$UNSIGNED_APK" | cut -f1)
    echo "📦 APK size: $APK_SIZE"
    echo "🔓 Status: UNSIGNED (for testing only)"
    echo "💡 To create signed APK: ./scripts/setup-android-signing.sh"
    FINAL_APK="$UNSIGNED_APK"
else
    echo "❌ No APK found! Build may have failed."
    exit 1
fi

echo
echo "📋 Build Summary:"
echo "  📱 Platform: Android"
echo "  🏗️  Build type: Release"
echo "  📦 APK: $FINAL_APK"
echo "  📊 Size: $APK_SIZE"

if [ -f "$KEYSTORE_PROPERTIES" ]; then
    echo "  🔐 Signing: Configured"
else
    echo "  🔓 Signing: Not configured"
fi

echo
echo "🚀 Next steps:"
if [ -f "$SIGNED_APK" ]; then
    echo "  • Test APK on device: adb install \"$FINAL_APK\""
    echo "  • Upload to R2 for distribution"
else
    echo "  • Set up signing: ./scripts/setup-android-signing.sh"
    echo "  • Test unsigned APK: adb install \"$FINAL_APK\""
fi
