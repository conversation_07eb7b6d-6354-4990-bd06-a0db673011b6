#!/bin/bash

# 🔍 CouchDB Setup Verification Script
# Checks if CouchDB binaries are properly configured for Electron builds

echo "🔍 Checking CouchDB setup for Electron builds..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

RESOURCES_DIR="electron/resources"

echo ""
echo -e "${BLUE}📋 CouchDB Binary Status:${NC}"
echo ""

# Check macOS binaries
MACOS_DIR="${RESOURCES_DIR}/couchdb-macos"
MACOS_BIN="${MACOS_DIR}/bin/couchdb"

if [ -d "$MACOS_DIR" ] && [ -f "$MACOS_BIN" ]; then
    echo -e "${GREEN}  ✅ macOS: CouchDB binaries found${NC}"
    echo -e "     📁 Location: ${MACOS_DIR}"
    echo -e "     🔧 Binary: ${MACOS_BIN}"
    
    # Check if executable
    if [ -x "$MACOS_BIN" ]; then
        echo -e "     ✅ Executable permissions: OK"
    else
        echo -e "     ${YELLOW}⚠️  Executable permissions: Missing (will be fixed during build)${NC}"
    fi
else
    echo -e "${RED}  ❌ macOS: CouchDB binaries missing${NC}"
    echo -e "     📁 Expected: ${MACOS_DIR}"
    echo -e "     🔧 Expected binary: ${MACOS_BIN}"
         echo -e "     💡 Run: npm run deploy:macos (uses bundled binaries)"
fi

echo ""

# Check Windows binaries
WINDOWS_DIR="${RESOURCES_DIR}/couchdb-windows"
WINDOWS_BIN="${WINDOWS_DIR}/bin/couchdb"
WINDOWS_CMD="${WINDOWS_DIR}/bin/couchdb.cmd"

if [ -d "$WINDOWS_DIR" ] && ([ -f "$WINDOWS_BIN" ] || [ -f "$WINDOWS_CMD" ]); then
    echo -e "${GREEN}  ✅ Windows: CouchDB binaries found${NC}"
    echo -e "     📁 Location: ${WINDOWS_DIR}"
    if [ -f "$WINDOWS_BIN" ]; then
        echo -e "     🔧 Binary: ${WINDOWS_BIN}"
    fi
    if [ -f "$WINDOWS_CMD" ]; then
        echo -e "     🔧 Batch file: ${WINDOWS_CMD}"
    fi
    
    # Get directory size instead of individual file
    DIR_SIZE=$(du -sh "$WINDOWS_DIR" 2>/dev/null | cut -f1 || echo "unknown")
    if [ "$DIR_SIZE" != "unknown" ]; then
        echo -e "     📊 Bundle size: ${DIR_SIZE}"
    fi
else
    echo -e "${RED}  ❌ Windows: CouchDB binaries missing${NC}"
    echo -e "     📁 Expected: ${WINDOWS_DIR}"
    echo -e "     🔧 Expected binary: ${WINDOWS_BIN} or ${WINDOWS_CMD}"
         echo -e "     💡 Run: npm run deploy:windows (uses bundled binaries)"
fi

echo ""

# Overall status
if [ -f "$MACOS_BIN" ] && ([ -f "$WINDOWS_BIN" ] || [ -f "$WINDOWS_CMD" ]); then
    echo -e "${GREEN}🎉 All CouchDB binaries are ready for cross-platform builds!${NC}"
elif [ -f "$MACOS_BIN" ] || [ -f "$WINDOWS_BIN" ] || [ -f "$WINDOWS_CMD" ]; then
    echo -e "${YELLOW}⚠️  Partial setup: Some CouchDB binaries are missing${NC}"
    echo -e "   Run the appropriate deploy command to auto-download missing binaries"
else
    echo -e "${RED}❌ No CouchDB binaries found${NC}"
    echo -e "   Run deploy commands to auto-download and setup binaries"
fi

echo ""
echo -e "${BLUE}💡 Available commands:${NC}"
echo -e "   npm run deploy:macos     - Build macOS (uses bundled CouchDB)"
echo -e "   npm run deploy:windows   - Build Windows (uses bundled CouchDB)"
echo -e "   npm run check:couchdb    - Run this check again"
echo "" 