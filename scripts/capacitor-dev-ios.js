#!/usr/bin/env node

// Automate Capacitor dev mode for iOS: set dev environment and start
const fs = require('fs');
const os = require('os');
const path = require('path');
const { execSync } = require('child_process');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  throw new Error('No local network IP found.');
}

function main() {
  const ip = getLocalIP();
  const devServerUrl = `http://${ip}:3000`;
  
  console.log(`🚀 Starting Capacitor iOS development mode...`);
  console.log(`📱 Dev server: ${devServerUrl}`);
  
  // Set environment variables for development
  process.env.NODE_ENV = 'development';
  process.env.CAP_MODE = 'dev';
  process.env.CAPACITOR_DEV_SERVER_URL = devServerUrl;
  
  // Sync with development environment
  try {
    console.log('🔄 Syncing Capacitor project for iOS development...');
    execSync('npx cap sync ios', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development',
        CAP_MODE: 'dev',
        CAPACITOR_DEV_SERVER_URL: devServerUrl
      }
    });
  } catch (err) {
    console.warn('\n⚠️  cap sync failed (this might be normal for dev mode):', err.message);
  }
  
  // Open Xcode
  try {
    console.log('📱 Opening Xcode...');
    execSync('npx cap open ios', { stdio: 'inherit' });
    console.log(`✅ iOS Development mode ready!`);
    console.log(`🔧 Start your Next.js dev server: npm run dev`);
    console.log(`📱 The iOS app will connect to: ${devServerUrl}`);
    console.log(`📸 Camera and Photos permissions configured for addstock functionality`);
  } catch (err) {
    console.error('❌ Failed to open Xcode:', err.message);
  }
}

main(); 