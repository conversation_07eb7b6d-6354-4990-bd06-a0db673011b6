/**
 * Verification script for Asset Path Coordinator
 */

const { AssetPathCoordinator, BuildTarget, AssetType } = require('../lib/config/asset-path-coordinator.ts');

console.log('🔍 Verifying Asset Path Coordinator Implementation...\n');

// Test 1: Build Target Detection
console.log('1. Testing Build Target Detection:');
process.env.BUILD_TARGET = 'electron';
const electronCoordinator = new AssetPathCoordinator();
console.log(`   Electron build target: ${electronCoordinator.getConfig().buildTarget}`);
console.log(`   Is static export: ${electronCoordinator.isStaticExport()}`);

process.env.BUILD_TARGET = 'web';
const webCoordinator = new AssetPathCoordinator();
console.log(`   Web build target: ${webCoordinator.getConfig().buildTarget}`);
console.log(`   Is static export: ${webCoordinator.isStaticExport()}`);

// Test 2: Asset Prefix Generation
console.log('\n2. Testing Asset Prefix Generation:');
console.log(`   Electron asset prefix: "${electronCoordinator.getAssetPrefix()}"`);
console.log(`   Web asset prefix: "${webCoordinator.getAssetPrefix()}"`);

// Test 3: Public Path Generation
console.log('\n3. Testing Public Path Generation:');
console.log(`   Electron public path: "${electronCoordinator.getPublicPath()}"`);
console.log(`   Web public path: "${webCoordinator.getPublicPath()}"`);

// Test 4: Asset Path Resolution
console.log('\n4. Testing Asset Path Resolution:');
const testPaths = [
  { path: '/static/chunks/main.js', type: AssetType.CHUNK },
  { path: '/static/css/main.css', type: AssetType.CSS },
  { path: '/fonts/inter.woff2', type: AssetType.FONT }
];

testPaths.forEach(({ path, type }) => {
  const electronResolved = electronCoordinator.resolveAssetPath(type, path);
  const webResolved = webCoordinator.resolveAssetPath(type, path);
  console.log(`   ${type}: ${path}`);
  console.log(`     Electron: ${electronResolved}`);
  console.log(`     Web: ${webResolved}`);
});

// Test 5: Path Validation
console.log('\n5. Testing Path Validation:');
const testValidationPaths = [
  './_next/static/chunks/main.js',
  '/static/chunks/other.js', // This should be flagged for electron
  './static/css/main.css'
];

const electronValidation = electronCoordinator.validatePathConsistency(testValidationPaths);
const webValidation = webCoordinator.validatePathConsistency(testValidationPaths);

console.log(`   Electron validation - Valid: ${electronValidation.isValid}, Errors: ${electronValidation.errors.length}`);
console.log(`   Web validation - Valid: ${webValidation.isValid}, Errors: ${webValidation.errors.length}`);

if (electronValidation.errors.length > 0) {
  console.log('   Electron validation errors:');
  electronValidation.errors.forEach(error => {
    console.log(`     - ${error.type}: ${error.originalPath} -> ${error.expectedPath}`);
  });
}

console.log('\n✅ Asset Path Coordinator verification complete!');

// Clean up
delete process.env.BUILD_TARGET;