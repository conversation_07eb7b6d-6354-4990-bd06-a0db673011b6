#!/usr/bin/env node

/**
 * 🚀 Dual R2 Upload Script
 * 
 * This script uploads a file to R2 twice:
 * 1. With its original versioned name (e.g., bistro-1.0.0.exe)
 * 2. As the latest version (bistro-latest.exe)
 * 
 * This ensures users can download both specific versions and always get the latest
 */

const { uploadFileToR2, createR2Client, validateR2Config } = require('./upload-to-r2.js');
const path = require('path');
require('dotenv').config();

// ANSI color codes for beautiful terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// Emoji helpers
const emoji = {
  rocket: '🚀',
  check: '✅',
  cross: '❌',
  upload: '📤',
  success: '🎉',
  info: 'ℹ️'
};

function log(message, color = colors.white) {
  console.log(`${color}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`${emoji.rocket} ${colors.bright}${step}${colors.reset} ${message}`);
}

function logSuccess(message) {
  console.log(`${colors.green}${emoji.success} ${message}${colors.reset}`);
}

function logError(message) {
  log(`${emoji.cross} ${colors.red}${message}${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.blue}${emoji.info} ${message}${colors.reset}`);
}

async function uploadBothVersions(filePath) {
  const fileName = path.basename(filePath);
  const versionedKey = fileName; // Keep original name (e.g., bistro-1.0.0.exe)
  const latestKey = 'bistro-latest.exe'; // Always use this for latest
  
  console.log('');
  log(`${emoji.rocket} ${colors.bright}Starting Dual R2 Upload Process${colors.reset}`, colors.cyan);
  console.log('');
  
  try {
    // Step 1: Validate configuration
    validateR2Config();
    console.log('');
    
    // Step 2: Create R2 client
    const client = createR2Client();
    console.log('');
    
    // Step 3: Upload versioned file
    logStep('UPLOAD 1/2', `Uploading as versioned file: ${versionedKey}`);
    const versionedResult = await uploadFileToR2(client, filePath, versionedKey);
    logSuccess(`Versioned upload completed: ${versionedKey}`);
    console.log('');
    
    // Step 4: Upload as latest
    logStep('UPLOAD 2/2', `Uploading as latest file: ${latestKey}`);
    const latestResult = await uploadFileToR2(client, filePath, latestKey);
    logSuccess(`Latest upload completed: ${latestKey}`);
    console.log('');
    
    // Step 5: Summary
    log(`${emoji.success} ${colors.green}${colors.bright}DUAL UPLOAD COMPLETED SUCCESSFULLY!${colors.reset}`);
    console.log('');
    logInfo('Upload Summary:');
    logInfo(`  📁 Source File: ${fileName}`);
    logInfo(`  📤 Versioned Key: ${versionedKey}`);
    logInfo(`  📤 Latest Key: ${latestKey}`);
    logInfo(`  📊 File Size: ${Math.round(versionedResult.size / 1024 / 1024)} MB`);
    logInfo(`  🌐 Versioned URL: ${versionedResult.publicUrl}`);
    logInfo(`  🌐 Latest URL: ${latestResult.publicUrl}`);
    console.log('');
    logSuccess('Both versions are now available for download! 🎊');
    
    return {
      versioned: versionedResult,
      latest: latestResult
    };
    
  } catch (error) {
    console.log('');
    logError('Dual upload process failed!');
    logError(error.message);
    console.log('');
    logInfo('Troubleshooting:');
    logInfo('  1. Check your .env file has correct R2 credentials');
    logInfo('  2. Verify your R2 bucket exists and is accessible');
    logInfo('  3. Ensure the file path is correct');
    logInfo('  4. Check your internet connection');
    console.log('');
    throw error;
  }
}

async function main() {
  // Parse command line arguments
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    logError('Usage: node upload-both-to-r2.js <file-path>');
    logInfo('Example: node upload-both-to-r2.js ./electron/release/bistro-1.0.0.exe');
    logInfo('');
    logInfo('This will upload the file twice:');
    logInfo('  1. As bistro-1.0.0.exe (versioned)');
    logInfo('  2. As bistro-latest.exe (latest)');
    process.exit(1);
  }
  
  const filePath = args[0];
  
  try {
    await uploadBothVersions(filePath);
    process.exit(0);
  } catch (error) {
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { uploadBothVersions };