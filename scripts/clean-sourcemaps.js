#!/usr/bin/env node

/**
 * <PERSON>ript to remove sourcemap files from the build directory
 * Run this after the build process to ensure no sourcemaps are deployed
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Build directory paths to check
const buildPaths = [
  path.join(process.cwd(), '.next'),
  path.join(process.cwd(), 'out'),
  path.join(process.cwd(), 'dist')
];

// Get all files recursively
async function getFiles(dir) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(subdirs.map(async (subdir) => {
    const res = path.resolve(dir, subdir);
    try {
      const stats = await stat(res);
      return stats.isDirectory() ? getFiles(res) : res;
    } catch (err) {
      console.error(`${colors.red}Error reading ${res}: ${err.message}${colors.reset}`);
      return [];
    }
  }));
  return files.flat();
}

// Main function
async function cleanSourcemaps() {
  console.log(`${colors.cyan}🔍 Searching for sourcemap files...${colors.reset}`);
  
  let totalRemoved = 0;
  
  for (const buildPath of buildPaths) {
    if (!fs.existsSync(buildPath)) {
      console.log(`${colors.yellow}⚠️ Directory not found: ${buildPath}${colors.reset}`);
      continue;
    }
    
    try {
      const files = await getFiles(buildPath);
      const mapFiles = files.filter(file => file.endsWith('.map'));
      
      console.log(`${colors.blue}Found ${mapFiles.length} sourcemap files in ${buildPath}${colors.reset}`);
      
      if (mapFiles.length > 0) {
        for (const file of mapFiles) {
          try {
            await unlink(file);
            console.log(`${colors.green}✅ Removed: ${file}${colors.reset}`);
            totalRemoved++;
          } catch (err) {
            console.error(`${colors.red}❌ Failed to remove ${file}: ${err.message}${colors.reset}`);
          }
        }
      }
    } catch (err) {
      console.error(`${colors.red}❌ Error processing ${buildPath}: ${err.message}${colors.reset}`);
    }
  }
  
  console.log(`${colors.green}🎉 Source code protection complete! Removed ${totalRemoved} sourcemap files.${colors.reset}`);
}

// Run the cleaner
cleanSourcemaps().catch(err => {
  console.error(`${colors.red}❌ Fatal error: ${err.message}${colors.reset}`);
  process.exit(1);
}); 