#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Electron static asset paths...');

const electronAppDir = path.join(__dirname, '../electron/app');

// Fix HTML files - convert absolute paths to relative
function fixHtmlPaths(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      fixHtmlPaths(filePath);
    } else if (file.endsWith('.html')) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // Track changes
      const originalContent = content;
      
      // Fix absolute paths to relative paths
      content = content.replace(/href="\/_next\//g, 'href="./_next/');
      content = content.replace(/src="\/_next\//g, 'src="./_next/');
      
      // Fix inline script chunk references
      content = content.replace(/"static\/chunks\//g, '"./_next/static/chunks/');
      
      // Fix preload links
      content = content.replace(/href="\/_next\/static\/media\//g, 'href="./_next/static/media/');
      
      // Fix any remaining absolute _next references
      content = content.replace(/"\/_next\//g, '"./_next/');
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✓ Fixed paths in ${path.relative(electronAppDir, filePath)}`);
      }
    }
  }
}

// Fix JavaScript files - ensure chunk loading uses relative paths
function fixJavaScriptPaths(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      fixJavaScriptPaths(filePath);
    } else if (file.endsWith('.js')) {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // Fix chunk loading paths in webpack runtime
      content = content.replace(/"\/_next\/static\/chunks\//g, '"./_next/static/chunks/');
      content = content.replace(/'\/_next\/static\/chunks\//g, '"./_next/static/chunks/');
      
      // Fix any references to static/chunks without _next prefix
      content = content.replace(/"static\/chunks\//g, '"./_next/static/chunks/');
      content = content.replace(/'static\/chunks\//g, '"./_next/static/chunks/');
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✓ Fixed chunk paths in ${path.relative(electronAppDir, filePath)}`);
      }
    }
  }
}

// Main execution
if (!fs.existsSync(electronAppDir)) {
  console.error('❌ Electron app directory not found');
  process.exit(1);
}

console.log(`📂 Processing: ${electronAppDir}`);

// Fix HTML files
console.log('🔧 Fixing HTML file paths...');
fixHtmlPaths(electronAppDir);

// Fix JavaScript files  
console.log('🔧 Fixing JavaScript chunk paths...');
const nextStaticDir = path.join(electronAppDir, '_next/static');
if (fs.existsSync(nextStaticDir)) {
  fixJavaScriptPaths(nextStaticDir);
} else {
  console.warn('⚠️  _next/static directory not found');
}

console.log('✅ Path fixing completed');

// Validate that critical files exist
console.log('🔍 Validating critical files...');
const criticalPaths = [
  'index.html',
  '_next/static',
  '_next/static/chunks'
];

let allValid = true;
for (const criticalPath of criticalPaths) {
  const fullPath = path.join(electronAppDir, criticalPath);
  if (fs.existsSync(fullPath)) {
    console.log(`  ✓ ${criticalPath}`);
  } else {
    console.error(`  ❌ Missing: ${criticalPath}`);
    allValid = false;
  }
}

if (allValid) {
  console.log('✅ All critical files found');
} else {
  console.error('❌ Some critical files are missing');
  process.exit(1);
}

console.log('\n🎯 Path fixing summary:');
console.log('  • Converted absolute /_next/ paths to relative ./_next/');
console.log('  • Fixed inline script chunk references');
console.log('  • Updated JavaScript chunk loading paths');
console.log('  • Validated critical file structure');
console.log('\n🚀 Electron app should now load assets correctly!');