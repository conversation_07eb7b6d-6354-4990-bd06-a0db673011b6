#!/usr/bin/env node

/**
 * System CouchDB Detection Tool
 * 
 * This script detects existing CouchDB installations on the system and
 * provides recommendations for resolving conflicts with the Bistro app.
 */

const http = require('http');
const { spawn } = require('child_process');
const os = require('os');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// CouchDB ports to check
const COUCHDB_PORTS = [5984, 5985, 5986, 5987, 6984, 6985];

async function checkPort(port) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${port}/`, { timeout: 3000 }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          resolve({
            port,
            available: true,
            isCouchDB: !!(json.couchdb || json.uuid),
            version: json.version,
            vendor: json.vendor,
            uuid: json.uuid,
            response: json
          });
        } catch (error) {
          resolve({
            port,
            available: true,
            isCouchDB: false,
            error: 'Invalid JSON response'
          });
        }
      });
    });
    
    req.on('error', () => {
      resolve({
        port,
        available: false,
        isCouchDB: false
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        port,
        available: false,
        isCouchDB: false,
        error: 'Timeout'
      });
    });
  });
}

async function testCouchDBCompatibility(port) {
  const testDbName = 'bistro-compatibility-test-' + Date.now();
  
  try {
    // Try to create a test database
    const createResponse = await fetch(`http://localhost:${port}/${testDbName}`, {
      method: 'PUT',
      signal: AbortSignal.timeout(5000)
    });
    
    const compatible = createResponse.ok || createResponse.status === 412;
    
    if (compatible) {
      // Clean up test database
      try {
        await fetch(`http://localhost:${port}/${testDbName}`, {
          method: 'DELETE',
          signal: AbortSignal.timeout(5000)
        });
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
    }
    
    return {
      compatible,
      status: createResponse.status,
      reason: compatible ? 'Can create/delete databases' : `HTTP ${createResponse.status}`
    };
  } catch (error) {
    return {
      compatible: false,
      reason: error.message
    };
  }
}

function detectSystemCouchDBService() {
  return new Promise((resolve) => {
    if (process.platform === 'win32') {
      // Check Windows services
      const proc = spawn('sc', ['query', 'couchdb'], { stdio: 'pipe' });
      let output = '';
      
      proc.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      proc.on('close', (code) => {
        const serviceExists = code === 0 && output.includes('SERVICE_NAME');
        const isRunning = serviceExists && output.includes('RUNNING');
        
        resolve({
          serviceExists,
          isRunning,
          output: serviceExists ? output : null
        });
      });
      
      proc.on('error', () => {
        resolve({ serviceExists: false, isRunning: false });
      });
    } else if (process.platform === 'darwin') {
      // Check macOS services (launchctl)
      const proc = spawn('launchctl', ['list'], { stdio: 'pipe' });
      let output = '';
      
      proc.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      proc.on('close', () => {
        const serviceExists = output.includes('couchdb') || output.includes('org.apache.couchdb');
        resolve({
          serviceExists,
          isRunning: serviceExists,
          output: serviceExists ? output : null
        });
      });
      
      proc.on('error', () => {
        resolve({ serviceExists: false, isRunning: false });
      });
    } else {
      // Linux - check systemctl
      const proc = spawn('systemctl', ['status', 'couchdb'], { stdio: 'pipe' });
      let output = '';
      
      proc.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      proc.on('close', (code) => {
        const serviceExists = code === 0 || output.includes('couchdb.service');
        const isRunning = serviceExists && output.includes('active (running)');
        
        resolve({
          serviceExists,
          isRunning,
          output: serviceExists ? output : null
        });
      });
      
      proc.on('error', () => {
        resolve({ serviceExists: false, isRunning: false });
      });
    }
  });
}

async function detectSystemCouchDB() {
  logSection('System CouchDB Detection Tool');
  
  logInfo(`Platform: ${process.platform} ${process.arch}`);
  logInfo(`Node.js: ${process.version}`);
  logInfo(`Hostname: ${os.hostname()}`);
  
  // 1. Check for CouchDB services
  logSection('1. System Service Detection');
  
  const serviceInfo = await detectSystemCouchDBService();
  
  if (serviceInfo.serviceExists) {
    if (serviceInfo.isRunning) {
      logWarning('CouchDB system service is installed and running');
    } else {
      logInfo('CouchDB system service is installed but not running');
    }
    
    if (serviceInfo.output) {
      console.log('\nService details:');
      console.log(serviceInfo.output.trim());
    }
  } else {
    logSuccess('No CouchDB system service detected');
  }
  
  // 2. Check ports
  logSection('2. Port Availability Check');
  
  const portResults = [];
  
  for (const port of COUCHDB_PORTS) {
    logInfo(`Checking port ${port}...`);
    const result = await checkPort(port);
    portResults.push(result);
    
    if (result.available && result.isCouchDB) {
      logWarning(`Port ${port}: CouchDB detected (v${result.version || 'unknown'})`);
      
      // Test compatibility
      const compatibility = await testCouchDBCompatibility(port);
      if (compatibility.compatible) {
        logSuccess(`  Compatible: ${compatibility.reason}`);
      } else {
        logError(`  Incompatible: ${compatibility.reason}`);
      }
    } else if (result.available && !result.isCouchDB) {
      logWarning(`Port ${port}: Other service detected`);
    } else {
      logSuccess(`Port ${port}: Available`);
    }
  }
  
  // 3. Summary and recommendations
  logSection('3. Summary and Recommendations');
  
  const couchdbPorts = portResults.filter(r => r.available && r.isCouchDB);
  const busyPorts = portResults.filter(r => r.available && !r.isCouchDB);
  const availablePorts = portResults.filter(r => !r.available);
  
  if (couchdbPorts.length > 0) {
    logWarning(`Found ${couchdbPorts.length} existing CouchDB installation(s):`);
    couchdbPorts.forEach(port => {
      console.log(`  - Port ${port.port}: CouchDB v${port.version || 'unknown'}`);
    });
    
    console.log('\n💡 Recommendations:');
    console.log('1. The Bistro app will automatically detect and work around existing CouchDB');
    console.log('2. If issues persist, consider stopping the system CouchDB service:');
    
    if (process.platform === 'win32') {
      console.log('   Windows: sc stop couchdb && sc config couchdb start= disabled');
    } else if (process.platform === 'darwin') {
      console.log('   macOS: sudo launchctl unload /Library/LaunchDaemons/org.apache.couchdb.plist');
    } else {
      console.log('   Linux: sudo systemctl stop couchdb && sudo systemctl disable couchdb');
    }
    
    console.log('3. Or configure the system CouchDB to use a different port');
  } else if (busyPorts.length > 0) {
    logWarning(`Found ${busyPorts.length} non-CouchDB service(s) on CouchDB ports`);
    console.log('\n💡 The Bistro app will automatically find alternative ports');
  } else {
    logSuccess('All CouchDB ports are available - no conflicts expected');
  }
  
  if (availablePorts.length > 0) {
    logInfo(`Available ports for Bistro: ${availablePorts.map(p => p.port).join(', ')}`);
  }
  
  // 4. Next steps
  logSection('4. Next Steps');
  
  console.log('🚀 To test the Bistro app with current system configuration:');
  console.log('   npm run electron:start');
  console.log('');
  console.log('🔧 To debug Windows-specific issues:');
  console.log('   npm run debug:windows-couchdb');
  console.log('');
  console.log('🧪 To validate Windows CouchDB fixes:');
  console.log('   npm run test:windows-couchdb-fix');
  
  return {
    serviceInfo,
    portResults,
    couchdbPorts,
    availablePorts: availablePorts.map(p => p.port)
  };
}

// Run the detection
detectSystemCouchDB().then(results => {
  console.log('\n' + '='.repeat(60));
  log('System CouchDB detection completed!', 'green');
  console.log('='.repeat(60));
}).catch(error => {
  logError(`Detection failed: ${error.message}`);
  process.exit(1);
});
