#!/bin/bash

# Clear browser caches script
# This script helps clear browser caches to test new code without interference from old cached data

echo "🧹 Browser Cache Clearing Utility 🧹"
echo "-----------------------------------"
echo ""

# Function to clear Chrome cache on macOS
clear_chrome_macos() {
  echo "🔍 Attempting to close Chrome..."
  osascript -e 'tell application "Google Chrome" to quit' 2>/dev/null
  sleep 1
  
  echo "🗑️  Clearing Chrome cache..."
  rm -rf ~/Library/Caches/Google/Chrome/Default/Cache/*
  rm -rf ~/Library/Caches/Google/Chrome/Default/Code\ Cache/*
  rm -rf ~/Library/Application\ Support/Google/Chrome/Default/IndexedDB/*
  rm -rf ~/Library/Application\ Support/Google/Chrome/Default/Local\ Storage/*
  
  echo "✅ Chrome cache cleared!"
}

# Function to clear Safari cache on macOS
clear_safari_macos() {
  echo "🔍 Attempting to close Safari..."
  osascript -e 'tell application "Safari" to quit' 2>/dev/null
  sleep 1
  
  echo "🗑️  Clearing Safari cache..."
  rm -rf ~/Library/Caches/com.apple.Safari/*
  rm -rf ~/Library/Safari/LocalStorage/*
  rm -rf ~/Library/Safari/Databases/*
  
  echo "✅ Safari cache cleared!"
}

# Function to clear Firefox cache on macOS
clear_firefox_macos() {
  echo "🔍 Attempting to close Firefox..."
  osascript -e 'tell application "Firefox" to quit' 2>/dev/null
  sleep 1
  
  echo "🗑️  Clearing Firefox cache..."
  rm -rf ~/Library/Caches/Firefox/*
  rm -rf ~/Library/Application\ Support/Firefox/Profiles/*/storage/*
  
  echo "✅ Firefox cache cleared!"
}

# Detect OS
if [[ "$OSTYPE" == "darwin"* ]]; then
  echo "🍎 macOS detected"
  
  echo ""
  echo "Which browser cache would you like to clear?"
  echo "1) Chrome"
  echo "2) Safari"
  echo "3) Firefox"
  echo "4) All browsers"
  echo ""
  read -p "Enter your choice (1-4): " browser_choice
  
  case $browser_choice in
    1)
      clear_chrome_macos
      ;;
    2)
      clear_safari_macos
      ;;
    3)
      clear_firefox_macos
      ;;
    4)
      clear_chrome_macos
      clear_safari_macos
      clear_firefox_macos
      ;;
    *)
      echo "❌ Invalid choice. Exiting."
      exit 1
      ;;
  esac
  
  echo ""
  echo "🎉 Cache clearing complete! Please restart your browser."
  
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
  echo "🐧 Linux detected"
  echo "❌ Linux cache clearing not implemented yet."
  echo "Please use the in-app cache cleaner or clear your browser cache manually."
  
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
  echo "🪟 Windows detected"
  echo "❌ Windows cache clearing not implemented yet."
  echo "Please use the in-app cache cleaner or clear your browser cache manually."
  
else
  echo "❓ Unknown operating system: $OSTYPE"
  echo "Please use the in-app cache cleaner or clear your browser cache manually."
fi

echo ""
echo "💡 TIP: You can also use the 'Clear Cache' button in the app's navigation bar."
echo "    This will clear the PouchDB databases stored in your browser." 