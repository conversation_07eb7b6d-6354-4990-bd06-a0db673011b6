#!/usr/bin/env node

/**
 * 🚀 Direct R2 Upload Script
 * 
 * This script uploads files directly to Cloudflare R2 with proper credentials
 * Provides detailed terminal feedback and handles errors gracefully
 */

const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const { Upload } = require('@aws-sdk/lib-storage');
const { NodeHttpHandler } = require("@aws-sdk/node-http-handler");
const https = require("https");
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// ANSI color codes for beautiful terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Emoji helpers
const emoji = {
  rocket: '🚀',
  check: '✅',
  cross: '❌',
  warning: '⚠️',
  upload: '📤',
  file: '📁',
  config: '🔧',
  success: '🎉',
  loading: '⏳',
  info: 'ℹ️'
};

function log(message, color = colors.white) {
  console.log(`${color}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`${emoji.rocket} ${colors.bright}${step}${colors.reset} ${message}`);
}

function logSuccess(message) {
  console.log(`${colors.green}${emoji.success} ${message}${colors.reset}`);
}

function logError(message) {
  log(`${emoji.cross} ${colors.red}${message}${colors.reset}`);
}

function logWarning(message) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.blue}${emoji.info} ${message}${colors.reset}`);
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function validateR2Config() {
  logStep('VALIDATE', 'Checking R2 configuration...');
  
  const requiredEnvVars = [
    'R2_ACCESS_KEY_ID',
    'R2_SECRET_ACCESS_KEY',
    'R2_ENDPOINT',
    'R2_BUCKET_NAME'
  ];
  
  const missing = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    logError('Missing required environment variables:');
    missing.forEach(varName => {
      log(`  ${emoji.cross} ${varName}`, colors.red);
    });
    log('');
    logInfo('Please add these to your .env file:');
    missing.forEach(varName => {
      log(`  ${varName}=your_value_here`, colors.cyan);
    });
    log('');
    logInfo('See R2_SETUP.md for detailed instructions.');
    process.exit(1);
  }
  
  logSuccess('R2 configuration validated');
  logInfo(`  Endpoint: ${process.env.R2_ENDPOINT}`);
  logInfo(`  Bucket: ${process.env.R2_BUCKET_NAME}`);
  logInfo(`  Region: ${process.env.R2_REGION || 'auto'}`);
}

function createR2Client() {
  logStep('CONNECT', 'Creating R2 client...');
  
  try {
    const httpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 60000, // 1 minute
    });

    const client = new S3Client({
      region: process.env.R2_REGION || 'auto',
      endpoint: process.env.R2_ENDPOINT,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
      },
      forcePathStyle: true, // Required for R2
      requestHandler: new NodeHttpHandler({
        httpsAgent,
        connectionTimeout: 300000, // 5 minutes
        socketTimeout: 300000, // 5 minutes
      }),
      maxAttempts: 5, // Increased retries
    });
    
    logSuccess('R2 client created successfully');
    return client;
  } catch (error) {
    logError(`Failed to create R2 client: ${error.message}`);
    process.exit(1);
  }
}

async function uploadFileToR2(client, filePath, key) {
  logStep('UPLOAD', `Uploading ${path.basename(filePath)} to R2...`);
  
  const maxRetries = 3;
  let attempt = 0;
  
  while (attempt < maxRetries) {
    try {
      attempt++;
      if (attempt > 1) {
        logWarning(`Retry attempt ${attempt}/${maxRetries}...`);
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }
      
      // Get file stats
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;
      
      if (attempt === 1) {
        logInfo(`  File: ${path.basename(filePath)}`);
        logInfo(`  Size: ${formatFileSize(fileSize)}`);
        logInfo(`  Key: ${key}`);
        logInfo(`  📖 Preparing file stream for upload...`);
      }

      // Determine content type
      let contentType = 'application/octet-stream';
      if (filePath.endsWith('.exe')) {
        contentType = 'application/x-msdownload';
      }
      
      // Create file stream for large files
      const fileStream = fs.createReadStream(filePath);
      
      // Handle stream errors
      fileStream.on('error', (error) => {
        logError(`File stream error: ${error.message}`);
        throw error;
      });
      
      // Upload to R2 with streaming
      const uploader = new Upload({
        client,
        params: {
          Bucket: process.env.R2_BUCKET_NAME,
          Key: key,
          Body: fileStream,
          ContentType: contentType,
          ContentLength: fileSize, // Important for large files
          // Make file publicly readable if needed
          // ACL: 'public-read', // Uncomment if your bucket is public
        },
        // Configure for large files with more conservative settings
        partSize: 1024 * 1024 * 5, // 5MB parts (smaller for better reliability)
        queueSize: 2, // Reduce concurrent uploads to avoid overwhelming connection
        leavePartsOnError: false, // Clean up failed parts
      });

      // Add progress tracking
      let lastProgress = 0;
      uploader.on('httpUploadProgress', (progress) => {
        if (progress.total) {
          const percent = ((progress.loaded / progress.total) * 100).toFixed(1);
          // Only update progress every 5% to reduce console spam
          if (parseFloat(percent) - lastProgress >= 5) {
            process.stdout.write(`\r${colors.blue}${emoji.upload} Uploading... ${percent}% (${formatFileSize(progress.loaded)}/${formatFileSize(progress.total)})${colors.reset}`);
            lastProgress = parseFloat(percent);
          }
        }
      });
      
      logInfo(`  🚀 Starting streaming upload to R2... (${formatFileSize(fileSize)})`);
      const startUploadTime = Date.now();
      
      const result = await uploader.done();
      
      // Clear progress line
      process.stdout.write('\r' + ' '.repeat(80) + '\r'); // Clear line
      
      const uploadTime = ((Date.now() - startUploadTime) / 1000).toFixed(2);
      const uploadSpeed = (fileSize / (Date.now() - startUploadTime) * 1000);
      let speedStr = `${(uploadSpeed / (1024 * 1024)).toFixed(2)} MB/s`;
      if (uploadSpeed < 1024 * 1024) {
        speedStr = `${(uploadSpeed / 1024).toFixed(2)} KB/s`;
      }
      
      logSuccess(`✅ Upload completed successfully! (${uploadTime}s @ ${speedStr})`);
      logInfo(`  ETag: ${result.ETag}`);
      
      // Generate public URL
      const publicUrl = `${process.env.R2_ENDPOINT}/${process.env.R2_BUCKET_NAME}/${key}`;
      logInfo(`  📡 Object URL: ${publicUrl}`);
      
      return {
        success: true,
        key,
        etag: result.ETag,
        publicUrl,
        size: fileSize,
        uploadTime: parseFloat(uploadTime),
        uploadSpeed: speedStr
      };
      
    } catch (error) {
      if (attempt < maxRetries) {
        logWarning(`Upload attempt ${attempt} failed: ${error.message}`);
        if (error.code === 'EPIPE' || error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
          logWarning('Network error detected, will retry...');
          continue; // Retry for network errors
        }
      }
      
      logError(`Upload failed after ${attempt} attempts: ${error.message}`);
      throw error;
    }
  }
  
  throw new Error(`Upload failed after ${maxRetries} attempts`);
}

async function verifyUpload(client, key) {
  logStep('VERIFY', 'Verifying upload...');
  
  try {
    const command = new HeadObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: key,
    });
    
    const result = await client.send(command);
    
    logSuccess('Upload verified successfully!');
    logInfo(`  Content Length: ${formatFileSize(result.ContentLength)}`);
    logInfo(`  Last Modified: ${result.LastModified}`);
    logInfo(`  ETag: ${result.ETag}`);
    
    return true;
  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    return false;
  }
}

function findLatestWindowsExecutable() {
  const latestPath = path.join(process.cwd(), 'releases', 'windows', 'latest');
  
  if (!fs.existsSync(latestPath)) {
    throw new Error('No latest Windows release found in releases/windows/latest/');
  }
  
  const files = fs.readdirSync(latestPath);
  const exeFile = files.find(file => file.endsWith('.exe') && file.includes('bistro'));
  
  if (!exeFile) {
    throw new Error('No bistro executable found in releases/windows/latest/');
  }
  
  return path.join(latestPath, exeFile);
}

async function main() {
  // Parse command line arguments
  const args = process.argv.slice(2);
  
  let filePath;
  let key;
  
  if (args.length === 0) {
    // No arguments provided - auto-find latest Windows executable
    logInfo('No file path provided, searching for latest Windows release...');
    try {
      filePath = findLatestWindowsExecutable();
      key = 'bistro.exe'; // Use memory default key format
      logSuccess(`Found latest release: ${path.basename(filePath)}`);
    } catch (error) {
      logError(error.message);
      logInfo('Usage: node upload-to-r2.js <file-path> [key]');
      logInfo('Example: node upload-to-r2.js ./releases/windows/latest/bistro-1.0.0.exe bistro.exe');
      process.exit(1);
    }
  } else {
    // Manual file path provided
    filePath = args[0];
    key = args[1] || 'bistro.exe'; // Use memory default key format
  }
  
  console.log('');
  log(`${emoji.rocket} ${colors.bright}Starting R2 Upload Process${colors.reset}`, colors.cyan);
  console.log('');
  
  try {
    // Step 1: Validate configuration
    validateR2Config();
    console.log('');
    
    // Step 2: Create R2 client
    const client = createR2Client();
    console.log('');
    
    // Step 3: Upload file
    const uploadResult = await uploadFileToR2(client, filePath, key);
    console.log('');
    
    // Step 4: Verify upload
    const verified = await verifyUpload(client, key);
    console.log('');
    
    if (verified) {
      log(`${emoji.success} ${colors.green}${colors.bright}UPLOAD COMPLETED SUCCESSFULLY!${colors.reset}`);
      console.log('');
      logInfo('Summary:');
      logInfo(`  File: ${path.basename(filePath)}`);
      logInfo(`  Key: ${key}`);
      logInfo(`  Size: ${formatFileSize(uploadResult.size)}`);
      logInfo(`  URL: ${uploadResult.publicUrl}`);
      console.log('');
      logSuccess('Your Windows app is now available for download!');
    } else {
      logError('Upload verification failed. Please check your R2 configuration.');
      process.exit(1);
    }
    
  } catch (error) {
    console.log('');
    logError('Upload process failed!');
    logError(error.message);
    console.log('');
    logInfo('Troubleshooting:');
    logInfo('  1. Check your .env file has correct R2 credentials');
    logInfo('  2. Verify your R2 bucket exists and is accessible');
    logInfo('  3. Ensure the file path is correct');
    logInfo('  4. Check your internet connection');
    console.log('');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { uploadFileToR2, createR2Client, validateR2Config };