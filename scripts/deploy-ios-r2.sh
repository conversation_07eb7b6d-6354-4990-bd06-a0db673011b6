#!/bin/bash

# 📱 iOS R2 Deployment Script
# Creates iOS update metadata and deploys to R2 storage

set -e

echo "📱 iOS R2 Deployment Pipeline"
echo "============================="

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Version management
VERSION_FILE="$PROJECT_ROOT/package.json"
if [ -f "$VERSION_FILE" ]; then
    VERSION=$(node -p "require('$VERSION_FILE').version")
else
    VERSION="1.0.0"
fi

echo "📦 Version: $VERSION"

# Step 1: Create update metadata JSON
UPDATE_JSON="/tmp/ios-update.json"
cat > "$UPDATE_JSON" << EOF
{
  "version": "$VERSION",
  "url": "https://apps.apple.com/app/bistro-restaurant-pos/id1234567890",
  "releaseNotes": "Latest version of Bistro Restaurant POS",
  "mandatory": false,
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF

echo "📄 Created iOS update metadata: ios-update.json"

# Step 2: Upload to R2
echo ""
echo "☁️  Uploading iOS update metadata to R2..."

# Upload update metadata
echo "📤 Uploading update metadata..."
node "$SCRIPT_DIR/upload-to-r2.js" "$UPDATE_JSON" "ios-update.json"

# Step 3: Cleanup
rm -f "$UPDATE_JSON"

# Step 4: Summary
echo ""
echo "🎉 iOS deployment completed!"
echo ""
echo "📋 Deployment Summary:"
echo "  📱 Platform: iOS"
echo "  📦 Version: $VERSION"
echo "  🏪 Distribution: App Store"
echo ""
echo "🔗 Update URL:"
echo "  📄 Update Info: https://pub-d1ae66d7e9a247a08a1ad96b22c13e10.r2.dev/ios-update.json"
echo ""
echo "📝 Next Steps:"
echo "  1. Build and submit iOS app to App Store Connect"
echo "  2. Update the App Store URL in ios-update.json if needed"
echo "  3. Test update detection on iOS devices"
echo ""
echo "✅ iOS update system is now configured!"