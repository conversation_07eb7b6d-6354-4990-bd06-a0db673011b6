#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing RSC chunk paths for static builds...');

const outDir = path.join(__dirname, '..', 'out');

if (!fs.existsSync(outDir)) {
  console.error('❌ Output directory not found');
  process.exit(1);
}

// Fix HTML files - convert RSC chunk references to relative paths
function fixHtmlFiles(dir) {
  const files = fs.readdirSync(dir);
  let filesFixed = 0;
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      filesFixed += fixHtmlFiles(filePath);
    } else if (file.endsWith('.html')) {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      
      // Fix RSC chunk references in inline JavaScript
      // Pattern: "static/chunks/..." -> "./_next/static/chunks/..."
      content = content.replace(/"static\/chunks\//g, '"./_next/static/chunks/');
      content = content.replace(/'static\/chunks\//g, '"./_next/static/chunks/');
      
      // Fix JSON-encoded chunk references in RSC payloads
      content = content.replace(/\\"static\\\/chunks\\\//g, '\\"./_next/static/chunks/');
      content = content.replace(/,\\"static\\\//g, ',\\"./_next/static/');
      
      // Fix CSS href references in RSC payloads
      content = content.replace(/\\"\\\/\\\_next\\\/static\\\//g, '\\"./_next/static/');
      content = content.replace(/href=\\"\\\/\\\_next\\\//g, 'href=\\"./_next/');
      
      // Fix any remaining absolute _next references to relative
      content = content.replace(/href="\/_next\//g, 'href="./_next/');
      content = content.replace(/src="\/_next\//g, 'src="./_next/');
      
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✓ Fixed RSC chunks in ${path.relative(outDir, filePath)}`);
        filesFixed++;
      }
    }
  }
  
  return filesFixed;
}

// Main execution
console.log(`📂 Processing: ${outDir}`);

const filesFixed = fixHtmlFiles(outDir);

if (filesFixed > 0) {
  console.log(`✅ Fixed RSC chunk paths in ${filesFixed} files`);
  console.log('\n🎯 RSC chunk path fixing summary:');
  console.log('  • Converted "static/chunks/" to "./_next/static/chunks/" in inline JavaScript');
  console.log('  • Fixed absolute /_next/ paths to relative ./_next/ paths');
  console.log('  • Preserved next/font functionality with proper asset loading');
} else {
  console.log('✅ No files needed fixing');
}

console.log('\n🚀 RSC chunk paths are now compatible with static builds!');
