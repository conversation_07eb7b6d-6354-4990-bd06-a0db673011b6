/**
 * Simple verification that the asset configuration files exist and have correct structure
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Asset Path Configuration System Structure...\n');

const filesToCheck = [
  'lib/config/asset-path-coordinator.ts',
  'lib/config/asset-config.ts',
  'lib/utils/asset-path-validator.ts',
  'lib/config/index.ts'
];

let allFilesExist = true;

filesToCheck.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  const exists = fs.existsSync(fullPath);
  
  console.log(`${exists ? '✅' : '❌'} ${filePath}`);
  
  if (exists) {
    const content = fs.readFileSync(fullPath, 'utf8');
    const lines = content.split('\n').length;
    console.log(`   📄 ${lines} lines`);
    
    // Check for key exports/classes
    if (filePath.includes('asset-path-coordinator')) {
      const hasCoordinator = content.includes('export class AssetPathCoordinator');
      const hasEnums = content.includes('export enum BuildTarget') && content.includes('export enum AssetType');
      console.log(`   🏗️  AssetPathCoordinator class: ${hasCoordinator ? '✅' : '❌'}`);
      console.log(`   📋 Enums (BuildTarget, AssetType): ${hasEnums ? '✅' : '❌'}`);
    }
    
    if (filePath.includes('asset-config')) {
      const hasConfigManager = content.includes('export class AssetConfigManager');
      const hasInterfaces = content.includes('StaticExportConfig') && content.includes('WebpackAssetConfig');
      console.log(`   🏗️  AssetConfigManager class: ${hasConfigManager ? '✅' : '❌'}`);
      console.log(`   📋 Configuration interfaces: ${hasInterfaces ? '✅' : '❌'}`);
    }
    
    if (filePath.includes('asset-path-validator')) {
      const hasValidator = content.includes('export class AssetPathValidator');
      const hasValidationTypes = content.includes('ValidationResult') && content.includes('FileAssetReference');
      console.log(`   🏗️  AssetPathValidator class: ${hasValidator ? '✅' : '❌'}`);
      console.log(`   📋 Validation types: ${hasValidationTypes ? '✅' : '❌'}`);
    }
    
    if (filePath.includes('index.ts')) {
      const hasExports = content.includes('export {') && content.includes('AssetPathCoordinator');
      console.log(`   📤 Main exports: ${hasExports ? '✅' : '❌'}`);
    }
  } else {
    allFilesExist = false;
  }
  
  console.log('');
});

console.log(`📊 Summary: ${allFilesExist ? '✅ All files created successfully' : '❌ Some files are missing'}`);

// Check if the files can be imported (basic syntax check)
console.log('\n🔧 Checking TypeScript syntax...');
try {
  const { execSync } = require('child_process');
  
  // Try to compile the TypeScript files to check for syntax errors
  filesToCheck.forEach(filePath => {
    try {
      execSync(`npx tsc --noEmit --skipLibCheck ${filePath}`, { stdio: 'pipe' });
      console.log(`✅ ${filePath} - TypeScript syntax OK`);
    } catch (error) {
      console.log(`❌ ${filePath} - TypeScript syntax errors`);
      // Don't show the full error to keep output clean
    }
  });
} catch (error) {
  console.log('⚠️  TypeScript compiler not available, skipping syntax check');
}

console.log('\n✅ Asset Path Configuration System structure verification complete!');