#!/bin/bash

# 🚀 Secure Windows R2 Deployment Script (No Dependencies)
# This script builds the Windows app and uploads it directly to Cloudflare R2
# Skips dependency installation to avoid hanging

set -e  # Exit on any error

echo "🚀 Starting secure Windows R2 deployment process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
RELEASE_DIR="electron/release"
UPLOAD_SCRIPT="scripts/upload-to-r2.js"

echo ""
echo -e "${CYAN}${BOLD}🔧 SECURE R2 DEPLOYMENT (NO DEPS)${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Release Directory: ${RELEASE_DIR}"
echo -e "  Upload Script: ${UPLOAD_SCRIPT}"
echo -e "  Credentials: From .env file (secure)"
echo ""

# Step 1: Validate environment
echo -e "${YELLOW}🔍 Validating environment...${NC}"

if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found!${NC}"
    echo -e "  Please create .env file with your R2 credentials."
    echo -e "  See R2_SETUP.md for instructions."
    exit 1
fi

if [ ! -f "$UPLOAD_SCRIPT" ]; then
    echo -e "${RED}❌ Upload script not found: $UPLOAD_SCRIPT${NC}"
    exit 1
fi

echo -e "${GREEN}  ✅ Environment validated${NC}"
echo ""

# Step 2: Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
if [ -d "$RELEASE_DIR" ]; then
    rm -rf "$RELEASE_DIR"
    echo -e "${GREEN}  ✅ Cleaned release directory${NC}"
else
    echo -e "${BLUE}  ℹ️  No previous builds to clean${NC}"
fi
echo ""

# Skip dependency installation (main difference from original script)
echo -e "${BLUE}ℹ️  Skipping dependency installation (using existing)${NC}"
echo ""

# Step 3: Build the app
echo -e "${YELLOW}🏗️ Building Electron app for Windows...${NC}"
echo -e "  Building production-ready Windows installer..."
echo -e "  ${BLUE}Note: USB detection warnings are normal for cross-platform builds${NC}"
echo ""

cd electron
npm run electron:build:win
cd ..

echo ""
echo -e "${GREEN}  ✅ Windows build completed${NC}"
echo ""

# Step 4: Find the .exe file
echo -e "${YELLOW}🔍 Locating built executable...${NC}"
EXE_FILE=$(find "$RELEASE_DIR" -maxdepth 1 -name "*.exe" | head -1)

if [ -z "$EXE_FILE" ]; then
    echo -e "${RED}❌ No .exe file found in release directory!${NC}"
    echo -e "  Expected location: ${RELEASE_DIR}"
    echo -e "  Available files:"
    ls -la "$RELEASE_DIR" 2>/dev/null || echo "    (directory not found)"
    exit 1
fi

echo -e "${GREEN}  ✅ Found executable: $(basename "$EXE_FILE")${NC}"

# Get file size for feedback
FILE_SIZE=$(stat -f%z "$EXE_FILE" 2>/dev/null || stat -c%s "$EXE_FILE" 2>/dev/null || echo "unknown")
if [ "$FILE_SIZE" != "unknown" ]; then
    # Convert bytes to human readable
    if [ "$FILE_SIZE" -gt 1048576 ]; then
        SIZE_MB=$((FILE_SIZE / 1048576))
        echo -e "  📊 File size: ${SIZE_MB} MB"
    else
        SIZE_KB=$((FILE_SIZE / 1024))
        echo -e "  📊 File size: ${SIZE_KB} KB"
    fi
fi
echo ""

# Step 5: Upload to R2 using secure script
echo -e "${YELLOW}📤 Uploading to Cloudflare R2...${NC}"
echo -e "  Using direct credential authentication"
echo -e "  No API endpoints or web server required"
echo ""

# Run the secure upload script with enhanced output
if node "$UPLOAD_SCRIPT" "$EXE_FILE" "bistro-latest.exe"; then
    echo ""
    echo -e "${GREEN}${BOLD}🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!${NC}"
    echo ""
    echo -e "${CYAN}📋 Deployment Summary:${NC}"
    echo -e "  ✅ Built Windows app ($(basename "$EXE_FILE"))"
    echo -e "  ✅ Uploaded securely to Cloudflare R2"
    echo -e "  ✅ File verified and available for download"
    echo -e "  ✅ Landing page download buttons ready"
    echo ""
    echo -e "${YELLOW}🔗 What's Next:${NC}"
    echo -e "  1. 🌐 Start your Next.js server: ${BOLD}npm run dev${NC}"
    echo -e "  2. 🖱️  Visit landing page and test Windows download"
    echo -e "  3. ✅ Verify download works correctly"
    echo -e "  4. 🎊 Share with your users!"
    echo ""
    echo -e "${GREEN}${BOLD}✅ Your Windows app is now live and ready!${NC}"
    echo ""
else
    echo ""
    echo -e "${RED}${BOLD}❌ DEPLOYMENT FAILED!${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting Steps:${NC}"
    echo -e "  1. Check your .env file has correct R2 credentials"
    echo -e "  2. Verify your R2 bucket exists and is accessible"
    echo -e "  3. Ensure you have internet connection"
    echo -e "  4. Check R2_SETUP.md for configuration help"
    echo -e "  5. Try uploading manually: ${BOLD}node ${UPLOAD_SCRIPT} \"${EXE_FILE}\" \"bistro-latest.exe\"${NC}"
    echo ""
    exit 1
fi