#!/bin/bash

# 🌐 Web Deployment Script for Landing Page Only
# This script builds and deploys the web version without native dependencies

set -e

echo "🌐 Starting web deployment (landing page only)..."

# Check if we have the required files
if [[ ! -f "package.web.json" ]]; then
    echo "❌ Error: package.web.json not found!"
    echo "   This file is required for web builds to avoid native dependency issues."
    exit 1
fi

if [[ ! -f "Dockerfile.web" ]]; then
    echo "❌ Error: Dockerfile.web not found!"
    echo "   This file is required for web builds."
    exit 1
fi

echo "✅ Required files found"

# Set build arguments (customize as needed)
NEXT_PUBLIC_COUCHDB_URL=${NEXT_PUBLIC_COUCHDB_URL:-""}
NEXT_PUBLIC_COUCHDB_USER=${NEXT_PUBLIC_COUCHDB_USER:-""}
NEXT_PUBLIC_COUCHDB_PASSWORD=${NEXT_PUBLIC_COUCHDB_PASSWORD:-""}
NEXT_PUBLIC_COUCHDB_DB_NAME=${NEXT_PUBLIC_COUCHDB_DB_NAME:-""}

# Build with the web-specific Dockerfile
echo "🔨 Building web Docker image..."
docker build \
    -f Dockerfile.web \
    --dockerignore .dockerignore.web \
    --build-arg NEXT_PUBLIC_COUCHDB_URL="$NEXT_PUBLIC_COUCHDB_URL" \
    --build-arg NEXT_PUBLIC_COUCHDB_USER="$NEXT_PUBLIC_COUCHDB_USER" \
    --build-arg NEXT_PUBLIC_COUCHDB_PASSWORD="$NEXT_PUBLIC_COUCHDB_PASSWORD" \
    --build-arg NEXT_PUBLIC_COUCHDB_DB_NAME="$NEXT_PUBLIC_COUCHDB_DB_NAME" \
    -t bistro-web:latest \
    .

echo "✅ Web build completed successfully!"
echo ""
echo "🚀 Your web build is ready!"
echo "   • Image: bistro-web:latest"
echo "   • Port: 3010"
echo "   • Content: Landing page only (no restaurant functionality)"
echo ""
echo "To run locally:"
echo "   docker run -p 3010:3010 bistro-web:latest"
echo ""
echo "To push to registry:"
echo "   docker tag bistro-web:latest your-registry/bistro-web:latest"
echo "   docker push your-registry/bistro-web:latest"