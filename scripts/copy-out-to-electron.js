#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 🎯 Enhanced static file copy process for Electron
const outDir = path.join(__dirname, '../out');
const electronAppDir = path.join(__dirname, '../electron/app');

console.log('🔄 Starting enhanced static file copy process for Electron...');
console.log(`📂 Source: ${outDir}`);
console.log(`📂 Target: ${electronAppDir}`);
console.log(`📁 Out directory exists: ${fs.existsSync(outDir)}`);

// 🚨 CRITICAL: Validate that static export was successful
function validateStaticExport() {
  console.log('🔍 Validating Next.js static export...');
  
  if (!fs.existsSync(outDir)) {
    throw new Error(`❌ Static export directory not found: ${outDir}\nPlease run 'next build' with output: 'export' first.`);
  }
  
  // Check for essential static export files
  const essentialFiles = [
    'index.html',
    '_next/static'
  ];
  
  const missingFiles = [];
  for (const file of essentialFiles) {
    const filePath = path.join(outDir, file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  }
  
  if (missingFiles.length > 0) {
    throw new Error(`❌ Static export is incomplete. Missing files/directories: ${missingFiles.join(', ')}`);
  }
  
  // Count HTML files to ensure export worked
  const htmlFiles = [];
  function findHtmlFiles(dir, relativePath = '') {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const relativeItemPath = path.join(relativePath, item);
      
      if (fs.statSync(itemPath).isDirectory() && !item.startsWith('_')) {
        findHtmlFiles(itemPath, relativeItemPath);
      } else if (item.endsWith('.html')) {
        htmlFiles.push(relativeItemPath);
      }
    }
  }
  
  findHtmlFiles(outDir);
  console.log(`📄 Found ${htmlFiles.length} HTML pages in static export:`);
  htmlFiles.forEach(file => console.log(`  ✓ ${file}`));
  
  if (htmlFiles.length === 0) {
    throw new Error('❌ No HTML pages found in static export. Export may have failed.');
  }
  
  console.log('✅ Static export validation passed');
  return htmlFiles;
}

// 🧹 Clean and prepare Electron app directory
function prepareElectronAppDir() {
  console.log('🧹 Preparing Electron app directory...');
  
  // Remove existing app directory
  if (fs.existsSync(electronAppDir)) {
    fs.rmSync(electronAppDir, { recursive: true, force: true });
    console.log('  ✓ Removed existing app directory');
  }
  
  // Create fresh app directory
  fs.mkdirSync(electronAppDir, { recursive: true });
  console.log('  ✓ Created fresh app directory');
}

// 📦 Copy static export to Electron app directory
function copyStaticExport() {
  console.log('📦 Copying static export to Electron...');
  
  try {
    // Copy entire out directory contents to electron/app
    fs.cpSync(outDir, electronAppDir, { 
      recursive: true,
      force: true,
      preserveTimestamps: true 
    });
    
    console.log('✅ Static export copied successfully');
    
    // Verify the copy was successful
    const copiedFiles = fs.readdirSync(electronAppDir);
    console.log(`📁 Electron app directory now contains ${copiedFiles.length} items:`);
    copiedFiles.forEach(file => {
      const filePath = path.join(electronAppDir, file);
      const isDir = fs.statSync(filePath).isDirectory();
      console.log(`  ${isDir ? '📁' : '📄'} ${file}`);
    });
    
    return copiedFiles;
    
  } catch (error) {
    throw new Error(`❌ Failed to copy static export: ${error.message}`);
  }
}

// 🔍 Validate Electron app directory
function validateElectronApp(copiedFiles) {
  console.log('🔍 Validating Electron app setup...');
  
  // Check for critical files
  const criticalFiles = ['index.html'];
  const criticalDirs = ['_next'];
  
  const missingCritical = [];
  
  for (const file of criticalFiles) {
    if (!copiedFiles.includes(file)) {
      missingCritical.push(`file: ${file}`);
    }
  }
  
  for (const dir of criticalDirs) {
    if (!copiedFiles.includes(dir)) {
      missingCritical.push(`directory: ${dir}`);
    }
  }
  
  if (missingCritical.length > 0) {
    throw new Error(`❌ Critical files/directories missing in Electron app: ${missingCritical.join(', ')}`);
  }
  
  // Validate index.html content
  const indexPath = path.join(electronAppDir, 'index.html');
  const indexContent = fs.readFileSync(indexPath, 'utf8');
  
  if (!indexContent.includes('_next/static')) {
    console.warn('⚠️  index.html may not reference static assets correctly');
  }
  
  if (indexContent.length < 100) {
    console.warn('⚠️  index.html seems unusually small');
  }
  
  console.log('✅ Electron app validation passed');
}

// 🚀 Generate electron-serve compatible structure
function optimizeForElectronServe() {
  console.log('🚀 Optimizing for electron-serve...');
  
  try {
    // Ensure all assets use relative paths for electron-serve
    // electron-serve will handle the fallback to index.html automatically
    
    console.log('✅ Electron-serve optimization completed');
  } catch (error) {
    console.warn(`⚠️  Electron-serve optimization failed: ${error.message}`);
  }
}

// 📊 Generate build report
function generateBuildReport(htmlFiles, copiedFiles) {
  console.log('\n📊 Electron Static Build Report:');
  console.log('══════════════════════════════════');
  console.log(`📄 HTML Pages Exported: ${htmlFiles.length}`);
  console.log(`📁 Total Files Copied: ${copiedFiles.length}`);
  console.log(`📂 Source: ${path.relative(process.cwd(), outDir)}`);
  console.log(`📂 Target: ${path.relative(process.cwd(), electronAppDir)}`);
  
  // Calculate directory size
  function getDirectorySize(dirPath) {
    let totalSize = 0;
    function calculateSize(dir) {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stats = fs.statSync(itemPath);
        if (stats.isDirectory()) {
          calculateSize(itemPath);
        } else {
          totalSize += stats.size;
        }
      }
    }
    calculateSize(dirPath);
    return totalSize;
  }
  
  const appSize = getDirectorySize(electronAppDir);
  const appSizeMB = (appSize / 1024 / 1024).toFixed(2);
  console.log(`💾 App Directory Size: ${appSizeMB} MB`);
  
  console.log('\n🎯 Key Files:');
  htmlFiles.slice(0, 10).forEach(file => console.log(`  ✓ ${file}`));
  if (htmlFiles.length > 10) {
    console.log(`  ... and ${htmlFiles.length - 10} more pages`);
  }
  
  console.log('\n🚀 Ready for Electron packaging!');
  console.log('💡 Next steps:');
  console.log('  1. cd electron');
  console.log('  2. npm run electron:start (test the app)');
  console.log('  3. npm run electron:build (create distributable)');
}

// 🏃‍♂️ Main execution
try {
  console.log('🎬 Starting Electron static copy process...\n');
  
  // Step 1: Validate the static export exists and is complete
  const htmlFiles = validateStaticExport();
  
  // Step 2: Prepare clean Electron app directory
  prepareElectronAppDir();
  
  // Step 3: Copy the static export
  const copiedFiles = copyStaticExport();
  
  // Step 4: Validate the Electron app setup
  validateElectronApp(copiedFiles);
  
  // Step 5: Optimize for electron-serve
  optimizeForElectronServe();
  
  // Step 6: Generate comprehensive build report
  generateBuildReport(htmlFiles, copiedFiles);
  
  console.log('\n🎉 Electron static copy process completed successfully!');
  
} catch (error) {
  console.error('\n❌ Electron static copy process failed:');
  console.error(`💥 Error: ${error.message}`);
  
  // Provide helpful troubleshooting tips
  console.error('\n💡 Troubleshooting tips:');
  console.error('  1. Ensure Next.js build completed successfully with output: "export"');
  console.error('  2. Check that BUILD_TARGET=electron was set during build');
  console.error('  3. Verify all pages use "use client" or are statically exportable');
  console.error('  4. Check next.config.ts for proper static export configuration');
  
  process.exit(1);
}