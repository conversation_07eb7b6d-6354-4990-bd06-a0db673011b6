#!/usr/bin/env node

/**
 * 🗄️ CouchDB Preparation Script
 * 
 * Ensures CouchDB binaries are ready for Electron packaging
 * Uses existing binaries from electron/resources directory
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const RESOURCES_DIR = path.join(__dirname, '..', 'electron', 'resources');

function log(message, color = '\x1b[36m') {
  console.log(`${color}🗄️ [CouchDB Prep] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [CouchDB Prep] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [CouchDB Prep] ${message}\x1b[0m`);
}

function logWarning(message) {
  console.log(`\x1b[33m⚠️ [CouchDB Prep] ${message}\x1b[0m`);
}

async function prepareCouchDB() {
  log('Checking CouchDB binaries for Electron...');
  
  // Ensure resources directory exists
  if (!fs.existsSync(RESOURCES_DIR)) {
    fs.mkdirSync(RESOURCES_DIR, { recursive: true });
    log(`Created resources directory: ${RESOURCES_DIR}`);
  }
  
  const platform = process.platform;
  let targetDir, binaryName;
  
  if (platform === 'darwin') {
    targetDir = path.join(RESOURCES_DIR, 'couchdb-macos');
    binaryName = 'couchdb';
  } else if (platform === 'win32') {
    targetDir = path.join(RESOURCES_DIR, 'couchdb-windows');
    binaryName = 'couchdb.cmd';
  } else {
    targetDir = path.join(RESOURCES_DIR, 'couchdb-linux');
    binaryName = 'couchdb';
  }
  
  // Check if CouchDB binaries exist
  if (fs.existsSync(targetDir)) {
    const binDir = path.join(targetDir, 'bin');
    const couchdbBin = path.join(binDir, binaryName);
    
    if (fs.existsSync(couchdbBin)) {
      logSuccess(`CouchDB binary found: ${couchdbBin}`);
      
      // Fix permissions on macOS/Linux
      if (platform !== 'win32') {
        try {
          execSync(`chmod +x "${couchdbBin}"`);
          logSuccess('CouchDB binary permissions fixed');
        } catch (e) {
          logWarning('Could not fix permissions (might be on read-only volume)');
        }
      }
      
      // List contents for debugging
      log('CouchDB directory structure:');
      try {
        const contents = execSync(`find "${targetDir}" -type f -name "*couchdb*" | head -10`, { encoding: 'utf8' });
        console.log(contents);
      } catch (e) {
        // Ignore find errors
      }
      
      return true;
    } else {
      logError(`CouchDB binary not found at: ${couchdbBin}`);
      logError(`Directory exists but binary is missing!`);
      return false;
    }
  } else {
    logError(`CouchDB directory not found: ${targetDir}`);
    logError('');
    logError('🔧 SETUP REQUIRED:');
    logError('');
    
    if (platform === 'darwin') {
      logError('For macOS, you need to:');
      logError('1. Run: ./bundle-couchdb-macos.sh');
      logError('   OR');
      logError('2. Download CouchDB from: https://couchdb.apache.org/');
      logError('3. Extract to: electron/resources/couchdb-macos/');
      logError('');
      logError('The directory should contain:');
      logError('  electron/resources/couchdb-macos/bin/couchdb');
      logError('  electron/resources/couchdb-macos/lib/');
      logError('  electron/resources/couchdb-macos/etc/');
    } else if (platform === 'win32') {
      logError('For Windows, you need to:');
      logError('1. Download CouchDB from: https://couchdb.apache.org/');
      logError('2. Extract to: electron/resources/couchdb-windows/');
      logError('');
      logError('The directory should contain:');
      logError('  electron/resources/couchdb-windows/bin/couchdb.cmd');
    } else {
      logError('For Linux, you need to:');
      logError('1. Install CouchDB and copy binaries to: electron/resources/couchdb-linux/');
    }
    
    return false;
  }
}

async function main() {
  try {
    const success = await prepareCouchDB();
    if (success) {
      logSuccess('CouchDB binaries are ready for Electron packaging! 🚀');
      logSuccess('The Electron app will now be able to launch CouchDB automatically.');
    } else {
      logError('CouchDB preparation failed!');
      logError('Please follow the setup instructions above.');
      process.exit(1);
    }
  } catch (error) {
    logError(`CouchDB preparation failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { prepareCouchDB }; 