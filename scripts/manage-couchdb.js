#!/usr/bin/env node

/**
 * 🗄️ CouchDB Management Script
 * 
 * Helps manage the persistent CouchDB instance for Electron static releases
 */

const { spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

function log(message, color = '\x1b[36m') {
  console.log(`${color}🗄️ [CouchDB Manager] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [CouchDB Manager] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [CouchDB Manager] ${message}\x1b[0m`);
}

function logWarning(message) {
  console.log(`\x1b[33m⚠️ [CouchDB Manager] ${message}\x1b[0m`);
}

async function findCouchDBProcesses() {
  try {
    let processes = [];
    
    if (process.platform === 'win32') {
      // Windows - CouchDB runs as beam.smp.exe (Erlang VM), not couchdb.exe
      const output = execSync('tasklist /FI "IMAGENAME eq beam.smp.exe" /FO CSV', { encoding: 'utf8' });
      const lines = output.split('\n').slice(1); // Skip header
      processes = lines.filter(line => line.includes('beam.smp.exe')).map(line => {
        const parts = line.split(',');
        return {
          pid: parts[1]?.replace(/"/g, ''),
          name: parts[0]?.replace(/"/g, '')
        };
      });
    } else {
      // macOS/Linux
      const output = execSync('ps aux | grep couchdb | grep -v grep', { encoding: 'utf8' });
      const lines = output.split('\n').filter(line => line.trim());
      processes = lines.map(line => {
        const parts = line.trim().split(/\s+/);
        return {
          pid: parts[1],
          name: 'couchdb',
          command: parts.slice(10).join(' ')
        };
      });
    }
    
    return processes;
  } catch (error) {
    // No processes found or command failed
    return [];
  }
}

async function findCouchDBPorts() {
  try {
    // Check multiple possible locations for the URI file
    const possiblePaths = [
      path.join(os.homedir(), 'Library', 'Application Support', 'bistro', 'pouchdb-data'),
      path.join(__dirname, '..', 'electron', 'pouchdb-data'),
      path.join(process.cwd(), 'electron', 'pouchdb-data')
    ];
    
    let userDataPath = null;
    let uriFile = null;
    
    for (const testPath of possiblePaths) {
      const testUriFile = path.join(testPath, 'couchdb-config', 'couch.uri');
      if (fs.existsSync(testUriFile)) {
        userDataPath = testPath;
        uriFile = testUriFile;
        break;
      }
    }
    
    if (!uriFile) {
      return [];
    }
    
    if (fs.existsSync(uriFile)) {
      const uriContent = fs.readFileSync(uriFile, 'utf8').trim();
      const portMatch = uriContent.match(/:(\d+)\/$/);
      if (portMatch) {
        const port = parseInt(portMatch[1]);
        
        // Test if it's responsive
        try {
          const response = await fetch(`http://localhost:${port}/`);
          if (response.ok) {
            const data = await response.json();
            return [{
              port,
              status: 'running',
              version: data.version,
              couchdb: data.couchdb
            }];
          }
        } catch (error) {
          return [{
            port,
            status: 'not_responsive',
            error: error.message
          }];
        }
      }
    }
    
    return [];
  } catch (error) {
    return [];
  }
}

async function stopCouchDB() {
  log('Stopping CouchDB processes...');
  
  const processes = await findCouchDBProcesses();
  if (processes.length === 0) {
    logWarning('No CouchDB processes found');
    return true;
  }
  
  let stopped = 0;
  for (const proc of processes) {
    try {
      if (process.platform === 'win32') {
        execSync(`taskkill /PID ${proc.pid} /F`, { stdio: 'ignore' });
      } else {
        execSync(`kill -TERM ${proc.pid}`, { stdio: 'ignore' });
      }
      logSuccess(`Stopped CouchDB process ${proc.pid}`);
      stopped++;
    } catch (error) {
      logError(`Failed to stop process ${proc.pid}: ${error.message}`);
    }
  }
  
  if (stopped > 0) {
    logSuccess(`Stopped ${stopped} CouchDB process(es)`);
    return true;
  } else {
    logError('Failed to stop any CouchDB processes');
    return false;
  }
}

async function showStatus() {
  log('Checking CouchDB status...');
  
  const processes = await findCouchDBProcesses();
  const ports = await findCouchDBPorts();
  
  console.log('\n📊 CouchDB Status Report:');
  console.log('========================');
  
  if (processes.length > 0) {
    console.log(`\n🔄 Running Processes: ${processes.length}`);
    processes.forEach((proc, i) => {
      console.log(`  ${i + 1}. PID: ${proc.pid} - ${proc.name}`);
      if (proc.command) {
        console.log(`     Command: ${proc.command.substring(0, 80)}...`);
      }
    });
  } else {
    console.log('\n❌ No CouchDB processes found');
  }
  
  if (ports.length > 0) {
    console.log(`\n🌐 Active Ports: ${ports.length}`);
    ports.forEach((port, i) => {
      console.log(`  ${i + 1}. Port: ${port.port} - Status: ${port.status}`);
      if (port.version) {
        console.log(`     Version: ${port.couchdb} ${port.version}`);
        console.log(`     URL: http://localhost:${port.port}/`);
        console.log(`     Admin: ****************************:${port.port}/_utils/`);
      }
      if (port.error) {
        console.log(`     Error: ${port.error}`);
      }
    });
  } else {
    console.log('\n❌ No active CouchDB ports found');
  }
  
  console.log('\n💡 Tips:');
  console.log('  - Use "npm run couchdb:stop" to stop all CouchDB processes');
  console.log('  - Use "npm run couchdb:status" to check status again');
  console.log('  - CouchDB data is stored in your user data directory');
  console.log('');
}

async function cleanupData() {
  log('Cleaning up CouchDB data...');
  
  // First stop CouchDB
  await stopCouchDB();
  
  try {
    const userDataPath = path.join(os.homedir(), 'Library', 'Application Support', 'bistro', 'pouchdb-data');
    
    if (fs.existsSync(userDataPath)) {
      // Remove lock files and logs
      const filesToClean = [
        'couchdb-stderr.log',
        'couchdb-stdout.log',
        'couchdb.log'
      ];
      
      for (const file of filesToClean) {
        const filePath = path.join(userDataPath, file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          logSuccess(`Removed ${file}`);
        }
      }
      
      // Clean config directory
      const configPath = path.join(userDataPath, 'couchdb-config');
      if (fs.existsSync(configPath)) {
        const configFiles = fs.readdirSync(configPath);
        for (const file of configFiles) {
          if (file.endsWith('.pid') || file === 'couch.uri') {
            fs.unlinkSync(path.join(configPath, file));
            logSuccess(`Removed config file ${file}`);
          }
        }
      }
      
      logSuccess('CouchDB cleanup completed');
    } else {
      logWarning('No CouchDB data directory found');
    }
  } catch (error) {
    logError(`Cleanup failed: ${error.message}`);
  }
}

async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'status':
      await showStatus();
      break;
      
    case 'stop':
      await stopCouchDB();
      break;
      
    case 'cleanup':
      await cleanupData();
      break;
      
    default:
      console.log('🗄️ CouchDB Management Script');
      console.log('');
      console.log('Usage:');
      console.log('  node scripts/manage-couchdb.js status   - Show CouchDB status');
      console.log('  node scripts/manage-couchdb.js stop     - Stop all CouchDB processes');
      console.log('  node scripts/manage-couchdb.js cleanup  - Stop and clean up data files');
      console.log('');
      console.log('NPM Scripts:');
      console.log('  npm run couchdb:status   - Show status');
      console.log('  npm run couchdb:stop     - Stop CouchDB');
      console.log('  npm run couchdb:cleanup  - Full cleanup');
      break;
  }
}

if (require.main === module) {
  main().catch(error => {
    logError(`Script failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { findCouchDBProcesses, findCouchDBPorts, stopCouchDB, showStatus, cleanupData };