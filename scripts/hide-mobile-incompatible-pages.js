#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Pages that should be excluded from mobile builds due to dependencies
const MOBILE_INCOMPATIBLE_PAGES = [
  'app/(protected)/analytics',
  'app/(protected)/debug/caisse-test',
  'app/(protected)/debug/permissions',
  'app/(protected)/payment-history-demo'
];

function hidePages() {
  console.log('🚫 Hiding mobile-incompatible pages...');
  
  for (const pagePath of MOBILE_INCOMPATIBLE_PAGES) {
    const fullPath = path.join(__dirname, '..', pagePath);
    const hiddenPath = fullPath + '_mobile_hidden';
    
    if (fs.existsSync(fullPath)) {
      console.log(`  Hiding: ${pagePath}`);
      fs.renameSync(fullPath, hiddenPath);
    }
  }
  
  console.log('✅ Mobile-incompatible pages hidden');
}

function restorePages() {
  console.log('🔄 Restoring mobile-incompatible pages...');
  
  for (const pagePath of MOBILE_INCOMPATIBLE_PAGES) {
    const fullPath = path.join(__dirname, '..', pagePath);
    const hiddenPath = fullPath + '_mobile_hidden';
    
    if (fs.existsSync(hiddenPath)) {
      console.log(`  Restoring: ${pagePath}`);
      fs.renameSync(hiddenPath, fullPath);
    }
  }
  
  console.log('✅ Mobile-incompatible pages restored');
}

// Command line interface
const command = process.argv[2];

if (command === 'hide') {
  hidePages();
} else if (command === 'restore') {
  restorePages();
} else {
  console.log('Usage: node hide-mobile-incompatible-pages.js [hide|restore]');
  process.exit(1);
}