# Sync Troubleshooting Guide

## Common Issues When Sync Doesn't Work Despite Finding Device IP

### 1. **Restaurant ID Validation Blocking Sync**
**Symptoms:** Devices found, but no sync connections established
**Cause:** The restaurant validation system is preventing sync to unverified peers

**Check:**
```javascript
// In browser console
localStorage.getItem('restaurantId')
// Should return a valid restaurant ID
```

**Fix:**
- Ensure you're logged in with a valid restaurant account
- Check that `restaurantId` is set in localStorage
- Verify the discovered server belongs to the same restaurant

### 2. **Database Naming Convention Mismatch**
**Symptoms:** Server reachable but databases not found
**Cause:** Database names don't match expected pattern

**Expected Pattern:**
```
resto-{cleanRestaurantId}-{dbName}
```

**Example:**
- Restaurant ID: `rest123`
- Database: `orders`
- Expected name: `resto-rest123-orders`

**Fix:**
- Create databases with correct naming on CouchDB server
- Or update restaurant ID to match existing databases

### 3. **CORS Configuration Issues**
**Symptoms:** Network errors in browser console
**Cause:** CouchDB not configured to allow browser requests

**Fix CouchDB CORS:**
```bash
# Enable CORS on CouchDB
curl -X PUT *********************************/_config/httpd/enable_cors -d '"true"'
curl -X PUT *********************************/_config/cors/origins -d '"*"'
curl -X PUT *********************************/_config/cors/credentials -d '"true"'
curl -X PUT *********************************/_config/cors/methods -d '"GET, PUT, POST, HEAD, DELETE"'
curl -X PUT *********************************/_config/cors/headers -d '"accept, authorization, content-type, origin, referer, x-csrf-token"'
```

### 4. **PouchDB Instance Not Ready**
**Symptoms:** "PouchDB not ready" errors
**Cause:** Database initialization failed

**Check:**
```javascript
// In browser console
window.indexedDB // Should exist
// Check if databases are created in IndexedDB
```

**Fix:**
- Refresh the page
- Clear browser storage and reinitialize
- Check browser IndexedDB support

### 5. **Authentication Issues**
**Symptoms:** 401/403 errors when connecting to CouchDB
**Cause:** CouchDB requires authentication but credentials not provided

**Fix:**
- Configure CouchDB to allow anonymous access for testing
- Or implement proper authentication in sync bridge

### 6. **Network Discovery vs Sync Connection Gap**
**Symptoms:** Devices discovered but `establishConnection` never called
**Cause:** Discovery finds devices but doesn't trigger sync establishment

**Debug:**
```javascript
// Check if peers are marked as verified
autonomousSync.discoveredPeers.forEach(peer => {
  console.log(`Peer ${peer.ip}:${peer.port} verified: ${peer.verified}`);
});
```

**Fix:**
- Ensure discovered devices are properly converted to peers
- Check if restaurant validation is blocking unverified peers

### 7. **Sync Bridge Configuration**
**Symptoms:** Connection established but no actual sync
**Cause:** Sync bridge not properly configured

**Check:**
```javascript
// Verify sync bridge configuration
console.log(autonomousSync.syncBridge.config);
```

**Common Issues:**
- `autoSyncDatabases` doesn't include required databases
- `syncDirection` set incorrectly
- Timeout values too low

### 8. **Mobile App Specific Issues**
**Symptoms:** Works in browser but not in mobile app
**Cause:** Capacitor HTTP plugin or network security

**Fix:**
- Check Capacitor HTTP plugin configuration
- Verify network security config allows HTTP connections
- Test with HTTPS if possible

## Diagnostic Steps

### Step 1: Verify Discovery
```bash
# Check if CouchDB is running
curl http://*************:5984/
# Should return CouchDB info
```

### Step 2: Test Database Access
```bash
# Test database existence
curl http://*************:5984/resto-yourrestaurantid-orders
# Should return database info or 404 if doesn't exist
```

### Step 3: Test CORS
```bash
# Test CORS headers
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS \
     http://*************:5984/
```

### Step 4: Check Browser Console
Look for these error patterns:
- `CORS policy` - CORS issue
- `401 Unauthorized` - Authentication issue
- `404 Not Found` - Database doesn't exist
- `Restaurant validation failed` - Restaurant ID issue

### Step 5: Use Debug Tools
1. Open P2P Debug page
2. Go to "Troubleshooter" tab
3. Run full diagnostic
4. Check "Diagnostics" tab for detailed results

## Quick Fixes

### Enable Anonymous Access (Testing Only)
```bash
# Allow anonymous access to CouchDB
curl -X PUT *********************************/_config/chttpd/require_valid_user -d '"false"'
```

### Create Missing Databases
```bash
# Create required databases
curl -X PUT *********************************/resto-yourrestaurantid-orders
curl -X PUT *********************************/resto-yourrestaurantid-staff
curl -X PUT *********************************/resto-yourrestaurantid-inventory
curl -X PUT *********************************/resto-yourrestaurantid-settings
```

### Reset Local State
```javascript
// Clear all local data and restart
localStorage.clear();
// Refresh page
location.reload();
```

## Monitoring Sync Health

Use the enhanced P2P Debug page:
1. **Discovery Tab**: Check if servers are found
2. **Database Sync Tab**: Monitor active connections
3. **Diagnostics Tab**: Run connection tests
4. **Troubleshooter Tab**: Automated problem detection
5. **Monitoring Tab**: Real-time health metrics

## Getting Help

If sync still doesn't work:
1. Run the troubleshooter and note which step fails
2. Check browser console for specific error messages
3. Verify CouchDB server logs
4. Test with a simple curl command first
5. Check network connectivity between devices

The most common issue is CORS configuration - ensure CouchDB allows browser requests from your app's origin.
