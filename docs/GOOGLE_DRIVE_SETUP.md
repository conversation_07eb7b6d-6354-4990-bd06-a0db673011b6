# 📁 Google Drive Setup Guide

## Overview

This application now uses Google Drive for image storage instead of R2. Google Drive offers:
- ✅ **Free storage** (15GB with any Google account)
- ✅ **Easy setup** with OAuth or Service Account  
- ✅ **Global accessibility** - your images are accessible anywhere
- ✅ **Built-in backup** - Google's reliability
- ✅ **No monthly costs** for most use cases

## Setup Options

Choose one of these two authentication methods:

### 🔗 Option 1: OAuth (Recommended for beginners)
- ✅ **Easier to set up** - just need Google account
- ✅ **No technical JSON files** to manage
- ⚠️ **Requires user interaction** during initial setup
- 👍 **Best for**: Small restaurants, personal use

### 🔒 Option 2: Service Account (Recommended for production)
- ✅ **No user interaction** required after setup
- ✅ **More secure** for server applications
- ✅ **Better for automation**
- ⚠️ **More complex setup** with JSON key files
- 👍 **Best for**: Production deployments, multiple locations

## Step-by-Step Setup

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. **Create a new project** or select existing one
3. **Enable the Google Drive API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Drive API"
   - Click "Enable"

### 2A. OAuth Setup (Easy Option)

1. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client ID"
   - Choose "Web application"
   - Add authorized redirect URI: `http://localhost:3000/api/auth/callback/google`

2. **Get your credentials**:
   - Copy **Client ID** and **Client Secret**
   - You'll need these in the app settings

3. **Get Refresh Token**:
   - Use [OAuth 2.0 Playground](https://developers.google.com/oauthplayground/)
   - Configure it with your Client ID/Secret
   - Authorize Google Drive API v3
   - Exchange authorization code for tokens
   - Copy the **refresh_token**

### 2B. Service Account Setup (Advanced Option)

1. **Create Service Account**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "Service Account"
   - Fill in the details and create

2. **Generate Private Key**:
   - Click on your service account
   - Go to "Keys" tab
   - Click "Add Key" > "Create new key" > "JSON"
   - Download the JSON file

3. **Extract Credentials**:
   - Open the JSON file
   - Copy `client_email` and `private_key`
   - Keep the `project_id` for reference

## 3. Configure in Your App

1. **Open your restaurant app**
2. **Go to Settings** > **Cloud Storage** tab
3. **Choose your authentication method**:

### For OAuth:
- Paste your **Client ID**
- Paste your **Client Secret** 
- Paste your **Refresh Token**
- Click "Connect with OAuth"

### For Service Account:
- Paste your **Service Account Email**
- Paste your **Private Key** (entire content)
- Add **Project ID** (optional)
- Click "Connect with Service Account"

4. **Test the connection** - should show green ✅

## 🎯 Usage

Once configured, the app will automatically:
- ✅ **Upload receipt images** to Google Drive
- ✅ **Create restaurant-specific folders** for organization  
- ✅ **Generate download URLs** for viewing images
- ✅ **Handle offline mode** gracefully
- ✅ **Show upload progress** during uploads

## 📁 File Organization

Your Google Drive will have this structure:
```
📁 Restaurant_[YOUR_ID]_Images/
  📄 receipt_purchase123_timestamp_random.webp
  📄 receipt_purchase456_timestamp_random.webp
  📄 upload_timestamp_random.webp
```

## 🔧 Troubleshooting

### "Google Drive service not initialized"
- ❌ Check your credentials in Settings > Cloud Storage
- ❌ Ensure Google Drive API is enabled in Google Cloud Console
- ❌ Test the connection using the "Test" button

### "Failed to upload file"
- ❌ Check internet connection
- ❌ Verify Google Drive has enough space (15GB free limit)
- ❌ Ensure file is under 10MB (app limit)

### "Image not loading"
- ❌ Check browser console for 🖼️ debug logs
- ❌ Verify the file still exists in Google Drive
- ❌ Test the connection in settings

### OAuth Issues
- ❌ Check redirect URI matches exactly: `http://localhost:3000/api/auth/callback/google`
- ❌ Ensure OAuth consent screen is configured
- ❌ Verify Google Drive API scope is included

### Service Account Issues  
- ❌ Ensure JSON key file has correct permissions
- ❌ Check service account has Google Drive API access
- ❌ Verify private key format (includes BEGIN/END lines)

## 🔄 Migration from R2

If you were previously using R2 storage:

1. ✅ **Set up Google Drive** following this guide
2. ✅ **Test upload/download** with new receipts
3. ✅ **Old R2 images** will show placeholders until re-uploaded
4. ✅ **No data loss** - just re-upload important receipts

## 💡 Tips

- 🎯 **Free quota**: 15GB covers thousands of receipt images
- 🎯 **Mobile apps**: Work great with Google Drive
- 🎯 **Offline mode**: Images show placeholders when offline
- 🎯 **Multiple restaurants**: Each gets its own folder
- 🎯 **Backup**: Your images are safe in Google Drive

## 🆘 Getting Help

If you run into issues:
1. Check the "Test" button in Settings > Cloud Storage
2. Look for 🖼️ debug logs in browser console
3. Verify your Google Cloud Console setup
4. Ensure API quotas aren't exceeded

---

📧 **Need help?** Check your browser console for detailed error messages starting with 🖼️ or ❌. 