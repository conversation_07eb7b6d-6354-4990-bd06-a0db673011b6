# 🔄 Electron Auto-Update System Guide

## Overview

This restaurant management app uses **electron-updater** with a custom **Shadcn UI** interface for seamless auto-updates in production. The system supports both **full downloads** and **efficient blockmap partial updates**.

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Update Server (R2/CDN)                  │
├─────────────────────────────────────────────────────────────┤
│  📁 /updates/                                              │
│  ├── latest.yml              (Windows update metadata)     │
│  ├── latest-mac.yml          (macOS update metadata)       │
│  ├── restaurant-app-1.2.0.exe                             │
│  ├── restaurant-app-1.2.0.exe.blockmap                    │
│  ├── restaurant-app-1.2.0.dmg                             │
│  └── restaurant-app-1.2.0.dmg.blockmap                    │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    Electron App                            │
├─────────────────────────────────────────────────────────────┤
│  🔍 Check: latest.yml every 30 minutes                    │
│  📥 Download: .blockmap + partial chunks                   │
│  🎨 UI: Custom Shadcn dialog (replaces native)            │
│  🔄 Install: quitAndInstall() on user confirmation         │
└─────────────────────────────────────────────────────────────┘
```

## 📂 R2 Deployment Structure

### Required Files in R2 Bucket

```
your-r2-bucket/
├── api/
│   └── updates/
│       └── check/
│           ├── latest.yml                    # Windows metadata
│           ├── latest-mac.yml                # macOS metadata  
│           ├── restaurant-app-1.2.0.exe      # Windows installer
│           ├── restaurant-app-1.2.0.exe.blockmap  # Windows blockmap
│           ├── restaurant-app-1.2.0.dmg      # macOS installer
│           └── restaurant-app-1.2.0.dmg.blockmap  # macOS blockmap
```

### latest.yml Structure (Windows)

```yaml
version: 1.2.0
files:
  - url: restaurant-app-1.2.0.exe
    sha512: abc123...
    size: 85234567
    blockMapSize: 12345
path: restaurant-app-1.2.0.exe
sha512: abc123...
releaseDate: '2024-01-15T10:30:00.000Z'
releaseName: 'Version 1.2.0'
releaseNotes: |
  🎉 New Features:
  - Enhanced order management
  - Improved kitchen display
  - Better offline sync
  
  🐛 Bug Fixes:
  - Fixed printer connectivity
  - Resolved sync conflicts
```

### latest-mac.yml Structure (macOS)

```yaml
version: 1.2.0
files:
  - url: restaurant-app-1.2.0.dmg
    sha512: def456...
    size: 92345678
    blockMapSize: 15678
path: restaurant-app-1.2.0.dmg
sha512: def456...
releaseDate: '2024-01-15T10:30:00.000Z'
releaseName: 'Version 1.2.0'
releaseNotes: |
  🎉 New Features:
  - Enhanced order management
  - Improved kitchen display
  - Better offline sync
```

## 🔧 Blockmap vs Full Updates

### Blockmap Partial Updates (Recommended)

**How it works:**
1. App downloads `.blockmap` file (small, ~10-50KB)
2. Compares local vs remote file blocks
3. Downloads only **changed blocks** (not entire file)
4. Reconstructs new version locally

**Benefits:**
- ⚡ **90% faster** downloads
- 📱 **Bandwidth efficient** (mobile-friendly)
- 🔄 **Resumable** downloads
- 💰 **Lower CDN costs**

**Example:**
```
App v1.1.0 → v1.2.0
Full download: 85MB
Blockmap update: 12MB (only changed code)
```

### Full .exe/.dmg Updates (Fallback)

**When used:**
- First-time installation
- Blockmap corruption/failure
- Major version changes
- User manually downloads

## 🎨 Custom Shadcn UI Implementation

### Features
- 🎯 **Modern Design**: Consistent with app's UI
- 📊 **Progress Tracking**: Real-time download progress
- 🔔 **Smart Notifications**: Toast messages
- ⚡ **Non-blocking**: Continue working while downloading
- 🎮 **User Control**: Install now or later options

### Components Created

1. **`UpdateDialog`** (`/app/components/ui/update-dialog.tsx`)
   - Main update dialog with progress tracking
   - Handles all update states (checking, downloading, ready)
   - Beautiful progress bars and status indicators

2. **`UpdateManager`** (`/app/components/update-manager.tsx`)
   - Global update state management
   - IPC communication with Electron main process
   - Automatic integration with app layout

3. **IPC Communication** (Enhanced preload.ts)
   - `onUpdateAvailable()` - Listen for new updates
   - `onDownloadProgress()` - Track download progress
   - `onUpdateDownloaded()` - Update ready notification
   - `quitAndInstall()` - Apply update and restart

## 🚀 Deployment Process

### 1. Build & Package

```bash
# Build for Windows
npm run electron:build:win:publish

# Build for macOS  
npm run publish:mac
```

### 2. Upload to R2

The build scripts automatically:
1. Generate `.exe`/`.dmg` installers
2. Create `.blockmap` files
3. Generate `latest.yml`/`latest-mac.yml`
4. Copy files to `../public/updates/`

### 3. Deploy to R2

```bash
# Upload to R2 bucket
aws s3 sync ./public/updates/ s3://your-r2-bucket/api/updates/check/ \
  --endpoint-url https://your-account-id.r2.cloudflarestorage.com
```

### 4. Update Server Configuration

Ensure your update server URL in `electron/package.json` points to:

```json
{
  "build": {
    "publish": {
      "provider": "generic",
      "url": "https://your-domain.com/api/updates/check"
    }
  }
}
```

## 🔒 Security Features

### Code Signing
- **Windows**: Authenticode signing with certificate
- **macOS**: Apple Developer ID signing
- **Verification**: electron-updater validates signatures

### Integrity Checks
- **SHA512 hashes** in metadata files
- **Blockmap verification** for partial updates
- **HTTPS-only** download URLs
- **Signature validation** before installation

## 🐛 Troubleshooting

### Common Issues

1. **Update not detected**
   - Check `latest.yml` accessibility
   - Verify version number format
   - Ensure HTTPS and CORS headers

2. **Download fails**
   - Check file permissions in R2
   - Verify blockmap file exists
   - Test direct file download URLs

3. **Installation fails**
   - Check code signing certificates
   - Verify file integrity (SHA512)
   - Review Electron logs

### Debug Mode

```javascript
// Enable debug logging
process.env.ELECTRON_UPDATER_DEBUG = '1'

// Force update check
autoUpdater.checkForUpdatesAndNotify()
```

### Testing Updates

```javascript
// Force dev mode updates (testing)
process.env.ELECTRON_FORCE_UPDATE = 'true'

// Mock update server
process.env.ELECTRON_UPDATE_URL = 'http://localhost:3000/updates'
```

## 📊 Update Flow States

```mermaid
stateDiagram-v2
    [*] --> Checking
    Checking --> Available: New version found
    Checking --> UpToDate: No updates
    Available --> Downloading: Auto-download starts
    Downloading --> Downloaded: Download complete
    Downloaded --> Installing: User clicks "Restart Now"
    Installing --> [*]: App restarts
    
    Available --> [*]: User dismisses
    Downloaded --> [*]: User clicks "Later"
    
    Checking --> Error: Network/server error
    Downloading --> Error: Download fails
    Error --> [*]: User acknowledges
```

## 🎯 Best Practices

### For Developers
1. **Test updates** in staging environment first
2. **Gradual rollouts** using version targeting
3. **Monitor download success** rates
4. **Keep release notes** informative and concise
5. **Sign all releases** for security

### For Users
1. **Stable internet** recommended for updates
2. **Save work** before installing updates
3. **Allow app restart** for update completion
4. **Report issues** if updates fail

## 🔗 Related Files

- `electron/src/index.ts` - Main process auto-updater logic
- `electron/src/preload.ts` - IPC communication setup
- `electron/package.json` - Build and publish configuration
- `app/components/ui/update-dialog.tsx` - Custom update UI
- `app/components/update-manager.tsx` - Update state management
- `components/layout-client-wrapper.tsx` - Integration point

---

**🎉 Result**: Users get seamless, fast, and secure auto-updates with a beautiful UI that matches your app's design system!