# 🔒 Secure Build Guide: Preventing Source Code Leakage

## Background

Turbopack currently always builds production sourcemaps for the browser, which can include your project source code if deployed to production. This is a security risk as it exposes your application's source code to anyone who knows how to use browser developer tools.

## 🛡️ Safe Build Options

We've implemented several solutions to prevent source code leakage:

### 1. Use Secure Build Scripts

We've added several npm scripts to build the application securely:

```bash
# Option 1: Build with sourcemaps disabled
npm run build:safe

# Option 2: Production build with sourcemaps disabled
npm run build:prod

# Option 3: Comprehensive secure build (recommended)
npm run build:secure

# Option 4: Build and then clean sourcemaps
npm run build:clean
```

### 2. Clean Existing Sourcemaps

If you've already built the application, you can clean existing sourcemaps:

```bash
npm run clean:sourcemaps
```

### 3. Docker Build

Our Dockerfile has been updated to use the secure build process. When building with Dock<PERSON>, sourcemaps will be automatically disabled.

## 📋 Implementation Details

1. **next.config.ts**: Added `productionBrowserSourceMaps: false` and modified webpack config to disable sourcemaps
2. **package.json**: Added various build scripts with environment variables to disable sourcemaps
3. **.gitignore**: Updated to prevent sourcemap files from being committed
4. **scripts/safe-build.sh**: Shell script that cleans `.next` directory and builds without sourcemaps
5. **scripts/clean-sourcemaps.js**: Node.js script to remove sourcemap files after build
6. **turbopack.config.js**: Configuration to disable sourcemaps in Turbopack
7. **Dockerfile**: Updated to use secure build process

## ✅ Best Practices

1. Always use one of the secure build scripts for production builds
2. Periodically check the `.next` directory for `.map` files
3. If deploying to production, run the clean script as part of your CI/CD pipeline

## 🔍 Verification

To verify that your build doesn't contain sourcemaps:

```bash
# Count sourcemap files in the build directory
find .next -name "*.map" | wc -l
```

The result should be 0. If not, run the clean script:

```bash
npm run clean:sourcemaps
``` 