# Technology Stack & Build System

## Core Technologies

### Frontend
- **Framework**: Next.js 15+ with App Router
- **UI Library**: Shadcn UI with Radix UI components
- **Styling**: Tailwind CSS with OKLCH color format
- **State Management**: React Hook Form + Zod validation
- **Icons**: Lucide React

### Backend & Database
- **Database**: PouchDB-CouchDB (offline-first)
- **Authentication**: JWT with bcryptjs
- **API**: Next.js API routes (REST)
- **File Storage**: AWS S3/R2 integration

### Multi-Platform
- **Desktop**: Electron with auto-updater
- **Mobile**: Capacitor (iOS/Android)
- **Web**: Static export for landing page only

### Key Libraries
- **Charts**: Recharts
- **Printing**: node-thermal-printer, canvas
- **Networking**: Socket.io, simple-peer (P2P sync)
- **Hardware**: USB detection, barcode scanning
- **Utilities**: date-fns, uuid, jose

## Build System

### Development Commands
```bash
npm run dev                    # Start development server
npm run electron:dev          # Start Electron development
npm run cap:dev:android       # Start Android development
npm run cap:dev:ios          # Start iOS development
```

### Build Commands
```bash
npm run build                 # Web build (landing page only)
npm run build:electron        # Electron static build
npm run build:mobile          # Mobile build for Capacitor
npm run build:static          # Static export build
```

### Platform-Specific Builds
```bash
# Electron
npm run electron:build        # Build Electron app
npm run electron:build:mac    # macOS build
npm run electron:build:win    # Windows build

# Mobile
npm run cap:build:android     # Android build
npm run cap:build:ios         # iOS build

# Deployment
npm run deploy:windows        # Deploy Windows to R2
npm run deploy:macos          # Deploy macOS DMG
npm run deploy:android        # Deploy Android APK
```

### Database Management
```bash
npm run prepare:couchdb       # Setup CouchDB
npm run verify:couchdb        # Verify CouchDB setup
npm run check:couchdb         # Check CouchDB status
```

## Build Targets

- **web**: Landing page only, excludes restaurant functionality
- **electron**: Full desktop app with bundled CouchDB
- **mobile**: Capacitor build with IndexedDB
- **static**: Static export for offline deployment

## Configuration Notes

- TypeScript strict mode disabled for faster development
- Source maps disabled in production for security
- Build errors ignored to allow deployment with warnings
- Offline-first: API calls fallback to local PouchDB when server unreachable