# Project Structure & Organization

## Root Directory Structure

```
bistro/
├── app/                      # Next.js App Router
├── components/               # Shared React components
├── lib/                      # Utilities, services, and business logic
├── types/                    # TypeScript type definitions
├── hooks/                    # Custom React hooks
├── public/                   # Static assets
├── electron/                 # Electron desktop app
├── android/                  # Android Capacitor build
├── ios/                      # iOS Capacitor build
├── scripts/                  # Build and deployment scripts
└── docs/                     # Documentation
```

## App Directory (Next.js App Router)

```
app/
├── (protected)/              # Protected routes requiring authentication
│   ├── analytics/           # Business analytics and reporting
│   ├── finance/             # Financial management
│   ├── inventory/           # Stock and inventory management
│   ├── menu/                # Menu management
│   ├── ordering/            # Point of sale interface
│   ├── orders/              # Order management
│   ├── settings/            # Application settings
│   ├── staff/               # Personnel management
│   └── waiter/              # Waiter interface
├── api/                     # API routes
│   ├── auth/                # Authentication endpoints
│   ├── staff/               # Staff management API
│   └── sync/                # Data synchronization API
├── auth/                    # Authentication pages
├── components/              # Page-specific components
└── hooks/                   # App-specific hooks
```

## Components Directory

```
components/
├── ui/                      # Shadcn UI base components
├── auth/                    # Authentication components
├── analytics/               # Analytics and reporting components
├── staff/                   # Staff management components
├── stock/                   # Inventory components
├── payment/                 # Payment processing components
├── print/                   # Printing components
├── mobile/                  # Mobile-specific components
└── providers/               # React context providers
```

## Lib Directory

```
lib/
├── api/                     # API client utilities
├── auth/                    # Authentication logic
├── db/                      # Database utilities and schemas
├── services/                # Business logic services
├── sync/                    # Data synchronization
├── types/                   # Shared TypeScript types
├── utils/                   # General utilities
└── constants.ts             # Application constants
```

## Key Conventions

### File Naming
- **Components**: PascalCase (e.g., `OrderList.tsx`)
- **Utilities**: kebab-case (e.g., `date-utils.ts`)
- **API routes**: kebab-case (e.g., `staff-management.ts`)
- **Types**: kebab-case (e.g., `order-types.ts`)

### Import Aliases
- `@/*` maps to project root
- Use absolute imports for better maintainability
- Group imports: external libraries, internal modules, relative imports

### Component Structure
- Prefer functional components with hooks
- Use TypeScript interfaces for props
- Export components as default when single export
- Co-locate related components in feature directories

### API Structure
- RESTful endpoints under `/api`
- Consistent error handling and response formats
- Offline-first: graceful fallback to local data
- Authentication middleware for protected routes

### Database Conventions
- PouchDB documents with `_id` and `_rev` fields
- Consistent document schemas across platforms
- Conflict resolution for offline sync
- Indexed fields for performance

### Mobile Considerations
- Responsive design with mobile-first approach
- Touch-friendly UI elements (44px minimum)
- Safe area handling for iOS
- Capacitor plugins for native functionality