# Implementation Plan

- [x] 1. Create unified asset path configuration system
  - Implement AssetPathCoordinator class with build target-aware path resolution
  - Create centralized configuration for all asset types (static, font, chunk, CSS, image)
  - Add path validation utilities for build-time consistency checking
  - _Requirements: 1.1, 3.1, 5.1_

- [x] 2. Enhance Next.js configuration for consistent static exports
  - Modify next.config.ts to use unified asset prefix system for static builds
  - Configure webpack output.publicPath consistently across all asset types
  - Implement build target-specific configuration branching
  - Remove dependency on post-build scripts by handling paths natively
  - _Requirements: 1.1, 1.2, 3.1, 3.2, 4.3, 5.2_

- [ ] 3. Optimize font loading system for static exports
  - Create FontConfigManager to handle next/font/google optimization for static builds
  - Implement font asset embedding for offline functionality
  - Configure font CSS generation with relative paths
  - Ensure font variables work consistently across all components
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 4. Implement webpack runtime enhancements for dynamic imports
  - Create webpack plugin to ensure consistent chunk loading paths
  - Implement runtime public path management for dynamic imports
  - Add chunk reference validation and correction
  - Ensure RSC payloads use relative asset paths natively
  - _Requirements: 1.3, 1.4, 3.3, 3.4_

- [ ] 5. Add comprehensive asset path validation system
  - Implement build-time validation for all asset references
  - Create detailed error reporting for path inconsistencies
  - Add validation for font assets, chunks, and static resources
  - Ensure validation works within Next.js build process without external scripts
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 6. Create build target compatibility layer
  - Ensure web and server builds continue using standard Next.js asset handling
  - Implement automatic configuration switching based on BUILD_TARGET
  - Maintain backward compatibility with existing build scripts
  - Test configuration changes across all build targets
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [ ] 7. Remove existing post-build script dependencies
  - Eliminate fix-rsc-chunk-paths.js by implementing native RSC path handling
  - Remove fix-electron-paths.js by configuring webpack correctly
  - Update build.js to remove script-based workarounds
  - Ensure electron-runtime-fix.js is no longer needed
  - _Requirements: 3.2, 3.4, 5.2, 5.4_

- [ ] 8. Implement comprehensive testing and validation
  - Create unit tests for AssetPathCoordinator and FontConfigManager
  - Add integration tests for build process with different targets
  - Test font loading in static export environments
  - Validate asset loading in simulated Electron and Capacitor environments
  - _Requirements: 1.1, 1.2, 2.1, 2.2, 3.1, 3.2_