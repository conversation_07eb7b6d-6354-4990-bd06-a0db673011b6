# Design Document

## Overview

The Next.js static export asset path issue stems from inconsistent path generation across different parts of the build system. Next.js 15 with App Router generates mixed asset references - some using relative paths (`./_next/`) and others using absolute paths (`/_next/`) or bare paths (`static/chunks/`). This inconsistency breaks loading in file:// protocol environments like Electron and Capacitor.

The root cause is that Next.js handles different asset types through separate systems:
1. **Static assets** (CSS, images) - handled by webpack with `output.publicPath`
2. **Font assets** - handled by `next/font` with its own path resolution
3. **JavaScript chunks** - handled by webpack runtime with dynamic imports
4. **RSC payloads** - handled by React Server Components with embedded asset references

The solution requires coordinating all these systems to use consistent relative paths natively within the Next.js build process.

## Architecture

### Core Components

1. **Enhanced Next.js Configuration**
   - Unified asset prefix configuration for static builds
   - Webpack configuration for consistent chunk loading
   - Font optimization for static exports

2. **Asset Path Coordinator**
   - Centralized path resolution for all asset types
   - Build target-aware path generation
   - Consistent relative path enforcement

3. **Font Loading Optimizer**
   - Static export-compatible font configuration
   - Embedded font asset handling
   - CSS variable generation for offline use

4. **Webpack Runtime Enhancer**
   - Dynamic import path correction
   - Chunk loading path consistency
   - Runtime public path management

## Components and Interfaces

### 1. Enhanced Next.js Configuration

```typescript
interface StaticExportConfig {
  assetPrefix: string;
  basePath: string;
  output: 'export';
  trailingSlash: boolean;
  images: { unoptimized: boolean };
  experimental: {
    optimizeCss: boolean;
    esmExternals: boolean;
  };
}

interface WebpackConfig {
  output: {
    publicPath: string;
    assetModuleFilename: string;
  };
  resolve: {
    alias: Record<string, string>;
  };
}
```

### 2. Asset Path Coordinator

```typescript
interface AssetPathCoordinator {
  getAssetPrefix(buildTarget: BuildTarget): string;
  resolveAssetPath(assetType: AssetType, path: string): string;
  validatePathConsistency(paths: string[]): ValidationResult;
}

enum AssetType {
  STATIC = 'static',
  FONT = 'font',
  CHUNK = 'chunk',
  CSS = 'css',
  IMAGE = 'image'
}

enum BuildTarget {
  WEB = 'web',
  ELECTRON = 'electron',
  MOBILE = 'mobile',
  SERVER = 'server'
}
```

### 3. Font Configuration Manager

```typescript
interface FontConfig {
  fonts: GoogleFontConfig[];
  staticExport: boolean;
  assetPrefix: string;
  cssVariables: boolean;
}

interface GoogleFontConfig {
  family: string;
  subsets: string[];
  weights: string[];
  display: 'swap' | 'block' | 'fallback';
  variable: string;
}
```

## Data Models

### Build Configuration Model

```typescript
interface BuildConfig {
  target: BuildTarget;
  staticExport: boolean;
  assetPrefix: string;
  publicPath: string;
  fontOptimization: boolean;
  pathValidation: boolean;
}
```

### Asset Reference Model

```typescript
interface AssetReference {
  type: AssetType;
  originalPath: string;
  resolvedPath: string;
  isRelative: boolean;
  isValid: boolean;
}
```

## Error Handling

### Asset Path Validation

1. **Build-time Validation**
   - Scan generated HTML for inconsistent paths
   - Validate font asset references
   - Check chunk loading paths
   - Report specific files with issues

2. **Runtime Error Recovery**
   - Fallback path resolution for missing assets
   - Dynamic path correction for failed loads
   - Console warnings for development debugging

3. **Configuration Validation**
   - Validate Next.js config for static export compatibility
   - Check webpack configuration consistency
   - Verify font configuration completeness

### Error Types

```typescript
enum AssetPathError {
  INCONSISTENT_PATHS = 'inconsistent_paths',
  MISSING_FONT_ASSETS = 'missing_font_assets',
  BROKEN_CHUNK_REFERENCES = 'broken_chunk_references',
  INVALID_PUBLIC_PATH = 'invalid_public_path'
}

interface ValidationError {
  type: AssetPathError;
  file: string;
  line?: number;
  originalPath: string;
  expectedPath: string;
  suggestion: string;
}
```

## Testing Strategy

### Unit Tests

1. **Configuration Tests**
   - Test Next.js config generation for different build targets
   - Validate webpack configuration consistency
   - Test font configuration generation

2. **Path Resolution Tests**
   - Test asset path coordinator with various input paths
   - Validate path transformation logic
   - Test build target-specific path generation

### Integration Tests

1. **Build Process Tests**
   - Test complete build process for each target
   - Validate generated HTML structure
   - Test asset loading in simulated environments

2. **Font Loading Tests**
   - Test Google Fonts loading in static exports
   - Validate font CSS generation
   - Test font fallback behavior

### End-to-End Tests

1. **Electron Environment Tests**
   - Test app loading in Electron with file:// protocol
   - Validate all assets load correctly
   - Test font rendering and functionality

2. **Capacitor Environment Tests**
   - Test app loading in Capacitor WebView
   - Validate mobile-specific asset handling
   - Test offline functionality

## Implementation Approach

### Phase 1: Core Configuration Enhancement

1. **Next.js Configuration Overhaul**
   - Implement unified asset prefix system
   - Configure webpack for consistent path handling
   - Set up build target-specific configurations

2. **Asset Path Coordinator Implementation**
   - Create centralized path resolution system
   - Implement build target detection
   - Add path validation utilities

### Phase 2: Font System Optimization

1. **Font Configuration Enhancement**
   - Optimize next/font for static exports
   - Implement font asset embedding
   - Create CSS variable system for offline use

2. **Font Loading Validation**
   - Add font asset validation
   - Implement fallback font handling
   - Create font loading error recovery

### Phase 3: Webpack Runtime Enhancement

1. **Dynamic Import Path Correction**
   - Enhance webpack runtime for relative paths
   - Implement chunk loading path consistency
   - Add runtime path validation

2. **RSC Payload Optimization**
   - Ensure RSC payloads use relative paths
   - Optimize server component asset references
   - Implement RSC-specific path validation

### Phase 4: Validation and Testing

1. **Comprehensive Validation System**
   - Implement build-time asset validation
   - Add runtime error detection
   - Create detailed error reporting

2. **Cross-Platform Testing**
   - Test in Electron environments
   - Validate Capacitor compatibility
   - Ensure web build compatibility

## Key Design Decisions

### 1. Native Next.js Integration
- **Decision**: Implement all fixes within Next.js configuration and webpack setup
- **Rationale**: Eliminates need for post-build scripts and ensures maintainability
- **Trade-offs**: Requires deeper Next.js configuration knowledge but provides cleaner solution

### 2. Unified Asset Prefix System
- **Decision**: Use single asset prefix configuration for all asset types
- **Rationale**: Ensures consistency across all asset loading mechanisms
- **Trade-offs**: May require more complex configuration but eliminates path inconsistencies

### 3. Build Target-Aware Configuration
- **Decision**: Dynamically configure asset handling based on BUILD_TARGET environment variable
- **Rationale**: Maintains compatibility with existing build system while enabling static export fixes
- **Trade-offs**: Adds configuration complexity but preserves existing functionality

### 4. Font Asset Embedding
- **Decision**: Embed font assets directly in static export rather than external references
- **Rationale**: Ensures offline functionality and eliminates external dependencies
- **Trade-offs**: Increases bundle size but guarantees font availability

This design provides a comprehensive, native Next.js solution that addresses all aspects of the asset path inconsistency issue while maintaining compatibility with existing build targets and eliminating the need for post-build processing scripts.