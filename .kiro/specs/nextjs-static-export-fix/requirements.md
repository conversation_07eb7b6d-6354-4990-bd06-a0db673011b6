# Requirements Document

## Introduction

This feature addresses the critical issue where Next.js 15 static exports generate inconsistent asset paths that prevent proper loading in Electron and Capacitor mobile environments. The app builds successfully but fails to load due to 404 errors for certain asset types, particularly affecting next/font/google assets and dynamic chunks. The solution must work natively within the Next.js build system without requiring post-build scripts.

## Requirements

### Requirement 1

**User Story:** As a developer, I want Next.js static exports to generate consistent relative asset paths, so that the app loads properly in Electron and Capacitor environments.

#### Acceptance Criteria

1. WHEN building with BUILD_TARGET=electron or BUILD_TARGET=mobile THEN all asset references SHALL use consistent relative paths starting with "./_next/"
2. WHEN the static export is served from file:// protocol THEN all assets SHALL load without 404 errors
3. WHEN next/font/google is used THEN font assets SHALL be properly referenced with relative paths
4. WHEN dynamic chunks are loaded THEN chunk references SHALL use relative paths compatible with static serving

### Requirement 2

**User Story:** As a developer, I want the font loading system to work seamlessly in static exports, so that Google Fonts display correctly in offline environments.

#### Acceptance Criteria

1. WH<PERSON> using next/font/google with multiple fonts THEN all font files SHALL be properly embedded in the static export
2. WHEN the app loads offline THEN fonts SHALL display correctly without external network requests
3. WHEN font CSS is generated THEN it SHALL use relative paths to font files
4. WHEN font variables are applied THEN they SHALL work consistently across all components

### Requirement 3

**User Story:** As a developer, I want the build configuration to handle asset paths natively within Next.js, so that no post-build scripts or workarounds are required.

#### Acceptance Criteria

1. WHEN configuring Next.js for static export THEN the webpack and Next.js configuration SHALL ensure consistent relative paths at build time
2. WHEN the build completes THEN all HTML files SHALL contain properly formatted asset references without requiring post-processing
3. WHEN RSC (React Server Components) payloads are generated THEN they SHALL use relative asset paths natively from Next.js
4. WHEN the app initializes THEN all JavaScript chunks SHALL load successfully from relative paths without any fix scripts

### Requirement 4

**User Story:** As a developer, I want the solution to maintain compatibility with existing build targets, so that web and server builds continue to work normally.

#### Acceptance Criteria

1. WHEN BUILD_TARGET is "web" THEN the build SHALL use standard Next.js asset handling
2. WHEN BUILD_TARGET is "server" THEN the build SHALL use server-side rendering with absolute paths
3. WHEN BUILD_TARGET is "electron" or "mobile" THEN the build SHALL use static export with relative paths
4. WHEN switching between build targets THEN the configuration SHALL adapt automatically without manual changes

### Requirement 5

**User Story:** As a developer, I want the solution to be implemented entirely within the Next.js configuration and webpack setup, so that the build process is clean and maintainable.

#### Acceptance Criteria

1. WHEN implementing the fix THEN all changes SHALL be made to next.config.ts and related configuration files only
2. WHEN the build runs THEN it SHALL produce correct asset paths without requiring any post-build processing scripts
3. WHEN debugging asset issues THEN the problem SHALL be traceable to configuration rather than script-based workarounds
4. WHEN maintaining the codebase THEN developers SHALL only need to understand Next.js configuration, not custom build scripts