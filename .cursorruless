
**Overall App Guidelines (The Big Picture 🗺️):**

*   **Restaurant First!** 🏢 Everything revolves around individual restaurants now, each with its own database (`restaurant-{cleanRestaurantId}`). Forget the old user-centric way.
*   **Use the New Stuff:** Strictly use `RestaurantDB` (from `lib/db/v3`), `new-auth.ts` for logins, and context providers to pass things around.
*   **Clean IDs:** Always clean the restaurant ID using `cleanRestaurantId()` before using it.
*   **Structure is Key:**
    *   Use dedicated services for different parts (menu, tables, etc.).
    *   Components get data through custom hooks, not directly from the database.
    *   Follow the `MenuManagement.tsx` example for how components should work.
    *   APIs must use the new auth and get the database context correctly.
*   **No Old Code:** Get rid of old database lookups, V2 code, and direct database creation.
*   **Remember:** Offline-first, proper error handling, performance, and security are always important.

**Financial Management System Recap (Money Matters 💰):**

*   **Main Goal:** Help Algerian restaurants track cash (`La Caisse`) 💵 accurately to prevent theft and manage basic expenses.
*   **Key Parts:**
    *   **Finance Dashboard (`/finance`):** Shows income/expenses/profit, lists transactions, and manages expense entries (rent, supplies, etc.).
    *   **Cash Register (`/finance/caisse`):** Manages cash drawer open/close sessions, tracks every cash movement (in/out) with reasons, compares expected vs. actual cash, and flags differences. 🕵️
*   **Focus:** Built to address the primary concern of cash tracking and theft.
*   **Future:** Designed to connect later with orders, inventory, suppliers, and payroll. 🔗
