# Kitchen Display System Workflow

## Overview

The Kitchen Display System (KDS) is a comprehensive order management system designed for restaurant kitchen operations. It provides real-time order tracking, status management, and seamless communication between kitchen staff and waiters. The system supports multi-station operations with barcode-based item completion tracking.

## Architecture Components

### Core Components

1. **KitchenDisplay Component** (`/Users/<USER>/Desktop/rest/shop/components/KitchenDisplay.tsx`)
   - Main interface for kitchen staff
   - Real-time order display in three-column layout (Pending, Preparing, Ready)
   - Order status management and timing tracking
   - Item-level status updates and notifications

2. **Kitchen Queue Service** (`/Users/<USER>/Desktop/rest/shop/lib/services/kitchen-queue-service.ts`)
   - Manages queue state persistence using PouchDB
   - Handles barcode scanning and item completion tracking
   - Provides station-based queue management

3. **Kitchen Print Service** (`/Users/<USER>/Desktop/rest/shop/lib/services/kitchen-print-service.ts`)
   - Generates kitchen tickets with barcodes
   - Manages multi-station printing workflow
   - Handles kitchen-specific print formatting

4. **Kitchen Barcode Scanner** (`/Users/<USER>/Desktop/rest/shop/app/components/KitchenBarcodeScanner.tsx`)
   - Barcode scanning interface for item completion
   - Real-time scan history and status tracking
   - Auto-generates expo tickets when orders are complete

## Order Status Flow

### Status Progression
```
pending → preparing → served → completed
              ↓
           cancelled
```

### Status Definitions
- **pending**: New orders awaiting kitchen preparation (lines 17, 100-104)
- **preparing**: Orders currently being prepared (lines 18, 101-104)
- **served**: Orders ready for pickup/delivery (lines 19, 103)
- **completed**: Orders fully delivered to customers (lines 20, 144)
- **cancelled**: Cancelled orders (lines 146)

### Status Management Functions
Located in KitchenDisplay.tsx (lines 202-221):
- `handleUpdateOrderStatus()` - Updates order status with timestamp tracking
- `trackStatusChange()` - Records status change timestamps (lines 333-341)
- Status transitions include validation and automatic refresh

## Kitchen Staff Interface

### Display Layout (lines 742-1159)
Three-column responsive grid:

1. **Pending Orders Column** (lines 743-869)
   - Yellow accent border (border-l-yellow-500)
   - Shows new orders awaiting preparation
   - "Start Preparing" action button
   - Cancel order functionality

2. **Preparing Orders Column** (lines 871-1013)
   - Orange accent border (border-l-orange-500)
   - Displays orders in preparation
   - Shows preparation timing information
   - "Ready to Serve" action button
   - Back to pending option

3. **Ready Orders Column** (lines 1015-1158)
   - Green accent border (border-l-green-500)
   - Shows orders ready for pickup
   - "Mark Delivered" action button
   - Back to preparing option

### Key Features

#### Real-time Updates (lines 188-199)
- Auto-refresh every 30 seconds when enabled
- Manual refresh functionality
- Last refresh timestamp display

#### Search and Filtering (lines 95-164)
- Search by order ID, table ID, item names, customer info
- Status filtering (active, all, pending, preparing, served, completed, cancelled)
- Priority sorting options (newest, oldest, priority-based)

#### Timing Tracking (lines 224-285)
- Tracks time spent in each status
- Shows pending time, preparation time, and total time
- Visual timing indicators for staff awareness

## Item-Level Management

### Item Status Tracking (lines 74-82, 287-330)
Advanced item management includes:
- Individual item status updates (damaged, remade)
- Quantity-specific tracking
- Notes for special handling
- Visual status indicators

### Item Status Dialog (lines 1187-1274)
Modal interface for updating item status:
- Status selection (damaged/remade)
- Quantity adjustment
- Optional notes field
- Real-time status history

## Queue Management System

### Queue Schema (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/schemas/queue-schema.ts`)

#### Queue Item Structure (lines 140-155)
```typescript
interface QueueItem {
  _id: string;
  type: 'queue_item';
  orderId: string;
  orderNumber: string;
  stationId: string;
  items: OrderItem[];
  completedItemIds: string[];
  status: 'pending' | 'in-progress' | 'completed';
  estimatedTime?: number;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}
```

#### Item Completion Tracking (lines 157-171)
```typescript
interface ItemCompletion {
  barcode: string;
  orderId: string;
  stationId: string;
  itemName: string;
  status: 'pending' | 'completed';
  scannedBy?: string;
  completedAt?: string;
}
```

### Queue Operations
Key functions in kitchen-queue-service.ts:

1. **addOrderToQueue()** (lines 62-72)
   - Adds orders to station-specific queues
   - Tracks items per station

2. **completeItem()** (lines 77-95)
   - Handles barcode-based item completion
   - Returns completion status and order completion state

3. **getStationQueue()** (lines 144-153)
   - Retrieves current queue status for a station
   - Returns queue metrics and item lists

## Barcode System Integration

### Barcode Generation
Kitchen print service generates unique barcodes for each item:
- Format: Daily sequence + item index (e.g., "002005")
- Compact 6-digit numeric format for efficient scanning
- Embedded in kitchen tickets with visual barcode images

### Barcode Scanning Interface
KitchenBarcodeScanner.tsx provides:
- Real-time barcode input and scanning
- Scan history with success/failure tracking
- Automatic expo ticket generation when orders complete
- Instructions and help interface

### Scanning Workflow (lines 49-124)
1. Scan item barcode from kitchen ticket
2. System validates barcode against pending items
3. Item marked as complete with timestamp
4. Check if entire order is complete
5. Generate expo ticket if all items done
6. Update kitchen display in real-time

## Multi-Station Operations

### Station Assignment
Orders are automatically distributed to stations based on:
- Menu item categories
- Printer configurations
- Category-to-station mappings

### Queue Coordination
Inter-station communication includes:
- Queue status sharing between stations
- Pending item counts for coordination
- Order completion synchronization

## Kitchen-Waiter Communication

### Status Notifications
- Real-time status updates visible to all users
- Toast notifications for status changes
- Visual indicators for order progression

### Order Information Sharing
- Table assignments and customer details
- Special instructions and notes
- Order timing and priority information

### Communication Workflow
1. Waiter creates order → appears in kitchen pending queue
2. Kitchen starts preparation → status visible to waiters
3. Kitchen marks ready → waiter notification
4. Waiter confirms delivery → order completed

## Performance Features

### Auto-refresh System (lines 188-199)
- Configurable automatic refresh (30-second intervals)
- Manual refresh with loading indicators
- Timestamp tracking for refresh operations

### Offline-First Architecture
- PouchDB persistence for queue data
- Local-first operations with sync capabilities
- Conflict resolution for concurrent updates

### Responsive Design
- Mobile-friendly interface
- Touch-optimized controls
- Compact display for kitchen environments

## Technical Implementation Details

### Database Integration
- Uses V4 database schema with PouchDB
- Queue operations with conflict resolution
- Indexed queries for performance

### State Management
- React hooks for order data (`useOrderV4`)
- Local state for UI interactions
- Toast notifications for user feedback

### Error Handling
- Comprehensive error catching and user feedback
- Retry mechanisms for failed operations
- Graceful degradation for offline scenarios

## Security and Permissions

### Authentication
- Multi-user authentication system
- Role-based access control
- Kitchen staff permissions validation

### Data Access
- Secure API endpoints for order operations
- Real-time data synchronization
- Audit trail for all status changes

## Future Enhancements

The system architecture supports:
- Additional station types and workflows
- Enhanced analytics and reporting
- Integration with external kitchen equipment
- Advanced notification systems
- Mobile application extensions

## Configuration

### Kitchen Display Settings
- Auto-refresh toggles
- Font size preferences (small, medium, large)
- Display mode selection (display vs printer mode)
- Column layout customization

### Print System Configuration
- Multi-printer support with category assignments
- Barcode feature enabling/disabling
- Queue coordination features
- Print preview and validation

This documentation provides a comprehensive overview of the Kitchen Display System workflow, covering all major components, interactions, and implementation details found in the codebase analysis.