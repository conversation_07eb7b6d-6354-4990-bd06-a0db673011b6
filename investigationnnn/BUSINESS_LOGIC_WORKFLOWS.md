# 🔄 Bistro Restaurant SaaS - Business Logic Workflows

## 📋 Overview

This document details the core business workflows and logic patterns in the Bistro restaurant management system. Each workflow represents a critical business process with specific rules, validations, and data transformations.

## 🛒 Order Management Workflows

### 📝 Order Creation Workflow

```mermaid
graph TD
    A[🎯 Start Order] --> B[📋 Select Order Type]
    B --> C{🏪 Order Type?}
    C -->|Dine-in| D[🪑 Select Table]
    C -->|Takeaway| E[👤 Customer Info]
    C -->|Delivery| F[🚚 Delivery Details]
    
    D --> G[🍽️ Add Menu Items]
    E --> G
    F --> G
    
    G --> H[💰 Calculate Totals]
    H --> I[💸 Apply Discounts?]
    I -->|Yes| J[🏷️ Apply Discount]
    I -->|No| K[✅ Finalize Order]
    J --> K
    
    K --> L[💾 Save to Database]
    L --> M[🖨️ Print Kitchen Ticket]
    M --> N[📊 Update Analytics]
    N --> O[🎉 Order Created]
```

#### 🎯 Business Rules
- **Order ID Generation**: `YYYYMMDD-XXX` format based on business day (5 AM reset)
- **Table Validation**: Ensure table exists and is available for dine-in orders
- **Menu Item Validation**: Verify items exist, are available, and pricing is current
- **Stock Checking**: Optional pre-order stock validation for critical items
- **Discount Rules**: Percentage or fixed amount, with reason tracking
- **Minimum Order**: Configurable minimum order amounts by order type

#### 💡 Key Features
- **Custom Pizza Quarters**: Mix different pizza types in single order
- **Supplement Integration**: Automatic stock consumption for addons
- **Real-time Pricing**: Dynamic pricing based on size and addons
- **Multi-currency Support**: Configurable currency and tax rates

### 🔄 Order Status Progression

```mermaid
stateDiagram-v2
    [*] --> pending: Order Created
    pending --> preparing: Kitchen Accepts
    preparing --> served: Food Ready
    served --> completed: Payment Processed
    
    pending --> cancelled: Order Cancelled
    preparing --> cancelled: Kitchen Cancels
    
    completed --> [*]: Order Archived
    cancelled --> [*]: Order Archived
    
    note right of preparing: Kitchen Queue Integration
    note right of completed: Stock Consumption & COGS
```

#### 🎯 Status Transition Rules
- **Pending → Preparing**: Kitchen accepts order, items added to queue
- **Preparing → Served**: All items marked complete in kitchen queue
- **Served → Completed**: Payment processed, stock consumed, COGS calculated
- **Any → Cancelled**: Order voided, stock returned, refund processed

### 💳 Payment Processing Workflow

```mermaid
graph TD
    A[💰 Process Payment] --> B{💳 Payment Method?}
    B -->|Cash| C[💵 Cash Payment]
    B -->|Card| D[💳 Card Payment]
    B -->|Mixed| E[🔄 Mixed Payment]
    
    C --> F[💸 Calculate Change]
    D --> G[✅ Card Validation]
    E --> H[📊 Split Amounts]
    
    F --> I[💾 Record Transaction]
    G --> I
    H --> I
    
    I --> J[📈 Update Cash Register]
    J --> K[🧾 Generate Receipt]
    K --> L[📊 Update Analytics]
    L --> M[✅ Payment Complete]
    
    M --> N[🍽️ Consume Stock]
    N --> O[💰 Calculate COGS]
    O --> P[📊 Update Profit Metrics]
```

#### 🎯 Payment Business Rules
- **Amount Validation**: Must match order total or be partial payment
- **Change Calculation**: Automatic change calculation for cash payments
- **Partial Payments**: Support for multiple payment installments
- **Refund Processing**: Automatic refund calculation for cancelled orders
- **Cash Register Integration**: All payments update cash register balance
- **Receipt Generation**: Automatic receipt printing with order details

## 🍽️ Menu Management Workflows

### 📋 Menu Structure Management

```mermaid
graph TD
    A[🍽️ Menu Management] --> B[📂 Category Management]
    A --> C[🍕 Item Management]
    A --> D[📏 Size Management]
    A --> E[🧩 Addon Management]
    
    B --> B1[➕ Add Category]
    B --> B2[✏️ Edit Category]
    B --> B3[🗑️ Delete Category]
    
    C --> C1[➕ Add Menu Item]
    C --> C2[✏️ Edit Item Details]
    C --> C3[💰 Update Pricing]
    C --> C4[🗑️ Remove Item]
    
    D --> D1[📏 Add Size Option]
    D --> D2[🔄 Rename Size]
    D --> D3[❌ Remove Size]
    
    E --> E1[🧩 Create Addon]
    E --> E2[📦 Link to Stock]
    E --> E3[💰 Set Pricing]
```

#### 🎯 Menu Business Rules
- **Category Hierarchy**: Categories contain items, sizes are category-wide
- **Size-based Pricing**: Each item can have different prices per size
- **Addon Integration**: Addons can consume stock items automatically
- **Recipe Integration**: Items linked to recipes for COGS calculation
- **Availability Control**: Items can be marked unavailable without deletion
- **Pricing Validation**: Prices must be positive, support decimal precision

### 🍕 Custom Pizza System

```mermaid
graph TD
    A[🍕 Custom Pizza Order] --> B[📏 Select Size]
    B --> C[🔄 Choose Quarters]
    C --> D[🍕 Quarter 1: Pizza Type]
    C --> E[🍕 Quarter 2: Pizza Type]
    C --> F[🍕 Quarter 3: Pizza Type]
    C --> G[🍕 Quarter 4: Pizza Type]
    
    D --> H[💰 Calculate Quarter Price]
    E --> H
    F --> H
    G --> H
    
    H --> I[🧩 Add Common Addons]
    I --> J[💰 Calculate Total]
    J --> K[📦 Stock Calculation]
    K --> L[✅ Add to Order]
```

#### 🎯 Pizza Business Rules
- **Quarter System**: Each pizza divided into 4 quarters, each can be different
- **Size Consistency**: All quarters must be same size
- **Price Calculation**: Average of all quarter prices + size multiplier
- **Stock Consumption**: Each quarter consumes stock based on its recipe
- **Addon Application**: Addons apply to entire pizza, not individual quarters

## 📦 Inventory Management Workflows

### 📊 Stock Management Workflow

```mermaid
graph TD
    A[📦 Inventory Management] --> B[📥 Stock Receiving]
    A --> C[📊 Stock Counting]
    A --> D[🔄 Stock Adjustments]
    A --> E[🗑️ Waste Tracking]
    
    B --> B1[📋 Create Purchase Order]
    B1 --> B2[📦 Receive Goods]
    B2 --> B3[✅ Update Stock Levels]
    B3 --> B4[💰 Update Costs]
    
    C --> C1[📊 Physical Count]
    C1 --> C2[🔍 Compare with System]
    C2 --> C3[📝 Record Discrepancies]
    C3 --> C4[🔄 Adjust Stock Levels]
    
    D --> D1[➕ Stock Increase]
    D --> D2[➖ Stock Decrease]
    D1 --> D3[📝 Record Reason]
    D2 --> D3
    D3 --> D4[💾 Update Database]
    
    E --> E1[🗑️ Record Waste]
    E1 --> E2[📝 Waste Reason]
    E2 --> E3[💰 Calculate Loss]
    E3 --> E4[📊 Update Reports]
```

#### 🎯 Inventory Business Rules
- **Stock Levels**: Real-time tracking with minimum threshold alerts
- **Cost Tracking**: FIFO/LIFO cost calculation for COGS
- **Automatic Consumption**: Stock consumed automatically on order completion
- **Waste Tracking**: All waste must be categorized with reasons
- **Purchase Integration**: Purchase orders update stock and costs
- **Audit Trail**: All stock movements logged with timestamps and reasons

### 🔄 Automatic Stock Consumption

```mermaid
graph TD
    A[✅ Order Completed] --> B[🔍 Analyze Order Items]
    B --> C{🍕 Item Type?}
    C -->|Regular Item| D[📋 Get Recipe]
    C -->|Custom Pizza| E[🍕 Process Quarters]
    C -->|Item with Addons| F[🧩 Process Addons]
    
    D --> G[📦 Calculate Consumption]
    E --> H[🍕 Calculate Quarter Consumption]
    F --> I[🧩 Calculate Addon Consumption]
    
    G --> J[📊 Update Stock Levels]
    H --> J
    I --> J
    
    J --> K[💰 Calculate COGS]
    K --> L[📝 Create Consumption Log]
    L --> M[📊 Update Analytics]
```

#### 🎯 Consumption Business Rules
- **Recipe-based**: Consumption calculated from item recipes
- **Size Scaling**: Consumption scales with item size
- **Addon Integration**: Supplements consume additional stock
- **Batch Tracking**: Track which stock batches were consumed
- **Cost Calculation**: COGS calculated using current stock costs
- **Audit Trail**: All consumption logged for inventory reconciliation

## 👥 Staff Management Workflows

### 📅 Staff Scheduling Workflow

```mermaid
graph TD
    A[👥 Staff Scheduling] --> B[📅 Create Weekly Schedule]
    B --> C[👤 Select Staff Member]
    C --> D[📅 Select Day]
    D --> E[⏰ Choose Shift]
    E --> F[✅ Assign Shift]
    F --> G{🔄 More Assignments?}
    G -->|Yes| C
    G -->|No| H[💾 Save Schedule]
    
    H --> I[📱 Notify Staff]
    I --> J[📊 Generate Reports]
    J --> K[✅ Schedule Complete]
    
    L[📝 Daily Attendance] --> M[👤 Select Staff]
    M --> N[⏰ Select Shift]
    N --> O[✅ Mark Present/Absent]
    O --> P[📝 Add Notes]
    P --> Q[💾 Record Attendance]
    Q --> R[💰 Update Payroll]
```

#### 🎯 Scheduling Business Rules
- **Shift Templates**: Predefined shifts with start/end times
- **Availability Checking**: Prevent double-booking staff members
- **Minimum Staffing**: Ensure minimum staff levels per shift
- **Overtime Calculation**: Automatic overtime detection and calculation
- **Attendance Integration**: Scheduled shifts linked to attendance tracking
- **Payroll Integration**: Attendance automatically updates payroll calculations

### 💰 Staff Payment System

```mermaid
graph TD
    A[💰 Staff Payment] --> B{💼 Payment Type?}
    B -->|Salary| C[💰 Fixed Salary]
    B -->|Hourly| D[⏰ Hourly Rate]
    B -->|Per Shift| E[🎯 Shift Rate]
    
    C --> F[📅 Calculate Monthly]
    D --> G[⏰ Calculate Hours Worked]
    E --> H[🎯 Count Shifts Worked]
    
    F --> I[💰 Base Amount]
    G --> I
    H --> I
    
    I --> J[➕ Add Bonuses]
    J --> K[➖ Subtract Advances]
    K --> L[➖ Subtract Deductions]
    L --> M[💰 Final Payment Amount]
    
    M --> N[📊 Create Payment Record]
    N --> O[💾 Update Staff Balance]
    O --> P[🧾 Generate Pay Slip]
```

#### 🎯 Payment Business Rules
- **Multiple Payment Models**: Salary, hourly, per-shift, or mixed
- **Balance Tracking**: Track advances, deductions, bonuses
- **Automatic Calculation**: Payments calculated from attendance records
- **Payment Periods**: Weekly, bi-weekly, or monthly payment cycles
- **Tax Integration**: Support for tax calculations and deductions
- **Audit Trail**: All payment transactions logged and traceable

## 🚚 Delivery Management Workflows

### 🚚 Delivery Order Processing

```mermaid
graph TD
    A[🚚 Delivery Order] --> B[📍 Validate Address]
    B --> C[👤 Assign Driver]
    C --> D{🚗 Driver Type?}
    D -->|Staff| E[👨‍💼 Staff Driver]
    D -->|Freelance| F[🚚 Freelance Driver]
    
    E --> G[📱 Notify Driver]
    F --> H[💰 Set Payment Rate]
    H --> G
    
    G --> I[🍽️ Prepare Order]
    I --> J[📦 Package Order]
    J --> K[🚚 Out for Delivery]
    
    K --> L[📍 Track Delivery]
    L --> M{✅ Delivered?}
    M -->|Yes| N[✅ Mark Delivered]
    M -->|No| O[❌ Failed Attempt]
    
    N --> P[💰 Process Payment]
    O --> Q[📝 Record Failure Reason]
    Q --> R[🔄 Reschedule Delivery]
    
    P --> S[💰 Pay Driver]
    S --> T[📊 Update Analytics]
```

#### 🎯 Delivery Business Rules
- **Address Validation**: Ensure delivery address is complete and valid
- **Driver Assignment**: Automatic or manual driver assignment
- **Payment Models**: Fixed rate or percentage-based driver payment
- **Delivery Tracking**: Real-time status updates and GPS tracking
- **Failed Delivery Handling**: Automatic rescheduling and customer notification
- **Collection Tracking**: For freelance drivers using collection model

### 💰 Freelance Driver Management

```mermaid
graph TD
    A[🚚 Freelance Driver] --> B[📝 Driver Registration]
    B --> C[💰 Set Payment Model]
    C --> D{💳 Payment Type?}
    D -->|Per Delivery| E[💰 Fixed Rate]
    D -->|Collection| F[📊 Percentage Rate]
    
    E --> G[🚚 Assign Deliveries]
    F --> H[💰 Track Collections]
    
    G --> I[📊 Track Performance]
    H --> I
    
    I --> J[💰 Calculate Payments]
    J --> K[📝 Generate Reports]
    K --> L[💳 Process Payment]
    
    M[📊 Collection Model] --> N[💰 Collect Payment]
    N --> O[💸 Deduct Expenses]
    O --> P[💰 Calculate Driver Share]
    P --> Q[📝 Record Transaction]
```

#### 🎯 Freelance Driver Rules
- **Registration Process**: Phone-based registration with performance tracking
- **Payment Models**: Per-delivery fixed rate or collection percentage
- **Performance Metrics**: Track delivery success rate, customer ratings
- **Expense Tracking**: Fuel, parking, and other delivery expenses
- **Collection Management**: Track money collected vs. expected amounts
- **Payment Processing**: Automatic payment calculation and processing

## 💰 Financial Management Workflows

### 💵 Cash Register Management

```mermaid
graph TD
    A[💵 Cash Register] --> B[🌅 Opening Balance]
    B --> C[💰 Daily Transactions]
    C --> D[💳 Order Payments]
    C --> E[➕ Manual Cash In]
    C --> F[➖ Manual Cash Out]
    
    D --> G[📊 Update Balance]
    E --> G
    F --> G
    
    G --> H[🌙 End of Day]
    H --> I[💰 Count Physical Cash]
    I --> J[🔍 Compare with System]
    J --> K{💰 Match?}
    K -->|Yes| L[✅ Balanced]
    K -->|No| M[❌ Discrepancy]
    
    L --> N[📊 Generate Report]
    M --> O[📝 Record Difference]
    O --> P[🔍 Investigate]
    P --> N
```

#### 🎯 Cash Register Rules
- **Opening Balance**: Must be set at start of each business day
- **Transaction Recording**: All cash movements automatically recorded
- **Real-time Balance**: Current balance calculated from all transactions
- **End-of-day Reconciliation**: Physical count vs. system balance
- **Discrepancy Handling**: All differences must be documented and investigated
- **Audit Trail**: Complete transaction history with timestamps and users

### 📊 Financial Reporting

```mermaid
graph TD
    A[📊 Financial Reports] --> B[📈 Sales Reports]
    A --> C[💰 Cash Flow]
    A --> D[📊 Profit Analysis]
    A --> E[💸 Expense Reports]
    
    B --> B1[📅 Daily Sales]
    B --> B2[📊 Item Performance]
    B --> B3[👥 Staff Performance]
    
    C --> C1[💵 Cash In/Out]
    C --> C2[💳 Payment Methods]
    C --> C3[🏦 Bank Reconciliation]
    
    D --> D1[💰 Gross Profit]
    D --> D2[📊 COGS Analysis]
    D --> D3[📈 Profit Margins]
    
    E --> E1[📂 Expense Categories]
    E --> E2[📊 Expense Trends]
    E --> E3[💰 Budget vs. Actual]
```

#### 🎯 Financial Reporting Rules
- **Real-time Data**: All reports based on real-time transaction data
- **Date Range Filtering**: Flexible date range selection for all reports
- **Multi-currency Support**: Handle multiple currencies with conversion
- **Export Capabilities**: Export reports to PDF, Excel, CSV formats
- **Automated Scheduling**: Automatic report generation and email delivery
- **Drill-down Analysis**: Detailed breakdown of all summary figures

## 🔄 Data Synchronization Workflows

### 🌐 Multi-Device Sync

```mermaid
graph TD
    A[🔄 Data Sync] --> B[🔍 Discover Devices]
    B --> C{🌐 Connection Type?}
    C -->|Local Network| D[🏠 Local Sync]
    C -->|Internet| E[🌍 Internet Sync]
    
    D --> F[📡 Direct Connection]
    E --> G[🔗 Proxy Connection]
    
    F --> H[🔄 Bidirectional Sync]
    G --> H
    
    H --> I[⚡ Conflict Detection]
    I --> J{🔍 Conflicts Found?}
    J -->|Yes| K[🔧 Resolve Conflicts]
    J -->|No| L[✅ Sync Complete]
    
    K --> M[📝 Log Resolution]
    M --> L
    
    L --> N[📊 Update Status]
    N --> O[🔔 Notify Users]
```

#### 🎯 Synchronization Rules
- **Automatic Discovery**: Devices automatically discover sync partners
- **Conflict Resolution**: Last-write-wins with manual override options
- **Incremental Sync**: Only changed documents are synchronized
- **Offline Support**: Full functionality when disconnected
- **Data Integrity**: Checksums and validation ensure data consistency
- **Performance Optimization**: Compression and batching for large datasets

## 🎯 Business Intelligence Workflows

### 📊 Analytics Processing

```mermaid
graph TD
    A[📊 Analytics Engine] --> B[📥 Data Collection]
    B --> C[🔄 Data Processing]
    C --> D[📈 Metric Calculation]
    D --> E[📊 Report Generation]
    
    B --> B1[💰 Sales Data]
    B --> B2[📦 Inventory Data]
    B --> B3[👥 Staff Data]
    B --> B4[🚚 Delivery Data]
    
    C --> C1[🧹 Data Cleaning]
    C --> C2[🔗 Data Joining]
    C --> C3[📊 Aggregation]
    
    D --> D1[💰 Revenue Metrics]
    D --> D2[📊 Performance KPIs]
    D --> D3[📈 Trend Analysis]
    
    E --> E1[📱 Dashboard Updates]
    E --> E2[📧 Automated Reports]
    E --> E3[🚨 Alert Generation]
```

#### 🎯 Analytics Business Rules
- **Real-time Processing**: Metrics updated in real-time as data changes
- **Historical Tracking**: Maintain historical data for trend analysis
- **Comparative Analysis**: Compare current performance with previous periods
- **Predictive Analytics**: Forecast future trends based on historical data
- **Alert System**: Automatic alerts for unusual patterns or thresholds
- **Custom Metrics**: Support for restaurant-specific KPIs and metrics

---

## 🎯 Workflow Integration Points

### 🔗 Cross-System Dependencies

1. **Order → Inventory**: Order completion triggers stock consumption
2. **Order → Finance**: Payments update cash register and financial reports
3. **Staff → Payroll**: Attendance automatically updates payment calculations
4. **Inventory → Menu**: Stock levels affect menu item availability
5. **Delivery → Finance**: Driver payments integrated with expense tracking
6. **Analytics → All Systems**: Real-time data feeds from all business processes

### 🔄 Data Flow Patterns

```mermaid
graph LR
    A[📱 User Interface] --> B[🔧 Business Logic]
    B --> C[💾 Database Layer]
    C --> D[🔄 Sync Engine]
    D --> E[🌐 Other Devices]
    
    B --> F[📊 Analytics Engine]
    F --> G[📈 Reports & Dashboards]
    
    B --> H[🔔 Notification System]
    H --> I[📧 Email/SMS/Push]
```

This comprehensive workflow documentation shows how Bistro handles complex restaurant operations through well-defined business processes. Each workflow includes proper validation, error handling, and integration with other system components, ensuring reliable and consistent operation across all business functions. 🏆