# 🏗️ Bistro Restaurant SaaS - Backend Architecture & Business Logic Analysis

## 📋 Executive Summary

Bistro is a comprehensive offline-first restaurant management SaaS platform built with Next.js 15+ and PouchDB-CouchDB. The system provides complete restaurant functionality including point-of-sale, inventory management, staff scheduling, kitchen operations, and financial tracking with multi-platform support (web, desktop, mobile).

## 🎯 Core Business Model

### 🏢 Multi-Tenant SaaS Architecture
- **Restaurant-based tenancy**: Each restaurant is an isolated tenant with its own data
- **Authentication flow**: Two-tier authentication (restaurant → user)
- **Data isolation**: Complete data separation between restaurants
- **Scalable design**: Supports unlimited restaurants with centralized management

### 💰 Revenue Streams
- SaaS subscription model via bistro.icu
- Multi-restaurant support with centralized billing
- Offline-first capability reduces infrastructure costs
- Desktop/mobile apps provide premium value

## 🏛️ Database Architecture

### 📊 Data Storage Strategy
The system uses a **hybrid offline-first architecture**:

#### 🔄 PouchDB-CouchDB Sync System
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │  Desktop App    │    │   Web Client    │
│   (IndexedDB)   │◄──►│  (CouchDB)      │◄──►│   (IndexedDB)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────────────┐
                    │   Central CouchDB       │
                    │   (Cloud/On-premise)    │
                    └─────────────────────────┘
```

#### 🗃️ Document Types & Schemas
1. **Authentication Documents**
   - `restaurant`: Restaurant entity with credentials
   - `user`: Staff/admin users with role-based permissions

2. **Core Business Documents**
   - `order_document`: Orders with items, payments, delivery tracking
   - `menu_document`: Menu categories, items, pricing, sizes
   - `inventory_document`: Stock items, adjustments, purchase logs
   - `staff_document`: Employee records, schedules, attendance

3. **Financial Documents**
   - `cash_transaction`: All cash movements (sales, expenses, adjustments)
   - `payment_snapshot`: Staff payment calculations and history
   - `expense`: Business expenses with categorization

4. **Operational Documents**
   - `table_document`: Table management and status
   - `queue_document`: Kitchen queue and order tracking
   - `settings_document`: Restaurant configuration

## 🔐 Authentication & Authorization

### 🎫 Two-Tier Authentication System
```
1. Restaurant Authentication
   ├── Phone number + password
   ├── Creates restaurant session
   └── Enables user selection

2. User Authentication  
   ├── Username/email + password
   ├── Role-based permissions
   └── Full system access
```

### 👥 Role-Based Access Control (RBAC)
```typescript
interface Permissions {
  pages: PagePermission[];      // Page-level access
  components: ComponentPermission[]; // UI component access  
  actions: ActionPermission[];  // CRUD operation access
}
```

#### 🎭 Default Role Permissions
- **Owner/Admin**: Full access to all features
- **Manager**: Limited admin access, no sensitive settings
- **Staff**: POS operations, basic inventory viewing
- **Waiter**: Order taking, table management only

### 🛡️ Security Features
- **JWT-based sessions** with refresh tokens
- **Restriction system** for account suspension
- **Hierarchical restrictions** (owner restriction affects all users)
- **API permission validation** on every request
- **XSS protection** with input sanitization

## 💼 Core Business Logic

### 🛒 Order Management Workflow

#### 📝 Order Creation Process
```
1. Order Initialization
   ├── Generate unique ID (YYYYMMDD-XXX format)
   ├── Validate menu items and pricing
   ├── Calculate totals with discounts
   └── Create order document

2. Order Processing
   ├── Status: pending → preparing → served → completed
   ├── Kitchen queue integration
   ├── Real-time status updates
   └── Payment processing

3. Order Completion
   ├── Stock consumption calculation
   ├── COGS (Cost of Goods Sold) calculation
   ├── Profit margin analysis
   └── Financial transaction recording
```

#### 💳 Payment Processing
```typescript
interface PaymentFlow {
  methods: 'cash' | 'card' | 'online' | 'mixed';
  validation: {
    amountValidation: boolean;
    changeCalculation: boolean;
    partialPayments: boolean;
  };
  integration: {
    cashRegister: boolean;
    financialReporting: boolean;
    taxCalculation: boolean;
  };
}
```

### 🍽️ Menu Management System

#### 📋 Menu Structure
```
Menu Document
├── Categories[]
│   ├── Items[]
│   │   ├── Pricing by size
│   │   ├── Addons/supplements
│   │   └── Stock consumption rules
│   ├── Sizes[] (shared across category)
│   └── Category-specific settings
```

#### 🍕 Advanced Features
- **Custom pizza quarters**: Mix different pizzas in one order
- **Size-based pricing**: Flexible pricing per size
- **Supplement system**: Stock-consuming addons
- **Recipe integration**: COGS calculation per item

### 📦 Inventory Management

#### 📊 Stock Tracking System
```
Stock Item
├── Current quantity
├── Minimum threshold alerts
├── Purchase history
├── Consumption logs
└── Waste tracking
```

#### 🔄 Consumption Tracking
- **Automatic consumption** on order completion
- **Recipe-based calculations** for complex items
- **Supplement consumption** for addons
- **Packaging consumption** tracking
- **Waste logging** with reasons

#### 📈 Purchase & Supplier Management
- **Purchase order creation** with supplier integration
- **Cost tracking** for COGS calculations
- **Supplier performance** metrics
- **Automated reordering** based on thresholds

### 👥 Staff Management System

#### 📅 Scheduling & Attendance
```
Staff Management
├── Weekly schedule grid
├── Shift-based attendance
├── Real-time presence tracking
└── Payroll integration
```

#### 💰 Payment System
```typescript
interface StaffPayment {
  paymentTypes: {
    baseSalary: number;
    shiftRate: number;
    shiftRates: { [shiftId: string]: number };
  };
  balanceTracking: {
    advances: number;
    deductions: number;
    bonuses: number;
  };
  paymentCalculation: {
    strategy: 'weekly' | 'monthly' | 'per_shift';
    unpaidBalance: number;
    totalEarnings: number;
  };
}
```

### 🏪 Kitchen Operations

#### 🍳 Queue Management
```
Kitchen Queue System
├── Order reception
├── Item-level tracking
├── Station-based queues
├── Completion notifications
└── Performance metrics
```

#### 🖨️ Printing System
- **Kitchen ticket printing** with thermal printers
- **Receipt printing** for customers
- **Barcode generation** for order tracking
- **Multi-station printing** support

## 💰 Financial Management

### 💵 Cash Register System
```typescript
interface CashRegister {
  transactions: {
    sales: OrderPayment[];
    manual_in: CashDeposit[];
    manual_out: Expense[];
  };
  balance: {
    opening: number;
    current: number;
    expected: number;
  };
  reconciliation: {
    counted: number;
    difference: number;
    notes: string;
  };
}
```

### 📊 Financial Reporting
- **Real-time cash balance** calculation
- **Daily sales summaries** with profit margins
- **Expense tracking** by category
- **COGS analysis** per order/item
- **Staff payment tracking** and history

### 🚚 Delivery Management
```typescript
interface DeliverySystem {
  drivers: {
    staff: StaffDriver[];
    freelance: FreelanceDriver[];
  };
  tracking: {
    status: 'pending' | 'out_for_delivery' | 'delivered';
    attempts: DeliveryAttempt[];
    collection: CollectionStatus;
  };
  payment: {
    freelanceRates: number;
    collectionModel: boolean;
    expenseTracking: boolean;
  };
}
```

## 🔄 Synchronization System

### 🌐 Multi-Device Sync
```
Autonomous Sync Manager
├── Local network discovery
├── Internet fallback sync
├── Conflict resolution
├── Real-time updates
└── Offline operation
```

#### 🔍 Discovery Mechanisms
1. **Local Network**: mDNS/Bonjour discovery of CouchDB instances
2. **Internet Fallback**: Proxy-based sync through central server
3. **Device Registration**: Automatic peer discovery and connection
4. **Performance Optimization**: Cached server lists and smart routing

#### ⚡ Conflict Resolution
- **Automatic conflict resolution** for most document types
- **Last-write-wins** with timestamp comparison
- **Manual resolution** for critical conflicts
- **Revision tracking** for audit trails

## 🏗️ Technical Architecture

### 🖥️ Multi-Platform Support
```
Platform Architecture
├── Web (Next.js)
│   ├── Landing page only
│   └── Authentication portal
├── Desktop (Electron)
│   ├── Full restaurant functionality
│   ├── Bundled CouchDB
│   └── Auto-updater system
└── Mobile (Capacitor)
    ├── POS interface
    ├── IndexedDB storage
    └── Native integrations
```

### 🔧 Build System
```bash
# Development
npm run dev                    # Web development
npm run electron:dev          # Desktop development
npm run cap:dev:android       # Android development

# Production Builds
npm run build:electron        # Desktop app
npm run build:mobile          # Mobile app
npm run deploy:windows        # Windows deployment
npm run deploy:macos          # macOS deployment
```

### 📱 Mobile Optimizations
- **Touch-friendly UI** with 44px minimum touch targets
- **Offline-first design** with local data storage
- **Native hardware integration** (barcode scanning, printing)
- **Performance optimization** for low-end devices

## 🎯 Business Intelligence

### 📈 Analytics & Reporting
```typescript
interface Analytics {
  sales: {
    dailyTotals: number;
    itemPerformance: ItemSales[];
    categoryAnalysis: CategorySales[];
    profitMargins: ProfitAnalysis;
  };
  operations: {
    orderVolume: number;
    averageOrderValue: number;
    kitchenPerformance: KitchenMetrics;
    staffProductivity: StaffMetrics;
  };
  financial: {
    cashFlow: CashFlowData;
    expenseAnalysis: ExpenseBreakdown;
    profitLoss: ProfitLossStatement;
  };
}
```

### 🎲 Data-Driven Insights
- **Menu optimization** based on sales data
- **Staff scheduling** optimization
- **Inventory forecasting** with demand prediction
- **Customer behavior** analysis
- **Profit margin** optimization recommendations

## 🔒 Data Security & Compliance

### 🛡️ Security Measures
- **Data encryption** at rest and in transit
- **Access logging** for audit trails
- **Regular backups** with point-in-time recovery
- **GDPR compliance** with data anonymization
- **PCI DSS considerations** for payment data

### 📋 Backup & Recovery
```
Backup Strategy
├── Real-time sync to multiple nodes
├── Daily automated backups
├── Point-in-time recovery
├── Cross-platform data export
└── Disaster recovery procedures
```

## 🚀 Scalability & Performance

### ⚡ Performance Optimizations
- **Lazy loading** of non-critical components
- **Database indexing** for fast queries
- **Caching strategies** for frequently accessed data
- **Background sync** to minimize UI blocking
- **Memory management** for long-running sessions

### 📊 Scalability Features
- **Horizontal scaling** with multiple CouchDB nodes
- **Load balancing** for high-traffic restaurants
- **Resource monitoring** and auto-scaling
- **Multi-region deployment** support

## 🔮 Future Roadmap

### 🎯 Planned Features
- **AI-powered demand forecasting**
- **Advanced analytics dashboard**
- **Third-party integrations** (accounting, delivery platforms)
- **Multi-language support**
- **Advanced reporting** with custom dashboards
- **API ecosystem** for third-party developers

### 🌟 Innovation Areas
- **Machine learning** for menu optimization
- **IoT integration** for smart kitchen equipment
- **Blockchain** for supply chain transparency
- **Voice ordering** integration
- **Augmented reality** for menu visualization

---

## 📝 Conclusion

Bistro represents a sophisticated, production-ready restaurant management platform that successfully balances feature richness with operational simplicity. The offline-first architecture ensures reliability, while the comprehensive business logic covers all aspects of restaurant operations from order taking to financial reporting.

The system's strength lies in its **holistic approach** - rather than being just a POS system, it's a complete business management platform that understands the interconnected nature of restaurant operations. Every component is designed to work together, creating a seamless experience for restaurant owners and staff.

The **technical architecture** is robust and scalable, supporting everything from single-location restaurants to multi-location chains, while the **business logic** is comprehensive enough to handle complex scenarios like custom pizza orders, freelance delivery management, and sophisticated inventory tracking.

This analysis reveals a mature, well-architected system that could compete effectively in the restaurant management software market. 🏆