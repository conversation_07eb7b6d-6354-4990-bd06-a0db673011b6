# Order Management Workflow - Complete Investigation Report

## Overview

This restaurant management system implements a comprehensive order management workflow with multi-platform support (web, desktop, mobile), offline-first capabilities, and real-time synchronization. The system handles the complete order lifecycle from creation to completion, including table management, kitchen operations, and payment processing.

## Order Lifecycle & States

### Order Status States

The system uses a validated status transition model defined in `/Users/<USER>/Desktop/rest/shop/lib/db/v4/utils/order-status-validation.ts`:

```typescript
type OrderStatus = 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';
```

#### Status Transition Flow

1. **pending** → `served`, `completed`, `cancelled`
   - Order is being cooked/prepared in kitchen
   - Kitchen staff is actively working on the order

2. **preparing** → `served`, `completed`, `cancelled` 
   - Legacy status, functionally equivalent to pending
   - Maintained for backward compatibility

3. **served** → `completed`, `cancelled`
   - Food is ready and served to customer
   - Awaiting payment processing

4. **completed** → Terminal state
   - Payment processed and order finalized
   - No further transitions allowed

5. **cancelled** → Terminal state
   - Order was cancelled at any stage
   - No further transitions allowed

### Order Types

Defined in `/Users/<USER>/Desktop/rest/shop/lib/types/order-types.ts`:

- **dine-in** (Sur Place): Table service orders requiring table assignment
- **takeaway** (À Emporter): Customer pickup orders
- **delivery** (Livraison): Orders delivered to customer address

Legacy support for `table` → `dine-in` and `takeout` → `takeaway`.

## Core Components & Architecture

### 1. Order Schema (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/schemas/order-schema.ts`)

The system uses a comprehensive order document structure:

```typescript
interface OrderDocument {
  _id: string;                    // Format: order:YYYYMMDD-XXX
  type: 'order_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  
  // Core Order Data
  tableId: string;
  status: OrderStatus;
  orderType: AllOrderTypes;
  items: OrderItem[];
  total: number;
  
  // Pricing & Discounts
  subtotal?: number;
  discountType?: 'percentage' | 'fixed_amount';
  discountValue?: number;
  discountAmount?: number;
  discountReason?: string;
  
  // Customer Information
  customer?: Customer;
  deliveryPerson?: DeliveryPerson;
  
  // Payment Tracking
  paymentStatus?: 'unpaid' | 'paid' | 'partially_paid';
  paymentMethod?: 'cash' | 'card' | 'online' | 'mixed';
  paymentDetails?: PaymentDetails;
  
  // Financial Metrics
  totalCogs?: number;             // Cost of goods sold
  grossProfit?: number;           // total - totalCogs
  profitMargin?: number;          // (grossProfit / total) * 100
  
  // Audit Trail
  createdBy?: string;
  createdByName?: string;
  
  // Void Management
  hasVoids?: boolean;
  totalVoidedAmount?: number;
  originalTotal?: number;
  voidHistory?: VoidEntry[];
  
  // Enhanced Delivery
  deliveryStatus?: 'pending' | 'out_for_delivery' | 'delivered' | 'failed' | 'partially_delivered';
  deliveryAttempts?: DeliveryAttempt[];
  collectionStatus?: CollectionStatus;
}
```

### 2. Order Operations (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-ops.ts`)

Key database operations:

#### Order ID Generation
- Format: `order:YYYYMMDD-XXX` (e.g., `order:************`)
- Business day calculation with 5 AM reset hour
- Daily sequence numbering with zero-padding

#### Core CRUD Operations
- `createOrder()`: Creates new order with auto-generated ID
- `getOrder()`: Retrieves order by ID
- `getAllOrders()`: Fetches all orders with sorting
- `getActiveOrders()`: Gets pending/preparing/served orders
- `updateOrder()`: Updates order with conflict resolution
- `deleteOrder()`: Removes order from database

#### Specialized Operations
- `updateOrderStatus()`: Status updates with validation and COGS calculation
- `voidOrderItems()`: Handles item voiding with audit trail
- `updateDeliveryStatus()`: Enhanced delivery tracking
- `processDeliveryFailure()`: Handles failed deliveries with waste tracking
- `updateCollectionStatus()`: Collection tracking for delivery orders

### 3. Order Hook (`/Users/<USER>/Desktop/rest/shop/lib/hooks/use-order-v4.ts`)

React hook providing order management functionality:

```typescript
interface UseOrderReturn {
  orders: Order[];                 // All orders
  activeOrders: Order[];          // Non-completed orders
  isLoading: boolean;
  error: Error | null;
  syncState: SyncState;
  isReady: boolean;
  
  // Operations
  refreshOrders: () => Promise<void>;
  createOrder: (order: NewOrder) => Promise<Order>;
  updateOrder: (orderId: string, updates: Partial<OrderDocument>) => Promise<Order>;
  getOrder: (orderId: string) => Promise<Order | null>;
  deleteOrder: (orderId: string) => Promise<void>;
}
```

Features:
- Real-time order synchronization
- Conflict resolution for concurrent updates
- Event-driven updates via window events
- Automatic periodic refresh (30s intervals)
- Optimized queries with database indexing

## User Interface Components

### 1. Order Creation Interface (`/Users/<USER>/Desktop/rest/shop/app/components/NewOrderingInterface.tsx`)

Primary order creation component featuring:
- Menu item selection with categorization
- Size and addon configuration
- Pizza quarter customization
- Customer information capture
- Table assignment for dine-in orders
- Delivery person assignment
- Real-time total calculation
- Order preview and editing

### 2. Order Management (`/Users/<USER>/Desktop/rest/shop/app/components/WaiterOrderList.tsx`)

Waiter interface for order management:
- Filterable order list (status-based)
- Real-time search functionality
- Order status updates
- Table switching capabilities
- Order editing integration
- Customer information display

### 3. Kitchen Display (`/Users/<USER>/Desktop/rest/shop/components/KitchenDisplay.tsx`)

Kitchen workflow interface:
- Real-time active order display
- Status transition controls
- Order timing and priority
- Item-level completion tracking
- Kitchen printing integration
- Auto-refresh capabilities

### 4. Table Management (`/Users/<USER>/Desktop/rest/shop/components/tables/IntegratedTableManager.tsx`)

Table configuration and management:
- Table creation and configuration
- Seating capacity management
- Packaging configuration per table
- Table status tracking

## Business Logic & Services

### 1. Order Finance (`/Users/<USER>/Desktop/rest/shop/lib/services/simplified-order-finance.ts`)

Comprehensive payment processing service:

```typescript
interface OrderPaymentResult {
  orderId: string;
  success: boolean;
  error?: string;
  registeredInCaisse: boolean;
  sessionActivated?: boolean;
  orderUpdated?: boolean;
}
```

Process flow:
1. COGS calculation and consumption log creation
2. Order status update to completed/paid
3. Cash session integration
4. Transaction rollback capability
5. Financial metrics calculation

### 2. Kitchen Print Service (`/Users/<USER>/Desktop/rest/shop/lib/services/kitchen-print-service.ts`)

Automated kitchen order printing:
- Order receipt generation
- Kitchen ticket printing
- Delivery-specific formatting
- Auto-print on order creation
- Print queue management

### 3. Order Completion Tracking (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-completion-ops.ts`)

Barcode-based order completion:
- Item-level completion tracking
- Barcode generation and parsing
- Station-based workflow
- Quality control integration

## Integration Points

### 1. Inventory Management
- Real-time stock consumption tracking
- COGS calculation for profitability
- Waste management for failed deliveries
- Recipe-based ingredient tracking

### 2. Staff Management
- Role-based access control
- Staff attribution for orders
- Performance tracking
- Shift-based reporting

### 3. Financial System
- Cash session integration
- Transaction logging
- Payment method tracking
- Revenue recognition

### 4. Printing System
- Kitchen order printing
- Customer receipts
- Delivery documentation
- Barcode label generation

## API Endpoints & Data Flow

### Order Creation Flow
1. User creates order via `NewOrderingInterface`
2. Order validation and sanitization
3. Database insertion via `createOrder()`
4. Event dispatch for real-time updates
5. Kitchen print service activation
6. Inventory consumption logging

### Order Status Updates
1. Status change request via UI component
2. Status transition validation
3. Business logic execution (COGS, consumption)
4. Database update with conflict resolution
5. Event propagation to connected clients
6. Print service integration for status changes

### Payment Processing
1. Payment initiation via order finance service
2. COGS calculation and consumption logging
3. Order status update to completed
4. Cash session transaction creation
5. Financial metrics calculation
6. Receipt generation and printing

## Business Rules & Validation

### Order Validation Rules
- Table requirement for dine-in orders
- Customer information requirements by order type:
  - Delivery: Phone and address required
  - Takeaway: Phone optional
  - Dine-in: Customer info optional
- Status transition validation per business workflow
- Item quantity and pricing validation

### Financial Rules
- COGS calculation on order completion
- Profit margin tracking
- Discount application and audit
- Void tracking with reason codes
- Collection status for delivery orders

### Inventory Rules
- Stock consumption on order completion
- Waste tracking for failed deliveries
- Recipe-based consumption calculation
- Stock availability validation

## Data Synchronization & Offline Support

### Offline-First Architecture
- PouchDB local storage with CouchDB sync
- Conflict resolution for concurrent updates
- Queue management for offline operations
- Automatic retry mechanisms

### Real-time Updates
- Event-driven architecture with window events
- WebSocket-like behavior via PouchDB changes
- Multi-tab synchronization
- Cross-device order visibility

### Conflict Resolution
- Revision-based document versioning
- Automatic retry with exponential backoff
- Safe update patterns with rollback capability
- Transaction isolation for payment processing

## Key File References

### Core Schema & Types
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/schemas/order-schema.ts` (Lines 1-516)
- `/Users/<USER>/Desktop/rest/shop/lib/types/order-types.ts` (Lines 1-161)
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/utils/order-status-validation.ts` (Lines 1-110)

### Database Operations
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-ops.ts` (Lines 250-1311)
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-completion-ops.ts` (Lines 1-100)

### React Hooks & Services
- `/Users/<USER>/Desktop/rest/shop/lib/hooks/use-order-v4.ts` (Lines 1-443)
- `/Users/<USER>/Desktop/rest/shop/lib/services/simplified-order-finance.ts` (Lines 1-150)

### UI Components
- `/Users/<USER>/Desktop/rest/shop/app/components/NewOrderingInterface.tsx` (Lines 1-100)
- `/Users/<USER>/Desktop/rest/shop/app/components/WaiterOrderList.tsx` (Lines 1-100)
- `/Users/<USER>/Desktop/rest/shop/components/KitchenDisplay.tsx` (Lines 1-100)
- `/Users/<USER>/Desktop/rest/shop/components/tables/IntegratedTableManager.tsx` (Lines 1-100)

### Application Pages
- `/Users/<USER>/Desktop/rest/shop/app/(protected)/ordering/page.tsx` (Lines 1-85)
- `/Users/<USER>/Desktop/rest/shop/app/(protected)/orders/page.tsx` (Lines 1-24)

## Performance & Scalability Considerations

### Database Indexing
- Compound indexes for order queries
- Status and date-based filtering optimization
- Table-specific order retrieval
- Payment status indexing

### Query Optimization
- Lazy loading for large order sets
- Pagination support
- Filtered result sets
- Background data synchronization

### Memory Management
- Component-level state isolation
- Event listener cleanup
- Periodic cache invalidation
- Efficient re-rendering patterns

## Security & Audit Trail

### Access Control
- Role-based component access
- Operation-level permissions
- Staff attribution tracking
- Session-based authentication

### Audit Capabilities
- Complete void history tracking
- Status change attribution
- Payment audit trail
- Delivery attempt logging
- Collection discrepancy tracking

### Data Integrity
- Schema validation at runtime
- Required field enforcement
- Status transition validation
- Financial calculation verification

This comprehensive order management system provides a robust foundation for restaurant operations with full lifecycle support, real-time capabilities, and extensive audit trails.