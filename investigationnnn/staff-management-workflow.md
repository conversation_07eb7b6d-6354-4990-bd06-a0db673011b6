# Staff Management Workflow Documentation

## Overview

This document provides a comprehensive analysis of the Staff Management system in the restaurant management application. The system is built with an offline-first architecture using PouchDB/CouchDB for local data storage and MongoDB for authentication, supporting multiple platforms (web, mobile, desktop).

## Architecture Overview

### Multi-Database Architecture
- **PouchDB/CouchDB**: Local staff data, schedules, attendance, and operational data
- **MongoDB**: Authentication credentials and user management
- **Offline-First**: Full functionality without internet connection
- **Multi-Platform**: Web, Electron desktop, and Capacitor mobile apps

### Core File Structure
```
/app/(protected)/staff/               - Staff management UI
/lib/services/staff-service.ts        - Main staff service layer
/lib/services/staff-auth-service.ts   - Authentication service  
/lib/types/staff.ts                   - Staff type definitions
/lib/schemas/unified-staff-schema.ts  - Unified schema definitions
/components/staff/                    - Staff UI components
/app/api/staff/                       - Staff API endpoints
```

## 1. Staff Registration and Profile Management

### Staff Creation Process

**Main Entry Point**: `/app/(protected)/staff/page.tsx` (lines 865-1060)

**Workflow**:
1. **Form Submission** via `SimpleStaffForm` component
2. **Data Validation** using unified schema validation
3. **UUID Generation** for consistent ID across databases
4. **Local Storage** in PouchDB using `staff-service.ts`
5. **Optional Authentication** creation via MongoDB

**Key Implementation** (`/app/(protected)/staff/page.tsx`, lines 865-1046):
```typescript
const handleAddStaff = async (data: StaffFormData) => {
  // Generate UUID for both auth and staff documents
  let staffId = uuidv4();
  
  // Use unified-create API for complete staff creation
  const response = await fetch('/api/staff/unified-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(requestData)
  });
}
```

### Data Storage Structure

**Staff Document Schema** (`/lib/schemas/unified-staff-schema.ts`, lines 191-210):
```typescript
export interface StaffDocument {
  _id: string; // Format: staff:{uuid}
  type: 'staff';
  id: string; // UUID v4
  name: string;
  role: StaffRole;
  contact?: string;
  email?: string;
  phone?: string;
  status: StaffStatus;
  paymentConfig: PaymentConfig;
  userId?: string;
  hasUserAccount: boolean;
  username?: string;
  permissions: StaffPermissions;
}
```

**Supported Staff Roles** (`/lib/types/staff.ts`, lines 8-17):
- MANAGER
- CHEF  
- WAITER
- BARTENDER
- HOST
- KITCHEN_HELPER
- CASHIER
- CLEANER
- DELIVERY

### Profile Management Features

**Update Process** (`/app/(protected)/staff/page.tsx`, lines 1063-1159):
- Real-time form updates
- Schedule modification support
- Payment configuration changes
- Permission updates
- Contact information management

## 2. Role-Based Permissions and Access Control

### Permission System Architecture

**Permission Types** (`/lib/hooks/use-permissions.ts`, lines 7-33):
- **Page Permissions**: Access to main application sections
- **Tab Permissions**: Fine-grained access within pages
- **Owner Override**: Full access for restaurant owners

**Permission Structure** (`/lib/hooks/use-permissions.ts`, lines 40-98):
```typescript
const DEFAULT_PERMISSIONS: UserPermissions = {
  pages: {
    menu: false,
    orders: false,
    finance: false,
    analytics: false,
    inventory: false,
    staff: false,
    settings: false,
    suppliers: false,
  },
  tabs: {
    inventory: {
      inventory: false,
      subrecipes: false,
      counts: false,
      waste: false,
    },
    staff: {
      shifts_schedule: false,
      attendance: false,
      payments: false,
    }
  }
};
```

### Authentication System

**Multi-Stage Authentication Process**:

1. **MongoDB Authentication** (`/app/api/auth/staff/login/route.ts`, lines 36-71):
   - Username/password verification
   - Role-based access control
   - JWT token generation

2. **PouchDB Permission Loading** (`/app/api/auth/staff/login/route.ts`, lines 74-100):
   - Staff-specific permissions from local database
   - Offline-capable permission storage
   - Non-blocking permission retrieval

**Staff Authentication Creation** (`/app/api/staff/auth/create/route.ts`, lines 32-164):
```typescript
export async function POST(request: NextRequest) {
  // Validate staff data
  const validation = createAuthSchema.safeParse(body);
  
  // Create MongoDB user
  const mongoUserResult = await createMongoUser({
    name: name || '',
    username,
    plaintextPassword: password,
    role: normalizedRole,
    restaurantId: cleanedRestaurantId,
    staffIdToLink: staffId
  });
}
```

### Role Utilities (`/lib/auth/role-utils.ts`):
- `isAdmin(user)`: Admin role checking
- `isOwner(user)`: Owner role verification  
- `canManageStaff(user)`: Staff management permissions
- `canManageSettings(user)`: Settings access control

## 3. Shift Scheduling and Time Tracking

### Shift Management System

**Shift Operations** (`/lib/db/v4/operations/shift-ops.ts`):

**Shift Creation** (lines 58-96):
```typescript
export async function createShift(
  name: string,
  startTime: string,
  endTime: string,
  color?: string
): Promise<ShiftsDocument['shifts'][0]> {
  const newShift = {
    id: uuidv4(),
    name,
    startTime,
    endTime,
    ...(color ? { color } : {})
  };
  
  // Update shifts document with conflict resolution
  const updatedDoc = await safeUpdateDocument<ShiftsDocument>(
    'shifts',
    (shiftsDoc) => ({
      ...shiftsDoc,
      shifts: [...shiftsDoc.shifts, newShift],
      updatedAt: new Date().toISOString()
    }),
    `createShift(${name})`
  );
}
```

### Schedule Management

**Weekly Schedule Structure** (`/lib/types/staff.ts`, lines 38-46):
```typescript
export interface WeeklySchedule {
  monday: string[];
  tuesday: string[];
  wednesday: string[];
  thursday: string[];
  friday: string[];
  saturday: string[];
  sunday: string[];
}
```

**Schedule Update Process** (`/lib/services/staff-service.ts`, lines 399-446):
```typescript
updateStaffWeeklySchedule: async (staffId: string, weeklySchedule: WeeklySchedule) => {
  // Save to database using per-staff schedule operations
  const schedule = await setStaffSchedule(staffId, {
    weeklySchedule: weeklySchedule,
    effectiveFrom: new Date().toISOString(),
    isActive: true,
  });
  
  // Backup to localStorage for offline access
  const schedules = staffService.loadSchedulesFromStorage();
  schedules[staffId] = {
    staffId: staffId,
    weeklySchedule: weeklySchedule,
    effectiveFrom: new Date().toISOString(),
    isActive: true,
  };
  staffService.saveSchedulesToStorage(schedules);
}
```

### Attendance Tracking

**Attendance Recording** (`/lib/services/staff-service.ts`, lines 326-389):
```typescript
recordShiftAttendance: async (
  staffId: string,
  shiftId: string,
  attendanceData: {
    date: string;
    status: 'present' | 'late' | 'absent';
    shiftName?: string;
    notes?: string;
  }
): Promise<AttendanceRecord> => {
  // Record in database
  const dbResult = await dbRecordStaffAttendance(
    staffId,
    attendanceData.date,
    attendanceData.status,
    shiftId,
    attendanceData.shiftName,
    attendanceData.notes
  );
  
  // Auto-sync with staff allowance system
  if (attendanceData.status === 'present' || attendanceData.status === 'late') {
    await markStaffPresent(
      staffId, 
      staffMember.name, 
      shiftId, 
      attendanceData.shiftName || 'Shift', 
      attendanceData.date
    );
  }
}
```

**Attendance Document Schema** (`/lib/schemas/unified-staff-schema.ts`, lines 225-234):
```typescript
export interface AttendanceDocument {
  _id: string; // Format: attendance:{uuid}
  type: 'attendance';
  staffId: string;
  records: AttendanceRecord[];
  schemaVersion: string;
  createdAt: string;
  updatedAt: string;
}
```

## 4. Staff Payment and Payroll Workflows

### Payment Configuration System

**Payment Types** (`/lib/types/staff.ts`, lines 19-27):
```typescript
export type PaymentType = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'PER_SHIFT';

export interface PaymentConfig {
  type: PaymentType;
  baseSalary: number;
  shiftRate?: number;
  shiftRates?: Record<string, number>; // Specific rates per shift ID
  paymentDay?: number; // Day of month/week for payments
  nextPaymentDueDate?: string;
}
```

### Payment System Interface

**Staff Payment System** (`/components/staff/payment/StaffPaymentSystem.tsx`):
- **Staff Selection**: Choose staff member for payment
- **Payment Mode**: Salary, per-shift, bonus, advance, deduction
- **Payment Processing**: Real-time payment calculation
- **Payment History**: Comprehensive payment tracking

**Payment Modes** (lines 14-15):
```typescript
export type PaymentMode = 'salary' | 'per-shift' | 'bonus' | 'advance' | 'deduction';
```

### Payment Document Schema

**Payment Storage** (`/lib/schemas/unified-staff-schema.ts`, lines 236-316):
```typescript
export interface PaymentDocument {
  _id: string; // Format: payment:YYYY-MM-DD-{uuid}
  type: 'payment';
  staffId: string;
  amount: number;
  paymentType: 'SALARY' | 'BONUS' | 'ADVANCE' | 'DEDUCTION' | 'SHIFT_PAYMENT';
  paymentDate: string;
  status: PaymentStatus;
  metadata?: {
    paidAttendanceIds?: string[];
    baseLogic?: BaseLogic;
    shiftBreakdown?: Array<{
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
      amount: number;
    }>;
    calculationBreakdown?: {
      baseSalary: number;
      shiftEarnings: number;
      bonusAmount: number;
      deductionAmount: number;
      finalAmount: number;
    };
  };
}
```

### Balance Management System

**Staff Balance Tracking** (`/lib/schemas/unified-staff-schema.ts`, lines 318-332):
```typescript
export interface BalanceDocument {
  _id: string; // Format: staff_balance:YYYY-MM-DD-{uuid}
  type: 'staff_balance';
  staffId: string;
  balanceType: BalanceType; // 'ADVANCE' | 'DEDUCTION' | 'BONUS'
  amount: number;
  reason: string;
  date: string;
  isUsed: boolean;
  usedInPaymentId?: string;
}
```

## 5. Performance Tracking and Management

### Performance Metrics

**Attendance Analytics**:
- Total hours worked per week/month
- Attendance rate calculation
- Shift completion tracking
- Late arrival tracking

**Performance Indicators** (`/lib/types/staff.ts`, lines 56-62):
```typescript
export interface StaffPresence {
  staffId: string;
  isPresent: boolean;
  attendanceHistory: AttendanceRecord[];
  totalHoursThisWeek: number;
  totalHoursThisMonth: number;
}
```

### Data Loading and Performance

**Staff with Schedules Loading** (`/lib/services/staff-service.ts`, lines 141-239):
```typescript
getStaffWithSchedules: async (): Promise<StaffMember[]> => {
  // Get basic staff documents
  const v4Staff = await getAllStaffMembers();
  
  // Load schedule for each staff member
  const staffWithSchedules = await Promise.all(
    v4Staff.map(async (staff) => {
      // Load schedule from database
      const scheduleData = await getStaffSchedule(staff.id);
      
      // Load attendance history
      const attendanceDoc = await dbGetStaffAttendance(staff.id);
      
      return convertedStaff;
    })
  );
}
```

## 6. Authentication and Session Management

### Multi-Stage Authentication Flow

1. **Initial Authentication** (`/app/api/auth/staff/login/route.ts`):
   - MongoDB credential verification
   - JWT token generation
   - Restaurant context establishment

2. **Permission Loading** (`/lib/hooks/use-permissions.ts`):
   - PouchDB permission retrieval
   - Role-based access control
   - Offline permission caching

3. **Session Management**:
   - JWT token refresh
   - Offline session persistence
   - Multi-device synchronization

### Staff Authentication Service

**Auth Creation Process** (`/lib/services/staff-auth-service.ts`, lines 21-201):
```typescript
export async function createStaffAuth(
  staffMember: StaffDocument,
  authData: {
    username: string;
    password: string;
  }
): Promise<{
  success: boolean;
  userId?: string;
  error?: string;
  offlineError?: boolean;
}> {
  // Validate staff member and auth data
  // Determine restaurant context
  // Create MongoDB authentication user
  // Update staff document with auth info
}
```

## 7. Data Synchronization and Offline Support

### Offline-First Architecture

**Key Features**:
- Full offline functionality
- Automatic sync when online
- Conflict resolution for concurrent updates
- Local data persistence

**Sync Mechanisms**:
- PouchDB-CouchDB replication
- MongoDB authentication sync
- Schedule backup to localStorage
- Attendance history caching

### Database Initialization

**V4 Database Setup** (`/app/(protected)/staff/page.tsx`, lines 616-703):
```typescript
useEffect(() => {
  const loadStaffData = async () => {
    // Initialize V4 database with restaurant context
    const restaurantId = getCurrentRestaurantId();
    await initializeV4Database(restaurantId);
    
    // Load staff with schedules
    const staffWithSchedules = await staffService.getStaffWithSchedules();
    setStaffList(adaptedStaff);
  };
}, [isAuthenticated]);
```

## 8. API Endpoints and Integration Points

### Key API Endpoints

**Staff Management APIs**:
- `POST /api/staff/unified-create` - Complete staff creation with auth
- `POST /api/staff/auth/create` - Authentication credential creation
- `POST /api/auth/staff/login` - Staff login endpoint
- `GET /api/admin/staff` - Admin staff overview

**Permission Management**:
- `GET /api/staff/permissions` - Retrieve staff permissions
- `PUT /api/staff/permissions/[staffId]` - Update staff permissions

### Integration with External Systems

**Authentication Integration**:
- MongoDB user management
- JWT token system
- Multi-restaurant support

**Data Storage Integration**:
- PouchDB for offline operations
- CouchDB for data persistence
- localStorage for backup storage

## 9. Security Considerations

### Data Protection

**Authentication Security**:
- Password hashing using bcrypt
- JWT token-based sessions
- Role-based access control
- Restaurant-specific data isolation

**Permission System**:
- Granular page/tab permissions
- Owner privilege override
- Staff-specific access controls
- Offline permission caching

### Offline Security

**Local Data Protection**:
- Client-side permission enforcement
- Secure local storage practices
- Data sync validation
- Conflict resolution security

## 10. Mobile and Multi-Platform Support

### Platform Compatibility

**Supported Platforms**:
- Web browsers (responsive design)
- Electron desktop applications
- Capacitor mobile apps (iOS/Android)
- Progressive Web App (PWA)

**Platform-Specific Features**:
- Offline-first operation on all platforms
- Platform-aware HTTP client
- Device-specific optimizations
- Cross-platform data synchronization

## Conclusion

The Staff Management system provides a comprehensive solution for restaurant staff operations with robust offline support, multi-platform compatibility, and detailed role-based access control. The system's architecture prioritizes data consistency, user experience, and operational reliability across all supported platforms.

The implementation demonstrates best practices for offline-first applications, with careful attention to data synchronization, conflict resolution, and user permission management. The modular design allows for easy maintenance and feature extension while maintaining backward compatibility.