# 🔄 Bistro Restaurant SaaS - Business Logic Workflows

## 📋 Overview

This document details the core business workflows and logic patterns in the Bistro restaurant management system. Each workflow represents a critical business process with specific rules, validations, and data transformations.

## 🛒 Order Management Workflows

### 📝 Order Creation Workflow

```mermaid
graph TD
    A[🎯 Start Order] --> B[📋 Select Order Type]
    B --> C{🏪 Order Type?}
    C -->|Dine-in| D[🪑 Select Table]
    C -->|Takeaway| E[👤 Customer Info]
    C -->|Delivery| F[🚚 Delivery Details]
    
    D --> G[🍽️ Add Menu Items]
    E --> G
    F --> G
    
    G --> H[💰 Calculate Totals]
    H --> I[💸 Apply Discounts?]
    I -->|Yes| J[🏷️ Apply Discount]
    I -->|No| K[✅ Finalize Order]
    J --> K
    
    K --> L[💾 Save to Database]
    L --> M[🖨️ Print Kitchen Ticket]
    M --> N[📊 Update Analytics]
    N --> O[🎉 Order Created]
```

#### 🎯 Business Rules
- **Order ID Generation**: `YYYYMMDD-XXX` format based on business day (5 AM reset)
- **Table Validation**: Ensure table exists and is available for dine-in orders
- **Menu Item Validation**: Verify items exist, are available, and pricing is current
- **Stock Checking**: Optional pre-order stock validation for critical items
- **Discount Rules**: Percentage or fixed amount, with reason tracking
- **Minimum Order**: Configurable minimum order amounts by order type

#### 💡 Key Features
- **Custom Pizza Quarters**: Mix different pizza types in single order
- **Supplement Integration**: Automatic stock consumption for addons
- **Real-time Pricing**: Dynamic pricing based on size and addons
- **Multi-currency Support**: Configurable currency and tax rates

### 🔄 Order Status Progression

```mermaid
stateDiagram-v2
    [*] --> pending: Order Created
    pending --> preparing: Kitchen Accepts
    preparing --> served: Food Ready
    served --> completed: Payment Processed
    
    pending --> cancelled: Order Cancelled
    preparing --> cancelled: Kitchen Cancels
    
    completed --> [*]: Order Archived
    cancelled --> [*]: Order Archived
    
    note right of preparing: Kitchen Queue Integration
    note right of completed: Stock Consumption & COGS
```

#### 🎯 Status Transition Rules
- **Pending → Preparing**: Kitchen accepts order, items added to queue
- **Preparing → Served**: All items marked complete in kitchen queue
- **Served → Completed**: Payment processed, stock consumed, COGS calculated
- **Any → Cancelled**: Order voided, stock returned, refund processed

### 💳 Payment Processing Workflow

```mermaid
graph TD
    A[💰 Process Payment] --> B{💳 Payment Method?}
    B -->|Cash| C[💵 Cash Payment]
    B -->|Card| D[💳 Card Payment]
    B -->|Mixed| E[🔄 Mixed Payment]
    
    C --> F[💸 Calculate Change]
    D --> G[✅ Card Validation]
    E --> H[📊 Split Amounts]
    
    F --> I[💾 Record Transaction]
    G --> I
    H --> I
    
    I --> J[📈 Update Cash Register]
    J --> K[🧾 Generate Receipt]
    K --> L[📊 Update Analytics]
    L --> M[✅ Payment Complete]
    
    M --> N[🍽️ Consume Stock]
    N --> O[💰 Calculate COGS]
    O --> P[📊 Update Profit Metrics]
```

#### 🎯 Payment Business Rules
- **Amount Validation**: Must match order total or be partial payment
- **Change Calculation**: Automatic change calculation for cash payments
- **Partial Payments**: Support for multiple payment installments
- **Refund Processing**: Automatic refund calculation for cancelled orders
- **Cash Register Integration**: All payments update cash register balance
- **Receipt Generation**: Automatic receipt printing with order details

## 🍽️ Menu Management Workflows

### 📋 Menu Structure Management

```mermaid
graph TD
    A[🍽️ Menu Management] --> B[📂 Category Management]
    A --> C[🍕 Item Management]
    A --> D[📏 Size Management]
    A --> E[🧩 Addon Management]
    
    B --> B1[➕ Add Category]
    B --> B2[✏️ Edit Category]
    B --> B3[🗑️ Delete Category]
    
    C --> C1[➕ Add Menu Item]
    C --> C2[✏️ Edit Item Details]
    C --> C3[💰 Update Pricing]
    C --> C4[🗑️ Remove Item]
    
    D --> D1[📏 Add Size Option]
    D --> D2[🔄 Rename Size]
    D --> D3[❌ Remove Size]
    
    E --> E1[🧩 Create Addon]
    E --> E2[📦 Link to Stock]
    E --> E3[💰 Set Pricing]
```

#### 🎯 Menu Business Rules
- **Category Hierarchy**: Categories contain items, sizes are category-wide
- **Size-based Pricing**: Each item can have different prices per size
- **Addon Integration**: Addons can consume stock items automatically
- **Recipe Integration**: Items linked to recipes for COGS calculation
- **Availability Control**: Items can be marked unavailable without deletion
- **Pricing Validation**: Prices must be positive, support decimal precision

### 🍕 Custom Pizza System

```mermaid
graph TD
    A[🍕 Custom Pizza Order] --> B[📏 Select Size]
    B --> C[🔄 Choose Quarters]
    C --> D[🍕 Quarter 1: Pizza Type]
    C --> E[🍕 Quarter 2: Pizza Type]
    C --> F[🍕 Quarter 3: Pizza Type]
    C --> G[🍕 Quarter 4: Pizza Type]
    
    D --> H[💰 Calculate Quarter Price]
    E --> H
    F --> H
    G --> H
    
    H --> I[🧩 Add Common Addons]
    I --> J[💰 Calculate Total]
    J --> K[📦 Stock Calculation]
    K --> L[✅ Add to Order]
```

#### 🎯 Pizza Business Rules
- **Quarter System**: Each pizza divided into 4 quarters, each can be different
- **Size Consistency**: All quarters must be same size
- **Price Calculation**: Average of all quarter prices + size multiplier
- **Stock Consumption**: Each quarter consumes stock based on its recipe
- **Addon Application**: Addons apply to entire pizza, not individual quarters

## 📦 Inventory Management Workflows

### 📊 Stock Management Workflow

```mermaid
graph TD
    A[📦 Inventory Management] --> B[📥 Stock Receiving]
    A --> C[📊 Stock Counting]
    A --> D[🔄 Stock Adjustments]
    A --> E[🗑️ Waste Tracking]
    
    B --> B1[📋 Create Purchase Order]
    B1 --> B2[📦 Receive Goods]
    B2 --> B3[✅ Update Stock Levels]
    B3 --> B4[💰 Update Costs]
    
    C --> C1[📊 Physical Count]
    C1 --> C2[🔍 Compare with System]
    C2 --> C3[📝 Record Discrepancies]
    C3 --> C4[🔄 Adjust Stock Levels]
    
    D --> D1[➕ Stock Increase]
    D --> D2[➖ Stock Decrease]
    D1 --> D3[📝 Record Reason]
    D2 --> D3
    D3 --> D4[💾 Update Database]
    
    E --> E1[🗑️ Record Waste]
    E1 --> E2[📝 Waste Reason]
    E2 --> E3[💰 Calculate Loss]
    E3 --> E4[📊 Update Reports]
```

#### 🎯 Inventory Business Rules
- **Stock Levels**: Real-time tracking with minimum threshold alerts
- **Cost Tracking**: FIFO/LIFO cost calculation for COGS
- **Automatic Consumption**: Stock consumed automatically on order completion
- **Waste Tracking**: All waste must be categorized with reasons
- **Purchase Integration**: Purchase orders update stock and costs
- **Audit Trail**: All stock movements logged with timestamps and reasons

### 🔄 Automatic Stock Consumption

```mermaid
graph TD
    A[✅ Order Completed] --> B[🔍 Analyze Order Items]
    B --> C{🍕 Item Type?}
    C -->|Regular Item| D[📋 Get Recipe]
    C -->|Custom Pizza| E[🍕 Process Quarters]
    C -->|Item with Addons| F[🧩 Process Addons]
    
    D --> G[📦 Calculate Consumption]
    E --> H[🍕 Calculate Quarter Consumption]
    F --> I[🧩 Calculate Addon Consumption]
    
    G --> J[📊 Update Stock Levels]
    H --> J
    I --> J
    
    J --> K[💰 Calculate COGS]
    K --> L[📝 Create Consumption Log]
    L --> M[📊 Update Analytics]
```

#### 🎯 Consumption Business Rules
- **Recipe-based**: Consumption calculated from item recipes
- **Size Scaling**: Consumption scales with item size
- **Addon Integration**: Supplements consume additional stock
- **Batch Tracking**: Track which stock batches were consumed
- **Cost Calculation**: COGS calculated using current stock costs
- **Audit Trail**: All consumption logged for inventory reconciliation

## 👥 Staff Management Workflows

### 📅 Staff Scheduling Workflow

```mermaid
graph TD
    A[👥 Staff Scheduling] --> B[📅 Create Weekly Schedule]
    B --> C[👤 Select Staff Member]
    C --> D[📅 Select Day]
    D --> E[⏰ Choose Shift]
    E --> F[✅ Assign Shift]
    F --> G{🔄 More Assignments?}
    G -->|Yes| C
    G -->|No| H[💾 Save Schedule]
    
    H --> I[📱 Notify Staff]
    I --> J[📊 Generate Reports]
    J --> K[✅ Schedule Complete]
    
    L[📝 Daily Attendance] --> M[👤 Select Staff]
    M --> N[⏰ Select Shift]
    N --> O[✅ Mark Present/Absent]
    O --> P[📝 Add Notes]
    P --> Q[💾 Record Attendance]
    Q --> R[💰 Update Payroll]
```

#### 🎯 Scheduling Business Rules
- **Shift Templates**: Predefined shifts with start/end times
- **Availability Checking**: Prevent double-booking staff members
- **Minimum Staffing**: Ensure minimum staff levels per shift
- **Overtime Calculation**: Automatic overtime detection and calculation
- **Attendance Integration**: Scheduled shifts linked to attendance tracking
- **Payroll Integration**: Attendance automatically updates payroll calculations

### 💰 Staff Payment System

```mermaid
graph TD
    A[💰 Staff Payment] --> B{💼 Payment Type?}
    B -->|Salary| C[💰 Fixed Salary]
    B -->|Hourly| D[⏰ Hourly Rate]
    B -->|Per Shift| E[🎯 Shift Rate]
    
    C --> F[📅 Calculate Monthly]
    D --> G[⏰ Calculate Hours Worked]
    E --> H[🎯 Count Shifts Worked]
    
    F --> I[💰 Base Amount]
    G --> I
    H --> I
    
    I --> J[➕ Add Bonuses]
    J --> K[➖ Subtract Advances]
    K --> L[➖ Subtract Deductions]
    L --> M[💰 Final Payment Amount]
    
    M --> N[📊 Create Payment Record]
    N --> O[💾 Update Staff Balance]
    O --> P[🧾 Generate Pay Slip]
```

#### 🎯 Payment Business Rules
- **Multiple Payment Models**: Salary, hourly, per-shift, or mixed
- **Balance Tracking**: Track advances, deductions, bonuses
- **Automatic Calculation**: Payments calculated from attendance records
- **Payment Periods**: Weekly, bi-weekly, or monthly payment cycles
- **Tax Integration**: Support for tax calculations and deductions
- **Audit Trail**: All payment transactions logged and traceable

## 🚚 Delivery Management Workflows

### 🚚 Delivery Order Processing

```mermaid
graph TD
    A[🚚 Delivery Order] --> B[📍 Validate Address]
    B --> C[👤 Assign Driver]
    C --> D{🚗 Driver Type?}
    D -->|Staff| E[👨‍💼 Staff Driver]
    D -->|Freelance| F[🚚 Freelance Driver]
    
    E --> G[📱 Notify Driver]
    F --> H[💰 Set Payment Rate]
    H --> G
    
    G --> I[🍽️ Prepare Order]
    I --> J[📦 Package Order]
    J --> K[🚚 Out for Delivery]
    
    K --> L[📍 Track Delivery]
    L --> M{✅ Delivered?}
    M -->|Yes| N[✅ Mark Delivered]
    M -->|No| O[❌ Failed Attempt]
    
    N --> P[💰 Process Payment]
    O --> Q[📝 Record Failure Reason]
    Q --> R[🔄 Reschedule Delivery]
    
    P --> S[💰 Pay Driver]
    S --> T[📊 Update Analytics]
```

#### 🎯 Delivery Business Rules
- **Address Validation**: Ensure delivery address is complete and valid
- **Driver Assignment**: Automatic or manual driver assignment
- **Payment Models**: Fixed rate or percentage-based driver payment
- **Delivery Tracking**: Real-time status updates and GPS tracking
- **Failed Delivery Handling**: Automatic rescheduling and customer notification
- **Collection Tracking**: For freelance drivers using collection model

### 💰 Freelance Driver Management

```mermaid
graph TD
    A[🚚 Freelance Driver] --> B[📝 Driver Registration]
    B --> C[💰 Set Payment Model]
    C --> D{💳 Payment Type?}
    D -->|Per Delivery| E[💰 Fixed Rate]
    D -->|Collection| F[📊 Percentage Rate]
    
    E --> G[🚚 Assign Deliveries]
    F --> H[💰 Track Collections]
    
    G --> I[📊 Track Performance]
    H --> I
    
    I --> J[💰 Calculate Payments]
    J --> K[📝 Generate Reports]
    K --> L[💳 Process Payment]
    
    M[📊 Collection Model] --> N[💰 Collect Payment]
    N --> O[💸 Deduct Expenses]
    O --> P[💰 Calculate Driver Share]
    P --> Q[📝 Record Transaction]
```

#### 🎯 Freelance Driver Rules
- **Registration Process**: Phone-based registration with performance tracking
- **Payment Models**: Per-delivery fixed rate or collection percentage
- **Performance Metrics**: Track delivery success rate, customer ratings
- **Expense Tracking**: Fuel, parking, and other delivery expenses
- **Collection Management**: Track money collected vs. expected amounts
- **Payment Processing**: Automatic payment calculation and processing

## 💰 Financial Management Workflows

### 💵 Cash Register Management

```mermaid
graph TD
    A[💵 Cash Register] --> B[🌅 Opening Balance]
    B --> C[💰 Daily Transactions]
    C --> D[💳 Order Payments]
    C --> E[➕ Manual Cash In]
    C --> F[➖ Manual Cash Out]
    
    D --> G[📊 Update Balance]
    E --> G
    F --> G
    
    G --> H[🌙 End of Day]
    H --> I[💰 Count Physical Cash]
    I --> J[🔍 Compare with System]
    J --> K{💰 Match?}
    K -->|Yes| L[✅ Balanced]
    K -->|No| M[❌ Discrepancy]
    
    L --> N[📊 Generate Report]
    M --> O[📝 Record Difference]
    O --> P[🔍 Investigate]
    P --> N
```

#### 🎯 Cash Register Rules
- **Opening Balance**: Must be set at start of each business day
- **Transaction Recording**: All cash movements automatically recorded
- **Real-time Balance**: Current balance calculated from all transactions
- **End-of-day Reconciliation**: Physical count vs. system balance
- **Discrepancy Handling**: All differences must be documented and investigated
- **Audit Trail**: Complete transaction history with timestamps and users

### 📊 Financial Reporting

```mermaid
graph TD
    A[📊 Financial Reports] --> B[📈 Sales Reports]
    A --> C[💰 Cash Flow]
    A --> D[📊 Profit Analysis]
    A --> E[💸 Expense Reports]
    
    B --> B1[📅 Daily Sales]
    B --> B2[📊 Item Performance]
    B --> B3[👥 Staff Performance]
    
    C --> C1[💵 Cash In/Out]
    C --> C2[💳 Payment Methods]
    C --> C3[🏦 Bank Reconciliation]
    
    D --> D1[💰 Gross Profit]
    D --> D2[📊 COGS Analysis]
    D --> D3[📈 Profit Margins]
    
    E --> E1[📂 Expense Categories]
    E --> E2[📊 Expense Trends]
    E --> E3[💰 Budget vs. Actual]
```

#### 🎯 Financial Reporting Rules
- **Real-time Data**: All reports based on real-time transaction data
- **Date Range Filtering**: Flexible date range selection for all reports
- **Multi-currency Support**: Handle multiple currencies with conversion
- **Export Capabilities**: Export reports to PDF, Excel, CSV formats
- **Automated Scheduling**: Automatic report generation and email delivery
- **Drill-down Analysis**: Detailed breakdown of all summary figures

## 🔄 Data Synchronization Workflows

### 🌐 Multi-Device Sync

```mermaid
graph TD
    A[🔄 Data Sync] --> B[🔍 Discover Devices]
    B --> C{🌐 Connection Type?}
    C -->|Local Network| D[🏠 Local Sync]
    C -->|Internet| E[🌍 Internet Sync]
    
    D --> F[📡 Direct Connection]
    E --> G[🔗 Proxy Connection]
    
    F --> H[🔄 Bidirectional Sync]
    G --> H
    
    H --> I[⚡ Conflict Detection]
    I --> J{🔍 Conflicts Found?}
    J -->|Yes| K[🔧 Resolve Conflicts]
    J -->|No| L[✅ Sync Complete]
    
    K --> M[📝 Log Resolution]
    M --> L
    
    L --> N[📊 Update Status]
    N --> O[🔔 Notify Users]
```

#### 🎯 Synchronization Rules
- **Automatic Discovery**: Devices automatically discover sync partners
- **Conflict Resolution**: Last-write-wins with manual override options
- **Incremental Sync**: Only changed documents are synchronized
- **Offline Support**: Full functionality when disconnected
- **Data Integrity**: Checksums and validation ensure data consistency
- **Performance Optimization**: Compression and batching for large datasets

## 🎯 Business Intelligence Workflows

### 📊 Analytics Processing

```mermaid
graph TD
    A[📊 Analytics Engine] --> B[📥 Data Collection]
    B --> C[🔄 Data Processing]
    C --> D[📈 Metric Calculation]
    D --> E[📊 Report Generation]
    
    B --> B1[💰 Sales Data]
    B --> B2[📦 Inventory Data]
    B --> B3[👥 Staff Data]
    B --> B4[🚚 Delivery Data]
    
    C --> C1[🧹 Data Cleaning]
    C --> C2[🔗 Data Joining]
    C --> C3[📊 Aggregation]
    
    D --> D1[💰 Revenue Metrics]
    D --> D2[📊 Performance KPIs]
    D --> D3[📈 Trend Analysis]
    
    E --> E1[📱 Dashboard Updates]
    E --> E2[📧 Automated Reports]
    E --> E3[🚨 Alert Generation]
```

#### 🎯 Analytics Business Rules
- **Real-time Processing**: Metrics updated in real-time as data changes
- **Historical Tracking**: Maintain historical data for trend analysis
- **Comparative Analysis**: Compare current performance with previous periods
- **Predictive Analytics**: Forecast future trends based on historical data
- **Alert System**: Automatic alerts for unusual patterns or thresholds
- **Custom Metrics**: Support for restaurant-specific KPIs and metrics

---

## 🎯 Workflow Integration Points

### 🔗 Cross-System Dependencies

1. **Order → Inventory**: Order completion triggers stock consumption
2. **Order → Finance**: Payments update cash register and financial reports
3. **Staff → Payroll**: Attendance automatically updates payment calculations
4. **Inventory → Menu**: Stock levels affect menu item availability
5. **Delivery → Finance**: Driver payments integrated with expense tracking
6. **Analytics → All Systems**: Real-time data feeds from all business processes

### 🔄 Data Flow Patterns

```mermaid
graph LR
    A[📱 User Interface] --> B[🔧 Business Logic]
    B --> C[💾 Database Layer]
    C --> D[🔄 Sync Engine]
    D --> E[🌐 Other Devices]
    
    B --> F[📊 Analytics Engine]
    F --> G[📈 Reports & Dashboards]
    
    B --> H[🔔 Notification System]
    H --> I[📧 Email/SMS/Push]
```

This comprehensive workflow documentation shows how Bistro handles complex restaurant operations through well-defined business processes. Each workflow includes proper validation, error handling, and integration with other system components, ensuring reliable and consistent operation across all business functions. 🏆# Database Synchronization and Offline Workflow Investigation

## Executive Summary

This restaurant management system implements a sophisticated **offline-first** architecture using a multi-tiered database synchronization strategy. The system combines **PouchDB** for client-side storage, **CouchDB** for local server synchronization, and **MongoDB** for centralized data persistence, with advanced P2P networking capabilities and robust conflict resolution.

## Architecture Overview

### Database Layer Structure

```
┌─────────────────────────────────────────────────────────────────┐
│                    MULTI-PLATFORM SYNC ARCHITECTURE              │
├─────────────────────────────────────────────────────────────────┤
│  Mobile Apps      │  Desktop Apps     │  Web Interface          │
│  (Capacitor)      │  (Electron)       │  (Browser)              │
│  ┌─────────────┐  │  ┌─────────────┐  │  ┌─────────────┐        │
│  │  PouchDB    │  │  │ CouchDB IPC │  │  │  PouchDB    │        │
│  │ IndexedDB   │  │  │ (Main Proc) │  │  │ IndexedDB   │        │
│  └─────────────┘  │  └─────────────┘  │  └─────────────┘        │
└─────────────────────────────────────────────────────────────────┤
                           │ SYNC LAYER │                          
├─────────────────────────────────────────────────────────────────┤
│           Local Network P2P Sync (Direct CouchDB)               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  mDNS Discovery → CouchDB:5984 → PouchDB Replication      │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│          Internet Fallback Sync (VPS Proxy)                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  Device Registry → VPS Proxy → Remote CouchDB              │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│              Server Persistence Layer                           │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               MongoDB Atlas                                  │ │
│  │     (Centralized backups & reporting)                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Database Components

### 1. PouchDB Client Layer

**File**: `/lib/db/v4/core/db-instance.ts` (Lines 1-1532)

The system uses **PouchDB** as the primary client-side database with platform-specific configurations:

#### Mobile Configuration (Capacitor)
```typescript
// Mobile-specific configuration (Lines 294-298)
this.db = new PouchDB(this.dbIdentifier, {
  adapter: 'idb',  // Force IndexedDB for mobile
  auto_compaction: true
});
```

#### Desktop Configuration (Electron)
```typescript
// Desktop uses IPC to CouchDB in main process (Lines 232-260)
// No direct PouchDB instance in renderer - operations via IPC
this.db = null; // Explicitly null in Electron mode
```

#### Browser Configuration
```typescript
// Browser mode uses standard PouchDB (Lines 300-303)
this.db = new PouchDB(this.dbIdentifier, {
  auto_compaction: true
});
```

### 2. Database Initialization System

**File**: `/lib/db/v4/core/db-instance.ts` (Lines 162-446)

#### Enhanced Initialization Process
- **Automatic index creation** for optimal query performance (Lines 393-401)
- **Default document initialization** to prevent runtime errors (Lines 404-411) 
- **Restaurant ID cleaning and validation** (Lines 175-183)
- **Environment detection** for platform-specific setup (Lines 232-388)

#### Safe Document Initialization
**File**: `/lib/db/v4/core/db-instance.ts` (Lines 1418-1496)

```typescript
async safeInitializeDocument<T extends { _id: string }>(
  docId: string,
  defaultFactory: () => T,
  operationName: string = 'safeInitializeDocument'
): Promise<T>
```

Uses **document initialization locks** to prevent concurrent access conflicts during setup.

## Conflict Resolution System

### Universal Conflict Resolution

**File**: `/lib/db/v4/core/conflict-resolution.ts` (Lines 1-205)

#### Core Conflict Resolution Patterns

1. **Retry with Exponential Backoff**
```typescript
export async function retryWithConflictResolution<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  operationName: string = 'operation'
): Promise<T>
```

2. **Safe Document Updates** (Lines 56-78)
```typescript
// Get-Modify-Put pattern with automatic revision handling
export async function safeUpdateDocument<T extends { _id: string; _rev?: string }>(
  docId: string,
  updateFunction: (doc: T) => T,
  operationName: string = 'safeUpdateDocument'
): Promise<T>
```

3. **Array-Based Document Operations** (Lines 113-188)
- `safeUpdateArrayDocument`: Update arrays within documents
- `safeAddToArrayDocument`: Add items to document arrays  
- `safeUpdateArrayItem`: Update specific array items
- `safeRemoveFromArrayDocument`: Remove items from arrays

#### Upsert Logic with Conflict Prevention
**File**: `/lib/db/v4/core/db-instance.ts` (Lines 744-803)

The system implements automatic upsert logic that:
- Fetches latest `_rev` before updates to prevent 409 conflicts
- Handles missing documents gracefully
- Preserves document integrity during concurrent operations

## P2P Synchronization Architecture

### 1. Network Discovery System

**File**: `/lib/services/ip-discovery.ts` (Lines 1-293)

#### Intelligent CouchDB Server Discovery
- **Hierarchical subnet scanning**: Primary subnet (192.168.1.x) → localhost → fallback subnets
- **Server caching** with performance-based ranking (Lines 25-103)
- **Multi-platform HTTP clients**: CapacitorHttp for mobile, fetch for desktop
- **Response time optimization** for server selection

#### Discovery Strategy
```typescript
// Primary subnet scan (Lines 243-248)
let servers = await scanSubnet(PRIMARY_SUBNET, [5984], options);

// Localhost check for Electron CouchDB (Lines 252-263)
const localhostServers = [];
for (const port of DEFAULT_PORTS) {
  const server = await testCouchDBServer('127.0.0.1', port, timeout);
}
```

### 2. Native Sync Service

**File**: `/lib/services/native-sync.ts` (Lines 1-319)

#### Dual Sync Mode Architecture
1. **Local CouchDB Sync** - Direct database replication
2. **Internet Proxy Sync** - VPS-mediated sync for remote devices

#### Sync Authentication & Security
```typescript
// Local sync with configurable credentials (Lines 108-119)
const { getCouchDBUrl } = await import('@/lib/config/database-credentials');
remoteUrl = getCouchDBUrl(server.ip, server.port, dbName);

// Internet proxy sync with JWT tokens (Lines 94-107)
syncOptions = {
  live: options.live ?? true,
  retry: options.retry ?? true,
  ajax: {
    headers: {
      'Authorization': `Bearer ${options.authToken}`
    }
  }
};
```

#### Self-Sync Prevention
**File**: `/lib/services/native-sync.ts` (Lines 242-274)

Intelligent self-detection prevents devices from syncing to themselves:
- Allows localhost in Electron (bundled CouchDB server)
- Blocks localhost in browsers to prevent self-sync
- Multiple environment detection methods

### 3. Autonomous Sync Management

**File**: `/lib/services/autonomous-sync-manager.ts` (Lines 1-698)

#### Fully Autonomous Operation
- **Auto-discovery** every 30 seconds (Lines 458-468)
- **Auto-reconnection** on failure (Lines 471-488)
- **Server preference ranking** by performance (Lines 337-372)
- **Internet fallback** when local sync unavailable (Lines 313-322)

#### Intelligent Server Selection
```typescript
private sortServersByPerformance(servers: EnhancedSyncServer[]): EnhancedSyncServer[] {
  return servers.sort((a, b) => {
    // 1. LOCAL FIRST: Direct local servers beat internet proxy
    const aIsLocal = !(a as any).isProxy;
    const bIsLocal = !(b as any).isProxy;
    
    // 2. Preferred servers first
    // 3. Response time (faster first)  
    // 4. Port preference (5984 first)
    // 5. IP preference (localhost first)
  });
}
```

## Internet Sync & P2P Networking

### 1. Internet Discovery Service

**File**: `/lib/services/internet-discovery.ts` (Lines 1-366)

#### Device Registration System
- **Heartbeat mechanism** every 60 seconds (Lines 197-245)
- **Device type detection** (desktop/mobile) (Lines 634-638)
- **Automatic unregistration** on disconnect (Lines 161-195)
- **Peer caching** with failure tracking (Lines 23-97)

### 2. Internet Sync Service

**File**: `/lib/services/internet-sync.ts` (Lines 1-286)

#### VPS Proxy Architecture
```typescript
// Proxy URL construction (Line 131)
const proxyUrl = `${this.config.vpsBaseUrl}/api/sync/proxy/${peer.id}/${dbName}`;

// JWT Authentication for proxy (Lines 146-154)
this.syncHandler = localDb.sync(proxyUrl, {
  live: true,
  retry: true,
  ajax: {
    headers: {
      'Authorization': `Bearer ${this.config.authToken}`
    }
  }
});
```

### 3. Hybrid Autonomous Sync

**File**: `/lib/services/hybrid-autonomous-sync.ts` (Lines 1-519)

#### Intelligent Connection Management
- **Local network priority** over internet sync (Lines 435-441)
- **Automatic fallback** to internet when local unavailable
- **Dynamic server switching** based on availability
- **Connection state management** (Lines 322-349)

## Offline-First Capabilities

### 1. Database Initialization for Offline

**File**: `/lib/db/pouchdb-init.ts` (Lines 1-170)

#### Environment-Specific Loading
```typescript
export const initPouchDB = async (): Promise<any> => {
  const isMobile = isCapacitorEnvironment();
  const isDesktop = isElectronEnvironment();
  
  if (isMobile) {
    return await loadMobilePouchDB();
  } else {
    return await loadDesktopPouchDB(isDesktop);
  }
};
```

#### Mobile PouchDB Verification
```typescript
// Mobile sync capability verification (Lines 68-86)
const hasSync = typeof testDb.sync === 'function';
const hasReplicate = typeof testDb.replicate === 'object';

if (!hasSync) {
  throw new Error('PouchDB sync method not available');
}
```

### 2. Unified Database Provider

**File**: `/lib/context/unified-db-provider.tsx` (Lines 1-420)

#### Context-Based Database Management
- **Authentication-aware initialization** (Lines 156-278)
- **Restaurant switching support** (Lines 102-122)
- **Error recovery mechanisms** (Lines 257-274)
- **Sync status integration** (Lines 134-152)

## MongoDB Integration & Data Persistence

### 1. MongoDB Connection Management

**File**: `/lib/mongodb.ts` (Lines 1-201)

#### Production-Ready MongoDB Setup
```typescript
// Connection options for reliability (Lines 53-67)
const options: MongoClientOptions = {
  connectTimeoutMS: 15000,
  socketTimeoutMS: 30000,
  serverSelectionTimeoutMS: 15000,
  maxPoolSize: 10,
  minPoolSize: 1,
  retryWrites: true,
  retryReads: true
};
```

#### Connection Health Monitoring
```typescript
export async function isMongoDBReachable(): Promise<{ reachable: boolean; error?: string }> {
  // 5-second timeout with detailed error reporting (Lines 143-200)
  // Network state detection for browsers
  // Authentication and permission error handling
}
```

### 2. MongoDB Operations

**File**: `/lib/db/mongo/restaurant-settings-ops.ts` and `/lib/auth/mongo-auth-ops.ts`

The system maintains MongoDB operations for:
- Restaurant settings and configuration backup
- User authentication and session management
- Centralized reporting and analytics data
- Cross-device data consistency verification

## Sync Status & Event Management

### 1. Database Change Events

**File**: `/lib/services/native-sync.ts` (Lines 137-147, 161-172)

```typescript
// Real-time change event emission
window.dispatchEvent(new CustomEvent('pouchdb-change', {
  detail: {
    doc,
    isLocal: false, // From sync (pull)
    direction: 'pull'
  }
}));
```

### 2. Auto-Print Integration

The sync system integrates with auto-print services by emitting database change events that trigger automatic printing of new orders from synchronized data.

## Performance Optimizations

### 1. Index Management

**File**: `/lib/db/v4/core/db-instance.ts` (Lines 53-97)

#### Comprehensive Index Strategy
```typescript
// Order indexes for optimal query performance
{ fields: ['type', 'status', 'createdAt'], name: 'order-type-status-created-at-idx' },
{ fields: ['type', 'tableId'], name: 'order-type-tableid-idx' },
{ fields: ['type', 'paymentStatus'], name: 'order-type-paymentstatus-idx' },

// Inventory log indexes
{ fields: ['type', 'stockItemId', 'createdAt'], name: 'log-type-stockitem-date-idx' },

// Sales aggregation indexes  
{ fields: ['type', 'date'], name: 'aggregation-daily-date-idx' }
```

### 2. Server Caching

**File**: `/lib/services/ip-discovery.ts` (Lines 25-103)

#### Intelligent Caching Strategy
- **5-minute cache duration** for discovered servers
- **Failure count tracking** to avoid problematic servers
- **Response time-based ranking** for optimal server selection
- **Background cache refresh** while returning cached results immediately

### 3. Connection Pooling

**File**: `/lib/services/autonomous-sync-manager.ts` (Lines 373-431)

The system maintains intelligent connection management:
- **Single active connection** to prevent resource conflicts
- **Connection preference ranking** (local > internet)
- **Automatic failover** on connection loss
- **Failed server avoidance** with retry limits

## Data Consistency & Integrity

### 1. Restaurant ID Management

**File**: `/lib/db/db-utils.ts` (Lines 13-43, 51-59)

#### Restaurant ID Cleaning & Validation
```typescript
export function cleanRestaurantId(id: string): string {
  // Handle multiple potential prefix patterns
  let cleanedId = id.replace(/^(restaurant[-_:])+/, '');
  cleanedId = cleanedId.replace(/^(resto[-_:])+/, '');
  
  // Ensure ID is valid for database use
  return formatDocId(cleanedId);
}
```

### 2. Document Validation

**File**: `/lib/db/v4/core/validation.ts` and `/lib/db/v4/schemas/`

The system includes comprehensive schema validation for:
- Order documents and line items
- Inventory transactions and stock levels
- Menu items and recipe compositions
- Staff schedules and payment tracking
- Sales aggregation and reporting data

## Multi-Platform Build Considerations

### 1. Build Target Optimization

**File**: `/lib/build-config.ts` and `next.config.ts`

#### Conditional Module Loading
The system uses webpack aliases to replace server-dependent modules with empty stubs for static builds:

```typescript
// For static/offline builds, replace server modules
'@/lib/mongodb': path.resolve(__dirname, 'lib/services/empty-mongodb.js'),
'@/lib/auth/mongo-auth-ops': path.resolve(__dirname, 'lib/services/empty-auth-ops.js')
```

### 2. Platform Detection

**File**: `/lib/db/pouchdb-init.ts` (Lines 15-45)

#### Multi-Platform Environment Detection
```typescript
export const isCapacitorEnvironment = (): boolean => {
  // Primary check: Capacitor object exists
  if ((window as any).Capacitor) return true;
  
  // Secondary check: Capacitor in user agent
  if (navigator.userAgent.includes('capacitor')) return true;
  
  // Conservative mobile detection with multiple indicators
  const isMobileUA = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const hasTouch = 'ontouchstart' in window;
  const hasOrientation = 'orientation' in window;
  
  return isMobileUA && (hasTouch || hasOrientation);
};
```

## Security Considerations

### 1. Authentication Integration

**File**: `/lib/services/native-sync.ts` (Lines 108-119)
**File**: `/lib/services/internet-sync.ts` (Lines 146-154)

#### Multi-Layer Authentication
- **Configurable CouchDB credentials** for local sync
- **JWT Bearer tokens** for internet proxy sync  
- **Device registration** with heartbeat verification
- **Permission-based access control** per user role

### 2. Self-Sync Prevention

**File**: `/lib/services/native-sync.ts` (Lines 242-274)

The system prevents infinite sync loops and data corruption through intelligent self-detection across different environments.

## Performance Metrics & Monitoring

### 1. Sync Performance Tracking

**File**: `/lib/services/native-sync.ts` (Lines 130-153)

#### Real-Time Metrics
```typescript
.on('change', (info: any) => {
  if (info.direction === 'pull') {
    this.status.docsReceived += info.change?.docs_read || 0;
  } else if (info.direction === 'push') {
    this.status.docsSent += info.change?.docs_written || 0;
  }
  this.status.lastSync = new Date();
});
```

### 2. Discovery Performance

**File**: `/lib/services/ip-discovery.ts` (Lines 108-184)

#### Response Time Tracking
- **Server response time measurement** for optimal selection
- **Failure rate tracking** to avoid problematic servers
- **Cache hit rate optimization** for faster subsequent discoveries

## Conclusion

This restaurant management system implements a **world-class offline-first architecture** with:

1. **Multi-tiered database synchronization** (PouchDB → CouchDB → MongoDB)
2. **Intelligent P2P networking** with mDNS discovery and internet fallback
3. **Robust conflict resolution** with exponential backoff and document locking
4. **Platform-optimized implementations** for mobile, desktop, and web
5. **Production-ready security** with multi-layer authentication
6. **Performance optimization** through intelligent caching and connection management
7. **Data integrity** with comprehensive validation and safe operations

The architecture ensures **seamless offline operation** while providing **automatic synchronization** when network connectivity is available, making it ideal for restaurant environments where reliable internet may not always be guaranteed.

## Key Files Reference

| Component | File Path | Lines | Description |
|-----------|-----------|-------|-------------|
| Core Database | `/lib/db/v4/core/db-instance.ts` | 1-1532 | Main database instance with multi-platform support |
| Conflict Resolution | `/lib/db/v4/core/conflict-resolution.ts` | 1-205 | Universal conflict resolution utilities |
| Native Sync | `/lib/services/native-sync.ts` | 1-319 | Local and internet sync service |
| Autonomous Sync | `/lib/services/autonomous-sync-manager.ts` | 1-698 | Fully autonomous background sync |
| P2P Discovery | `/lib/services/ip-discovery.ts` | 1-293 | Network server discovery with caching |
| Internet Discovery | `/lib/services/internet-discovery.ts` | 1-366 | Internet peer discovery and registration |
| PouchDB Init | `/lib/db/pouchdb-init.ts` | 1-170 | Platform-specific PouchDB initialization |
| MongoDB Connection | `/lib/mongodb.ts` | 1-201 | Production-ready MongoDB client |
| Database Provider | `/lib/context/unified-db-provider.tsx` | 1-420 | React context for database management |
| Hybrid Sync | `/lib/services/hybrid-autonomous-sync.ts` | 1-519 | Intelligent local/internet sync switching |# Financial and Analytics Workflow Investigation

## Executive Summary

This restaurant management system implements a sophisticated, offline-first financial and analytics architecture using Next.js 15, PouchDB/MongoDB, and pre-computed aggregations. The system provides real-time financial tracking, comprehensive analytics, and multi-platform support (web, desktop, mobile) with P2P synchronization capabilities.

## 1. Architecture Overview

### Core Technologies
- **Frontend**: Next.js 15 with React 19, TypeScript
- **Database**: Dual-layer (PouchDB for offline, MongoDB for persistence)
- **Analytics Engine**: Pre-computed aggregation system
- **Multi-Platform**: Electron (desktop), Capacitor (mobile), Web
- **Synchronization**: P2P with offline-first design

### Key Principles
- **Offline-First**: All operations work without internet
- **Pre-computed Analytics**: Sub-100ms dashboard loading
- **Real-time Updates**: Incremental aggregation updates
- **Multi-platform Consistency**: Same data across all platforms

## 2. Financial Data Collection and Processing

### 2.1 Revenue Tracking Mechanisms

#### Order-Based Revenue (`/lib/hooks/useOrderAnalyticsV4.ts`)
- **Primary Source**: Orders with `status: 'completed'` and `paymentStatus: 'paid'|'partially_paid'`
- **Data Points**: 
  - Order total amounts
  - Individual item prices and quantities
  - Payment method breakdown
  - Order type categorization
- **Processing Logic** (Lines 217-272):
  ```typescript
  const processableOrders = orders.filter(order => 
    order.status === 'completed' && 
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid')
  );
  ```

#### Cash Transaction Tracking (`/lib/services/finance-service.ts`)
- **Manual Transactions**: Cash in/out operations
- **Automated Sales Recording**: Order payments create cash transactions
- **Validation** (Lines 112-149): Comprehensive amount and description validation
- **Conflict Resolution**: Retry mechanism for database conflicts

### 2.2 Expense Management System

#### Multi-Source Expense Tracking (`/components/analytics/tabs/ExpensesV4Tab.tsx`)
- **Staff Payments**: Salaries, advances, per-shift payments
- **Supplier Payments**: Inventory purchases, services
- **Operational Expenses**: Rent, utilities, maintenance
- **Manual Expenses**: Ad-hoc expenditures

#### Expense Categories (Lines 76-107):
```typescript
const EXPENSE_CATEGORIES = {
  STAFF: { name: 'Personnel', color: 'hsl(230, 85%, 55%)' },
  SUPPLIERS: { name: 'Fournisseurs', color: 'hsl(160, 85%, 45%)' },
  RENT: { name: 'Loyer', color: 'hsl(340, 85%, 55%)' },
  UTILITIES: { name: 'Services', color: 'hsl(45, 85%, 55%)' },
  OTHER: { name: 'Autres', color: 'hsl(270, 85%, 55%)' }
};
```

#### Expense Database Operations (`/lib/db/v4/operations/expense-ops.ts`)
- **Persistent Storage**: Document-based expense tracking
- **CRUD Operations**: Create, read, update, delete with conflict resolution
- **No Cash Impact**: Expenses don't automatically affect cash register (Line 53)

## 3. Daily Financial Snapshot Generation

### 3.1 Daily Snapshot Component (`/app/components/finance/DailySnapshot.tsx`)

#### Key Metrics Calculation (Lines 155-168):
- **Total Income**: Revenue from completed orders
- **Total Expenses**: Sum of all expense categories
- **Net Profit**: Income minus expenses
- **Cash Balance**: Real-time cash register balance

#### Payment Method Breakdown (Lines 78-107):
```typescript
const getPaymentMethodBreakdown = () => {
  const cashPayments = transactions.filter(tx => tx.type === 'sales' && tx.amount > 0);
  const cardPayments = []; // TODO: Implement card tracking
  const otherPayments = transactions.filter(tx => tx.type !== 'sales' && tx.amount > 0);
  // Returns breakdown with amounts, percentages, and counts
};
```

#### Expense Category Analysis (Lines 110-133):
- **Category Aggregation**: Groups expenses by type
- **Percentage Calculations**: Relative expense distribution
- **Trend Analysis**: Comparison with previous periods

### 3.2 Automated Daily Aggregations (`/lib/db/v4/operations/sales-aggregation-ops.ts`)

#### Daily Aggregation Schema (Lines 30-86):
```typescript
interface DailySalesAggregation {
  date: string; // YYYY-MM-DD
  totalRevenue: number;
  totalOrders: number;
  totalItems: number;
  averageOrderValue: number;
  totalCogs: number; // Cost of goods sold
  grossProfit: number;
  profitMargin: number;
  orderTypeBreakdown: {...};
  paymentMethodBreakdown: {...};
  hourlyBreakdown: Array<{...}>;
  topItems: Array<{...}>;
  statusBreakdown: {...};
}
```

#### Incremental Updates (Lines 71-116):
- **Real-time Processing**: Updates occur on order create/update/delete
- **Conflict Resolution**: Retry mechanism for concurrent updates
- **Performance**: Sub-100ms updates through pre-computation

## 4. Revenue and Expense Tracking Mechanisms

### 4.1 Revenue Analytics (`/components/analytics/tabs/SalesTab.tsx`)

#### KPI Calculations (Lines 358-396):
- **Net Sales**: Sum of paid order amounts
- **Order Count**: Completed and paid orders only
- **Average Ticket**: Revenue divided by order count
- **Items per Order**: Total items sold divided by orders
- **Profit Calculations**: Based on recipe COGS when available

#### Sales by Item Analysis (Lines 217-272):
```typescript
function calculateSalesByItem(orders: OrderDocument[]): SaleItem[] {
  // Filters completed/paid orders
  // Excludes voided items
  // Calculates profit using real COGS from recipes
  // Includes profitability scoring and contribution analysis
}
```

#### Peak Hours Analysis (Lines 323-356):
- **24-Hour Breakdown**: Sales and order count by hour
- **Average Ticket**: Per-hour ticket size analysis
- **Temporal Patterns**: Identifies busy periods

### 4.2 Expense Analytics (`/components/analytics/tabs/ExpensesV4Tab.tsx`)

#### Category-Based Analysis (Lines 208-226):
```typescript
const getExpensesByCategory = (expenses: Expense[]): ExpenseCategory[] => {
  // Groups expenses by category
  // Calculates totals and percentages
  // Sorts by amount descending
  // Returns structured data for visualization
}
```

#### Filtering and Search (Lines 250-258):
- **Date Range Filtering**: Configurable period selection
- **Category Filtering**: Specific expense type focus
- **Search Functionality**: Text-based expense lookup
- **Real-time Updates**: Immediate filter application

## 5. Profit/Loss Calculations

### 5.1 Gross Profit Calculation

#### Recipe-Based COGS (`/components/analytics/tabs/SalesTab.tsx`, Lines 221-256):
```typescript
// Only use real COGS from order or item if available
const itemCogs = item.cogs || 0;
const itemSales = item.price * effectiveQuantity;
const itemProfit = itemCogs > 0 ? itemSales - (itemCogs * effectiveQuantity) : 0;
```

#### Profitability Metrics (Lines 264-271):
- **Profitability Score**: Profit per unit sold
- **Contribution Analysis**: Percentage of total profit
- **Margin Calculation**: Profit as percentage of sales
- **Recipe Coverage Validation**: Ensures accurate calculations

### 5.2 Net Profit Calculation (`/app/components/finance/DailySnapshot.tsx`)

#### Daily P&L Calculation (Lines 156-167):
```typescript
const totalIncome = 0; // Revenue from completed orders
const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
const netProfit = totalIncome - totalExpenses;
const profitChange = calculatePercentChange(netProfit, previousProfit);
```

#### Trend Analysis (Lines 72-75):
- **Period Comparison**: Current vs previous day/week/month
- **Growth Percentage**: Relative change calculations
- **Visual Indicators**: Up/down/stable trend arrows

## 6. Analytics Dashboards and Metrics

### 6.1 Main Analytics Page (`/app/(protected)/analytics/page.tsx`)

#### Tab Structure (Lines 148-198):
- **Sales Tab**: Revenue, orders, and item performance
- **COGS Tab**: Cost analysis and profit margins (development only)
- **Expenses Tab**: Expense management and analytics (development only)

#### Real-time Updates (Lines 95-99):
```typescript
const handleRefresh = () => {
  setIsLoading(true);
  // Simulated refresh - actual implementation fetches fresh data
  setTimeout(() => setIsLoading(false), 1000);
};
```

### 6.2 Sales Analytics Dashboard (`/components/analytics/tabs/SalesTab.tsx`)

#### Key Performance Indicators (Lines 292-374):
- **Net Sales**: Total revenue with trend indicators
- **Total Profit**: Gross profit when recipes available
- **Profit Margin**: Percentage profitability
- **Order Count**: Total completed orders
- **Average Ticket**: Mean order value
- **Items per Order**: Average basket size

#### Order Type Breakdown (Lines 377-452):
- **Dine-in**: Restaurant table orders
- **Takeaway**: Customer pickup orders
- **Delivery**: Third-party or direct delivery
- **Progress Visualization**: Percentage breakdowns with progress bars

### 6.3 Financial Dashboard (`/app/(protected)/finance/page.tsx`)

#### Cash Management (Lines 84-114):
```typescript
const calculateCashFlow = useCallback(() => {
  // Filters transactions by date range
  // Separates cash in vs cash out
  // Calculates running totals
  // Returns structured cash flow data
}, [cashTransactions, dateRange]);
```

#### Quick Actions (Lines 139-164):
- **Cash In/Out**: Manual cash register adjustments
- **Transaction History**: Real-time financial events
- **Balance Tracking**: Current cash position
- **Error Handling**: Comprehensive validation and retry logic

## 7. Data Aggregation and Storage Patterns

### 7.1 Pre-computed Aggregation System (`/lib/db/v4/schemas/sales-aggregation-schemas.ts`)

#### Aggregation Levels (Lines 18-386):
- **Daily Aggregations**: Day-by-day sales and profit metrics
- **Weekly Aggregations**: Rolling 7-day summaries
- **Monthly Aggregations**: Month-over-month comparisons
- **Item Performance**: Individual menu item tracking
- **Real-time Dashboard**: Live operational metrics

#### Performance Benefits:
- **Sub-100ms Loading**: Pre-computed data eliminates real-time calculations
- **Mobile Optimized**: Small data payloads for mobile devices
- **P2P Sync Friendly**: Incremental updates reduce sync overhead
- **Offline Capable**: Works without internet connectivity

### 7.2 Incremental Update Engine (`/lib/db/v4/operations/sales-aggregation-ops.ts`)

#### Update Workflow (Lines 41-68):
```typescript
export async function updateAggregationsForOrder(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  previousOrder?: OrderDocument
): Promise<void> {
  // Updates all aggregation levels in parallel
  // Handles create, update, and delete operations
  // Maintains data consistency across aggregations
}
```

#### Conflict Resolution (Lines 82-115):
- **Retry Mechanism**: Automatic retry on database conflicts
- **Version Control**: Document versioning for consistency
- **Error Recovery**: Comprehensive error handling and logging
- **Performance Tracking**: Operation timing and success rates

### 7.3 Data Access Hooks (`/lib/hooks/useSalesAggregationV4.ts`)

#### Hook Architecture (Lines 63-315):
- **State Management**: Centralized aggregation state
- **Performance Tracking**: Load time and cache hit rate monitoring
- **Auto-refresh**: Periodic data updates for stale detection
- **Error Handling**: Graceful degradation on failures

#### Specialized Hooks (Lines 324-393):
- **Dashboard Hook**: Optimized for dashboard components
- **Daily Sales Hook**: Date-specific sales analysis
- **Item Performance Hook**: Individual item tracking

## 8. System Integration Points

### 8.1 Order Integration
- **Order Creation**: Triggers aggregation updates automatically
- **Payment Processing**: Updates cash register and sales aggregations
- **Order Modifications**: Handles voided items and quantity changes
- **Status Changes**: Tracks completed, cancelled, and pending orders

### 8.2 Staff Payment Integration
- **Salary Payments**: Recorded as staff expense category
- **Per-shift Payments**: Automated expense tracking
- **Cash Advances**: Manual cash-out transactions
- **Payment History**: Complete audit trail maintenance

### 8.3 Inventory Integration
- **Purchase Costs**: Supplier payments as expenses
- **COGS Calculation**: Recipe-based cost tracking
- **Waste Tracking**: Loss recording and analysis
- **Stock Valuation**: Inventory impact on profitability

## 9. Performance and Scalability

### 9.1 Performance Optimizations
- **Pre-computed Aggregations**: Eliminate real-time calculations
- **Incremental Updates**: Only process changes, not full recalculations
- **Conflict Resolution**: Handle concurrent access gracefully
- **Cache Strategy**: Multiple levels of data caching

### 9.2 Scalability Features
- **Horizontal Sync**: P2P distribution across devices
- **Offline Operation**: Full functionality without internet
- **Multi-platform**: Consistent experience across platforms
- **Data Partitioning**: Restaurant-specific data isolation

## 10. Security and Data Integrity

### 10.1 Data Validation
- **Input Sanitization**: XSS prevention on all user inputs
- **Amount Validation**: Comprehensive financial amount checking
- **User Authentication**: Multi-user access control
- **Permission System**: Role-based feature access

### 10.2 Audit Trail
- **Transaction Logging**: Complete financial operation history
- **User Tracking**: Who performed each operation
- **Timestamp Tracking**: When each operation occurred
- **Change History**: Full audit trail for modifications

## 11. Key File References

### Core Financial Files
- `/lib/services/finance-service.ts` - Core financial operations and cash register management
- `/app/components/finance/DailySnapshot.tsx` - Daily financial summary component
- `/lib/db/v4/operations/expense-ops.ts` - Expense CRUD operations
- `/lib/db/v4/operations/sales-aggregation-ops.ts` - Analytics aggregation engine

### Analytics Components
- `/components/analytics/tabs/SalesTab.tsx` - Sales analytics dashboard
- `/components/analytics/tabs/ExpensesV4Tab.tsx` - Expense analytics interface
- `/lib/hooks/useOrderAnalyticsV4.ts` - Real-time analytics data processing
- `/lib/hooks/useSalesAggregationV4.ts` - Aggregation system React hooks

### Schema Definitions
- `/lib/db/v4/schemas/sales-aggregation-schemas.ts` - Aggregation data structures
- `/types/stock.ts` - Inventory and financial type definitions

### Dashboard Pages
- `/app/(protected)/analytics/page.tsx` - Main analytics interface
- `/app/(protected)/finance/page.tsx` - Financial management dashboard
- `/app/(protected)/finance/daily-snapshot/page.tsx` - Daily snapshot viewer

## 12. Conclusion

This financial and analytics system demonstrates sophisticated restaurant management capabilities with:

- **Real-time Financial Tracking**: Immediate updates on revenue, expenses, and cash flow
- **Comprehensive Analytics**: Detailed sales, profit, and operational metrics
- **Offline-First Architecture**: Full functionality without internet dependency
- **Multi-Platform Consistency**: Same experience across web, desktop, and mobile
- **Scalable Performance**: Sub-100ms dashboard loading through pre-computed aggregations
- **Data Integrity**: Comprehensive validation, conflict resolution, and audit trails

The system successfully balances performance, functionality, and reliability to provide restaurant owners with essential financial insights and operational control.# Kitchen Display System Workflow

## Overview

The Kitchen Display System (KDS) is a comprehensive order management system designed for restaurant kitchen operations. It provides real-time order tracking, status management, and seamless communication between kitchen staff and waiters. The system supports multi-station operations with barcode-based item completion tracking.

## Architecture Components

### Core Components

1. **KitchenDisplay Component** (`/Users/<USER>/Desktop/rest/shop/components/KitchenDisplay.tsx`)
   - Main interface for kitchen staff
   - Real-time order display in three-column layout (Pending, Preparing, Ready)
   - Order status management and timing tracking
   - Item-level status updates and notifications

2. **Kitchen Queue Service** (`/Users/<USER>/Desktop/rest/shop/lib/services/kitchen-queue-service.ts`)
   - Manages queue state persistence using PouchDB
   - Handles barcode scanning and item completion tracking
   - Provides station-based queue management

3. **Kitchen Print Service** (`/Users/<USER>/Desktop/rest/shop/lib/services/kitchen-print-service.ts`)
   - Generates kitchen tickets with barcodes
   - Manages multi-station printing workflow
   - Handles kitchen-specific print formatting

4. **Kitchen Barcode Scanner** (`/Users/<USER>/Desktop/rest/shop/app/components/KitchenBarcodeScanner.tsx`)
   - Barcode scanning interface for item completion
   - Real-time scan history and status tracking
   - Auto-generates expo tickets when orders are complete

## Order Status Flow

### Status Progression
```
pending → preparing → served → completed
              ↓
           cancelled
```

### Status Definitions
- **pending**: New orders awaiting kitchen preparation (lines 17, 100-104)
- **preparing**: Orders currently being prepared (lines 18, 101-104)
- **served**: Orders ready for pickup/delivery (lines 19, 103)
- **completed**: Orders fully delivered to customers (lines 20, 144)
- **cancelled**: Cancelled orders (lines 146)

### Status Management Functions
Located in KitchenDisplay.tsx (lines 202-221):
- `handleUpdateOrderStatus()` - Updates order status with timestamp tracking
- `trackStatusChange()` - Records status change timestamps (lines 333-341)
- Status transitions include validation and automatic refresh

## Kitchen Staff Interface

### Display Layout (lines 742-1159)
Three-column responsive grid:

1. **Pending Orders Column** (lines 743-869)
   - Yellow accent border (border-l-yellow-500)
   - Shows new orders awaiting preparation
   - "Start Preparing" action button
   - Cancel order functionality

2. **Preparing Orders Column** (lines 871-1013)
   - Orange accent border (border-l-orange-500)
   - Displays orders in preparation
   - Shows preparation timing information
   - "Ready to Serve" action button
   - Back to pending option

3. **Ready Orders Column** (lines 1015-1158)
   - Green accent border (border-l-green-500)
   - Shows orders ready for pickup
   - "Mark Delivered" action button
   - Back to preparing option

### Key Features

#### Real-time Updates (lines 188-199)
- Auto-refresh every 30 seconds when enabled
- Manual refresh functionality
- Last refresh timestamp display

#### Search and Filtering (lines 95-164)
- Search by order ID, table ID, item names, customer info
- Status filtering (active, all, pending, preparing, served, completed, cancelled)
- Priority sorting options (newest, oldest, priority-based)

#### Timing Tracking (lines 224-285)
- Tracks time spent in each status
- Shows pending time, preparation time, and total time
- Visual timing indicators for staff awareness

## Item-Level Management

### Item Status Tracking (lines 74-82, 287-330)
Advanced item management includes:
- Individual item status updates (damaged, remade)
- Quantity-specific tracking
- Notes for special handling
- Visual status indicators

### Item Status Dialog (lines 1187-1274)
Modal interface for updating item status:
- Status selection (damaged/remade)
- Quantity adjustment
- Optional notes field
- Real-time status history

## Queue Management System

### Queue Schema (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/schemas/queue-schema.ts`)

#### Queue Item Structure (lines 140-155)
```typescript
interface QueueItem {
  _id: string;
  type: 'queue_item';
  orderId: string;
  orderNumber: string;
  stationId: string;
  items: OrderItem[];
  completedItemIds: string[];
  status: 'pending' | 'in-progress' | 'completed';
  estimatedTime?: number;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}
```

#### Item Completion Tracking (lines 157-171)
```typescript
interface ItemCompletion {
  barcode: string;
  orderId: string;
  stationId: string;
  itemName: string;
  status: 'pending' | 'completed';
  scannedBy?: string;
  completedAt?: string;
}
```

### Queue Operations
Key functions in kitchen-queue-service.ts:

1. **addOrderToQueue()** (lines 62-72)
   - Adds orders to station-specific queues
   - Tracks items per station

2. **completeItem()** (lines 77-95)
   - Handles barcode-based item completion
   - Returns completion status and order completion state

3. **getStationQueue()** (lines 144-153)
   - Retrieves current queue status for a station
   - Returns queue metrics and item lists

## Barcode System Integration

### Barcode Generation
Kitchen print service generates unique barcodes for each item:
- Format: Daily sequence + item index (e.g., "002005")
- Compact 6-digit numeric format for efficient scanning
- Embedded in kitchen tickets with visual barcode images

### Barcode Scanning Interface
KitchenBarcodeScanner.tsx provides:
- Real-time barcode input and scanning
- Scan history with success/failure tracking
- Automatic expo ticket generation when orders complete
- Instructions and help interface

### Scanning Workflow (lines 49-124)
1. Scan item barcode from kitchen ticket
2. System validates barcode against pending items
3. Item marked as complete with timestamp
4. Check if entire order is complete
5. Generate expo ticket if all items done
6. Update kitchen display in real-time

## Multi-Station Operations

### Station Assignment
Orders are automatically distributed to stations based on:
- Menu item categories
- Printer configurations
- Category-to-station mappings

### Queue Coordination
Inter-station communication includes:
- Queue status sharing between stations
- Pending item counts for coordination
- Order completion synchronization

## Kitchen-Waiter Communication

### Status Notifications
- Real-time status updates visible to all users
- Toast notifications for status changes
- Visual indicators for order progression

### Order Information Sharing
- Table assignments and customer details
- Special instructions and notes
- Order timing and priority information

### Communication Workflow
1. Waiter creates order → appears in kitchen pending queue
2. Kitchen starts preparation → status visible to waiters
3. Kitchen marks ready → waiter notification
4. Waiter confirms delivery → order completed

## Performance Features

### Auto-refresh System (lines 188-199)
- Configurable automatic refresh (30-second intervals)
- Manual refresh with loading indicators
- Timestamp tracking for refresh operations

### Offline-First Architecture
- PouchDB persistence for queue data
- Local-first operations with sync capabilities
- Conflict resolution for concurrent updates

### Responsive Design
- Mobile-friendly interface
- Touch-optimized controls
- Compact display for kitchen environments

## Technical Implementation Details

### Database Integration
- Uses V4 database schema with PouchDB
- Queue operations with conflict resolution
- Indexed queries for performance

### State Management
- React hooks for order data (`useOrderV4`)
- Local state for UI interactions
- Toast notifications for user feedback

### Error Handling
- Comprehensive error catching and user feedback
- Retry mechanisms for failed operations
- Graceful degradation for offline scenarios

## Security and Permissions

### Authentication
- Multi-user authentication system
- Role-based access control
- Kitchen staff permissions validation

### Data Access
- Secure API endpoints for order operations
- Real-time data synchronization
- Audit trail for all status changes

## Future Enhancements

The system architecture supports:
- Additional station types and workflows
- Enhanced analytics and reporting
- Integration with external kitchen equipment
- Advanced notification systems
- Mobile application extensions

## Configuration

### Kitchen Display Settings
- Auto-refresh toggles
- Font size preferences (small, medium, large)
- Display mode selection (display vs printer mode)
- Column layout customization

### Print System Configuration
- Multi-printer support with category assignments
- Barcode feature enabling/disabling
- Queue coordination features
- Print preview and validation

This documentation provides a comprehensive overview of the Kitchen Display System workflow, covering all major components, interactions, and implementation details found in the codebase analysis.# Order Management Workflow - Complete Investigation Report

## Overview

This restaurant management system implements a comprehensive order management workflow with multi-platform support (web, desktop, mobile), offline-first capabilities, and real-time synchronization. The system handles the complete order lifecycle from creation to completion, including table management, kitchen operations, and payment processing.

## Order Lifecycle & States

### Order Status States

The system uses a validated status transition model defined in `/Users/<USER>/Desktop/rest/shop/lib/db/v4/utils/order-status-validation.ts`:

```typescript
type OrderStatus = 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';
```

#### Status Transition Flow

1. **pending** → `served`, `completed`, `cancelled`
   - Order is being cooked/prepared in kitchen
   - Kitchen staff is actively working on the order

2. **preparing** → `served`, `completed`, `cancelled` 
   - Legacy status, functionally equivalent to pending
   - Maintained for backward compatibility

3. **served** → `completed`, `cancelled`
   - Food is ready and served to customer
   - Awaiting payment processing

4. **completed** → Terminal state
   - Payment processed and order finalized
   - No further transitions allowed

5. **cancelled** → Terminal state
   - Order was cancelled at any stage
   - No further transitions allowed

### Order Types

Defined in `/Users/<USER>/Desktop/rest/shop/lib/types/order-types.ts`:

- **dine-in** (Sur Place): Table service orders requiring table assignment
- **takeaway** (À Emporter): Customer pickup orders
- **delivery** (Livraison): Orders delivered to customer address

Legacy support for `table` → `dine-in` and `takeout` → `takeaway`.

## Core Components & Architecture

### 1. Order Schema (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/schemas/order-schema.ts`)

The system uses a comprehensive order document structure:

```typescript
interface OrderDocument {
  _id: string;                    // Format: order:YYYYMMDD-XXX
  type: 'order_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  
  // Core Order Data
  tableId: string;
  status: OrderStatus;
  orderType: AllOrderTypes;
  items: OrderItem[];
  total: number;
  
  // Pricing & Discounts
  subtotal?: number;
  discountType?: 'percentage' | 'fixed_amount';
  discountValue?: number;
  discountAmount?: number;
  discountReason?: string;
  
  // Customer Information
  customer?: Customer;
  deliveryPerson?: DeliveryPerson;
  
  // Payment Tracking
  paymentStatus?: 'unpaid' | 'paid' | 'partially_paid';
  paymentMethod?: 'cash' | 'card' | 'online' | 'mixed';
  paymentDetails?: PaymentDetails;
  
  // Financial Metrics
  totalCogs?: number;             // Cost of goods sold
  grossProfit?: number;           // total - totalCogs
  profitMargin?: number;          // (grossProfit / total) * 100
  
  // Audit Trail
  createdBy?: string;
  createdByName?: string;
  
  // Void Management
  hasVoids?: boolean;
  totalVoidedAmount?: number;
  originalTotal?: number;
  voidHistory?: VoidEntry[];
  
  // Enhanced Delivery
  deliveryStatus?: 'pending' | 'out_for_delivery' | 'delivered' | 'failed' | 'partially_delivered';
  deliveryAttempts?: DeliveryAttempt[];
  collectionStatus?: CollectionStatus;
}
```

### 2. Order Operations (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-ops.ts`)

Key database operations:

#### Order ID Generation
- Format: `order:YYYYMMDD-XXX` (e.g., `order:************`)
- Business day calculation with 5 AM reset hour
- Daily sequence numbering with zero-padding

#### Core CRUD Operations
- `createOrder()`: Creates new order with auto-generated ID
- `getOrder()`: Retrieves order by ID
- `getAllOrders()`: Fetches all orders with sorting
- `getActiveOrders()`: Gets pending/preparing/served orders
- `updateOrder()`: Updates order with conflict resolution
- `deleteOrder()`: Removes order from database

#### Specialized Operations
- `updateOrderStatus()`: Status updates with validation and COGS calculation
- `voidOrderItems()`: Handles item voiding with audit trail
- `updateDeliveryStatus()`: Enhanced delivery tracking
- `processDeliveryFailure()`: Handles failed deliveries with waste tracking
- `updateCollectionStatus()`: Collection tracking for delivery orders

### 3. Order Hook (`/Users/<USER>/Desktop/rest/shop/lib/hooks/use-order-v4.ts`)

React hook providing order management functionality:

```typescript
interface UseOrderReturn {
  orders: Order[];                 // All orders
  activeOrders: Order[];          // Non-completed orders
  isLoading: boolean;
  error: Error | null;
  syncState: SyncState;
  isReady: boolean;
  
  // Operations
  refreshOrders: () => Promise<void>;
  createOrder: (order: NewOrder) => Promise<Order>;
  updateOrder: (orderId: string, updates: Partial<OrderDocument>) => Promise<Order>;
  getOrder: (orderId: string) => Promise<Order | null>;
  deleteOrder: (orderId: string) => Promise<void>;
}
```

Features:
- Real-time order synchronization
- Conflict resolution for concurrent updates
- Event-driven updates via window events
- Automatic periodic refresh (30s intervals)
- Optimized queries with database indexing

## User Interface Components

### 1. Order Creation Interface (`/Users/<USER>/Desktop/rest/shop/app/components/NewOrderingInterface.tsx`)

Primary order creation component featuring:
- Menu item selection with categorization
- Size and addon configuration
- Pizza quarter customization
- Customer information capture
- Table assignment for dine-in orders
- Delivery person assignment
- Real-time total calculation
- Order preview and editing

### 2. Order Management (`/Users/<USER>/Desktop/rest/shop/app/components/WaiterOrderList.tsx`)

Waiter interface for order management:
- Filterable order list (status-based)
- Real-time search functionality
- Order status updates
- Table switching capabilities
- Order editing integration
- Customer information display

### 3. Kitchen Display (`/Users/<USER>/Desktop/rest/shop/components/KitchenDisplay.tsx`)

Kitchen workflow interface:
- Real-time active order display
- Status transition controls
- Order timing and priority
- Item-level completion tracking
- Kitchen printing integration
- Auto-refresh capabilities

### 4. Table Management (`/Users/<USER>/Desktop/rest/shop/components/tables/IntegratedTableManager.tsx`)

Table configuration and management:
- Table creation and configuration
- Seating capacity management
- Packaging configuration per table
- Table status tracking

## Business Logic & Services

### 1. Order Finance (`/Users/<USER>/Desktop/rest/shop/lib/services/simplified-order-finance.ts`)

Comprehensive payment processing service:

```typescript
interface OrderPaymentResult {
  orderId: string;
  success: boolean;
  error?: string;
  registeredInCaisse: boolean;
  sessionActivated?: boolean;
  orderUpdated?: boolean;
}
```

Process flow:
1. COGS calculation and consumption log creation
2. Order status update to completed/paid
3. Cash session integration
4. Transaction rollback capability
5. Financial metrics calculation

### 2. Kitchen Print Service (`/Users/<USER>/Desktop/rest/shop/lib/services/kitchen-print-service.ts`)

Automated kitchen order printing:
- Order receipt generation
- Kitchen ticket printing
- Delivery-specific formatting
- Auto-print on order creation
- Print queue management

### 3. Order Completion Tracking (`/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-completion-ops.ts`)

Barcode-based order completion:
- Item-level completion tracking
- Barcode generation and parsing
- Station-based workflow
- Quality control integration

## Integration Points

### 1. Inventory Management
- Real-time stock consumption tracking
- COGS calculation for profitability
- Waste management for failed deliveries
- Recipe-based ingredient tracking

### 2. Staff Management
- Role-based access control
- Staff attribution for orders
- Performance tracking
- Shift-based reporting

### 3. Financial System
- Cash session integration
- Transaction logging
- Payment method tracking
- Revenue recognition

### 4. Printing System
- Kitchen order printing
- Customer receipts
- Delivery documentation
- Barcode label generation

## API Endpoints & Data Flow

### Order Creation Flow
1. User creates order via `NewOrderingInterface`
2. Order validation and sanitization
3. Database insertion via `createOrder()`
4. Event dispatch for real-time updates
5. Kitchen print service activation
6. Inventory consumption logging

### Order Status Updates
1. Status change request via UI component
2. Status transition validation
3. Business logic execution (COGS, consumption)
4. Database update with conflict resolution
5. Event propagation to connected clients
6. Print service integration for status changes

### Payment Processing
1. Payment initiation via order finance service
2. COGS calculation and consumption logging
3. Order status update to completed
4. Cash session transaction creation
5. Financial metrics calculation
6. Receipt generation and printing

## Business Rules & Validation

### Order Validation Rules
- Table requirement for dine-in orders
- Customer information requirements by order type:
  - Delivery: Phone and address required
  - Takeaway: Phone optional
  - Dine-in: Customer info optional
- Status transition validation per business workflow
- Item quantity and pricing validation

### Financial Rules
- COGS calculation on order completion
- Profit margin tracking
- Discount application and audit
- Void tracking with reason codes
- Collection status for delivery orders

### Inventory Rules
- Stock consumption on order completion
- Waste tracking for failed deliveries
- Recipe-based consumption calculation
- Stock availability validation

## Data Synchronization & Offline Support

### Offline-First Architecture
- PouchDB local storage with CouchDB sync
- Conflict resolution for concurrent updates
- Queue management for offline operations
- Automatic retry mechanisms

### Real-time Updates
- Event-driven architecture with window events
- WebSocket-like behavior via PouchDB changes
- Multi-tab synchronization
- Cross-device order visibility

### Conflict Resolution
- Revision-based document versioning
- Automatic retry with exponential backoff
- Safe update patterns with rollback capability
- Transaction isolation for payment processing

## Key File References

### Core Schema & Types
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/schemas/order-schema.ts` (Lines 1-516)
- `/Users/<USER>/Desktop/rest/shop/lib/types/order-types.ts` (Lines 1-161)
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/utils/order-status-validation.ts` (Lines 1-110)

### Database Operations
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-ops.ts` (Lines 250-1311)
- `/Users/<USER>/Desktop/rest/shop/lib/db/v4/operations/order-completion-ops.ts` (Lines 1-100)

### React Hooks & Services
- `/Users/<USER>/Desktop/rest/shop/lib/hooks/use-order-v4.ts` (Lines 1-443)
- `/Users/<USER>/Desktop/rest/shop/lib/services/simplified-order-finance.ts` (Lines 1-150)

### UI Components
- `/Users/<USER>/Desktop/rest/shop/app/components/NewOrderingInterface.tsx` (Lines 1-100)
- `/Users/<USER>/Desktop/rest/shop/app/components/WaiterOrderList.tsx` (Lines 1-100)
- `/Users/<USER>/Desktop/rest/shop/components/KitchenDisplay.tsx` (Lines 1-100)
- `/Users/<USER>/Desktop/rest/shop/components/tables/IntegratedTableManager.tsx` (Lines 1-100)

### Application Pages
- `/Users/<USER>/Desktop/rest/shop/app/(protected)/ordering/page.tsx` (Lines 1-85)
- `/Users/<USER>/Desktop/rest/shop/app/(protected)/orders/page.tsx` (Lines 1-24)

## Performance & Scalability Considerations

### Database Indexing
- Compound indexes for order queries
- Status and date-based filtering optimization
- Table-specific order retrieval
- Payment status indexing

### Query Optimization
- Lazy loading for large order sets
- Pagination support
- Filtered result sets
- Background data synchronization

### Memory Management
- Component-level state isolation
- Event listener cleanup
- Periodic cache invalidation
- Efficient re-rendering patterns

## Security & Audit Trail

### Access Control
- Role-based component access
- Operation-level permissions
- Staff attribution tracking
- Session-based authentication

### Audit Capabilities
- Complete void history tracking
- Status change attribution
- Payment audit trail
- Delivery attempt logging
- Collection discrepancy tracking

### Data Integrity
- Schema validation at runtime
- Required field enforcement
- Status transition validation
- Financial calculation verification

This comprehensive order management system provides a robust foundation for restaurant operations with full lifecycle support, real-time capabilities, and extensive audit trails.# Printing System Workflow Analysis

This document provides a comprehensive analysis of the restaurant management system's printing capabilities, covering kitchen printing, receipt generation, and multi-platform support.

## System Architecture Overview

### Core Printing Components

The printing system is built around several interconnected services:

1. **Kitchen Print Service** (`/lib/services/kitchen-print-service.ts`) - Core kitchen printing logic
2. **Print Service** (`/lib/services/print-service.ts`) - Restaurant info and receipt templates
3. **Auto Print Service** (`/lib/services/auto-print-service.ts`) - Automated printing on order sync
4. **Barcode Service** - Item tracking and completion management

### Multi-Platform Printing Support

The system supports printing across different platforms:
- **Desktop (Electron)**: Full printer access with OS-level printer discovery
- **Web Browser**: Limited to browser print dialogs
- **Mobile**: Print jobs queued for desktop sync

## Kitchen Printing Systems

### Three Printing System Types

The system implements three distinct kitchen printing approaches:

#### 1. Single Station System
```typescript
// Features: { queueEnabled: false, barcodeEnabled: false }
- All order items print on one ticket at a single kitchen printer
- Expo/head chef reads ticket and delegates items to stations verbally
- Simple coordination, all items on one document
```

#### 2. Multi-Station System
```typescript
// Features: { queueEnabled: true, barcodeEnabled: false }
- Each station/printer gets only the items assigned to it (by category)
- POS splits order by station and prints relevant items at each printer
- Queue coordination info shows other stations' status
```

#### 3. Multi-Station + Barcode System
```typescript
// Features: { queueEnabled: true, barcodeEnabled: true }
- Same as Multi-Station but each ticket has unique barcodes for each item
- Cooks scan barcode when item is finished
- System tracks which items are "done" and prints expo tickets when complete
```

### Printer Configuration and Management

Located in `/lib/services/kitchen-print-service.ts`:

#### Printer Discovery
```typescript
interface OSPrinterInfo {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown';
  rawStatus: number; // OS status code
  type: 'thermal' | 'inkjet' | 'laser';
  description?: string;
  isDefault?: boolean;
  ipAddress?: string;
}
```

#### Printer Configuration
```typescript
interface PrinterConfig {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[]; // Categories assigned to this printer (station)
  type: 'thermal' | 'inkjet' | 'laser';
  simulated: boolean;
  isReceiptPrinter?: boolean; // Flag to identify receipt printers
}
```

### Order Routing and Item Assignment

The system uses sophisticated logic to route order items to appropriate printers:

1. **Direct Category Assignment**: Items with `categoryId` match printer's `assignedCategories`
2. **MenuItemId Matching**: When `menuItemId` matches a category ID
3. **Smart Keyword Matching**: Fallback using item name keywords
4. **Round-Robin Fallback**: Even distribution if no match found

```typescript
// From kitchen-print-service.ts:717-750
private isItemTypeMatch(itemName: string, categoryId: string): boolean {
  const flexibleKeywords = {
    'hot': ['pizza', 'burger', 'pasta', 'soup', 'grilled'],
    'cold': ['salad', 'sandwich', 'smoothie', 'ice cream'],
    'beverage': ['coffee', 'tea', 'juice', 'soda', 'water', 'drink'],
    'dessert': ['cake', 'cookie', 'chocolate', 'sweet']
  };
  // Smart matching logic...
}
```

### Print Job Generation

#### Barcode Generation
The system generates compact barcodes for kitchen item tracking:

```typescript
// From kitchen-print-service.ts:292-301
private generateSimpleBarcodeId(orderId: string, itemIndex: number): string {
  // Format: dailySequence (3 digits) + item index (3 digits)
  // Example: order 002, item #5 → "002005"
  const dailySeq = extractDailySequence(orderId); // 3-digit string
  const idxStr = itemIndex.toString().padStart(3, '0');
  return `${dailySeq}${idxStr}`;
}
```

#### Template System
Print templates are generated using HTML with thermal printer optimizations:

```typescript
// Thermal printer styling (58mm width)
const thermalStyles = `
  body {
    font-family: 'Courier New', monospace;
    width: 58mm;
    max-width: 220px;
    margin: 0;
    padding: 1px;
    line-height: 1.0;
  }
  @page {
    size: 58mm auto;
    margin: 0;
  }
`;
```

## Receipt Printing System

### Receipt Service Architecture

Located in `/lib/services/print-service.ts`, handles customer receipts:

#### Restaurant Information Management
```typescript
interface RestaurantInfo {
  name: string;
  address: string;
  phone: string;
  secondaryPhone?: string;
  logoUrl: string;
  footer: string;
}
```

#### Receipt Template Generation
The service generates professional receipt layouts with:
- Restaurant header with logo and contact info
- Itemized order details with prices
- Tax calculations and totals
- Payment information (cash, change)
- Footer message

### Receipt Components

#### Header Generation
```typescript
async generateRestaurantHeader(fontSize: {
  header: number;
  normal: number;
  bold: number;
}): Promise<string>
```

#### Footer Generation
```typescript
async generateRestaurantFooter(fontSize: {
  normal: number;
}): Promise<string>
```

## Auto-Print System

### Automatic Printing on Order Sync

Located in `/lib/services/auto-print-service.ts`, handles automated printing when orders arrive from mobile devices:

#### Configuration
```typescript
interface AutoPrintConfig {
  enabled: boolean;
  printOnOrderCreated: boolean; // Print local orders
  printOnOrderSynced: boolean;  // Print synced orders from mobile
  desktopOnly: boolean;         // Only work on desktop
  delayMs: number;             // Delay before printing
}
```

#### Event System
The auto-print service listens for:
- `order-created` events (local orders)
- `order-synced` events (mobile-to-desktop sync)
- `database-change` events (low-level DB changes)

#### Workflow
```
Mobile Device → Creates Order → Syncs via CouchDB → Desktop detects sync
→ AutoPrintService triggers → KitchenPrintService executes → Printers receive jobs
```

### Provider Integration

Located in `/components/providers/AutoPrintProvider.tsx`:
- React context for auto-print configuration
- Toast notifications for print success/failure
- Service lifecycle management

## Print Preview and Testing

### Print Preview System

#### Multi-Tab Preview (`/app/components/print/AllPrintPreview.tsx`)
- Shows all print jobs that will be generated for an order
- Separate tabs for each printer/station
- System-aware preview (different for each printing system)
- Confirmation flow before actual printing

#### Single Print Preview (`/app/components/print/PrintPreviewDialog.tsx`)
- Individual print job preview with iframe rendering
- Thermal printer paper simulation (58mm width)
- Print-to-window functionality for actual printing
- HTML download capability for testing

### Testing and Validation

#### Kitchen Printer Validator (`/components/debug/KitchenPrinterValidator.tsx`)
Comprehensive testing suite including:
- Category assignment validation
- Order routing tests
- System compatibility tests
- Print job quality verification

#### Test Coverage
```typescript
interface SystemTestResult {
  system: string;
  success: boolean;
  printJobCount: number;
  error?: string;
  validationPassed: boolean;
  routingPassed: boolean;
}
```

## Platform-Specific Implementation

### Electron Desktop Integration

Located in `/electron/src/index.ts`, provides:

#### System Printer Discovery
```typescript
ipcMain.handle('get-system-printers', async () => {
  // OS-level printer discovery
  // Returns array of available printers with status
});
```

#### USB Device Detection
```typescript
ipcMain.handle('get-usb-devices', async () => {
  // Detects USB barcode scanners and other devices
});
```

### Database Persistence

#### Printer Settings Storage (`/lib/db/v4/operations/printer-settings-ops.ts`)
```typescript
export const savePrinterSettings = async (printers: PrinterConfig[]): Promise<void>
export const loadPrinterSettings = async (): Promise<PrinterConfig[]>
```

## Error Handling and Reliability

### Print Job Error Handling
The system includes comprehensive error handling:

1. **Printer Offline Detection**: Monitors printer status and shows warnings
2. **Category Assignment Validation**: Ensures all menu categories have assigned printers
3. **Item Routing Verification**: Tests that all order items can be routed
4. **Retry Mechanisms**: Automatic retry for failed print jobs
5. **Fallback Routing**: Round-robin assignment when specific routing fails

### Production Safety Features

#### Development vs Production Mode
```typescript
// Production safety checks prevent mock printers in live environment
const isProduction = process.env.NODE_ENV === 'production';
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

if (isProduction && isElectron && printer.simulated) {
  throw new Error('Mock printers are not allowed in production builds');
}
```

#### Mock Printer Management
For development/testing:
- Automatic creation based on real menu categories
- Simulated printer status and responses
- Development-only visibility (blocked in production)

## Queue Management and Kitchen Coordination

### Kitchen Queue Service Integration
The printing system integrates with kitchen queue management:

#### Station Queue Coordination
```typescript
async getStationQueueContext(stationId: string): Promise<{
  totalPendingItems: number
}>
```

#### Queue Information on Tickets
Multi-station tickets include:
- Current station's queue status
- Other stations' queue counts
- Order coordination information

### Order Completion Tracking

#### Barcode Scanning System
```typescript
interface ItemStatus {
  orderId: string;
  itemId: string;
  itemName: string;
  status: 'pending' | 'done';
  scannedAt?: string;
  stationId?: string;
  createdAt?: string;
}
```

#### Expo Ticket Generation
When all items are scanned as complete:
- Automatic expo ticket generation
- "Ready for Assembly" notifications
- Order completion tracking

## Configuration and Settings

### Kitchen Printing Setup (`/components/settings/KitchenPrintingSetup.tsx`)

#### Printer Management Interface
- OS printer discovery and selection
- Category assignment interface  
- Receipt printer designation
- Real-time printer status monitoring

#### Feature Toggle System
```typescript
interface PrintingFeatures {
  queueEnabled: boolean;    // Show queue coordination info
  barcodeEnabled: boolean;  // Generate barcodes for item tracking
}
```

### Auto-Print Settings (`/components/settings/AutoPrintSettings.tsx`)
- Enable/disable automatic printing
- Configure print triggers (created vs synced orders)
- Set print delays and timing
- Desktop-only enforcement

## Performance and Optimization

### Print Job Optimization
- Estimated print time calculation (~10 lines per second for thermal)
- Content length optimization (target <100 lines)
- Barcode generation optimization
- Parallel printing for multi-station systems

### Memory Management
- Minimal event listener overhead
- No order caching in auto-print service
- Cleanup on component unmount
- Efficient category lookup caching

## Security Considerations

### Desktop-Only Operation
- Auto-print only works on desktop/Electron
- Mobile devices cannot trigger printing directly
- Prevents unauthorized print access

### Event Validation
- Order format validation in print service
- Source tracking (created vs synced)
- Error handling for malformed events

## Future Enhancement Capabilities

The system is designed for extensibility:

### Print Queue Management
- Queue orders during printer offline periods
- Retry failed prints automatically
- Priority-based printing

### Advanced Filtering
- Print only specific order types
- Category-based auto-print rules
- Time-based printing schedules

### Analytics Integration
- Print success/failure rate tracking
- Performance metrics collection
- Usage statistics and optimization insights

## Supported Printer Types and Hardware

### Thermal Printers
- 58mm paper width standard
- ESC/POS command support
- USB and network connectivity
- Popular models: Star TSP, Epson TM series

### Standard Printers
- Inkjet and laser printer support
- Standard paper sizes
- Network and USB connectivity
- Receipt-size formatting adaptation

### Barcode Scanner Integration
- USB HID barcode scanners
- Real-time scanning events
- Battery level monitoring (where supported)
- Kitchen workflow integration

This printing system provides a comprehensive, multi-platform solution for restaurant order management with robust error handling, extensive testing capabilities, and support for various kitchen workflow configurations.# Staff Management Workflow Documentation

## Overview

This document provides a comprehensive analysis of the Staff Management system in the restaurant management application. The system is built with an offline-first architecture using PouchDB/CouchDB for local data storage and MongoDB for authentication, supporting multiple platforms (web, mobile, desktop).

## Architecture Overview

### Multi-Database Architecture
- **PouchDB/CouchDB**: Local staff data, schedules, attendance, and operational data
- **MongoDB**: Authentication credentials and user management
- **Offline-First**: Full functionality without internet connection
- **Multi-Platform**: Web, Electron desktop, and Capacitor mobile apps

### Core File Structure
```
/app/(protected)/staff/               - Staff management UI
/lib/services/staff-service.ts        - Main staff service layer
/lib/services/staff-auth-service.ts   - Authentication service  
/lib/types/staff.ts                   - Staff type definitions
/lib/schemas/unified-staff-schema.ts  - Unified schema definitions
/components/staff/                    - Staff UI components
/app/api/staff/                       - Staff API endpoints
```

## 1. Staff Registration and Profile Management

### Staff Creation Process

**Main Entry Point**: `/app/(protected)/staff/page.tsx` (lines 865-1060)

**Workflow**:
1. **Form Submission** via `SimpleStaffForm` component
2. **Data Validation** using unified schema validation
3. **UUID Generation** for consistent ID across databases
4. **Local Storage** in PouchDB using `staff-service.ts`
5. **Optional Authentication** creation via MongoDB

**Key Implementation** (`/app/(protected)/staff/page.tsx`, lines 865-1046):
```typescript
const handleAddStaff = async (data: StaffFormData) => {
  // Generate UUID for both auth and staff documents
  let staffId = uuidv4();
  
  // Use unified-create API for complete staff creation
  const response = await fetch('/api/staff/unified-create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(requestData)
  });
}
```

### Data Storage Structure

**Staff Document Schema** (`/lib/schemas/unified-staff-schema.ts`, lines 191-210):
```typescript
export interface StaffDocument {
  _id: string; // Format: staff:{uuid}
  type: 'staff';
  id: string; // UUID v4
  name: string;
  role: StaffRole;
  contact?: string;
  email?: string;
  phone?: string;
  status: StaffStatus;
  paymentConfig: PaymentConfig;
  userId?: string;
  hasUserAccount: boolean;
  username?: string;
  permissions: StaffPermissions;
}
```

**Supported Staff Roles** (`/lib/types/staff.ts`, lines 8-17):
- MANAGER
- CHEF  
- WAITER
- BARTENDER
- HOST
- KITCHEN_HELPER
- CASHIER
- CLEANER
- DELIVERY

### Profile Management Features

**Update Process** (`/app/(protected)/staff/page.tsx`, lines 1063-1159):
- Real-time form updates
- Schedule modification support
- Payment configuration changes
- Permission updates
- Contact information management

## 2. Role-Based Permissions and Access Control

### Permission System Architecture

**Permission Types** (`/lib/hooks/use-permissions.ts`, lines 7-33):
- **Page Permissions**: Access to main application sections
- **Tab Permissions**: Fine-grained access within pages
- **Owner Override**: Full access for restaurant owners

**Permission Structure** (`/lib/hooks/use-permissions.ts`, lines 40-98):
```typescript
const DEFAULT_PERMISSIONS: UserPermissions = {
  pages: {
    menu: false,
    orders: false,
    finance: false,
    analytics: false,
    inventory: false,
    staff: false,
    settings: false,
    suppliers: false,
  },
  tabs: {
    inventory: {
      inventory: false,
      subrecipes: false,
      counts: false,
      waste: false,
    },
    staff: {
      shifts_schedule: false,
      attendance: false,
      payments: false,
    }
  }
};
```

### Authentication System

**Multi-Stage Authentication Process**:

1. **MongoDB Authentication** (`/app/api/auth/staff/login/route.ts`, lines 36-71):
   - Username/password verification
   - Role-based access control
   - JWT token generation

2. **PouchDB Permission Loading** (`/app/api/auth/staff/login/route.ts`, lines 74-100):
   - Staff-specific permissions from local database
   - Offline-capable permission storage
   - Non-blocking permission retrieval

**Staff Authentication Creation** (`/app/api/staff/auth/create/route.ts`, lines 32-164):
```typescript
export async function POST(request: NextRequest) {
  // Validate staff data
  const validation = createAuthSchema.safeParse(body);
  
  // Create MongoDB user
  const mongoUserResult = await createMongoUser({
    name: name || '',
    username,
    plaintextPassword: password,
    role: normalizedRole,
    restaurantId: cleanedRestaurantId,
    staffIdToLink: staffId
  });
}
```

### Role Utilities (`/lib/auth/role-utils.ts`):
- `isAdmin(user)`: Admin role checking
- `isOwner(user)`: Owner role verification  
- `canManageStaff(user)`: Staff management permissions
- `canManageSettings(user)`: Settings access control

## 3. Shift Scheduling and Time Tracking

### Shift Management System

**Shift Operations** (`/lib/db/v4/operations/shift-ops.ts`):

**Shift Creation** (lines 58-96):
```typescript
export async function createShift(
  name: string,
  startTime: string,
  endTime: string,
  color?: string
): Promise<ShiftsDocument['shifts'][0]> {
  const newShift = {
    id: uuidv4(),
    name,
    startTime,
    endTime,
    ...(color ? { color } : {})
  };
  
  // Update shifts document with conflict resolution
  const updatedDoc = await safeUpdateDocument<ShiftsDocument>(
    'shifts',
    (shiftsDoc) => ({
      ...shiftsDoc,
      shifts: [...shiftsDoc.shifts, newShift],
      updatedAt: new Date().toISOString()
    }),
    `createShift(${name})`
  );
}
```

### Schedule Management

**Weekly Schedule Structure** (`/lib/types/staff.ts`, lines 38-46):
```typescript
export interface WeeklySchedule {
  monday: string[];
  tuesday: string[];
  wednesday: string[];
  thursday: string[];
  friday: string[];
  saturday: string[];
  sunday: string[];
}
```

**Schedule Update Process** (`/lib/services/staff-service.ts`, lines 399-446):
```typescript
updateStaffWeeklySchedule: async (staffId: string, weeklySchedule: WeeklySchedule) => {
  // Save to database using per-staff schedule operations
  const schedule = await setStaffSchedule(staffId, {
    weeklySchedule: weeklySchedule,
    effectiveFrom: new Date().toISOString(),
    isActive: true,
  });
  
  // Backup to localStorage for offline access
  const schedules = staffService.loadSchedulesFromStorage();
  schedules[staffId] = {
    staffId: staffId,
    weeklySchedule: weeklySchedule,
    effectiveFrom: new Date().toISOString(),
    isActive: true,
  };
  staffService.saveSchedulesToStorage(schedules);
}
```

### Attendance Tracking

**Attendance Recording** (`/lib/services/staff-service.ts`, lines 326-389):
```typescript
recordShiftAttendance: async (
  staffId: string,
  shiftId: string,
  attendanceData: {
    date: string;
    status: 'present' | 'late' | 'absent';
    shiftName?: string;
    notes?: string;
  }
): Promise<AttendanceRecord> => {
  // Record in database
  const dbResult = await dbRecordStaffAttendance(
    staffId,
    attendanceData.date,
    attendanceData.status,
    shiftId,
    attendanceData.shiftName,
    attendanceData.notes
  );
  
  // Auto-sync with staff allowance system
  if (attendanceData.status === 'present' || attendanceData.status === 'late') {
    await markStaffPresent(
      staffId, 
      staffMember.name, 
      shiftId, 
      attendanceData.shiftName || 'Shift', 
      attendanceData.date
    );
  }
}
```

**Attendance Document Schema** (`/lib/schemas/unified-staff-schema.ts`, lines 225-234):
```typescript
export interface AttendanceDocument {
  _id: string; // Format: attendance:{uuid}
  type: 'attendance';
  staffId: string;
  records: AttendanceRecord[];
  schemaVersion: string;
  createdAt: string;
  updatedAt: string;
}
```

## 4. Staff Payment and Payroll Workflows

### Payment Configuration System

**Payment Types** (`/lib/types/staff.ts`, lines 19-27):
```typescript
export type PaymentType = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'PER_SHIFT';

export interface PaymentConfig {
  type: PaymentType;
  baseSalary: number;
  shiftRate?: number;
  shiftRates?: Record<string, number>; // Specific rates per shift ID
  paymentDay?: number; // Day of month/week for payments
  nextPaymentDueDate?: string;
}
```

### Payment System Interface

**Staff Payment System** (`/components/staff/payment/StaffPaymentSystem.tsx`):
- **Staff Selection**: Choose staff member for payment
- **Payment Mode**: Salary, per-shift, bonus, advance, deduction
- **Payment Processing**: Real-time payment calculation
- **Payment History**: Comprehensive payment tracking

**Payment Modes** (lines 14-15):
```typescript
export type PaymentMode = 'salary' | 'per-shift' | 'bonus' | 'advance' | 'deduction';
```

### Payment Document Schema

**Payment Storage** (`/lib/schemas/unified-staff-schema.ts`, lines 236-316):
```typescript
export interface PaymentDocument {
  _id: string; // Format: payment:YYYY-MM-DD-{uuid}
  type: 'payment';
  staffId: string;
  amount: number;
  paymentType: 'SALARY' | 'BONUS' | 'ADVANCE' | 'DEDUCTION' | 'SHIFT_PAYMENT';
  paymentDate: string;
  status: PaymentStatus;
  metadata?: {
    paidAttendanceIds?: string[];
    baseLogic?: BaseLogic;
    shiftBreakdown?: Array<{
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
      amount: number;
    }>;
    calculationBreakdown?: {
      baseSalary: number;
      shiftEarnings: number;
      bonusAmount: number;
      deductionAmount: number;
      finalAmount: number;
    };
  };
}
```

### Balance Management System

**Staff Balance Tracking** (`/lib/schemas/unified-staff-schema.ts`, lines 318-332):
```typescript
export interface BalanceDocument {
  _id: string; // Format: staff_balance:YYYY-MM-DD-{uuid}
  type: 'staff_balance';
  staffId: string;
  balanceType: BalanceType; // 'ADVANCE' | 'DEDUCTION' | 'BONUS'
  amount: number;
  reason: string;
  date: string;
  isUsed: boolean;
  usedInPaymentId?: string;
}
```

## 5. Performance Tracking and Management

### Performance Metrics

**Attendance Analytics**:
- Total hours worked per week/month
- Attendance rate calculation
- Shift completion tracking
- Late arrival tracking

**Performance Indicators** (`/lib/types/staff.ts`, lines 56-62):
```typescript
export interface StaffPresence {
  staffId: string;
  isPresent: boolean;
  attendanceHistory: AttendanceRecord[];
  totalHoursThisWeek: number;
  totalHoursThisMonth: number;
}
```

### Data Loading and Performance

**Staff with Schedules Loading** (`/lib/services/staff-service.ts`, lines 141-239):
```typescript
getStaffWithSchedules: async (): Promise<StaffMember[]> => {
  // Get basic staff documents
  const v4Staff = await getAllStaffMembers();
  
  // Load schedule for each staff member
  const staffWithSchedules = await Promise.all(
    v4Staff.map(async (staff) => {
      // Load schedule from database
      const scheduleData = await getStaffSchedule(staff.id);
      
      // Load attendance history
      const attendanceDoc = await dbGetStaffAttendance(staff.id);
      
      return convertedStaff;
    })
  );
}
```

## 6. Authentication and Session Management

### Multi-Stage Authentication Flow

1. **Initial Authentication** (`/app/api/auth/staff/login/route.ts`):
   - MongoDB credential verification
   - JWT token generation
   - Restaurant context establishment

2. **Permission Loading** (`/lib/hooks/use-permissions.ts`):
   - PouchDB permission retrieval
   - Role-based access control
   - Offline permission caching

3. **Session Management**:
   - JWT token refresh
   - Offline session persistence
   - Multi-device synchronization

### Staff Authentication Service

**Auth Creation Process** (`/lib/services/staff-auth-service.ts`, lines 21-201):
```typescript
export async function createStaffAuth(
  staffMember: StaffDocument,
  authData: {
    username: string;
    password: string;
  }
): Promise<{
  success: boolean;
  userId?: string;
  error?: string;
  offlineError?: boolean;
}> {
  // Validate staff member and auth data
  // Determine restaurant context
  // Create MongoDB authentication user
  // Update staff document with auth info
}
```

## 7. Data Synchronization and Offline Support

### Offline-First Architecture

**Key Features**:
- Full offline functionality
- Automatic sync when online
- Conflict resolution for concurrent updates
- Local data persistence

**Sync Mechanisms**:
- PouchDB-CouchDB replication
- MongoDB authentication sync
- Schedule backup to localStorage
- Attendance history caching

### Database Initialization

**V4 Database Setup** (`/app/(protected)/staff/page.tsx`, lines 616-703):
```typescript
useEffect(() => {
  const loadStaffData = async () => {
    // Initialize V4 database with restaurant context
    const restaurantId = getCurrentRestaurantId();
    await initializeV4Database(restaurantId);
    
    // Load staff with schedules
    const staffWithSchedules = await staffService.getStaffWithSchedules();
    setStaffList(adaptedStaff);
  };
}, [isAuthenticated]);
```

## 8. API Endpoints and Integration Points

### Key API Endpoints

**Staff Management APIs**:
- `POST /api/staff/unified-create` - Complete staff creation with auth
- `POST /api/staff/auth/create` - Authentication credential creation
- `POST /api/auth/staff/login` - Staff login endpoint
- `GET /api/admin/staff` - Admin staff overview

**Permission Management**:
- `GET /api/staff/permissions` - Retrieve staff permissions
- `PUT /api/staff/permissions/[staffId]` - Update staff permissions

### Integration with External Systems

**Authentication Integration**:
- MongoDB user management
- JWT token system
- Multi-restaurant support

**Data Storage Integration**:
- PouchDB for offline operations
- CouchDB for data persistence
- localStorage for backup storage

## 9. Security Considerations

### Data Protection

**Authentication Security**:
- Password hashing using bcrypt
- JWT token-based sessions
- Role-based access control
- Restaurant-specific data isolation

**Permission System**:
- Granular page/tab permissions
- Owner privilege override
- Staff-specific access controls
- Offline permission caching

### Offline Security

**Local Data Protection**:
- Client-side permission enforcement
- Secure local storage practices
- Data sync validation
- Conflict resolution security

## 10. Mobile and Multi-Platform Support

### Platform Compatibility

**Supported Platforms**:
- Web browsers (responsive design)
- Electron desktop applications
- Capacitor mobile apps (iOS/Android)
- Progressive Web App (PWA)

**Platform-Specific Features**:
- Offline-first operation on all platforms
- Platform-aware HTTP client
- Device-specific optimizations
- Cross-platform data synchronization

## Conclusion

The Staff Management system provides a comprehensive solution for restaurant staff operations with robust offline support, multi-platform compatibility, and detailed role-based access control. The system's architecture prioritizes data consistency, user experience, and operational reliability across all supported platforms.

The implementation demonstrates best practices for offline-first applications, with careful attention to data synchronization, conflict resolution, and user permission management. The modular design allows for easy maintenance and feature extension while maintaining backward compatibility.