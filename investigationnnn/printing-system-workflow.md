# Printing System Workflow Analysis

This document provides a comprehensive analysis of the restaurant management system's printing capabilities, covering kitchen printing, receipt generation, and multi-platform support.

## System Architecture Overview

### Core Printing Components

The printing system is built around several interconnected services:

1. **Kitchen Print Service** (`/lib/services/kitchen-print-service.ts`) - Core kitchen printing logic
2. **Print Service** (`/lib/services/print-service.ts`) - Restaurant info and receipt templates
3. **Auto Print Service** (`/lib/services/auto-print-service.ts`) - Automated printing on order sync
4. **Barcode Service** - Item tracking and completion management

### Multi-Platform Printing Support

The system supports printing across different platforms:
- **Desktop (Electron)**: Full printer access with OS-level printer discovery
- **Web Browser**: Limited to browser print dialogs
- **Mobile**: Print jobs queued for desktop sync

## Kitchen Printing Systems

### Three Printing System Types

The system implements three distinct kitchen printing approaches:

#### 1. Single Station System
```typescript
// Features: { queueEnabled: false, barcodeEnabled: false }
- All order items print on one ticket at a single kitchen printer
- Expo/head chef reads ticket and delegates items to stations verbally
- Simple coordination, all items on one document
```

#### 2. Multi-Station System
```typescript
// Features: { queueEnabled: true, barcodeEnabled: false }
- Each station/printer gets only the items assigned to it (by category)
- POS splits order by station and prints relevant items at each printer
- Queue coordination info shows other stations' status
```

#### 3. Multi-Station + Barcode System
```typescript
// Features: { queueEnabled: true, barcodeEnabled: true }
- Same as Multi-Station but each ticket has unique barcodes for each item
- Cooks scan barcode when item is finished
- System tracks which items are "done" and prints expo tickets when complete
```

### Printer Configuration and Management

Located in `/lib/services/kitchen-print-service.ts`:

#### Printer Discovery
```typescript
interface OSPrinterInfo {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown';
  rawStatus: number; // OS status code
  type: 'thermal' | 'inkjet' | 'laser';
  description?: string;
  isDefault?: boolean;
  ipAddress?: string;
}
```

#### Printer Configuration
```typescript
interface PrinterConfig {
  id: string;
  name: string;
  ipAddress?: string;
  status: 'online' | 'offline' | 'unknown';
  assignedCategories: string[]; // Categories assigned to this printer (station)
  type: 'thermal' | 'inkjet' | 'laser';
  simulated: boolean;
  isReceiptPrinter?: boolean; // Flag to identify receipt printers
}
```

### Order Routing and Item Assignment

The system uses sophisticated logic to route order items to appropriate printers:

1. **Direct Category Assignment**: Items with `categoryId` match printer's `assignedCategories`
2. **MenuItemId Matching**: When `menuItemId` matches a category ID
3. **Smart Keyword Matching**: Fallback using item name keywords
4. **Round-Robin Fallback**: Even distribution if no match found

```typescript
// From kitchen-print-service.ts:717-750
private isItemTypeMatch(itemName: string, categoryId: string): boolean {
  const flexibleKeywords = {
    'hot': ['pizza', 'burger', 'pasta', 'soup', 'grilled'],
    'cold': ['salad', 'sandwich', 'smoothie', 'ice cream'],
    'beverage': ['coffee', 'tea', 'juice', 'soda', 'water', 'drink'],
    'dessert': ['cake', 'cookie', 'chocolate', 'sweet']
  };
  // Smart matching logic...
}
```

### Print Job Generation

#### Barcode Generation
The system generates compact barcodes for kitchen item tracking:

```typescript
// From kitchen-print-service.ts:292-301
private generateSimpleBarcodeId(orderId: string, itemIndex: number): string {
  // Format: dailySequence (3 digits) + item index (3 digits)
  // Example: order 002, item #5 → "002005"
  const dailySeq = extractDailySequence(orderId); // 3-digit string
  const idxStr = itemIndex.toString().padStart(3, '0');
  return `${dailySeq}${idxStr}`;
}
```

#### Template System
Print templates are generated using HTML with thermal printer optimizations:

```typescript
// Thermal printer styling (58mm width)
const thermalStyles = `
  body {
    font-family: 'Courier New', monospace;
    width: 58mm;
    max-width: 220px;
    margin: 0;
    padding: 1px;
    line-height: 1.0;
  }
  @page {
    size: 58mm auto;
    margin: 0;
  }
`;
```

## Receipt Printing System

### Receipt Service Architecture

Located in `/lib/services/print-service.ts`, handles customer receipts:

#### Restaurant Information Management
```typescript
interface RestaurantInfo {
  name: string;
  address: string;
  phone: string;
  secondaryPhone?: string;
  logoUrl: string;
  footer: string;
}
```

#### Receipt Template Generation
The service generates professional receipt layouts with:
- Restaurant header with logo and contact info
- Itemized order details with prices
- Tax calculations and totals
- Payment information (cash, change)
- Footer message

### Receipt Components

#### Header Generation
```typescript
async generateRestaurantHeader(fontSize: {
  header: number;
  normal: number;
  bold: number;
}): Promise<string>
```

#### Footer Generation
```typescript
async generateRestaurantFooter(fontSize: {
  normal: number;
}): Promise<string>
```

## Auto-Print System

### Automatic Printing on Order Sync

Located in `/lib/services/auto-print-service.ts`, handles automated printing when orders arrive from mobile devices:

#### Configuration
```typescript
interface AutoPrintConfig {
  enabled: boolean;
  printOnOrderCreated: boolean; // Print local orders
  printOnOrderSynced: boolean;  // Print synced orders from mobile
  desktopOnly: boolean;         // Only work on desktop
  delayMs: number;             // Delay before printing
}
```

#### Event System
The auto-print service listens for:
- `order-created` events (local orders)
- `order-synced` events (mobile-to-desktop sync)
- `database-change` events (low-level DB changes)

#### Workflow
```
Mobile Device → Creates Order → Syncs via CouchDB → Desktop detects sync
→ AutoPrintService triggers → KitchenPrintService executes → Printers receive jobs
```

### Provider Integration

Located in `/components/providers/AutoPrintProvider.tsx`:
- React context for auto-print configuration
- Toast notifications for print success/failure
- Service lifecycle management

## Print Preview and Testing

### Print Preview System

#### Multi-Tab Preview (`/app/components/print/AllPrintPreview.tsx`)
- Shows all print jobs that will be generated for an order
- Separate tabs for each printer/station
- System-aware preview (different for each printing system)
- Confirmation flow before actual printing

#### Single Print Preview (`/app/components/print/PrintPreviewDialog.tsx`)
- Individual print job preview with iframe rendering
- Thermal printer paper simulation (58mm width)
- Print-to-window functionality for actual printing
- HTML download capability for testing

### Testing and Validation

#### Kitchen Printer Validator (`/components/debug/KitchenPrinterValidator.tsx`)
Comprehensive testing suite including:
- Category assignment validation
- Order routing tests
- System compatibility tests
- Print job quality verification

#### Test Coverage
```typescript
interface SystemTestResult {
  system: string;
  success: boolean;
  printJobCount: number;
  error?: string;
  validationPassed: boolean;
  routingPassed: boolean;
}
```

## Platform-Specific Implementation

### Electron Desktop Integration

Located in `/electron/src/index.ts`, provides:

#### System Printer Discovery
```typescript
ipcMain.handle('get-system-printers', async () => {
  // OS-level printer discovery
  // Returns array of available printers with status
});
```

#### USB Device Detection
```typescript
ipcMain.handle('get-usb-devices', async () => {
  // Detects USB barcode scanners and other devices
});
```

### Database Persistence

#### Printer Settings Storage (`/lib/db/v4/operations/printer-settings-ops.ts`)
```typescript
export const savePrinterSettings = async (printers: PrinterConfig[]): Promise<void>
export const loadPrinterSettings = async (): Promise<PrinterConfig[]>
```

## Error Handling and Reliability

### Print Job Error Handling
The system includes comprehensive error handling:

1. **Printer Offline Detection**: Monitors printer status and shows warnings
2. **Category Assignment Validation**: Ensures all menu categories have assigned printers
3. **Item Routing Verification**: Tests that all order items can be routed
4. **Retry Mechanisms**: Automatic retry for failed print jobs
5. **Fallback Routing**: Round-robin assignment when specific routing fails

### Production Safety Features

#### Development vs Production Mode
```typescript
// Production safety checks prevent mock printers in live environment
const isProduction = process.env.NODE_ENV === 'production';
const isElectron = typeof window !== 'undefined' && (window as any).electronAPI;

if (isProduction && isElectron && printer.simulated) {
  throw new Error('Mock printers are not allowed in production builds');
}
```

#### Mock Printer Management
For development/testing:
- Automatic creation based on real menu categories
- Simulated printer status and responses
- Development-only visibility (blocked in production)

## Queue Management and Kitchen Coordination

### Kitchen Queue Service Integration
The printing system integrates with kitchen queue management:

#### Station Queue Coordination
```typescript
async getStationQueueContext(stationId: string): Promise<{
  totalPendingItems: number
}>
```

#### Queue Information on Tickets
Multi-station tickets include:
- Current station's queue status
- Other stations' queue counts
- Order coordination information

### Order Completion Tracking

#### Barcode Scanning System
```typescript
interface ItemStatus {
  orderId: string;
  itemId: string;
  itemName: string;
  status: 'pending' | 'done';
  scannedAt?: string;
  stationId?: string;
  createdAt?: string;
}
```

#### Expo Ticket Generation
When all items are scanned as complete:
- Automatic expo ticket generation
- "Ready for Assembly" notifications
- Order completion tracking

## Configuration and Settings

### Kitchen Printing Setup (`/components/settings/KitchenPrintingSetup.tsx`)

#### Printer Management Interface
- OS printer discovery and selection
- Category assignment interface  
- Receipt printer designation
- Real-time printer status monitoring

#### Feature Toggle System
```typescript
interface PrintingFeatures {
  queueEnabled: boolean;    // Show queue coordination info
  barcodeEnabled: boolean;  // Generate barcodes for item tracking
}
```

### Auto-Print Settings (`/components/settings/AutoPrintSettings.tsx`)
- Enable/disable automatic printing
- Configure print triggers (created vs synced orders)
- Set print delays and timing
- Desktop-only enforcement

## Performance and Optimization

### Print Job Optimization
- Estimated print time calculation (~10 lines per second for thermal)
- Content length optimization (target <100 lines)
- Barcode generation optimization
- Parallel printing for multi-station systems

### Memory Management
- Minimal event listener overhead
- No order caching in auto-print service
- Cleanup on component unmount
- Efficient category lookup caching

## Security Considerations

### Desktop-Only Operation
- Auto-print only works on desktop/Electron
- Mobile devices cannot trigger printing directly
- Prevents unauthorized print access

### Event Validation
- Order format validation in print service
- Source tracking (created vs synced)
- Error handling for malformed events

## Future Enhancement Capabilities

The system is designed for extensibility:

### Print Queue Management
- Queue orders during printer offline periods
- Retry failed prints automatically
- Priority-based printing

### Advanced Filtering
- Print only specific order types
- Category-based auto-print rules
- Time-based printing schedules

### Analytics Integration
- Print success/failure rate tracking
- Performance metrics collection
- Usage statistics and optimization insights

## Supported Printer Types and Hardware

### Thermal Printers
- 58mm paper width standard
- ESC/POS command support
- USB and network connectivity
- Popular models: Star TSP, Epson TM series

### Standard Printers
- Inkjet and laser printer support
- Standard paper sizes
- Network and USB connectivity
- Receipt-size formatting adaptation

### Barcode Scanner Integration
- USB HID barcode scanners
- Real-time scanning events
- Battery level monitoring (where supported)
- Kitchen workflow integration

This printing system provides a comprehensive, multi-platform solution for restaurant order management with robust error handling, extensive testing capabilities, and support for various kitchen workflow configurations.