# 🌐 Bistro Restaurant SaaS - API Endpoints Documentation

## 📋 Overview

This document provides comprehensive documentation of all API endpoints in the Bistro restaurant management system. The API follows RESTful principles with Next.js API routes and includes authentication, authorization, and comprehensive error handling.

## 🔐 Authentication System

### 🏢 Restaurant Authentication

#### `POST /api/auth/restaurant`
**Purpose**: Authenticate restaurant using phone number and password

**Request Body**:
```typescript
{
  phoneNumber: string;    // Restaurant phone number
  password: string;       // Restaurant password
}
```

**Response**:
```typescript
{
  success: boolean;
  restaurant: {
    id: string;
    phoneNumber: string;
    email?: string;
    users: string[];      // Array of user IDs
  };
  token: string;          // JWT token for session
}
```

**Business Logic**:
- Validates phone number format and existence
- Verifies password using bcrypt comparison
- Checks restaurant restriction status
- Generates JWT token with restaurant context
- Returns restaurant data and user list

#### `POST /api/auth/logout`
**Purpose**: Invalidate current authentication session

**Headers**:
```
Authorization: Bearer <jwt_token>
```

**Response**:
```typescript
{
  success: boolean;
  message: string;
}
```

### 👥 User Authentication

#### `POST /api/auth/user`
**Purpose**: Authenticate user within restaurant context

**Request Body**:
```typescript
{
  restaurantId: string;   // Restaurant context
  username: string;       // Username or email
  password: string;       // User password
}
```

**Response**:
```typescript
{
  success: boolean;
  user: {
    id: string;
    name: string;
    role: string;
    permissions: Permissions;
    restaurantId: string;
  };
  token: string;          // Updated JWT with user context
}
```

**Business Logic**:
- Validates user exists within restaurant
- Verifies password and account status
- Checks user and restaurant restrictions
- Updates JWT token with user permissions
- Returns complete user profile with permissions

#### `POST /api/auth/check-restriction`
**Purpose**: Check if user/restaurant is restricted

**Request Body**:
```typescript
{
  userId: string;
  role: string;
  restaurantId?: string;
}
```

**Response**:
```typescript
{
  isRestricted: boolean;
  reason?: 'user' | 'owner' | 'error';
  ownerName?: string;
  message: string;
}
```

**Business Logic**:
- Implements hierarchical restriction checking
- Owner restriction affects all restaurant users
- Returns detailed restriction information
- Used for real-time access control

## 👥 Staff Management APIs

### 📋 Staff Operations

#### `GET /api/staff/list`
**Purpose**: Get all staff members for restaurant

**Headers**:
```
Authorization: Bearer <jwt_token>
```

**Query Parameters**:
```
?status=active|inactive|all    // Filter by status
&role=manager|chef|server      // Filter by role
&limit=50                      // Limit results
&offset=0                      // Pagination offset
```

**Response**:
```typescript
{
  success: boolean;
  staff: StaffMember[];
  total: number;
  pagination: {
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}
```

#### `POST /api/staff/create`
**Purpose**: Create new staff member

**Request Body**:
```typescript
{
  name: string;
  email?: string;
  phone?: string;
  role: 'manager' | 'chef' | 'server' | 'cashier' | 'delivery';
  paymentConfig: {
    type: 'salary' | 'hourly' | 'per_shift';
    baseSalary?: number;
    shiftRate?: number;
  };
  hasUserAccount?: boolean;
  username?: string;
  password?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  staff: StaffMember;
  user?: AuthUser;        // If user account created
}
```

**Business Logic**:
- Validates staff data and prevents duplicates
- Creates staff document with unique ID
- Optionally creates user account with permissions
- Sets up default payment configuration
- Links staff to restaurant context

#### `PUT /api/staff/[staffId]`
**Purpose**: Update existing staff member

**Request Body**:
```typescript
{
  name?: string;
  email?: string;
  phone?: string;
  role?: string;
  status?: 'active' | 'inactive';
  paymentConfig?: PaymentConfig;
}
```

**Response**:
```typescript
{
  success: boolean;
  staff: StaffMember;
}
```

#### `DELETE /api/staff/[staffId]`
**Purpose**: Delete staff member (soft delete)

**Response**:
```typescript
{
  success: boolean;
  message: string;
}
```

### 📅 Staff Scheduling APIs

#### `GET /api/staff/schedule/[staffId]`
**Purpose**: Get staff member's schedule

**Response**:
```typescript
{
  success: boolean;
  schedule: {
    staffId: string;
    weeklySchedule: {
      monday: string[];     // Array of shift IDs
      tuesday: string[];
      // ... other days
    };
    effectiveFrom: string;
    isActive: boolean;
  };
}
```

#### `POST /api/staff/schedule/[staffId]`
**Purpose**: Update staff member's schedule

**Request Body**:
```typescript
{
  weeklySchedule: {
    monday: string[];       // Shift IDs
    tuesday: string[];
    // ... other days
  };
  effectiveFrom?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  schedule: ScheduleDocument;
}
```

### 📊 Staff Attendance APIs

#### `GET /api/staff/attendance/[staffId]`
**Purpose**: Get staff attendance records

**Query Parameters**:
```
?startDate=2024-01-01     // Filter start date
&endDate=2024-01-31       // Filter end date
&status=present|absent    // Filter by status
```

**Response**:
```typescript
{
  success: boolean;
  attendance: AttendanceRecord[];
  summary: {
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateCount: number;
    totalHours: number;
  };
}
```

#### `POST /api/staff/attendance/record`
**Purpose**: Record staff attendance

**Request Body**:
```typescript
{
  staffId: string;
  date: string;           // ISO date
  shiftId: string;
  status: 'present' | 'late' | 'absent';
  notes?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  attendance: AttendanceRecord;
}
```

**Business Logic**:
- Validates staff exists and shift is scheduled
- Prevents duplicate attendance records
- Auto-syncs with staff allowance system
- Updates payroll calculations
- Sends notifications if configured

## 🛒 Order Management APIs

### 📋 Order Operations

#### `GET /api/orders`
**Purpose**: Get orders with filtering and pagination

**Query Parameters**:
```
?status=pending|preparing|served|completed|cancelled
&orderType=dine-in|takeaway|delivery
&tableId=table_123
&startDate=2024-01-01
&endDate=2024-01-31
&limit=50
&offset=0
```

**Response**:
```typescript
{
  success: boolean;
  orders: OrderDocument[];
  total: number;
  pagination: PaginationInfo;
  summary: {
    totalRevenue: number;
    averageOrderValue: number;
    orderCount: number;
  };
}
```

#### `POST /api/orders/create`
**Purpose**: Create new order

**Request Body**:
```typescript
{
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  tableId?: string;
  items: Array<{
    menuItemId: string;
    quantity: number;
    size?: string;
    notes?: string;
    addons?: Array<{
      id: string;
      name: string;
      price: number;
    }>;
    // Custom pizza support
    compositeType?: 'pizza_quarters';
    quarters?: PizzaQuarter[];
  }>;
  customer?: {
    name: string;
    phone: string;
    address?: string;
  };
  deliveryPerson?: DeliveryPerson;
  notes?: string;
  discountType?: 'percentage' | 'fixed_amount';
  discountValue?: number;
  discountReason?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  order: OrderDocument;
  kitchenTicket?: string;   // Kitchen ticket ID if printed
}
```

**Business Logic**:
- Generates unique order ID with date-based sequence
- Validates menu items and calculates pricing
- Applies discounts and calculates totals
- Creates kitchen queue entries
- Prints kitchen tickets automatically
- Updates table status for dine-in orders
- Handles custom pizza quarter calculations

#### `PUT /api/orders/[orderId]/status`
**Purpose**: Update order status

**Request Body**:
```typescript
{
  status: 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';
  notes?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  order: OrderDocument;
}
```

**Business Logic**:
- Validates status transition rules
- Updates kitchen queue status
- Triggers stock consumption on completion
- Calculates COGS and profit metrics
- Updates freelancer stats for delivery orders
- Sends status notifications

#### `POST /api/orders/[orderId]/payment`
**Purpose**: Process order payment

**Request Body**:
```typescript
{
  paymentMethod: 'cash' | 'card' | 'online' | 'mixed';
  amountPaid: number;
  receivedAmount?: number;  // For cash payments
  splitPayments?: Array<{   // For mixed payments
    method: string;
    amount: number;
  }>;
}
```

**Response**:
```typescript
{
  success: boolean;
  order: OrderDocument;
  receipt?: {
    receiptId: string;
    printData: string;
  };
  change?: number;
}
```

**Business Logic**:
- Validates payment amounts and methods
- Calculates change for cash payments
- Updates cash register balance
- Marks order as completed
- Triggers stock consumption and COGS calculation
- Generates and prints receipt
- Updates financial analytics

### 🍽️ Order Item Management

#### `POST /api/orders/[orderId]/items`
**Purpose**: Add item to existing order

**Request Body**:
```typescript
{
  menuItemId: string;
  quantity: number;
  size?: string;
  notes?: string;
  addons?: OrderAddon[];
}
```

#### `PUT /api/orders/[orderId]/items/[itemId]`
**Purpose**: Update order item

**Request Body**:
```typescript
{
  quantity?: number;
  notes?: string;
  addons?: OrderAddon[];
}
```

#### `DELETE /api/orders/[orderId]/items/[itemId]`
**Purpose**: Remove item from order (void item)

**Request Body**:
```typescript
{
  reason: string;
  quantityToVoid?: number;  // Partial void support
}
```

**Response**:
```typescript
{
  success: boolean;
  order: OrderDocument;
  voidEntry: VoidEntry;
}
```

## 🍽️ Menu Management APIs

### 📋 Menu Operations

#### `GET /api/menu`
**Purpose**: Get complete menu structure

**Response**:
```typescript
{
  success: boolean;
  menu: {
    categories: MenuCategory[];
    lastUpdated: string;
  };
}
```

#### `PUT /api/menu`
**Purpose**: Update entire menu structure

**Request Body**:
```typescript
{
  categories: MenuCategory[];
}
```

**Response**:
```typescript
{
  success: boolean;
  menu: MenuDocument;
}
```

### 📂 Category Management

#### `POST /api/menu/categories`
**Purpose**: Add new menu category

**Request Body**:
```typescript
{
  name: string;
  description?: string;
  color?: string;
  displayOrder?: number;
  sizes?: string[];
}
```

**Response**:
```typescript
{
  success: boolean;
  category: MenuCategory;
}
```

#### `PUT /api/menu/categories/[categoryId]`
**Purpose**: Update menu category

**Request Body**:
```typescript
{
  name?: string;
  description?: string;
  color?: string;
  isActive?: boolean;
  displayOrder?: number;
}
```

#### `DELETE /api/menu/categories/[categoryId]`
**Purpose**: Delete menu category

**Response**:
```typescript
{
  success: boolean;
  message: string;
  itemsAffected: number;    // Number of items that were deleted
}
```

### 🍕 Menu Item Management

#### `POST /api/menu/categories/[categoryId]/items`
**Purpose**: Add item to category

**Request Body**:
```typescript
{
  name: string;
  description?: string;
  prices: {
    [sizeName: string]: number;
  };
  image?: string;
  allergens?: string[];
  preparationTime?: number;
}
```

**Response**:
```typescript
{
  success: boolean;
  item: MenuItem;
}
```

#### `PUT /api/menu/categories/[categoryId]/items/[itemId]`
**Purpose**: Update menu item

**Request Body**:
```typescript
{
  name?: string;
  description?: string;
  prices?: {
    [sizeName: string]: number;
  };
  isActive?: boolean;
  displayOrder?: number;
}
```

#### `DELETE /api/menu/categories/[categoryId]/items/[itemId]`
**Purpose**: Delete menu item

**Response**:
```typescript
{
  success: boolean;
  message: string;
}
```

## 📦 Inventory Management APIs

### 📊 Inventory Operations

#### `GET /api/inventory`
**Purpose**: Get complete inventory data

**Query Parameters**:
```
?category=food|beverage|supplies
&lowStock=true              // Only items below minimum
&inactive=false             // Include inactive items
```

**Response**:
```typescript
{
  success: boolean;
  inventory: {
    stockItems: StockItem[];
    lowStockItems: StockItem[];
    totalValue: number;
    lastUpdated: string;
  };
}
```

#### `POST /api/inventory/items`
**Purpose**: Add new stock item

**Request Body**:
```typescript
{
  name: string;
  description?: string;
  category: string;
  unit: string;
  currentQuantity: number;
  minimumQuantity: number;
  costPerUnit: number;
  supplier?: string;
  barcode?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  stockItem: StockItem;
}
```

#### `PUT /api/inventory/items/[itemId]`
**Purpose**: Update stock item

**Request Body**:
```typescript
{
  name?: string;
  description?: string;
  minimumQuantity?: number;
  costPerUnit?: number;
  supplier?: string;
  isActive?: boolean;
}
```

### 🔄 Stock Adjustments

#### `POST /api/inventory/adjustments`
**Purpose**: Record stock adjustment

**Request Body**:
```typescript
{
  stockItemId: string;
  adjustmentType: 'increase' | 'decrease' | 'correction';
  quantity: number;
  reason: string;
  notes?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  adjustment: StockAdjustment;
  updatedStockItem: StockItem;
}
```

**Business Logic**:
- Validates stock item exists and is active
- Updates current quantity based on adjustment type
- Recalculates average cost for increases
- Creates audit trail entry
- Triggers low stock alerts if applicable

### 🛒 Purchase Management

#### `POST /api/inventory/purchases`
**Purpose**: Record stock purchase

**Request Body**:
```typescript
{
  stockItemId: string;
  supplier: string;
  quantity: number;
  unitCost: number;
  invoiceNumber?: string;
  deliveryDate: string;
  expiryDate?: string;
  notes?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  purchase: PurchaseLog;
  updatedStockItem: StockItem;
}
```

**Business Logic**:
- Updates stock quantity and average cost
- Records purchase in audit trail
- Updates supplier information
- Calculates total purchase cost
- Updates inventory valuation

### 🗑️ Waste Tracking

#### `POST /api/inventory/waste`
**Purpose**: Record waste/loss

**Request Body**:
```typescript
{
  stockItemId: string;
  quantity: number;
  reason: 'expired' | 'damaged' | 'spoiled' | 'overproduction' | 'other';
  description?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  wasteLog: WasteLog;
  updatedStockItem: StockItem;
}
```

## 💰 Financial Management APIs

### 💵 Cash Register Operations

#### `GET /api/finance/cash-register`
**Purpose**: Get current cash register status

**Query Parameters**:
```
?date=2024-01-01           // Specific date
&includeTransactions=true   // Include transaction details
```

**Response**:
```typescript
{
  success: boolean;
  cashRegister: {
    currentBalance: number;
    openingBalance: number;
    totalIn: number;
    totalOut: number;
    transactions: CashTransaction[];
    lastTransaction: string;
  };
}
```

#### `POST /api/finance/cash-register/transaction`
**Purpose**: Record manual cash transaction

**Request Body**:
```typescript
{
  type: 'manual_in' | 'manual_out';
  amount: number;
  description: string;
  category?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  transaction: CashTransaction;
  newBalance: number;
}
```

**Business Logic**:
- Validates transaction amount and description
- Updates cash register balance
- Creates transaction record with timestamp
- Updates financial analytics
- Triggers alerts for large transactions

#### `POST /api/finance/cash-register/reconcile`
**Purpose**: Reconcile cash register at end of day

**Request Body**:
```typescript
{
  countedAmount: number;
  notes?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  reconciliation: {
    expectedAmount: number;
    countedAmount: number;
    difference: number;
    isBalanced: boolean;
    reconciliationId: string;
  };
}
```

### 📊 Financial Reporting

#### `GET /api/finance/reports/sales`
**Purpose**: Get sales report

**Query Parameters**:
```
?startDate=2024-01-01
&endDate=2024-01-31
&groupBy=day|week|month
&includeItems=true
```

**Response**:
```typescript
{
  success: boolean;
  report: {
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    topSellingItems: ItemSales[];
    salesByPeriod: PeriodSales[];
    paymentMethodBreakdown: PaymentBreakdown;
  };
}
```

#### `GET /api/finance/reports/profit-loss`
**Purpose**: Get profit & loss report

**Query Parameters**:
```
?startDate=2024-01-01
&endDate=2024-01-31
```

**Response**:
```typescript
{
  success: boolean;
  report: {
    revenue: {
      totalSales: number;
      salesByCategory: CategorySales[];
    };
    costs: {
      totalCogs: number;
      cogsBreakdown: CogsBreakdown[];
    };
    expenses: {
      totalExpenses: number;
      expensesByCategory: ExpenseBreakdown[];
    };
    profit: {
      grossProfit: number;
      netProfit: number;
      profitMargin: number;
    };
  };
}
```

## 🚚 Delivery Management APIs

### 🚚 Delivery Operations

#### `GET /api/delivery/orders`
**Purpose**: Get delivery orders

**Query Parameters**:
```
?status=pending|out_for_delivery|delivered|failed
&driverId=driver_123
&date=2024-01-01
```

**Response**:
```typescript
{
  success: boolean;
  orders: OrderDocument[];
  summary: {
    totalDeliveries: number;
    pendingDeliveries: number;
    completedDeliveries: number;
    failedDeliveries: number;
  };
}
```

#### `POST /api/delivery/assign-driver`
**Purpose**: Assign driver to delivery order

**Request Body**:
```typescript
{
  orderId: string;
  driverType: 'staff' | 'freelance';
  driverId?: string;        // For staff drivers
  driverPhone?: string;     // For freelance drivers
  driverName?: string;      // For new freelance drivers
  paymentRate?: number;     // For freelance drivers
}
```

**Response**:
```typescript
{
  success: boolean;
  order: OrderDocument;
  driver: StaffMember | FreelancerDocument;
}
```

**Business Logic**:
- Validates driver availability
- Creates freelancer record if new
- Updates order with driver information
- Sends notification to driver
- Updates delivery tracking status

#### `PUT /api/delivery/[orderId]/status`
**Purpose**: Update delivery status

**Request Body**:
```typescript
{
  status: 'out_for_delivery' | 'delivered' | 'failed';
  notes?: string;
  failureReason?: string;
  deliveryAttempt?: {
    attemptedAt: string;
    status: string;
    notes?: string;
  };
}
```

**Response**:
```typescript
{
  success: boolean;
  order: OrderDocument;
}
```

### 🚚 Freelancer Management

#### `GET /api/delivery/freelancers`
**Purpose**: Get freelancer drivers

**Query Parameters**:
```
?active=true
&sortBy=totalDeliveries|totalEarnings|lastActive
```

**Response**:
```typescript
{
  success: boolean;
  freelancers: FreelancerDocument[];
  summary: {
    totalFreelancers: number;
    activeFreelancers: number;
    totalDeliveries: number;
    totalEarnings: number;
  };
}
```

#### `POST /api/delivery/freelancers`
**Purpose**: Register new freelancer

**Request Body**:
```typescript
{
  name: string;
  phone: string;
  email?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  freelancer: FreelancerDocument;
}
```

#### `PUT /api/delivery/freelancers/[phone]/stats`
**Purpose**: Update freelancer statistics

**Request Body**:
```typescript
{
  deliveryCompleted?: boolean;
  orderValue?: number;
  rating?: number;
}
```

**Response**:
```typescript
{
  success: boolean;
  freelancer: FreelancerDocument;
}
```

## 🔄 Synchronization APIs

### 🌐 Sync Management

#### `GET /api/sync/status`
**Purpose**: Get synchronization status

**Response**:
```typescript
{
  success: boolean;
  syncStatus: {
    isConnected: boolean;
    lastSync: string;
    connectedDevices: number;
    pendingChanges: number;
    syncErrors: SyncError[];
  };
}
```

#### `POST /api/sync/trigger`
**Purpose**: Manually trigger synchronization

**Request Body**:
```typescript
{
  syncType?: 'full' | 'incremental';
  targetDevices?: string[];
}
```

**Response**:
```typescript
{
  success: boolean;
  syncId: string;
  message: string;
}
```

#### `GET /api/sync/discover-peers`
**Purpose**: Discover available sync peers

**Response**:
```typescript
{
  success: boolean;
  devices: Array<{
    id: string;
    ip: string;
    port: number;
    type: 'desktop' | 'mobile';
    lastSeen: string;
    isOnline: boolean;
  }>;
}
```

#### `POST /api/sync/register-device`
**Purpose**: Register device for internet sync

**Request Body**:
```typescript
{
  type: 'desktop' | 'mobile';
  ip: string;
  port: number;
  couchdbUrl?: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  deviceId: string;
  message: string;
}
```

## 🔧 System Administration APIs

### ⚙️ Settings Management

#### `GET /api/settings`
**Purpose**: Get restaurant settings

**Response**:
```typescript
{
  success: boolean;
  settings: RestaurantSettings;
}
```

#### `PUT /api/settings`
**Purpose**: Update restaurant settings

**Request Body**:
```typescript
{
  restaurantInfo?: {
    name?: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  operationalSettings?: {
    businessHours?: BusinessHours;
    timezone?: string;
    currency?: string;
    taxRate?: number;
  };
  features?: {
    deliveryEnabled?: boolean;
    tableService?: boolean;
    inventoryTracking?: boolean;
  };
}
```

**Response**:
```typescript
{
  success: boolean;
  settings: RestaurantSettings;
}
```

### 🪑 Table Management

#### `GET /api/tables`
**Purpose**: Get all tables

**Response**:
```typescript
{
  success: boolean;
  tables: TableDocument[];
}
```

#### `POST /api/tables`
**Purpose**: Create new table

**Request Body**:
```typescript
{
  tableNumber: string;
  capacity: number;
  location?: string;
}
```

#### `PUT /api/tables/[tableId]/status`
**Purpose**: Update table status

**Request Body**:
```typescript
{
  status: 'available' | 'occupied' | 'reserved' | 'out_of_order';
  orderId?: string;
  notes?: string;
}
```

## 🔍 Analytics & Reporting APIs

### 📊 Analytics Dashboard

#### `GET /api/analytics/dashboard`
**Purpose**: Get dashboard analytics

**Query Parameters**:
```
?period=today|week|month|custom
&startDate=2024-01-01
&endDate=2024-01-31
```

**Response**:
```typescript
{
  success: boolean;
  analytics: {
    sales: {
      totalRevenue: number;
      totalOrders: number;
      averageOrderValue: number;
      revenueGrowth: number;
    };
    operations: {
      busyHours: HourlyData[];
      popularItems: ItemPopularity[];
      tableUtilization: number;
      averageServiceTime: number;
    };
    financial: {
      grossProfit: number;
      profitMargin: number;
      totalExpenses: number;
      cashFlow: number;
    };
    staff: {
      totalHours: number;
      attendanceRate: number;
      productivityScore: number;
    };
  };
}
```

#### `GET /api/analytics/trends`
**Purpose**: Get trend analysis

**Query Parameters**:
```
?metric=revenue|orders|customers
&period=daily|weekly|monthly
&compare=previous_period|last_year
```

**Response**:
```typescript
{
  success: boolean;
  trends: {
    current: DataPoint[];
    comparison: DataPoint[];
    growth: number;
    trend: 'up' | 'down' | 'stable';
  };
}
```

## 🚨 Error Handling & Status Codes

### 📋 Standard Response Format
```typescript
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}
```

### 🔢 HTTP Status Codes
- **200 OK**: Successful operation
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict (e.g., duplicate)
- **422 Unprocessable Entity**: Validation errors
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### 🛡️ Security Headers
All API responses include security headers:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000
```

## 🔐 Authentication & Authorization

### 🎫 JWT Token Structure
```typescript
interface JWTPayload {
  restaurantId: string;
  userId?: string;
  role?: string;
  permissions?: string[];
  iat: number;           // Issued at
  exp: number;           // Expires at
  iss: string;           // Issuer
}
```

### 🛡️ Permission Validation
Each protected endpoint validates:
1. **Token validity**: JWT signature and expiration
2. **Restaurant context**: User belongs to restaurant
3. **Role permissions**: User has required role
4. **Component access**: User can access specific features
5. **Action permissions**: User can perform specific actions

### 🔒 Rate Limiting
- **Authentication endpoints**: 5 requests per minute
- **Data modification**: 100 requests per minute
- **Read operations**: 1000 requests per minute
- **File uploads**: 10 requests per minute

---

This comprehensive API documentation covers all major endpoints in the Bistro restaurant management system. Each endpoint includes proper authentication, validation, error handling, and business logic to ensure reliable and secure operations. The API design follows RESTful principles while providing the flexibility needed for complex restaurant management workflows. 🏆