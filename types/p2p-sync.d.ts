// P2P Sync Types
export interface PeerInfo {
  id: string;
  ip: string;
  address: string;
  port: number;
  hostname: string;
  restaurantName?: string;
  restaurantId?: string;
  platform?: 'desktop' | 'mobile' | 'unknown';
  lastSeen?: Date;
  isOnline?: boolean;
  verified?: boolean;
  dbUrl?: string;
  responseTime?: number;
  auth?: {
    requiresAuth: boolean;
    username?: string;
    password?: string;
  };
}

export interface SyncStatus {
  peerId: string;
  dbName: string;
  status: 'active' | 'complete' | 'paused' | 'error';
  direction: 'push' | 'pull' | 'both';
  details?: any;
}

export interface P2PSyncAPI {
  // Get all discovered peers on the network
  getPeers: () => Promise<PeerInfo[]>;
  
  // Get the status of all active syncs
  getSyncStatus: () => Promise<SyncStatus[]>;
  
  // Get the mDNS status
  getMdnsStatus: () => Promise<'not_running' | 'running' | 'error'>;
  
  // Get the device ID
  getSystemId: () => Promise<string>;
  
  // Get the server port
  getServerPort: () => Promise<number>;
  
  // Get published service info (name, type, domain, port, txt records, ip)
  getServiceInfo: () => Promise<{ name: string; type: string; domain: string; port: number; txt: Record<string,string>; ip: string }>;
  
  // Start syncing with a peer
  startSync: (peerId: string, dbName: string, direction?: 'push' | 'pull' | 'both') => Promise<{ success: boolean, error?: string }>;
  
  // Stop syncing with a peer for a specific database
  stopSync: (peerId: string, dbName: string) => Promise<{ success: boolean, error?: string }>;
  
  // Stop all syncs with a peer
  stopAllSyncsWithPeer: (peerId: string) => Promise<{ success: boolean, error?: string }>;
  
  // Event listeners
  onPeerDiscovered: (callback: (peerInfo: PeerInfo) => void) => void;
  onPeerLost: (callback: (peerId: string) => void) => void;
  onSyncStatusUpdated: (callback: (status: SyncStatus) => void) => void;
}

// Database related API
export interface DatabaseAPI {
  ensureDbOpened: (dbIdentifier: string) => Promise<any>;
  get: (dbIdentifier: string, docId: string, options?: any) => Promise<any>;
  put: (dbIdentifier: string, doc: any) => Promise<any>;
  remove: (dbIdentifier: string, docId: string, rev: string) => Promise<any>;
  bulkDocs: (dbIdentifier: string, docsParam: Array<any> | { docs: Array<any> }, options?: any) => Promise<any>;
  createIndex: (dbIdentifier: string, indexDefinition: any) => Promise<any>;
  find: (dbIdentifier: string, findRequest: any) => Promise<any>;
  close: (dbIdentifier: string) => Promise<any>;
  destroy: (dbIdentifier: string) => Promise<any>;
}

// Complete ElectronAPI interface
export interface ElectronAPI {
  send: (channel: string, data: any) => void;
  receive: (channel: string, func: (...args: any[]) => void) => void;
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  database: DatabaseAPI;
  p2pSync: P2PSyncAPI;
}

// Extend the Window interface to include our ElectronAPI
declare global {
  interface Window {
    electronAPI: ElectronAPI;
    IS_DESKTOP_APP: boolean;
  }
} 