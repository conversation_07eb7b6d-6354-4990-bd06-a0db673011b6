import { ReactNode } from 'react';

// Declare module for multi-user auth provider
declare module '@/lib/context/multi-user-auth-provider' {
  export interface User {
    id: string;
    name: string;
    email?: string;
    role: string;
    restaurantId: string;
    permissions?: any;
  }

  export interface UserSession {
    id: string;
    user: User;
    token: string;
    refreshToken?: string;
    lastActive: string;
    isOnline: boolean;
    loginTimestamp: string;
    deviceFingerprint: string;
  }

  export interface UseAuthReturn {
    // Core auth state
    isAuthenticated: boolean;
    user: User | null;
    loading: boolean;
    error: string | null;
    isRestricted: boolean;
    
    // Add permissions to the user object
    permissions: any;
    
    // Auth actions
    login: (credentials: { identifier: string; password: string; restaurantId?: string; isStaffLogin?: boolean }) => Promise<boolean>;
    logout: () => void;
    logoutAll: () => void;
    register: (userData: { name: string; email: string; password: string; phoneNumber?: string }) => Promise<boolean>;
    
    // Role-based properties
    isAdmin: boolean;
    isOwner: boolean;
    canManageStaff: boolean;
    
    // Offline capabilities
    offlineLogin: (role?: string) => Promise<boolean>;
    isOfflineMode: boolean;
    
    // Multi-user specific properties
    availableUsers: UserSession[];
    switchToUser: (userId: string) => boolean;
    addUserSession: (credentials: { identifier: string; password: string; isStaffLogin?: boolean }) => Promise<boolean>;
    removeUserSession: (userId: string) => void;
    hasMultipleUsers: boolean;
    canSwitchUsers: boolean;
    getSessionStats: () => {
      totalSessions: number;
      onlineSessions: number;
      offlineSessions: number;
      roles: Record<string, number>;
      lastActiveSession: string | null;
    };
    refreshCurrentSession: () => Promise<boolean>;
    checkRestrictionStatus: () => Promise<boolean>;
    restaurantId: string | null;
  }

  export function MultiUserAuthProvider(props: { children: ReactNode }): JSX.Element;
  export function useAuth(): UseAuthReturn;
}