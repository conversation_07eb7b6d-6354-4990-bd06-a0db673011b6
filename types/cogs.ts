export interface RecipeIngredient {
  stockItemId: string;
  quantity: number;
}

export interface SubRecipeIngredient {
  subRecipeId: string;
  quantity: number;
}

export interface SubRecipe {
  _id: string;
  name: string;
  ingredients: RecipeIngredient[];
  yield: {
    quantity: number;
    unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml';
  };
  costPerUnit?: number;
  lastProduced?: string;
  createdAt: string;
  updatedAt: string;
  currentStock?: number;
}

export interface MenuItemRecipe {
  _id: string;
  menuItemId: string;
  menuItemName?: string;
  size?: string;
  ingredients: (RecipeIngredient | SubRecipeIngredient)[];
  costPerUnit?: number;
  createdAt: string;
  updatedAt: string;
}

export interface ProductionBatch {
  _id: string;
  subRecipeId: string;
  batchSize: number;
  producedQuantity: number;
  date: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export type SubRecipeFormData = Omit<SubRecipe, '_id' | 'costPerUnit' | 'lastProduced' | 'createdAt' | 'updatedAt'>;
export type MenuItemRecipeFormData = Omit<MenuItemRecipe, '_id' | 'costPerUnit' | 'createdAt' | 'updatedAt'>;
export type ProductionBatchFormData = Omit<ProductionBatch, '_id' | 'performedBy' | 'createdAt' | 'updatedAt'>;
