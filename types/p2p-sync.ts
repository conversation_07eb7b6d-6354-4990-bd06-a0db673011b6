export interface PeerInfo {
  id: string;
  ip: string;
  port: number;
  hostname: string;
  restaurantId?: string;
  verified?: boolean;
  responseTime?: number;
  lastSeen?: Date;
  auth?: {
    username?: string;
    password?: string;
    requiresAuth?: boolean;
  };
}

export interface SyncStatus {
  peerId: string;
  dbName: string;
  status: 'active' | 'paused' | 'complete' | 'error';
  direction?: 'push' | 'pull' | 'both';
  error?: string;
  pauseReason?: string;  // Add reason for paused state
  progress?: {
    docs_read?: number;
    docs_written?: number;
    pending?: number;
    docsPushed?: number;  // For backward compatibility with Electron
    docsPulled?: number;  // For backward compatibility with Electron
    totalDocs?: number;   // For backward compatibility with Electron
  };
  details?: {
    error?: string;       // For backward compatibility with Electron
    progress?: {
      docsPushed?: number;
      docsPulled?: number;
      totalDocs?: number;
    };
  };
} 