// Import types from p2p-sync.ts
import { PeerInfo, SyncStatus } from './p2p-sync';

// Add or update the p2pSync API interface in the Window interface definition
interface Window {
  IS_DESKTOP_APP?: boolean;
  isRedirecting?: boolean;
  p2pLog?: {
    onLog: (callback: (message: string) => void) => void;
  };
  electronAPI?: {
    // IPC renderer for direct invocations
    ipcRenderer: {
      invoke: (channel: string, ...args: any[]) => Promise<any>;
    };
    
    // P2P sync API
    p2pSync: {
      // Get all discovered peers on the network
      getPeers: () => Promise<PeerInfo[]>;
      
      // Get the status of all active syncs
      getSyncStatus: () => Promise<SyncStatus[]>;
      
      // Get the current mDNS status
      getMdnsStatus: () => Promise<'not_running' | 'running' | 'error'>;
      
      // Get system ID (device ID) for diagnostics
      getSystemId: () => Promise<string>;
      
      // Get server port for diagnostics
      getServerPort: () => Promise<number>;
      
      // Get service info for diagnostics
      getServiceInfo: () => Promise<any>;
      
      // Get info about the mDNS service
      getMdnsInfo: () => Promise<{
        published: boolean;
        name?: string;
        type?: string;
        port?: number;
        diagnostics?: any;
      }>;
      
      // Get detailed mDNS diagnostics
      getMdnsDiagnostics: () => Promise<any>;
      
      // Restart the mDNS service
      restartMdns: () => Promise<{
        success: boolean;
        diagnostics?: any;
        error?: string;
      }>;
      
      // Reset the mDNS system to recover from errors
      resetMdns: () => Promise<{ success: boolean; error?: string }>;
      
      // Start syncing with a peer
      startSync: (peerId: string, dbName: string, direction?: 'push' | 'pull' | 'both') => Promise<any>;
      
      // Stop syncing with a peer for a specific database
      stopSync: (peerId: string, dbName: string) => Promise<any>;
      
      // Stop all syncs with a peer
      stopAllSyncsWithPeer: (peerId: string) => Promise<any>;
      
      // Event listeners for peer discovery and sync status
      onPeerDiscovered: (callback: (peerInfo: PeerInfo) => void) => void;
      onPeerLost: (callback: (peerId: string) => void) => void;
      onSyncStatusUpdated: (callback: (status: SyncStatus) => void) => void;
    };
    
    // ... other API definitions ...
  };
} 