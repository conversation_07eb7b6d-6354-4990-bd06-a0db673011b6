/**
 * Electron API Type Definitions
 * These type definitions help TypeScript understand the Electron IPC API
 */

// Generic IPC response type
interface IPCResponse<T> {
  ok: boolean;
  error?: {
    message: string;
    status?: number;
  };
  data?: T;
}

interface ElectronAPI {
  pouchdb: {
    // Staff operations
    addStaffMember: (dbIdentifier: string, staffMember: any) => Promise<any>;
    getAllStaff: (dbIdentifier: string) => Promise<any[]>;
    getStaffById: (dbIdentifier: string, staffId: string) => Promise<any>;
    updateStaffMember: (dbIdentifier: string, staffId: string, updates: any) => Promise<any>;
    deleteStaffMember: (dbIdentifier: string, staffId: string) => Promise<IPCResponse<{ id: string }>>;
    
    // Generic PouchDB operations
    get: (dbIdentifier: string, docId: string) => Promise<any>;
    put: (dbIdentifier: string, doc: any) => Promise<IPCResponse<any>>;
    remove: (dbIdentifier: string, docId: string, rev?: string) => Promise<IPCResponse<any>>;
    getAll: (dbIdentifier: string, options?: any) => Promise<any[]>;
  };
  
   // Auto-updater methods
  onUpdateAvailable: (callback: (info: any) => void) => void;
  onDownloadProgress: (callback: (progress: any) => void) => void;
  onUpdateDownloaded: (callback: (info: any) => void) => void;
  onUpdateError: (callback: (error: any) => void) => void;
  quitAndInstall: () => Promise<void>;
  
  // Generic IPC methods
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  send: (channel: string, ...args: any[]) => void;
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
}

// Extend the Window interface
interface Window {
  electronAPI?: ElectronAPI;
}