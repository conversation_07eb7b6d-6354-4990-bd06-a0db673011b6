export interface StockCount {
  id: string;
  name: string;
  date: string;
  status: 'draft' | 'in_progress' | 'completed';
  countType: 'full' | 'partial' | 'cycle';
  countArea: 'all' | 'kitchen' | 'bar' | 'storage';
  notes?: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface StockCountItem {
  id: string;
  stockCountId: string;
  stockItemId: string;
  theoreticalQuantity: number;
  countedQuantity?: number;
  variance?: number;
  varianceValue?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WasteLog {
  id: string;
  stockItemId: string;
  quantity: number;
  reason: 'expired' | 'damaged' | 'spilled' | 'contaminated' | 'cooking_error' | 'returned' | 'other';
  notes?: string;
  date: string;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}

export type StockCountFormData = Omit<StockCount, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>;
export type StockCountItemFormData = Omit<StockCountItem, 'id' | 'createdAt' | 'updatedAt' | 'variance' | 'varianceValue'>;
export type WasteLogFormData = Omit<WasteLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy' | 'date'>;
