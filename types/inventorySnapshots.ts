export interface SnapshotItem {
  stockItemId: string;
  itemName: string; // Denormalized for easier reporting
  unit: string; // Denormalized
  quantityAtSnapshot: number;
  costPerUnitAtSnapshot: number; // From StockItem.costPerUnit (weighted avg) at snapshot time
  totalValueAtSnapshot: number;
}

export interface InventorySnapshot {
  _id: string; // e.g., "snapshot_YYYY-MM-DD" or "snapshot_uuid"
  date: string; // ISO timestamp of when the snapshot was taken
  items: SnapshotItem[];
  totalValue: number; // Sum of all item.totalValueAtSnapshot
  notes?: string;
  performedBy?: string;
  createdAt: string;
  updatedAt?: string; // Good practice to include for potential future updates
} 