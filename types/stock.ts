export interface StockItem {
  id: string;
  name: string;
  category: string;
  unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml';
  supplierId?: string;
  quantity?: number;
  minLevel?: number;
  costPerUnit?: number;
  expiryDate?: string;
  createdAt: string;
  updatedAt: string;
  purchaseUnits?: PurchaseUnit[];
}

export interface PurchaseUnit {
  id: string;
  name: string;
  conversionToBase: number;
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StockAdjustment {
  id: string;
  stockItemId: string;
  adjustmentType: 'addition' | 'reduction';
  quantity: number;
  date: string;
  reason?: string;
  performedBy: string;
}

export type StockItemFormData = Omit<StockItem, 'id' | 'createdAt' | 'updatedAt'>;
export type StockAdjustmentFormData = Omit<StockAdjustment, 'id' | 'date' | 'performedBy'>;

// New interface to track purchase expenses
export interface PurchaseLog {
  id: string;
  stockItemId: string;
  date: string;
  quantity: number;
  unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml';
  purchaseUnitId?: string;
  baseQuantity: number;
  costPerUnit?: number;
  costPerBaseUnit?: number;
  totalCost: number;
  supplierId?: string;
  notes?: string;
  hasReceiptImage?: boolean;
  performedBy: string;
  createdAt: string;
  updatedAt: string;
}