{"$schema": "https://json.schemastore.org/package.json", "name": "bistro-web", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "node scripts/build.js web", "build:web": "node scripts/build.js web", "build:clean": "npm run build && npm run clean:sourcemaps", "start": "NODE_ENV=production node server.js", "lint": "next lint", "clean:sourcemaps": "node ./scripts/clean-sourcemaps.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.2.4", "@types/bcryptjs": "^2.4.6", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "express": "^5.1.0", "get-port": "^7.1.0", "googleapis": "^149.0.0", "i18next": "^25.0.2", "input-otp": "^1.4.1", "jose": "^6.0.10", "jsbarcode": "^3.11.6", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.469.0", "mongodb": "^6.16.0", "motion": "^12.5.0", "nano": "^10.1.4", "next": "^15.3.1", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "pouchdb-browser": "^9.0.0", "pouchdb-core": "^9.0.0", "pouchdb-find": "^9.0.0", "pouchdb-http": "^6.0.2", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.1", "react-resizable-panels": "^2.1.7", "react-to-print": "^3.0.6", "recharts": "^2.15.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "ws": "^8.18.1", "zod": "^3.24.1"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@types/aws-sdk": "^0.0.42", "@types/json-schema": "^7.0.15", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/nano": "^6.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/socket.io": "^3.0.1", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "ignore-loader": "^0.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-eslint": "^8.33.0", "workbox-build": "^7.3.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0"}}