surround everything in knowledge before making a change to not cause problems and conflicts be cautious

-use emojies and simple way when chatting to explain dont explain in code

nextjs fullstack pouch couchdb electron
capacitor for mobile 
index in web 
RESTO DATA IS OFFLINE LOCAL ONLY
we have a couchdb server run locally pouch syncs with
 if u dont know a best practice or find errors dont guess check docs using the mcp
dont make test files 
(dont run/build the app with commands)
you MUST USE shadcn ui ux minimalistic compact design space efficient