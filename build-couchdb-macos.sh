#!/bin/bash
set -e

# 📦 Variables
SRC_DIR="/Users/<USER>/Desktop/rest/shop/here/apache-couchdb-3.5.0"
BUILD_DIR="$SRC_DIR"
DEST_DIR="/Users/<USER>/Desktop/rest/shop/resources/couchdb-macos"

# 🏁 Start
cd "$SRC_DIR"
echo "🚀 Starting CouchDB build from source at $SRC_DIR"

# 1️⃣ Install Xcode Command Line Tools (if needed)
echo "🔧 Checking for Xcode Command Line Tools..."
xcode-select -p >/dev/null 2>&1 || xcode-select --install

# 2️⃣ Install Homebrew if missing
if ! command -v brew >/dev/null 2>&1; then
  echo "❌ Homebrew not found. Please install Homebrew first."
  exit 1
fi

# 3️⃣ Install dependencies
echo "📦 Installing dependencies..."
brew install autoconf autoconf-archive automake libtool erlang icu4c pkg-config openssl@3 help2man python@3.11 spidermonkey

# 4️⃣ Set environment variables for SpiderMonkey
SPIDERMONKEY_PREFIX="$(brew --prefix spidermonkey)"
export LDFLAGS="-L$SPIDERMONKEY_PREFIX/lib"
export CPPFLAGS="-I$SPIDERMONKEY_PREFIX/include/mozjs-128"
export PKG_CONFIG_PATH="$SPIDERMONKEY_PREFIX/lib/pkgconfig"
echo "🧬 Using SpiderMonkey at $SPIDERMONKEY_PREFIX"

# 5️⃣ Configure the build
echo "⚙️ Configuring CouchDB..."
./configure --spidermonkey-version 128

# 6️⃣ Build CouchDB
echo "🔨 Building CouchDB..."
make release

# 7️⃣ Copy the built binaries
echo "📦 Copying built CouchDB to $DEST_DIR..."
rm -rf "$DEST_DIR"
mkdir -p "$DEST_DIR"
cp -R rel/couchdb/* "$DEST_DIR/"

# 8️⃣ Done!
echo "🎉 CouchDB build complete! Binaries are in $DEST_DIR" 