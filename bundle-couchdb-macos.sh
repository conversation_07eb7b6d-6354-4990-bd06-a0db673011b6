#!/bin/bash
set -e

# 🏁 Start in the app root directory
cd "$(dirname "$0")"

# 📦 Variables
temp_dir="couchdb-build-tmp"
src_dir="$temp_dir/couchdb-src"
bundle_dir="electron/resources/couchdb-macos"

# 🧹 Clean up any previous temp build
echo "Cleaning up previous temp build..."
rm -rf "$temp_dir"

# 🏗️ Create temp build directory
mkdir -p "$temp_dir"

# 🛠️ Check for Homebrew and install if missing
if ! command -v brew >/dev/null 2>&1; then
  echo "Homebrew not found. Please install Homebrew first."
  exit 1
fi

# 🛠️ Install build dependencies if missing
deps=(autoconf autoconf-archive automake libtool erlang icu4c spidermonkey pkg-config)
for dep in "${deps[@]}"; do
  if ! brew list "$dep" >/dev/null 2>&1; then
    echo "Installing $dep..."
    brew install "$dep"
  fi
done

# 🧬 Clone CouchDB source
echo "Cloning CouchDB source..."
git clone --depth 1 https://github.com/apache/couchdb.git "$src_dir"

# ⚙️ Build CouchDB
cd "$src_dir"
echo "Configuring CouchDB..."

# Get SpiderMonkey version dynamically
SPIDERMONKEY_VERSION=$(brew list --versions spidermonkey | grep -o '[0-9]\+' | head -1)
echo "Using SpiderMonkey version: $SPIDERMONKEY_VERSION"

./configure --spidermonkey-version $SPIDERMONKEY_VERSION
echo "Building CouchDB..."
make release

# 📦 Bundle CouchDB
cd ../../
echo "Preparing bundle directory..."
rm -rf "$bundle_dir"
mkdir -p "$bundle_dir"
cp -R "$src_dir/rel/couchdb/"* "$bundle_dir/"

# 🧹 Clean up temp build
echo "Cleaning up temp files..."
rm -rf "$temp_dir"

# 🔑 Fix permissions
echo "Fixing permissions..."
chmod -R 770 "$bundle_dir"

# ✅ Done
echo "🎉 CouchDB is bundled in $bundle_dir!" 