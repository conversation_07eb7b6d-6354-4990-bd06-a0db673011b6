import { Loader2 } from 'lucide-react';

export default function Loading() {
  return (
    <div className="flex items-center justify-center w-full h-80">
      <div className="flex flex-col items-center">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        <h3 className="mt-4 text-lg font-medium">Loading orders...</h3>
        <p className="text-sm text-muted-foreground mt-1">Preparing order data and ensuring indexes</p>
      </div>
    </div>
  );
} 