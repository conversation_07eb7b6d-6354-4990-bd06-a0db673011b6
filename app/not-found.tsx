import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <h1 className="text-9xl font-bold text-gray-300">404</h1>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Page introuvable
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            La page que vous recherchez n'existe pas ou a été déplacée.
          </p>
        </div>
        
        <div className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/">
              Retour à l'accueil
            </Link>
          </Button>
          
          <Button variant="outline" asChild className="w-full">
            <Link href="/auth">
              Se connecter
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}