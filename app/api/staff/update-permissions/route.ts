import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyJwtAuth } from "@/lib/api-middleware";

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;

// import { updatePermissions } from "@/lib/staff/staff-creator";

// Validation schema for updating staff permissions
const updatePermissionsSchema = z.object({
  staffId: z.string().uuid("Staff ID must be a valid UUID"),
  permissions: z.object({
    pages: z.object({
      menu: z.boolean(),
      orders: z.boolean(),
      waiter: z.boolean(),
      kitchen: z.boolean(),
      finance: z.boolean(),
      analytics: z.boolean(),
      inventory: z.boolean(),
      production: z.boolean(),
      staff: z.boolean(),
      settings: z.boolean(),
      suppliers: z.boolean(),
    })
  }),
});

/**
 * Update staff permissions
 *
 * This endpoint updates the permissions for a staff member.
 */
export async function POST(request: NextRequest) {
  try {
    console.log("API: /api/staff/update-permissions - Request received");

    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", {
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the authenticated user
    console.log("API: User authenticated", {
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });

    // Verify that the user is authorized to update staff permissions
    // Only restaurant owners or admins should be able to do this
    const { user } = authResult;

    if (user.role !== 'owner' && user.role !== 'admin') {
      console.error("API: Insufficient permissions", {
        userRole: user.role,
        requiredRoles: ['owner', 'admin'],
        userId: user.id
      });
      return NextResponse.json(
        { error: "Insufficient permissions to update staff permissions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = updatePermissionsSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validation.error.formErrors.fieldErrors
        },
        { status: 400 }
      );
    }

    const { staffId, permissions } = validation.data;

    // Update the staff permissions using the updatePermissions function
    // const staffMember = await updatePermissions(
    //   staffId,
    //   permissions,
    //   user.restaurantId
    // );

    // TODO: Implement or re-export updatePermissions if needed

    // Return the updated staff member
    return NextResponse.json({
      success: true,
      // staffMember
    });
  } catch (error) {
    console.error("API: Error in update-permissions:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
