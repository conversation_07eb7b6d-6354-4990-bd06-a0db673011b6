import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { addAuthToStaff } from "@/lib/staff/staff-creator";

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


// Validation schema for adding auth to staff
const addAuthSchema = z.object({
  staffId: z.string().uuid("Staff ID must be a valid UUID"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

/**
 * Add auth credentials to an existing staff member
 *
 * This endpoint adds auth credentials to an existing staff member
 * with consistent IDs between the auth document and the staff document.
 */
export async function POST(request: NextRequest) {
  try {
    console.log("API: /api/staff/add-auth - Request received");

    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", {
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the authenticated user
    console.log("API: User authenticated", {
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });

    // Verify that the user is authorized to add auth to staff
    // Only restaurant owners or admins should be able to do this
    const { user } = authResult;

    if (user.role !== 'owner' && user.role !== 'admin') {
      console.error("API: Insufficient permissions", {
        userRole: user.role,
        requiredRoles: ['owner', 'admin'],
        userId: user.id
      });
      return NextResponse.json(
        { error: "Insufficient permissions to add auth to staff" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validation = addAuthSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validation.error.formErrors.fieldErrors
        },
        { status: 400 }
      );
    }

    const { staffId, username, password } = validation.data;

    // Add auth to the staff member using the addAuthToStaff function
    const staffMember = await addAuthToStaff(
      staffId,
      {
        username,
        password
      },
      user.restaurantId
    );

    // Return the updated staff member
    return NextResponse.json({
      success: true,
      staffMember
    });
  } catch (error) {
    console.error("API: Error in add-auth:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
