// import { getUserByUsername } from "@/lib/couchdb-auth";
import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


export async function GET(request: NextRequest) {
  console.log('[API] MONGODB_URI (masked):', process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/\/\/.*:.*@/, '//USER:PASSWORD@') : 'NOT SET');
  try {
    // Verify JWT authentication
    const authResult = await verifyJwtAuth(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get username and restaurantId from query params
    const searchParams = request.nextUrl.searchParams;
    const username = searchParams.get('username');
    const restaurantId = searchParams.get('restaurantId') || authResult.user?.restaurantId;
    
    if (!username) {
      return NextResponse.json(
        { error: "Username parameter is required" },
        { status: 400 }
      );
    }
    
    if (!restaurantId) {
      return NextResponse.json(
        { error: "Restaurant ID parameter is required" },
        { status: 400 }
      );
    }
    
    // Check if username exists
    // const existingUser = await getUserByUsername(username, restaurantId);
    const existingUser = null; // Placeholder for debugging
    
    // Return availability status
    return NextResponse.json({
      available: !existingUser,
      username
    });
    
  } catch (error) {
    console.error("Error checking username availability:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
} 