import { NextRequest, NextResponse } from 'next/server';
import { GoogleDriveService } from '@/lib/services/google-drive-service';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


export async function GET(request: NextRequest) {
  try {
    console.log('🔗 Google Drive download API called');

    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');
    const restaurantId = searchParams.get('restaurantId');

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID is required' },
        { status: 400 }
      );
    }

    // For download URLs, we can generate them without restaurant-specific config
    // since Google Drive file IDs are globally accessible if you have the right permissions
    const googleDriveService = new GoogleDriveService();
    
    // If restaurantId is provided, try to initialize with restaurant config
    if (restaurantId) {
      await googleDriveService.initializeFromMongoDB(restaurantId);
    }

    const result = await googleDriveService.getDownloadUrl(fileId);

    if (result.success) {
      console.log('✅ Download URL generated successfully');
      return NextResponse.json({
        success: true,
        url: result.url
      });
    } else {
      console.error('❌ Failed to get download URL:', result.error);
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Google Drive download API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to get download URL' 
      },
      { status: 500 }
    );
  }
} 