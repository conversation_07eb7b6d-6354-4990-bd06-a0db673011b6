import { NextRequest, NextResponse } from 'next/server';
import { getEnvironmentInfo } from '@/lib/utils/environment';
import { v4 as uuidv4 } from 'uuid';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


export async function GET(request: NextRequest) {
  try {
    const environmentInfo = getEnvironmentInfo();
    
    // Only serve discovery for desktop/Electron instances
    if (environmentInfo.buildTarget !== 'electron' && environmentInfo.buildTarget !== 'static') {
      return NextResponse.json({
        error: 'Discovery only available on desktop instances',
        available: false
      }, { status: 404 });
    }

    // Get device information
    const deviceId = process.env.DEVICE_ID || uuidv4();
    const hostname = require('os').hostname();
    
    // Get CouchDB port - try multiple methods
    let couchdbPort = 5984; // Default fallback
    
    // Try to get from environment or process
    if (process.env.COUCHDB_PORT) {
      couchdbPort = parseInt(process.env.COUCHDB_PORT);
    }
    
    // Try to get from global if set by Electron
    if (typeof global !== 'undefined' && (global as any).COUCHDB_PORT) {
      couchdbPort = (global as any).COUCHDB_PORT;
    }

    // Get network interface information
    const networkInterfaces = require('os').networkInterfaces();
    const ipAddresses: string[] = [];
    
    Object.values(networkInterfaces).forEach((interfaces: any) => {
      interfaces?.forEach((iface: any) => {
        if (iface.family === 'IPv4' && !iface.internal) {
          ipAddresses.push(iface.address);
        }
      });
    });

    // Prepare discovery response
    const discoveryInfo = {
      // Device identification
      deviceId,
      hostname,
      platform: 'desktop',
      
      // Network information
      ipAddresses,
      primaryIp: ipAddresses[0] || 'unknown',
      
      // Service information
      services: {
        http: {
          port: 3000,
          endpoint: '/api/discovery/announce',
          available: true
        },
        couchdb: {
          port: couchdbPort,
          endpoint: `http://${ipAddresses[0] || 'localhost'}:${couchdbPort}`,
          available: true,
          auth: {
            username: 'admin',
            password: 'admin'
          }
        }
      },
      
      // Discovery metadata
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      serviceType: 'bistro-pos',
      
      // Additional service info
      capabilities: [
        'couchdb-sync',
        'order-management',
        'inventory-sync',
        'staff-management'
      ],
      
      // Environment info
      environment: {
        buildTarget: environmentInfo.buildTarget,
        isElectron: environmentInfo.isElectron,
        isMobile: environmentInfo.isMobile,
        isDesktop: environmentInfo.isDesktop
      }
    };

    // Set CORS headers for cross-origin access
    const response = NextResponse.json(discoveryInfo);
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    
    return response;
    
  } catch (error) {
    console.error('[Discovery API] Error generating discovery response:', error);
    
    return NextResponse.json({
      error: 'Failed to generate discovery information',
      available: false,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  const response = new NextResponse(null, { status: 200 });
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  return response;
}