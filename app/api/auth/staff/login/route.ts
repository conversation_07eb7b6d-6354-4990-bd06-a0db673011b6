import { NextRequest, NextResponse } from 'next/server';
import { getMongoUserByUsername } from '@/lib/auth/mongo-auth-ops';
import { verifyPassword } from '@/lib/auth/new-auth-service';
import { generateToken } from '@/lib/auth/new-auth-service';
import { User as JwtUser } from '@/lib/auth/new-auth-service';
import { getStaffMember } from '@/lib/db/v4/operations/per-staff-ops';
import { initializeV4Database } from '@/lib/db/v4';
import { getAllStaff } from '@/lib/db/v4/operations/per-staff-ops';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


export async function POST(req: NextRequest) {
  // Parse request body - this should not fail for valid requests
  let body: any;
  try {
    body = await req.json();
    console.log('[API Staff Login] Received POST request with body:', body);
  } catch (parseError) {
    console.error('[API Staff Login] Failed to parse request body:', parseError);
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  const { username, password } = body;

  if (!username || !password) {
    console.warn('[API Staff Login] Missing username or password:', { username, password });
    return NextResponse.json(
      { error: 'Username and password are required' },
      { status: 400 }
    );
  }

  // STEP 1: MongoDB Authentication (CRITICAL - MUST SUCCEED FOR LOGIN)
  let staffUser: any = null;

  try {
    console.log('[API Staff Login] Fetching user from MongoDB by username:', username);
    staffUser = await getMongoUserByUsername(username);

    if (!staffUser) {
      console.log('[API Staff Login] No user found with username:', username);
      return NextResponse.json({ error: 'Invalid credentials - user not found' }, { status: 401 });
    }

    console.log('[API Staff Login] MongoDB user found:', {
      _id: staffUser._id,
      name: staffUser.name,
      username: staffUser.username,
      role: staffUser.role,
      restaurantId: staffUser.restaurantId
    });

    // Verify password
    console.log('[API Staff Login] Verifying password for user:', username);
    const isValidPassword = await verifyPassword(password, staffUser.password);
    console.log('[API Staff Login] Password valid:', isValidPassword);

    if (!isValidPassword) {
      console.warn('[API Staff Login] Invalid credentials - password incorrect for username:', username);
      return NextResponse.json({ error: 'Invalid credentials - password incorrect' }, { status: 401 });
    }

    console.log('[API Staff Login] ✅ MongoDB authentication successful for:', username);

  } catch (authError) {
    console.error('[API Staff Login] ❌ MongoDB authentication failed:', authError);
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }

  // STEP 2: PouchDB Operations (OPTIONAL - NEVER FAIL LOGIN)
  let actualPermissions = null;

  if (staffUser.restaurantId) {
    try {
      console.log('[API Staff Login] 🔄 Attempting to fetch permissions from PouchDB (non-blocking)');

      // Initialize V4 database for the restaurant
      await initializeV4Database(staffUser.restaurantId);

      // Get ALL staff members and find the one with matching userId
      const allStaff = await getAllStaff();
      const staffMember = allStaff.find(staff => staff.userId === staffUser._id);

      if (staffMember?.permissions) {
        actualPermissions = staffMember.permissions;
        console.log('[API Staff Login] ✅ Permissions loaded successfully from PouchDB');
      } else {
        console.log('[API Staff Login] ⚠️ Staff member found but no permissions in PouchDB');
      }
    } catch (pouchError) {
      console.warn('[API Staff Login] ⚠️ PouchDB operations failed (non-blocking):', pouchError instanceof Error ? pouchError.message : pouchError);
      console.warn('[API Staff Login] 📱 This is expected on mobile devices - continuing with login');
      // Continue with login even if permissions fail - permissions can be loaded later
    }
  } else {
    console.log('[API Staff Login] ⚠️ No restaurantId found, skipping PouchDB operations');
  }

  // STEP 3: JWT Generation and Success Response (MUST SUCCEED)
  try {
    const userForToken: JwtUser = {
      id: staffUser._id,
      name: staffUser.name,
      email: staffUser.email,
      username: staffUser.username,
      role: staffUser.role,
      restaurantId: staffUser.restaurantId,
      permissions: actualPermissions, // Use actual permissions from PouchDB (may be null)
      metadata: staffUser.metadata,
    };

    console.log('[API Staff Login] 🔑 Generating JWT for user:', {
      id: userForToken.id,
      username: userForToken.username,
      role: userForToken.role,
      restaurantId: userForToken.restaurantId,
      permissions: actualPermissions ? 'loaded from PouchDB' : 'none (will load later)'
    });

    const token = generateToken(userForToken);
    console.log('[API Staff Login] ✅ JWT generated successfully for user:', username);

    return NextResponse.json({
      message: 'Staff login successful',
      token,
      user: {
        id: staffUser._id,
        name: staffUser.name,
        username: staffUser.username,
        role: staffUser.role,
        restaurantId: staffUser.restaurantId
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (tokenError) {
    console.error('[API Staff Login] ❌ JWT generation failed:', tokenError);
    return NextResponse.json({ error: 'Token generation failed' }, { status: 500 });
  }

}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}