import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
import { getMongoUserById } from '@/lib/auth/mongo-auth-ops';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


// Fixed route handler for NextJS App Router
export async function GET(
  req: NextRequest,
  context: any
) {
  try {
    // Get userId from params and ensure it's a string
    // Using optional chaining and nullish coalescing to handle undefined params
    const userId = context.params?.userId ?? '';

    // Validate userId
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Add cache control headers to prevent excessive requests
    const headers = new Headers();
    headers.append('Cache-Control', 'public, max-age=300'); // Cache for 5 minutes
    headers.append('ETag', `"${userId}-${Date.now()}"`); // Add ETag for caching

    console.log("API: /api/auth/user/[userId] - GET request received", { userId });

    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(req);

    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", {
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the authenticated user
    console.log("API: User authenticated", {
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });

    const { user } = authResult;

    // Only allow owners, admins, or the user themselves to access user documents
    if (user.role !== 'owner' && user.role !== 'admin' && user.id !== userId) {
      return NextResponse.json(
        { error: "Insufficient permissions to access this resource" },
        { status: 403 }
      );
    }

    // Get user from auth database
    try {
      const userDoc = await getMongoUserById(userId);
      if (!userDoc) {
        return NextResponse.json(
          { error: "User not found" },
          { status: 404 }
        );
      }
      // Remove sensitive fields
      const safeUserDoc = {
        ...userDoc,
        password: undefined, // Remove password hash
        salt: undefined, // Remove password salt if exists
        fetchedAt: new Date().toISOString(), // Add timestamp for debugging
      };
      return NextResponse.json(safeUserDoc, { headers });
    } catch (error) {
      console.error("API: Error fetching user document:", error);
      return NextResponse.json(
        { error: error instanceof Error ? error.message : "Error fetching user document" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("API: Unhandled error:", error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}