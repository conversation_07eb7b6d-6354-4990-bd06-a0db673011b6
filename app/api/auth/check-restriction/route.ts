import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  getMongoUserById,
  // getMongoOwnerForRestaurant,
} from '@/lib/auth/mongo-auth-ops'; // Adjusted path assuming @ is src or root
import { AuthUser } from '@/lib/db/v4/schemas/auth-schema';
import { checkRestrictionFromToken } from '@/lib/auth/restriction-middleware';
import { checkUserRestriction } from '@/lib/auth/owner-lookup';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';

interface RequestBody {
  userId: string;
  role: string;
  restaurantId?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as RequestBody;
    const { userId, role, restaurantId } = body;

    if (!userId || !role) {
      return NextResponse.json({ error: 'Missing userId or role' }, { status: 400 });
    }

    console.log(`🔒 [API CheckRestriction] Checking restriction for user ${userId}, role: ${role}, restaurant: ${restaurantId}`);

    // Get user from database
    const currentUser = await getMongoUserById(userId);
    if (!currentUser) {
      console.log(`❌ [API CheckRestriction] User ${userId} not found`);
      return NextResponse.json({ error: 'User not found', isRestricted: true }, { status: 404 });
    }

    // Use the new hierarchical restriction check
    const restrictionResult = await checkUserRestriction(currentUser);
    
    console.log(`🔒 [API CheckRestriction] Restriction result for ${userId}:`, {
      isRestricted: restrictionResult.isRestricted,
      reason: restrictionResult.reason,
      ownerName: restrictionResult.ownerName
    });

    return NextResponse.json({
      isRestricted: restrictionResult.isRestricted,
      reason: restrictionResult.reason,
      ownerName: restrictionResult.ownerName,
      message: restrictionResult.isRestricted 
        ? (restrictionResult.reason === 'owner' 
          ? `Access restricted: Restaurant owner ${restrictionResult.ownerName || 'account'} is restricted`
          : 'Access restricted: Your account is restricted')
        : 'Access allowed'
    });

  } catch (error) {
    console.error('❌ [API CheckRestriction] Error:', error);
    // Default-deny for enforcement safety
    return NextResponse.json({ 
      error: 'Error checking restrictions', 
      isRestricted: true,
      reason: 'error'
    }, { status: 500 });
  }
}