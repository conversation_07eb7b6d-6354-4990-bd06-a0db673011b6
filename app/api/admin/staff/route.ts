import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { verifyJwtAuth, authorize } from '@/lib/api-middleware';

export async function GET(request: NextRequest) {
  try {
    // Check admin authorization using the shared authorize helper
    const authResult = await verifyJwtAuth(request);
    const authorization = authorize({
      auth: authResult,
      requireAdminSession: true
    });
    
    if ('authorized' in authorization && authorization.authorized === false) {
      return NextResponse.json(
        { success: false, error: authorization.error },
        { status: authorization.status }
      );
    }

    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    
    // Get restaurant count from users collection (each user has a restaurantId)
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      return NextResponse.json({
        success: true,
        restaurants: 0,
        owners: 0,
        staff: 0
      });
    }

    const usersCollection = db.collection('users');
    
    // Get all users and group by restaurant
    const allUsers = await usersCollection.find({}).toArray();
    
    // Count unique restaurants and user types
    const restaurantIds = new Set();
    let ownerCount = 0;
    let staffCount = 0;
    
    allUsers.forEach((user: any) => {
      if (user.restaurantId) {
        restaurantIds.add(user.restaurantId);
      }
      if (user.role === 'owner') {
        ownerCount++;
      } else if (user.role === 'staff' || user.role === 'manager') {
        staffCount++;
      }
    });

    return NextResponse.json({
      success: true,
      restaurants: restaurantIds.size,
      owners: ownerCount,
      staff: staffCount
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[Admin Staff] Error fetching summary:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch summary' },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}