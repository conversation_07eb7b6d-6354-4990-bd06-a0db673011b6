import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
    try {
        // Get admin email from environment
        const adminEmail = process.env.ADMIN_EMAIL;

        if (!adminEmail) {
            return NextResponse.json(
                { success: false, error: 'Admin email not configured' },
                { status: 500 }
            );
        }

        // In a production system, you'd verify the current user's email against the admin email
        // For now, we'll check if the request has admin authorization
        const adminEmailHeader = request.headers.get('x-admin-email');

        // Simple admin check - in production, implement proper admin session management
        if (adminEmailHeader === adminEmail) {
            return NextResponse.json({
                success: true,
                email: adminEmail,
                isAdmin: true
            });
        }

        // For development, allow access if ADMIN_EMAIL is set
        if (process.env.NODE_ENV === 'development') {
            return NextResponse.json({
                success: true,
                email: adminEmail,
                isAdmin: true
            }, {
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email',
                }
            });
        }

        return NextResponse.json(
            { success: false, error: 'Unauthorized admin access' },
            { status: 403 }
        );

    } catch (error) {
        console.error('[Admin Check] Error:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email',
      'Access-Control-Max-Age': '86400',
    },
  });
}