import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { verifyJwtAuth, authorize } from '@/lib/api-middleware';

interface AdminAction {
  _id?: string;
  type: string;
  actor: string;
  actorEmail?: string;
  target?: string;
  description?: string;
  timestamp: string;
  metadata?: any;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authorization using the shared authorize helper
    const authResult = await verifyJwtAuth(request);
    const authorization = authorize({ 
      auth: authResult, 
      requireAdminSession: true 
    });
    
    if ('authorized' in authorization && authorization.authorized === false) {
      return NextResponse.json(
        { success: false, error: authorization.error },
        { status: authorization.status }
      );
    }

    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    
    // Ensure the admin-actions collection exists
    const collections = await db.listCollections({ name: 'admin-actions' }).toArray();
    if (collections.length === 0) {
      return NextResponse.json({
        success: true,
        actions: []
      });
    }

    const actionsCollection = db.collection<AdminAction>('admin-actions');
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Fetch recent actions
    const actions = await actionsCollection
      .find({})
      .sort({ timestamp: -1 })
      .skip(offset)
      .limit(limit)
      .toArray();

    return NextResponse.json({
      success: true,
      actions: actions.map(action => ({
        id: action._id,
        type: action.type,
        actor: action.actor,
        actorEmail: action.actorEmail,
        target: action.target,
        description: action.description,
        at: action.timestamp
      }))
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[Admin Actions] Error fetching actions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch admin actions' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authorization using the shared authorize helper
    const authResult = await verifyJwtAuth(request);
    const authorization = authorize({ 
      auth: authResult, 
      requireAdminSession: true 
    });
    
    if ('authorized' in authorization && authorization.authorized === false) {
      return NextResponse.json(
        { success: false, error: authorization.error },
        { status: authorization.status }
      );
    }

    const body = await request.json();
    const { type, target, description, metadata } = body;

    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const actionsCollection = db.collection<AdminAction>('admin-actions');

    // Create new action log
    const newAction: AdminAction = {
      type,
      actor: authResult.user?.name || 'Unknown',
      actorEmail: authResult.user?.email,
      target,
      description,
      timestamp: new Date().toISOString(),
      metadata
    };

    const result = await actionsCollection.insertOne(newAction as any);

    return NextResponse.json({
      success: true,
      action: {
        id: result.insertedId,
        ...newAction,
        at: newAction.timestamp
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[Admin Actions] Error creating action log:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to log admin action' },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}