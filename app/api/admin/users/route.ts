import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { AuthUser } from '@/lib/db/v4/schemas/auth-schema';

// Admin authentication check
function isAdminAuthorized(request: NextRequest): boolean {
  const adminEmail = process.env.ADMIN_EMAIL;
  const adminEmailHeader = request.headers.get('x-admin-email');
  
  if (!adminEmail) {
    return false;
  }

  // Check admin authorization
  if (adminEmailHeader === adminEmail) {
    return true;
  }

  // Allow in development mode
  if (process.env.NODE_ENV === 'development') {
    return true;
  }

  return false;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authorization
    if (!isAdminAuthorized(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized admin access' },
        { 
          status: 403,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email',
          }
        }
      );
    }

    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    
    // Ensure the users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      return NextResponse.json({
        success: true,
        users: []
      });
    }

    const usersCollection = db.collection<AuthUser>('users');
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const search = searchParams.get('search');
    
    // Build query
    let query: any = {};
    
    if (role) {
      query.role = role;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } }
      ];
    }

    // Fetch users (exclude password field)
    const users = await usersCollection
      .find(query, { 
        projection: { 
          password: 0 // Exclude password from results
        } 
      })
      .sort({ createdAt: -1 })
      .toArray();

    return NextResponse.json({
      success: true,
      users: users,
      total: users.length
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email',
      }
    });

  } catch (error) {
    console.error('[Admin Users] Error fetching users:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email',
      'Access-Control-Max-Age': '86400',
    },
  });
}