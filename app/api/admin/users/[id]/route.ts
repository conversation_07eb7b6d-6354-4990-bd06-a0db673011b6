import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { AuthUser } from '@/lib/db/v4/schemas/auth-schema';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Admin authentication check
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminEmailHeader = request.headers.get('x-admin-email');
    
    if (!adminEmail || (adminEmailHeader !== adminEmail && process.env.NODE_ENV !== 'development')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized admin access' },
        { status: 403 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { restricted } = body;

    if (typeof restricted !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'Invalid restriction value' },
        { status: 400 }
      );
    }

    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');

    const usersCollection = db.collection<AuthUser>('users');

    // First, get the user to check if they exist and get their role
    const user = await usersCollection.findOne({ _id: id });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Update the user's restriction status
    const updateResult = await usersCollection.updateOne(
      { _id: id },
      {
        $set: {
          restricted: restricted,
          updatedAt: new Date().toISOString(),
        },
      }
    );

    if (updateResult.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Clear owner restriction cache when owner status changes
    if (user.role === 'owner' && user.restaurantId) {
      // Import and clear cache - we'll do this inline to avoid import issues
      try {
        const { clearOwnerRestrictionCache } = await import('@/lib/auth/owner-lookup');
        clearOwnerRestrictionCache(user.restaurantId);
        console.log(`🔒 [Admin API] Cleared owner restriction cache for restaurant ${user.restaurantId}`);
      } catch (error) {
        console.warn('❌ [Admin API] Failed to clear owner restriction cache:', error);
      }
    }

    // Get updated user data
    const updatedUser = await usersCollection.findOne(
      { _id: id },
      { projection: { password: 0 } }
    );

    return NextResponse.json({
      success: true,
      message: `User ${restricted ? 'restricted' : 'unrestricted'} successfully`,
      user: updatedUser,
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email'
      }
    });
  } catch (error) {
    console.error('[Admin Users] Error updating user restriction:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user restriction' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Admin authentication check
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminEmailHeader = request.headers.get('x-admin-email');
    
    if (!adminEmail || (adminEmailHeader !== adminEmail && process.env.NODE_ENV !== 'development')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized admin access' },
        { status: 403 }
      );
    }

    const { id } = params;
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');

    const usersCollection = db.collection<AuthUser>('users');

    // Get user by ID (exclude password)
    const user = await usersCollection.findOne(
      { _id: id },
      { projection: { password: 0 } }
    );

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      user: user,
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email'
      }
    });
  } catch (error) {
    console.error('[Admin Users] Error fetching user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Admin authentication check
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminEmailHeader = request.headers.get('x-admin-email');
    
    if (!adminEmail || (adminEmailHeader !== adminEmail && process.env.NODE_ENV !== 'development')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized admin access' },
        { status: 403 }
      );
    }

    const { id } = params;
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');

    const usersCollection = db.collection<AuthUser>('users');

    // First, get the user to check if they exist and get their role
    const user = await usersCollection.findOne({ _id: id });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Delete the user
    const deleteResult = await usersCollection.deleteOne({ _id: id });

    if (deleteResult.deletedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found or already deleted' },
        { status: 404 }
      );
    }

    // If this was an owner, also delete all staff in their restaurant
    if (user.role === 'owner' && user.restaurantId) {
      await usersCollection.deleteMany({
        restaurantId: user.restaurantId,
        role: { $in: ['staff', 'manager', 'admin'] },
      });
    }

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully',
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email'
      }
    });
  } catch (error) {
    console.error('[Admin Users] Error deleting user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-admin-email',
      'Access-Control-Max-Age': '86400',
    },
  });
}