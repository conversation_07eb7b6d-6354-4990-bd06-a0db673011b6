import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await req.json();
    const { deviceId, deviceType, couchdbPort, ipAddress } = body;

    if (!deviceId || !deviceType || !ipAddress) {
      return NextResponse.json({ 
        error: 'deviceId, deviceType, and ipAddress are required' 
      }, { status: 400 });
    }

    if (deviceType === 'desktop' && !couchdbPort) {
      return NextResponse.json({ 
        error: 'couchdbPort is required for desktop devices' 
      }, { status: 400 });
    }

    const database = databaseV4.getDatabase();
    const collection = database.collection('sync_devices');

    const deviceRecord = {
      deviceId,
      deviceType,
      restaurantId: decoded.restaurantId,
      ipAddress,
      couchdbPort: deviceType === 'desktop' ? couchdbPort : null,
      registeredAt: new Date(),
      lastSeen: new Date(),
      status: 'active'
    };

    await collection.replaceOne(
      { deviceId, restaurantId: decoded.restaurantId },
      deviceRecord,
      { upsert: true }
    );

    return NextResponse.json({
      message: 'Device registered successfully',
      deviceId,
      restaurantId: decoded.restaurantId
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Sync Register Device] Error:', error);
    return NextResponse.json({ 
      error: 'Device registration failed' 
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}