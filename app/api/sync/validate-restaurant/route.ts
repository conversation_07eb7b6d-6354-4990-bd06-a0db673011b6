import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import { validateRestaurantContext, validateDatabaseName } from '@/lib/sync/restaurant-validation';

// Static export configuration
export const dynamic = 'force-static';
export const revalidate = false;


export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const body = await req.json();
    const { dbName, expectedRestaurantId } = body;

    // Validate restaurant context
    const restaurantValidation = validateRestaurantContext(token);
    
    const validation = {
      tokenValid: true,
      restaurantContextValid: restaurantValidation.isValid,
      restaurantId: restaurantValidation.restaurantId,
      source: restaurantValidation.source,
      dbNameValid: dbName ? validateDatabaseName(dbName, restaurantValidation.restaurantId || '') : null,
      expectedMatch: expectedRestaurantId ? restaurantValidation.restaurantId === expectedRestaurantId : null,
      error: restaurantValidation.error
    };

    // Additional JWT validation
    try {
      const decoded = verifyToken(token);
      if (!decoded || !decoded.restaurantId) {
        validation.tokenValid = false;
      } else if (decoded.restaurantId !== restaurantValidation.restaurantId) {
        validation.error = 'JWT restaurant ID mismatch with context';
        validation.restaurantContextValid = false;
      }
    } catch (error) {
      validation.tokenValid = false;
      validation.error = 'JWT verification failed';
    }

    const isSecure = validation.tokenValid && 
                    validation.restaurantContextValid && 
                    (validation.dbNameValid !== false) &&
                    (validation.expectedMatch !== false);

    return NextResponse.json({
      secure: isSecure,
      validation,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Sync Validate Restaurant] Error:', error);
    return NextResponse.json({ 
      secure: false,
      error: 'Validation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}