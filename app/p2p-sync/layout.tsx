import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import { Database, UsersRound, Wifi } from 'lucide-react';

export const metadata: Metadata = {
  title: 'P2P Sync | Resto',
  description: 'Sync data between devices',
};

interface P2PSyncLayoutProps {
  children: React.ReactNode;
}

export default function P2PSyncLayout({ children }: P2PSyncLayoutProps) {
  return (
    <div className="flex flex-col min-h-screen">
      <div className="border-b bg-card">
        <div className="container flex h-14 items-center px-4">
          <div className="mr-4 flex">
            <Link
              href="/p2p-sync"
              className="mr-6 flex items-center space-x-2 font-bold"
            >
              <Database className="h-5 w-5" />
              <span>P2P Sync</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link
                href="/p2p-sync"
                className="transition-colors hover:text-primary"
              >
                Overview
              </Link>
              <Link
                href="/p2p-sync/peers"
                className="transition-colors hover:text-primary"
              >
                <div className="flex items-center gap-1">
                  <UsersRound className="h-4 w-4" />
                  <span>Peers</span>
                </div>
              </Link>
              <Link
                href="/mdns-browser"
                className="transition-colors hover:text-primary"
              >
                <div className="flex items-center gap-1">
                  <Wifi className="h-4 w-4" />
                  <span>mDNS Browser</span>
                </div>
              </Link>
            </nav>
          </div>
        </div>
      </div>
      <div className="flex-1">
        {children}
      </div>
    </div>
  );
} 