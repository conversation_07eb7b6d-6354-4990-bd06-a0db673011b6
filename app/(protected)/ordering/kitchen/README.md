# Kitchen Display System

The Kitchen Display System (KDS) is designed to efficiently manage and process orders in a restaurant kitchen environment.

## Features

- **Real-time Order Management**: Display pending, preparing, and ready-to-serve orders
- **Order Status Updates**: Kitchen staff can easily update order status as they progress
- **Automatic Refresh**: Orders list refreshes automatically every 30 seconds
- **Filtering & Searching**: Filter orders by status and search by order details
- **Sorting Options**: Sort orders by newest, oldest, or priority
- **Visual Status Indicators**: Color-coded order cards to quickly identify status
- **Notifications**: Audio and visual notifications for new orders

## Order Workflow

1. **Pending Orders**: New orders appear in the Pending column when created by waitstaff
2. **Preparing**: Kitchen staff click "Start Preparing" to move orders to the Preparing column
3. **Ready to Serve**: When food is ready, staff click "Ready to Serve" to move to the Ready column
4. **Delivered**: Once the order is delivered to customers, staff click "Mark Delivered"

## Implementation Details

- Built with React and Next.js
- Uses the restaurant database's order management functions
- Follows the application's component and styling conventions
- Works completely offline-first with real-time sync capabilities 