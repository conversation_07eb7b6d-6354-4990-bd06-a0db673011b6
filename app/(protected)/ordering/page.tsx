"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { usePermissions } from '@/lib/hooks/use-permissions';
import NewOrderingInterface from '../../components/NewOrderingInterface';
import OrderList from '../../components/OrderList';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { EditOrderProvider } from '@/components/providers/EditOrderContext';
import { Button } from '@/components/ui/button';
import { Package } from 'lucide-react';
import CollectionDialog from '@/components/ordering/CollectionDialog';

export default function OrderingPage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const { hasTabAccess } = usePermissions();
  const [tab, setTab] = useState('ordering');
  const [collectionDialogOpen, setCollectionDialogOpen] = useState(false);

  // Check if user has collection access
  const hasCollectionAccess = hasTabAccess('orders', 'collection');

  useEffect(() => {
    // Redirect to login if not authenticated and auth check is complete
    if (!loading && !isAuthenticated) {
      navigate('auth');
    }
  }, [isAuthenticated, loading, navigate]);

  // Show loading until auth is determined
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render the page when authenticated
  if (!isAuthenticated) return null;

  return (
    <EditOrderProvider>
      <div className="absolute inset-0 flex flex-col max-w-full overflow-hidden">
        <Tabs value={tab} onValueChange={setTab} className="flex-grow flex flex-col max-w-full overflow-hidden">
          <div className="flex-shrink-0 flex justify-between items-center px-3 pt-3 pb-1">
            <TabsList>
              <TabsTrigger value="ordering">Ordering Interface</TabsTrigger>
              <TabsTrigger value="orders">Orders List</TabsTrigger>
            </TabsList>
            
            {/* Collection Button - Only show if user has permission */}
            {hasCollectionAccess && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCollectionDialogOpen(true)}
                className="flex items-center gap-2"
              >
                <Package className="h-4 w-4" />
                Collectes
              </Button>
            )}
          </div>
          <TabsContent value="ordering" className="flex-grow overflow-hidden min-h-0">
            <NewOrderingInterface />
          </TabsContent>
          <TabsContent value="orders" className="flex-grow overflow-hidden min-h-0">
            <OrderList setTab={setTab} />
          </TabsContent>
        </Tabs>

        {/* Collection Dialog */}
        <CollectionDialog
          open={collectionDialogOpen}
          onOpenChange={setCollectionDialogOpen}
        />
      </div>
    </EditOrderProvider>
  );
}