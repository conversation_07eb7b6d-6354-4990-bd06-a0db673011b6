"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useSuppliersV4 } from '@/lib/hooks/useSuppliersV4';
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { StockItem, PurchaseLog, StockAdjustment } from "@/types/stock";
import { StockCount, StockCountItem, WasteLog } from "@/types/stockCount";
import { Supplier } from "@/types/suppliers";
import React from 'react';
import { useToast } from "@/components/ui/use-toast";
import { TabPermissionGuard } from "@/components/auth/TabPermissionGuard";
import { useExpenses } from '@/lib/services/finance-service';
import { databaseV4 } from "@/lib/db/v4/core/db-instance";
import { useIsMobile } from "@/hooks/use-mobile";
import { attachReceiptImage } from '@/lib/db/v4/operations/purchase-transaction-ops';

// UI Components
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Search,
  RefreshCw,
  DollarSign,
  ShoppingCart,
  Calendar,
  TrendingDown,
  TrendingUp,
  Receipt,
  Package,
  Coffee,
  Wine,
  ShoppingBag,
  ChefHat,
  ClipboardList,
  ClipboardCheck,
  Trash2,
  AlertTriangle,
  BarChart,
  ListPlus,
  History,
  Truck,
  ChevronRight,
  ListFilter,
  MoreHorizontal
} from "lucide-react";

import { StockItemForm } from "@/components/stock/StockItemForm";
import { MultiPurchaseLogForm } from "@/components/stock/MultiPurchaseLogForm";
import { PurchaseHistoryView } from "@/components/stock/PurchaseHistoryView";

import { SubRecipesList } from "@/components/production/SubRecipesList";
import { StockCountSheet } from "@/components/stock/StockCountSheet";
import { StockCountEntryForm } from "@/components/stock/StockCountEntryForm";
import { StockCountVarianceReport } from "@/components/stock/StockCountVarianceReport";
import { WasteTrackingForm } from "@/components/stock/WasteTrackingForm";
import { format, subDays } from "date-fns";
import { BatchProduction } from "@/components/production/BatchProduction";
import { MenuRecipesList } from "@/components/production/MenuRecipesList";
import { BatchedPurchaseHistory, PurchaseBatch } from "@/components/stock/BatchedPurchaseHistory";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

// Add categories constant
const categories = [
  "Ingrédients",
  "Boissons",
  "Emballages",
  "Fournitures de nettoyage",
  "Fournitures de bureau",
  "Autre"
] as const;

// Helper function to get an icon for a category
const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Ingrédients':
      return <Package className="h-4 w-4" />;
    case 'Boissons':
      return <Coffee className="h-4 w-4" />;
    case 'Emballages':
      return <ShoppingBag className="h-4 w-4" />;
    case 'Fournitures de nettoyage':
      return <Receipt className="h-4 w-4" />;
    case 'Fournitures de bureau':
      return <Receipt className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
};

export default function InventoryPage() {

  const { isAuthenticated, user, loading } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const {
    stockItems,
    purchaseTransactions,
    stockCounts,
    wasteLogs,
    isLoading: isLoadingStock,
    isReady: isStockReady,
    refreshStock,
    refreshPurchaseTransactions,
    refreshStockCounts,
    refreshWasteLogs,
    createStockItem,
    updateStockItem,
    deleteStockItem,
    createPurchaseTransaction,
    getPurchaseTransactionsForItem,
    createStockAdjustment,
    createStockCount,
    updateStockCount,
    deleteStockCount,
    getStockCount,
    createStockCountItem,
    getStockCountItems,
    updateStockCountItem,
    applyStockCount,
    createWasteLog
  } = useStockV4();

  // Get COGS settings to check if it's enabled
  const { isCogsEnabled } = useCOGSV4();

  const {
    suppliers,
    isLoading: isLoadingSuppliers,
    isReady: isSuppliersReady,
    refreshSuppliers,
    createSupplier,
    updateSupplier,
    deleteSupplier
  } = useSuppliersV4();

  const { addExpense } = useExpenses();

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedTab, setSelectedTab] = useState<string>("inventory");
  const [editingItem, setEditingItem] = useState<StockItem | null>(null);
  const [selectedItem, setSelectedItem] = useState<StockItem | null>(null);
  const [itemPurchases, setItemPurchases] = useState<any[]>([]);
  const [isItemDialogOpen, setIsItemDialogOpen] = useState(false);
  const [isNewItemDialogOpen, setIsNewItemDialogOpen] = useState(false);
  const [isLoadingPurchases, setIsLoadingPurchases] = useState(false);

  // Stock count states
  const [selectedCount, setSelectedCount] = useState<StockCount | null>(null);
  const [countItems, setCountItems] = useState<StockCountItem[]>([]);
  const [isLoadingCountItems, setIsLoadingCountItems] = useState(false);
  const [countView, setCountView] = useState<'sheet' | 'entry' | 'report'>('sheet');

  // Waste tracking states
  const [isWasteDialogOpen, setIsWasteDialogOpen] = useState(false);
  const [selectedWasteItem, setSelectedWasteItem] = useState<StockItem | null>(null);

  const [sevenDayTotal, setSevenDayTotal] = useState<number>(0);
  const [thirtyDayTotal, setThirtyDayTotal] = useState<number>(0);
  const [categoryTotals, setCategoryTotals] = useState<Record<string, number>>({});

  // Add state for the add stock subtabs
  const [selectedAddStockSubTab, setSelectedAddStockSubTab] = useState<string>("add");

  // Add state for mobile filter sheet
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);

  // Add state for editing purchase batches
  const [editingBatch, setEditingBatch] = useState<any>(null);

  // Calculate purchase metrics
  useEffect(() => {
    if (purchaseTransactions.length > 0) {
      const now = new Date();
      const sevenDaysAgo = subDays(now, 7);
      const thirtyDaysAgo = subDays(now, 30);

      // Seven day total
      const sevenDayPurchases = purchaseTransactions.filter(
        purchase => new Date(purchase.date) >= sevenDaysAgo
      );
      const sevenDaySum = sevenDayPurchases.reduce(
        (total, purchase) => total + purchase.totalCost, 0
      );
      setSevenDayTotal(sevenDaySum);

      // Thirty day total
      const thirtyDayPurchases = purchaseTransactions.filter(
        purchase => new Date(purchase.date) >= thirtyDaysAgo
      );
      const thirtyDaySum = thirtyDayPurchases.reduce(
        (total, purchase) => total + purchase.totalCost, 0
      );
      setThirtyDayTotal(thirtyDaySum);

      // Category totals for last 30 days
      const catTotals: Record<string, number> = {};
      for (const purchase of thirtyDayPurchases) {
        for (const item of purchase.items) {
          const stockItem = stockItems.find(i => i.id === item.stockItemId);
          if (stockItem) {
            const category = stockItem.category;
            catTotals[category] = (catTotals[category] || 0) + item.totalCost;
          }
        }
      }
      setCategoryTotals(catTotals);
    }
  }, [purchaseTransactions, stockItems]);

  // Filter stock items based on search term and category
  const filteredItems = stockItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        item.category.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === "all" ? true : item.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Group items by category for the tabbed view
  const itemsByCategory = categories.reduce((acc, category) => {
    acc[category] = filteredItems.filter(item => item.category === category);
    return acc;
  }, {} as Record<string, StockItem[]>);

  // Count items in each category (for badges)
  const categoryCounts = categories.reduce((acc, category) => {
    acc[category] = stockItems.filter(item => item.category === category).length;
    return acc;
  }, {} as Record<string, number>);

  // Load purchases for a specific item
  const loadItemPurchases = async (itemId: string) => {
    setIsLoadingPurchases(true);
    try {
      const transactions = await getPurchaseTransactionsForItem(itemId);
      setItemPurchases(transactions);
    } catch (error) {
      console.error('Error loading item purchases:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger l\'historique des achats',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingPurchases(false);
    }
  };

  // Handle stock item form submission
  const handleStockItemSubmit = async (data: any) => {
    try {
      if (editingItem) {
        await updateStockItem(editingItem.id, data);
        toast({
          title: 'Succès',
          description: 'Article mis à jour avec succès'
        });
      } else {
        await createStockItem(data);
        toast({
          title: 'Succès',
          description: 'Nouvel article ajouté avec succès'
        });
      }

      // Refresh data
      await refreshStock();
      setIsNewItemDialogOpen(false);
      setEditingItem(null);

      return true;
    } catch (error) {
      console.error('Error saving stock item:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible d\'enregistrer l\'article',
        variant: 'destructive'
      });
      return false;
    }
  };

  // Handle purchase log submission with optional receipt file
  const handlePurchaseSubmit = async (data: any, receiptFile?: File): Promise<void> => {
    try {
      // Create purchase transaction
      const transaction = await createPurchaseTransaction({
        date: data.date,
        items: data.items,
        amountPaid: data.amountPaid || 0,
        supplierId: data.supplierId === 'none' ? undefined : data.supplierId,
        notes: data.notes,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      });

      // Attach receipt image if provided
      if (receiptFile && transaction._id) {
        try {
          await attachReceiptImage(transaction._id, receiptFile, user?.restaurantId || '');
          console.log('Receipt image attached successfully');
        } catch (attachmentError) {
          console.error('Failed to attach receipt image:', attachmentError);
          // Show warning but don't fail the whole transaction
          toast({
            title: '⚠️ Warning',
            description: 'Purchase saved but receipt attachment failed',
            variant: 'destructive'
          });
        }
      }

      // Register expense if needed
      if (typeof addExpense === 'function') {
        try {
          const totalAmount = data.items.reduce((sum: number, item: any) => sum + item.totalCost, 0);
          await addExpense(
            totalAmount,
            `Purchase Transaction (${data.items.length} items)`,
            'inventory'
          );
        } catch (expenseError) {
          console.error('Failed to register expense:', expenseError);
          // Continue with the rest of the function - the purchase is already saved
        }
      }

      toast({
        title: '✅ Success',
        description: receiptFile ? 'Purchase and receipt saved successfully' : 'Purchase saved successfully'
      });

      // Refresh data
      await refreshPurchaseTransactions();
      await refreshStock();
      await refreshSuppliers(); // Refresh suppliers to update balances if needed

      // Close dialog
      setIsNewItemDialogOpen(false);
    } catch (error) {
      console.error('Error saving purchase log:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible d\'enregistrer l\'achat',
        variant: 'destructive'
      });
    }
  };

  // Open item details dialog
  const openItemDetailsDialog = async (item: StockItem) => {
    setSelectedItem(item);
    await loadItemPurchases(item.id);
    setIsItemDialogOpen(true);
  };

  // Open edit item dialog
  const openEditDialog = (item: StockItem) => {
    setEditingItem(item);
    setIsNewItemDialogOpen(true);
  };

  // Stock count handlers
  const handleCreateStockCount = async () => {
    try {
      const now = new Date();
      const countName = `Comptage ${format(now, 'dd/MM/yyyy HH:mm')}`;
      
      const newCount = await createStockCount({
        name: countName,
        date: now.toISOString(),
        countType: 'partial', // Start with partial for flexibility
        countArea: 'all',
        status: 'in_progress', // Start directly in progress
        notes: ''
      });

      // Auto-initialize with all stock items
      for (const item of stockItems) {
        await createStockCountItem({
          stockCountId: newCount.id,
          stockItemId: item.id,
          theoreticalQuantity: typeof item.quantity === 'number' ? item.quantity : 0
        });
      }

      // Set as selected and switch to entry view
      setSelectedCount(newCount);
      const items = await getStockCountItems(newCount.id);
      setCountItems(items);
      setCountView('entry');

      toast({
        title: '✅ Comptage créé',
        description: 'Commencez à compter vos articles'
      });

      await refreshStockCounts();
    } catch (error) {
      console.error('Error creating stock count:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de créer le comptage de stock',
        variant: 'destructive'
      });
    }
  };

  const handleSelectCount = async (count: StockCount) => {
    setSelectedCount(count);
    setIsLoadingCountItems(true);
    try {
      const items = await getStockCountItems(count.id);
      setCountItems(items);

      // Set the appropriate view based on count status
      if (count.status === 'completed') {
        setCountView('report');
      } else if (items.length > 0) {
        setCountView('entry');
      } else {
        setCountView('sheet');
      }
    } catch (error) {
      console.error('Error loading count items:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les articles du comptage',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingCountItems(false);
    }
  };

  const handleInitializeCount = async () => {
    if (!selectedCount) return;

    setIsLoadingCountItems(true);
    try {
      // Create count items for all stock items
      for (const item of stockItems) {
        await createStockCountItem({
          stockCountId: selectedCount.id,
          stockItemId: item.id,
          theoreticalQuantity: typeof item.quantity === 'number' ? item.quantity : 0
        });
      }

      // Update count status to in_progress
      await updateStockCount(selectedCount.id, { status: 'in_progress' });
      await refreshStockCounts();

      // Refresh count items
      const items = await getStockCountItems(selectedCount.id);
      setCountItems(items);

      // Switch to entry view
      setCountView('entry');

      toast({
        title: 'Succès',
        description: 'Feuille de comptage initialisée avec succès'
      });
    } catch (error) {
      console.error('Error initializing count:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible d\'initialiser le comptage',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingCountItems(false);
    }
  };

  const handleSaveCountItems = async (items: { stockItemId: string; countedQuantity: number; notes?: string }[]) => {
    if (!selectedCount) return;

    try {
      // Save each item in the array
      for (const item of items) {
        // Find the count item
        const countItem = countItems.find(ci => ci.stockItemId === item.stockItemId);
        if (!countItem) continue;

        // Update the count item
        await updateStockCountItem(countItem.id, {
          countedQuantity: item.countedQuantity,
          notes: item.notes
        });
      }

      // Refresh count items
      const updatedItems = await getStockCountItems(selectedCount.id);
      setCountItems(updatedItems);
    } catch (error) {
      console.error('Error saving count items:', error);
      throw error;
    }
  };

  const handleCompleteCount = async () => {
    if (!selectedCount) return;

    try {
      await applyStockCount(selectedCount.id);

      // Refresh data
      await refreshStockCounts();
      await refreshStock();

      // Get updated count and items
      const updatedCount = await getStockCount(selectedCount.id);
      setSelectedCount(updatedCount);

      const items = await getStockCountItems(selectedCount.id);
      setCountItems(items);

      // Switch to report view
      setCountView('report');

      toast({
        title: 'Succès',
        description: 'Comptage de stock terminé et inventaire mis à jour'
      });
    } catch (error) {
      console.error('Error completing count:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de terminer le comptage de stock',
        variant: 'destructive'
      });
    }
  };

  // Waste tracking handlers
  const handleCreateWasteLog = async (data: any) => {
    try {
      await createWasteLog({
        stockItemId: data.stockItemId,
        quantity: data.quantity,
        reason: data.reason,
        notes: data.notes,
        date: new Date().toISOString()
      });

      toast({
        title: 'Succès',
        description: 'Perte enregistrée avec succès'
      });

      setIsWasteDialogOpen(false);
      await refreshWasteLogs();
      await refreshStock(); // Refresh stock since quantities have changed
    } catch (error) {
      console.error('Error recording waste:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible d\'enregistrer la perte',
        variant: 'destructive'
      });
    }
  };

  const openWasteDialog = (item?: StockItem) => {
    if (item) {
      setSelectedWasteItem(item);
    } else {
      setSelectedWasteItem(null);
    }
    setIsWasteDialogOpen(true);
  };

  // Handle editing a purchase batch
  const handleEditPurchase = (batch: PurchaseBatch) => {
    // Transform the batch into the format expected by MultiPurchaseLogForm
    const editingData = {
      date: batch.date + 'T00:00:00.000Z', // Convert date string to ISO
      supplierId: batch.supplier?.id,
      notes: batch.items[0]?.purchase?.notes || '', // Use notes from first item
      items: batch.items.map(({ purchase }) => ({
        stockItemId: purchase.items[0]?.stockItemId || '',
        quantity: purchase.items[0]?.quantity || 0,
        purchaseUnitId: purchase.items[0]?.purchaseUnitId,
        costPerUnit: purchase.items[0]?.costPerUnit || 0,
        totalCost: purchase.items[0]?.totalCost || 0
      })),
      amountPaid: batch.items[0]?.purchase?.amountPaid || 0
    };

    setEditingBatch(editingData);
    setSelectedTab('addstock'); // Switch to Ajouter Stock tab
    setSelectedAddStockSubTab('add'); // Make sure we're on the add subtab
  };

  // Clear editing state when tab changes away from addstock
  useEffect(() => {
    if (selectedTab !== 'addstock' && editingBatch) {
      setEditingBatch(null);
    }
  }, [selectedTab, editingBatch]);

  // Helper to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // 1. Add a summary card for total inventory value at the top of the page
  const totalInventoryValue = stockItems.reduce((sum, item) => {
    if (typeof item.quantity === 'number' && typeof item.costPerUnit === 'number') {
      return sum + (item.quantity * item.costPerUnit);
    }
    return sum;
  }, 0);

  // Helper component for mobile card view
  const MobileInventoryCard = ({ item }: { item: StockItem }) => {
    // Calculate 30-day expense for this item
    const itemExpense = purchaseTransactions
      .filter(p => p.items.some(i => i.stockItemId === item.id))
      .filter(p => new Date(p.date) >= subDays(new Date(), 30))
      .reduce((sum, p) => {
        const relevantItems = p.items.filter(i => i.stockItemId === item.id);
        return sum + relevantItems.reduce((itemSum, i) => itemSum + i.totalCost, 0);
      }, 0);
      
    const totalValue = item.quantity !== undefined && item.costPerUnit !== undefined 
      ? item.quantity * item.costPerUnit 
      : 0;

    const isLowStock = item.quantity !== undefined && item.quantity < 10;
      
    return (
      <div 
        className="flex items-center py-1.5 px-2 hover:bg-muted/30 active:bg-muted/50 border-b border-muted/20 last:border-b-0 cursor-pointer transition-colors text-xs min-h-[44px]" 
        onClick={() => openItemDetailsDialog(item)}
      >
        {/* Icon + Name (45% width) */}
        <div className="flex items-center gap-1.5 flex-1 min-w-0 pr-2">
          <div className="p-0.5 rounded bg-primary/10 shrink-0">
            <div className="w-4 h-4 flex items-center justify-center">
              {getCategoryIcon(item.category)}
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate text-sm leading-tight">{item.name}</div>
            <div className="text-muted-foreground text-xs truncate leading-tight">{item.category}</div>
          </div>
          {isLowStock && (
            <div className="w-1.5 h-1.5 bg-red-500 rounded-full shrink-0"></div>
          )}
        </div>
        
        {/* Quantity (15% width) */}
        <div className="w-14 text-center shrink-0 px-1">
          <div className="font-medium text-xs leading-tight">{item.quantity ?? '-'}</div>
          <div className="text-muted-foreground text-xs leading-tight">{item.unit}</div>
        </div>
        
        {/* Cost (18% width) */}
        <div className="w-16 text-right shrink-0 px-1">
          <div className="font-medium text-xs leading-tight">{item.costPerUnit !== undefined ? formatCurrency(item.costPerUnit) : '-'}</div>
        </div>
        
        {/* Value (18% width) */}
        <div className="w-16 text-right shrink-0 px-1">
          <div className="font-medium text-primary text-xs leading-tight">{totalValue > 0 ? formatCurrency(totalValue) : '-'}</div>
        </div>
        
        {/* Actions (4% width) */}
        <div className="w-6 shrink-0">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-5 w-5 opacity-60 hover:opacity-100" 
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                openEditDialog(item);
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Adjust Stock
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                setSelectedTab("addstock");
                setSelectedAddStockSubTab("add");
              }}>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add Purchase
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    );
  };

  return (
    <div className={isMobile ? "min-h-screen bg-background" : "container mx-auto py-6 px-4"}>
      {/* Mobile Header */}
      {isMobile && (
        <div className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold">Inventory</h1>
              <p className="text-xs text-muted-foreground">Manage your stock & purchases</p>
          </div>
            <Button 
              size="sm" 
              onClick={() => setIsNewItemDialogOpen(true)}
              className="h-8 px-3"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className={isMobile ? "px-4 py-4 space-y-4" : "space-y-6"}>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          {/* Tab Navigation */}
          {isMobile ? (
            <div className="flex overflow-x-auto scrollbar-hide gap-1 pb-2">
              {[
                { value: "inventory", icon: Package, label: "Stock" },
                { value: "addstock", icon: ListPlus, label: "Add" },
                { value: "counts", icon: ClipboardList, label: "Counts" },
                { value: "waste", icon: Trash2, label: "Waste" },
                { value: "subrecipes", icon: ClipboardCheck, label: "Prod" },
                { value: "recettes", icon: ChefHat, label: "Recipe" },
              ].map((tab) => (
                <TabPermissionGuard key={tab.value} page="inventory" tab={tab.value === "addstock" ? "inventory" : tab.value === "recettes" ? "subrecipes" : tab.value}>
                  <Button
                    variant={selectedTab === tab.value ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSelectedTab(tab.value)}
                    className={`flex items-center gap-1.5 px-3 py-2 whitespace-nowrap ${
                      selectedTab === tab.value 
                        ? "bg-primary text-primary-foreground shadow-sm" 
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    <tab.icon className="h-4 w-4" />
                    <span className="text-xs font-medium">{tab.label}</span>
                  </Button>
                </TabPermissionGuard>
              ))}
            </div>
          ) : (
            <TabsList className="grid w-fit grid-cols-3 lg:grid-cols-6 gap-1 h-auto p-1 bg-muted/50">
            <TabPermissionGuard page="inventory" tab="inventory">
                <TabsTrigger value="inventory" className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <Package className="h-4 w-4" />
                <span>Inventaire</span>
              </TabsTrigger>
            </TabPermissionGuard>
            <TabPermissionGuard page="inventory" tab="inventory">
                <TabsTrigger value="addstock" className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <ListPlus className="h-4 w-4" />
                <span>Ajout de stock</span>
              </TabsTrigger>
            </TabPermissionGuard>
            <TabPermissionGuard page="inventory" tab="counts">
                <TabsTrigger value="counts" className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <ClipboardList className="h-4 w-4" />
                <span>Comptages</span>
              </TabsTrigger>
            </TabPermissionGuard>
            <TabPermissionGuard page="inventory" tab="waste">
                <TabsTrigger value="waste" className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <Trash2 className="h-4 w-4" />
                <span>Pertes</span>
              </TabsTrigger>
            </TabPermissionGuard>
            <TabPermissionGuard page="inventory" tab="subrecipes">
                <TabsTrigger value="production" className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <ClipboardCheck className="h-4 w-4" />
                <span>Production</span>
              </TabsTrigger>
            </TabPermissionGuard>
            <TabPermissionGuard page="inventory" tab="subrecipes">
                <TabsTrigger value="recettes" className="flex items-center gap-2 px-4 py-2.5 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                <ChefHat className="h-4 w-4" />
                <span>Recettes</span>
              </TabsTrigger>
            </TabPermissionGuard>
          </TabsList>
          )}

        {/* Add Stock Tab Content */}
          <TabsContent value="addstock" className="mt-4">
            <Card className="border-0 shadow-sm">
            <CardHeader className={isMobile ? "p-4 pb-3" : "pb-4"}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/5">
                      <ListPlus className="h-5 w-5 text-blue-600" />
                    </div>
                <div>
                  <CardTitle className={isMobile ? "text-lg" : "text-xl"}>
                        Stock Management
                  </CardTitle>
                  {!isMobile && (
                    <CardDescription className="mt-1">
                          Add new stock and track purchase history
                    </CardDescription>
                  )}
                    </div>
                </div>
              </div>
              
              {/* Sub tabs for Add Stock */}
              <Tabs 
                value={selectedAddStockSubTab} 
                onValueChange={setSelectedAddStockSubTab}
                className="mt-4"
              >
                  <TabsList className={isMobile ? "grid w-full grid-cols-2 h-auto p-0.5" : "grid w-full grid-cols-2 mb-4 h-auto p-0.5"}>
                    <TabsTrigger value="add" className={isMobile ? "flex items-center gap-2 py-2 text-sm" : "flex items-center gap-2 py-2.5"}>
                    <Truck className="h-4 w-4" />
                      <span>Add Stock</span>
                  </TabsTrigger>
                    <TabsTrigger value="history" className={isMobile ? "flex items-center gap-2 py-2 text-sm" : "flex items-center gap-2 py-2.5"}>
                    <History className="h-4 w-4" />
                      <span>Purchase History</span>
                  </TabsTrigger>
                </TabsList>
                
                {/* Add Stock Subtab */}
                <TabsContent value="add" className="mt-0">
                  <CardContent className={isMobile ? "p-0 pt-3" : "pt-2"}>
                    <div className={isMobile ? "space-y-3" : "space-y-4"}>
                      <MultiPurchaseLogForm
                        onSubmit={async (purchaseData, receiptFile) => {
                          try {
                            console.log('=== PURCHASE DEBUG START ===');
                            console.log('Purchase data received:', JSON.stringify(purchaseData, null, 2));
                            console.log('Receipt file:', receiptFile ? `${receiptFile.name} (${receiptFile.size} bytes)` : 'No receipt');
                            console.log('createPurchaseTransaction function:', typeof createPurchaseTransaction);
                            
                            // Create purchase transaction first (ensure it works)
                            console.log('Calling createPurchaseTransaction...');
                            const createdTransaction = await createPurchaseTransaction(purchaseData);
                            console.log('Transaction created successfully:', createdTransaction?._id);
                            
                            // Attach receipt image if provided
                            if (receiptFile && user?.restaurantId) {
                              try {
                                console.log('Attaching receipt image...');
                                const { attachReceiptImage } = await import('@/lib/db/v4/operations/purchase-transaction-ops');
                                await attachReceiptImage(createdTransaction._id, receiptFile, user.restaurantId);
                                console.log('Receipt image attached successfully');
                              } catch (imageError: any) {
                                console.error('Failed to attach receipt image:', imageError);
                                // Don't throw - the transaction was successful
                                const isUnsupportedEnvironment = imageError.message?.includes('not yet supported in this environment');
                                toast({
                                  title: isUnsupportedEnvironment ? "✅ Purchase Saved" : "⚠️ Purchase Saved",
                                  description: isUnsupportedEnvironment 
                                    ? "Purchase recorded successfully. Note: Image attachments require browser mode."
                                    : "Purchase recorded but receipt image failed to upload",
                                  variant: "default"
                                });
                              }
                            }
                            
                            // Refresh data
                            await refreshPurchaseTransactions();
                            await refreshStock();
                            await refreshSuppliers();
                            
                            // Clear editing state on successful submission
                            setEditingBatch(null);
                            
                            toast({
                                title: editingBatch ? "Purchase Updated" : "Purchase Recorded",
                                description: editingBatch 
                                  ? "Your purchase has been successfully updated"
                                  : "Your purchase has been successfully recorded",
                            });
                            
                            return createdTransaction;
                          } catch (error) {
                            const errorMessage = error instanceof Error ? error.message : 
                                               error ? String(error) : 
                                               'Unknown error occurred';
                            console.error('Error recording purchase:', errorMessage, error);
                            toast({
                                title: "Error",
                                description: `Failed to record purchase: ${errorMessage}`,
                              variant: "destructive",
                            });
                            return false;
                          }
                        }}
                        editingBatch={editingBatch}
                        stockItems={stockItems}
                        suppliers={suppliers}
                        onCreateSupplier={async (supplierData: Partial<Supplier>) => {
                          try {
                            // Create a new supplier with all provided information
                            const newSupplier = await createSupplier({
                              name: supplierData.name || '',
                              phoneNumber: supplierData.phoneNumber || '0000000000',
                              category: supplierData.category,
                              notes: supplierData.notes,
                              balance: supplierData.balance || 0,
                              isActive: supplierData.isActive !== undefined ? supplierData.isActive : true,
                            });
                            
                            toast({
                                title: "Supplier Created",
                                description: `Supplier "${supplierData.name}" has been created successfully`,
                            });
                            
                            await refreshSuppliers();
                            return newSupplier;
                          } catch (error) {
                              console.error('Error creating supplier:', error);
                            toast({
                                title: "Error",
                                description: "Failed to create supplier",
                              variant: "destructive",
                            });
                            throw error;
                          }
                        }}
                        onCreateStockItem={async (data) => {
                          try {
                            const newItem = await createStockItem(data);
                            
                            toast({
                                title: "Item Created",
                                description: `Item "${data.name}" has been created successfully`,
                            });
                            
                            await refreshStock();
                            return newItem;
                          } catch (error) {
                              console.error('Error creating item:', error);
                            toast({
                                title: "Error",
                                description: "Failed to create item",
                              variant: "destructive",
                            });
                            throw error;
                          }
                        }}
                        isMobile={isMobile}
                      />
                    </div>
                  </CardContent>
                </TabsContent>
                
                {/* History Subtab */}
                <TabsContent value="history" className="mt-0">
                  <CardContent className={isMobile ? "p-0 pt-3" : "pt-2"}>
                      <div className={isMobile ? "mt-2" : "mt-0"}>
                      <BatchedPurchaseHistory 
                        purchases={purchaseTransactions}
                        stockItems={stockItems}
                        suppliers={suppliers}
                        searchTerm={searchTerm}
                        onSearchChange={setSearchTerm}
                        onEditPurchase={handleEditPurchase}
                        isMobile={isMobile}
                        showIndividualPurchases={true}
                      />
                    </div>
                  </CardContent>
                </TabsContent>
              </Tabs>
            </CardHeader>
          </Card>
        </TabsContent>

        {/* Inventory Tab Content */}
          <TabsContent value="inventory" className="mt-4">
          <TabPermissionGuard page="inventory" tab="inventory">
              <Card className="border-0 shadow-sm">
              <CardHeader className={isMobile ? "p-4 pb-3" : "pb-4"}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-green-500/10 to-green-600/5">
                        <Package className="h-5 w-5 text-green-600" />
                      </div>
                  <div>
                    <CardTitle className={isMobile ? "text-lg" : "text-xl"}>
                          Inventory Items
                    </CardTitle>
                    {!isMobile && (
                      <CardDescription className="mt-1">
                            Manage your ingredients, supplies and inventory items
                      </CardDescription>
                    )}
                  </div>
                </div>
                
                    {/* Quick actions */}
                    {!isMobile && (
                      <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" onClick={() => {
                      refreshStock();
                      refreshPurchaseTransactions();
                    }}>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Refresh
                    </Button>
                    <Button 
                      size="sm" 
                      onClick={() => setIsNewItemDialogOpen(true)}
                    >
                          <Plus className="h-4 w-4 mr-2" />
                          New Item
                    </Button>
                      </div>
                    )}
                  </div>
                  
                  {/* Mobile quick actions */}
                  {isMobile && (
                    <div className="flex items-center gap-2 mt-3">
                      <Button size="sm" variant="outline" onClick={() => {
                        refreshStock();
                        refreshPurchaseTransactions();
                      }} className="flex-1">
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Sync
                      </Button>
                  <Button 
                    size="sm" 
                        className="gap-1"
                    onClick={() => {
                      setSelectedTab("addstock");
                      setSelectedAddStockSubTab("add");
                    }}
                  >
                    <ShoppingCart className="h-4 w-4" /> 
                        Purchase
                  </Button>
                </div>
                  )}
              </CardHeader>
              
              <CardContent className={isMobile ? "p-4 pt-0" : "pt-0"}>
                <Tabs defaultValue="ingredients" className="w-full">
                    <TabsList className={isMobile ? "grid w-full grid-cols-2 h-auto p-0.5 mb-4" : "mb-4 flex h-auto p-0.5"}>
                      <TabsTrigger value="ingredients" className={isMobile ? "flex items-center gap-2 py-2 text-sm" : "flex items-center gap-2 py-2.5"}>
                      <Package className="h-4 w-4" />
                        <span>Ingredients</span>
                    </TabsTrigger>
                      <TabsTrigger value="sous-recettes" className={isMobile ? "flex items-center gap-2 py-2 text-sm" : "flex items-center gap-2 py-2.5"}>
                      <ChefHat className="h-4 w-4" />
                        <span>Sub-recipes</span>
                    </TabsTrigger>
                  </TabsList>
                  
                                     <TabsContent value="ingredients" className="mt-0">
                    
                                         {/* New Item Dialog - works for both mobile and desktop */}
                     <Dialog open={isNewItemDialogOpen} onOpenChange={setIsNewItemDialogOpen}>
                       <DialogContent className="sm:max-w-md">
                         <DialogHeader>
                            <DialogTitle>{editingItem ? 'Edit Item' : 'New Item'}</DialogTitle>
                           <DialogDescription>
                              {editingItem ? 'Update your inventory item details' : 'Add a new item to your inventory'}
                           </DialogDescription>
                         </DialogHeader>
                         <div className="py-4">
                           <StockItemForm
                             onSubmit={handleStockItemSubmit}
                             initialData={editingItem || undefined}
                             suppliers={suppliers}
                           />
                         </div>
                       </DialogContent>
                     </Dialog>

                {/* Tabbed view by category */}
                <Tabs defaultValue="all" value={selectedCategory} onValueChange={setSelectedCategory}>
                  {isMobile ? (
                          <div className="flex justify-between items-center mb-4">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex items-center gap-2"
                        onClick={() => setIsFilterSheetOpen(true)}
                      >
                        <ListFilter className="h-4 w-4" />
                              <span>Filter</span>
                              {selectedCategory !== "all" && (
                                <Badge variant="secondary" className="ml-1 h-4 w-4 p-0 text-xs">1</Badge>
                              )}
                      </Button>
                      <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
                              <SheetContent side="left" className="w-[85vw]">
                          <SheetHeader>
                            <SheetTitle>Filter Inventory</SheetTitle>
                            <SheetDescription>
                              Select a category to filter items
                            </SheetDescription>
                          </SheetHeader>
                                <div className="mt-6 flex flex-col space-y-2">
                            <Button 
                              variant={selectedCategory === "all" ? "default" : "outline"} 
                                    className="justify-start h-12"
                              onClick={() => {
                                setSelectedCategory("all");
                                setIsFilterSheetOpen(false);
                              }}
                            >
                                    <Package className="h-4 w-4 mr-3" />
                              <span>All Items</span>
                              <Badge variant="secondary" className="ml-auto">{stockItems.length}</Badge>
                            </Button>
                            {categories.map(category => (
                              <Button 
                                key={category} 
                                variant={selectedCategory === category ? "default" : "outline"} 
                                      className="justify-start h-12"
                                onClick={() => {
                                  setSelectedCategory(category);
                                  setIsFilterSheetOpen(false);
                                }}
                              >
                                      <div className="mr-3">
                                {getCategoryIcon(category)}
                                      </div>
                                      <span>{category}</span>
                                <Badge variant="secondary" className="ml-auto">{categoryCounts[category] || 0}</Badge>
                              </Button>
                            ))}
                          </div>
                        </SheetContent>
                      </Sheet>
                      
                            <div className="text-sm text-muted-foreground">
                              {filteredItems.length} items
                            </div>
                    </div>
                  ) : (
                          <TabsList className="mb-4 flex flex-wrap h-auto bg-muted/50">
                            <TabsTrigger value="all" className="flex items-center gap-2 px-3 py-2 data-[state=active]:bg-background">
                      <Package className="h-4 w-4" />
                              <span>All</span>
                      <Badge variant="secondary" className="ml-1">{stockItems.length}</Badge>
                    </TabsTrigger>

                    {categories.map(category => (
                              <TabsTrigger key={category} value={category} className="flex items-center gap-2 px-3 py-2 data-[state=active]:bg-background">
                        {getCategoryIcon(category)}
                        <span>{category}</span>
                        <Badge variant="secondary" className="ml-1">{categoryCounts[category] || 0}</Badge>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  )}

                  <TabsContent value="all" className="mt-0">
                    <div className="relative mb-4">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                              placeholder="Search inventory items..."
                              className="pl-10 h-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    
                    {isMobile ? (
                      <div className="border border-muted/30 rounded-lg overflow-hidden bg-background">
                        {filteredItems.length === 0 ? (
                          <div className="p-8 text-center">
                            <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                            <h3 className="font-medium mb-2">No items found</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                              No inventory items match your search criteria
                            </p>
                            <Button size="sm" onClick={() => setIsNewItemDialogOpen(true)}>
                              <Plus className="h-4 w-4 mr-2" />
                              Add First Item
                            </Button>
                          </div>
                        ) : (
                          <>
                            {/* Table Header */}
                            <div className="flex items-center py-2 px-2 bg-muted/50 border-b border-muted/30 text-xs font-medium text-muted-foreground">
                              <div className="flex-1 pr-2">Item</div>
                              <div className="w-14 text-center px-1">Qty</div>
                              <div className="w-16 text-right px-1">Cost</div>
                              <div className="w-16 text-right px-1">Value</div>
                              <div className="w-6"></div>
                            </div>
                            {/* Table Rows */}
                            {filteredItems.map((item) => (
                              <MobileInventoryCard key={item.id} item={item} />
                            ))}
                          </>
                        )}
                      </div>
                    ) : (
                            <Card className="border-0 shadow-sm">
                      <CardContent className="p-0">
                        <Table>
                          <TableHeader>
                                    <TableRow className="border-b">
                                      <TableHead className="font-semibold">Item</TableHead>
                                      <TableHead className="font-semibold">Category</TableHead>
                                      <TableHead className="font-semibold">Unit</TableHead>
                                      <TableHead className="font-semibold">Quantity</TableHead>
                                      <TableHead className="font-semibold">Cost per Unit</TableHead>
                                      <TableHead className="font-semibold">Total Value</TableHead>
                                      <TableHead className="font-semibold text-right">30-Day Expense</TableHead>
                                      <TableHead className="font-semibold text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                              {filteredItems.length === 0 ? (
                                <TableRow>
                                        <TableCell colSpan={8} className="text-center py-12">
                                          <div className="flex flex-col items-center">
                                            <Package className="h-12 w-12 mb-4 text-muted-foreground/50" />
                                            <h3 className="font-medium mb-2">No items found</h3>
                                            <p className="text-sm text-muted-foreground mb-4">
                                              No inventory items match your search criteria
                                            </p>
                                            <Button size="sm" onClick={() => setIsNewItemDialogOpen(true)}>
                                              <Plus className="h-4 w-4 mr-2" />
                                              Add First Item
                                            </Button>
                                          </div>
                                  </TableCell>
                                </TableRow>
                              ) : (
                                filteredItems.map((item) => {
                                  // Calculate 30-day expense for this item
                                  const itemExpense = purchaseTransactions
                                    .filter(p => p.items.some(i => i.stockItemId === item.id))
                                    .filter(p => new Date(p.date) >= subDays(new Date(), 30))
                                    .reduce((sum, p) => {
                                      const relevantItems = p.items.filter(i => i.stockItemId === item.id);
                                      return sum + relevantItems.reduce((itemSum, i) => itemSum + i.totalCost, 0);
                                    }, 0);

                                        const isLowStock = item.quantity !== undefined && item.quantity < 10;

                                  return (
                                          <TableRow key={item.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => openItemDetailsDialog(item)}>
                                      <TableCell className="font-medium">
                                              <div className="flex items-center gap-3">
                                                <div className="p-1.5 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5">
                                          {getCategoryIcon(item.category)}
                                                </div>
                                                <div>
                                                  <div className="flex items-center gap-2">
                                                    <span>{item.name}</span>
                                                    {isLowStock && (
                                                      <Badge variant="destructive" className="text-xs">
                                                        Low Stock
                                            </Badge>
                                          )}
                                                  </div>
                                                  {item.supplierId && (
                                                    <div className="flex items-center gap-1 mt-1">
                                                      <ShoppingCart className="h-3 w-3 text-muted-foreground" />
                                                      <span className="text-xs text-muted-foreground">Has Supplier</span>
                                                    </div>
                                                  )}
                                                </div>
                                        </div>
                                      </TableCell>
                                            <TableCell>
                                              <Badge variant="outline" className="text-xs">
                                                {item.category}
                                              </Badge>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">{item.unit}</TableCell>
                                            <TableCell>
                                              <span className={isLowStock ? "text-destructive font-medium" : ""}>
                                                {item.quantity !== undefined ? `${item.quantity} ${item.unit}` : '-'}
                                              </span>
                                            </TableCell>
                                      <TableCell>{item.costPerUnit !== undefined ? formatCurrency(item.costPerUnit) : '-'}</TableCell>
                                            <TableCell className="font-medium">
                                              {item.quantity !== undefined && item.costPerUnit !== undefined ? formatCurrency(item.quantity * item.costPerUnit) : '-'}
                                            </TableCell>
                                      <TableCell className="text-right">
                                              <span className="font-medium">{formatCurrency(itemExpense)}</span>
                                      </TableCell>
                                      <TableCell className="text-right">
                                              <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                                    <MoreHorizontal className="h-4 w-4" />
                                                  </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                  <DropdownMenuItem onClick={(e) => {
                                            e.stopPropagation();
                                            openEditDialog(item);
                                          }}>
                                                    <Plus className="h-4 w-4 mr-2" />
                                                    Adjust Stock
                                                  </DropdownMenuItem>
                                                  <DropdownMenuItem onClick={(e) => {
                                                    e.stopPropagation();
                                                    setSelectedTab("addstock");
                                                    setSelectedAddStockSubTab("add");
                                                  }}>
                                                    <ShoppingCart className="h-4 w-4 mr-2" />
                                                    Add Purchase
                                                  </DropdownMenuItem>
                                                </DropdownMenuContent>
                                              </DropdownMenu>
                                      </TableCell>
                                    </TableRow>
                                  );
                                })
                              )}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                    )}
                  </TabsContent>

                  {/* Category tabs */}
                  {categories.map(category => (
                    <TabsContent key={category} value={category} className="mt-0">
                            <Card className="border-0 shadow-sm">
                              <CardHeader className="pb-3">
                          <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5">
                              {getCategoryIcon(category)}
                                    </div>
                                    <div>
                                      <CardTitle className="text-lg">{category}</CardTitle>
                            <CardDescription>
                                        {formatCurrency(categoryTotals[category] || 0)} spent in last 30 days
                            </CardDescription>
                                    </div>
                                  </div>
                          </div>
                        </CardHeader>
                        <CardContent className="p-0">
                          {isMobile ? (
                                  <div className="border border-muted/30 rounded-lg overflow-hidden bg-background">
                              {itemsByCategory[category] && itemsByCategory[category].length > 0 ? (
                                <>
                                  {/* Table Header */}
                                  <div className="flex items-center py-2 px-2 bg-muted/50 border-b border-muted/30 text-xs font-medium text-muted-foreground">
                                    <div className="flex-1 pr-2">Item</div>
                                    <div className="w-14 text-center px-1">Qty</div>
                                    <div className="w-16 text-right px-1">Cost</div>
                                    <div className="w-16 text-right px-1">Value</div>
                                    <div className="w-6"></div>
                                  </div>
                                  {/* Table Rows */}
                                  {itemsByCategory[category].map((item) => (
                                    <MobileInventoryCard key={item.id} item={item} />
                                  ))}
                                </>
                              ) : (
                                      <div className="p-8 text-center">
                                        <div className="p-3 rounded-full bg-muted/50 w-fit mx-auto mb-4">
                                          {getCategoryIcon(category)}
                                        </div>
                                        <h3 className="font-medium mb-2">No {category.toLowerCase()} found</h3>
                                        <p className="text-sm text-muted-foreground mb-4">
                                          Add your first {category.toLowerCase()} item to get started
                                        </p>
                                        <Button size="sm" onClick={() => setIsNewItemDialogOpen(true)}>
                                          <Plus className="h-4 w-4 mr-2" />
                                          Add Item
                                        </Button>
                                </div>
                              )}
                            </div>
                          ) : (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                        <TableHead className="font-semibold">Item</TableHead>
                                        <TableHead className="font-semibold">Unit</TableHead>
                                        <TableHead className="font-semibold">Quantity</TableHead>
                                        <TableHead className="font-semibold">Cost per Unit</TableHead>
                                        <TableHead className="font-semibold">Total Value</TableHead>
                                        <TableHead className="font-semibold text-right">30-Day Expense</TableHead>
                                        <TableHead className="font-semibold text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {itemsByCategory[category].length === 0 ? (
                                <TableRow>
                                          <TableCell colSpan={7} className="text-center py-12">
                                            <div className="flex flex-col items-center">
                                              <div className="p-3 rounded-full bg-muted/50 w-fit mx-auto mb-4">
                                                {getCategoryIcon(category)}
                                              </div>
                                              <h3 className="font-medium mb-2">No {category.toLowerCase()} found</h3>
                                              <p className="text-sm text-muted-foreground mb-4">
                                    {searchTerm
                                                  ? 'No items found in this category matching your search'
                                                  : `Add your first ${category.toLowerCase()} item to get started`}
                                              </p>
                                              <Button size="sm" onClick={() => setIsNewItemDialogOpen(true)}>
                                                <Plus className="h-4 w-4 mr-2" />
                                                Add Item
                                              </Button>
                                            </div>
                                  </TableCell>
                                </TableRow>
                              ) : (
                                itemsByCategory[category].map((item) => {
                                  // Calculate 30-day expense for this item
                                  const itemExpense = purchaseTransactions
                                    .filter(p => p.items.some(i => i.stockItemId === item.id))
                                    .filter(p => new Date(p.date) >= subDays(new Date(), 30))
                                    .reduce((sum, p) => {
                                      const relevantItems = p.items.filter(i => i.stockItemId === item.id);
                                      return sum + relevantItems.reduce((itemSum, i) => itemSum + i.totalCost, 0);
                                    }, 0);

                                          const isLowStock = item.quantity !== undefined && item.quantity < 10;

                                  return (
                                            <TableRow key={item.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => openItemDetailsDialog(item)}>
                                      <TableCell className="font-medium">
                                                <div className="flex items-center gap-3">
                                                  <div className="p-1.5 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5">
                                          {getCategoryIcon(item.category)}
                                                  </div>
                                                  <div>
                                                    <div className="flex items-center gap-2">
                                                      <span>{item.name}</span>
                                                      {isLowStock && (
                                                        <Badge variant="destructive" className="text-xs">
                                                          Low Stock
                                            </Badge>
                                          )}
                                                    </div>
                                                    {item.supplierId && (
                                                      <div className="flex items-center gap-1 mt-1">
                                                        <ShoppingCart className="h-3 w-3 text-muted-foreground" />
                                                        <span className="text-xs text-muted-foreground">Has Supplier</span>
                                                      </div>
                                                    )}
                                                  </div>
                                        </div>
                                      </TableCell>
                                              <TableCell className="text-muted-foreground">{item.unit}</TableCell>
                                              <TableCell>
                                                <span className={isLowStock ? "text-destructive font-medium" : ""}>
                                                  {item.quantity !== undefined ? `${item.quantity} ${item.unit}` : '-'}
                                                </span>
                                              </TableCell>
                                      <TableCell>{item.costPerUnit !== undefined ? formatCurrency(item.costPerUnit) : '-'}</TableCell>
                                              <TableCell className="font-medium">
                                                {item.quantity !== undefined && item.costPerUnit !== undefined ? formatCurrency(item.quantity * item.costPerUnit) : '-'}
                                              </TableCell>
                                      <TableCell className="text-right">
                                                <span className="font-medium">{formatCurrency(itemExpense)}</span>
                                      </TableCell>
                                      <TableCell className="text-right">
                                                <DropdownMenu>
                                                  <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                                      <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                  </DropdownMenuTrigger>
                                                  <DropdownMenuContent align="end">
                                                    <DropdownMenuItem onClick={(e) => {
                                            e.stopPropagation();
                                            openEditDialog(item);
                                          }}>
                                                      <Plus className="h-4 w-4 mr-2" />
                                                      Adjust Stock
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem onClick={(e) => {
                                                      e.stopPropagation();
                                                      setSelectedTab("addstock");
                                                      setSelectedAddStockSubTab("add");
                                                    }}>
                                                      <ShoppingCart className="h-4 w-4 mr-2" />
                                                      Add Purchase
                                                    </DropdownMenuItem>
                                                  </DropdownMenuContent>
                                                </DropdownMenu>
                                      </TableCell>
                                    </TableRow>
                                  );
                                })
                              )}
                            </TableBody>
                          </Table>
                          )}
                        </CardContent>
                      </Card>
                    </TabsContent>
                  ))}
                </Tabs>
              </TabsContent>
              
              <TabsContent value="sous-recettes" className="mt-0">
                <SubRecipesList />
              </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </TabPermissionGuard>
        </TabsContent>

        {/* Stock Counts Tab Content */}
          <TabsContent value="counts" className="mt-4">
          <TabPermissionGuard page="inventory" tab="counts">
              <div className={isMobile ? "flex flex-col space-y-2 mb-2" : "flex justify-between items-center gap-2 mb-3"}>
              <div>
                  <h2 className={isMobile ? "text-base font-semibold" : "text-lg font-semibold"}>Comptages de stock</h2>
                {!isMobile && (
                <p className="text-sm text-muted-foreground">Gérez les comptages physiques d'inventaire et la réconciliation</p>
                )}
              </div>
              <div className={isMobile ? "flex gap-2" : "flex items-center gap-2"}>
                <Button size={isMobile ? "sm" : "sm"} variant="outline" onClick={refreshStockCounts} className={isMobile ? "flex-1" : ""}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Rafraîchir
                </Button>

                <Button 
                  onClick={handleCreateStockCount}
                  className={isMobile ? "flex-1" : ""}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  {isMobile ? "Nouveau" : "Nouveau comptage"}
                </Button>
              </div>
            </div>

            <div className={isMobile ? "grid grid-cols-1 gap-4" : "grid grid-cols-1 md:grid-cols-3 gap-4"}>
              {/* For mobile, combine into a single column with expandable sections */}
              {isMobile ? (
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center justify-between">
                        <span>Comptages de stock</span>
                        <Badge variant="outline" className="ml-2">
                          {stockCounts.length}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      <ScrollArea className="max-h-[300px]">
                        {stockCounts.length === 0 ? (
                          <div className="p-4 text-center text-muted-foreground">
                            Aucun comptage de stock trouvé. Créez votre premier comptage pour commencer.
                          </div>
                        ) : (
                          <div className="space-y-1 p-2">
                            {stockCounts.map((count) => (
                              <Button
                                key={count.id}
                                variant={selectedCount?.id === count.id ? "secondary" : "ghost"}
                                className="w-full justify-start text-left"
                                onClick={() => handleSelectCount(count)}
                              >
                                <div className="flex items-center gap-2">
                                  {count.status === 'draft' && <ClipboardList className="h-4 w-4" />}
                                  {count.status === 'in_progress' && <ClipboardList className="h-4 w-4 text-blue-500" />}
                                  {count.status === 'completed' && <ClipboardCheck className="h-4 w-4 text-green-500" />}
                                  <div className="flex flex-col">
                                    <span className="font-medium">{count.name}</span>
                                    <span className="text-xs text-muted-foreground">
                                      {format(new Date(count.date), 'MMM d, yyyy')} - {count.countType.toUpperCase()}
                                    </span>
                                  </div>
                                </div>
                                <Badge
                                  variant={count.status === 'completed' ? "success" : count.status === 'in_progress' ? "secondary" : "outline"}
                                  className="ml-auto"
                                >
                                  {count.status.replace('_', ' ').toUpperCase()}
                                </Badge>
                              </Button>
                            ))}
                          </div>
                        )}
                      </ScrollArea>
                    </CardContent>
                  </Card>
                  
                  {/* Selected count details */}
                  {!selectedCount ? (
                    <Card className="h-full flex items-center justify-center">
                      <CardContent className="text-center p-4">
                        <ClipboardList className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                        <h3 className="text-base font-medium mb-2">Aucun comptage sélectionné</h3>
                        <Button size="sm" onClick={handleCreateStockCount}>
                          <Plus className="h-4 w-4 mr-1" />
                          Créer un comptage
                        </Button>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-base">{selectedCount.name}</CardTitle>
                            <CardDescription className="text-xs">
                              {format(new Date(selectedCount.date), 'PPP')}
                            </CardDescription>
                          </div>
                          <div className="flex gap-1">
                            {selectedCount.status === 'draft' && (
                              <Button size="sm" onClick={handleInitializeCount}>
                                <ClipboardList className="h-4 w-4 mr-1" />
                                Initialiser
                              </Button>
                            )}
                            {countView === 'sheet' && selectedCount.status !== 'completed' && countItems.length > 0 && (
                              <Button size="sm" onClick={() => setCountView('entry')}>
                                Entrer
                              </Button>
                            )}
                            {countView === 'entry' && (
                              <Button size="sm" onClick={() => setCountView('sheet')}>
                                Feuille
                              </Button>
                            )}
                            {selectedCount.status === 'completed' && countView !== 'report' && (
                              <Button size="sm" onClick={() => setCountView('report')}>
                                Rapport
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-2 pt-0">
                        {isLoadingCountItems ? (
                          <div className="flex justify-center items-center h-[400px]">
                            <div className="text-center">
                              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                              <p className="text-muted-foreground">Chargement des données de comptage...</p>
                            </div>
                          </div>
                        ) : (
                          <div>
                              {countView === 'sheet' && selectedCount && (
                              <StockCountSheet
                                stockCount={selectedCount}
                                stockItems={stockItems}
                              />
                            )}
                              {countView === 'entry' && selectedCount && (
                              <StockCountEntryForm
                                stockCount={selectedCount}
                                stockItems={stockItems}
                                countItems={countItems}
                                onSaveItems={handleSaveCountItems}
                                onComplete={handleCompleteCount}
                              />
                            )}
                              {countView === 'report' && selectedCount && (
                              <StockCountVarianceReport
                                stockCount={selectedCount}
                                countItems={countItems}
                                stockItems={stockItems.reduce((acc, item) => {
                                  acc[item.id] = item;
                                  return acc;
                                }, {} as Record<string, StockItem>)}
                              />
                            )}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <>
                  {/* Desktop layout remains unchanged */}
              <div className="md:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Comptages de stock</CardTitle>
                    <CardDescription>Sélectionnez un comptage à voir ou à modifier</CardDescription>
                  </CardHeader>
                  <CardContent className="p-0">
                    <ScrollArea className="h-[500px]">
                      {stockCounts.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground">
                          Aucun comptage de stock trouvé. Créez votre premier comptage pour commencer.
                        </div>
                      ) : (
                        <div className="space-y-1 p-2">
                          {stockCounts.map((count) => (
                            <Button
                              key={count.id}
                              variant={selectedCount?.id === count.id ? "secondary" : "ghost"}
                              className="w-full justify-start text-left"
                              onClick={() => handleSelectCount(count)}
                            >
                              <div className="flex items-center gap-2">
                                {count.status === 'draft' && <ClipboardList className="h-4 w-4" />}
                                {count.status === 'in_progress' && <ClipboardList className="h-4 w-4 text-blue-500" />}
                                {count.status === 'completed' && <ClipboardCheck className="h-4 w-4 text-green-500" />}
                                <div className="flex flex-col">
                                  <span className="font-medium">{count.name}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {format(new Date(count.date), 'MMM d, yyyy')} - {count.countType.toUpperCase()}
                                  </span>
                                </div>
                              </div>
                              <Badge
                                variant={count.status === 'completed' ? "success" : count.status === 'in_progress' ? "secondary" : "outline"}
                                className="ml-auto"
                              >
                                {count.status.replace('_', ' ').toUpperCase()}
                              </Badge>
                            </Button>
                          ))}
                        </div>
                      )}
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>

              <div className="md:col-span-2">
                {!selectedCount ? (
                  <Card className="h-full flex items-center justify-center">
                    <CardContent className="text-center p-8">
                          <ClipboardList className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                          <h3 className="text-base font-medium mb-2">Aucun comptage sélectionné</h3>
                          <Button size="sm" onClick={handleCreateStockCount}>
                        <Plus className="h-4 w-4 mr-1" />
                            Créer un comptage
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                        <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                              <CardTitle className="text-base">{selectedCount.name}</CardTitle>
                              <CardDescription className="text-xs">
                                {format(new Date(selectedCount.date), 'PPP')}
                          </CardDescription>
                        </div>
                            <div className="flex gap-1">
                          {selectedCount.status === 'draft' && (
                            <Button size="sm" onClick={handleInitializeCount}>
                              <ClipboardList className="h-4 w-4 mr-1" />
                                  Initialiser
                            </Button>
                          )}
                          {countView === 'sheet' && selectedCount.status !== 'completed' && countItems.length > 0 && (
                            <Button size="sm" onClick={() => setCountView('entry')}>
                                  Entrer
                            </Button>
                          )}
                          {countView === 'entry' && (
                            <Button size="sm" onClick={() => setCountView('sheet')}>
                                  Feuille
                            </Button>
                          )}
                          {selectedCount.status === 'completed' && countView !== 'report' && (
                            <Button size="sm" onClick={() => setCountView('report')}>
                                  Rapport
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                        <CardContent className="p-2 pt-0">
                      {isLoadingCountItems ? (
                        <div className="flex justify-center items-center h-[400px]">
                          <div className="text-center">
                            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                            <p className="text-muted-foreground">Chargement des données de comptage...</p>
                          </div>
                        </div>
                      ) : (
                        <div>
                          {countView === 'sheet' && selectedCount && (
                            <StockCountSheet
                              stockCount={selectedCount}
                              stockItems={stockItems}
                            />
                          )}
                          {countView === 'entry' && selectedCount && (
                            <StockCountEntryForm
                              stockCount={selectedCount}
                              stockItems={stockItems}
                              countItems={countItems}
                              onSaveItems={handleSaveCountItems}
                              onComplete={handleCompleteCount}
                            />
                          )}
                          {countView === 'report' && selectedCount && (
                            <StockCountVarianceReport
                              stockCount={selectedCount}
                              countItems={countItems}
                              stockItems={stockItems.reduce((acc, item) => {
                                acc[item.id] = item;
                                return acc;
                              }, {} as Record<string, StockItem>)}
                            />
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
                </>
              )}
            </div>
          </TabPermissionGuard>
        </TabsContent>

        {/* Waste Tracking Tab Content */}
        <TabsContent value="waste" className="mt-4">
          <TabPermissionGuard page="inventory" tab="waste">
            <div className={isMobile ? "flex flex-col space-y-2 mb-2" : "flex justify-between items-center gap-2 mb-3"}>
              <div>
                <h2 className={isMobile ? "text-lg font-semibold" : "text-xl font-semibold"}>Suivi des pertes</h2>
                {!isMobile && (
                <p className="text-sm text-muted-foreground">Enregistrez et suivez les pertes et gaspillages d'inventaire</p>
                )}
              </div>
              <div className={isMobile ? "flex gap-2" : "flex items-center gap-2"}>
                <Button size="sm" variant="outline" onClick={refreshWasteLogs} className={isMobile ? "flex-1" : ""}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Rafraîchir
                </Button>

                <Dialog open={isWasteDialogOpen} onOpenChange={setIsWasteDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="destructive" className={isMobile ? "flex-1" : ""}>
                      <Trash2 className="h-4 w-4 mr-1" />
                      {isMobile ? "Perte" : "Enregistrer une perte"}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Enregistrer une perte</DialogTitle>
                      <DialogDescription>
                        Enregistrez les articles d'inventaire gaspillés ou avariés
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <WasteTrackingForm
                        onSubmit={handleCreateWasteLog}
                        stockItems={stockItems}
                        selectedItemId={selectedWasteItem?.id}
                      />
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <Card>
                <CardHeader className={isMobile ? "pb-2" : ""}>
                  <CardTitle className={isMobile ? "text-base" : ""}>Historique des pertes</CardTitle>
                  {!isMobile && (
                  <CardDescription>Enregistrez et suivez les pertes et gaspillages d'inventaire</CardDescription>
                  )}
                </CardHeader>
                <CardContent className="p-0">
                  {isMobile ? (
                    <div className="p-3 space-y-2">
                      {wasteLogs.length === 0 ? (
                        <div className="text-center py-4 text-muted-foreground text-sm">
                          Aucune perte enregistrée.
                        </div>
                      ) : (
                        wasteLogs
                          .filter(log => !log.notes?.includes('HIDDEN_STOCK_WASTE: true')) // Hide behind-the-scenes stock waste
                          .map((log) => {
                          // 🚀 NEW: Check if this is a menu item waste log
                          const isMenuItemWaste = log.stockItemId === 'menu-item-waste';
                          
                          let displayName = 'Unknown Item';
                          let displayUnit = '';
                          let value = 0;
                          
                                    if (isMenuItemWaste) {
            // Parse menu item data from notes
            try {
              const dataMatch = log.notes?.match(/DATA: ({.*})/);
              if (dataMatch) {
                const menuItemData = JSON.parse(dataMatch[1]);
                displayName = menuItemData.menuItemName;
                // 🚀 Use cost value instead of selling price
                value = menuItemData.costValue || menuItemData.wasteValue || 0;
                displayUnit = 'item(s)';
              } else {
                // Fallback - extract menu item name from notes
                const nameMatch = log.notes?.match(/Menu Item Waste: ([^(]+)/);
                displayName = nameMatch ? nameMatch[1].trim() : 'Menu Item';
                displayUnit = 'item(s)';
                value = 0; // No cost data available in fallback
              }
            } catch (e) {
              displayName = 'Menu Item';
              displayUnit = 'item(s)';
              value = 0;
            }
          } else {
                            // Regular stock item waste
                            const stockItem = stockItems.find(item => item.id === log.stockItemId);
                            displayName = stockItem ? stockItem.name : 'Unknown Item';
                            displayUnit = stockItem?.unit || '';
                            value = stockItem?.costPerUnit ? stockItem.costPerUnit * log.quantity : 0;
                          }
                          
                          return (
                            <Card key={log.id} className="border-muted">
                              <CardContent className="p-3">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <div className="flex items-center gap-2">
                                      <p className="font-medium text-sm">{displayName}</p>
                                      {isMenuItemWaste && (
                                        <Badge variant="secondary" className="text-xs px-1.5 py-0">
                                          Menu Item
                                        </Badge>
                                      )}
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                      {format(new Date(log.date), 'MMM d, yyyy HH:mm')}
                                    </p>
                                  </div>
                                  <p className="text-destructive font-semibold">-{formatCurrency(value)}</p>
                                </div>
                                <div className="mt-2 flex justify-between">
                                  <div className="flex gap-2 text-xs">
                                    <Badge variant="outline">
                                      {log.reason.replace('_', ' ').toUpperCase()}
                                    </Badge>
                                    <span>{log.quantity} {displayUnit}</span>
                                  </div>
                                </div>
                                {log.notes && !log.notes.includes('DATA:') && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {log.notes.split(' | DATA:')[0]}
                                  </p>
                                )}
                              </CardContent>
                            </Card>
                          );
                        })
                      )}
                    </div>
                  ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date & Heure</TableHead>
                        <TableHead>Article</TableHead>
                        <TableHead>Quantité</TableHead>
                        <TableHead>Raison</TableHead>
                        <TableHead>Notes</TableHead>
                        <TableHead className="text-right">Valeur</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {wasteLogs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            Aucune perte enregistrée. Utilisez le bouton "Enregistrer une perte" pour enregistrer une perte.
                          </TableCell>
                        </TableRow>
                      ) : (
                        wasteLogs
                          .filter(log => !log.notes?.includes('HIDDEN_STOCK_WASTE: true')) // Hide behind-the-scenes stock waste
                          .map((log) => {
                          // 🚀 NEW: Check if this is a menu item waste log
                          const isMenuItemWaste = log.stockItemId === 'menu-item-waste';
                          
                          let displayName = 'Unknown Item';
                          let displayUnit = '';
                          let value = 0;
                          let notes = log.notes || '-';
                          
                                      if (isMenuItemWaste) {
              // Parse menu item data from notes
              try {
                const dataMatch = log.notes?.match(/DATA: ({.*})/);
                if (dataMatch) {
                  const menuItemData = JSON.parse(dataMatch[1]);
                  displayName = menuItemData.menuItemName;
                  // 🚀 Use cost value instead of selling price
                  value = menuItemData.costValue || menuItemData.wasteValue || 0;
                  displayUnit = 'item(s)';
                  notes = log.notes?.split(' | DATA:')[0] || '-';
                } else {
                  // Fallback - extract menu item name from notes
                  const nameMatch = log.notes?.match(/Menu Item Waste: ([^(]+)/);
                  displayName = nameMatch ? nameMatch[1].trim() : 'Menu Item';
                  displayUnit = 'item(s)';
                  notes = log.notes || '-';
                  value = 0; // No cost data available in fallback
                }
              } catch (e) {
                displayName = 'Menu Item';
                displayUnit = 'item(s)';
                notes = log.notes || '-';
                value = 0;
              }
            } else {
                            // Regular stock item waste
                            const stockItem = stockItems.find(item => item.id === log.stockItemId);
                            displayName = stockItem ? stockItem.name : 'Unknown Item';
                            displayUnit = stockItem?.unit || '';
                            value = stockItem?.costPerUnit ? stockItem.costPerUnit * log.quantity : 0;
                          }

                          return (
                            <TableRow key={log.id}>
                              <TableCell>{format(new Date(log.date), 'MMM d, yyyy HH:mm')}</TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  {displayName}
                                  {isMenuItemWaste && (
                                    <Badge variant="secondary" className="text-xs px-1.5 py-0">
                                      Menu Item
                                    </Badge>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                {log.quantity} {displayUnit}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {log.reason.replace('_', ' ').toUpperCase()}
                                </Badge>
                              </TableCell>
                              <TableCell>{notes}</TableCell>
                              <TableCell className="text-right font-medium text-destructive">
                                {formatCurrency(value)}
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabPermissionGuard>
        </TabsContent>

        {/* Recettes Tab Content */}
        <TabsContent value="recettes" className="mt-4">
          <div className="space-y-8">
            {/* knowledge: use production SubRecipesList for sub-recipe creation */}
            <SubRecipesList />
            <MenuRecipesList />
          </div>
        </TabsContent>

        {/* Production Tab Content */}
        <TabsContent value="production" className="mt-4">
          <TabPermissionGuard page="inventory" tab="subrecipes">
            <BatchProduction />
          </TabPermissionGuard>
        </TabsContent>
      </Tabs>

      {/* Item Details Dialog */}
      <Dialog open={isItemDialogOpen} onOpenChange={setIsItemDialogOpen}>
        <DialogContent className={isMobile ? "sm:max-w-full max-h-[90vh] overflow-auto" : "sm:max-w-[800px]"}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {getCategoryIcon(selectedItem?.category || 'Other')}
              {selectedItem?.name}
              {isCogsEnabled && (
                <span className="ml-2 inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700">
                  <ChefHat className="mr-1 h-3 w-3" />
                  COGS Enabled
                </span>
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedItem?.category} • {selectedItem?.unit}
              {isCogsEnabled && selectedItem && typeof selectedItem.quantity === 'number' && (
                <> • Quantity: {selectedItem.quantity} {selectedItem.unit}</>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className={isMobile ? "py-2" : "py-4"}>
            {selectedItem && (
              <Tabs defaultValue="purchases" className={isMobile ? "space-y-2" : ""}>
                <TabsList className={isMobile ? "w-full mb-2" : ""}>
                  <TabsTrigger value="purchases" className="flex items-center gap-1">
                    <ShoppingCart className="h-4 w-4" />
                    <span className={isMobile ? "text-xs" : ""}>Purchase History</span>
                  </TabsTrigger>
                  <TabsTrigger value="adjustments" className="flex items-center gap-1">
                    <RefreshCw className="h-4 w-4" />
                    <span className={isMobile ? "text-xs" : ""}>Adjustments</span>
                  </TabsTrigger>
                  {isCogsEnabled && (
                    <TabsTrigger value="waste" className="flex items-center gap-1">
                      <Trash2 className="h-4 w-4" />
                      <span className={isMobile ? "text-xs" : ""}>Waste</span>
                    </TabsTrigger>
                  )}
                </TabsList>

                <TabsContent value="purchases">
                  <div className={isMobile ? "space-y-2" : "space-y-4"}>
              <PurchaseHistoryView
                stockItem={selectedItem}
                purchases={itemPurchases}
                suppliers={suppliers}
                isLoading={isLoadingPurchases}
                      isMobile={isMobile}
                    />

                    <div className="flex flex-col sm:flex-row gap-2 mt-2">
                      <Button 
                        onClick={() => {
              setIsNewItemDialogOpen(true);
              setIsItemDialogOpen(false);
                        }}
                        className="flex-1"
                      >
              <ShoppingCart className="mr-2 h-4 w-4" />
              Log Purchase
            </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          if (selectedItem) {
                            openEditDialog(selectedItem);
                          }
                        }}
                        className="flex-1"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Adjust Stock
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="adjustments">
                  <div className={isMobile ? "space-y-2" : "space-y-4"}>
                    {/* Stock Adjustments Tab Content */}
                    <Card>
                      <CardHeader className={isMobile ? "p-3 pb-2" : "pb-2"}>
                        <div className="flex justify-between items-center">
                          <CardTitle className={isMobile ? "text-base" : "text-lg"}>Adjustment History</CardTitle>
                          <Button 
                            variant="outline" 
                            size={isMobile ? "sm" : "default"} 
                            onClick={() => {
                              if (selectedItem) {
                                openEditDialog(selectedItem);
                              }
                            }}
                          >
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Adjust Stock
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className={isMobile ? "p-3 pt-0" : ""}>
                        {/* Stock adjustments would be displayed here */}
                        <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                          <RefreshCw className="h-12 w-12 mb-2" />
                          <p>No stock adjustments found for this item.</p>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mt-4"
                            onClick={() => {
                              if (selectedItem) {
                                openEditDialog(selectedItem);
                              }
                            }}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            Record First Adjustment
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {isCogsEnabled && (
                  <TabsContent value="waste">
                    <div className={isMobile ? "space-y-2" : "space-y-4"}>
                      {/* Waste Tracking Tab Content */}
                      <Card>
                        <CardHeader className={isMobile ? "p-3 pb-2" : "pb-2"}>
                          <div className="flex justify-between items-center">
                            <CardTitle className={isMobile ? "text-base" : "text-lg"}>Waste History</CardTitle>
                            <Button 
                              variant="outline" 
                              size={isMobile ? "sm" : "default"} 
                              onClick={() => {
                                if (selectedItem) {
                                  setSelectedWasteItem(selectedItem);
                                  setIsWasteDialogOpen(true);
                                  setIsItemDialogOpen(false);
                                }
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Log Waste
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className={isMobile ? "p-3 pt-0" : ""}>
                          {/* Waste logs would be displayed here */}
                          <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                            <Trash2 className="h-12 w-12 mb-2" />
                            <p>No waste records found for this item.</p>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="mt-4"
                              onClick={() => {
                                if (selectedItem) {
                                  setSelectedWasteItem(selectedItem);
                                  setIsWasteDialogOpen(true);
                                  setIsItemDialogOpen(false);
                                }
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Record First Waste
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                )}
              </Tabs>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
    </div>
  );
}