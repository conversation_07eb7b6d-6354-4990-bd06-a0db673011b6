"use client";

import { PageHeader } from "@/components/ui/page-header";
// import { P2PSettings } from "@/components/settings/p2p-settings";
import { Separator } from "@/components/ui/separator";
import { Network } from "lucide-react";

export default function NetworkSettingsPage() {
  return (
    <div className="space-y-6">
      <PageHeader
        heading="Network Settings"
        description="Configure network and synchronization settings"
        icon={<Network className="h-6 w-6" />}
      />
      
      <Separator />

      <div className="grid gap-4">
        {/* P2PSettings component removed due to missing file */}
      </div>
    </div>
  );
}
