"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle, AlertTriangle } from 'lucide-react';

export default function FixStaffIdsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const fixStaffIds = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/staff/fix-staff-ids', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fix staff IDs');
      }
      
      setResults(data.results);
    } catch (err) {
      console.error('Error fixing staff IDs:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Fix Staff IDs</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Staff ID Migration Tool</CardTitle>
          <CardDescription>
            This tool fixes mismatches between staff IDs in the auth documents and the staff document.
            It ensures that the staffId in the auth document's metadata matches the ID in the staff document.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Use this tool if staff members are having issues with permissions not loading correctly.
            This is a one-time fix for existing staff members.
          </p>
          
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {results && (
            <div className="mt-4 space-y-4">
              <Alert variant={results.updated > 0 ? "default" : "warning"}>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>Migration Results</AlertTitle>
                <AlertDescription>
                  <div className="mt-2">
                    <p>Total staff members: {results.total}</p>
                    <p>Updated: {results.updated}</p>
                    <p>Skipped: {results.skipped}</p>
                    <p>Failed: {results.failed}</p>
                  </div>
                </AlertDescription>
              </Alert>
              
              {results.details && results.details.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-lg font-medium mb-2">Details</h3>
                  <div className="border rounded-md overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff ID</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {results.details.map((detail: any, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{detail.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{detail.staffId}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{detail.name || detail.username || 'N/A'}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {detail.status === 'updated' && <span className="text-green-600">Updated</span>}
                              {detail.status === 'skipped' && <span className="text-yellow-600">Skipped</span>}
                              {detail.status === 'failed' && <span className="text-red-600">Failed</span>}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {detail.reason || detail.error || (detail.oldStaffId && `Old staffId: ${detail.oldStaffId}`) || ''}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button 
            onClick={fixStaffIds} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Fixing Staff IDs...
              </>
            ) : (
              'Fix Staff IDs'
            )}
          </Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>What This Tool Does</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            This tool addresses a common issue where the staffId in the auth document's metadata
            doesn't match the ID in the staff document. This mismatch can cause permissions to not
            load correctly when staff members log in.
          </p>
          <p className="mb-4">
            For each staff member in the staff document:
          </p>
          <ol className="list-decimal pl-6 mb-4 space-y-2">
            <li>It finds the corresponding auth document using the userId</li>
            <li>It updates the staffId in the auth document's metadata to match the ID in the staff document</li>
            <li>This ensures that when the staff member logs in, their permissions are correctly loaded</li>
          </ol>
          <p>
            After running this tool, staff members should be able to log in and have their permissions
            loaded correctly from the staff document.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
