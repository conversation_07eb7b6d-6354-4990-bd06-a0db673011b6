'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Hourglass, QrCode, ArrowRight, AlertCircle, RefreshCw, Wifi, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { usePermissions } from '@/lib/hooks/use-permissions';
import { OfflineConnectionDialog } from '@/components/ui/offline-connection-dialog';
import { toast } from 'sonner';
import { useStaticNavigation } from '@/lib/utils/navigation';

// Minimal ExtendedUser type for this component's needs
interface ExtendedUser {
  id?: string;
  name?: string;
  role?: string;
  permissions?: {
    pages?: Record<string, boolean>;
  };
}

export default function WaitingForSyncPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { isLoading: permissionsLoading, permissions, error: permissionsError } = usePermissions();
  const [isScanQrDialogOpen, setIsScanQrDialogOpen] = useState(false);
  const [syncAttempted, setSyncAttempted] = useState(false);
  const [redirectDelayActive, setRedirectDelayActive] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'attempting' | 'success' | 'failed'>('idle');
  const { navigate } = useStaticNavigation();

  const extendedUser = user as ExtendedUser | null;

  // Check if user has gained permissions and redirect
  useEffect(() => {
    console.log("🔄 SYNC PAGE - Checking redirect conditions:", {
      isAuthenticated,
      permissionsReady: !permissionsLoading,
      role: extendedUser?.role,
      hasPermissionsObject: !!extendedUser?.permissions?.pages,
      syncStatus
    });

    if (redirectDelayActive) {
      return;
    }

    // If user has gained permissions, redirect to menu
    if (isAuthenticated && !permissionsLoading && extendedUser?.permissions?.pages) {
      const hasAnyPermission = Object.values(extendedUser.permissions.pages).some(Boolean);
      if (hasAnyPermission) {
        console.log("✅ SYNC PAGE - User has gained permissions, redirecting to menu");
        toast.success("🎉 Permissions synced successfully! Redirecting to menu...");
        setRedirectDelayActive(true);
        setTimeout(() => {
          navigate('menu');
        }, 2000);
      }
    }
  }, [isAuthenticated, permissionsLoading, extendedUser, router, redirectDelayActive, syncStatus, navigate]);

  // Function to refresh authentication and check for new permissions
  const handleRefreshPermissions = async () => {
    setIsRefreshing(true);
    setSyncStatus('attempting');
    
    try {
      console.log("🔄 SYNC PAGE - Refreshing authentication to check for new permissions");
      
      // Reload the page to trigger auth check and get updated permissions
      window.location.reload();
    } catch (error) {
      console.error("❌ SYNC PAGE - Error refreshing permissions:", error);
      toast.error("Failed to refresh permissions. Please try again.");
      setSyncStatus('failed');
      setIsRefreshing(false);
    }
  };

  // Function to trigger P2P LAN sync
  const triggerLanSync = async () => {
    console.log('🌐 SYNC PAGE - Attempting to trigger P2P LAN sync');
    setSyncAttempted(true);
    setSyncStatus('attempting');
    
    try {
      // Try to trigger a LAN sync if available
      if (typeof window !== 'undefined' && (window as any).lanSync?.forceSyncNow) {
        await (window as any).lanSync.forceSyncNow();
        console.log("🔄 LAN sync request sent");
        toast.success("🔄 LAN sync triggered - checking for updates...");
        
        // Wait a moment then refresh permissions
        setTimeout(() => {
          handleRefreshPermissions();
        }, 3000);
      } else {
        console.log("⚠️ LAN sync not available in this context");
        toast.info("🌐 LAN sync not available. Try the QR code method or manual refresh.");
        setSyncStatus('failed');
      }
    } catch (error) {
      console.error("❌ SYNC PAGE - LAN sync error:", error);
      toast.error("LAN sync failed. Please try the QR code method.");
      setSyncStatus('failed');
    }
  };

  // Placeholder for QR scanning functionality
  const handleScanQrCode = () => {
    setIsScanQrDialogOpen(true);
    setSyncAttempted(true);
    setSyncStatus('attempting');
  };

  // Allow staff to proceed to limited menu even without permissions
  const proceedToLimitedMenu = () => {
    console.log('📱 SYNC PAGE - Proceeding to limited menu with sync options');
    toast.info("📱 Proceeding with limited access. You can try syncing again from the menu.");
    navigate('menu');
  };

  // Handle successful QR sync
  const handleQrSyncSuccess = () => {
    console.log("✅ SYNC PAGE - QR sync completed successfully");
    setSyncStatus('success');
    toast.success("🎉 QR sync completed! Refreshing permissions...");
    
    // Refresh permissions after successful sync
    setTimeout(() => {
      handleRefreshPermissions();
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-slate-800 border-slate-700 shadow-2xl">
        <CardHeader className="text-center space-y-2">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            {syncStatus === 'success' ? (
              <CheckCircle2 className="h-8 w-8 text-white" />
            ) : syncStatus === 'attempting' ? (
              <RefreshCw className="h-8 w-8 text-white animate-spin" />
            ) : (
              <Hourglass className="h-8 w-8 text-white" />
            )}
          </div>
          <CardTitle className="text-xl font-bold text-white">
            {syncStatus === 'success' ? 'Sync Successful!' : 'Waiting for Data Sync'}
          </CardTitle>
          <CardDescription className="text-slate-300">
            {syncStatus === 'success' 
              ? 'Your permissions have been synced. Redirecting...'
              : 'Your device needs to sync with the main terminal to access your permissions and data.'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {syncStatus !== 'success' && (
            <>
              <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-amber-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-slate-300">
                    <p className="font-medium text-slate-200 mb-1">Fresh Device Detected</p>
                    <p>This device hasn't synced with the restaurant's main system yet. Choose a sync method below to get your permissions and access the app.</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-slate-300 mb-2 flex items-center">
                  <Wifi className="h-5 w-5 mr-2 text-emerald-400" />
                  1. Automatic Network Sync
                </h3>
                <p className="text-sm text-slate-400 mb-3">
                  Connect automatically via the local network. Make sure you're on the same WiFi as the main terminal.
                </p>
                <Button 
                  onClick={triggerLanSync}
                  disabled={isRefreshing || syncStatus === 'attempting'}
                  className="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors duration-150"
                >
                  {syncStatus === 'attempting' ? (
                    <>
                      <RefreshCw className="h-5 w-5 animate-spin" />
                      <span>Syncing...</span>
                    </>
                  ) : (
                    <>
                      <Wifi className="h-5 w-5" />
                      <span>Try Network Sync</span>
                    </>
                  )}
                </Button>
              </div>

              <div className="border-t border-slate-700 my-4"></div>

              <div>
                <h3 className="text-lg font-semibold text-slate-300 mb-2 flex items-center">
                  <QrCode className="h-5 w-5 mr-2 text-sky-400" />
                  2. QR Code Sync
                </h3>
                <p className="text-sm text-slate-400 mb-3">
                  Scan a QR code from the main terminal to sync directly. Ask your manager to show the sync QR code.
                </p>
                <Button 
                  onClick={handleScanQrCode}
                  disabled={syncStatus === 'attempting'}
                  className="w-full bg-sky-500 hover:bg-sky-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors duration-150"
                >
                  <QrCode className="h-5 w-5" />
                  <span>Scan QR Code</span>
                </Button>
              </div>

              <div className="border-t border-slate-700 my-4"></div>

              <div>
                <h3 className="text-lg font-semibold text-slate-300 mb-2 flex items-center">
                  <RefreshCw className="h-5 w-5 mr-2 text-purple-400" />
                  3. Manual Refresh
                </h3>
                <p className="text-sm text-slate-400 mb-3">
                  If sync happened in the background, refresh to check for new permissions.
                </p>
                <Button 
                  onClick={handleRefreshPermissions}
                  disabled={isRefreshing || syncStatus === 'attempting'}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 font-semibold py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors duration-150"
                >
                  {isRefreshing ? (
                    <>
                      <RefreshCw className="h-5 w-5 animate-spin" />
                      <span>Refreshing...</span>
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-5 w-5" />
                      <span>Refresh Permissions</span>
                    </>
                  )}
                </Button>
              </div>

              <div className="border-t border-slate-700 my-4"></div>
              
              <div className="mt-4">
                <Button 
                  onClick={proceedToLimitedMenu}
                  variant="outline"
                  className="w-full border-amber-500 text-amber-400 hover:bg-amber-500 hover:text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors duration-150"
                >
                  <ArrowRight className="h-5 w-5" />
                  <span>Continue with Limited Access</span>
                </Button>
                <p className="text-xs text-amber-400 mt-2 text-center">
                  You can proceed with limited functionality and try syncing again later from the menu.
                </p>
              </div>
            </>
          )}

          {syncStatus === 'success' && (
            <div className="bg-emerald-900/30 border border-emerald-700 rounded-lg p-4 text-center">
              <CheckCircle2 className="h-8 w-8 text-emerald-400 mx-auto mb-2" />
              <p className="text-emerald-300 font-medium">Sync completed successfully!</p>
              <p className="text-emerald-400 text-sm mt-1">Redirecting to menu...</p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {isScanQrDialogOpen && (
        <OfflineConnectionDialog
          defaultOpen={true}
        />
      )}
    </div>
  );
} 