'use client';

import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { SimpleStaffForm } from './SimpleStaffForm';

interface StaffFormDialogProps {
  onStaffCreated?: () => void;
  trigger?: React.ReactNode;
}

export function StaffFormDialog({ onStaffCreated, trigger }: StaffFormDialogProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);

  const handleSuccess = (staffId: string) => {
    setIsOpen(false);
    toast({
      title: "Staff created",
      description: "New staff member has been added successfully",
    });
    
    if (onStaffCreated) {
      onStaffCreated();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Staff
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Create New Staff Member</DialogTitle>
        </DialogHeader>
        <SimpleStaffForm 
          onSuccess={handleSuccess}
          onCancel={() => setIsOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
