"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from '@/lib/utils/currency';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getSessionSummary,
  closeSessionWithCount
} from '@/lib/db/v4/operations/cash-session-ops';
import { createCashTransaction } from '@/lib/db/v4/operations/cash-ops';

export default function CaisseTestPage() {
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p>This page is only available in development mode.</p>
        </div>
      </div>
    );
  }
  const { toast } = useToast();
  const { user } = useAuth();
  const { isReady } = useUnifiedDB();
  
  const [sessionSummary, setSessionSummary] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Test inputs
  const [saleAmount, setSaleAmount] = useState('');
  const [manualAmount, setManualAmount] = useState('');
  const [countAmount, setCountAmount] = useState('');

  const loadSessionSummary = async () => {
    if (!isReady) return;
    
    try {
      setIsLoading(true);
      const summary = await getSessionSummary();
      setSessionSummary(summary);
      console.log('🔧 Session Summary:', summary);
    } catch (error) {
      console.error('Error loading session:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger l'état de la caisse",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSale = async () => {
    if (!user || !saleAmount) return;
    
    try {
      setIsLoading(true);
      await createCashTransaction({
        type: 'sales',
        amount: parseFloat(saleAmount),
        description: `Test sale - ${formatCurrency(parseFloat(saleAmount))}`,
        time: new Date().toISOString(),
        performedBy: user.name || 'Test User',
        metadata: {
          transactionCategory: 'test_sale',
          testMode: true
        }
      });
      
      toast({
        title: "✅ Vente enregistrée",
        description: `Montant: ${formatCurrency(parseFloat(saleAmount))}`
      });
      
      setSaleAmount('');
      await loadSessionSummary();
    } catch (error) {
      console.error('Error creating sale:', error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Erreur de vente",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualTransaction = async (type: 'manual_in' | 'manual_out') => {
    if (!user || !manualAmount) return;
    
    try {
      setIsLoading(true);
      const amount = type === 'manual_out' ? -Math.abs(parseFloat(manualAmount)) : parseFloat(manualAmount);
      
      await createCashTransaction({
        type,
        amount,
        description: `Test ${type} - ${formatCurrency(Math.abs(amount))}`,
        time: new Date().toISOString(),
        performedBy: user.name || 'Test User',
        metadata: {
          transactionCategory: `test_${type}`,
          testMode: true
        }
      });
      
      toast({
        title: `✅ ${type === 'manual_in' ? 'Dépôt' : 'Retrait'} enregistré`,
        description: `Montant: ${formatCurrency(Math.abs(amount))}`
      });
      
      setManualAmount('');
      await loadSessionSummary();
    } catch (error) {
      console.error('Error creating manual transaction:', error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Erreur de transaction",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCount = async () => {
    if (!user || !countAmount || !sessionSummary?.isActive) return;
    
    try {
      setIsLoading(true);
      const result = await closeSessionWithCount({
        countedAmount: parseFloat(countAmount),
        countedBy: user.name || 'Test User',
        notes: 'Test counting'
      });
      
      toast({
        title: "✅ Session fermée",
        description: `Comptage: ${formatCurrency(parseFloat(countAmount))} • Écart: ${formatCurrency(result.countRecord.variance)} • Nouvelle session créée`
      });
      
      setCountAmount('');
      await loadSessionSummary();
    } catch (error) {
      console.error('Error counting:', error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Erreur de comptage",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isReady) {
      loadSessionSummary();
    }
  }, [isReady]);

  if (!isReady) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Chargement de la base de données...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">🧪 Test Système Caisse</h1>
        <Button onClick={loadSessionSummary} disabled={isLoading}>
          Actualiser
        </Button>
      </div>

      {/* Current State */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            État Actuel
            {sessionSummary?.isActive ? (
              <Badge variant="default" className="bg-green-600">Session Active</Badge>
            ) : (
              <Badge variant="secondary">En Attente</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sessionSummary ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Montant Tiroir</div>
                <div className="text-2xl font-bold">{formatCurrency(sessionSummary.drawerAmount)}</div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">Montant Attendu</div>
                <div className="text-2xl font-bold">{formatCurrency(sessionSummary.expectedAmount)}</div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">Ouverture Session</div>
                <div className="text-lg">{formatCurrency(sessionSummary.sessionStartAmount)}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Ventes Session</div>
                <div className="text-lg">{formatCurrency(sessionSummary.sessionSalesAmount)}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Manuel Session</div>
                <div className="text-lg">{formatCurrency(sessionSummary.sessionManualAmount)}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Transactions</div>
                <div className="text-lg">{sessionSummary.transactionCount}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Session ID</div>
                <div className="text-xs font-mono">{sessionSummary.session._id}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Status</div>
                <div className="text-sm">{sessionSummary.isActive ? '🟢 Active' : '🟡 En attente'}</div>
              </div>
            </div>
          ) : (
            <div>Chargement...</div>
          )}
        </CardContent>
      </Card>

      {/* Test Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Sale */}
        <Card>
          <CardHeader>
            <CardTitle>1. Créer Vente (Active la Session)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="sale-amount">Montant Vente</Label>
              <Input
                id="sale-amount"
                type="number"
                value={saleAmount}
                onChange={(e) => setSaleAmount(e.target.value)}
                placeholder="ex: 1500"
              />
            </div>
            <Button 
              onClick={handleSale} 
              disabled={isLoading || !saleAmount}
              className="w-full"
            >
              Créer Vente
            </Button>
            <p className="text-xs text-muted-foreground">
              💡 La première vente active automatiquement la session
            </p>
          </CardContent>
        </Card>

        {/* Manual Transactions */}
        <Card>
          <CardHeader>
            <CardTitle>2. Transaction Manuelle</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="manual-amount">Montant</Label>
              <Input
                id="manual-amount"
                type="number"
                value={manualAmount}
                onChange={(e) => setManualAmount(e.target.value)}
                placeholder="ex: 500"
              />
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={() => handleManualTransaction('manual_in')} 
                disabled={isLoading || !manualAmount}
                variant="outline"
                className="flex-1"
              >
                Dépôt
              </Button>
              <Button 
                onClick={() => handleManualTransaction('manual_out')} 
                disabled={isLoading || !manualAmount}
                variant="outline"
                className="flex-1"
              >
                Retrait
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Count */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>3. Compter & Fermer Session</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="count-amount">Montant Compté</Label>
              <Input
                id="count-amount"
                type="number"
                value={countAmount}
                onChange={(e) => setCountAmount(e.target.value)}
                placeholder={sessionSummary?.expectedAmount ? sessionSummary.expectedAmount.toString() : "ex: 6500"}
              />
            </div>
            <Button 
              onClick={handleCount} 
              disabled={isLoading || !countAmount || !sessionSummary?.isActive}
              className="w-full"
            >
              {sessionSummary?.isActive ? 'Compter & Fermer Session' : 'Session non active - effectuez une vente d\'abord'}
            </Button>
            <p className="text-xs text-muted-foreground">
              💡 Ferme la session actuelle et crée automatiquement une nouvelle session avec le montant compté
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>🔧 Debug Info</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-muted p-4 rounded overflow-auto">
            {JSON.stringify(sessionSummary, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
} 