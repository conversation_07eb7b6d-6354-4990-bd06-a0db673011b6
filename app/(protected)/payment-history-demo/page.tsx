'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import NEW payment history components
import NewPaymentHistory from '@/components/payment/NewPaymentHistory';
import SimplePaymentHistory from '@/components/payment/SimplePaymentHistory';

// Import database functions
import { getAllStaff, initializeV4Database } from '@/lib/db/v4';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import type { StaffDocument } from '@/lib/db/v4/schemas/per-staff-schemas';

export default function PaymentHistoryDemoPage() {
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const [staff, setStaff] = useState<StaffDocument[]>([]);
  const [selectedStaffId, setSelectedStaffId] = useState<string>('');
  const [dbInitialized, setDbInitialized] = useState(false);
  const [initializingDb, setInitializingDb] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user) {
      initializeDatabase();
    }
  }, [isAuthenticated, user]);

  const initializeDatabase = async () => {
    if (initializingDb) return;
    
    setInitializingDb(true);
    try {
      // Get restaurant ID from auth
      const restaurantId = user?.restaurantId || getCurrentRestaurantId();
      
      if (!restaurantId) {
        throw new Error('No restaurant ID found in authentication data');
      }

      console.log('🔧 Initializing database with restaurant ID:', restaurantId);
      
      // Initialize the V4 database
      await initializeV4Database(restaurantId);
      
      // Add a small delay to ensure database is fully ready
      await new Promise(resolve => setTimeout(resolve, 100));
      
      setDbInitialized(true);

      // Load staff after database is ready
      await loadStaff();
      
      toast({
        title: "✅ Database Initialized",
        description: "Payment history demo ready",
      });
    } catch (error) {
      console.error('Error initializing database:', error);
      toast({
        title: "❌ Database Initialization Failed",
        description: `Failed to initialize database: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setInitializingDb(false);
    }
  };

  const loadStaff = async () => {
    if (!dbInitialized) {
      console.log('Database not initialized, skipping staff load');
      return;
    }
    
    try {
      const staffList = await getAllStaff();
      setStaff(staffList);
      if (staffList.length > 0) {
        setSelectedStaffId(staffList[0].id);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
      toast({
        title: "❌ Error Loading Staff",
        description: "Failed to load staff list",
        variant: "destructive",
      });
    }
  };

  const selectedStaff = staff.find(s => s.id === selectedStaffId);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">💳 Payment History Component Demo</h1>
          <p className="text-muted-foreground">Showcase of the NEW payment history components</p>
        </div>
        <Badge variant={dbInitialized ? "default" : "secondary"}>
          {dbInitialized ? "✅ Database Ready" : initializingDb ? "⏳ Initializing..." : "❌ Not Initialized"}
        </Badge>
      </div>

      {!isAuthenticated && (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">Please log in to view the payment history demo</p>
          </CardContent>
        </Card>
      )}

      {isAuthenticated && !dbInitialized && (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center gap-2">
              <p className="text-muted-foreground">
                {initializingDb ? "Initializing database..." : "Database not initialized"}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {isAuthenticated && dbInitialized && (
        <>
          {/* Staff Selection */}
          <Card>
            <CardHeader>
              <CardTitle>👤 Select Staff Member</CardTitle>
            </CardHeader>
            <CardContent>
              <select 
                value={selectedStaffId} 
                onChange={(e) => setSelectedStaffId(e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="">Select a staff member...</option>
                {staff.map(s => (
                  <option key={s.id} value={s.id}>{s.name} - {s.role}</option>
                ))}
              </select>
              {selectedStaff && (
                <div className="mt-2 text-sm text-muted-foreground">
                  Selected: {selectedStaff.name} ({selectedStaff.role})
                </div>
              )}
            </CardContent>
          </Card>

          {selectedStaffId && (
            <Tabs defaultValue="full" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="full">Full Component</TabsTrigger>
                <TabsTrigger value="simple">Simple Component</TabsTrigger>
                <TabsTrigger value="comparison">Side by Side</TabsTrigger>
              </TabsList>

              <TabsContent value="full" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>🎯 Full Payment History Component</CardTitle>
                    <CardDescription>
                      Complete payment history with analytics, filtering, and export functionality
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <NewPaymentHistory 
                      staffId={selectedStaffId}
                      showAnalytics={true}
                      maxHeight="600px"
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="simple" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>⚡ Simple Payment History Component</CardTitle>
                    <CardDescription>
                      Lightweight payment history for embedding in other pages
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <SimplePaymentHistory 
                      staffId={selectedStaffId}
                      maxItems={5}
                      showExport={true}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="comparison" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>🎯 Full Component</CardTitle>
                      <CardDescription>
                        With analytics and filtering
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <NewPaymentHistory 
                        staffId={selectedStaffId}
                        showAnalytics={true}
                        maxHeight="400px"
                      />
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>⚡ Simple Component</CardTitle>
                      <CardDescription>
                        Lightweight and compact
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <SimplePaymentHistory 
                        staffId={selectedStaffId}
                        maxItems={8}
                        showExport={false}
                      />
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          )}

          {/* Component Features */}
          <Card>
            <CardHeader>
              <CardTitle>✨ Component Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">🎯 Full Component Features:</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Payment analytics dashboard</li>
                    <li>• Advanced filtering (date, amount, search)</li>
                    <li>• CSV export functionality</li>
                    <li>• Expandable payment details</li>
                    <li>• Responsive design</li>
                    <li>• Currency formatting</li>
                    <li>• Payment status indicators</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">⚡ Simple Component Features:</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Compact payment display</li>
                    <li>• Show/hide details toggle</li>
                    <li>• Configurable item limit</li>
                    <li>• Optional export button</li>
                    <li>• Minimal space usage</li>
                    <li>• Quick payment overview</li>
                    <li>• Easy integration</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage Examples */}
          <Card>
            <CardHeader>
              <CardTitle>📝 Usage Examples</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Full Component:</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`<NewPaymentHistory 
  staffId={staffId}
  showAnalytics={true}
  maxHeight="600px"
/>`}
                  </pre>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Simple Component:</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`<SimplePaymentHistory 
  staffId={staffId}
  maxItems={5}
  showExport={false}
/>`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
