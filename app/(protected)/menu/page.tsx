"use client";

import { useState } from 'react';
import dynamic from 'next/dynamic';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { Loader2 } from 'lucide-react';

// Import MenuManagement component dynamically to avoid SSR issues
// Use type assertion to help TypeScript understand the component props
const MenuManagement = dynamic(
  () => import('../../components/MenuManagement').then(mod => ({ default: mod.MenuManagement })),
  { ssr: false }
) as any; // Using 'any' here to bypass the type checking for the dynamic import

export default function MenuPage() {
  return <MenuManagement />;
}