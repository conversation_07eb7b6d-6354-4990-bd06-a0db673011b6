"use client";

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { EditOrderProvider } from '@/components/providers/EditOrderContext';
import EnhancedMobileOrderingInterface from '@/app/components/EnhancedMobileOrderingInterface';
import WaiterOrderList from '@/app/components/WaiterOrderList';
import { cn } from '@/lib/utils';
import { usePermissions } from '@/lib/hooks/use-permissions';

// Android-safe error boundary component
const AndroidSafeTabContent = ({ 
  children, 
  tabName 
}: { 
  children: React.ReactNode; 
  tabName: string; 
}) => {
  const [hasError, setHasError] = useState(false);
  const [errorInfo, setErrorInfo] = useState<string>('');

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error(`Error in ${tabName} tab:`, error);
      setHasError(true);
      setErrorInfo(error.message || 'Unknown error');
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error(`Unhandled promise rejection in ${tabName} tab:`, event.reason);
      setHasError(true);
      setErrorInfo(event.reason?.message || 'Promise rejection');
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [tabName]);

  const resetError = useCallback(() => {
    setHasError(false);
    setErrorInfo('');
  }, []);

  if (hasError) {
    return (
      <div className="flex items-center justify-center min-h-[200px] p-6">
        <div className="text-center space-y-4">
          <div className="text-2xl">⚠️</div>
          <div>
            <h3 className="font-semibold">Erreur dans l'onglet {tabName}</h3>
            <p className="text-muted-foreground text-sm mt-1">{errorInfo}</p>
          </div>
          <Button onClick={resetError} variant="outline" size="sm">
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default function WaiterPage() {
  const { isAuthenticated, loading, user } = useAuth();
  const { isOwner, hasPageAccess, isLoading: permsLoading } = usePermissions();
  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [tab, setTab] = useState('waiter');
  const [isAndroid, setIsAndroid] = useState(false);

  // Detect Android environment
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isAndroidDevice = /Android/i.test(navigator.userAgent);
      const isCapacitor = !!(window as any).Capacitor;
      setIsAndroid(isAndroidDevice || isCapacitor);
      
      if (isAndroidDevice || isCapacitor) {
        console.log('🤖 Android environment detected, applying mobile optimizations');
      }
    }
  }, []);

  // Android-safe tab switching with memory management
  const handleTabChange = useCallback((newTab: string) => {
    try {
      console.log(`🔄 Switching from ${tab} to ${newTab}`);
      
      // Android memory management: small delay to allow cleanup
      if (isAndroid && tab !== newTab) {
        setTimeout(() => {
          setTab(newTab);
        }, 50);
      } else {
        setTab(newTab);
      }
    } catch (error) {
      console.error('Error switching tabs:', error);
      // Fallback to direct tab change
      setTab(newTab);
    }
  }, [tab, isAndroid]);

  // Debug logging
  useEffect(() => {
    console.log('[WaiterPage] Auth state:', {
      isAuthenticated,
      loading,
      user: user ? {
        id: user.id,
        name: user.name,
        restaurantId: user.restaurantId,
        role: user.role
      } : null
    });
  }, [isAuthenticated, loading, user]);

  useEffect(() => {
    // Redirect to login if not authenticated and auth check is complete
    if (!loading && !isAuthenticated) {
      navigate('auth');
    }
  }, [isAuthenticated, loading, navigate]);

  // Show loading until auth and permissions are determined
  if (loading || permsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Only render the page when authenticated and has waiter/orders access (owner bypass)
  if (!isAuthenticated) return null;

  const canUseWaiter = isOwner || hasPageAccess('orders') || hasPageAccess('menu');
  if (!canUseWaiter) {
    return (
      <div className="flex items-center justify-center min-h-screen p-6 text-center">
        <div className="max-w-sm space-y-3">
          <div className="text-3xl">🔒</div>
          <div className="font-semibold">Accès refusé</div>
          <div className="text-muted-foreground text-sm">
            Votre compte ne dispose pas des permissions nécessaires pour accéder au poste serveur.
          </div>
        </div>
      </div>
    );
  }

  return (
    <EditOrderProvider>
      <div className="h-screen flex flex-col bg-background">
        <Tabs value={tab} onValueChange={handleTabChange} className="flex-grow flex flex-col">
          {/* Compact Navigation Header */}
          <div className="flex-shrink-0 bg-background border-b sticky top-0 z-50">
            <div className="safe-area-inset-top px-3 py-1.5">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant={tab === 'waiter' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleTabChange('waiter')}
                    className="h-7 px-2 text-xs font-medium rounded-md"
                  >
                    <span className="text-sm mr-1">👨‍🍳</span>
                    POS
                  </Button>
                  <Button
                    variant={tab === 'orders' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleTabChange('orders')}
                    className="h-7 px-2 text-xs font-medium rounded-md"
                  >
                    <span className="text-sm mr-1">📋</span>
                    Commandes
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <TabsContent value="waiter" className="flex-grow overflow-hidden min-h-0 m-0 p-0">
            <AndroidSafeTabContent tabName="POS">
              <EnhancedMobileOrderingInterface />
            </AndroidSafeTabContent>
          </TabsContent>
          
          <TabsContent value="orders" className="flex-grow overflow-hidden min-h-0 m-0 p-0 pb-safe">
            <AndroidSafeTabContent tabName="Commandes">
              <WaiterOrderList onEditOrder={(order) => {
                handleTabChange('waiter');
              }} />
            </AndroidSafeTabContent>
          </TabsContent>
        </Tabs>
      </div>
    </EditOrderProvider>
  );
} 