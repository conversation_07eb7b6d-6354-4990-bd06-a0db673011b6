'use client';

import { AppSidebar } from "@/components/app-sidebar";
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SidebarInset, SidebarTrigger, SidebarProvider } from "@/components/ui/sidebar";
import { Menu } from "lucide-react";
import FloatingDebugButton from "@/components/debug/FloatingDebugButton";
import { useMobileLayout } from '@/hooks/use-mobile-layout';
import { cn } from '@/lib/utils';

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isMobile, safeAreaInsets } = useMobileLayout();

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-h-0">
          {/* Improved Mobile Header */}
          <header 
            className={cn(
              "sticky top-0 z-30 flex items-center gap-4 border-b bg-background/95 backdrop-blur-sm",
              isMobile ? "h-16 px-4" : "h-14 px-6 lg:hidden",
              "safe-top"
            )}
            style={{
              paddingTop: isMobile ? `max(${safeAreaInsets.top}px, 8px)` : undefined
            }}
          >
            <SidebarTrigger 
              className={cn(
                "transition-colors",
                isMobile 
                  ? "h-10 w-10 rounded-lg bg-primary/10 hover:bg-primary/20 text-primary border-0" 
                  : "lg:hidden"
              )}
            >
              <Menu className={cn(isMobile ? "h-5 w-5" : "h-6 w-6")} />
              <span className="sr-only">Toggle Menu</span>
            </SidebarTrigger>
            
            <div className="flex-1 text-center">
              <h1 className={cn(
                "font-semibold text-foreground",
                isMobile ? "text-lg" : "text-lg"
              )}>
                Restaurant Manager
              </h1>
            </div>
            
            {/* Space for future header actions */}
            {isMobile && <div className="w-10" />}
          </header>

          {/* Main Content */}
          <main 
            className={cn(
              "flex-1 flex flex-col min-h-0 bg-background",
              isMobile ? "overflow-auto" : ""
            )}
            style={{
              paddingBottom: isMobile ? `max(${safeAreaInsets.bottom}px, 16px)` : undefined
            }}
          >
            <div className={cn(
              "flex-1 flex flex-col min-h-0",
              isMobile ? "p-4 gap-4" : "p-4 sm:p-6 gap-4"
            )}>
              <ProtectedRoute>
                {children}
              </ProtectedRoute>
            </div>
          </main>
          
          <FloatingDebugButton />
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}