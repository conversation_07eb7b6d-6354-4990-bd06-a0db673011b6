"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function OrdersPage() {
  const router = useRouter();
  
  useEffect(() => {
    // Redirect to the new ordering page
    router.replace("/ordering");
  }, [router]);
  
  return (
    <div className="container mx-auto py-6 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Redirecting...</h1>
        <p className="text-muted-foreground">
          The Orders page has moved to a new location.
        </p>
      </div>
    </div>
  );
} 