"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeftIcon } from 'lucide-react'
import Link from 'next/link'
import DailySnapshot from '@/app/components/finance/DailySnapshot'
import { useToast } from '@/components/ui/use-toast'

export default function DailySnapshotPage() {
  const { toast } = useToast()
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  
  const handleDateChange = (date: Date) => {
    setSelectedDate(date)
  }
  
  return (
    <div className="container max-w-full px-4 py-4">
      {/* Header with back button and title */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/finance">
            <Button variant="outline" size="icon">
              <ArrowLeftIcon className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Daily Financial Snapshot</h1>
            <p className="text-muted-foreground">
              View a comprehensive summary of your daily financial activity
            </p>
          </div>
        </div>
      </div>
      
      {/* Daily Snapshot Component */}
      <DailySnapshot 
        date={selectedDate} 
        onDateChange={handleDateChange} 
      />
    </div>
  )
}
