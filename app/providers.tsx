'use client';

import React from 'react';
import { MultiUserAuthProvider } from '@/lib/context/multi-user-auth-provider';
import { UnifiedDBProvider } from '@/lib/context/unified-db-provider';
import { SettingsProvider } from '@/lib/context/settings-context';
import { ToastProvider } from '@/components/ui/use-toast';
import { ThemeProvider } from '@/components/theme-provider';
import { PlatformProvider } from '@/lib/context/platform-context';
import { DatabaseLoadingOverlay } from '@/components/DatabaseLoadingOverlay';
import { AutonomousSyncProvider } from '@/lib/context/autonomous-sync-provider';
import { AutoPrintProvider } from '@/components/providers/AutoPrintProvider';

// Electron API detection and logging setup
if (typeof window !== 'undefined') {
  // Check if we're in Electron
  const isElectron = !!(window as any).electronAPI;
  
  if (isElectron) {
    console.log('🖥️ Running in Electron environment');
    
    // Override console methods to send logs to main process if available
    if ((window as any).electronAPI?.logToMain) {
      const originalLog = console.log;
      const originalWarn = console.warn;
      const originalError = console.error;
      
      console.log = (...args) => {
        originalLog(...args);
        (window as any).electronAPI.logToMain('log', args.join(' '));
      };
      
      console.warn = (...args) => {
        originalWarn(...args);
        (window as any).electronAPI.logToMain('warn', args.join(' '));
      };
      
      console.error = (...args) => {
        originalError(...args);
        (window as any).electronAPI.logToMain('error', args.join(' '));
      };
    } else {
      console.log('[Console Override] window.electronAPI.logToMain not found. Logs will not be sent to terminal.');
    }
  } else {
    console.log('🌐 Running in web browser environment');
  }

  // Only override fetch for static builds, not Electron
  if (process.env.BUILD_TARGET === 'static' && !isElectron) {
    console.log('[API Fallback] Running in static mode - overriding fetch for offline responses');
    
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
      const isInternalApi = url.startsWith('/api/');
      
      if (isInternalApi) {
        console.log(`[API Fallback] Static build - returning offline response for: ${url}`);
        
        // Return appropriate offline responses for different endpoints
        if (url.includes('/api/health')) {
          return new Response(JSON.stringify({ status: 'offline' }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        
        if (url.includes('/api/auth/')) {
          return new Response(JSON.stringify({ error: 'Server unavailable in static mode' }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        
        // Default offline response
        return new Response(JSON.stringify({ error: 'API unavailable in static mode' }), {
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // For non-API requests, use original fetch
      return originalFetch(input, init);
    };
  }
}

export function Providers({ children }: { children: React.ReactNode }) {
  // Detect if we're in Electron environment
  const isElectron = typeof window !== 'undefined' && !!(window as any).electronAPI;

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange
      storageKey="bistro-theme"
      // Disable theme script injection for Electron to prevent forEach errors
      enableColorScheme={!isElectron}
      nonce={undefined}
    >
      <ToastProvider>
        <PlatformProvider>
          <DatabaseLoadingOverlay>
            <MultiUserAuthProvider>
              <UnifiedDBProvider>
                <AutonomousSyncProvider>
                  <AutoPrintProvider>
                    <SettingsProvider>
                      {children}
                    </SettingsProvider>
                  </AutoPrintProvider>
                </AutonomousSyncProvider>
              </UnifiedDBProvider>
            </MultiUserAuthProvider>
          </DatabaseLoadingOverlay>
        </PlatformProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}