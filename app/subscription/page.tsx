"use client";

import { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useToast } from "@/components/ui/use-toast";
import { useStaticNavigation } from '@/lib/utils/navigation';

export default function SubscriptionPage() {
  const [loading, setLoading] = useState(true);
  const [userName, setUserName] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();
  const { navigate } = useStaticNavigation();

  useEffect(() => {
    // Try to get user info from localStorage
    try {
      const authData = localStorage.getItem('auth_data');
      if (authData) {
        const parsedData = JSON.parse(authData);
        if (parsedData.user?.name) {
          setUserName(parsedData.user.name);
        }
      }
    } catch (e) {
      console.error('Error parsing auth data', e);
    }
    
    setLoading(false);
  }, []);

  const handleContactSupport = () => {
    toast({
      title: "Demande de support envoyée",
      description: "Nous vous contacterons bientôt au sujet de votre abonnement.",
      duration: 5000,
    });
  };

  const handleLogout = () => {
    // Clear auth data
    localStorage.removeItem('auth_data');
    localStorage.removeItem('auth_token');
    
    // Redirect to login
    router.push('/auth');
    navigate('auth');
  };

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Chargement...</div>;
  }

  return (
    <div className="container max-w-6xl mx-auto py-8 px-4">
      <Card className="max-w-3xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto bg-red-100 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold">
            Accès au compte restreint
          </CardTitle>
          <CardDescription className="text-lg mt-2">
            {userName ? `Bonjour ${userName},` : 'Bonjour,'} l'accès à votre compte a été temporairement restreint.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
              <p className="text-amber-800">
                Votre abonnement a peut-être expiré ou a été mis en pause. Veuillez contacter le support pour rétablir l'accès complet à la plateforme.
              </p>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="font-medium text-lg">Ce que cela signifie :</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <div className="bg-red-100 p-1 rounded-full mr-2 mt-0.5">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                </div>
                <span>Vous ne pouvez actuellement pas accéder aux fonctionnalités de la plateforme</span>
              </li>
              <li className="flex items-start">
                <div className="bg-amber-100 p-1 rounded-full mr-2 mt-0.5">
                  <Info className="h-4 w-4 text-amber-600" />
                </div>
                <span>Tous les comptes du personnel liés à votre restaurant sont également affectés</span>
              </li>
              <li className="flex items-start">
                <div className="bg-green-100 p-1 rounded-full mr-2 mt-0.5">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <span>Vos données sont toujours en sécurité et seront disponibles une fois l'accès rétabli</span>
              </li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="default" onClick={handleContactSupport}>
            Contacter le support
          </Button>
          <Button variant="outline" onClick={handleLogout}>
            Déconnexion
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 