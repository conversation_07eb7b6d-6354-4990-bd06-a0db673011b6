'use client';

// Fonts are now loaded in the main layout for better static export compatibility

export default function LandingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="landing-page" dir="rtl">
      <style jsx global>{`
        .landing-page h1 {
          font-family: var(--font-changa), sans-serif;
          font-weight: 800;
          letter-spacing: -0.02em;
        }

        .landing-page h2 {
          font-family: var(--font-changa), sans-serif;
          font-weight: 700;
        }

        .landing-page {
          font-family: var(--font-almarai), sans-serif;
        }

        .landing-page .body-text {
          font-family: var(--font-tajawal), sans-serif;
          line-height: 1.8;
        }
      `}</style>
      {children}
    </div>
  );
}