'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";

export default function InstallGuidePage() {
  return (
    <div className="container max-w-4xl py-10">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Install Restaurant Manager App</CardTitle>
          <CardDescription>
            Follow these steps to install the app on your device
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">For iPhone/iPad (iOS):</h2>
            <ol className="list-decimal pl-6 space-y-4">
              <li>
                <p>Open this website in <strong>Safari</strong> browser.</p>
                <p className="text-sm text-muted-foreground">Note: Other browsers on iOS will not work.</p>
              </li>
              <li>
                <p>Tap the share icon <span className="inline-block rounded-md bg-gray-100 px-2 py-1">↑</span> at the bottom of the screen.</p>
              </li>
              <li>
                <p>Scroll down and tap <strong>"Add to Home Screen"</strong>.</p>
              </li>
              <li>
                <p>Tap <strong>"Add"</strong> in the top right corner.</p>
              </li>
            </ol>
          </div>
          
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">For Android:</h2>
            <ol className="list-decimal pl-6 space-y-4">
              <li>
                <p>Open this website in <strong>Chrome</strong> browser.</p>
              </li>
              <li>
                <p>Tap the menu icon (three dots) in the top right corner.</p>
              </li>
              <li>
                <p>Tap <strong>"Add to Home screen"</strong> or <strong>"Install app"</strong>.</p>
              </li>
              <li>
                <p>Follow the on-screen instructions to complete installation.</p>
              </li>
            </ol>
          </div>
          
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">For Desktop (Chrome, Edge):</h2>
            <ol className="list-decimal pl-6 space-y-4">
              <li>
                <p>Look for the install icon <span className="inline-block rounded-md bg-gray-100 px-2 py-1">⊕</span> in the address bar.</p>
              </li>
              <li>
                <p>Click on it and select <strong>"Install"</strong>.</p>
              </li>
            </ol>
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/">Return to App</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 