import { useState, useEffect, useCallback } from 'react';
import { PeerInfo, SyncStatus } from '../../types/p2p-sync';

// Define a type for the p2pSync API to match the one in global.d.ts
type P2PSyncAPI = {
  getPeers: () => Promise<PeerInfo[]>;
  getSyncStatus: () => Promise<SyncStatus[]>;
  getMdnsStatus: () => Promise<'not_running' | 'running' | 'error'>;
  getSystemId: () => Promise<string>;
  getServerPort: () => Promise<number>;
  getServiceInfo: () => Promise<any>;
  getMdnsInfo: () => Promise<{
    published: boolean;
    name?: string;
    type?: string;
    port?: number;
    diagnostics?: any;
  }>;
  getMdnsDiagnostics: () => Promise<any>;
  restartMdns: () => Promise<{
    success: boolean;
    diagnostics?: any;
    error?: string;
  }>;
  resetMdns: () => Promise<{ success: boolean; error?: string }>;
  startSync: (peerId: string, dbName: string, direction?: 'push' | 'pull' | 'both') => Promise<any>;
  stopSync: (peerId: string, dbName: string) => Promise<any>;
  stopAllSyncsWithPeer: (peerId: string) => Promise<any>;
  onPeerDiscovered: (callback: (peerInfo: PeerInfo) => void) => void;
  onPeerLost: (callback: (peerId: string) => void) => void;
  onSyncStatusUpdated: (callback: (status: SyncStatus) => void) => void;
};

export function useP2PSync() {
  const [peers, setPeers] = useState<PeerInfo[]>([]);
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  const [mdnsStatus, setMdnsStatus] = useState<'not_running' | 'running' | 'error'>('not_running');
  const [systemId, setSystemId] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [mdnsDiagnostics, setMdnsDiagnostics] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Check if we're in the Electron app
  const isElectronApp = typeof window !== 'undefined' && window.IS_DESKTOP_APP;
  
  useEffect(() => {
    async function fetchInitialData() {
      if (!isElectronApp || !window.electronAPI?.p2pSync) {
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        
        const p2pSync = window.electronAPI.p2pSync as P2PSyncAPI;
        
        // Fetch all data in parallel
        const [peersResult, syncStatusesResult, mdnsStatusResult, systemIdResult, diagnosticsResult] = await Promise.all([
          p2pSync.getPeers(),
          p2pSync.getSyncStatus(),
          p2pSync.getMdnsStatus?.() || 'not_running',
          p2pSync.getSystemId?.() || '',
          (p2pSync.getMdnsDiagnostics?.() || Promise.resolve(null)).catch((err: Error) => {
            console.warn('[P2PSync] Error fetching mDNS diagnostics (may not be available):', err);
            return null;
          })
        ]);
        
        // Update state with fetched data
        setPeers(peersResult || []);
        setSyncStatuses(syncStatusesResult || []);
        setMdnsStatus(mdnsStatusResult || 'not_running');
        setSystemId(systemIdResult || '');
        setMdnsDiagnostics(diagnosticsResult);
        
        console.log('[P2PSync] Initial data fetched:', {
          peersResult,
          syncStatusesResult,
          mdnsStatusResult,
          systemId: systemIdResult?.substring(0, 10) + '...'
        });
        
        setIsLoading(false);
        setError(null);
      } catch (error) {
        console.error('[P2PSync] Error fetching initial data:', error);
        setError(String(error));
        setIsLoading(false);
      }
    }
    
    fetchInitialData();
    
    // Set up polling for updated data every 5 seconds
    const intervalId = setInterval(() => {
      if (isElectronApp && window.electronAPI?.p2pSync) {
        const p2pSync = window.electronAPI.p2pSync as P2PSyncAPI;
        
        // Just poll for mDNS status and diagnostics
        Promise.all([
          p2pSync.getMdnsStatus?.() || Promise.resolve('not_running'),
          (p2pSync.getMdnsDiagnostics?.() || Promise.resolve(null)).catch(() => null)
        ]).then(([status, diagnostics]) => {
          setMdnsStatus(status as 'not_running' | 'running' | 'error');
          console.log('[P2PSync] Got mDNS status:', status);
          if (diagnostics) {
            setMdnsDiagnostics(diagnostics);
          }
        }).catch(err => {
          console.error('[P2PSync] Error polling for status:', err);
        });
      }
    }, 5000);
    
    return () => clearInterval(intervalId);
  }, [isElectronApp]);
  
  // Function to restart mDNS service
  const restartMdnsService = useCallback(async () => {
    if (!isElectronApp || !window.electronAPI?.p2pSync) {
      return { success: false, error: 'Not in Electron environment' };
    }
    
    try {
      const p2pSync = window.electronAPI.p2pSync as P2PSyncAPI;
      
      // Try to use the new restart method first
      if (typeof p2pSync.restartMdns === 'function') {
        const result = await p2pSync.restartMdns();
        console.log('[P2PSync] Restart mDNS result:', result);
        
        // Update our local state with the new diagnostics if available
        if (result.diagnostics) {
          setMdnsDiagnostics(result.diagnostics);
        }
        
        // Re-fetch mDNS status
        const newStatus = await p2pSync.getMdnsStatus?.();
        if (newStatus) {
          setMdnsStatus(newStatus);
        }
        
        return result;
      } else {
        // Fall back to older reset method
        const result = await p2pSync.resetMdns();
        console.log('[P2PSync] Reset mDNS result (legacy):', result);
        return result;
      }
    } catch (error) {
      console.error('[P2PSync] Error restarting mDNS service:', error);
      return { success: false, error: String(error) };
    }
  }, [isElectronApp]);
  
  // Function to get mDNS service info
  const getMdnsInfo = useCallback(async () => {
    if (!isElectronApp || !window.electronAPI?.p2pSync) {
      return { published: false, error: 'Not in Electron environment' };
    }
    
    try {
      const p2pSync = window.electronAPI.p2pSync as P2PSyncAPI;
      const info = await p2pSync.getMdnsInfo();
      return info;
    } catch (error) {
      console.error('[P2PSync] Error getting mDNS info:', error);
      return { published: false, error: String(error) };
    }
  }, [isElectronApp]);
  
  return {
    peers,
    syncStatuses,
    mdnsStatus,
    systemId,
    isLoading,
    error,
    mdnsDiagnostics,
    isElectronApp,
    restartMdnsService,
    getMdnsInfo
  };
} 