import { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';

interface UpdateInfo {
  version: string;
  url: string;
  releaseNotes?: string;
  mandatory?: boolean;
}

interface UpdateState {
  isChecking: boolean;
  updateAvailable: boolean;
  updateInfo: UpdateInfo | null;
  error: string | null;
}

// Production R2 URL for iOS updates
const UPDATE_CHECK_URL = process.env.NEXT_PUBLIC_IOS_UPDATE_URL || 'https://pub-d1ae66d7e9a247a08a1ad96b22c13e10.r2.dev/ios-update.json';

export function useIOSUpdater() {
  const [state, setState] = useState<UpdateState>({
    isChecking: false,
    updateAvailable: false,
    updateInfo: null,
    error: null,
  });

  // Only run on iOS platform
  const isIOS = Capacitor.getPlatform() === 'ios';

  const getCurrentVersion = async (): Promise<string> => {
    try {
      // For iOS, we'll need to get version from package.json or Capacitor config
      // This is a simplified version - in production you'd want to get the actual app version
      return process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0';
    } catch (error) {
      console.error('Failed to get current version:', error);
      return '1.0.0'; // Fallback version
    }
  };

  const checkForUpdates = async (): Promise<void> => {
    if (!isIOS) return;

    // Skip update check if URL is still placeholder
    if (UPDATE_CHECK_URL.includes('your-r2-bucket.com')) {
      console.log('iOS update check skipped: R2 URL not configured yet');
      return;
    }

    setState(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      // Get current app version
      const currentVersion = await getCurrentVersion();

      // Fetch update info from R2 with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(UPDATE_CHECK_URL, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`iOS update check failed: ${response.status}`);
      }

      const updateInfo: UpdateInfo = await response.json();

      // Compare versions (simple string comparison for now)
      const updateAvailable = updateInfo.version !== currentVersion;

      setState(prev => ({
        ...prev,
        isChecking: false,
        updateAvailable,
        updateInfo: updateAvailable ? updateInfo : null,
      }));

      console.log(`iOS Version check: Current=${currentVersion}, Latest=${updateInfo.version}, Update=${updateAvailable}`);

    } catch (error) {
      console.warn('iOS update check failed (this is normal if R2 is not set up yet):', error);
      setState(prev => ({
        ...prev,
        isChecking: false,
        error: null, // Don't show error to user for failed update checks
      }));
    }
  };

  const openAppStore = async (): Promise<void> => {
    if (!isIOS || !state.updateInfo) return;

    try {
      // For iOS, redirect to App Store
      // In production, this would be your actual App Store URL
      const appStoreUrl = 'https://apps.apple.com/app/bistro-restaurant-pos/id1234567890';
      window.open(appStoreUrl, '_system');
    } catch (error) {
      console.error('Failed to open App Store:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to open App Store',
      }));
    }
  };

  // Auto-check for updates on mount (with delay to avoid blocking app startup)
  useEffect(() => {
    if (isIOS) {
      // Delay update check by 5 seconds to let app start properly
      const timeoutId = setTimeout(() => {
        checkForUpdates();
      }, 5000);

      return () => clearTimeout(timeoutId);
    }
  }, [isIOS]);

  return {
    ...state,
    isIOS,
    checkForUpdates,
    openAppStore,
  };
}