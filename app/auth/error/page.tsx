'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Building2 } from "lucide-react";
import Link from "next/link";

function AuthErrorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { navigate } = useStaticNavigation();
  const [error, setError] = useState<string>("");
  
  useEffect(() => {
    // Get error from URL parameters
    const errorParam = searchParams?.get('error');
    if (errorParam) {
      // Map error codes to user-friendly messages
      switch (errorParam) {
        case 'CredentialsSignin':
          setError('Invalid email or password');
          break;
        case 'SessionRequired':
          setError('You need to be signed in to access this page');
          break;
        default:
          setError(errorParam);
      }
    } else {
      setError('An unknown authentication error occurred');
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-background to-muted/20">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-destructive/10">
              <Building2 className="h-6 w-6 text-destructive" />
            </div>
          </div>
          <h1 className="text-2xl font-semibold tracking-tight">Authentication Error</h1>
          <p className="text-sm text-muted-foreground">Something went wrong during sign in</p>
        </div>

        <div className="bg-card border rounded-lg p-6 shadow-sm space-y-4">
          <div className="text-center space-y-2">
            <p className="text-sm font-medium text-destructive">Login Failed</p>
            <p className="text-sm text-muted-foreground">{error}</p>
          </div>
          <Button className="w-full h-10" onClick={() => navigate('auth')}>
            Return to Login
          </Button>
        </div>
        
        <div className="text-center">
          <Link
            href="/auth"
            className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-primary"
          >
            <ArrowLeft className="mr-1 h-4 w-4" />
            Back to Sign In
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function AuthError() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-background to-muted/20">
        <div className="w-full max-w-md">
          <div className="bg-card border rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
            <p className="text-center text-sm text-muted-foreground mt-4">Loading...</p>
          </div>
        </div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
} 