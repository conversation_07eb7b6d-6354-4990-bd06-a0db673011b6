'use client';

import Link from 'next/link';
import { Download, <PERSON>R<PERSON>, Check, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Toaster, toast } from 'sonner';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { isWebBuild, getAuthButtonText } from '@/lib/utils/build-utils';

// ShadCN-only primitives + Tailwind utilities.

// Download handler
async function handleWindowsDownload() {
  try {
    toast.loading('🔍 جاري تحضير الملف...', { id: 'download' });
    const res = await fetch('/api/releases/latest');
    if (!res.ok) {
      if (res.status === 404) {
        toast.error('❌ لا يوجد إصدار متاح حالياً', { id: 'download' });
        return;
      }
      throw new Error(`HTTP ${res.status}`);
    }
    const data = await res.json();
    if (!data?.success || !data?.downloadUrl) throw new Error('Invalid response');

    const a = document.createElement('a');
    a.href = data.downloadUrl;
    a.download = data.fileName || 'bistro-latest.exe';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    toast.success('✅ بدء التحميل...', { id: 'download' });
  } catch (e) {
    console.error(e);
    toast.error('❌ فشل في تحميل الملف. يرجى المحاولة لاحقاً.', { id: 'download' });
  }
}

function Header() {
  return (
    <header className="fixed top-0 left-0 right-0 z-50">
      <div className="mx-auto max-w-6xl px-4 md:px-6 h-16 flex items-center justify-between rounded-b-2xl border border-black/10 bg-white/70 backdrop-blur supports-[backdrop-filter]:bg-white/50 shadow-sm">
        <Link href="/" className="inline-flex items-center gap-2" aria-label="Bistro Home">
          <span className="text-lg font-semibold tracking-tight text-black">Bistro</span>
        </Link>

        <nav className="hidden md:flex items-center gap-6 text-sm text-black/70">
          <Link href="#pricing" className="hover:text-black transition-colors">الأسعار</Link>
          <Link href="/auth" className="hover:text-black transition-colors">تسجيل الدخول</Link>
        </nav>

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="rounded-full border-black/10 text-black/70">Beta</Badge>
          <Button
            size="sm"
            className="hidden sm:inline-flex h-9 px-4 rounded-full bg-black text-white hover:bg-black/90"
            onClick={handleWindowsDownload}
          >
            <Download className="w-4 h-4 mr-2" />
            تحميل
          </Button>
        </div>
      </div>
    </header>
  );
}

function Hero() {
  const auth = useAuth();
  const isLoggedIn = !!auth?.isAuthenticated && !!auth?.user;
  const loading = !!auth?.loading;

  return (
    <section className="relative w-full min-h-[92vh] flex items-center">
      {/* subtle spotlight */}
      <div className="pointer-events-none absolute inset-0 -z-0 [mask-image:radial-gradient(closest-side,white,transparent_70%)]" />
      <div className="mx-auto max-w-5xl px-4 md:px-6 w-full">
        <div className="text-center">
          <div className="mb-6 flex justify-center">
            <Badge variant="secondary" className="rounded-full px-3 py-1 bg-black/5 text-black/70 border border-black/10">
              مصمم للمطاعم الحديثة
            </Badge>
          </div>

          <h1 className="text-5xl md:text-7xl font-extrabold tracking-tight text-black leading-[1.05] mb-3">
            إدارة مطعمك، ببساطة وأناقة
          </h1>
          <div className="mx-auto mb-4 flex items-center justify-center gap-2 text-xs text-black/60">
            <Sparkles className="h-4 w-4" />
            <span>خفة، سرعة، وواجهة متزنة</span>
          </div>
          <p className="text-lg md:text-xl text-black/70 leading-relaxed mb-10 max-w-3xl mx-auto">
            منصة شاملة لإدارة الطلبات، المخزون، الموظفين والتقارير. واجهة بسيطة ومركزة تمنحك السيطرة الكاملة بدون تعقيد.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-3">
            <Button
              size="lg"
              onClick={handleWindowsDownload}
              className="h-12 px-7 rounded-xl bg-black text-white hover:bg-black/90 shadow-sm"
            >
              <Download className="w-4 h-4 mr-2" />
              تحميل مجاني Windows 10/11
            </Button>

            {loading ? (
              <Button size="lg" variant="outline" disabled className="h-12 px-7 rounded-xl border-black/20 text-black">
                جاري التحقق...
              </Button>
            ) : isLoggedIn ? (
              isWebBuild() ? (
                <Button
                  size="lg"
                  onClick={handleWindowsDownload}
                  variant="outline"
                  className="h-12 px-7 rounded-xl border-black/20 text-black hover:bg-black/5"
                >
                  تحميل التطبيق الكامل
                </Button>
              ) : (
                <Button asChild size="lg" variant="outline" className="h-12 px-7 rounded-xl border-black/20 text-black hover:bg-black/5">
                  <Link href="/menu">الذهاب إلى لوحة التحكم</Link>
                </Button>
              )
            ) : (
              <Button asChild size="lg" variant="outline" className="h-12 px-7 rounded-xl border-black/20 text-black hover:bg-black/5">
                <Link href="/auth" className="inline-flex items-center">
                  {getAuthButtonText(false)}
                  <ArrowRight className="w-4 h-4 mr-2" />
                </Link>
              </Button>
            )}
          </div>

          <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-3 max-w-2xl mx-auto">
            {['يدعم العمل دون إنترنت', 'تقارير مباشرة للمالك', 'إدارة المخزون بذكاء'].map((point, i) => (
              <div key={i} className="flex items-center justify-center gap-2 text-black/80">
                <span className="inline-flex h-5 w-5 items-center justify-center rounded-full bg-emerald-500/15">
                  <Check className="h-3.5 w-3.5 text-emerald-600" />
                </span>
                <span className="text-sm">{point}</span>
              </div>
            ))}
          </div>
          <div className="mt-6 text-xs text-black/50">
            لا حاجة لاشتراك شهري أثناء العرض التجريبي
          </div>
        </div>
      </div>
    </section>
  );
}

function Pricing() {
  return (
    <section id="pricing" className="relative w-full py-28">
      <div className="mx-auto max-w-6xl px-4 md:px-6">
        <div className="text-center mb-14">
          <h2 className="text-4xl md:text-5xl font-extrabold text-black mb-3">سعر واضح وبسيط</h2>
          <p className="text-lg text-black/70">عرض خاص للمشتركين الأوائل. ادفع مرة سنوياً واستمتع بكل الميزات.</p>
        </div>

        <div className="mx-auto grid max-w-4xl grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {/* Starter */}
          <div className="rounded-2xl border border-black/10 bg-white/70 backdrop-blur p-6 md:p-8">
            <div className="mb-5">
              <h3 className="text-lg font-semibold text-black">Starter</h3>
              <p className="text-sm text-black/60 mt-1">للتجربة والاحتياجات الأساسية</p>
            </div>
            <div className="flex items-baseline gap-2">
              <span className="text-3xl font-extrabold text-black">مجاني</span>
            </div>
            <ul className="mt-6 space-y-3 text-sm text-black/80">
              {['تجربة الواجهة الأساسية', 'معاينة نماذج البيانات', 'بدون دعم فني'].map((item, i) => (
                <li key={i} className="flex items-start gap-2">
                  <span className="mt-0.5 inline-flex h-5 w-5 items-center justify-center rounded-full bg-emerald-500/15">
                    <Check className="h-3.5 w-3.5 text-emerald-600" />
                  </span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            <Button disabled className="mt-7 w-full h-11 rounded-xl" variant="outline">
              الخطة الحالية
            </Button>
          </div>

          {/* Pro */}
          <div className="relative rounded-2xl border border-black/10 bg-white p-6 md:p-8 shadow-[0_20px_60px_-25px_rgba(0,0,0,0.25)]">
            <div className="absolute -top-3 right-4">
              <Badge variant="secondary" className="rounded-full px-3 bg-black text-white">الأكثر اختياراً</Badge>
            </div>
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-black">Bistro Pro</h3>
              <div className="flex items-baseline gap-2 mt-2">
                <span className="text-5xl font-extrabold tracking-tight text-black">25,000 د.ج</span>
                <span className="text-black/70">/ سنوياً</span>
              </div>
              <p className="text-sm text-black/50 line-through mt-1">40,000 د.ج / سنوياً</p>
              <p className="text-emerald-600 text-sm mt-2">السعر ثابت مدى الحياة للمشتركين الأوائل</p>
            </div>

            <ul className="space-y-3 text-black/90 text-sm mb-8">
              {[
                'جميع الميزات الحالية والمستقبلية',
                'تحديثات مستمرة ودعم فني',
                'تطبيق سطح المكتب + تطبيقات محمولة للنُدل والمالك',
              ].map((item, i) => (
                <li key={i} className="flex items-start gap-3">
                  <span className="mt-0.5 inline-flex h-5 w-5 items-center justify-center rounded-full bg-emerald-500/15">
                    <Check className="h-3.5 w-3.5 text-emerald-600" />
                  </span>
                  <span>{item}</span>
                </li>
              ))}
            </ul>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <Button asChild size="lg" className="w-full h-11 rounded-xl bg-black text-white hover:bg-black/90">
                <Link href="/auth" className="inline-flex items-center justify-center">
                  الاستفادة من العرض
                  <ArrowRight className="w-4 h-4 mr-2" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="h-11 rounded-xl border-black/15 text-black hover:bg-black/5">
                <a href="#faq">الأسئلة الشائعة</a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function LandingV2Page() {
  // In production, redirect this experimental page to /landing
  if (process.env.NODE_ENV === 'production') {
    if (typeof window !== 'undefined') {
      // Client-side hard redirect to ensure it never shows in prod
      window.location.replace('/landing');
    }
    // Render nothing on server while client redirects
    return null;
  }

  return (
    <div className="relative min-h-screen w-full bg-white overflow-x-hidden">
      {/* Morning Haze - fixed background */}
      <div
        className="fixed inset-0 z-0"
        style={{
          backgroundImage: `
            radial-gradient(circle at 50% 100%, rgba(253, 224, 71, 0.4) 0%, transparent 60%),
            radial-gradient(circle at 50% 100%, rgba(251, 191, 36, 0.4) 0%, transparent 70%),
            radial-gradient(circle at 50% 100%, rgba(244, 114, 182, 0.5) 0%, transparent 80%)
          `,
        }}
      />
      {/* Soft overlay for readability */}
      <div className="pointer-events-none fixed inset-0 z-0 bg-gradient-to-b from-white/60 via-white/40 to-white/20" />

      <div className="relative z-10">
        <Toaster position="bottom-right" />
        <Header />
        <main className="pt-16">
          <Hero />
          <Pricing />
          <div className="mx-auto mt-16 h-px w-24 bg-black/10 rounded" />
          <footer className="mx-auto max-w-6xl px-4 md:px-6 py-10 text-center text-xs text-black/50">
            © {new Date().getFullYear()} Bistro — تجربة مصممة بعناية.
          </footer>
        </main>
      </div>
    </div>
  );
}