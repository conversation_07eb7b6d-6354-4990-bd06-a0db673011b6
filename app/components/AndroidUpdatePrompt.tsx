'use client';

import { useAndroidUpdater } from '@/app/hooks/useAndroidUpdater';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, Download, RefreshCw, Smartphone } from 'lucide-react';

export function AndroidUpdatePrompt() {
  const {
    isAndroid,
    updateAvailable,
    updateInfo,
    isDownloading,
    downloadProgress,
    error,
    checkForUpdates,
    downloadAndInstallUpdate,
    installImmediateUpdate,
  } = useAndroidUpdater();

  // Don't render on non-Android platforms
  if (!isAndroid) return null;

  // Don't show if no update is available or if there's an error
  if (!updateAvailable || !updateInfo || error) return null;

  const isMandatory = updateInfo.mandatory || false;

  return (
    <Dialog open={updateAvailable} onOpenChange={() => !isMandatory}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5 text-blue-600" />
            App Update Available
          </DialogTitle>
          <DialogDescription>
            A new version ({updateInfo.version}) of the app is available.
            {isMandatory && (
              <span className="block mt-2 text-amber-600 font-medium">
                This update is required to continue using the app.
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Release Notes */}
          {updateInfo.releaseNotes && (
            <div className="bg-gray-50 p-3 rounded-md">
              <h4 className="font-medium text-sm mb-2">What's New:</h4>
              <p className="text-sm text-gray-600">{updateInfo.releaseNotes}</p>
            </div>
          )}

          {/* Download Progress */}
          {isDownloading && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Download className="h-4 w-4 animate-pulse" />
                Downloading update... {downloadProgress}%
              </div>
              <Progress value={downloadProgress} className="w-full" />
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 text-red-700 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {isMandatory ? (
            // Mandatory update - only install option
            <Button
              onClick={installImmediateUpdate}
              disabled={isDownloading}
              className="w-full"
            >
              {isDownloading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Installing...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Install Now
                </>
              )}
            </Button>
          ) : (
            // Optional update - install and skip options
            <>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="w-full sm:w-auto"
              >
                Later
              </Button>
              <Button
                onClick={downloadAndInstallUpdate}
                disabled={isDownloading}
                className="w-full sm:w-auto"
              >
                {isDownloading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Installing...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Update Now
                  </>
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
