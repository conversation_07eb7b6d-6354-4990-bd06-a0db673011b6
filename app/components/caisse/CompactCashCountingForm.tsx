"use client"

import React, { useState, useMemo, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { formatCurrency } from '@/lib/utils/currency';
import { CalculatorIcon, AlertTriangleIcon, CheckIcon, Banknote, Coins } from 'lucide-react';
import { cn } from '@/lib/utils';

// Denomination values in DZD - grouped for better space usage
const BILLS = [
  { value: 2000, label: '2000' },
  { value: 1000, label: '1000' },
  { value: 500, label: '500' },
  { value: 200, label: '200' },
  { value: 100, label: '100' },
];

const COINS = [
  { value: 50, label: '50' },
  { value: 20, label: '20' },
  { value: 10, label: '10' },
  { value: 5, label: '5' },
];

interface CompactCashCountingFormProps {
  expectedAmount: number;
  onSubmit: (countedAmount: number, notes?: string) => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
}

export default function CompactCashCountingForm({
  expectedAmount,
  onSubmit,
  onCancel,
  isSubmitting = false
}: CompactCashCountingFormProps) {
  // State for denomination counts
  const [denomCounts, setDenomCounts] = useState<Record<number, number>>(() =>
    [...BILLS, ...COINS].reduce((acc, denom) => ({ ...acc, [denom.value]: 0 }), {})
  );
  
  // State for quick entry - initialized with expectedAmount
  const [quickAmount, setQuickAmount] = useState(expectedAmount.toString());
  
  // State for notes
  const [notes, setNotes] = useState('');

  // Effect to update quickAmount when expectedAmount changes (if it's not manually edited)
  useEffect(() => {
    // Only update if quickAmount hasn't been manually changed by the user
    // A more robust solution might involve a separate flag or checking if the current quickAmount
    // still matches the previous expectedAmount. For now, we'll assume if it's not the initial load,
    // the user might have interacted with it.
    if (parseFloat(quickAmount) === 0 || quickAmount === expectedAmount.toString()) { // Simplified logic
      setQuickAmount(expectedAmount.toString());
      setDenomCounts([...BILLS, ...COINS].reduce((acc, denom) => ({ ...acc, [denom.value]: 0 }), {})); // Reset denoms when expectedAmount changes
    }
  }, [expectedAmount]);

  // Calculate total from denominations
  const totalFromDenominations = useMemo(() => {
    return Object.entries(denomCounts).reduce(
      (total, [denom, count]) => total + (parseInt(denom) * (count || 0)),
      0
    );
  }, [denomCounts]);

  // The counted amount is always derived from quickAmount or totalFromDenominations
  // If quickAmount is empty, use totalFromDenominations.
  const countedAmount = quickAmount === '' ? totalFromDenominations : parseFloat(quickAmount) || 0;
  
  // Calculate variance
  const variance = countedAmount - expectedAmount;

  // Update denomination count and update quickAmount
  const updateDenomCount = (denom: number, count: string) => {
    const newCount = parseInt(count) || 0;
    setDenomCounts(prev => {
      const updatedCounts = {
        ...prev,
        [denom]: newCount
      };
      // Recalculate total from updated denominations and set it to quickAmount
      const newTotal = Object.entries(updatedCounts).reduce(
        (total, [d, c]) => total + (parseInt(d) * (c || 0)),
        0
      );
      setQuickAmount(newTotal.toString()); // Update the quick amount based on denom changes
      return updatedCounts;
    });
  };

  // Handle changes to the quick amount input
  const handleQuickAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuickAmount(e.target.value);
    // When quick amount is manually changed, reset denom counts or clear them
    setDenomCounts([...BILLS, ...COINS].reduce((acc, denom) => ({ ...acc, [denom.value]: 0 }), {}));
  };

  // Handle form submission
  const handleSubmit = () => {
    if (countedAmount <= 0) return;
    onSubmit(countedAmount, notes || undefined);
  };

  // Render denomination group
  const renderDenomGroup = (denoms: typeof BILLS, title: string, icon: React.ReactNode) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
        {icon}
        {title}
      </div>
      {/* Changed to a 3-column grid for more compactness */}
      <div className="grid grid-cols-3 gap-2"> {/* Changed from grid-cols-2 to grid-cols-3 */}
        {denoms.map(denom => (
          <div key={denom.value} className="flex items-center gap-1 p-1 bg-background border rounded"> {/* Reduced padding */}
            <span className="text-xs w-8">{denom.label}</span>
            <Input
              type="number"
              min="0"
              value={denomCounts[denom.value] === 0 ? '' : denomCounts[denom.value]} // Display empty string for 0 to make it cleaner
              onChange={(e) => updateDenomCount(denom.value, e.target.value)}
              className="h-7 w-10 text-xs text-center p-0.5" // Reduced height and padding
              aria-label={`${denom.label} count`}
            />
            <span className="text-xs text-muted-foreground flex-1 text-right min-w-[40px]"> {/* Added min-width */}
              {formatCurrency(denom.value * (denomCounts[denom.value] || 0))}
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-4"> {/* Retaining overall spacing from CardContent */}
      {/* Summary */}
      <div className="bg-muted/50 p-3 rounded-lg space-y-1">
        <div className="flex justify-between text-sm">
          <span>Théorique:</span>
          <span className="font-medium">{formatCurrency(expectedAmount)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Compté:</span>
          <span className="font-bold">{formatCurrency(countedAmount)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Écart:</span>
          <span className={cn(
            "font-bold",
            variance === 0 ? "text-green-600" :
            variance > 0 ? "text-blue-600" : "text-red-600"
          )}>
            {variance > 0 ? '+' : ''}{formatCurrency(variance)}
          </span>
        </div>
      </div>

      {/* Combined Input and Denominations */}
      <div className="space-y-3"> {/* Added space-y-3 for consistent vertical spacing */}
          <div className="space-y-2">
            <Label htmlFor="quick-amount" className="text-sm">Montant Total</Label>
            <Input
              id="quick-amount"
              type="number"
              value={quickAmount}
              onChange={handleQuickAmountChange}
              placeholder="ex: 50000"
              className="h-9"
            />
          </div>

          {/* Denomination inputs are now always visible */}
          {renderDenomGroup(BILLS, "Billets", <Banknote className="h-3 w-3" />)}
          {renderDenomGroup(COINS, "Pièces", <Coins className="h-3 w-3" />)}
      </div>

      {/* Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes" className="text-sm">Notes (optionnel)</Label>
        <Textarea
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Remarques sur le comptage..."
          className="h-16 text-sm"
        />
      </div>

      {/* Variance Alert */}
      {variance !== 0 && countedAmount > 0 && (
        <div className={cn(
          "p-2 rounded text-xs border",
          variance > 0 ? "bg-blue-50 border-blue-200 text-blue-800" : "bg-red-50 border-red-200 text-red-800"
        )}>
          <div className="flex items-center gap-2">
            {variance > 0 ? (
              <CheckIcon className="h-3 w-3" />
            ) : (
              <AlertTriangleIcon className="h-3 w-3" />
            )}
            <span>
              {variance > 0 
                ? `Excédent de ${formatCurrency(Math.abs(variance))}` 
                : `Manque de ${formatCurrency(Math.abs(variance))}`
              }
            </span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex gap-2 pt-2">
        {onCancel && (
          <Button variant="outline" onClick={onCancel} className="flex-1 h-9">
            Annuler
          </Button>
        )}
        <Button 
          onClick={handleSubmit}
          disabled={countedAmount <= 0 || isSubmitting}
          className="flex-1 h-9"
        >
          {isSubmitting ? 'Enregistrement...' : 'Enregistrer'}
        </Button>
      </div>
    </div>
  );
} 