"use client"

import React, { useState, useEffect } from 'react';

import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import {
  Calculator,
  AlertTriangle,
  ArrowUpIcon,
  ArrowDownIcon,
  RefreshCw,
  DollarSign,
} from "lucide-react";
import { formatCurrency } from '@/lib/utils/currency';
import { useToast } from "@/hooks/use-toast";
import CompactCashCountingForm from './CompactCashCountingForm';
import EnhancedCollectionTable from './EnhancedCollectionTable';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { 
  getSessionSummary, 
  closeSessionWithCount 
} from '@/lib/db/v4/operations/cash-session-ops';

interface SimpleCaisseOverviewProps {
  className?: string;
  onCashIn?: () => void;
  onCashOut?: () => void;
  cashInAmount?: number;
  cashOutAmount?: number;
}

export default function SimpleCaisseOverview({ 
  className,
  onCashIn,
  onCashOut,
  cashInAmount = 0,
  cashOutAmount = 0
}: SimpleCaisseOverviewProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const { isReady: dbReady } = useUnifiedDB();
  
  const [isCountingDialogOpen, setIsCountingDialogOpen] = useState(false);
  const [sessionSummary, setSessionSummary] = useState<any>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(true);
  const [isClosingSession, setIsClosingSession] = useState(false);

  // Load session summary
  const loadSessionSummary = async () => {
    if (!dbReady) return;
    
    try {
      setIsLoadingSession(true);
      console.log('[SimpleCaisseOverview] Loading session summary...');
      const summary = await getSessionSummary();
      console.log('[SimpleCaisseOverview] Session summary loaded:', {
        sessionId: summary.session._id,
        isActive: summary.isActive,
        drawerAmount: summary.drawerAmount,
        sessionTotalAdded: summary.sessionTotalAdded
      });
      setSessionSummary(summary);
    } catch (error) {
      console.error('[SimpleCaisseOverview] Error loading session:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger l'état de la caisse.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingSession(false);
    }
  };

  // Close session with counting
  const handleCloseSession = async (countedAmount: number, notes?: string) => {
    console.log('🔧 [handleCloseSession] Called with:', { countedAmount, notes });

    if (!user) {
      console.error('🔧 [handleCloseSession] Missing user');
      toast({
        title: "Erreur",
        description: "Utilisateur non connecté. Veuillez vous reconnecter.",
        variant: "destructive"
      });
      return;
    }

    if (!sessionSummary?.isActive) {
      toast({
        title: "⚠️ Session non active",
        description: "La session n'a pas de ventes. Effectuez une vente d'abord.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsClosingSession(true);
      console.log('🔧 [handleCloseSession] Starting session close process...');
      
      const result = await closeSessionWithCount({
        countedAmount,
        countedBy: user.name || 'Utilisateur',
        notes
      });

      console.log('🔧 [handleCloseSession] Session close result:', result);

      const varianceText = result.countRecord.variance !== 0 ? 
        ` • Écart: ${formatCurrency(result.countRecord.variance)}` : '';
      
      const adjustmentText = result.drawerAdjustment !== 0 ?
        ` • Ajustement: ${formatCurrency(result.drawerAdjustment)}` : '';

      toast({
        title: "✅ Session fermée",
        description: `Comptage: ${formatCurrency(countedAmount)}${varianceText}${adjustmentText} • Nouvelle session créée`
      });

      setIsCountingDialogOpen(false);
      console.log('🔧 [handleCloseSession] Refreshing session summary...');
      await loadSessionSummary();
      console.log('🔧 [handleCloseSession] Session close process completed');
    } catch (error) {
      console.error('[SimpleCaisseOverview] Error closing session:', error);
      
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Impossible de fermer la session",
        variant: "destructive"
      });
    } finally {
      console.log('🔧 [handleCloseSession] Setting isClosingSession to false');
      setIsClosingSession(false);
    }
  };

  useEffect(() => {
    if (dbReady) {
      loadSessionSummary();
    }
  }, [dbReady]);

  if (isLoadingSession) {
    return (
      <div className={cn("w-full p-2", className)}>
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
          <span className="text-xs text-muted-foreground">Chargement de la caisse...</span>
        </div>
      </div>
    );
  }

  const drawerAmount = sessionSummary?.drawerAmount || 0;
  const isActive = sessionSummary?.isActive || false;

  return (
    <div className={cn("w-full p-2", className)}>
        {/* Header with drawer amount (always visible) */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-1">
            <DollarSign className="h-4 w-4 text-primary" />
            <span className="text-xs font-medium">État de la Caisse</span>
          </div>
          
          <div className="flex items-center gap-1">
            {isActive ? (
              <Badge variant="default" className="text-xs bg-green-600 px-1 py-0">
                Session Active
              </Badge>
            ) : (
              <Badge variant="secondary" className="text-xs px-1 py-0">
                En Attente
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={loadSessionSummary}
              disabled={isLoadingSession}
              className="h-5 w-5 p-0"
              title="Actualiser"
            >
              <RefreshCw className={cn("h-3 w-3", isLoadingSession && "animate-spin")} />
            </Button>
          </div>
        </div>

        {/* Drawer Amount Display */}
        <div className="text-center py-2">
          <div className="text-2xl font-bold text-foreground mb-0.5">
            {formatCurrency(drawerAmount)}
          </div>
          <p className="text-xs text-muted-foreground">
            💰 Montant dans le tiroir
            </p>
        </div>

        {/* Session Details */}
        <div className={cn(
          "p-2 border rounded-lg mb-2",
          isActive ? "bg-green-50 border-green-200" : "bg-muted/50 border-muted"
        )}>
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-1">
              <div className={cn(
                "w-1.5 h-1.5 rounded-full",
                isActive ? "bg-green-500 animate-pulse" : "bg-gray-400"
              )}></div>
              <span className={cn(
                "text-xs font-medium",
                isActive ? "text-green-800" : "text-muted-foreground"
              )}>
                {isActive ? "Session en cours" : "Session en attente"}
              </span>
            </div>
            <Badge variant="secondary" className="text-xs px-1 py-0">
              {sessionSummary?.session?.staffName || 'Auto'}
            </Badge>
          </div>
          <div className={cn(
            "text-xs space-y-0.5",
            isActive ? "text-green-700" : "text-muted-foreground"
          )}>
            <div className="flex justify-between">
              <span>Ouverture:</span>
              <span className="font-medium">{formatCurrency(sessionSummary?.sessionStartAmount || 0)}</span>
            </div>
            <div className="flex justify-between">
              <span>Ventes:</span>
              <span className="font-medium">{formatCurrency(sessionSummary?.sessionSalesAmount || 0)}</span>
          </div>
            <div className="flex justify-between">
              <span>Manuel:</span>
              <span className="font-medium">{formatCurrency(sessionSummary?.sessionManualAmount || 0)}</span>
            </div>
            <div className="flex justify-between border-t pt-0.5">
              <span className="font-medium">Total Tiroir:</span>
              <span className={cn(
                "font-bold",
                isActive ? "text-green-900" : "text-muted-foreground"
              )}>
                {formatCurrency(drawerAmount)}
              </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
        <div className="space-y-2">
          {/* Manual transaction buttons */}
              <div className="flex gap-1">
                <Button
                  onClick={onCashIn}
                  variant="outline"
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  disabled={isClosingSession}
                >
                  <ArrowUpIcon className="h-3 w-3 mr-1 text-green-600" />
                  Dépôt
                </Button>
                
                <Button
                  onClick={onCashOut}
                  variant="outline"
                  size="sm"
                  className="flex-1 h-7 text-xs"
                  disabled={isClosingSession}
                >
                  <ArrowDownIcon className="h-3 w-3 mr-1 text-red-600" />
                  Retrait
                </Button>
              </div>

          {/* Session counting button */}
              <Button
                onClick={() => setIsCountingDialogOpen(true)}
                variant="default"
                size="sm"
                className="w-full h-8 text-xs font-medium"
            disabled={!isActive || isClosingSession}
              >
                {isClosingSession ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                    Comptage en cours...
                  </>
                ) : (
                  <>
                    <Calculator className="h-3 w-3 mr-1" />
                {isActive ? 'Compter & Fermer Session' : 'Effectuez une vente d\'abord'}
                  </>
                )}
              </Button>
              
          {/* Info about session requirement */}
          {!isActive && (
                <div className="text-xs text-muted-foreground text-center p-1.5 bg-muted/30 rounded">
              💡 La session devient active dès la première vente
                </div>
          )}
        </div>

        {/* Delivery Collections Section */}
        <div className="mt-3">
          <EnhancedCollectionTable />
                </div>

      {/* Cash Counting Dialog */}
      <Dialog open={isCountingDialogOpen} onOpenChange={setIsCountingDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Comptage de Session</DialogTitle>
            <DialogDescription>
              Comptez l'argent physique dans le tiroir. Le montant compté deviendra le nouveau solde réel et une nouvelle session sera créée.
            </DialogDescription>
          </DialogHeader>
          
          {isActive && sessionSummary && (
            <CompactCashCountingForm
              expectedAmount={sessionSummary.drawerAmount}
              onSubmit={handleCloseSession}
              onCancel={() => setIsCountingDialogOpen(false)}
              isSubmitting={isClosingSession}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}