"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import {
  User,
  Phone,
  MapPin,
  Package,
  DollarSign,
  ChevronDown,
  ChevronUp,
  Plus,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Truck,
  Eye,
  EyeOff,
  Loader2,
  Check
} from "lucide-react";
import { formatCurrency } from '@/lib/utils/currency';
import { useToast } from "@/hooks/use-toast";
import {
  getCollectionSummaryByDriver,
  processDriverCollection,
  getDeliveryStatusColor,
  getPaymentModelColor,
  migrateExistingDeliveryOrders,
  type EnhancedPendingCollection
} from '@/lib/db/v4/operations/enhanced-collection-ops';

interface ManualExpense {
  description: string;
  amount: number;
  reason: string;
}

interface DriverCollectionState {
  actualAmount: string;
  discrepancyReason: string;
  manualExpenses: ManualExpense[];
  isExpanded: boolean;
  isProcessing: boolean;
}

export default function EnhancedCollectionTable() {
  const [driverSummaries, setDriverSummaries] = useState<Array<{
    driverType: 'staff' | 'freelance';
    driverId: string;
    driverName: string;
    paymentModel: 'collection' | 'prepaid';
    orderCount: number;
    totalExpected: number;
    collections: EnhancedPendingCollection[];
  }>>([]);
  
  const [driverStates, setDriverStates] = useState<Record<string, DriverCollectionState>>({});
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Load pending collections
  const loadCollections = async () => {
    try {
      setIsLoading(true);
      const summaries = await getCollectionSummaryByDriver();
      setDriverSummaries(summaries);
      
      // Initialize driver states with Method 3 expected amount
      const initialStates: Record<string, DriverCollectionState> = {};
      summaries.forEach(summary => {
        const key = `${summary.driverType}-${summary.driverId}`;
        
        // Calculate Method 3: Total - Tariffs - (Expenses will be added later)
        const freelancerTariffTotal = summary.driverType === 'freelance' && summary.paymentModel === 'collection' ? 
          summary.collections.reduce((sum, c) => sum + (c.deliveryTariff || c.collectionRate || 0), 0) : 0;
        const expectedCashAmount = summary.totalExpected - freelancerTariffTotal;
        
        initialStates[key] = {
          actualAmount: expectedCashAmount.toString(), // Auto-fill with expected cash amount
          discrepancyReason: '',
          manualExpenses: [],
          isExpanded: false,
          isProcessing: false
        };
      });
      setDriverStates(initialStates);
    } catch (error) {
      console.error('Error loading collections:', error);
      toast({
        variant: "destructive",
        title: "❌ Erreur",
        description: "Impossible de charger les collectes en attente"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadCollections();
  }, []);

  // Update driver state
  const updateDriverState = (driverKey: string, updates: Partial<DriverCollectionState>) => {
    setDriverStates(prev => ({
      ...prev,
      [driverKey]: { ...prev[driverKey], ...updates }
    }));
  };

  // Add manual expense
  const addManualExpense = (driverKey: string) => {
    const currentState = driverStates[driverKey];
    updateDriverState(driverKey, {
      manualExpenses: [
        ...currentState.manualExpenses,
        { description: '', amount: 0, reason: '' }
      ]
    });
  };

  // Remove manual expense
  const removeManualExpense = (driverKey: string, index: number) => {
    const currentState = driverStates[driverKey];
    updateDriverState(driverKey, {
      manualExpenses: currentState.manualExpenses.filter((_, i) => i !== index)
    });
  };

  // Update manual expense
  const updateManualExpense = (driverKey: string, index: number, field: keyof ManualExpense, value: string | number) => {
    const currentState = driverStates[driverKey];
    const updatedExpenses = [...currentState.manualExpenses];
    updatedExpenses[index] = { ...updatedExpenses[index], [field]: value };
    updateDriverState(driverKey, { manualExpenses: updatedExpenses });
  };

  // Process collection for a driver
  const processCollection = async (summary: typeof driverSummaries[0]) => {
    console.log('🔍 [processCollection] Starting validation for:', summary.driverName);
    const driverKey = `${summary.driverType}-${summary.driverId}`;
    const state = driverStates[driverKey];
    
    if (!state) {
      console.error('🚫 [processCollection] No state found for driver:', driverKey);
      toast({
        variant: "destructive",
        title: "❌ Erreur",
        description: "État du conducteur introuvable"
      });
      return;
    }

    console.log('📊 [processCollection] Current state:', {
      actualAmount: state.actualAmount,
      discrepancyReason: state.discrepancyReason,
      manualExpenses: state.manualExpenses
    });

    // Validation
    const actualAmount = parseFloat(state.actualAmount);
    if (isNaN(actualAmount) || actualAmount < 0) {
      console.error('🚫 [processCollection] Invalid actual amount:', state.actualAmount);
      toast({
        variant: "destructive",
        title: "❌ Erreur",
        description: "Montant reçu invalide"
      });
      return;
    }

    const manualExpenseTotal = state.manualExpenses.reduce((sum, exp) => sum + exp.amount, 0);
    
    // 🔧 FIX: Calculate freelancer tariff total for proper validation
    const freelancerTariffTotal = summary.driverType === 'freelance' && summary.paymentModel === 'collection' ? 
      summary.collections.reduce((sum, c) => sum + (c.deliveryTariff || c.collectionRate || 0), 0) : 0;
    
    // 🔧 FIX: Use the same calculation as the UI - subtract both tariffs and expenses
    const adjustedExpected = summary.totalExpected - freelancerTariffTotal - manualExpenseTotal;
    const discrepancy = actualAmount - adjustedExpected;

    console.log('💰 [processCollection] Financial calculation:', {
      actualAmount,
      totalExpected: summary.totalExpected,
      freelancerTariffTotal,
      manualExpenseTotal,
      adjustedExpected,
      discrepancy
    });

    // More lenient discrepancy validation - only require reason for significant discrepancies
    if (Math.abs(discrepancy) > 1.0 && !state.discrepancyReason.trim()) {
      console.error('🚫 [processCollection] Significant discrepancy without reason:', discrepancy);
      toast({
        variant: "destructive",
        title: "⚠️ Raison requise",
        description: `Écart de ${formatCurrency(Math.abs(discrepancy))} détecté. Veuillez fournir une raison.`
      });
      return;
    }

    // Validate manual expenses - only if they exist
    if (state.manualExpenses.length > 0) {
      const invalidExpenses = state.manualExpenses.some(exp => 
        !exp.description.trim() || exp.amount <= 0 || !exp.reason.trim()
      );
      if (invalidExpenses) {
        console.error('🚫 [processCollection] Invalid manual expenses found');
        toast({
          variant: "destructive",
          title: "❌ Frais de session invalides",
          description: "Tous les frais de session doivent avoir une description, un montant et des détails"
        });
        return;
      }
    }

    try {
      console.log('🚀 [processCollection] Starting processing...');
      updateDriverState(driverKey, { isProcessing: true });

      // Just mark collections as processed without caisse integration
      const { updateCollectionStatus } = await import('@/lib/db/v4/operations/order-ops');
      
      console.log(`📦 [processCollection] Processing ${summary.collections.length} orders`);
      
      // Update collection status for each order
      for (const collection of summary.collections) {
        console.log(`📝 [processCollection] Updating order ${collection.orderId}`);
        await updateCollectionStatus(collection.orderId, {
          actualAmount: collection.expectedAmount, // Individual order amount
          collectedBy: 'Current User', // TODO: Get from auth context
          discrepancyReason: discrepancy !== 0 ? state.discrepancyReason : undefined,
          manualExpenses: state.manualExpenses.length > 0 ? state.manualExpenses : undefined
        });
      }

      console.log('✅ [processCollection] All orders updated successfully');
      toast({
        title: "✅ Collecte validée",
        description: `Collecte de ${summary.driverName} validée (${summary.orderCount} commandes)`,
      });

      // Reload collections
      console.log('🔄 [processCollection] Reloading collections...');
      await loadCollections();
      console.log('✅ [processCollection] Collections reloaded');
    } catch (error) {
      console.error('❌ [processCollection] Error processing collection:', error);
      toast({
        variant: "destructive",
        title: "❌ Erreur",
        description: `Erreur lors de la validation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
      });
    } finally {
      updateDriverState(driverKey, { isProcessing: false });
      console.log('🏁 [processCollection] Processing completed');
    }
  };

  // Manual migration function
  const runMigration = async () => {
    try {
      setIsLoading(true);
      toast({
        title: "🔄 Migration en cours",
        description: "Migration des commandes existantes..."
      });

      const migratedCount = await migrateExistingDeliveryOrders();
      
      toast({
        title: "✅ Migration terminée",
        description: `${migratedCount} commande(s) migrée(s)`
      });

      // Reload collections after migration
      await loadCollections();
    } catch (error) {
      console.error('Migration error:', error);
      toast({
        variant: "destructive",
        title: "❌ Erreur de migration",
        description: "Impossible de migrer les commandes existantes"
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-2" />
        <span className="text-sm text-muted-foreground">Chargement des collectes...</span>
      </div>
    );
  }

  if (driverSummaries.length === 0) {
    return (
      <div className="p-4 border rounded-lg">
        <div className="flex items-center gap-2 text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span className="text-sm font-medium">Toutes les collectes sont traitées</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b">
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-primary" />
          <h3 className="font-semibold">🚚 Collectes Livraisons</h3>
          <Badge variant="secondary" className="text-xs">
            {driverSummaries.length} livreur{driverSummaries.length > 1 ? 's' : ''}
          </Badge>
        </div>
        <div className="text-xs text-muted-foreground">
          Argent collecté par livreurs → Caisse (séparé du tiroir-caisse)
        </div>
      </div>

      {/* Driver Collection Table */}
      <div className="space-y-1">
        {driverSummaries.map((summary) => {
          const driverKey = `${summary.driverType}-${summary.driverId}`;
          const state = driverStates[driverKey];
          
          if (!state) return null;

          const actualAmount = parseFloat(state.actualAmount) || 0;
          const manualExpenseTotal = state.manualExpenses.reduce((sum, exp) => sum + exp.amount, 0);
          
          // Calculate freelancer tariff total for deduction
          const freelancerTariffTotal = summary.driverType === 'freelance' && summary.paymentModel === 'collection' ? 
            summary.collections.reduce((sum, c) => sum + (c.deliveryTariff || c.collectionRate || 0), 0) : 0;
          
          // Method 3: Total - Tariffs - Expenses = Expected cash for register
          const adjustedExpected = summary.totalExpected - freelancerTariffTotal - manualExpenseTotal;
          const discrepancy = actualAmount - adjustedExpected;

          return (
            <div key={driverKey} className="border rounded-lg overflow-hidden">
              {/* Driver Header Row */}
              <div className="bg-muted/30 p-2 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-sm">{summary.driverName}</span>
                    <Badge variant="outline" className="text-xs px-1 py-0">
                      {summary.driverType === 'staff' ? '👤' : '🚚'}
                    </Badge>
                    <Badge variant="outline" className="text-xs px-1 py-0">
                      {summary.paymentModel === 'collection' ? '💰' : '💸'}
                    </Badge>
                    <Badge variant="secondary" className="text-xs px-1 py-0">
                      {summary.orderCount} cmd
                    </Badge>
                    {/* Show tariff rate for freelance collection model */}
                    {summary.driverType === 'freelance' && summary.paymentModel === 'collection' && summary.collections.some(c => c.deliveryTariff || c.collectionRate) && (
                      <Badge variant="outline" className="text-xs px-1 py-0 bg-blue-50 text-blue-700 border-blue-200">
                        🏷️ Tarifs variables
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">Ventes:</span>
                    <span className="font-semibold text-sm">{formatCurrency(summary.totalExpected)}</span>
                    
                    {/* Show deductions for validation clarity */}
                    {(freelancerTariffTotal > 0 || manualExpenseTotal > 0) && (
                      <>
                        <span className="text-xs text-muted-foreground">→</span>
                        <span className="text-xs text-muted-foreground">Espéré en caisse:</span>
                        <span className="font-semibold text-sm text-green-600">
                          {formatCurrency(adjustedExpected)}
                        </span>
                        <div className="text-xs text-muted-foreground bg-muted px-1 rounded">
                          {freelancerTariffTotal > 0 && `-${formatCurrency(freelancerTariffTotal)} tarif`}
                          {freelancerTariffTotal > 0 && manualExpenseTotal > 0 && ' '}
                          {manualExpenseTotal > 0 && `-${formatCurrency(manualExpenseTotal)} frais`}
                        </div>
                      </>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => updateDriverState(driverKey, { isExpanded: !state.isExpanded })}
                    >
                      {state.isExpanded ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Collection Controls */}
              <div className="p-2 pt-1 bg-background">
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 items-start">
                  <div className="col-span-2 lg:col-span-1">
                    <Label className="text-xs text-muted-foreground">💰 Reçu en caisse</Label>
                    <Input
                      type="text"
                      className="h-6 text-xs"
                      value={state.actualAmount}
                      onChange={(e) => {
                        // Allow typing numbers, remove non-numeric chars
                        const numericValue = e.target.value.replace(/[^0-9.]/g, '');
                        updateDriverState(driverKey, { actualAmount: numericValue });
                      }}
                      disabled={state.isProcessing}
                      placeholder={formatCurrency(adjustedExpected)}
                    />
                  </div>
                  <div className="col-span-2 lg:col-span-1 flex flex-col items-start justify-end">
                    <Label className="text-xs text-muted-foreground">📊 Écart</Label>
                    <div className={cn(
                      "h-6 px-2 rounded border flex items-center text-xs font-bold",
                      Math.abs(discrepancy) < 0.01 ? "text-green-700 bg-green-50 border-green-200" : "text-red-700 bg-red-50 border-red-200"
                    )}>
                      {formatCurrency(discrepancy)}
                    </div>
                    <p className="text-[0.6rem] text-muted-foreground mt-0.5 leading-none">
                      vs {formatCurrency(adjustedExpected)} attendu
                    </p>
                  </div>
                  <div className="col-span-2 lg:col-span-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addManualExpense(driverKey)}
                      disabled={state.isProcessing}
                      className="h-7 w-full text-xs gap-1 justify-center"
                    >
                      <Plus className="h-3 w-3" />
                      Frais Session
                    </Button>
                  </div>
                  <div className="col-span-2 lg:col-span-1">
                    <Button
                      size="sm"
                      onClick={() => processCollection(summary)}
                      disabled={state.isProcessing}
                      className="h-7 w-full text-xs gap-1 justify-center"
                    >
                      {state.isProcessing ? <><Loader2 className="h-3 w-3 animate-spin" /> Traitement...</> : <><Check className="h-3 w-3" /> Valider</>}
                    </Button>
                  </div>
                </div>

                {/* Method 3 Financial Breakdown */}
                {(freelancerTariffTotal > 0 || manualExpenseTotal > 0) && (
                  <div className="mt-1 py-1 px-2 bg-green-50 rounded border border-green-200 text-[0.7rem] leading-tight">
                    <div className="flex flex-wrap items-center justify-between gap-x-4 gap-y-1">
                      <span>
                        💵 Ventes: <span className="font-semibold text-green-700">{formatCurrency(summary.totalExpected)}</span>{' '}
                        <span className="text-green-500">({summary.orderCount} commandes)</span>
                      </span>
                      {freelancerTariffTotal > 0 && (
                        <span>
                          ➖ Tarifs: <span className="font-semibold text-red-700">-{formatCurrency(freelancerTariffTotal)}</span>{' '}
                          <span className="text-red-500">(Freelance)</span>
                        </span>
                      )}
                      {manualExpenseTotal > 0 && (
                        <span>
                          ➖ Frais: <span className="font-semibold text-orange-700">-{formatCurrency(manualExpenseTotal)}</span>{' '}
                          <span className="text-orange-500">(Session)</span>
                        </span>
                      )}
                      <span>
                        🏪 Attendu: <span className="font-semibold text-green-700">{formatCurrency(adjustedExpected)}</span>{' '}
                        <span className="text-green-500">(Cash)</span>
                      </span>
                    </div>
                  </div>
                )}

                {/* Manual Expenses */}
                {state.manualExpenses.length > 0 && (
                  <div className="mt-2 p-2 bg-muted/20 rounded border">
                    <div className="flex items-center justify-between mb-1">
                      <Label className="text-xs font-medium">⛽ Frais de session</Label>
                      <span className="text-xs text-muted-foreground">
                        Total: {formatCurrency(manualExpenseTotal)}
                      </span>
                    </div>
                    <div className="space-y-1">
                      {state.manualExpenses.map((expense, index) => (
                        <div key={index} className="grid grid-cols-4 gap-1">
                          <Input
                            type="number"
                            placeholder="Montant"
                            className="h-6 text-xs"
                            value={expense.amount || ''}
                            onChange={(e) => updateManualExpense(driverKey, index, 'amount', parseFloat(e.target.value) || 0)}
                          />
                          <Input
                            placeholder="Raison"
                            className="h-6 text-xs"
                            value={expense.reason}
                            onChange={(e) => updateManualExpense(driverKey, index, 'reason', e.target.value)}
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => removeManualExpense(driverKey, index)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Discrepancy Reason */}
                {Math.abs(discrepancy) > 0.01 && (
                  <div className="mt-2">
                    <Label className="text-xs text-muted-foreground">⚠️ Raison de l'écart</Label>
                    <Textarea
                      placeholder="Expliquez la différence..."
                      className="mt-1 h-16 text-xs"
                      value={state.discrepancyReason}
                      onChange={(e) => updateDriverState(driverKey, { discrepancyReason: e.target.value })}
                      disabled={state.isProcessing}
                    />
                  </div>
                )}
              </div>

              {/* Orders Table (Collapsible) */}
              <Collapsible open={state.isExpanded}>
                <CollapsibleContent>
                  <div className="border-t">
                    <Table>
                      <TableHeader>
                        <TableRow className="h-8">
                          <TableHead className="text-xs py-1 px-2">Commande</TableHead>
                          <TableHead className="text-xs py-1 px-2">Client</TableHead>
                          <TableHead className="text-xs py-1 px-2">📞 Téléphone</TableHead>
                          <TableHead className="text-xs py-1 px-2">📍 Adresse</TableHead>
                          <TableHead className="text-xs py-1 px-2">Articles</TableHead>
                          <TableHead className="text-xs py-1 px-2">Statut</TableHead>
                          {summary.driverType === 'freelance' && summary.paymentModel === 'collection' && summary.collections.some(c => c.deliveryTariff || c.collectionRate) && (
                            <TableHead className="text-xs py-1 px-2 text-right">🏷️ Tarif</TableHead>
                          )}
                          <TableHead className="text-xs py-1 px-2 text-right">Montant</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {summary.collections.map((collection) => (
                          <TableRow key={collection.orderId} className="h-8">
                            <TableCell className="py-1 px-2">
                              <span className="font-mono text-xs font-bold">
                                #{collection.orderId.slice(-6)}
                              </span>
                            </TableCell>
                            <TableCell className="py-1 px-2">
                              <span className="text-xs font-medium">{collection.customer.name}</span>
                            </TableCell>
                            <TableCell className="py-1 px-2">
                              <span className="text-xs text-muted-foreground">
                                {collection.customer.phone || '-'}
                              </span>
                            </TableCell>
                            <TableCell className="py-1 px-2">
                              <span className="text-xs text-muted-foreground truncate max-w-32">
                                {collection.customer.address || '-'}
                              </span>
                            </TableCell>
                            <TableCell className="py-1 px-2">
                              <span className="text-xs">
                                {collection.items.length} article{collection.items.length > 1 ? 's' : ''}
                              </span>
                            </TableCell>
                            <TableCell className="py-1 px-2">
                              <Badge 
                                variant="outline" 
                                className={cn(
                                  "text-xs px-1 py-0",
                                  collection.deliveryStatus === 'delivered' && "text-green-600 border-green-600",
                                  collection.deliveryStatus === 'failed' && "text-red-600 border-red-600",
                                  collection.deliveryStatus === 'partially_delivered' && "text-yellow-600 border-yellow-600"
                                )}
                              >
                                {collection.deliveryStatus === 'delivered' && '✅'}
                                {collection.deliveryStatus === 'failed' && '❌'}
                                {collection.deliveryStatus === 'partially_delivered' && '⚠️'}
                                {collection.deliveryStatus === 'pending' && '⏳'}
                                {collection.deliveryStatus === 'out_for_delivery' && '🚚'}
                              </Badge>
                            </TableCell>
                            {summary.driverType === 'freelance' && summary.paymentModel === 'collection' && (collection.deliveryTariff || collection.collectionRate) && (
                              <TableCell className="py-1 px-2 text-right">
                                <span className="font-semibold text-xs text-blue-600">
                                  {formatCurrency(collection.deliveryTariff || collection.collectionRate || 0)}
                                </span>
                              </TableCell>
                            )}
                            <TableCell className="py-1 px-2 text-right">
                              <span className="font-semibold text-xs">
                                {formatCurrency(collection.expectedAmount)}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          );
        })}
      </div>
    </div>
  );
}