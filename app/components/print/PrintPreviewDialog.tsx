"use client";

import React, { useRef, useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Printer, FileDown, Loader2, Refresh<PERSON>w, ExternalLink } from "lucide-react";
import { PrintJob } from '@/lib/services/kitchen-print-service';

interface PrintPreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  printJob: PrintJob | null;
  onPrint: () => void;
}

export function PrintPreviewDialog({
  open,
  onOpenChange,
  printJob,
  onPrint
}: PrintPreviewDialogProps) {
  const printFrameRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  
  // Reset loading state when printJob changes
  useEffect(() => {
    if (printJob) {
      setIsLoading(true);
      setHasError(false);
    }
  }, [printJob]);
  
  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsLoading(false);
  };
  
  // Handle iframe error events
  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };
  
  // Function to handle actual printing
  const handlePrint = () => {
    if (!printFrameRef.current?.contentWindow) {
      console.error('Print frame not available');
      return;
    }
    
    try {
      setIsPrinting(true);
      
      // Call the actual print function
      printFrameRef.current.contentWindow.print();
      
      // Callback happens when print dialog opens, not when printing is done
      setTimeout(() => {
        setIsPrinting(false);
        onPrint();
      }, 500);
    } catch (error) {
      console.error('Printing failed:', error);
      setIsPrinting(false);
      setHasError(true);
    }
  };
  
  // Function to print to a new window (alternative approach)
  const handlePrintToWindow = () => {
    if (!printJob) return;
    
    setIsPrinting(true);
    
    // Create a new window
    const printWindow = window.open('', '_blank', 'width=600,height=800');
    if (!printWindow) {
      alert('Veuillez autoriser les popups pour utiliser l\'aperçu d\'impression');
      setIsPrinting(false);
      return;
    }
    
    // Write the HTML to the new window
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${printJob.title}</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body {
              font-family: 'Courier New', monospace;
              margin: 0;
              padding: 10px;
              background-color: #f5f5f5;
              display: flex;
              justify-content: center;
              align-items: flex-start;
            }
            .receipt-container {
              width: 58mm;
              max-width: 220px;
              background-color: white;
              border: 1px solid #ddd;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              padding: 0;
              margin: 0;
            }
            @media print {
              body {
                width: 58mm;
                margin: 0;
                padding: 0;
                background-color: white;
              }
              .receipt-container {
                border: none;
                box-shadow: none;
                width: 58mm;
                max-width: none;
              }
              @page {
                size: 58mm auto;
                margin: 0;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-container">
            ${printJob.content}
          </div>
          <script>
            // Auto-print when loaded
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 500);
            };
            
            // Close the window when printing is done or canceled
            window.addEventListener('afterprint', function() {
              window.close();
            });
          </script>
        </body>
      </html>
    `);
    
    // Ensure content is fully loaded before printing
    printWindow.document.close();
    
    // Set timeout to reset printing state even if the window is closed without printing
    setTimeout(() => {
      setIsPrinting(false);
      onPrint();
    }, 1000);
  };
  
  // Function to download the print content as HTML
  const handleDownload = () => {
    if (!printJob) return;
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${printJob.title}</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body {
              font-family: 'Courier New', monospace;
              margin: 0;
              padding: 10px;
              background-color: #f5f5f5;
              display: flex;
              justify-content: center;
              align-items: flex-start;
            }
            .receipt-container {
              width: 58mm;
              max-width: 220px;
              background-color: white;
              border: 1px solid #ddd;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              padding: 0;
              margin: 0;
            }
            @media print {
              body {
                width: 58mm;
                margin: 0;
                padding: 0;
                background-color: white;
              }
              .receipt-container {
                border: none;
                box-shadow: none;
                width: 58mm;
                max-width: none;
              }
              @page {
                size: 58mm auto;
                margin: 0;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-container">
            ${printJob.content}
          </div>
        </body>
      </html>
    `;
    
    // Create a blob from the HTML content
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    // Create a temporary link to download the file
    const a = document.createElement('a');
    a.href = url;
    a.download = `${printJob.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`;
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);
  };
  
  if (!printJob) return null;
  
  return (
    <Dialog open={open} onOpenChange={(newOpenState) => {
      if (!isPrinting) {
        onOpenChange(newOpenState);
      }
    }}>
      <DialogContent className="sm:max-w-[650px] md:max-w-[750px] h-[85vh] max-h-[900px] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Printer className="h-5 w-5" />
            <span>{printJob.title}</span>
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 min-h-0 overflow-auto border rounded-md my-4 p-3 bg-white relative">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="text-sm text-muted-foreground">Chargement de l'aperçu...</span>
              </div>
            </div>
          )}
          
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/90 z-10">
              <div className="flex flex-col items-center gap-3 p-6 text-center">
                <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600">
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="12" />
                    <line x1="12" y1="16" x2="12.01" y2="16" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium">Erreur lors du chargement de l'aperçu</h3>
                <p className="text-sm text-muted-foreground">Une erreur s'est produite lors du chargement de l'aperçu d'impression.</p>
                <Button 
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    setIsLoading(true);
                    setHasError(false);
                    setTimeout(() => {
                      if (printFrameRef.current) {
                        printFrameRef.current.src = 'about:blank';
                        setTimeout(() => {
                          if (printFrameRef.current) {
                            // Force reload the iframe
                            printFrameRef.current.srcdoc = `
                              <!DOCTYPE html>
                              <html>
                                <head>
                                  <title>${printJob.title}</title>
                                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                  <style>
                                    body {
                                      font-family: 'Courier New', monospace;
                                      margin: 0;
                                      padding: 10px;
                                      background-color: #f5f5f5;
                                      display: flex;
                                      justify-content: center;
                                      align-items: flex-start;
                                    }
                                    .receipt-container {
                                      width: 58mm;
                                      max-width: 220px;
                                      background-color: white;
                                      border: 1px solid #ddd;
                                      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                                      padding: 0;
                                      margin: 0;
                                    }
                                    @media print {
                                      body {
                                        width: 58mm;
                                        margin: 0;
                                        padding: 0;
                                        background-color: white;
                                      }
                                      .receipt-container {
                                        border: none;
                                        box-shadow: none;
                                        width: 58mm;
                                        max-width: none;
                                      }
                                      @page {
                                        size: 58mm auto;
                                        margin: 0;
                                      }
                                    }
                                  </style>
                                </head>
                                <body>
                                  <div class="receipt-container">
                                    ${printJob.content}
                                  </div>
                                </body>
                              </html>
                            `;
                          }
                        }, 100);
                      }
                    }, 100);
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Réessayer
                </Button>
              </div>
            </div>
          )}
          
          <iframe
            ref={printFrameRef}
            onLoad={handleIframeLoad}
            onError={handleIframeError}
            className="w-full h-full border-0"
            srcDoc={`
              <!DOCTYPE html>
              <html>
                <head>
                  <title>${printJob.title}</title>
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <style>
                    body {
                      font-family: 'Courier New', monospace;
                      margin: 0;
                      padding: 10px;
                      background-color: #f5f5f5;
                      display: flex;
                      justify-content: center;
                      align-items: flex-start;
                    }
                    .receipt-container {
                      width: 58mm;
                      max-width: 220px;
                      background-color: white;
                      border: 1px solid #ddd;
                      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                      padding: 0;
                      margin: 0;
                    }
                    @media print {
                      body {
                        width: 58mm;
                        margin: 0;
                        padding: 0;
                        background-color: white;
                      }
                      .receipt-container {
                        border: none;
                        box-shadow: none;
                        width: 58mm;
                        max-width: none;
                      }
                      @page {
                        size: 58mm auto;
                        margin: 0;
                      }
                    }
                  </style>
                </head>
                <body>
                  <div class="receipt-container">
                    ${printJob.content}
                  </div>
                </body>
              </html>
            `}
          />
        </div>
        
        <DialogFooter className="flex-shrink-0 flex flex-col sm:flex-row sm:justify-between gap-2">
          <div className="text-xs text-muted-foreground">
            Ceci est un aperçu avant impression. Cliquez sur Imprimer pour envoyer à l'imprimante.
          </div>
          <div className="flex flex-wrap gap-2 justify-end">
            <Button variant="outline" size="sm" onClick={handleDownload} disabled={isPrinting || isLoading || hasError}>
              <FileDown className="h-4 w-4 mr-1" />
              Télécharger HTML
            </Button>
            <Button variant="outline" size="sm" onClick={handlePrintToWindow} disabled={isPrinting || isLoading || hasError}>
              <ExternalLink className="h-4 w-4 mr-1" />
              Ouvrir dans une nouvelle fenêtre
            </Button>
            <Button variant="outline" size="sm" onClick={() => onOpenChange(false)} disabled={isPrinting}>
              Fermer
            </Button>
            <Button onClick={handlePrint} className="gap-2" disabled={isPrinting || isLoading || hasError}>
              {isPrinting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Impression...
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4" />
                  Imprimer
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 