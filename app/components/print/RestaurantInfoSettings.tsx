import React, { useEffect, useState } from 'react';
import { printService } from '@/lib/services/print-service';
import { getSettings, updateSettings } from '@/lib/db/v4/operations/settings-ops';

const LOCAL_KEY = 'restaurant_info_settings_v4';

const RestaurantInfoSettings: React.FC = () => {
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [logo, setLogo] = useState('');
  const [footer, setFooter] = useState('Merci de votre visite!');
  const [saved, setSaved] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadSettings() {
      setLoading(true);
      setError(null);
      try {
        const dbSettings = await getSettings();
        setName(dbSettings.restaurantPhone || '');
        setAddress(dbSettings.restaurantAddress || '');
        setPhone(dbSettings.restaurantSecondaryPhone || '');
        setLogo(dbSettings.restaurantLogoUrl || '');
        setFooter(dbSettings.restaurantFooter || 'Merci de votre visite!');
        // Save to localStorage for printService fallback
        localStorage.setItem(LOCAL_KEY, JSON.stringify({
          name: dbSettings.restaurantPhone || '',
          address: dbSettings.restaurantAddress || '',
          phone: dbSettings.restaurantSecondaryPhone || '',
          logo: dbSettings.restaurantLogoUrl || '',
          footer: dbSettings.restaurantFooter || 'Merci de votre visite!'
        }));
      } catch (e) {
        // fallback to localStorage
        const raw = localStorage.getItem(LOCAL_KEY);
        if (raw) {
          try {
            const data = JSON.parse(raw);
            setName(data.name || '');
            setAddress(data.address || '');
            setPhone(data.phone || '');
            setLogo(data.logo || '');
            setFooter(data.footer || 'Merci de votre visite!');
          } catch {}
        } else {
          setError('Impossible de charger les infos du restaurant.');
        }
      } finally {
        setLoading(false);
      }
    }
    loadSettings();
  }, []);

  const handleSave = async () => {
    setError(null);
    try {
      await updateSettings({
        restaurantPhone: name,
        restaurantAddress: address,
        restaurantSecondaryPhone: phone,
        restaurantLogoUrl: logo,
        restaurantFooter: footer
      });
      localStorage.setItem(LOCAL_KEY, JSON.stringify({ name, address, phone, logo, footer }));
      printService.setRestaurantInfo(name, address, phone, logo);
      setSaved(true);
      setTimeout(() => setSaved(false), 1200);
    } catch (e) {
      setError('Erreur lors de la sauvegarde.');
    }
  };

  if (loading) return <div style={{ padding: 24 }}>Chargement...</div>;
  if (error) return <div style={{ padding: 24, color: 'red' }}>{error}</div>;

  return (
    <div style={{ maxWidth: 420, margin: '0 auto', padding: 24 }}>
      <h2 style={{ fontWeight: 700, fontSize: 22, marginBottom: 18 }}>Infos du Restaurant (Impression)</h2>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 14 }}>
        <label>
          Nom du restaurant
          <input value={name} onChange={e => setName(e.target.value)} style={{ width: '100%', padding: 8, marginTop: 4 }} />
        </label>
        <label>
          Adresse
          <input value={address} onChange={e => setAddress(e.target.value)} style={{ width: '100%', padding: 8, marginTop: 4 }} />
        </label>
        <label>
          Téléphone
          <input value={phone} onChange={e => setPhone(e.target.value)} style={{ width: '100%', padding: 8, marginTop: 4 }} />
        </label>
        <label>
          Logo (URL)
          <input value={logo} onChange={e => setLogo(e.target.value)} style={{ width: '100%', padding: 8, marginTop: 4 }} placeholder="https://..." />
        </label>
        <label>
          Message de bas de ticket
          <input value={footer} onChange={e => setFooter(e.target.value)} style={{ width: '100%', padding: 8, marginTop: 4 }} />
        </label>
        <button onClick={handleSave} style={{ marginTop: 12, padding: '10px 0', background: '#222', color: '#fff', border: 'none', borderRadius: 4, fontWeight: 600, fontSize: 16, cursor: 'pointer' }}>Sauvegarder</button>
        {saved && <div style={{ color: 'green', fontWeight: 500 }}>✅ Sauvegardé !</div>}
      </div>
    </div>
  );
};

export default RestaurantInfoSettings;