"use client"

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatCurrency } from '@/lib/utils/currency';
import { InfoIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface KpiMetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  trend?: 'up' | 'down';
  isPercentage?: boolean;
  tooltipText?: string;
  onClick?: () => void;
  className?: string;
}

export default function KpiMetricCard({
  title,
  value,
  icon,
  trend,
  isPercentage = false,
  tooltipText,
  onClick,
  className
}: KpiMetricCardProps) {
  // Format the value based on type
  const formattedValue = isPercentage
    ? `${value.toFixed(1)}%`
    : formatCurrency(value);

  // Get trend color
  const getTrendColor = () => {
    if (!trend) return '';
    return trend === 'up' ? 'text-green-600' : 'text-red-600';
  };

  // Get sparkline (placeholder for now)
  const getSparkline = () => {
    return (
      <div className="h-1 w-16 bg-muted rounded-full overflow-hidden">
        <div 
          className={cn(
            "h-full rounded-full",
            trend === 'up' ? "bg-green-500" : "bg-red-500"
          )}
          style={{ width: '70%' }}
        ></div>
      </div>
    );
  };

  return (
    <Card 
      className={cn(
        "border shadow-sm transition-all duration-200 hover:shadow-md",
        onClick && "cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div className="space-y-2">
            <div className="flex items-center gap-1">
              <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
              {tooltipText && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-3 w-3 text-muted-foreground/70" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">{tooltipText}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <div className="flex flex-col gap-1">
              <h2 className={cn(
                "text-2xl font-bold transition-all duration-300",
                getTrendColor()
              )}>
                {formattedValue}
              </h2>
              {getSparkline()}
            </div>
          </div>
          <div className={cn(
            "p-2 rounded-full",
            trend === 'up' ? "bg-green-100/80" :
            trend === 'down' ? "bg-red-100/80" :
            "bg-primary/10"
          )}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
