'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ShiftConfig } from '@/lib/types/staff';
import { staffService } from '../../../lib/services/staff-service';
import { Clock, Plus, Edit2, Trash2, X } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface ShiftManagerProps {
  onShiftsUpdate: () => void;
}

export default function ShiftManager({ onShiftsUpdate }: ShiftManagerProps) {
  const [shifts, setShifts] = useState<ShiftConfig[]>([]);
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [currentShift, setCurrentShift] = useState<ShiftConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const [name, setName] = useState('');
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  
  const { toast } = useToast();

  // Load shifts from database
  useEffect(() => {
    async function loadShifts() {
      setIsLoading(true);
      try {
        const loadedShifts = await staffService.getAllShifts();
        setShifts(loadedShifts);
      } catch (error) {
        console.error('Error loading shifts:', error);
        toast({
          title: 'Error',
          description: 'Failed to load shifts.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }
    
    loadShifts();
  }, [toast]);

  // Reset form
  const resetForm = () => {
    setName('');
    setStartTime('09:00');
    setEndTime('17:00');
  };

  // Set form for edit
  const setFormForEdit = (shift: ShiftConfig) => {
    setName(shift.name);
    setStartTime(shift.startTime);
    setEndTime(shift.endTime);
    setCurrentShift(shift);
    setIsEditOpen(true);
  };

  // Handle create shift
  const handleCreateShift = async () => {
    if (!name || !startTime || !endTime) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await staffService.createShift({
        name,
        startTime,
        endTime
      });
      
      toast({
        title: "Succès",
        description: "Shift créé avec succès",
      });
      
      resetForm();
      setIsAddOpen(false);
      onShiftsUpdate();
    } catch (error) {
      console.error('Error creating shift:', error);
      toast({
        title: "Erreur",
        description: "Échec de la création du shift",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle update shift
  const handleUpdateShift = async () => {
    if (!currentShift || !name || !startTime || !endTime) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await staffService.updateShift(currentShift.id, {
        name,
        startTime,
        endTime
      });
      
      toast({
        title: "Succès",
        description: "Shift mis à jour avec succès",
      });
      
      resetForm();
      setIsEditOpen(false);
      onShiftsUpdate();
    } catch (error) {
      console.error('Error updating shift:', error);
      toast({
        title: "Erreur",
        description: "Échec de la mise à jour du shift",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete shift
  const handleDeleteShift = async () => {
    if (!currentShift) return;

    setIsLoading(true);
    try {
      await staffService.deleteShift(currentShift.id);
      
      toast({
        title: "Succès",
        description: "Shift supprimé avec succès",
      });
      
      setIsDeleteOpen(false);
      onShiftsUpdate();
    } catch (error) {
      console.error('Error deleting shift:', error);
      toast({
        title: "Erreur",
        description: "Échec de la suppression du shift",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle close modal
  const handleCloseModal = () => {
    setIsAddOpen(false);
    setIsEditOpen(false);
    setIsDeleteOpen(false);
    resetForm();
  };

  // Add default "Matin" and "Soir" shifts
  const handleAddDefaultShifts = async () => {
    setIsLoading(true);
    try {
      await staffService.createShift({ name: 'Matin', startTime: '08:00', endTime: '16:00' });
      await staffService.createShift({ name: 'Soir', startTime: '16:00', endTime: '22:00' });
      
      toast({
        title: 'Shifts Added',
        description: 'Default morning and evening shifts created.',
      });
      
      handleCloseModal();
      onShiftsUpdate();
      
      // Reload shifts to show the newly created ones
      const loadedShifts = await staffService.getAllShifts();
      setShifts(loadedShifts);
    } catch (error) {
      console.error('Error creating default shifts:', error);
      toast({
        title: 'Error',
        description: 'Failed to create default shifts.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="shadow-sm border-muted">
      <CardHeader className="pb-2 space-y-1">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Shifts</CardTitle>
            <CardDescription className="text-sm">Définissez les types de shifts pour la planification du personnel</CardDescription>
          </div>
          <Button size="sm" onClick={() => setIsAddOpen(true)} className="h-8">
            <Plus className="h-3.5 w-3.5 mr-1" />
            Ajouter
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {shifts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <p className="text-sm">Aucun shift défini</p>
            <Button 
              variant="outline" 
              className="mt-3 text-xs h-8"
              onClick={() => setIsAddOpen(true)}
            >
              <Plus className="h-3.5 w-3.5 mr-1" />
              Créez votre premier shift
            </Button>
          </div>
        ) : (
          <div className="grid gap-3 md:grid-cols-3 lg:grid-cols-4">
            {shifts.map((shift) => (
              <div
                key={shift.id}
                className="relative rounded-md border p-3 shadow-sm hover:shadow-md transition-shadow"
                style={{ borderLeftColor: '#4CAF50', borderLeftWidth: '3px' }}
              >
                <div className="absolute top-2 right-2 flex space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setFormForEdit(shift)}
                  >
                    <Edit2 className="h-3.5 w-3.5" />
                    <span className="sr-only">Modifier</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-destructive"
                    onClick={() => {
                      setCurrentShift(shift);
                      setIsDeleteOpen(true);
                    }}
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                    <span className="sr-only">Supprimer</span>
                  </Button>
                </div>
                
                <h3 className="text-sm font-medium mb-1 pr-14">{shift.name}</h3>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{shift.startTime} - {shift.endTime}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Add Shift Dialog */}
      <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Ajouter un shift</DialogTitle>
            <DialogDescription>
              Créez un nouveau type de shift auquel le personnel peut être assigné.
            </DialogDescription>
          </DialogHeader>
          
          {/* Button to add both Matin & Soir shifts at once */}
          <div className="mb-2 flex justify-center">
            <Button
              variant="secondary"
              className="w-full"
              disabled={isLoading}
              onClick={handleAddDefaultShifts}
            >
              Ajouter Matin & Soir
            </Button>
          </div>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Nom du shift</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Shift du matin"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="startTime">Heure de début</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="endTime">Heure de fin</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                resetForm();
                setIsAddOpen(false);
              }}
            >
              Annuler
            </Button>
            <Button 
              onClick={handleCreateShift}
              disabled={isLoading}
            >
              {isLoading && (
                <div className="mr-2 animate-spin rounded-full h-4 w-4 border-b-2 border-background"></div>
              )}
              Créer le shift
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Shift Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Modifier le shift</DialogTitle>
            <DialogDescription>
              Mettez à jour les détails de ce shift.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Nom du shift</Label>
              <Input
                id="edit-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Shift du matin"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-startTime">Heure de début</Label>
                <Input
                  id="edit-startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit-endTime">Heure de fin</Label>
                <Input
                  id="edit-endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                resetForm();
                setIsEditOpen(false);
              }}
            >
              Annuler
            </Button>
            <Button 
              onClick={handleUpdateShift}
              disabled={isLoading}
            >
              {isLoading && (
                <div className="mr-2 animate-spin rounded-full h-4 w-4 border-b-2 border-background"></div>
              )}
              Enregistrer les modifications
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Supprimer le shift</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer ce shift ? Cela le retirera aussi de tous les plannings du personnel.
            </DialogDescription>
          </DialogHeader>
          
          {currentShift && (
            <div className="py-4">
              <div className="bg-muted p-3 rounded-md">
                <h4 className="font-medium">{currentShift.name}</h4>
                <div className="flex items-center text-sm text-muted-foreground mt-1">
                  <Clock className="h-3.5 w-3.5 mr-1.5" />
                  <span>{currentShift.startTime} - {currentShift.endTime}</span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-4">
                Cette action est irréversible et affectera tous les membres du personnel assignés à ce shift.
              </p>
            </div>
          )}
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteOpen(false)}
            >
              Annuler
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDeleteShift}
              disabled={isLoading}
            >
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
} 