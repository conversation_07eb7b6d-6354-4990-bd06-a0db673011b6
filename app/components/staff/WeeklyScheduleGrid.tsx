'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { staffService } from '@/lib/services/staff-service';
import { StaffMember, ShiftConfig, WeeklySchedule, StaffSchedule } from '@/lib/types/staff';
import { Clock, PlusCircle, Edit, CalendarDays, UserCog, XIcon } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface WeeklyScheduleGridProps {
  staff: StaffMember[];
  shifts: ShiftConfig[];
  onScheduleUpdate?: () => void;
}

// Move days outside component to avoid recreation on every render
const WEEKDAYS: { key: keyof WeeklySchedule; label: string }[] = [
  { key: 'monday', label: 'Lun' },
  { key: 'tuesday', label: 'Mar' },
  { key: 'wednesday', label: 'Mer' },
  { key: 'thursday', label: 'Jeu' },
  { key: 'friday', label: 'Ven' },
  { key: 'saturday', label: 'Sam' },
  { key: 'sunday', label: 'Dim' },
];

export function WeeklyScheduleGrid({ staff, shifts, onScheduleUpdate }: WeeklyScheduleGridProps) {
  const [selectedDay, setSelectedDay] = useState<keyof WeeklySchedule>('monday');
  const [selectedShift, setSelectedShift] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [availableStaff, setAvailableStaff] = useState<StaffMember[]>([]);
  const [selectedStaffIds, setSelectedStaffIds] = useState<string[]>([]);
  const { toast } = useToast();

  // 🔧 FIXED: Use useMemo to process staff assignments from props instead of separate loading
  const staffByShift = useMemo(() => {
    console.log('🔍 DEBUG: Processing staff assignments from props...');
    console.log('🔍 DEBUG: Staff input:', staff.length, staff.map(s => ({ 
      id: s.id, 
      name: s.name, 
      status: s.status,
      hasSchedule: !!s.schedule,
      scheduleStructure: s.schedule ? Object.keys(s.schedule) : 'no schedule'
    })));
    console.log('🔍 DEBUG: Shifts input:', shifts.length, shifts.map(s => ({ id: s.id, name: s.name })));
    
    const newStaffByShift: Record<string, StaffMember[]> = {};
    
    // Initialize empty arrays for all shift-day combinations
    for (const shift of shifts) {
      for (const day of WEEKDAYS) {
        const key = `${shift.id}_${day.key}`;
        newStaffByShift[key] = [];
      }
    }
    
    console.log('🔍 DEBUG: Initialized keys:', Object.keys(newStaffByShift));
    
    // Process each staff member's schedule
    for (const member of staff) {
      console.log(`🔍 DEBUG: Processing ${member.name}:`, {
        status: member.status,
        hasSchedule: !!member.schedule,
        scheduleData: member.schedule
      });
      
      // Only process active staff with schedules
      if (member.status !== 'ACTIVE') {
        console.log(`⚠️ DEBUG: Skipping ${member.name} - status is ${member.status}`);
        continue;
      }
      
      if (!member.schedule?.weeklySchedule) {
        console.log(`⚠️ DEBUG: Skipping ${member.name} - no weeklySchedule:`, member.schedule);
        continue;
      }
      
      const weeklySchedule = member.schedule.weeklySchedule;
      console.log(`✅ DEBUG: Processing ${member.name} schedule:`, weeklySchedule);
      
      // For each day in their schedule
      Object.entries(weeklySchedule).forEach(([day, shiftIds]) => {
        console.log(`🔍 DEBUG: Day ${day} for ${member.name}:`, shiftIds);
        
        if (!Array.isArray(shiftIds) || shiftIds.length === 0) {
          console.log(`⚠️ DEBUG: Skipping day ${day} - no shifts or not array:`, shiftIds);
          return;
        }
        
        // For each shift assigned on this day
        shiftIds.forEach((shiftId: string) => {
          console.log(`🔍 DEBUG: Processing shift ${shiftId} for ${member.name} on ${day}`);
          
          if (!shiftId) {
            console.log(`⚠️ DEBUG: Skipping empty shiftId`);
            return;
          }
          
          // Verify the shift exists in our current shifts list
          const shiftExists = shifts.some(s => s.id === shiftId);
          console.log(`🔍 DEBUG: Shift ${shiftId} exists:`, shiftExists);
          
          if (shiftExists) {
            const key = `${shiftId}_${day}`;
            console.log(`✅ DEBUG: Adding ${member.name} to key ${key}`);
            
            if (key in newStaffByShift) {
              newStaffByShift[key].push(member);
              console.log(`✅ DEBUG: Successfully added ${member.name} to ${key}. Total now:`, newStaffByShift[key].length);
            } else {
              console.log(`❌ DEBUG: Key ${key} not found in newStaffByShift`);
            }
          } else {
            console.log(`⚠️ DEBUG: Shift ${shiftId} not found in shifts list`);
          }
        });
      });
    }
    
    console.log('🔍 DEBUG: Final staffByShift result:', {
      totalKeys: Object.keys(newStaffByShift).length,
      keysWithStaff: Object.entries(newStaffByShift).filter(([_, staff]) => staff.length > 0).map(([key, staff]) => ({ key, count: staff.length })),
      fullResult: newStaffByShift
    });
    
    return newStaffByShift;
  }, [staff, shifts]); // Removed 'days' from dependencies since it's now constant

  // Open assign dialog with available staff - FIXED to target specific block
  const handleOpenAssignDialog = (shiftId: string, dayKey: keyof WeeklySchedule) => {
    console.log(`🎯 Opening assign dialog for specific block: shift ${shiftId} on ${dayKey}`);
    
    setSelectedShift(shiftId);
    setSelectedDay(dayKey); // Ensure we're targeting the correct day
    
    // Get currently assigned staff for this SPECIFIC shift and day combination
    const key = `${shiftId}_${dayKey}`;
    const assignedStaff = staffByShift[key] || [];
    const assignedStaffIds = assignedStaff.map(s => s.id);
    
    console.log(`📋 Currently assigned staff for ${key} (${assignedStaffIds.length}):`, assignedStaffIds);
    
    // Set the selected staff IDs to match current assignments
    setSelectedStaffIds(assignedStaffIds);
    
    // Filter out inactive staff
    const activeStaff = staff.filter(s => s.status === 'ACTIVE');
    setAvailableStaff(activeStaff);
    
    setIsAssignDialogOpen(true);
  };

  // 🔧 FIXED: Simplified staff assignment without redundant data loading
  const handleAssignStaff = async () => {
    if (!selectedShift) return;

    setIsLoading(true);
    
    try {
      console.log(`🔄 Updating assignments for shift ${selectedShift} on ${selectedDay}`);
      
      // Get currently assigned staff
      const key = `${selectedShift}_${selectedDay}`;
      const currentlyAssigned = staffByShift[key] || [];
      const currentlyAssignedIds = currentlyAssigned.map(s => s.id);
      
      // Find which staff to add and remove
      const toAdd = selectedStaffIds.filter(id => !currentlyAssignedIds.includes(id));
      const toRemove = currentlyAssignedIds.filter(id => !selectedStaffIds.includes(id));
      
      console.log(`Adding ${toAdd.length} staff, removing ${toRemove.length} staff`);
      
      // Helper to update a single staff member's schedule
      const updateStaffSchedule = async (staffId: string, isAdding: boolean) => {
        try {
          const staffMember = availableStaff.find(s => s.id === staffId);
          if (!staffMember) return false;
          
          // Get current schedule or create empty one
          const currentSchedule = await staffService.getStaffSchedule(staffId);
          const weeklySchedule: WeeklySchedule = currentSchedule?.weeklySchedule || {
            monday: [],
            tuesday: [],
            wednesday: [],
            thursday: [],
            friday: [],
            saturday: [],
            sunday: []
          };
          
          // Ensure arrays exist for all days
          WEEKDAYS.forEach(day => {
            if (!Array.isArray(weeklySchedule[day.key])) {
              weeklySchedule[day.key] = [];
            }
          });
          
          // Get the current shifts for the selected day
          const currentShifts = weeklySchedule[selectedDay] || [];
          const hasShift = currentShifts.includes(selectedShift);
          
          // Only update if needed
          if ((isAdding && !hasShift) || (!isAdding && hasShift)) {
            if (isAdding) {
              // Add the shift
              weeklySchedule[selectedDay] = [...currentShifts, selectedShift];
              console.log(`✅ Adding ${selectedShift} to ${staffMember.name}'s schedule on ${selectedDay}`);
            } else {
              // Remove the shift  
              weeklySchedule[selectedDay] = currentShifts.filter(id => id !== selectedShift);
              console.log(`✅ Removing ${selectedShift} from ${staffMember.name}'s schedule on ${selectedDay}`);
            }
            
            // Update schedule in database
            await staffService.updateStaffWeeklySchedule(staffId, weeklySchedule);
            return true;
          }
          
          return false; // No change needed
        } catch (error) {
          console.error(`❌ Error updating schedule for staff ${staffId}:`, error);
          return false;
        }
      };
      
      // Process additions and removals
      const results = await Promise.all([
        ...toAdd.map(staffId => updateStaffSchedule(staffId, true)),
        ...toRemove.map(staffId => updateStaffSchedule(staffId, false))
      ]);
      
      const successful = results.filter(Boolean).length;
      const total = toAdd.length + toRemove.length;
      
      if (successful > 0) {
        toast({
          title: "Planning mis à jour",
          description: `${successful} sur ${total} modifications appliquées avec succès`,
        });
        
        // Notify parent to refresh data
        if (onScheduleUpdate) {
          onScheduleUpdate();
        }
      } else if (total > 0) {
        toast({
          title: "Aucune modification",
          description: "Aucune modification n'était nécessaire",
        });
      }
      
      setIsAssignDialogOpen(false);
      setSelectedShift(null);
      setSelectedStaffIds([]);
    } catch (error) {
      console.error('Error updating staff assignments:', error);
      toast({
        title: "Erreur",
        description: "Échec de la mise à jour du planning",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle removing a staff member from a specific shift
  const handleRemoveStaff = async (staffId: string, shiftId: string, day: keyof WeeklySchedule) => {
    try {
      console.log(`🗑️ Removing staff ${staffId} from shift ${shiftId} on ${day}`);
      
      // Get current schedule
      const currentSchedule = await staffService.getStaffSchedule(staffId);
      if (!currentSchedule) {
        console.warn('No schedule found for staff member');
        return;
      }
      
      const weeklySchedule = { ...currentSchedule.weeklySchedule };
      const dayShifts = weeklySchedule[day] || [];
      
      // Remove the shift from the day
      weeklySchedule[day] = dayShifts.filter(id => id !== shiftId);
      
      // Update the schedule
      await staffService.updateStaffWeeklySchedule(staffId, weeklySchedule);
      
      toast({
        title: "Personnel retiré",
        description: "Le membre du personnel a été retiré du shift",
      });
      
      // Notify parent to refresh data
      if (onScheduleUpdate) {
        onScheduleUpdate();
      }
    } catch (error) {
      console.error('Error removing staff from shift:', error);
      toast({
        title: "Erreur",
        description: "Échec de la suppression du planning",
        variant: "destructive",
      });
    }
  };

  // Get role badge variant
  const getRoleBadgeVariant = (role: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (role) {
      case 'MANAGER':
        return "destructive";
      case 'CHEF':
      case 'WAITER':
        return "default";
      case 'DELIVERY':
        return "outline";
      default:
        return "secondary";
    }
  };

  return (
    <Card className="shadow-sm border-muted">
      <CardHeader className="pb-2 space-y-1">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Planning hebdomadaire</CardTitle>
            <CardDescription className="text-sm">Assignez le personnel aux shifts avec leurs heures de début et de fin</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {shifts.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <p className="text-sm">Aucun shift défini pour l'instant</p>
            <p className="text-xs mt-1">Créez des shifts pour gérer les plannings du personnel</p>
          </div>
        ) : (
          <div>
            {/* Mobile Day Selector */}
            <div className="flex overflow-x-auto md:hidden mb-4 pb-1 -mx-1 px-1">
              {WEEKDAYS.map((day) => (
                <button
                  key={day.key}
                  className={`px-3 py-1.5 text-xs border rounded-md shrink-0 mr-2 ${
                    selectedDay === day.key 
                      ? "bg-primary text-primary-foreground border-primary" 
                      : "bg-background text-muted-foreground border-border"
                  }`}
                  onClick={() => setSelectedDay(day.key)}
                >
                  {day.label}
                </button>
              ))}
            </div>
            
            <div className="overflow-auto">
              <div className="grid grid-cols-8 gap-2 min-w-[700px]">
                {/* Header - Days of week */}
                <div className="font-medium text-left pl-2 text-sm">Shifts</div>
                {WEEKDAYS.map((day) => (
                  <div
                    key={day.key}
                    className={`font-medium text-center px-2 py-1 rounded-md hidden md:block text-sm cursor-pointer hover:bg-muted transition-colors ${
                      selectedDay === day.key ? "bg-primary text-primary-foreground" : ""
                    }`}
                    onClick={() => setSelectedDay(day.key)}
                  >
                    {day.label}
                  </div>
                ))}
                
                {/* Shifts and staff rows */}
                {shifts.length === 0 ? (
                  <div className="col-span-8 text-center py-4 text-muted-foreground">
                    <p className="text-sm">Aucun shift défini</p>
                  </div>
                ) : (
                  shifts.map((shift) => (
                    <React.Fragment key={shift.id}>
                      {/* Shift info */}
                      <div className="flex flex-col justify-center border-t pt-2">
                        <div className="text-xs font-medium mb-1">{shift.name}</div>
                        <div className="flex items-center text-xs text-muted-foreground gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{shift.startTime} - {shift.endTime}</span>
                        </div>
                      </div>
                      
                      {/* Days cells - Show all days */}
                      {WEEKDAYS.map((day) => {
                        // Get assigned staff for this shift on this day
                        const assignedStaff = staffByShift[`${shift.id}_${day.key}`] || [];
                        
                        return (
                          <div
                            key={day.key}
                            className={`border rounded-md ${
                              selectedDay === day.key ? "border-primary bg-primary/5" : ""
                            } p-2 relative hidden md:block`}
                          >
                            <div className="flex justify-between mb-1">
                              <div className="text-xs font-medium">{assignedStaff.length > 0 ? `${assignedStaff.length} membre${assignedStaff.length > 1 ? 's' : ''}` : ''}</div>
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-5 w-5 absolute top-1 right-1"
                                onClick={() => handleOpenAssignDialog(shift.id, day.key)}
                              >
                                <UserCog className="h-3 w-3" />
                              </Button>
                            </div>
                            
                            <div className="space-y-1 max-h-20 overflow-y-auto pr-1 text-xs">
                              {assignedStaff.length > 0 ? (
                                assignedStaff.map((member) => (
                                  <div key={member.id} className="flex items-center justify-between text-xs">
                                    <span className="truncate">{member.name}</span>
                                    <Badge variant={getRoleBadgeVariant(member.role)} className="text-[10px] px-1 h-4 ml-1">
                                      {member.role.replace('_', ' ')}
                                    </Badge>
                                  </div>
                                ))
                              ) : (
                                <div className="text-xs text-muted-foreground text-center py-1">
                                  -
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                      
                      {/* Mobile day view */}
                      <div className="md:hidden col-span-7 border rounded-md p-3 mt-2">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <h4 className="text-xs font-medium">{shift.name} • {selectedDay.charAt(0).toUpperCase() + selectedDay.slice(1)}</h4>
                            <div className="flex items-center text-xs text-muted-foreground mt-1">
                              <Clock className="h-3 w-3 mr-1" />
                              <span>{shift.startTime} - {shift.endTime}</span>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 px-2"
                            onClick={() => handleOpenAssignDialog(shift.id, selectedDay)}
                          >
                            <UserCog className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                        
                        <div className="space-y-1 mb-2">
                          {(staffByShift[`${shift.id}_${selectedDay}`] || []).length > 0 ? (
                            (staffByShift[`${shift.id}_${selectedDay}`] || []).map((member) => (
                              <div key={member.id} className="flex items-center justify-between text-xs py-1 border-b border-muted last:border-0">
                                <span>{member.name}</span>
                                <Badge variant={getRoleBadgeVariant(member.role)} className="text-[10px] px-1 h-4">
                                  {member.role.replace('_', ' ')}
                                </Badge>
                              </div>
                            ))
                          ) : (
                            <div className="text-xs text-muted-foreground text-center py-1">
                              Aucun membre assigné
                            </div>
                          )}
                        </div>
                      </div>
                    </React.Fragment>
                  ))
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
      
      {/* Assign Staff Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
            <DialogTitle className="text-base">Assigner du personnel au shift</DialogTitle>
            <DialogDescription className="text-sm">
              {selectedShift && shifts.find(s => s.id === selectedShift)?.name} • {selectedDay.charAt(0).toUpperCase() + selectedDay.slice(1)}
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-3">
            {selectedShift && (
              <div className="mb-3 p-2 bg-muted/40 rounded-md">
                <div className="flex items-center text-xs">
                  <Clock className="h-3 w-3 mr-1.5 text-muted-foreground" />
                  <span>
                    {shifts.find(s => s.id === selectedShift)?.startTime} - {shifts.find(s => s.id === selectedShift)?.endTime}
                  </span>
                </div>
              </div>
            )}
            <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
              {availableStaff.length === 0 ? (
                <div className="text-center py-3 text-muted-foreground text-sm">
                  Aucun membre actif disponible
                </div>
              ) : (
                availableStaff.map((member) => (
                  <div 
                    key={member.id} 
                    className="flex items-center justify-between p-2 border rounded-md hover:bg-muted/30"
                  >
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id={`staff-${member.id}`}
                        checked={selectedStaffIds.includes(member.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedStaffIds([...selectedStaffIds, member.id]);
                          } else {
                            setSelectedStaffIds(selectedStaffIds.filter(id => id !== member.id));
                          }
                        }}
                      />
                      <Label 
                        htmlFor={`staff-${member.id}`}
                        className="text-sm cursor-pointer"
                      >
                        {member.name}
                      </Label>
                    </div>
                    <Badge variant={getRoleBadgeVariant(member.role)} className="text-xs">
                      {member.role.replace('_', ' ')}
                    </Badge>
                  </div>
                ))
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" className="mr-2" onClick={() => setIsAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAssignStaff} 
              disabled={isLoading}
            >
              {isLoading && (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
              )}
              Save Assignments
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}