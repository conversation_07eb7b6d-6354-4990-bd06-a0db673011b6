'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { format, parseISO, isSameDay, isToday, addDays, subDays, startOfWeek, endOfWeek } from 'date-fns';
import { CalendarIcon, Clock, UserCheck, UserX, MoreHorizontal, Check, X, CalendarDays, ArrowLeftRight, AlertCircle, Edit, ArrowLeft, ArrowRight, Users } from 'lucide-react';
import { cn } from "@/lib/utils";
import { staffService } from '@/lib/services/staff-service';
import { StaffMember, ShiftConfig, AttendanceRecord, WeeklySchedule } from '@/lib/types/staff';
import { useToast } from '@/components/ui/use-toast';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ShiftAttendanceProps {
  staff: StaffMember[];
  shifts: ShiftConfig[];
  onAttendanceUpdate?: () => void;
}

export function ShiftAttendance({ staff, shifts, onAttendanceUpdate }: ShiftAttendanceProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isLoading, setIsLoading] = useState(false);
  const [staffSchedule, setStaffSchedule] = useState<Record<string, StaffMember[]>>({});
  const [staffAttendance, setStaffAttendance] = useState<AttendanceRecord[]>([]);
  const [activeTab, setActiveTab] = useState('today');
  const [viewMode, setViewMode] = useState<'detailed' | 'summary'>('detailed');
  const [isRecordDialogOpen, setIsRecordDialogOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [selectedShift, setSelectedShift] = useState<ShiftConfig | null>(null);
  const [attendanceStatus, setAttendanceStatus] = useState<'present' | 'late'>('present');
  const [attendanceNotes, setAttendanceNotes] = useState('');
  const [updatingAttendance, setUpdatingAttendance] = useState(false);
  const [isReplacementOpen, setIsReplacementOpen] = useState(false);
  const [replacementStaff, setReplacementStaff] = useState<StaffMember | null>(null);
  const [replacementShift, setReplacementShift] = useState<ShiftConfig | null>(null);
  const { toast } = useToast();

  // Get day name (Monday, Tuesday, etc.) from the selected date
  const getDayName = () => {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayIndex = selectedDate.getDay();
    const dayName = days[dayIndex];
    // Cast as a valid day for WeeklySchedule
    return dayName as keyof WeeklySchedule;
  };

  // Handle date navigation
  const goToPreviousDay = () => {
    setSelectedDate(prev => subDays(prev, 1));
  };

  const goToNextDay = () => {
    setSelectedDate(prev => addDays(prev, 1));
  };

  const goToToday = () => {
    setSelectedDate(new Date());
  };

  // Load staff schedule and attendance for the selected date
  useEffect(() => {
    if (staff.length > 0 && shifts.length > 0) {
      loadScheduleForDay();
    }
  }, [selectedDate, staff.length, shifts.length]);

  // Helper function to check if a staff member is present
  const isStaffPresent = (staffId: string, shiftId: string) => {
    return staffAttendance.some(record => 
      record.staffId === staffId && 
      record.shiftId === shiftId && 
      (record.status === 'present' || record.status === 'late')
    );
  };

  // Helper function to get attendance record for a staff member and shift
  const getAttendanceRecord = (staffId: string, shiftId: string) => {
    return staffAttendance.find(record => 
      record.staffId === staffId && 
      record.shiftId === shiftId
    );
  };

  // Load staff scheduled for the selected day
  const loadScheduleForDay = async () => {
    if (isLoading) return; // Prevent concurrent loads
    
    setIsLoading(true);
    try {
      // Get day name
      const dayName = getDayName();
      
      // Get staff scheduled for this day
      const staffForDay = await staffService.getStaffForDay(dayName);

      // Group staff by their shifts for this day
      const staffByShift: Record<string, StaffMember[]> = {};
      
      // Initialize empty arrays for all shifts
      for (const shift of shifts) {
        staffByShift[shift.id] = [];
      }

      // For each staff member, check their schedule to assign them to the right shifts
      for (const member of staffForDay) {
        if (member.status !== 'ACTIVE') continue;
        
        // If the staff has a schedule for this day, use it to assign them to shifts
        if (member.schedule?.weeklySchedule) {
          const shiftIds = member.schedule.weeklySchedule[dayName] || [];
          
          // Add staff to each shift they're scheduled for
          for (const shiftId of shiftIds) {
            if (staffByShift[shiftId]) {
              staffByShift[shiftId].push(member);
            }
          }
        }
      }
      
      setStaffSchedule(staffByShift);
      
      // Load attendance records for this day
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      const allStaffAttendance: AttendanceRecord[] = [];
      
      // We need to get attendance for each staff member
      for (const shift of shifts) {
        const staffInShift = staffByShift[shift.id] || [];
        for (const member of staffInShift) {
          try {
            // Get attendance records in date range (just for this day)
            const attendance = await staffService.getStaffAttendance(
              member.id,
              dateString,
              dateString
            );
            allStaffAttendance.push(...attendance);
          } catch (err) {
            console.error(`Error loading attendance for staff ${member.id}:`, err);
            // Continue with other staff members
          }
        }
      }
      
      setStaffAttendance(allStaffAttendance);
      
      // Notify parent component
      if (onAttendanceUpdate) {
        onAttendanceUpdate();
      }
    } catch (error) {
      console.error('Error loading schedule:', error);
      
      // Enhanced error handling for database issues
      if (error instanceof Error) {
        if (error.message.includes('Database not ready') || error.message.includes('initialization timeout')) {
          toast({
            title: "⚠️ Base de données en cours d'initialisation",
            description: "Veuillez patienter un moment et réessayer",
            variant: "destructive",
            duration: 5000,
          });
        } else {
          toast({
            title: "Erreur",
            description: "Échec de chargement des données de présence",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Erreur",
          description: "Échec de chargement des données de présence",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Quick mark present without dialog - ENHANCED with better feedback
  const handleQuickMarkPresent = async (staff: StaffMember, shift: ShiftConfig) => {
    setUpdatingAttendance(true);
    try {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      
      console.log(`⚡ Quick marking ${staff.name} as present for ${shift.name}`, {
        staffId: staff.id,
        shiftId: shift.id,
        date: dateString,
        staffServiceExists: !!staffService.recordShiftAttendance
      });
      
      // Record attendance
      const result = await staffService.recordShiftAttendance(
        staff.id,
        shift.id,
        {
          date: dateString,
          status: 'present',
          notes: 'Marquage rapide - présent'
        }
      );
      
      console.log('✅ Attendance record result:', result);
      
      // Show immediate success feedback with emoji
      toast({
        title: "✅ Présence validée",
        description: `${staff.name} marqué présent pour ${shift.name}`,
        duration: 2000,
      });
      
      // Refresh data
      await loadScheduleForDay();
      
      // Notify parent component
      if (onAttendanceUpdate) {
        onAttendanceUpdate();
      }
      
    } catch (error) {
      console.error('❌ Error marking attendance:', {
        error,
        staffId: staff.id,
        shiftId: shift.id,
        staffName: staff.name,
        shiftName: shift.name
      });
      
      // Enhanced error handling for database issues
      if (error instanceof Error) {
        if (error.message.includes('Database not ready')) {
          toast({
            title: "⚠️ Base de données en cours d'initialisation",
            description: "Veuillez patienter un moment et réessayer",
            variant: "destructive",
            duration: 5000,
          });
        } else if (error.message.includes('initialization timeout')) {
          toast({
            title: "⏱️ Timeout d'initialisation",
            description: "La base de données prend trop de temps. Actualisez la page.",
            variant: "destructive",
            duration: 6000,
          });
        } else {
          toast({
            title: "❌ Erreur",
            description: `Impossible de valider la présence de ${staff.name}: ${error.message}`,
            variant: "destructive",
            duration: 4000,
          });
        }
      } else {
        toast({
          title: "❌ Erreur",
          description: `Impossible de valider la présence de ${staff.name}`,
          variant: "destructive",
          duration: 4000,
        });
      }
    } finally {
      setUpdatingAttendance(false);
    }
  };

  // Mark as late - ENHANCED with better feedback
  const handleQuickMarkLate = async (staff: StaffMember, shift: ShiftConfig) => {
    setUpdatingAttendance(true);
    try {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      
      console.log(`⚡ Quick marking ${staff.name} as late for ${shift.name}`);
      
      // Record attendance
      await staffService.recordShiftAttendance(
        staff.id,
        shift.id,
        {
          date: dateString,
          status: 'late',
          notes: 'Marquage rapide - en retard'
        }
      );
      
      // Show immediate success feedback with emoji
      toast({
        title: "⏰ Retard enregistré",
        description: `${staff.name} marqué en retard pour ${shift.name}`,
        duration: 2000,
      });
      
      // Refresh data
      await loadScheduleForDay();
      
      // Notify parent component
      if (onAttendanceUpdate) {
        onAttendanceUpdate();
      }
      
    } catch (error) {
      console.error('❌ Error marking attendance:', error);
      
      // Enhanced error handling for database issues
      if (error instanceof Error) {
        if (error.message.includes('Database not ready')) {
          toast({
            title: "⚠️ Base de données en cours d'initialisation",
            description: "Veuillez patienter un moment et réessayer",
            variant: "destructive",
            duration: 5000,
          });
        } else if (error.message.includes('initialization timeout')) {
          toast({
            title: "⏱️ Timeout d'initialisation", 
            description: "La base de données prend trop de temps. Actualisez la page.",
            variant: "destructive",
            duration: 6000,
          });
        } else {
          toast({
            title: "❌ Erreur",
            description: `Impossible d'enregistrer le retard de ${staff.name}: ${error.message}`,
            variant: "destructive",
            duration: 4000,
          });
        }
      } else {
        toast({
          title: "❌ Erreur",
          description: `Impossible d'enregistrer le retard de ${staff.name}`,
          variant: "destructive",
          duration: 4000,
        });
      }
    } finally {
      setUpdatingAttendance(false);
    }
  };

  // Mark as absent - NEW function for quick absent marking
  const handleQuickMarkAbsent = async (staff: StaffMember, shift: ShiftConfig) => {
    setUpdatingAttendance(true);
    try {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      
      console.log(`⚡ Quick marking ${staff.name} as absent for ${shift.name}`);
      
      // Record attendance
      await staffService.recordShiftAttendance(
        staff.id,
        shift.id,
        {
          date: dateString,
          status: 'absent',
          notes: 'Marquage rapide - absent'
        }
      );
      
      // Show immediate success feedback with emoji
      toast({
        title: "❌ Absence enregistrée",
        description: `${staff.name} marqué absent pour ${shift.name}`,
        duration: 2000,
      });
      
      // Refresh data
      await loadScheduleForDay();
      
      // Notify parent component
      if (onAttendanceUpdate) {
        onAttendanceUpdate();
      }
      
    } catch (error) {
      console.error('❌ Error marking attendance:', error);
      
      // Enhanced error handling for database issues
      if (error instanceof Error) {
        if (error.message.includes('Database not ready')) {
          toast({
            title: "⚠️ Base de données en cours d'initialisation",
            description: "Veuillez patienter un moment et réessayer",
            variant: "destructive",
            duration: 5000,
          });
        } else if (error.message.includes('initialization timeout')) {
          toast({
            title: "⏱️ Timeout d'initialisation", 
            description: "La base de données prend trop de temps. Actualisez la page.",
            variant: "destructive",
            duration: 6000,
          });
        } else {
          toast({
            title: "❌ Erreur",
            description: `Impossible d'enregistrer l'absence de ${staff.name}: ${error.message}`,
            variant: "destructive",
            duration: 4000,
          });
        }
      } else {
        toast({
          title: "❌ Erreur",
          description: `Impossible d'enregistrer l'absence de ${staff.name}`,
          variant: "destructive",
          duration: 4000,
        });
      }
    } finally {
      setUpdatingAttendance(false);
    }
  };

  // Open detailed attendance record dialog
  const handleOpenRecordDialog = (staff: StaffMember, shift: ShiftConfig) => {
    setSelectedStaff(staff);
    setSelectedShift(shift);
    
    // Check if there's already a record for this staff and shift
    const existingRecord = getAttendanceRecord(staff.id, shift.id);
    
    if (existingRecord) {
      setAttendanceStatus(existingRecord.status as 'present' | 'late');
      setAttendanceNotes(existingRecord.notes || '');
    } else {
      // Default values for new record
      setAttendanceStatus('present');
      setAttendanceNotes('');
    }
    
    setIsRecordDialogOpen(true);
  };

  // Save attendance record from dialog - ENHANCED with better feedback
  const handleRecordAttendance = async () => {
    if (!selectedStaff || !selectedShift) return;
    
    setUpdatingAttendance(true);
    try {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      
      console.log(`📝 Recording attendance for ${selectedStaff.name} on ${dateString}:`, {
        staffId: selectedStaff.id,
        shiftId: selectedShift.id,
        status: attendanceStatus,
        notes: attendanceNotes
      });
      
      // Record attendance
      await staffService.recordShiftAttendance(
        selectedStaff.id,
        selectedShift.id,
        {
          date: dateString,
          status: attendanceStatus,
          notes: attendanceNotes
        }
      );
      
      console.log('✅ Attendance recorded successfully');
      
      // Show immediate success feedback
      toast({
        title: "✅ Présence validée",
        description: `${selectedStaff.name} marqué comme ${attendanceStatus === 'present' ? 'présent' : 'en retard'} pour ${selectedShift.name}`,
        duration: 3000,
      });
      
      // Close the dialog first for immediate feedback
      setIsRecordDialogOpen(false);
      
      // Then refresh data
      await loadScheduleForDay();
      
      // Notify parent component
      if (onAttendanceUpdate) {
        onAttendanceUpdate();
      }
      
    } catch (error) {
      console.error('❌ Error recording attendance:', error);
      toast({
        title: "❌ Erreur de validation",
        description: `Impossible d'enregistrer la présence de ${selectedStaff.name}. Veuillez réessayer.`,
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setUpdatingAttendance(false);
    }
  };

  // Handle staff replacement - LEAN & SIMPLE
  const handleOpenReplacement = (originalStaff: StaffMember, shift: ShiftConfig) => {
    setReplacementStaff(originalStaff);
    setReplacementShift(shift);
    setIsReplacementOpen(true);
  };

  const handleReplaceStaff = async (newStaffId: string) => {
    if (!replacementStaff || !replacementShift) return;
    
    setUpdatingAttendance(true);
    try {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      const newStaff = staff.find(s => s.id === newStaffId);
      if (!newStaff) return;
      
      const dayName = format(selectedDate, 'EEEE').toLowerCase() as keyof WeeklySchedule;
      
      // 🔄 ACTUAL REPLACEMENT: Update shift assignments
      
      // 1. Remove original staff from this shift
      const originalStaffSchedule = await staffService.getStaffSchedule(replacementStaff.id);
      if (originalStaffSchedule?.weeklySchedule) {
        const updatedOriginalSchedule = { ...originalStaffSchedule.weeklySchedule };
        updatedOriginalSchedule[dayName] = (updatedOriginalSchedule[dayName] || [])
          .filter(shiftId => shiftId !== replacementShift.id);
        
        await staffService.updateStaffWeeklySchedule(replacementStaff.id, updatedOriginalSchedule);
      }
      
      // 2. Add replacement staff to this shift
      const newStaffSchedule = await staffService.getStaffSchedule(newStaffId);
      const updatedNewSchedule = newStaffSchedule?.weeklySchedule || {
        monday: [], tuesday: [], wednesday: [], thursday: [], friday: [], saturday: [], sunday: []
      };
      
      if (!updatedNewSchedule[dayName].includes(replacementShift.id)) {
        updatedNewSchedule[dayName] = [...updatedNewSchedule[dayName], replacementShift.id];
        await staffService.updateStaffWeeklySchedule(newStaffId, updatedNewSchedule);
      }
      
      // 3. Record attendance for both staff
      // Mark original staff as absent
      await staffService.recordShiftAttendance(
        replacementStaff.id,
        replacementShift.id,
        {
          date: dateString,
          status: 'absent',
          notes: `🔄 Remplacé par ${newStaff.name}`
        }
      );
      
      // Mark replacement staff as present
      await staffService.recordShiftAttendance(
        newStaffId,
        replacementShift.id,
        {
          date: dateString,
          status: 'present',
          notes: `🔄 Remplace ${replacementStaff.name}`
        }
      );
      
      toast({
        title: "✅ Remplacement effectué",
        description: `${newStaff.name} remplace définitivement ${replacementStaff.name} pour ${replacementShift.name}`,
        duration: 4000,
      });
      
      setIsReplacementOpen(false);
      await loadScheduleForDay();
      
      if (onAttendanceUpdate) {
        onAttendanceUpdate();
      }
      
    } catch (error) {
      console.error('❌ Error replacing staff:', error);
      toast({
        title: "❌ Erreur de remplacement",
        description: "Impossible d'effectuer le remplacement",
        variant: "destructive",
      });
    } finally {
      setUpdatingAttendance(false);
    }
  };

  // Get available replacement staff
  const getAvailableReplacements = () => {
    if (!replacementStaff || !replacementShift) return [];
    
    // Get staff already assigned to this shift
    const staffInShift = staffSchedule[replacementShift.id] || [];
    const staffIdsInShift = staffInShift.map(s => s.id);
    
    return staff.filter(member => 
      member.status === 'ACTIVE' && 
      member.id !== replacementStaff.id &&
      !staffIdsInShift.includes(member.id) && // 🚫 Exclude staff already in this shift
      !isStaffPresent(member.id, replacementShift.id)
    );
  };

  // Render a badge for attendance status
  const renderAttendanceStatus = (record?: AttendanceRecord) => {
    if (!record) {
      return (
        <Badge variant="outline" className="text-muted-foreground">
          Non enregistrée
        </Badge>
      );
    }
    
    if (record.status === 'present') {
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
          <UserCheck className="w-3 h-3 mr-1" /> Présent
        </Badge>
      );
    }
    
    if (record.status === 'late') {
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
          <Clock className="w-3 h-3 mr-1" /> En retard
        </Badge>
      );
    }
    
    return (
      <Badge variant="destructive" className="bg-red-100 text-red-800 hover:bg-red-200">
        <UserX className="w-3 h-3 mr-1" /> Absent
      </Badge>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl">Présence du personnel</CardTitle>
            <CardDescription>Gérer la présence quotidienne aux shifts</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={goToPreviousDay}>
              <ArrowLeft className="h-4 w-4 mr-1" /> Précédent
            </Button>
            <Button variant="outline" size="sm" onClick={goToToday}>
              Aujourd'hui
            </Button>
            <Button variant="outline" size="sm" onClick={goToNextDay}>
              Suivant <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="px-2">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {format(selectedDate, 'dd MMM yyyy')}
              </Button>
            </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          </div>
        </div>
        <div className="flex items-center mt-4 justify-between">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'detailed' | 'summary')}>
            <TabsList>
              <TabsTrigger value="detailed">Vue détaillée</TabsTrigger>
              <TabsTrigger value="summary">Vue synthétique</TabsTrigger>
            </TabsList>
          </Tabs>
          <div className="text-sm font-medium">
            {isToday(selectedDate) ? (
              <Badge variant="default" className="ml-2">Aujourd'hui</Badge>
            ) : (
              <span className="text-muted-foreground">
                {format(selectedDate, 'EEEE')}
              </span>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2">Chargement des données de présence...</span>
          </div>
        ) : (
          <>
            {/* Detailed View - Shows one card per shift */}
            {viewMode === 'detailed' && (
              <div className="space-y-4">
                {shifts.map(shift => {
                  const staffInShift = staffSchedule[shift.id] || [];
                  return (
                    <Card key={shift.id} className="overflow-hidden">
                      <CardHeader className="bg-muted/50 py-2">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="text-lg font-semibold">{shift.name}</h3>
                            <p className="text-xs text-muted-foreground">
                              {shift.startTime} - {shift.endTime}
                            </p>
                          </div>
                          <Badge variant="outline">
                            {staffInShift.length} personnel programmé
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="p-0">
                        {staffInShift.length === 0 ? (
                          <div className="p-4 text-center text-muted-foreground">
                            Aucun personnel programmé pour ce shift
                          </div>
                        ) : (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Membre du personnel</TableHead>
                                <TableHead>Statut</TableHead>
                                <TableHead>Notes</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {staffInShift.map(member => {
                                const attendanceRecord = getAttendanceRecord(member.id, shift.id);
                                const isPresent = !!attendanceRecord && (attendanceRecord.status === 'present' || attendanceRecord.status === 'late');
                                
                                return (
                                  <TableRow key={`${shift.id}-${member.id}`}>
                                    <TableCell className="font-medium">{member.name}</TableCell>
                                    <TableCell>{renderAttendanceStatus(attendanceRecord)}</TableCell>
                                    <TableCell>
                                      {attendanceRecord?.notes ? (
                                        <span className="text-sm">{attendanceRecord.notes}</span>
                                      ) : (
                                        <span className="text-muted-foreground text-sm">Aucune note</span>
                                      )}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      <div className="flex justify-end gap-1">
                                        {!isPresent ? (
                                          <>
                                            <Button 
                                              size="sm" 
                                              variant="default"
                                              onClick={() => handleQuickMarkPresent(member, shift)}
                                              disabled={updatingAttendance}
                                            >
                                              <UserCheck className="h-3.5 w-3.5 mr-1" /> Présent
                                            </Button>
                                            <Button 
                                              size="sm" 
                                              variant="outline"
                                              onClick={() => handleQuickMarkLate(member, shift)}
                                              disabled={updatingAttendance}
                                            >
                                              <Clock className="h-3.5 w-3.5 mr-1" /> En retard
                                            </Button>
                                            <Button 
                                              size="sm" 
                                              variant="destructive"
                                              onClick={() => handleQuickMarkAbsent(member, shift)}
                                              disabled={updatingAttendance}
                                            >
                                              <UserX className="h-3.5 w-3.5 mr-1" /> Absent
                                            </Button>
                                            <Button 
                                              size="sm" 
                                              variant="secondary"
                                              onClick={() => handleOpenReplacement(member, shift)}
                                              disabled={updatingAttendance}
                                            >
                                              <Users className="h-3.5 w-3.5 mr-1" /> Remplacer
                                            </Button>
                                          </>
                                        ) : (
                                          <>
                                            <Button 
                                              size="sm" 
                                              variant="outline"
                                              onClick={() => handleOpenRecordDialog(member, shift)}
                                              disabled={updatingAttendance}
                                            >
                                              <Edit className="h-3.5 w-3.5 mr-1" /> Modifier
                                            </Button>
                                            <Button 
                                              size="sm" 
                                              variant="secondary"
                                              onClick={() => handleOpenReplacement(member, shift)}
                                              disabled={updatingAttendance}
                                            >
                                              <Users className="h-3.5 w-3.5 mr-1" /> Remplacer
                                            </Button>
                                          </>
                                        )}
                                        
                                        <DropdownMenu>
                                          <DropdownMenuTrigger asChild>
                                            <Button size="icon" variant="ghost" className="h-8 w-8">
                                              <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                            <DropdownMenuLabel>Options</DropdownMenuLabel>
                                            <DropdownMenuItem onClick={() => handleOpenRecordDialog(member, shift)}>
                                              <Edit className="h-4 w-4 mr-2" />
                                              {attendanceRecord ? 'Modifier l\'enregistrement' : 'Ajouter un enregistrement'}
                                            </DropdownMenuItem>
                                          </DropdownMenuContent>
                                        </DropdownMenu>
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
            
            {/* Summary View - Shows a single compact table with all shifts */}
            {viewMode === 'summary' && (
              <Card>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Membre du personnel</TableHead>
                        {shifts.map(shift => (
                          <TableHead key={shift.id}>{shift.name}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {staff
                        .filter(member => member.status === 'ACTIVE')
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(member => (
                          <TableRow key={member.id}>
                            <TableCell className="font-medium">{member.name}</TableCell>
                            {shifts.map(shift => {
                              const isScheduled = (staffSchedule[shift.id] || []).some(s => s.id === member.id);
                              const record = getAttendanceRecord(member.id, shift.id);
                              
                              return (
                                <TableCell key={`${member.id}-${shift.id}`}>
                                  {isScheduled ? (
                                    <div className="flex items-center space-x-2">
                                      {record?.status === 'present' && <Check className="h-5 w-5 text-green-500" />}
                                      {record?.status === 'late' && <AlertCircle className="h-5 w-5 text-yellow-500" />}
                                      {!record && <X className="h-5 w-5 text-gray-300" />}
                                      
                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        onClick={() => handleOpenRecordDialog(member, shift)}
                                      >
                                        {record ? 'Modifier' : 'Enregistrer'}
                                      </Button>
                                    </div>
                                  ) : (
                                    <span className="text-muted-foreground text-sm">-</span>
                                  )}
                                </TableCell>
                              );
                            })}
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </CardContent>

      {/* Attendance Record Dialog */}
      <Dialog open={isRecordDialogOpen} onOpenChange={setIsRecordDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Enregistrer la présence</DialogTitle>
            <DialogDescription>
              {selectedStaff?.name} - Shift {selectedShift?.name} le {format(selectedDate, 'dd MMM yyyy')}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Statut
              </Label>
              <Select 
                value={attendanceStatus} 
                onValueChange={(value) => setAttendanceStatus(value as 'present' | 'late')}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Sélectionner le statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="present">Présent</SelectItem>
                  <SelectItem value="late">En retard</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                value={attendanceNotes}
                onChange={(e) => setAttendanceNotes(e.target.value)}
                placeholder="Détails supplémentaires..."
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRecordDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleRecordAttendance} disabled={updatingAttendance}>
              {updatingAttendance ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sauvegarde...
                </>
              ) : (
                'Enregistrer'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Staff Replacement Dialog - LEAN & SIMPLE */}
      <Dialog open={isReplacementOpen} onOpenChange={setIsReplacementOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>🔄 Remplacer le personnel</DialogTitle>
            <DialogDescription>
              {replacementStaff && replacementShift && (
                `Remplacer ${replacementStaff.name} pour le shift ${replacementShift.name}`
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label>Personnel de remplacement</Label>
              <Select onValueChange={handleReplaceStaff}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un remplaçant" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Personnel disponible</SelectLabel>
                    {getAvailableReplacements().map(member => (
                      <SelectItem key={member.id} value={member.id}>
                        {member.name} - {member.role}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            
            {getAvailableReplacements().length === 0 && (
              <div className="text-center py-4 text-muted-foreground">
                <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Aucun personnel disponible pour le remplacement</p>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsReplacementOpen(false)}
              disabled={updatingAttendance}
            >
              Annuler
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}