"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import {
  AlertTriangle,
  Package,
  Trash2,
  CheckCircle,
  XCircle
} from "lucide-react";
import { formatCurrency } from '@/lib/utils/currency';
import { useToast } from "@/hooks/use-toast";
import { OrderDocument, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { processDeliveryFailure } from '@/lib/db/v4/operations/order-ops';

interface DeliveryFailureHandlerProps {
  order: OrderDocument;
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

interface FailedItemState {
  itemIndex: number;
  item: OrderItem;
  failedQuantity: number;
  isSelected: boolean;
}

const FAILURE_REASONS = [
  'Client absent',
  'Adresse incorrecte',
  'Client refuse la commande',
  'Problème de paiement',
  'Commande annulée par le client',
  'Problème de qualité',
  'Autre'
];

export default function DeliveryFailureHandler({ 
  order, 
  onSuccess, 
  trigger 
}: DeliveryFailureHandlerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [failureReason, setFailureReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [failedItems, setFailedItems] = useState<FailedItemState[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Initialize failed items when dialog opens
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Initialize all items as potentially failed
      const initialFailedItems: FailedItemState[] = order.items.map((item, index) => ({
        itemIndex: index,
        item,
        failedQuantity: item.quantity,
        isSelected: false
      }));
      setFailedItems(initialFailedItems);
      setFailureReason('');
      setCustomReason('');
    }
  };

  // Update failed quantity for an item
  const updateFailedQuantity = (itemIndex: number, quantity: number) => {
    setFailedItems(prev => prev.map(failedItem => 
      failedItem.itemIndex === itemIndex 
        ? { ...failedItem, failedQuantity: Math.max(0, Math.min(quantity, failedItem.item.quantity)) }
        : failedItem
    ));
  };

  // Toggle item selection
  const toggleItemSelection = (itemIndex: number, selected: boolean) => {
    setFailedItems(prev => prev.map(failedItem => 
      failedItem.itemIndex === itemIndex 
        ? { ...failedItem, isSelected: selected }
        : failedItem
    ));
  };

  // Select all items
  const selectAllItems = () => {
    setFailedItems(prev => prev.map(item => ({ ...item, isSelected: true })));
  };

  // Deselect all items
  const deselectAllItems = () => {
    setFailedItems(prev => prev.map(item => ({ ...item, isSelected: false })));
  };

  // Calculate totals
  const selectedItems = failedItems.filter(item => item.isSelected && item.failedQuantity > 0);
  const totalFailedValue = selectedItems.reduce((total, failedItem) => {
    const itemPrice = failedItem.item.price + (failedItem.item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
    return total + (itemPrice * failedItem.failedQuantity);
  }, 0);

  // Process delivery failure
  const processFailure = async () => {
    if (selectedItems.length === 0) {
      toast({
        variant: "destructive",
        title: "Aucun article sélectionné",
        description: "Veuillez sélectionner au moins un article en échec"
      });
      return;
    }

    const reason = failureReason === 'Autre' ? customReason : failureReason;
    if (!reason.trim()) {
      toast({
        variant: "destructive",
        title: "Raison requise",
        description: "Veuillez spécifier la raison de l'échec de livraison"
      });
      return;
    }

    try {
      setIsProcessing(true);

      const failureData = selectedItems.map(item => ({
        itemIndex: item.itemIndex,
        quantity: item.failedQuantity
      }));

      await processDeliveryFailure(
        order._id,
        failureData,
        reason,
        order.deliveryPerson?.name
      );

      toast({
        title: "Échec de livraison traité",
        description: `${selectedItems.length} article(s) marqué(s) comme gaspillage`
      });

      setIsOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error processing delivery failure:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible de traiter l'échec de livraison"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <AlertTriangle className="h-4 w-4" />
      Échec de livraison
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Traiter l'échec de livraison
          </DialogTitle>
          <DialogDescription>
            Sélectionnez les articles qui n'ont pas pu être livrés. Ils seront automatiquement marqués comme gaspillage.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Info */}
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Commande #{order._id.slice(-6)}</span>
              <Badge variant="outline">
                {order.deliveryPerson?.name || 'Livreur inconnu'}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              Client: {order.customer?.name || 'Client'} • Total: {formatCurrency(order.total)}
            </div>
          </div>

          {/* Failure Reason */}
          <div className="space-y-3">
            <Label>Raison de l'échec</Label>
            <Select value={failureReason} onValueChange={setFailureReason}>
              <SelectTrigger>
                <SelectValue placeholder="Sélectionnez une raison" />
              </SelectTrigger>
              <SelectContent>
                {FAILURE_REASONS.map(reason => (
                  <SelectItem key={reason} value={reason}>
                    {reason}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {failureReason === 'Autre' && (
              <Textarea
                placeholder="Précisez la raison..."
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="min-h-[60px]"
              />
            )}
          </div>

          {/* Item Selection */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Articles en échec</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllItems}
                  className="h-7 text-xs"
                >
                  Tout sélectionner
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={deselectAllItems}
                  className="h-7 text-xs"
                >
                  Tout désélectionner
                </Button>
              </div>
            </div>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {failedItems.map((failedItem) => {
                const itemPrice = failedItem.item.price + (failedItem.item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
                const itemTotal = itemPrice * failedItem.failedQuantity;
                
                return (
                  <div
                    key={failedItem.itemIndex}
                    className={cn(
                      "p-3 border rounded-lg transition-colors",
                      failedItem.isSelected ? "bg-red-50 border-red-200" : "bg-background"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={failedItem.isSelected}
                        onCheckedChange={(checked) => toggleItemSelection(failedItem.itemIndex, !!checked)}
                        className="mt-1"
                      />
                      
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="font-medium">{failedItem.item.name}</span>
                            {failedItem.item.size && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                {failedItem.item.size}
                              </Badge>
                            )}
                          </div>
                          <span className="font-semibold">
                            {formatCurrency(itemTotal)}
                          </span>
                        </div>
                        
                        {failedItem.item.addons && failedItem.item.addons.length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            Suppléments: {failedItem.item.addons.map(addon => addon.name).join(', ')}
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2">
                          <Label className="text-xs">Quantité en échec:</Label>
                          <Input
                            type="number"
                            min={0}
                            max={failedItem.item.quantity}
                            value={failedItem.failedQuantity}
                            onChange={(e) => updateFailedQuantity(failedItem.itemIndex, parseInt(e.target.value) || 0)}
                            className="h-7 w-16 text-xs"
                            disabled={!failedItem.isSelected}
                          />
                          <span className="text-xs text-muted-foreground">
                            / {failedItem.item.quantity}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Summary */}
          {selectedItems.length > 0 && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Trash2 className="h-4 w-4 text-red-600" />
                  <span className="font-medium text-red-800">
                    {selectedItems.length} article(s) en gaspillage
                  </span>
                </div>
                <span className="font-bold text-red-800">
                  Perte: {formatCurrency(totalFailedValue)}
                </span>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isProcessing}
            >
              Annuler
            </Button>
            <Button
              onClick={processFailure}
              disabled={isProcessing || selectedItems.length === 0}
              className="gap-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                  Traitement...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Traiter l'échec
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 