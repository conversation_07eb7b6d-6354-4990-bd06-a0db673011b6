"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";

import { X, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { OrderDocument, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { processDeliveryFailure, updateOrder } from '@/lib/db/v4/operations/order-ops';
import { kitchenQueueService } from '@/lib/services/kitchen-queue-service';

interface OrderCancellationHandlerProps {
  order: OrderDocument;
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

interface WastedItemState {
  itemIndex: number;
  item: OrderItem;
  wastedQuantity: number;
  isSelected: boolean;
}

const CANCELLATION_REASONS = [
  'Client a annulé',
  'Erreur de commande',
  'Problème de stock',
  'Problème de qualité',
  'Erreur de prix',
  'Demande du client',
  'Autre'
];

export function OrderCancellationHandler({ order, onSuccess, trigger }: OrderCancellationHandlerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [cancellationType, setCancellationType] = useState<'simple' | 'with_waste'>('simple');
  const [wastedItems, setWastedItems] = useState<WastedItemState[]>([]);
  const [cancellationReason, setCancellationReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Initialize wasted items when dialog opens
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Initialize all items as potentially wasted
      const initialWastedItems: WastedItemState[] = order.items.map((item, index) => ({
        itemIndex: index,
        item,
        wastedQuantity: item.quantity,
        isSelected: false
      }));
      setWastedItems(initialWastedItems);
      setCancellationType('simple');
      setCancellationReason('');
      setCustomReason('');
    }
  };

  // Update wasted quantity for an item
  const updateWastedQuantity = (itemIndex: number, quantity: number) => {
    setWastedItems(prev => prev.map(wastedItem => 
      wastedItem.itemIndex === itemIndex 
        ? { ...wastedItem, wastedQuantity: Math.max(0, Math.min(quantity, wastedItem.item.quantity)) }
        : wastedItem
    ));
  };

  // Toggle item selection
  const toggleItemSelection = (itemIndex: number, selected: boolean) => {
    setWastedItems(prev => prev.map(wastedItem => 
      wastedItem.itemIndex === itemIndex 
        ? { ...wastedItem, isSelected: selected }
        : wastedItem
    ));
  };

  // Select all items
  const selectAllItems = () => {
    setWastedItems(prev => prev.map(item => ({ ...item, isSelected: true })));
  };

  // Deselect all items
  const deselectAllItems = () => {
    setWastedItems(prev => prev.map(item => ({ ...item, isSelected: false })));
  };

  // Get selected items for waste processing
  const selectedItems = wastedItems.filter(item => item.isSelected && item.wastedQuantity > 0);

  // Process order cancellation
  const processCancellation = async () => {
    const reason = cancellationReason === 'Autre' ? customReason : cancellationReason;
    if (!reason.trim()) {
      toast({
        variant: "destructive",
        title: "Raison requise",
        description: "Veuillez spécifier la raison de l'annulation"
      });
      return;
    }

    try {
      setIsProcessing(true);

      if (cancellationType === 'simple') {
        // Simple cancellation - just update order status
        await updateOrder(order._id, { status: 'cancelled' });
        await kitchenQueueService.cancelOrder(order._id);
        
        toast({
          title: "Commande annulée",
          description: `Commande #${order._id.slice(-6)} annulée sans gaspillage`
        });
      } else {
        // Cancellation with waste - use delivery failure system
        if (selectedItems.length === 0) {
          toast({
            variant: "destructive",
            title: "Aucun article sélectionné",
            description: "Veuillez sélectionner au moins un article gaspillé"
          });
          return;
        }

        const wasteData = selectedItems.map(item => ({
          itemIndex: item.itemIndex,
          quantity: item.wastedQuantity
        }));

        // Use processDeliveryFailure for waste tracking
        await processDeliveryFailure(
          order._id,
          wasteData,
          `Annulation avec gaspillage: ${reason}`,
          'Staff'
        );

        // Update order status to cancelled
        await updateOrder(order._id, { status: 'cancelled' });
        await kitchenQueueService.cancelOrder(order._id);

        toast({
          title: "Commande annulée avec gaspillage",
          description: `${selectedItems.length} article(s) marqué(s) comme gaspillage`
        });
      }

      setIsOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error processing order cancellation:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'annuler la commande"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300">
      <X className="h-4 w-4" />
      Annuler
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="pb-3">
          <DialogTitle className="flex items-center gap-2 text-lg">
            <X className="h-4 w-4 text-red-500" />
            Annuler la commande
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-3">
          {/* Compact Cancellation Type Selection */}
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={cancellationType === 'simple' ? 'default' : 'outline'}
                size="sm"
                className="h-auto p-2 flex flex-col items-start gap-1"
                onClick={() => setCancellationType('simple')}
                type="button"
              >
                <div className="flex items-center gap-1.5 w-full">
                  <X className="h-3 w-3" />
                  <span className="text-xs font-medium">Simple</span>
                </div>
                <span className="text-xs opacity-80 text-left">Pas encore préparée</span>
              </Button>

              <Button
                variant={cancellationType === 'with_waste' ? 'default' : 'outline'}
                size="sm"
                className="h-auto p-2 flex flex-col items-start gap-1"
                onClick={() => setCancellationType('with_waste')}
                type="button"
              >
                <div className="flex items-center gap-1.5 w-full">
                  <Trash2 className="h-3 w-3" />
                  <span className="text-xs font-medium">Avec gaspillage</span>
                </div>
                <span className="text-xs opacity-80 text-left">Déjà préparée</span>
              </Button>
            </div>
          </div>

          {/* Compact Reason Selection */}
          <div className="space-y-2">
            <Label className="text-sm">Raison</Label>
            <div className="grid grid-cols-2 gap-1">
              {CANCELLATION_REASONS.slice(0, -1).map((reason) => (
                <Button
                  key={reason}
                  variant={cancellationReason === reason ? 'default' : 'outline'}
                  size="sm"
                  className="h-7 text-xs justify-start"
                  onClick={() => setCancellationReason(reason)}
                  type="button"
                >
                  {reason}
                </Button>
              ))}
              <Button
                variant={cancellationReason === 'Autre' ? 'default' : 'outline'}
                size="sm"
                className="h-7 text-xs justify-start"
                onClick={() => setCancellationReason('Autre')}
                type="button"
              >
                Autre
              </Button>
            </div>

            {cancellationReason === 'Autre' && (
              <Textarea
                placeholder="Spécifiez la raison..."
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="mt-2 h-16 text-xs"
              />
            )}
          </div>

          {/* Compact Waste Item Selection */}
          {cancellationType === 'with_waste' && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm">Articles gaspillés</Label>
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={selectAllItems}
                    className="h-6 px-2 text-xs"
                    type="button"
                  >
                    Tout
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={deselectAllItems}
                    className="h-6 px-2 text-xs"
                    type="button"
                  >
                    Aucun
                  </Button>
                </div>
              </div>

              <div className="space-y-1 max-h-48 overflow-y-auto">
                {wastedItems.map((wastedItem, index) => {
                  const itemPrice = wastedItem.item.price + (wastedItem.item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
                  const itemTotal = itemPrice * wastedItem.wastedQuantity;

                  // Create display name with size and item number for identification
                  let displayName = wastedItem.item.name;
                  if (wastedItem.item.size) {
                    displayName += ` (${wastedItem.item.size})`;
                  }

                  // Add item number for better identification (especially for duplicates)
                  const itemNumber = index + 1;

                  // Show quantity controls only if item has more than 1 quantity
                  const showQuantityControls = wastedItem.item.quantity > 1;

                  return (
                    <div
                      key={wastedItem.itemIndex}
                      className={cn(
                        "p-2 border rounded transition-colors",
                        wastedItem.isSelected ? "bg-red-50 border-red-200" : "bg-background"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={wastedItem.isSelected}
                          onCheckedChange={(checked) => toggleItemSelection(wastedItem.itemIndex, !!checked)}
                          className="shrink-0"
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between gap-2">
                            <div className="min-w-0 flex-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xs font-medium text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                                  #{itemNumber}
                                </span>
                                <div className="text-sm font-medium truncate">{displayName}</div>
                              </div>
                              {wastedItem.item.addons && wastedItem.item.addons.length > 0 && (
                                <div className="text-xs text-muted-foreground truncate ml-8">
                                  + {wastedItem.item.addons.map(addon => addon.name).join(', ')}
                                </div>
                              )}
                            </div>
                            <div className="text-xs font-medium text-right">
                              {itemTotal.toFixed(0)} DA
                            </div>
                          </div>

                          {/* Show quantity controls only if item quantity > 1 */}
                          {showQuantityControls && (
                            <div className="flex items-center justify-between mt-1 ml-8">
                              <div className="flex items-center gap-1">
                                <span className="text-xs text-muted-foreground">Qté gaspillée:</span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-5 w-5 p-0 text-xs"
                                  onClick={() => updateWastedQuantity(wastedItem.itemIndex, wastedItem.wastedQuantity - 1)}
                                  disabled={wastedItem.wastedQuantity <= 0}
                                  type="button"
                                >
                                  -
                                </Button>
                                <span className="text-xs font-medium w-6 text-center">
                                  {wastedItem.wastedQuantity}
                                </span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-5 w-5 p-0 text-xs"
                                  onClick={() => updateWastedQuantity(wastedItem.itemIndex, wastedItem.wastedQuantity + 1)}
                                  disabled={wastedItem.wastedQuantity >= wastedItem.item.quantity}
                                  type="button"
                                >
                                  +
                                </Button>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                / {wastedItem.item.quantity}
                              </span>
                            </div>
                          )}

                          {/* Show quantity info for single items (no controls needed) */}
                          {!showQuantityControls && (
                            <div className="text-xs text-muted-foreground mt-1 ml-8">
                              Quantité: {wastedItem.item.quantity}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {selectedItems.length > 0 && (
                <div className="p-2 bg-red-50 border border-red-200 rounded text-center">
                  <div className="text-xs font-medium text-red-800">
                    {selectedItems.length} article(s) • {selectedItems.reduce((sum, item) => {
                      const itemPrice = item.item.price + (item.item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
                      return sum + (itemPrice * item.wastedQuantity);
                    }, 0).toFixed(0)} DA gaspillé
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="gap-2 pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsOpen(false)}
            disabled={isProcessing}
            className="flex-1"
          >
            Annuler
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={processCancellation}
            disabled={isProcessing || !cancellationReason || (cancellationReason === 'Autre' && !customReason.trim())}
            className="flex-1"
          >
            {isProcessing ? 'Traitement...' :
             cancellationType === 'simple' ? 'Confirmer' : 'Confirmer + Gaspillage'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
