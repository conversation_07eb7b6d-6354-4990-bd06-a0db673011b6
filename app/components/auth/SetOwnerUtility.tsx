'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

export function SetOwnerUtility() {
  const [userId, setUserId] = useState('');
  const [adminKey, setAdminKey] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const { toast } = useToast();
  
  const handleSetOwner = async () => {
    if (!userId) {
      toast({
        title: "Error",
        description: "User ID is required",
        variant: "destructive"
      });
      return;
    }
    
    if (!adminKey) {
      toast({
        title: "Error",
        description: "Admin key is required",
        variant: "destructive"
      });
      return;
    }
    
    try {
      setLoading(true);
      setResult(null);
      
      const response = await fetch('/api/auth/set-owner', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          adminKey
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        toast({
          title: "Error",
          description: data.error || 'Failed to set user as owner',
          variant: "destructive"
        });
        setResult(data);
        return;
      }
      
      toast({
        title: "Success",
        description: data.message || 'User has been set as owner'
      });
      
      setResult(data);
    } catch (error) {
      console.error('Error setting user as owner:', error);
      toast({
        title: "Error",
        description: 'Failed to set user as owner',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Set User as Owner</CardTitle>
        <CardDescription>
          Use this utility to set a user as an owner. This gives them full access to all features.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="userId" className="block text-sm font-medium">
            User ID
          </label>
          <Input
            id="userId"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            placeholder="Enter user ID"
          />
        </div>
        <div className="space-y-2">
          <label htmlFor="adminKey" className="block text-sm font-medium">
            Admin Key
          </label>
          <Input
            id="adminKey"
            type="password"
            value={adminKey}
            onChange={(e) => setAdminKey(e.target.value)}
            placeholder="Enter admin key"
          />
        </div>
      </CardContent>
      <CardFooter className="flex flex-col items-stretch gap-4">
        <Button 
          onClick={handleSetOwner} 
          disabled={loading || !userId || !adminKey}
          className="w-full"
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {loading ? 'Setting User as Owner...' : 'Set as Owner'}
        </Button>
        
        {result && (
          <div className="mt-4 p-4 bg-gray-50 rounded border text-sm overflow-x-auto">
            <pre className="whitespace-pre-wrap">{JSON.stringify(result, null, 2)}</pre>
          </div>
        )}
      </CardFooter>
    </Card>
  );
} 