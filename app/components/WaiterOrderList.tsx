"use client";

import React, { useState, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { format } from "date-fns";
import {
  Loader2,
  Search,
  UserRound,
  Users,
  Utensils,
  Clock,
  Pencil,
  X,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { Order } from "@/lib/db/v4/schemas/order-schema";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { TableSwitchDialog } from '@/components/orders/TableSwitchDialog';
import { useEditOrder } from '@/components/providers/EditOrderContext';
import { 
  androidSafeFilter, 
  androidSafeSort, 
  androidSafeDate,
  isAndroidEnvironment 
} from '@/lib/utils/android-safe';

const statusFilters = [
  { value: "all", label: "Toutes", icon: null, color: "text-muted-foreground" },
  { value: "pending", label: "En Attente", icon: Clock, color: "text-amber-600" },
  { value: "served", label: "Servi", icon: Utensils, color: "text-violet-600" },
  { value: "completed", label: "Terminé", icon: CheckCircle, color: "text-emerald-600" },
  { value: "cancelled", label: "Annulé", icon: XCircle, color: "text-red-600" },
];

const getStatusConfig = (status: string) => {
  const config = statusFilters.find(s => s.value === status);
  return config || statusFilters[0];
};

const TypeBadge = ({ type }: { type: string }) => {
  const getTypeConfig = (type: string) => {
    switch (type) {
      case "dine-in":
        return { icon: UserRound, label: "Sur Place", color: "text-blue-600" };
      case "delivery":
        return { icon: UserRound, label: "Livraison", color: "text-purple-600" };
      case "takeaway":
      case "takeout":
        return { icon: UserRound, label: "À Emporter", color: "text-orange-600" };
      default:
        return { icon: UserRound, label: type, color: "text-muted-foreground" };
    }
  };

  const config = getTypeConfig(type);
  const Icon = config.icon;

  return (
    <Badge variant="outline" className={cn("text-xs", config.color)}>
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  );
};

const StatusBadge = ({ status }: { status: string }) => {
  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Badge variant="outline" className={cn("text-xs font-medium", config.color)}>
      {Icon && <Icon className="h-3 w-3 mr-1.5" />}
      {config.label}
    </Badge>
  );
};

interface WaiterOrderListProps {
  onEditOrder?: (order: Order) => void;
}

const WaiterOrderList: React.FC<WaiterOrderListProps> = ({ onEditOrder }) => {
  const { orders, isLoading, error, refreshOrders, updateOrder } = useOrderV4();
  const { user } = useAuth();
  const { setEditOrder } = useEditOrder();
  const [status, setStatus] = useState("all");
  const [search, setSearch] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const { toast } = useToast();
  const [tableSwitchOrder, setTableSwitchOrder] = useState<Order | null>(null);
  const [cancelOrder, setCancelOrder] = useState<Order | null>(null);

  // Better loading logic: Only show loading if we have no data AND it's loading
  const shouldShowLoading = isLoading && orders.length === 0;
  
  // Handle errors with Android-safe error handling
  if (error && orders.length === 0) {
    return (
      <div className="w-full px-4 py-6">
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center space-y-4">
            <XCircle className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold text-red-600">Erreur de chargement</h3>
              <p className="text-muted-foreground mt-1">
                {error?.message || 'Une erreur est survenue'}
              </p>
            </div>
            <Button 
              onClick={() => {
                try {
                  refreshOrders();
                } catch (e) {
                  console.error('Refresh error:', e);
                  if (typeof window !== 'undefined' && window.location) {
                    window.location.reload();
                  }
                }
              }} 
              variant="outline"
            >
              Réessayer
            </Button>
          </div>
        </div>
      </div>
    );
  }
  
  const filteredOrders = useMemo(() => {
    try {
      // Android-safe filtering with enhanced error handling
      if (!Array.isArray(orders)) {
        console.warn('Orders is not an array:', orders);
        return [];
      }

      // First filter: ensure valid orders
      let filtered = androidSafeFilter(orders, (order) => 
        !!(order && typeof order === 'object' && order.id)
      );
      
      // Status filter
      if (status !== "all") {
        filtered = androidSafeFilter(filtered, (o) => o?.status === status);
      }
      
      // Search filter
      if (search.trim()) {
        const s = search.toLowerCase();
        filtered = androidSafeFilter(filtered, (o) => {
          return (
            (o?.id && o.id.toLowerCase().includes(s)) ||
            (o?.customer?.name && o.customer.name.toLowerCase().includes(s)) ||
            (Array.isArray(o?.items) && o.items.some(item => 
              item?.name && item.name.toLowerCase().includes(s)
            ))
          );
        });
      }
      
      // Android-safe sorting
      return androidSafeSort(filtered, (a, b) => {
        const dateA = androidSafeDate(a?.createdAt || 0).getTime();
        const dateB = androidSafeDate(b?.createdAt || 0).getTime();
        return dateB - dateA;
      });
    } catch (error) {
      console.error('Error in filteredOrders useMemo:', error);
      return [];
    }
  }, [orders, status, search]);

  // Mark order as served - Android-safe
  const handleMarkAsServed = async (order: Order) => {
    if (!order?.id) {
      console.error('Invalid order for marking as served:', order);
      return;
    }

    setIsProcessing(true);
    setErrorMsg("");
    
    try {
      await updateOrder(order.id, { status: 'served' });
      await refreshOrders();
      
      if (toast) {
        toast({
          title: "Commande servie",
          description: `La commande #${order.id.slice(-6)} a été marquée comme servie`
        });
      }
    } catch (e) {
      console.error('Error marking order as served:', e);
      setErrorMsg("Échec de mise à jour de la commande");
      
      if (toast) {
        toast({
          variant: "destructive",
          title: "Erreur",
          description: "Impossible de marquer la commande comme servie"
        });
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // Cancel order - Android-safe
  const handleCancelOrder = async (order: Order, reason: string) => {
    if (!order?.id) {
      console.error('Invalid order for cancellation:', order);
      return;
    }

    setIsProcessing(true);
    
    try {
      await updateOrder(order.id, {
        status: 'cancelled',
        notes: reason || 'Annulé par le serveur'
      });
      await refreshOrders();
      setCancelOrder(null);
      
      if (toast) {
        toast({
          title: "Commande annulée",
          description: `La commande #${order.id.slice(-6)} a été annulée`
        });
      }
    } catch (e) {
      console.error('Error cancelling order:', e);
      
      if (toast) {
        toast({
          variant: "destructive",
          title: "Erreur",
          description: "Impossible d'annuler la commande"
        });
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full px-4 py-6">
      <div className="space-y-4">
        {/* Header & Filters */}
        <div className="space-y-3">
          {/* Compact Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h1 className="text-lg font-bold">Commandes</h1>
              <div className="text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-full">
                {filteredOrders.length}
              </div>
            </div>
          </div>
          
          {/* Compact Search & Status Filters */}
          <div className="flex flex-col gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-3 w-3 text-muted-foreground" />
              <Input
                placeholder="Rechercher..."
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="pl-6 h-8 text-xs"
              />
            </div>
            
            <div className="flex gap-1 overflow-x-auto pb-1">
              {statusFilters.map(filter => {
                const Icon = filter.icon;
                const isActive = status === filter.value;
                const count = filter.value === "all" 
                  ? orders.length 
                  : orders.filter(o => o.status === filter.value).length;
                
                return (
                  <Button
                    key={filter.value}
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStatus(filter.value)}
                    className={cn(
                      "whitespace-nowrap h-8 px-2 text-xs",
                      !isActive && filter.color
                    )}
                  >
                    {Icon && <Icon className="h-3 w-3 mr-1" />}
                    {filter.label}
                    <Badge variant="secondary" className="ml-1 text-xs px-1">
                      {count}
                    </Badge>
                  </Button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-2">
          {shouldShowLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center space-y-2">
                <Loader2 className="h-6 w-6 animate-spin mx-auto text-muted-foreground" />
                <p className="text-muted-foreground text-sm">Chargement...</p>
              </div>
            </div>
          ) : filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <div className="space-y-2">
                <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center mx-auto">
                  <Search className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-base font-semibold">Aucune commande</h3>
                  <p className="text-muted-foreground mt-1 text-sm">
                    {search ? "Aucun résultat trouvé" : "Aucune commande pour le moment"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            filteredOrders.map(order => (
              <div
                key={order.id}
                className={cn(
                  "relative p-3 rounded-lg shadow-sm transition-all duration-200 border",
                  order.status === 'cancelled' && "opacity-70 border-dashed border-red-300 bg-red-50/20",
                  order.status === 'completed' && "bg-emerald-50/30 border-emerald-200",
                  order.status === 'served' && "bg-violet-50/30 border-violet-200",
                  order.status === 'pending' && "border-amber-200 bg-amber-50/20"
                )}
              >
                <div className="flex w-full items-start justify-between gap-2">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {/* Order ID & Type */}
                    <div className="flex flex-col gap-1 min-w-0 items-start">
                      <div className="font-mono text-xs font-bold">
                        #{order.id.slice(-6)}
                      </div>
                      <div className="flex gap-1 items-center">
                        <TypeBadge type={order.orderType} />
                      </div>
                    </div>
                    
                    {/* Status & Time */}
                    <div className="flex flex-col gap-1 min-w-0 items-start">
                      <StatusBadge status={order.status} />
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(order.createdAt), 'HH:mm')}
                      </div>
                    </div>
                    
                    {/* Items */}
                    <div className="flex-1 min-w-0 flex flex-col justify-center">
                      <div className="text-sm font-medium truncate">
                        {order.items?.map(item => item.name).join(", ")}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {order.items?.length} article{order.items?.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                    
                    {/* Total */}
                    <div className="text-right min-w-0">
                      <div className="text-base font-bold">
                        {order.total} DA
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Actions */}
                <div className="flex items-center justify-end gap-1 mt-2 pt-2 border-t border-border/50">
                  {/* Edit Action */}
                  {(order.status !== 'cancelled' && order.status !== 'completed') && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-7 px-2 text-blue-600 border-blue-200 hover:bg-blue-50"
                      onClick={() => {
                        setEditOrder(order);
                        onEditOrder?.(order);
                      }}
                    >
                      <Pencil className="h-3 w-3 mr-1" />
                      Modifier
                    </Button>
                  )}

                  {/* Table Switch Action - Only for dine-in orders */}
                  {(order.orderType === 'dine-in' && order.status !== 'cancelled' && order.status !== 'completed') && (
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="h-7 px-2 text-amber-600 border-amber-200 hover:bg-amber-50" 
                      onClick={() => setTableSwitchOrder(order)}
                    >
                      <Users className="h-3 w-3 mr-1" />
                      Table
                    </Button>
                  )}
                  
                  {/* Mark as Served Action */}
                  {(order.status === 'pending') && (
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="h-7 px-2 text-violet-600 border-violet-200 hover:bg-violet-50" 
                      onClick={() => handleMarkAsServed(order)}
                      disabled={isProcessing}
                    >
                      <Utensils className="h-3 w-3 mr-1" />
                      Servir
                    </Button>
                  )}
                  
                  {/* Cancel Action */}
                  {(order.status !== 'cancelled' && order.status !== 'completed') && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-7 px-2 text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Annuler
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Annuler la commande</AlertDialogTitle>
                          <AlertDialogDescription>
                            Êtes-vous sûr de vouloir annuler cette commande ?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Retour</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleCancelOrder(order, "Annulé par le serveur")}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Confirmer
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Table Switch Dialog */}
        {tableSwitchOrder && (
          <TableSwitchDialog
            open={!!tableSwitchOrder}
            onOpenChange={(open) => {
              if (!open) setTableSwitchOrder(null);
            }}
            order={tableSwitchOrder}
            onSuccess={() => {
              refreshOrders();
              setTableSwitchOrder(null);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default WaiterOrderList;