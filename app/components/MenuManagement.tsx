"use client";

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
    PlusCircle, Pencil, Trash2, X, Check, Smile, Save,
    RefreshCw, Plus, Edit, Database, PlusIcon, PencilIcon,
    TrashIcon, ArrowLeftIcon, CheckIcon, XIcon, ImageIcon,
    SlidersHorizontalIcon, ChevronRightIcon, AlertCircleIcon, DatabaseIcon, Loader2, CheckCircle2Icon, Beaker, Package, Utensils, Car, ChevronDown, ChevronRight, Pizza, Users, ChefHat, Printer, GlassWater, Link, Building
} from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import data from "@emoji-mart/data";
import Picker from "@emoji-mart/react";
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { useMenuV4 } from "@/lib/hooks/use-menu-v4";
import { SyncStatusNew } from "@/components/SyncStatusNew";
import { Category, MenuItem } from "@/lib/db/v4-menu-service";
import { useUnifiedDB } from "@/lib/context/unified-db-provider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
// Removed algerian-menu-seed import - now using universal seed data system
import { useToast } from "@/components/ui/use-toast";
import { PRESET_COLORS, getRandomPresetColor, getDifferentColor } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { IntegratedTableManager } from '@/components/tables/IntegratedTableManager';
import { KitchenPrintingSetup } from '@/components/settings/KitchenPrintingSetup';
import { SizesAndPackagingSection } from './menu/SizesAndPackagingSection';
import { SupplementSection } from './menu/SupplementSection';
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useOrderV4 } from "@/lib/hooks/use-order-v4";
import { EquipeMenuManagement } from './menu/EquipeMenuManagement';
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { MenuItemRecipeForm } from "@/components/production/MenuItemRecipeForm";
// Removed unused imports - components are defined locally or not needed

// Interface for Emoji Picker objects
interface EmojiObject {
    native: string;
    [key: string]: any;
}

// Create a local interface for handling item pricing in the UI
interface MenuItemWithPrices {
    id: string;
    name: string;
    prices: { [key: string]: number };
    image?: string;
    color?: string;
}

// Define the SyncState interface to match what is used in the component
interface SyncState {
    status: 'idle' | 'syncing' | 'error' | 'paused' | 'complete';
    error?: Error;
    lastSync?: string;
    progress?: {
        docs_read: number;
        docs_written: number;
        pending: number;
    };
}

// Define prop types for the components
interface HeaderSectionProps {
    stats: {
        categories: number;
        items: number;
    };
    syncState: SyncState;
    refreshCategories: () => Promise<void>;
    setIsAddingCategory: React.Dispatch<React.SetStateAction<boolean>>;
    handleSeedMenu: () => Promise<void>;
    fixCategoriesStructure?: () => Promise<void>;
    repairMenuSizes?: () => Promise<void>;
}

interface CategoryDetailsSectionProps {
    category: Category;
    isAddingItem: boolean;
    setIsAddingItem: React.Dispatch<React.SetStateAction<boolean>>;
    newItem: {
        name: string;
        price: number;
        prices?: { [key: string]: number };
    };
    setNewItem: React.Dispatch<React.SetStateAction<{
        name: string;
        price: number;
        prices?: { [key: string]: number };
    }>>;
    newItemInputRef: React.MutableRefObject<HTMLInputElement | null>;
    addMenuItem: (categoryId: string, item: Partial<MenuItem>) => Promise<MenuItem>;
    editingItemId: string | null;
    setEditingItemId: React.Dispatch<React.SetStateAction<string | null>>;
    editedItem: MenuItemWithPrices | null;
    setEditedItem: React.Dispatch<React.SetStateAction<MenuItemWithPrices | null>>;
    updateMenuItem: (categoryId: string, itemId: string, data: Partial<MenuItem>) => Promise<MenuItem>;
    setDeleteConfirmDialog: React.Dispatch<React.SetStateAction<{
        open: boolean;
        itemId?: string;
        categoryId?: string;
        tableId?: string;
        sizeName?: string;
        type: 'item' | 'category' | 'table' | 'size';
    }>>;
    isAddingSize: boolean;
    setIsAddingSize: React.Dispatch<React.SetStateAction<boolean>>;
    newSize: string;
    setNewSize: React.Dispatch<React.SetStateAction<string>>;
    addSizeToCategory: (categoryId: string, size: string) => Promise<string[]>;
    renameSizeInCategory: (categoryId: string, oldSizeName: string, newSizeName: string) => Promise<Category>;
    deleteSizeFromCategory: (categoryId: string, sizeName: string) => Promise<string[]>;
    refreshCategories: () => Promise<void>;
    stockItems: any[]; // Add stockItems prop
    updateCategory: (id: string, data: Partial<Category>) => Promise<Category>; // 🍕 Add for pizza settings
    handleOpenRecipeDialog: (item: MenuItem, size?: string) => void; // Add recipe dialog handler
}

interface DeleteConfirmationDialogProps {
    deleteConfirmDialog: {
        open: boolean;
        itemId?: string;
        categoryId?: string;
        tableId?: string;
        sizeName?: string;
        type: 'item' | 'category' | 'table' | 'size';
    };
    setDeleteConfirmDialog: React.Dispatch<React.SetStateAction<{
        open: boolean;
        itemId?: string;
        categoryId?: string;
        tableId?: string;
        sizeName?: string;
        type: 'item' | 'category' | 'table' | 'size';
    }>>;
    handleDeleteCategory: (id: string) => Promise<void>;
    handleDeleteItem: (categoryId: string, itemId: string) => Promise<void>;
    handleDeleteTable: (id: string) => Promise<void>;
    handleDeleteSize: (categoryId: string, sizeName: string) => Promise<void>;
    refreshCategories: () => Promise<void>;
    refreshTables: () => Promise<void>;
    categories: Category[]; // Add categories to the props
}

// Define the MenuCategoriesSectionProps interface
interface MenuCategoriesSectionProps {
    categories: Category[];
    selectedCategory: string;
    setSelectedCategory: React.Dispatch<React.SetStateAction<string>>;
    editingCategoryId: string | null;
    setEditingCategoryId: React.Dispatch<React.SetStateAction<string | null>>;
    editedCategory: Category | null;
    setEditedCategory: React.Dispatch<React.SetStateAction<Category | null>>;
    isAddingCategory: boolean;
    setIsAddingCategory: React.Dispatch<React.SetStateAction<boolean>>;
    newCategory: { name: string; emoji: string; color: string };
    setNewCategory: React.Dispatch<React.SetStateAction<{ name: string; emoji: string; color: string }>>;
    newCategoryInputRef: React.MutableRefObject<HTMLInputElement | null>;
    updateCategory: (id: string, data: Partial<Category>) => Promise<Category>;
    addCategory: (category: Category) => Promise<Category>;
    setDeleteConfirmDialog: React.Dispatch<React.SetStateAction<{
        open: boolean;
        itemId?: string;
        categoryId?: string;
        type: 'item' | 'category' | 'table' | 'size';
    }>>;
    showEmojiPicker: boolean;
    setShowEmojiPicker: React.Dispatch<React.SetStateAction<boolean>>;
    handleDeleteCategory: (id: string) => Promise<void>;
}

export function MenuManagement() {
    const { isAuthenticated, user, loading } = useAuth();
    const { toast } = useToast();
    const {
        categories,
        refreshCategories,
        addCategory,
        updateCategory,
        deleteCategory,
        addMenuItem,
        updateMenuItem,
        deleteMenuItem,
        addSizeToCategory,
        renameSizeInCategory,
        deleteSizeFromCategory,
        repairMenuSizes
    } = useMenuV4();

    const { stockItems } = useStockV4();
    const {
        menuItemRecipes,
        subRecipes,
        createMenuItemRecipe,
        updateMenuItemRecipe,
        deleteMenuItemRecipe,
        refreshData
    } = useCOGSV4();

    // Get sync status and convert to the expected format
    const { status: restaurantStatus } = useUnifiedDB();
    const [syncState, setSyncState] = useState<SyncState>({
        status: restaurantStatus.status,
        error: restaurantStatus.error,
        progress: restaurantStatus.progress,
        lastSync: new Date().toISOString() // Add missing property
    });

    // Editing states
    const [selectedCategory, setSelectedCategory] = useState<string>("");
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
        open: boolean;
        itemId?: string;
        categoryId?: string;
        tableId?: string;
        sizeName?: string;
        type: 'item' | 'category' | 'table' | 'size';
    }>({
        open: false,
        type: 'item'
    });

    // New inline editing states
    const [editingItemId, setEditingItemId] = useState<string | null>(null);
    const [editingCategoryId, setEditingCategoryId] = useState<string | null>(null);
    const [isAddingCategory, setIsAddingCategory] = useState(false);
    const [isAddingItem, setIsAddingItem] = useState(false);
    const [isAddingSize, setIsAddingSize] = useState(false);

    // Form fields
    const [newCategory, setNewCategory] = useState({
        name: "",
        emoji: "🍽️",
        color: PRESET_COLORS[0] // Use the first preset color as default
    });
    const [newItem, setNewItem] = useState<{
        name: string;
        price: number;
        prices?: { [key: string]: number };
    }>({
        name: "",
        price: 0,
        prices: {}
    });
    const [editedItem, setEditedItem] = useState<MenuItemWithPrices | null>(null);
    const [editedCategory, setEditedCategory] = useState<Category | null>(null);
    const [newSize, setNewSize] = useState("");

    // Recipe dialog states
    const [isRecipeDialogOpen, setIsRecipeDialogOpen] = useState(false);
    const [selectedRecipeItem, setSelectedRecipeItem] = useState<MenuItem | null>(null);
    const [selectedRecipeSize, setSelectedRecipeSize] = useState<string>('');

    // Refs for autofocus
    const newCategoryInputRef = useRef<HTMLInputElement>(null);
    const newItemInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (categories.length > 0 && !selectedCategory) {
            setSelectedCategory(categories[0].id);
        }
    }, [categories, selectedCategory]);

    // Autofocus for inline edits
    useEffect(() => {
        if (isAddingCategory) {
            setTimeout(() => {
                newCategoryInputRef.current?.focus();
            }, 100);
        }
    }, [isAddingCategory]);

    useEffect(() => {
        if (isAddingItem) {
            setTimeout(() => {
                newItemInputRef.current?.focus();
            }, 100);
        }
    }, [isAddingItem]);

    // If authentication is still loading, show loading state
    if (loading) {
        return (
            <div className="container py-10">
                <Card>
                    <CardHeader>
                        <CardTitle>Chargement</CardTitle>
                        <CardDescription>
                            Veillez patienter pendant la vérification de votre authentification...
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex justify-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    // If not authenticated, show authentication required message
    if (!isAuthenticated) {
        return (
            <div className="container py-10">
                <Card>
                    <CardHeader>
                        <CardTitle>Authentification requise</CardTitle>
                        <CardDescription>
                            Veuillez vous connecter pour accéder à la gestion du menu.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button asChild>
                            <a href="/auth/signin">Se connecter</a>
                        </Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    // Get stats for display in the header
    const stats = {
        categories: categories.length,
        items: categories.reduce((acc: number, category: Category) => acc + category.items.length, 0),
    };

    // Function to handle seeding the menu
    const handleSeedMenu = async () => {
        try {
            // Check if categories already exist
            if (categories.length > 0) {
                // Confirm with user before proceeding
                if (!window.confirm("This will replace your existing menu with sample data. Are you sure you want to continue?")) {
                    return;
                }

                // Delete existing categories
                for (const category of categories) {
                    await deleteCategory(category.id);
                }
            }

            // Seed menu data using universal seed system
            const { initializeUniversalSeedData } = await import('@/lib/db/v4/seed-data/seed-data-service');
            
            // 🚨 CRITICAL FIX: Use safe seeding to prevent data loss
            const seedResult = await initializeUniversalSeedData({
                skipIfExists: false,
                overwrite: true  // Safe now due to merge logic in seed service
            });
            
            if (!seedResult.success) {
                throw new Error(seedResult.message);
            }

            // Refresh categories
            await refreshCategories();

            // Show success toast
            toast({
                title: "Menu seeded successfully",
                description: "Your menu has been populated with sample data.",
            });
        } catch (error) {
            console.error("Error seeding menu:", error);
            toast({
                title: "Failed to seed menu",
                description: "An error occurred while seeding the menu.",
                variant: "destructive",
            });
        }
    };

    // Create wrapper functions that return void instead of arrays
    const handleDeleteSize = async (categoryId: string, sizeName: string): Promise<void> => {
        await deleteSizeFromCategory(categoryId, sizeName);
        // Result is ignored
    };

    // Recipe dialog handlers
    const handleOpenRecipeDialog = (item: MenuItem, size?: string) => {
        setSelectedRecipeItem(item);
        setSelectedRecipeSize(size || '');
        setIsRecipeDialogOpen(true);
    };

    const handleRecipeSubmit = async (data: any) => {
        console.log('[MenuManagement] Received recipe data:', data);
        try {
            const existingRecipe = menuItemRecipes.find(
                r => r.menuItemId === data.menuItemId && r.size === data.size
            );
            
            console.log('[MenuManagement] Existing recipe found:', existingRecipe);
            console.log('[MenuManagement] Will', existingRecipe ? 'update' : 'create', 'recipe');

            if (existingRecipe) {
                console.log('[MenuManagement] Updating recipe with data:', data);
                await updateMenuItemRecipe(existingRecipe._id, data);
                toast({
                    title: "Coût mis à jour",
                    description: "Le coût a été mis à jour avec succès.",
                });
            } else {
                console.log('[MenuManagement] Creating new recipe with data:', data);
                await createMenuItemRecipe(data);
                toast({
                    title: "Coût créé",
                    description: "Le coût a été créé avec succès.",
                });
            }

            setIsRecipeDialogOpen(false);
            setSelectedRecipeItem(null);
        } catch (error) {
            console.error("Error saving recipe:", error);
            toast({
                title: "Erreur",
                description: "Impossible d'enregistrer la recette.",
                variant: "destructive",
            });
        }
    };

    const handleRecipeCancel = () => {
        setIsRecipeDialogOpen(false);
        setSelectedRecipeItem(null);
        // Refresh COGS data to get any newly created sub-recipes
        refreshData();
    };

    // Render the main application with tabs
    return (
        <div className="flex flex-col h-full bg-muted/20">
            <div className="flex justify-between items-center p-4 border-b bg-background">
                <h1 className="text-2xl font-bold">Gestion du menu</h1>
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => refreshCategories()}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Rafraîchir
                    </Button>
                </div>
            </div>

            <Tabs defaultValue="menu" className="p-4">
                <TabsList className="mb-4">
                    <TabsTrigger value="menu" className="flex items-center gap-1.5">
                        <Utensils className="h-4 w-4" /> <span>Menu</span>
                    </TabsTrigger>
                    <TabsTrigger value="tables" className="flex items-center gap-1.5">
                        <Building className="h-4 w-4" /> <span>Tables</span>
                    </TabsTrigger>
                    <TabsTrigger value="printing" className="flex items-center gap-1.5">
                        <Printer className="h-4 w-4" /> <span>Impression</span>
                    </TabsTrigger>
                    <TabsTrigger value="equipe" className="flex items-center gap-1.5">
                        <Users className="h-4 w-4" /> <span>Équipe</span>
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="menu">
                    <div className="space-y-4">
                        <MenuCategoriesSection
                            categories={categories}
                            selectedCategory={selectedCategory}
                            setSelectedCategory={setSelectedCategory}
                            editingCategoryId={editingCategoryId}
                            setEditingCategoryId={setEditingCategoryId}
                            editedCategory={editedCategory}
                            setEditedCategory={setEditedCategory}
                            isAddingCategory={isAddingCategory}
                            setIsAddingCategory={setIsAddingCategory}
                            newCategory={newCategory}
                            setNewCategory={setNewCategory}
                            newCategoryInputRef={newCategoryInputRef}
                            updateCategory={updateCategory}
                            addCategory={addCategory}
                            setDeleteConfirmDialog={setDeleteConfirmDialog}
                            showEmojiPicker={showEmojiPicker}
                            setShowEmojiPicker={setShowEmojiPicker}
                            handleDeleteCategory={deleteCategory}
                        />
                        {selectedCategory && categories.find(c => c.id === selectedCategory) ? (
                            <div className="flex-1 flex flex-col min-h-0">
                                <ScrollArea className="flex-1">
                                    <div className="pr-4 py-1">
                                        <CategoryDetailsSection
                                            category={categories.find(c => c.id === selectedCategory)!}
                                            isAddingItem={isAddingItem}
                                            setIsAddingItem={setIsAddingItem}
                                            newItem={newItem}
                                            setNewItem={setNewItem}
                                            newItemInputRef={newItemInputRef}
                                            addMenuItem={addMenuItem}
                                            editingItemId={editingItemId}
                                            setEditingItemId={setEditingItemId}
                                            editedItem={editedItem}
                                            setEditedItem={setEditedItem}
                                            updateMenuItem={updateMenuItem}
                                            setDeleteConfirmDialog={setDeleteConfirmDialog}
                                            isAddingSize={isAddingSize}
                                            setIsAddingSize={setIsAddingSize}
                                            newSize={newSize}
                                            setNewSize={setNewSize}
                                            addSizeToCategory={addSizeToCategory}
                                            renameSizeInCategory={renameSizeInCategory}
                                            deleteSizeFromCategory={deleteSizeFromCategory}
                                            refreshCategories={refreshCategories}
                                            stockItems={stockItems || []}
                                            updateCategory={updateCategory}
                                            handleOpenRecipeDialog={handleOpenRecipeDialog}
                                        />
                                    </div>
                                </ScrollArea>
                            </div>
                        ) : (
                            <div className="flex items-center justify-center h-full text-muted-foreground p-8 rounded-lg bg-muted/30">
                                Sélectionnez une catégorie pour voir les détails
                            </div>
                        )}
                    </div>
                </TabsContent>

                <TabsContent value="tables">
                    <IntegratedTableManager />
                </TabsContent>

                <TabsContent value="printing">
                    <KitchenPrintingSetup categories={categories} />
                </TabsContent>
                <TabsContent value="equipe">
                    <EquipeMenuManagement />
                </TabsContent>
            </Tabs>

            <DeleteConfirmationDialog
                deleteConfirmDialog={deleteConfirmDialog}
                setDeleteConfirmDialog={setDeleteConfirmDialog}
                handleDeleteCategory={deleteCategory}
                handleDeleteItem={deleteMenuItem}
                handleDeleteTable={async () => { }}
                handleDeleteSize={handleDeleteSize}
                refreshCategories={refreshCategories}
                refreshTables={async () => { }}
                categories={categories}
            />

            {/* Recipe Dialog */}
            <Dialog open={isRecipeDialogOpen} onOpenChange={(open) => {
                setIsRecipeDialogOpen(open);
                if (!open) {
                    setSelectedRecipeItem(null);
                    // Refresh COGS data when dialog closes
                    refreshData();
                }
            }}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <ChefHat className="h-5 w-5 text-blue-600" />
                            Gestion des Coûts - {selectedRecipeItem?.name}
                        </DialogTitle>
                        <DialogDescription>
                            Gérez le coût et la recette pour cet article du menu
                        </DialogDescription>
                    </DialogHeader>

                    <div className="overflow-y-auto flex-1 relative z-0">
                        {selectedRecipeItem && (
                            <MenuItemRecipeForm
                                onSubmit={handleRecipeSubmit}
                                onCancel={handleRecipeCancel}
                                initialData={null}
                                menuItems={[{
                                    id: selectedRecipeItem.id,
                                    name: selectedRecipeItem.name,
                                    categoryName: categories.find(c => c.items.some(i => i.id === selectedRecipeItem.id))?.name || '',
                                    sizes: Object.keys(selectedRecipeItem.prices),
                                    prices: selectedRecipeItem.prices
                                }]}
                                existingRecipes={menuItemRecipes.filter(
                                    r => r.menuItemId === selectedRecipeItem.id
                                )}
                                selectedSize={selectedRecipeSize}
                                subRecipes={subRecipes}
                                isMobile={false}
                            />
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}

// Menu categories section component
function MenuCategoriesSection({
    categories,
    selectedCategory,
    setSelectedCategory,
    editingCategoryId,
    setEditingCategoryId,
    editedCategory,
    setEditedCategory,
    isAddingCategory,
    setIsAddingCategory,
    newCategory,
    setNewCategory,
    newCategoryInputRef,
    updateCategory,
    addCategory,
    setDeleteConfirmDialog,
    showEmojiPicker,
    setShowEmojiPicker,
    handleDeleteCategory
}: MenuCategoriesSectionProps) {
    // Cancel category editing
    const cancelCategoryEdit = () => {
        setEditingCategoryId(null);
        setEditedCategory(null);
    };

    // Save category edit
    const saveCategoryEdit = async () => {
        if (editedCategory) {
            try {
                await updateCategory(editedCategory.id, {
                    name: editedCategory.name,
                    emoji: editedCategory.emoji,
                    color: editedCategory.color
                });
                cancelCategoryEdit();
            } catch (error) {
                console.error('Error updating category:', error);
            }
        }
    };

    // Handle adding a new category
    const handleAddCategory = async () => {
        if (!newCategory.name) return;

        try {
            await addCategory({
                name: newCategory.name,
                emoji: newCategory.emoji || "🍽️",
                color: newCategory.color || getRandomPresetColor(),
                items: [],
                id: crypto.randomUUID()
            });

            setNewCategory({
                name: "",
                emoji: "🍽️",
                color: getRandomPresetColor() // Use a random color for the next new category
            });
            setIsAddingCategory(false);
        } catch (error) {
            console.error('❌ Error adding category:', error);
        }
    };

    // Start editing a category
    const startEditingCategory = (category: Category) => {
        setEditingCategoryId(category.id);
        setEditedCategory({ ...category });
    };

    // Handle delete category click
    const handleDeleteCategoryClick = (categoryId: string) => {
        setDeleteConfirmDialog({
            open: true,
            categoryId,
            type: 'category'
        });
    };

    // Handle cancelling new category addition
    const cancelAddCategory = () => {
        setIsAddingCategory(false);
        setNewCategory({
            name: "",
            emoji: "🍽️",
            color: PRESET_COLORS[0] // Reset to default color
        });
    };

    // Render individual category item (either view mode or edit mode)
    const renderCategoryItem = (category: Category) => {
        if (editingCategoryId === category.id && editedCategory) {
            return (
                <div className="flex items-center gap-1.5 min-w-[160px] px-2 py-1 bg-muted/40 rounded-md border" key={category.id}>
                    <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
                        <PopoverTrigger asChild>
                            <Button variant="ghost" className="h-6 w-6 p-0 hover:bg-muted/60">
                                <span className="text-sm">{editedCategory.emoji}</span>
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" side="bottom" align="start">
                            <Picker
                                data={data}
                                onEmojiSelect={(emoji: EmojiObject) => {
                                    setEditedCategory({
                                        ...editedCategory,
                                        emoji: emoji.native
                                    });
                                    setShowEmojiPicker(false);
                                }}
                                theme="light"
                            />
                        </PopoverContent>
                    </Popover>
                    <Input
                        value={editedCategory.name}
                        onChange={(e) => setEditedCategory({
                            ...editedCategory,
                            name: e.target.value
                        })}
                        className="h-6 flex-1 min-w-[80px] text-xs bg-transparent border-0 focus:ring-1 focus:ring-primary/20"
                        autoFocus
                    />
                    <div className="flex gap-0.5">
                        <Button
                            size="sm"
                            variant="ghost"
                            onClick={saveCategoryEdit}
                            className="h-6 w-6 p-0 hover:bg-green-100 hover:text-green-700"
                        >
                            <Check className="h-3 w-3" />
                        </Button>
                        <Button
                            size="sm"
                            variant="ghost"
                            onClick={cancelCategoryEdit}
                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-700"
                        >
                            <X className="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            );
        }

        // Default display mode - using Button like inventory page
        return (
            <div key={category.id} className="group inline-flex items-center">
                <Button
                    variant={selectedCategory === category.id ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className={cn(
                        "flex items-center gap-1.5 px-3 py-2 whitespace-nowrap h-8 min-w-[100px]",
                        selectedCategory === category.id
                            ? "bg-primary text-primary-foreground shadow-sm"
                            : "text-muted-foreground hover:text-foreground hover:bg-muted/60"
                    )}
                >
                    <span className="text-sm">{category.emoji || "🍽️"}</span>
                    <span className="truncate max-w-[120px] text-xs font-medium">{category.name}</span>
                </Button>
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-1 flex">
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                            e.stopPropagation();
                            startEditingCategory(category);
                        }}
                        className="h-6 w-6 p-0 hover:bg-muted/60"
                    >
                        <Pencil className="h-3 w-3 text-muted-foreground" />
                    </Button>
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCategoryClick(category.id);
                        }}
                        className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                    >
                        <Trash2 className="h-3 w-3" />
                    </Button>
                </div>
            </div>
        );
    };

    // Render the new category form
    const renderNewCategoryForm = () => {
        return (
            <div className="inline-flex items-center gap-1.5 min-w-[180px] px-2 py-1 bg-muted/40 rounded-md border">
                <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
                    <PopoverTrigger asChild>
                        <Button variant="ghost" className="h-6 w-6 p-0 hover:bg-muted/60">
                            <span className="text-sm">{newCategory.emoji || "🍽️"}</span>
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" side="bottom" align="start">
                        <Picker
                            data={data}
                            onEmojiSelect={(emoji: EmojiObject) => {
                                setNewCategory({
                                    ...newCategory,
                                    emoji: emoji.native
                                });
                                setShowEmojiPicker(false);
                            }}
                            theme="light"
                        />
                    </PopoverContent>
                </Popover>
                <Input
                    ref={newCategoryInputRef}
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({
                        ...newCategory,
                        name: e.target.value
                    })}
                    placeholder="Nom catégorie"
                    className="h-6 flex-1 min-w-[100px] text-xs bg-transparent border-0 focus:ring-1 focus:ring-primary/20"
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            handleAddCategory();
                        } else if (e.key === 'Escape') {
                            cancelAddCategory();
                        }
                    }}
                />
                <div className="flex gap-0.5">
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleAddCategory}
                        disabled={!newCategory.name}
                        className="h-6 w-6 p-0 hover:bg-green-100 hover:text-green-700 disabled:opacity-50"
                    >
                        <Check className="h-3 w-3" />
                    </Button>
                    <Button
                        size="sm"
                        variant="ghost"
                        onClick={cancelAddCategory}
                        className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-700"
                    >
                        <X className="h-3 w-3" />
                    </Button>
                </div>
            </div>
        );
    };

    return (
        <div className="flex-shrink-0">
            <Tabs
                value={selectedCategory || categories[0]?.id}
                onValueChange={setSelectedCategory}
                className="w-full"
            >
                {/* Category tabs with flex wrap - tabs appear on next line */}
                <div className="flex flex-wrap gap-1 pb-2 border-b border-border/40">
                    {categories.map((category: Category) => renderCategoryItem(category))}
                    {isAddingCategory && renderNewCategoryForm()}
                    {/* Add Category Button */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddingCategory(true)}
                        className="flex items-center gap-1 h-8"
                    >
                        <PlusCircle className="h-4 w-4" />
                        Ajouter une catégorie
                    </Button>
                </div>
            </Tabs>
        </div>
    );
}

// Category details section component
function CategoryDetailsSection({
    category,
    isAddingItem,
    setIsAddingItem,
    newItem,
    setNewItem,
    newItemInputRef,
    addMenuItem,
    editingItemId,
    setEditingItemId,
    editedItem,
    setEditedItem,
    updateMenuItem,
    setDeleteConfirmDialog,
    isAddingSize,
    setIsAddingSize,
    newSize,
    setNewSize,
    addSizeToCategory,
    renameSizeInCategory,
    deleteSizeFromCategory,
    refreshCategories,
    stockItems,
    updateCategory,
    handleOpenRecipeDialog
}: CategoryDetailsSectionProps): React.ReactNode {
    // Define the local delete dialog state type without addon
    type DeleteConfirmDialogState = {
        open: boolean;
        itemId?: string;
        categoryId?: string;
        tableId?: string;
        sizeName?: string;
        type: 'item' | 'category' | 'table' | 'size';
    };

    // Add this check for sizes near the start of the component
    const hasSizes = category.sizes && category.sizes.length > 0;

    // Handle delete item click
    const handleDeleteItemClick = (itemId: string) => {
        setDeleteConfirmDialog({
            open: true,
            categoryId: category.id,
            itemId,
            type: 'item'
        } as DeleteConfirmDialogState);
    };

    // Handle adding a new menu item
    const handleAddItem = async () => {
        if (!newItem.name) return;
        try {
            const prices: { [key: string]: number } = {};
            if (category.sizes && category.sizes.length > 0) {
                category.sizes.forEach((size: string) => {
                    prices[size] = (newItem.prices && newItem.prices[size]) || 0;
                });
            } else {
                prices['regular'] = newItem.price || 0;
            }
            // Assign a unique color to the item (different from other items in the same category)
            const existingColors = category.items.map(item => item.color).filter(Boolean);
            const color = getDifferentColor(existingColors);
            await addMenuItem(category.id, {
                name: newItem.name,
                prices: prices,
                color
            });
            setNewItem({
                name: "",
                price: 0,
                prices: {}
            });
            setIsAddingItem(false);
            await refreshCategories();
        } catch (error) {
            console.error('❌ Error adding item:', error);
        }
    };

    // Start editing an item
    const startEditingItem = (item: MenuItem) => {
        setEditingItemId(item.id);
        setEditedItem({ ...item } as MenuItemWithPrices);
    };

    // Cancel item editing
    const cancelItemEdit = () => {
        setEditingItemId(null);
        setEditedItem(null);
    };

    // Save item edit
    const saveItemEdit = async () => {
        if (editedItem) {
            try {
                await updateMenuItem(category.id, editedItem.id, {
                    name: editedItem.name,
                    prices: editedItem.prices
                });
                cancelItemEdit();
            } catch (error) {
                console.error('Error updating item:', error);
            }
        }
    };

    const renderItemRow = (item: MenuItem) => {
        const isEditing = editingItemId === item.id;
        const isSelected = false;
        // Use the item's own color
        const color = item.color || '#e0e0e0';
        if (isEditing && editedItem) {
            return (
                <TableRow key={item.id} className="h-12 bg-muted/20">
                    <TableCell className="py-1">
                        <div className="flex items-center gap-2">
                            <span className="inline-block w-3 h-3 rounded-full border border-gray-300" style={{ backgroundColor: color }} />
                            <Input
                                value={editedItem.name}
                                onChange={(e) => setEditedItem({ ...editedItem, name: e.target.value })}
                                className="h-7"
                                autoFocus
                            />
                        </div>
                    </TableCell>
                    <TableCell className="py-1">
                        <div className="grid gap-1.5">
                            {Object.entries(editedItem.prices).map(([size, price]) => (
                                <div key={size} className="flex items-center gap-2">
                                    <Label className="text-xs min-w-[50px]">{size}:</Label>
                                    <Input
                                        type="number"
                                        value={price.toString()}
                                        onChange={(e) => {
                                            const value = parseFloat(e.target.value) || 0;
                                            setEditedItem({
                                                ...editedItem,
                                                prices: {
                                                    ...editedItem.prices,
                                                    [size]: value
                                                }
                                            });
                                        }}
                                        className="h-6 text-xs"
                                    />
                                </div>
                            ))}
                        </div>
                    </TableCell>
                    <TableCell className="py-1 text-right">
                        <div className="flex justify-end gap-1">
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-7 w-7 p-0"
                                onClick={saveItemEdit}
                            >
                                <Check className="h-3.5 w-3.5 text-green-600" />
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-7 w-7 p-0"
                                onClick={cancelItemEdit}
                            >
                                <X className="h-3.5 w-3.5 text-red-600" />
                            </Button>
                        </div>
                    </TableCell>
                </TableRow>
            );
        }
        return (
            <TableRow key={item.id} className={`h-9 transition-colors ${isSelected ? 'bg-primary/10 border-l-4 border-primary' : 'hover:bg-muted/50'}`}>
                <TableCell className="py-1 text-xs font-medium">
                    <div className="flex items-center gap-2">
                        <span className="inline-block w-3 h-3 rounded-full border border-gray-300" style={{ backgroundColor: color }} />
                        {item.name}
                    </div>
                </TableCell>
                <TableCell className="py-1 col-span-2">
                    <div className="text-xs text-muted-foreground">
                        {Object.entries(item.prices).map(([size, price], idx) => (
                            <span key={`${item.id}-price-${size}`}>
                                {idx > 0 && ', '}
                                {size === 'default' ? 'Standard' : size}: {price} DA
                            </span>
                        ))}
                    </div>
                </TableCell>
                <TableCell className="py-1 text-right">
                    <div className="flex justify-end items-center gap-1">
                        <Button
                            variant="outline"
                            size="sm"
                            className="h-7 px-2 border-muted-foreground/30"
                            onClick={() => startEditingItem(item)}
                        >
                            <PencilIcon className="h-3.5 w-3.5 mr-1" />
                            Modifier
                        </Button>
                        {Object.keys(item.prices).length > 1 ? (
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="h-7 px-2 border-blue-500/30 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                    >
                                        <ChefHat className="h-3.5 w-3.5 mr-1" />
                                        Coût
                                        <ChevronDown className="h-3 w-3 ml-1" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    {Object.keys(item.prices).map((size) => (
                                        <DropdownMenuItem
                                            key={size}
                                            onClick={() => handleOpenRecipeDialog(item, size)}
                                            className="cursor-pointer"
                                        >
                                            <ChefHat className="h-3.5 w-3.5 mr-2" />
                                            {size}
                                        </DropdownMenuItem>
                                    ))}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        ) : (
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-7 px-2 border-blue-500/30 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                onClick={() => handleOpenRecipeDialog(item, Object.keys(item.prices)[0])}
                            >
                                <ChefHat className="h-3.5 w-3.5 mr-1" />
                                Coût
                            </Button>
                        )}
                        <Button
                            variant="outline"
                            size="sm"
                            className="h-7 px-2 text-destructive border-destructive/30 hover:bg-destructive/10 hover:text-destructive"
                            onClick={() => handleDeleteItemClick(item.id)}
                        >
                            <TrashIcon className="h-3.5 w-3.5 mr-1" />
                            Supprimer
                        </Button>
                    </div>
                </TableCell>
            </TableRow>
        );
    };

    const renderNewItemRow = () => {
        return (
            <TableRow className="h-12 bg-muted/20">
                <TableCell className="py-1">
                    <Input
                        ref={newItemInputRef}
                        value={newItem.name}
                        onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                        className="h-7"
                        placeholder="Item name"
                        autoFocus
                    />
                </TableCell>
                <TableCell className="py-1">
                    <div className="space-y-2">
                        {(category.sizes || ['default']).map(size => (
                            <div key={size} className="flex items-center gap-2">
                                <span className="text-xs min-w-16">{size === 'default' ? 'Standard' : size}:</span>
                                <div className="relative flex-1">
                                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">DA</span>
                                    <Input
                                        type="number"
                                        min="0"
                                        className="pl-8 h-8 text-xs"
                                        value={newItem.prices?.[size] || ''}
                                        onChange={(e) => {
                                            const prices = newItem.prices ? { ...newItem.prices } : {};
                                            prices[size] = Number(e.target.value);
                                            setNewItem({ ...newItem, prices });
                                        }}
                                        placeholder={`Price for ${size}`}
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                </TableCell>
                <TableCell className="py-1 text-right">
                    <div className="flex justify-end space-x-1">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={handleAddItem}
                            disabled={!newItem.name}
                        >
                            <Check className="h-3.5 w-3.5 text-green-500" />
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => {
                                setIsAddingItem(false);
                                setNewItem({ name: "", price: 0, prices: {} });
                            }}
                        >
                            <X className="h-3.5 w-3.5 text-red-500" />
                        </Button>
                    </div>
                </TableCell>
            </TableRow>
        );
    };

    // Render the options section (sizes and addons)
    const renderOptionsSection = () => {
        return (
            <div className="grid grid-cols-2 gap-3 mb-3">
                <SizesAndPackagingSection
                    category={category}
                    isAddingSize={isAddingSize}
                    setIsAddingSize={setIsAddingSize}
                    newSize={newSize}
                    setNewSize={setNewSize}
                    addSizeToCategory={addSizeToCategory}
                    renameSizeInCategory={renameSizeInCategory}
                    deleteSizeFromCategory={deleteSizeFromCategory}
                    refreshCategories={refreshCategories}
                    stockItems={stockItems}
                    setDeleteConfirmDialog={setDeleteConfirmDialog}
                />

                <SupplementSection
                    key={category.id}
                    categoryId={category.id}
                    availableSizes={category.sizes || []}
                    stockItems={stockItems}
                    refreshCategories={refreshCategories}
                />
            </div>
        );
    };

    // 🍕 Minimal Custom Pizza Settings (at bottom)
    const renderPizzaSettings = () => {
        return (
            <div className="mt-4 pt-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Pizza className="h-3.5 w-3.5 text-orange-600" />
                        <span className="text-sm font-medium">Pizza Personnalisée</span>
                    </div>
                    <Switch
                        id={`quarterable-${category.id}`}
                        checked={category.isQuarterable || false}
                        onCheckedChange={async (checked) => {
                            try {
                                await updateCategory(category.id, {
                                    isQuarterable: checked,
                                    quarterPricingMethod: checked ? (category.quarterPricingMethod || 'max') : undefined
                                });
                                await refreshCategories();
                            } catch (error) {
                                console.error('Error updating pizza settings:', error);
                            }
                        }}
                    />
                </div>

                {category.isQuarterable && (
                    <div className="mt-2 flex items-center gap-3 text-xs text-muted-foreground">
                        <span className="mr-1">Prix:</span>
                        <div className="flex items-center gap-1">
                            <input
                                type="radio"
                                id={`pricing-max-${category.id}`}
                                name={`pricing-${category.id}`}
                                value="max"
                                checked={(category.quarterPricingMethod || 'max') === 'max'}
                                onChange={async (e) => {
                                    if (e.target.checked) {
                                        try {
                                            await updateCategory(category.id, {
                                                quarterPricingMethod: 'max'
                                            });
                                            await refreshCategories();
                                        } catch (error) {
                                            console.error('Error updating pricing method:', error);
                                        }
                                    }
                                }}
                                className="w-3 h-3 cursor-pointer"
                            />
                            <label htmlFor={`pricing-max-${category.id}`} className="cursor-pointer">Max</label>
                        </div>
                        <div className="flex items-center gap-1">
                            <input
                                type="radio"
                                id={`pricing-avg-${category.id}`}
                                name={`pricing-${category.id}`}
                                value="average"
                                checked={category.quarterPricingMethod === 'average'}
                                onChange={async (e) => {
                                    if (e.target.checked) {
                                        try {
                                            await updateCategory(category.id, {
                                                quarterPricingMethod: 'average'
                                            });
                                            await refreshCategories();
                                        } catch (error) {
                                            console.error('Error updating pricing method:', error);
                                        }
                                    }
                                }}
                                className="w-3 h-3 cursor-pointer"
                            />
                            <label htmlFor={`pricing-avg-${category.id}`} className="cursor-pointer">Moyen</label>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="mt-2 space-y-3">
            <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                    <span className="text-xl">{category.emoji}</span>
                    <h2 className="text-lg font-semibold">{category.name}</h2>
                    <Badge variant="outline" className="ml-1 text-xs">
                        {category.items.length} {category.items.length === 1 ? 'item' : 'items'}
                    </Badge>
                </div>
                <div className="flex items-center gap-2">
                    {(!category.sizes || category.sizes.length === 0) && (
                        <span className="text-xs text-muted-foreground italic">
                            Ajoutez d'abord une taille
                        </span>
                    )}
                    <Button
                        variant="outline"
                        size="sm"
                        className="gap-1"
                        onClick={() => setIsAddingItem(true)}
                        disabled={!category.sizes || category.sizes.length === 0}
                    >
                        <PlusIcon className="h-3.5 w-3.5" />
                        Ajouter un article
                    </Button>
                </div>
            </div>

            {/* Render the options section */}
            {renderOptionsSection()}

            <Card>
                <CardContent className="p-0">
                    <div className="p-4">
                        <div className="flex justify-end">
                            {(!category.sizes || category.sizes.length === 0) && (
                                <span className="text-xs text-muted-foreground italic mr-2 self-center">
                                    Ajoutez d'abord une taille
                                </span>
                            )}
                            <Button
                                variant="outline"
                                size="sm"
                                className="gap-1"
                                onClick={() => setIsAddingItem(true)}
                                disabled={!category.sizes || category.sizes.length === 0}
                            >
                                <PlusIcon className="h-3.5 w-3.5" />
                                Ajouter un article
                            </Button>
                        </div>
                    </div>
                    <Table className="border-collapse">
                        <TableHeader>
                            <TableRow className="hover:bg-transparent border-b">
                                <TableHead className="w-[25%] py-2 font-medium text-sm">Nom</TableHead>
                                <TableHead className="w-[35%] py-2 font-medium text-sm">Tarification</TableHead>
                                <TableHead className="w-[15%] py-2 text-right font-medium text-sm">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {isAddingItem && renderNewItemRow()}
                            {category.items.map((item: MenuItem) => renderItemRow(item))}
                            {category.items.length === 0 && !isAddingItem && (
                                <TableRow>
                                    <TableCell colSpan={3} className="text-center py-6 text-muted-foreground text-sm">
                                        No items yet. Click "Add Item" to create the first menu item.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            {/* 🍕 Pizza Settings at the bottom */}
            {renderPizzaSettings()}
        </div>
    );
}

// Delete confirmation dialog component
function DeleteConfirmationDialog({
    deleteConfirmDialog,
    setDeleteConfirmDialog,
    handleDeleteCategory,
    handleDeleteItem,
    handleDeleteTable,
    handleDeleteSize,
    refreshCategories,
    refreshTables,
    categories
}: DeleteConfirmationDialogProps) {
    // Close the dialog
    const onClose = () => setDeleteConfirmDialog({ ...deleteConfirmDialog, open: false });

    // Get affected menu items for size deletion
    const getAffectedItemsForSize = (): MenuItem[] => {
        if (deleteConfirmDialog.type !== 'size' ||
            !deleteConfirmDialog.categoryId ||
            !deleteConfirmDialog.sizeName) {
            return [];
        }

        // Find the category
        const category = categories.find(c => c.id === deleteConfirmDialog.categoryId);

        if (!category) return [];

        // Find items using this size
        return category.items.filter(item =>
            item.prices && item.prices[deleteConfirmDialog.sizeName as string] !== undefined
        );
    };

    // Confirm the delete operation
    const confirmDelete = async () => {
        try {
            if (deleteConfirmDialog.type === 'category' && deleteConfirmDialog.categoryId) {
                // Delete the category
                await handleDeleteCategory(deleteConfirmDialog.categoryId);
                await refreshCategories();
            } else if (deleteConfirmDialog.type === 'item' && deleteConfirmDialog.categoryId && deleteConfirmDialog.itemId) {
                // Delete the menu item
                await handleDeleteItem(deleteConfirmDialog.categoryId, deleteConfirmDialog.itemId);
                await refreshCategories();
            } else if (deleteConfirmDialog.type === 'table' && deleteConfirmDialog.tableId) {
                // Delete the table
                await handleDeleteTable(deleteConfirmDialog.tableId);
                await refreshTables();
            } else if (deleteConfirmDialog.type === 'size' && deleteConfirmDialog.categoryId && deleteConfirmDialog.sizeName) {
                // Delete the size
                await handleDeleteSize(deleteConfirmDialog.categoryId, deleteConfirmDialog.sizeName);
                await refreshCategories();
            }
            // Close the dialog
            onClose();
        } catch (error) {
            console.error('Error deleting:', error);
        }
    };

    // Get dialog content based on the delete type
    const getDialogContent = (): { title: string; message: string; extraContent?: React.ReactNode } => {
        switch (deleteConfirmDialog.type) {
            case 'category':
                return {
                    title: 'Delete Category',
                    message: 'Are you sure you want to delete this category? This will also delete all menu items in this category. This action cannot be undone.'
                };
            case 'item':
                return {
                    title: 'Delete Menu Item',
                    message: 'Are you sure you want to delete this menu item? This action cannot be undone.'
                };
            case 'table':
                return {
                    title: 'Delete Table',
                    message: 'Are you sure you want to delete this table? This action cannot be undone.'
                };
            case 'size':
                const affectedItems = getAffectedItemsForSize();
                return {
                    title: 'Delete Size',
                    message: 'Are you sure you want to delete this size? This will remove the size option and its price from all menu items shown below. This action cannot be undone.',
                    extraContent: (
                        <div className="mt-2 border border-destructive/20 rounded p-2 bg-destructive/5 max-h-32 overflow-y-auto">
                            <p className="text-xs font-medium mb-1">Items affected ({affectedItems.length}):</p>
                            {affectedItems.length > 0 ? (
                                <div className="space-y-1">
                                    {affectedItems.map(item => (
                                        <div key={item.id} className="flex justify-between text-xs">
                                            <span className="font-medium">{item.name}</span>
                                            <span className="text-muted-foreground">
                                                {item.prices[deleteConfirmDialog.sizeName as string]} DA
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-xs text-muted-foreground">No items will be affected</p>
                            )}
                        </div>
                    )
                };
            default:
                return {
                    title: 'Delete Confirmation',
                    message: 'Are you sure you want to delete this item? This action cannot be undone.'
                };
        }
    };

    const { title, message, extraContent } = getDialogContent();

    return (
        <Dialog open={deleteConfirmDialog.open} onOpenChange={(open) => {
            if (!open) onClose();
        }}>
            <DialogContent className="max-w-xs">
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                </DialogHeader>
                <p className="text-sm text-muted-foreground py-2">{message}</p>
                {extraContent}
                <DialogFooter className="flex flex-row justify-between gap-2">
                    <Button
                        variant="outline"
                        className="flex-1"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        variant="destructive"
                        className="flex-1"
                        onClick={confirmDelete}
                    >
                        Delete
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export { CategoryDetailsSection };
export { DeleteConfirmationDialog };