'use client';

import { motion } from 'framer-motion';

/**
 * 🎬 Demo Video Section Component
 * 
 * Uses Mux video player for streaming demo video
 */

export default function DemoVideoSection() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
      className="mt-12 w-full max-w-6xl mx-auto"
    >
      <div className="rounded-lg shadow-2xl overflow-hidden relative">
        <iframe
          src="https://player.mux.com/pNIhzAlIG56ipnwQ01E02eaorbFiILdX600trdy2wYKYlU?metadata-video-title=demo-video&video-title=demo-video&accent-color=%23f56e00&autoplay=1&muted=1&loop=1&controls=false&hide_controls=true&disable_cookies=true&hide_title=true"
          style={{ 
            width: '100%', 
            border: 'none', 
            aspectRatio: '35/24',
            pointerEvents: 'none'
          }}
          allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
          allowFullScreen
          title="Bistro Demo Video"
        />
        {/* Overlay to prevent interaction and hide any remaining controls */}
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{ zIndex: 1 }}
        />
      </div>
    </motion.div>
  );
}