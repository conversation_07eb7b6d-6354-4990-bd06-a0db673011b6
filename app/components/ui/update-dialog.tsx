"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Download, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface UpdateDialogProps {
  isOpen: boolean
  onClose: () => void
  updateInfo?: {
    version: string
    releaseNotes?: string
    downloadProgress?: number
    status: 'checking' | 'available' | 'downloading' | 'downloaded' | 'error'
  }
  onInstallUpdate?: () => void
  onDismiss?: () => void
}

export function UpdateDialog({ 
  isOpen, 
  onClose, 
  updateInfo, 
  onInstallUpdate, 
  onDismiss 
}: UpdateDialogProps) {
  const { toast } = useToast()
  const [isInstalling, setIsInstalling] = useState(false)

  const handleInstallNow = async () => {
    setIsInstalling(true)
    try {
      await onInstallUpdate?.()
      toast({
        title: "🔄 Restarting...",
        description: "The app will restart to apply the update.",
      })
    } catch (error) {
      toast({
        title: "❌ Update Failed",
        description: "Failed to install update. Please try again.",
        variant: "destructive",
      })
      setIsInstalling(false)
    }
  }

  const getStatusIcon = () => {
    switch (updateInfo?.status) {
      case 'checking':
        return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />
      case 'available':
      case 'downloading':
        return <Download className="h-5 w-5 text-blue-500" />
      case 'downloaded':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <RefreshCw className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = () => {
    switch (updateInfo?.status) {
      case 'checking':
        return 'Checking for updates...'
      case 'available':
        return 'Update available'
      case 'downloading':
        return 'Downloading update...'
      case 'downloaded':
        return 'Update ready to install'
      case 'error':
        return 'Update failed'
      default:
        return 'Checking...'
    }
  }

  const getStatusVariant = () => {
    switch (updateInfo?.status) {
      case 'available':
      case 'downloading':
        return 'default' as const
      case 'downloaded':
        return 'default' as const
      case 'error':
        return 'destructive' as const
      default:
        return 'secondary' as const
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getStatusIcon()}
            App Update
          </DialogTitle>
          <DialogDescription className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant={getStatusVariant()}>
                {getStatusText()}
              </Badge>
              {updateInfo?.version && (
                <Badge variant="outline">
                  v{updateInfo.version}
                </Badge>
              )}
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Download Progress */}
          {updateInfo?.status === 'downloading' && updateInfo.downloadProgress !== undefined && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Downloading...</span>
                <span>{Math.round(updateInfo.downloadProgress)}%</span>
              </div>
              <Progress value={updateInfo.downloadProgress} className="h-2" />
            </div>
          )}

          {/* Release Notes */}
          {updateInfo?.releaseNotes && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">What's new:</h4>
              <div className="text-sm text-muted-foreground bg-muted/50 rounded-md p-3 max-h-32 overflow-y-auto">
                {updateInfo.releaseNotes}
              </div>
            </div>
          )}

          {/* Status Messages */}
          {updateInfo?.status === 'available' && (
            <p className="text-sm text-muted-foreground">
              📥 A new version is available and will download in the background.
            </p>
          )}

          {updateInfo?.status === 'downloaded' && (
            <p className="text-sm text-muted-foreground">
              ✨ The update is ready! Restart the app to apply the new version.
            </p>
          )}

          {updateInfo?.status === 'error' && (
            <p className="text-sm text-destructive">
              ❌ Failed to download the update. Please check your internet connection.
            </p>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {updateInfo?.status === 'downloaded' ? (
            <>
              <Button 
                variant="outline" 
                onClick={onDismiss}
                disabled={isInstalling}
              >
                Later
              </Button>
              <Button 
                onClick={handleInstallNow}
                disabled={isInstalling}
                className="bg-green-600 hover:bg-green-700"
              >
                {isInstalling ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Restarting...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Restart Now
                  </>
                )}
              </Button>
            </>
          ) : updateInfo?.status === 'available' ? (
            <>
              <Button variant="outline" onClick={onClose}>
                Continue Working
              </Button>
              <Button onClick={onClose}>
                <Download className="mr-2 h-4 w-4" />
                Download in Background
              </Button>
            </>
          ) : (
            <Button onClick={onClose}>
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook for managing update state
export function useUpdateDialog() {
  const [isOpen, setIsOpen] = useState(false)
  const [updateInfo, setUpdateInfo] = useState<UpdateDialogProps['updateInfo']>()

  useEffect(() => {
    // Listen for Electron update events via IPC
    if (typeof window !== 'undefined' && window.electronAPI) {
      const handleUpdateAvailable = (info: any) => {
        setUpdateInfo({
          version: info.version,
          releaseNotes: info.releaseNotes,
          status: 'available'
        })
        setIsOpen(true)
      }

      const handleDownloadProgress = (progress: any) => {
        setUpdateInfo(prev => ({
          ...prev!,
          downloadProgress: progress.percent,
          status: 'downloading'
        }))
      }

      const handleUpdateDownloaded = () => {
        setUpdateInfo(prev => ({
          ...prev!,
          status: 'downloaded'
        }))
      }

      const handleUpdateError = (error: any) => {
        setUpdateInfo(prev => ({
          ...prev!,
          status: 'error'
        }))
      }

      // Register IPC listeners
      window.electronAPI.onUpdateAvailable(handleUpdateAvailable)
      window.electronAPI.onDownloadProgress(handleDownloadProgress)
      window.electronAPI.onUpdateDownloaded(handleUpdateDownloaded)
      window.electronAPI.onUpdateError(handleUpdateError)

      return () => {
        // Cleanup listeners if needed
      }
    }
  }, [])

  const installUpdate = async () => {
    if (window.electronAPI) {
      await window.electronAPI.quitAndInstall()
    }
  }

  const dismissUpdate = () => {
    setIsOpen(false)
    // Could store user preference to not show again for this version
  }

  return {
    isOpen,
    updateInfo,
    onClose: () => setIsOpen(false),
    onInstallUpdate: installUpdate,
    onDismiss: dismissUpdate
  }
}