'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  getPrintEnvironmentInfo, 
  shouldShowPrintPreview, 
  shouldUseSilentPrinting,
  isDevelopmentMode,
  isProductionMode,
  isElectronEnvironment
} from '@/lib/utils/environment';

/**
 * Debug component to show print environment configuration
 * Only visible in development mode
 */
export default function PrintEnvironmentDebug() {
  // Only show in development
  if (!isDevelopmentMode()) {
    return null;
  }

  const envInfo = getPrintEnvironmentInfo();
  const showPreviewSuccess = shouldShowPrintPreview(true);
  const showPreviewFailed = shouldShowPrintPreview(false);
  const useSilentPrinting = shouldUseSilentPrinting();

  return (
    <Card className="border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="text-yellow-800 flex items-center gap-2">
          🐛 Print Environment Debug
          <Badge variant="outline" className="text-xs">DEV ONLY</Badge>
        </CardTitle>
        <CardDescription className="text-yellow-700">
          Current print environment configuration and behavior
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Environment Info */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-sm mb-2">Environment</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Mode:</span>
                <Badge variant={envInfo.mode === 'development' ? 'default' : 'secondary'}>
                  {envInfo.mode}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Platform:</span>
                <Badge variant={envInfo.platform === 'electron' ? 'default' : 'secondary'}>
                  {envInfo.platform}
                </Badge>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm mb-2">Print Behavior</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Silent Printing:</span>
                <Badge variant={envInfo.silentPrinting ? 'default' : 'secondary'}>
                  {envInfo.silentPrinting ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Show Previews:</span>
                <Badge variant={envInfo.showPreviews ? 'default' : 'secondary'}>
                  {envInfo.showPreviews ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Preview Logic Test */}
        <div>
          <h4 className="font-medium text-sm mb-2">Preview Logic Test</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex justify-between">
              <span>Print Success:</span>
              <Badge variant={showPreviewSuccess ? 'default' : 'secondary'}>
                {showPreviewSuccess ? 'Show Preview' : 'No Preview'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Print Failed:</span>
              <Badge variant={showPreviewFailed ? 'destructive' : 'secondary'}>
                {showPreviewFailed ? 'Show Preview' : 'No Preview'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Environment Checks */}
        <div>
          <h4 className="font-medium text-sm mb-2">Environment Checks</h4>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${isDevelopmentMode() ? 'bg-green-500' : 'bg-gray-300'}`} />
              <span>Development</span>
            </div>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${isProductionMode() ? 'bg-green-500' : 'bg-gray-300'}`} />
              <span>Production</span>
            </div>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${isElectronEnvironment() ? 'bg-green-500' : 'bg-gray-300'}`} />
              <span>Electron</span>
            </div>
          </div>
        </div>

        {/* Expected Behavior */}
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <h4 className="font-medium text-sm mb-2 text-blue-800">Expected Behavior</h4>
          <div className="text-xs text-blue-700 space-y-1">
            {envInfo.mode === 'development' ? (
              <>
                <div>• Print previews will always be shown</div>
                <div>• Longer delays for debugging</div>
                <div>• Debug logging enabled</div>
              </>
            ) : (
              <>
                <div>• Print previews only shown on failure</div>
                <div>• Faster print execution</div>
                <div>• Silent printing in Electron</div>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
