"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { useTableDB } from '@/lib/hooks/useTableDB';
import type { Table } from '@/lib/db/v4';

export function TableLayout() {
  const {
    tables,
    isLoading,
    addTable,
    updateTable,
    deleteTable,
  } = useTableDB();

  const [isAddTableDialogOpen, setIsAddTableDialogOpen] = useState(false);
  const [newTableName, setNewTableName] = useState("");
  const [newTableSeats, setNewTableSeats] = useState(4);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);

  const handleAddTable = async () => {
    try {
      await addTable({
        name: newTableName || `T${tables.length + 1}`,
        seats: newTableSeats,
        position: { x: 0, y: 0 },
        status: 'free',
      });
      setIsAddTableDialogOpen(false);
      setNewTableName("");
      setNewTableSeats(4);
    } catch (err) {
      // Optionally show error
    }
  };

  const handleEditTable = (table: Table) => {
    setSelectedTable(table);
  };

  const handleUpdateTable = async () => {
    if (!selectedTable) return;
    try {
      await updateTable(selectedTable.id, {
        name: selectedTable.name,
        seats: selectedTable.seats,
      });
      setSelectedTable(null);
    } catch (err) {
      // Optionally show error
    }
  };

  const handleDeleteTable = async (tableId: string) => {
    try {
      await deleteTable(tableId);
    } catch (err) {
      // Optionally show error
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {tables.map((table) => (
          <Card key={table.id} className="relative hover:shadow-lg transition-all duration-200">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-medium">{table.name}</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditTable(table)}
                    className="hover:bg-secondary"
                  >
                    Edit
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteTable(table.id)}
                    className="hover:bg-destructive hover:text-destructive-foreground"
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Seats:</span>
                <span className="font-medium">{table.seats}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={isAddTableDialogOpen} onOpenChange={setIsAddTableDialogOpen}>
        <DialogTrigger asChild>
          <Button className="mt-6">Add Table</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Table</DialogTitle>
            <DialogDescription>Create a new table for your restaurant layout.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Table Name</Label>
              <Input
                id="name"
                placeholder={`T${tables.length + 1}`}
                value={newTableName}
                onChange={(e) => setNewTableName(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="seats">Number of Seats</Label>
              <Input
                id="seats"
                type="number"
                min="1"
                value={newTableSeats}
                onChange={(e) => setNewTableSeats(parseInt(e.target.value) || 4)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleAddTable}>Add Table</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {selectedTable && (
        <Dialog open={!!selectedTable} onOpenChange={() => setSelectedTable(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Table</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Table Name</Label>
                <Input
                  id="edit-name"
                  value={selectedTable.name}
                  onChange={(e) =>
                    setSelectedTable({
                      ...selectedTable,
                      name: e.target.value,
                    })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-seats">Number of Seats</Label>
                <Input
                  id="edit-seats"
                  type="number"
                  min="1"
                  value={selectedTable.seats}
                  onChange={(e) =>
                    setSelectedTable({
                      ...selectedTable,
                      seats: parseInt(e.target.value) || 4,
                    })
                  }
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleUpdateTable}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
} 