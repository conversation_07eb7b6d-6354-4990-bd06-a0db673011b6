import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowLeftIcon,
  BanknoteIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  RefreshCwIcon
} from 'lucide-react';
import { formatCurrency } from "@/lib/utils/currency";

interface FinanceHeaderProps {
  title: string;
  subtitle: string;
  icon?: React.ReactNode;
  backUrl?: string;
  cashBalance?: number;
  todayIncome?: number;
  todayExpense?: number;
  isLoading?: boolean;
  onRefresh?: () => void;
}

export default function FinanceHeader({
  title,
  subtitle,
  icon,
  backUrl = '/finance',
  cashBalance,
  todayIncome,
  todayExpense,
  isLoading = false,
  onRefresh
}: FinanceHeaderProps) {
  const router = useRouter();
  const { navigate } = useStaticNavigation();

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2 md:flex-row md:justify-between md:items-center">
        <div className="flex items-center gap-2">
          {backUrl && (
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => {
                const cleanBackUrl = backUrl.replace(/^\//, '').replace(/\.html$/, '');
                navigate(cleanBackUrl);
              }}
            >
              <ArrowLeftIcon className="h-4 w-4" />
            </Button>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              {icon}
              {title}
            </h1>
            <p className="text-muted-foreground">
              {subtitle}
            </p>
          </div>
        </div>
        
        {onRefresh && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onRefresh}
            disabled={isLoading}
          >
            <RefreshCwIcon className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
        )}
      </div>
      
      {/* Financial snapshot - optional */}
      {(cashBalance !== undefined || todayIncome !== undefined || todayExpense !== undefined) && (
        <Card className="bg-muted/50">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {cashBalance !== undefined && (
                <div className="flex items-center gap-3">
                  <div className="rounded-full p-2 bg-amber-100">
                    <BanknoteIcon className="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Cash Balance</p>
                    <p className="text-lg font-bold">{formatCurrency(cashBalance)}</p>
                  </div>
                </div>
              )}
              
              {todayIncome !== undefined && (
                <div className="flex items-center gap-3">
                  <div className="rounded-full p-2 bg-green-100">
                    <TrendingUpIcon className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Today's Income</p>
                    <p className="text-lg font-bold">{formatCurrency(todayIncome)}</p>
                  </div>
                </div>
              )}
              
              {todayExpense !== undefined && (
                <div className="flex items-center gap-3">
                  <div className="rounded-full p-2 bg-red-100">
                    <TrendingDownIcon className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Today's Expenses</p>
                    <p className="text-lg font-bold">{formatCurrency(todayExpense)}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 