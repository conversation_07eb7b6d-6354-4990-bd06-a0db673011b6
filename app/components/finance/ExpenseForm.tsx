import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarIcon, CircleDollarSignIcon, BanknoteIcon, WalletIcon, RepeatIcon, ClockIcon, ArrowRightIcon } from "lucide-react";
import { format } from 'date-fns';

// Expense categories with icons
export const EXPENSE_CATEGORIES = [
  { id: 'rent', name: 'Rent', icon: 'building' },
  { id: 'utilities', name: 'Utilities', icon: 'zap' },
  { id: 'telecom', name: 'Phone & Internet', icon: 'phone' },
  { id: 'supplies', name: 'Supplies', icon: 'shopping-cart' },
  { id: 'salaries', name: 'Salaries', icon: 'users' },
  { id: 'tax', name: 'Taxes', icon: 'calculator' },
  { id: 'maintenance', name: 'Maintenance', icon: 'tool' },
  { id: 'marketing', name: 'Marketing', icon: 'megaphone' },
  { id: 'insurance', name: 'Insurance', icon: 'shield' },
  { id: 'other', name: 'Other', icon: 'file-text' },
];

// Payment methods
export const PAYMENT_METHODS = [
  { id: 'cash', name: 'Cash' },
  { id: 'bank', name: 'Bank Transfer' },
  { id: 'transfer', name: 'Card Payment' },
  { id: 'credit', name: 'Credit' },
];

// Frequency options for recurring expenses
export const FREQUENCY_OPTIONS = [
  { id: 'monthly', name: 'Monthly' },
  { id: 'quarterly', name: 'Quarterly' },
  { id: 'yearly', name: 'Yearly' },
];

// Payment sources (where the money comes from)
export const PAYMENT_SOURCES = [
  { id: 'cash_register', name: 'La Caisse', icon: 'banknote' },
  { id: 'capital', name: 'Owner\'s Pocket', icon: 'wallet' },
  { id: 'bank_account', name: 'Bank Account', icon: 'credit-card' },
];

export interface ExpenseFormData {
  category: string;
  description: string;
  amount: number;
  date: Date;
  paymentMethod: 'cash' | 'bank' | 'transfer' | 'credit';
  notes: string;
  isRecurring: boolean;
  frequency: 'monthly' | 'quarterly' | 'yearly';
  paymentSource?: 'cash_register' | 'capital' | 'bank_account'; // Add payment source
  isPaid?: boolean; // Is the expense already paid
}

interface ExpenseFormProps {
  onSubmit: (expenseData: ExpenseFormData) => void;
  onCancel?: () => void;
  initialData?: Partial<ExpenseFormData>;
  title?: string;
  description?: string;
  submitLabel?: string;
  isLoading?: boolean;
}

export default function ExpenseForm({
  onSubmit,
  onCancel,
  initialData,
  title = "Record Expense",
  description = "Add a new expense to track your business costs",
  submitLabel = "Save Expense",
  isLoading = false
}: ExpenseFormProps) {
  // State for active tab (one-time or recurring)
  const [activeTab, setActiveTab] = useState<'one-time' | 'recurring'>(initialData?.isRecurring ? 'recurring' : 'one-time');

  // Initialize form data with defaults and any provided initial data
  const [formData, setFormData] = useState<ExpenseFormData>({
    category: initialData?.category || '',
    description: initialData?.description || '',
    amount: initialData?.amount || 0,
    date: initialData?.date || new Date(),
    paymentMethod: initialData?.paymentMethod || 'cash',
    notes: initialData?.notes || '',
    isRecurring: initialData?.isRecurring || false,
    frequency: initialData?.frequency || 'monthly',
    paymentSource: initialData?.paymentSource || 'cash_register',
    isPaid: initialData?.isPaid || true, // Default to paid immediately
  });

  // State for date pickers
  const [oneTimeDatePickerOpen, setOneTimeDatePickerOpen] = useState(false);
  const [recurringDatePickerOpen, setRecurringDatePickerOpen] = useState(false);

  // Update isRecurring when tab changes
  const handleTabChange = (value: string) => {
    const isRecurring = value === 'recurring';
    setActiveTab(value as 'one-time' | 'recurring');
    setFormData(prev => ({
      ...prev,
      isRecurring
    }));
  };

  // Handle input changes
  const handleChange = (field: keyof ExpenseFormData, value: any) => {
    console.log(`Changing ${field} to:`, value);
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Submitting form data:', formData);
    onSubmit(formData);
  };

  // Check if form is valid
  const isFormValid = () => {
    // For recurring expenses, we don't require description or payment method
    if (formData.isRecurring) {
      const valid = (
        formData.category &&
        formData.amount > 0 &&
        formData.date &&
        formData.frequency
      );
      console.log('Recurring expense form valid:', valid);
      return valid;
    }

    // For regular expenses, require all fields
    const valid = (
      formData.category &&
      formData.description &&
      formData.amount > 0 &&
      formData.date &&
      formData.paymentMethod
    );
    console.log('One-time expense form valid:', valid);
    return valid;
  };

  return (
    <div className="w-full max-h-[80vh] overflow-y-auto">
      <div className="p-4 border-b sticky top-0 bg-background z-10">
        <h2 className="text-lg font-semibold flex items-center gap-2">
          <CircleDollarSignIcon className="h-5 w-5" />
          {title}
        </h2>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>

      <div className="p-4">
        {/* Tabs for expense type selection */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="one-time" className="flex items-center gap-2">
              <ArrowRightIcon className="h-4 w-4" />
              One-time Expense
            </TabsTrigger>
            <TabsTrigger value="recurring" className="flex items-center gap-2">
              <RepeatIcon className="h-4 w-4" />
              Recurring Expense
            </TabsTrigger>
          </TabsList>

          {/* One-time expense form */}
          <TabsContent value="one-time" className="mt-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="col-span-1 md:col-span-2">
                  <Label htmlFor="description" className="text-sm font-medium">Description *</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleChange('description', e.target.value)}
                    placeholder="What is this expense for?"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="amount" className="text-sm font-medium">Amount (DZD) *</Label>
                  <div className="relative mt-1">
                    <Input
                      id="amount"
                      type="number"
                      min="0"
                      step="1"
                      className="pl-8"
                      value={formData.amount || ''}
                      onChange={(e) => handleChange('amount', parseFloat(e.target.value) || 0)}
                    />
                    <span className="absolute left-3 top-2 text-muted-foreground">DA</span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="category" className="text-sm font-medium">Category *</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => handleChange('category', value)}
                  >
                    <SelectTrigger id="category" className="mt-1">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPENSE_CATEGORIES.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium">Date *</Label>
                  <Popover open={oneTimeDatePickerOpen} onOpenChange={setOneTimeDatePickerOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal mt-1"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.date ? format(formData.date, 'PP') : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.date}
                        onSelect={(date) => {
                          if (date) {
                            handleChange('date', date);
                            setOneTimeDatePickerOpen(false);
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label htmlFor="paymentMethod" className="text-sm font-medium">Payment Method *</Label>
                  <Select
                    value={formData.paymentMethod}
                    onValueChange={(value) => handleChange('paymentMethod', value as any)}
                  >
                    <SelectTrigger id="paymentMethod" className="mt-1">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      {PAYMENT_METHODS.map((method) => (
                        <SelectItem key={method.id} value={method.id}>
                          {method.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 mt-2">
                  <Checkbox
                    id="isPaid"
                    checked={formData.isPaid}
                    onCheckedChange={(checked) => handleChange('isPaid', checked)}
                  />
                  <Label htmlFor="isPaid" className="text-sm">
                    Already paid
                  </Label>
                </div>

                {/* Payment Source - Only shown when isPaid is true */}
                {formData.isPaid && (
                  <div>
                    <Label htmlFor="paymentSource" className="text-sm font-medium">Payment Source</Label>
                    <Select
                      value={formData.paymentSource}
                      onValueChange={(value) => handleChange('paymentSource', value as any)}
                    >
                      <SelectTrigger id="paymentSource" className="mt-1">
                        <SelectValue placeholder="Select source" />
                      </SelectTrigger>
                      <SelectContent>
                        {PAYMENT_SOURCES.map((source) => (
                          <SelectItem key={source.id} value={source.id}>
                            {source.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="col-span-1 md:col-span-2">
                  <Label htmlFor="notes" className="text-sm font-medium">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleChange('notes', e.target.value)}
                    placeholder="Any additional details..."
                    className="mt-1"
                    rows={2}
                  />
                </div>
              </div>
            </form>
          </TabsContent>

          {/* Recurring expense form */}
          <TabsContent value="recurring" className="mt-4">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
              <p className="text-sm text-blue-800 flex items-center">
                <RepeatIcon className="h-4 w-4 mr-2" />
                Recurring expenses are automatically generated based on the frequency you select.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="col-span-1 md:col-span-2">
                  <Label htmlFor="description" className="text-sm font-medium">Description (Optional)</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleChange('description', e.target.value)}
                    placeholder="Optional description for this recurring expense"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="amount" className="text-sm font-medium">Amount (DZD) *</Label>
                  <div className="relative mt-1">
                    <Input
                      id="amount"
                      type="number"
                      min="0"
                      step="1"
                      className="pl-8"
                      value={formData.amount || ''}
                      onChange={(e) => handleChange('amount', parseFloat(e.target.value) || 0)}
                    />
                    <span className="absolute left-3 top-2 text-muted-foreground">DA</span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="category" className="text-sm font-medium">Category *</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => handleChange('category', value)}
                  >
                    <SelectTrigger id="category" className="mt-1">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPENSE_CATEGORIES.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium">Start Date *</Label>
                  <Popover open={recurringDatePickerOpen} onOpenChange={setRecurringDatePickerOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal mt-1"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.date ? format(formData.date, 'PP') : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.date}
                        onSelect={(date) => {
                          if (date) {
                            handleChange('date', date);
                            setRecurringDatePickerOpen(false);
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label htmlFor="frequency" className="text-sm font-medium">Frequency *</Label>
                  <Select
                    value={formData.frequency}
                    onValueChange={(value) => handleChange('frequency', value as any)}
                  >
                    <SelectTrigger id="frequency" className="mt-1">
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      {FREQUENCY_OPTIONS.map((option) => (
                        <SelectItem key={option.id} value={option.id}>
                          {option.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1 md:col-span-2">
                  <Label htmlFor="notes" className="text-sm font-medium">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleChange('notes', e.target.value)}
                    placeholder="Any additional details..."
                    className="mt-1"
                    rows={2}
                  />
                </div>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </div>

      <div className="p-4 border-t sticky bottom-0 bg-background flex justify-between items-center">
        {onCancel && (
          <Button variant="outline" onClick={onCancel} disabled={isLoading} size="sm">
            Cancel
          </Button>
        )}
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid() || isLoading}
          size="sm"
          className="bg-primary hover:bg-primary/90"
        >
          {isLoading ? "Processing..." : activeTab === 'recurring' ? "Save Recurring Expense" : submitLabel}
        </Button>
      </div>
    </div>
  );
}