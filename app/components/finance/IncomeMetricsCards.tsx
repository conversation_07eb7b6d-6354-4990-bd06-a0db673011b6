"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { formatCurrency } from '@/lib/utils/currency'
import {
  TrendingUpIcon,
  CircleDollarSignIcon,
  ShoppingBagIcon,
  CalendarIcon
} from 'lucide-react'

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  trend?: 'up' | 'down';
  isCurrency?: boolean;
  className?: string;
}

function CompactMetricCard({
  title,
  value,
  icon,
  trend,
  isCurrency = true,
  className
}: MetricCardProps) {
  // Format the value based on type
  const formattedValue = typeof value === 'number'
    ? isCurrency
      ? formatCurrency(value)
      : value.toFixed(1)
    : value;

  return (
    <Card className={className}>
      <CardContent className="p-3">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-xs font-medium text-muted-foreground">{title}</p>
            <p className={`text-base font-semibold mt-0.5 ${trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : ''}`}>
              {formattedValue}
            </p>
          </div>
          <div className={`p-1.5 rounded-full ${trend === 'up' ? 'bg-green-100/80' : trend === 'down' ? 'bg-red-100/80' : 'bg-blue-100/80'}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface IncomeMetricsCardsProps {
  totalIncome: number;
  averageOrderValue: number;
  averageItemsPerOrder: number;
  totalOrders: number;
  className?: string;
}

export default function IncomeMetricsCards({
  totalIncome,
  averageOrderValue,
  averageItemsPerOrder,
  totalOrders,
  className
}: IncomeMetricsCardsProps) {
  return (
    <div className={`grid grid-cols-2 sm:grid-cols-4 gap-3 ${className}`}>
      <CompactMetricCard
        title="Total Income"
        value={totalIncome}
        icon={<TrendingUpIcon className="h-4 w-4 text-green-600" />}
        trend="up"
      />
      <CompactMetricCard
        title="Avg Order Value"
        value={averageOrderValue}
        icon={<CircleDollarSignIcon className="h-4 w-4 text-blue-600" />}
      />
      <CompactMetricCard
        title="Avg Items/Order"
        value={averageItemsPerOrder}
        icon={<ShoppingBagIcon className="h-4 w-4 text-purple-600" />}
        isCurrency={false}
      />
      <CompactMetricCard
        title="Total Orders"
        value={totalOrders}
        icon={<CalendarIcon className="h-4 w-4 text-amber-600" />}
        isCurrency={false}
      />
    </div>
  )
}
