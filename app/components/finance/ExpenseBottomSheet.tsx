"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/app/components/ui/date-picker";
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { PlusIcon, ArrowDownIcon } from 'lucide-react';
import { useExpenses } from '@/lib/services/finance-service';

// Expense categories
const EXPENSE_CATEGORIES = [
  { value: 'rent', label: 'Rent' },
  { value: 'utilities', label: 'Utilities (Water, Electricity, etc.)' },
  { value: 'salaries', label: 'Salaries' },
  { value: 'inventory', label: 'Inventory Purchases' },
  { value: 'equipment', label: 'Equipment' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'taxes', label: 'Taxes' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'transport', label: 'Transport' },
  { value: 'other', label: 'Other' },
];

// Payment methods
const PAYMENT_METHODS = [
  { value: 'cash', label: 'Cash' },
  { value: 'bank', label: 'Bank Transfer' },
  { value: 'transfer', label: 'Mobile Transfer' },
  { value: 'credit', label: 'Credit (To Pay Later)' },
];

// Payment sources
const PAYMENT_SOURCES = [
  { value: 'cash_register', label: 'Cash Register' },
  { value: 'capital', label: 'Capital' },
  { value: 'bank_account', label: 'Bank Account' },
];

export interface ExpenseFormData {
  date: string;
  category: string;
  description: string;
  amount: number;
  paymentMethod: 'cash' | 'bank' | 'transfer' | 'credit';
  notes?: string;
  isRecurring?: boolean;
  frequency?: 'monthly' | 'quarterly' | 'yearly';
  nextDueDate?: string;
  isPaid?: boolean;
  paymentSource?: 'cash_register' | 'capital' | 'bank_account';
}

interface ExpenseBottomSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ExpenseFormData) => Promise<void>;
}

export default function ExpenseBottomSheet({
  open,
  onOpenChange,
  onSubmit
}: ExpenseBottomSheetProps) {
  // Form state
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [category, setCategory] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'bank' | 'transfer' | 'credit'>('cash');
  const [notes, setNotes] = useState<string>('');
  const [isRecurring, setIsRecurring] = useState<boolean>(false);
  const [frequency, setFrequency] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');
  const [nextDueDate, setNextDueDate] = useState<Date | undefined>(undefined);
  const [isPaid, setIsPaid] = useState<boolean>(true);
  const [paymentSource, setPaymentSource] = useState<'cash_register' | 'capital' | 'bank_account'>('cash_register');

  // Loading state
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const { addExpense } = useExpenses();

  // Reset form
  const resetForm = () => {
    setDate(new Date());
    setCategory('');
    setDescription('');
    setAmount('');
    setPaymentMethod('cash');
    setNotes('');
    setIsRecurring(false);
    setFrequency('monthly');
    setNextDueDate(undefined);
    setIsPaid(true);
    setPaymentSource('cash_register');
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!category || !description || !amount) {
      // Show validation error
      return;
    }

    setIsSubmitting(true);

    try {
      // knowledge: v4 DatePicker compliance and linter fix start
      await addExpense(
        format(date ?? new Date(), 'yyyy-MM-dd'),
        category,
        description,
        parseFloat(amount),
        paymentMethod,
        notes,
        isRecurring,
        isRecurring ? frequency : undefined,
        isRecurring && nextDueDate ? format(nextDueDate, 'yyyy-MM-dd') : undefined,
        isPaid,
        isPaid ? paymentSource : undefined
      );
      // knowledge: v4 DatePicker compliance and linter fix end
      // Reset form and close sheet on success
      resetForm();
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting expense:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="h-[90vh] sm:h-[80vh] overflow-y-auto">
        <SheetHeader className="mb-4">
          <SheetTitle>New Expense</SheetTitle>
          <SheetDescription>
            Record a new expense in the system
          </SheetDescription>
        </SheetHeader>

        <div className="grid gap-4 py-4">
          {/* Basic expense details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <DatePicker
                date={date}
                setDate={setDate}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="amount">Amount (DA)</Label>
              <div className="relative">
                <ArrowDownIcon className="absolute left-3 top-2.5 h-4 w-4 text-red-500" />
                <Input
                  id="amount"
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="e.g., 5000"
                  className="pl-9"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {EXPENSE_CATEGORIES.map((cat) => (
                  <SelectItem key={cat.value} value={cat.value}>
                    {cat.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Expense description"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="paymentMethod">Payment Method</Label>
            <Select
              value={paymentMethod}
              onValueChange={(value) => {
                setPaymentMethod(value as 'cash' | 'bank' | 'transfer' | 'credit');
                // If credit is selected, set isPaid to false
                if (value === 'credit') {
                  setIsPaid(false);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a method" />
              </SelectTrigger>
              <SelectContent>
                {PAYMENT_METHODS.map((method) => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Payment details */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPaid"
                checked={isPaid}
                onChange={(e) => setIsPaid(e.target.checked)}
                className="rounded border-gray-300 text-primary focus:ring-primary"
                disabled={paymentMethod === 'credit'}
              />
              <Label htmlFor="isPaid" className="cursor-pointer">
                Paid immediately
              </Label>
            </div>
          </div>

          {isPaid && (
            <div className="space-y-2">
              <Label htmlFor="paymentSource">Payment Source</Label>
              <Select value={paymentSource} onValueChange={(value) => setPaymentSource(value as 'cash_register' | 'capital' | 'bank_account')}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a source" />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_SOURCES.map((source) => (
                    <SelectItem key={source.value} value={source.value}>
                      {source.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Recurring expense details */}
          <div className="space-y-2 pt-2 border-t">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isRecurring"
                checked={isRecurring}
                onChange={(e) => setIsRecurring(e.target.checked)}
                className="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="isRecurring" className="cursor-pointer">
                Recurring expense
              </Label>
            </div>
          </div>

          {isRecurring && (
            <>
              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Select value={frequency} onValueChange={(value) => setFrequency(value as 'monthly' | 'quarterly' | 'yearly')}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="nextDueDate">Next due date</Label>
                <DatePicker
                  date={nextDueDate}
                  setDate={setNextDueDate}
                />
              </div>
            </>
          )}

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes..."
              rows={3}
            />
          </div>
        </div>

        <SheetFooter className="mt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !category || !description || !amount}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            {isSubmitting ? 'Saving...' : 'Save Expense'}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
