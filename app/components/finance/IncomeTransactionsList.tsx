"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { format } from 'date-fns'
import { formatCurrency } from '@/lib/utils/currency'

interface Transaction {
  description: string;
  amount: number;
  date: string;
  category?: string;
}

interface IncomeTransactionsListProps {
  transactions: Transaction[];
  className?: string;
}

// Activity item component
function ActivityItem({ transaction }: { transaction: Transaction }) {
  // Format order description to be more human-readable
  const formatDescription = (description: string, category?: string) => {
    // Only process order descriptions
    if (category === 'Order' && description.includes('Order')) {
      // Extract order number from the description
      const orderMatch = description.match(/Order ([\w-]+)/);
      const orderNumber = orderMatch ? orderMatch[1] : '';

      // Check if it's a table order or takeaway
      if (description.toLowerCase().includes('table')) {
        const tableMatch = description.match(/Table ([\w-]+)/);
        const tableNumber = tableMatch ? tableMatch[1] : '';
        return `#${orderNumber} - Table ${tableNumber}`;
      } else {
        return `#${orderNumber} - Takeaway`;
      }
    }
    return description;
  };

  return (
    <div className="flex items-center justify-between py-1.5 px-2 border-b last:border-0 hover:bg-muted/30 transition-colors">
      <div>
        <p className="font-medium text-xs">{formatDescription(transaction.description, transaction.category)}</p>
        <div className="flex items-center gap-1.5">
          <span className="text-[10px] text-muted-foreground">
            {format(new Date(transaction.date), 'MMM d, yyyy')}
          </span>
          {transaction.category && (
            <span className="text-[10px] bg-muted px-1 py-0.5 rounded-sm">{transaction.category}</span>
          )}
        </div>
      </div>
      <div className="text-right">
        <p className={`font-medium text-xs ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
          {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
        </p>
      </div>
    </div>
  );
}

export default function IncomeTransactionsList({
  transactions,
  className
}: IncomeTransactionsListProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-1 pt-4">
        <CardTitle className="text-sm font-medium">Recent Transactions</CardTitle>
      </CardHeader>
      <CardContent className="p-0 pb-2">
        <ScrollArea className="h-[240px]">
          {transactions.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              <p className="text-sm">No transactions found</p>
            </div>
          ) : (
            <div>
              {transactions.map((tx, index) => (
                <ActivityItem
                  key={index}
                  transaction={tx}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
