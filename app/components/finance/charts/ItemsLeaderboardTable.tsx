"use client"

import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"

interface SoldItem {
  name: string;
  count: number;
}

interface ItemsLeaderboardTableProps {
  data: SoldItem[];
  className?: string;
}

export default function ItemsLeaderboardTable({
  data,
  className
}: ItemsLeaderboardTableProps) {
  // Validate and sort data
  const validData = Array.isArray(data) ? data.filter(item => {
    return item && typeof item === 'object' &&
           typeof item.name === 'string' &&
           !isNaN(Number(item.count));
  }) : [];

  // Sort data by count (descending)
  const sortedData = [...validData].sort((a, b) => b.count - a.count);

  // Calculate total items sold
  const totalSold = sortedData.reduce((sum, item) => sum + item.count, 0);

  return (
    <Card className={className}>
      <CardHeader className="pb-1 pt-4">
        <CardTitle className="text-sm font-medium">Items Leaderboard</CardTitle>
      </CardHeader>
      <CardContent className="p-0 pb-2">
        <ScrollArea className="h-[240px]">
          {sortedData.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              <p className="text-sm">No data available</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="w-[50px] h-8 text-xs">Rank</TableHead>
                  <TableHead className="text-xs">Item</TableHead>
                  <TableHead className="text-right w-[60px] text-xs">Qty</TableHead>
                  <TableHead className="text-right w-[70px] text-xs">% of Sales</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedData.map((item, index) => {
                  const percentage = totalSold > 0 ? (item.count / totalSold) * 100 : 0;

                  // Determine badge color based on rank
                  let badgeVariant: "default" | "secondary" | "outline" = "outline";
                  if (index === 0) badgeVariant = "default";
                  else if (index < 3) badgeVariant = "secondary";

                  return (
                    <TableRow key={item.name} className="h-9">
                      <TableCell className="py-1">
                        <Badge variant={badgeVariant} className="w-6 h-6 rounded-full flex items-center justify-center p-0 text-xs">
                          {index + 1}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-1 text-sm">{item.name}</TableCell>
                      <TableCell className="text-right py-1 text-sm">{item.count}</TableCell>
                      <TableCell className="text-right py-1 text-sm">{percentage.toFixed(1)}%</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
