"use client"

import React from 'react'
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
  Tooltip,
  TooltipProps
} from 'recharts'

interface TopSellingItem {
  name: string;
  count: number;
}

interface TopSellingItemsChartProps {
  data: TopSellingItem[];
  className?: string;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-md shadow-sm p-2 text-sm">
        <p className="font-medium">{label}</p>
        <p className="text-muted-foreground">
          <span className="font-medium">{payload[0].value}</span> items sold
        </p>
      </div>
    );
  }
  return null;
};

export default function TopSellingItemsChart({
  data,
  className
}: TopSellingItemsChartProps) {
  // Validate and sort data
  const validData = Array.isArray(data) ? data.filter(item => {
    return item && typeof item === 'object' &&
           typeof item.name === 'string' &&
           !isNaN(Number(item.count));
  }) : [];

  // Sort data by count (descending)
  const sortedData = [...validData].sort((a, b) => b.count - a.count);

  // Take only top 5 items
  const chartData = sortedData.slice(0, 5);

  // Chart colors using CSS variables for theme consistency
  const chartColors = [
    'var(--chart-1)',
    'var(--chart-2)',
    'var(--chart-3)',
    'var(--chart-4)',
    'var(--chart-5)'
  ];

  return (
    <Card className={className}>
      <CardHeader className="pb-1 pt-4">
        <CardTitle className="text-sm font-medium">Top Items Sold</CardTitle>
      </CardHeader>
      <CardContent className="p-0 pb-4">
        <div className="h-[180px] w-full">
          {chartData.length === 0 ? (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <p className="text-sm">No data available</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                layout="vertical"
                margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                accessibilityLayer
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={false} opacity={0.2} />
                <XAxis
                  type="number"
                  tickLine={false}
                  axisLine={false}
                  fontSize={10}
                  tickFormatter={(value) => value.toLocaleString()}
                />
                <YAxis
                  dataKey="name"
                  type="category"
                  width={90}
                  tickLine={false}
                  axisLine={false}
                  fontSize={11}
                  tickFormatter={(value) => value.length > 12 ? `${value.substring(0, 12)}...` : value}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar
                  dataKey="count"
                  name="Quantity Sold"
                  radius={[0, 4, 4, 0]}
                  barSize={16}
                >
                  {chartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={chartColors[index % chartColors.length]}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
