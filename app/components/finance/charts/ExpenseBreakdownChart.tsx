"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils/currency"
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  Legend
} from 'recharts'

interface ExpenseCategory {
  name: string
  value: number
  color: string
}

interface ExpenseBreakdownChartProps {
  data: ExpenseCategory[]
  onCategoryClick?: (category: string) => void
  className?: string
}

export default function ExpenseBreakdownChart({
  data,
  onCategoryClick,
  className
}: ExpenseBreakdownChartProps) {
  // Validate data before processing
  const validData = Array.isArray(data) ? data.filter(category => {
    return category && typeof category === 'object' &&
           typeof category.name === 'string' &&
           !isNaN(Number(category.value));
  }) : [];

  // Calculate total
  const total = validData.reduce((sum, category) => sum + (Number(category.value) || 0), 0)

  // Sort data by value (descending)
  const sortedData = [...validData].sort((a, b) => (Number(b.value) || 0) - (Number(a.value) || 0))

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && Array.isArray(payload) && payload.length) {
      try {
        const item = payload[0].payload
        if (!item || typeof item !== 'object') return null;

        const value = Number(item.value) || 0;
        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';

        return (
          <div className="bg-background border rounded-md shadow-md p-3 text-sm">
            <p className="font-medium">{item.name || 'Unknown'}</p>
            <p>{formatCurrency(value)}</p>
            <p className="text-muted-foreground">{percentage}% of total</p>
          </div>
        )
      } catch (error) {
        console.error('Error rendering tooltip:', error);
        return (
          <div className="bg-background border rounded-md shadow-md p-3 text-sm">
            <p>Error displaying data</p>
          </div>
        );
      }
    }
    return null
  }

  // Custom legend
  const CustomLegend = ({ payload }: any) => {
    if (!payload || !Array.isArray(payload)) return null

    return (
      <div className="grid grid-cols-1 gap-1 mt-2 text-xs">
        {payload.map((entry: any, index: number) => {
          try {
            if (!entry || typeof entry !== 'object' || !entry.value) return null;

            const item = validData.find(d => d.name === entry.value)
            if (!item) return null

            const value = Number(item.value) || 0;
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';

            return (
              <div
                key={`legend-${index}`}
                className="flex justify-between items-center p-1 rounded hover:bg-accent/50 cursor-pointer"
                onClick={() => onCategoryClick?.(item.name)}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <span>{item.name}</span>
                </div>
                <div className="flex gap-2">
                  <span>{formatCurrency(value)}</span>
                  <span className="text-muted-foreground">({percentage}%)</span>
                </div>
              </div>
            )
          } catch (error) {
            console.error('Error rendering legend item:', error);
            return null;
          }
        })}
      </div>
    )
  }

  // Generate demo data if no data provided
  const demoData = [
    { name: 'Rent', value: 25000, color: '#f43f5e' },
    { name: 'Utilities', value: 12000, color: '#3b82f6' },
    { name: 'Salaries', value: 35000, color: '#10b981' },
    { name: 'Supplies', value: 18000, color: '#f59e0b' },
    { name: 'Marketing', value: 8000, color: '#8b5cf6' },
    { name: 'Other', value: 5000, color: '#6b7280' },
  ]

  // Use demo data if no valid data provided
  const chartData = validData.length > 0 ? sortedData : demoData

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Expense Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                onClick={(data) => onCategoryClick?.(data.name)}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={<CustomLegend />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="text-center mt-2">
          <p className="text-sm font-medium">Total Expenses</p>
          <p className="text-xl font-bold">{formatCurrency(total)}</p>
        </div>
      </CardContent>
    </Card>
  )
}
