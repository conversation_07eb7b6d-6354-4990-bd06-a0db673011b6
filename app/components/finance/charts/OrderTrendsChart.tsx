"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps
} from 'recharts'
import { format, parseISO, isValid } from 'date-fns'

interface OrderTrend {
  date: string;
  count: number;
  amount: number;
}

interface OrderTrendsChartProps {
  data: OrderTrend[];
  className?: string;
  timePeriod: string;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    const date = parseISO(label);
    return (
      <div className="bg-background border rounded-md shadow-sm p-2 text-sm">
        <p className="font-medium">{isValid(date) ? format(date, 'PPP') : label}</p>
        <p className="text-muted-foreground">
          <span className="font-medium text-blue-500">{payload[0].value}</span> orders
        </p>
        {payload[1] && (
          <p className="text-muted-foreground">
            <span className="font-medium text-green-500">
              {new Intl.NumberFormat('fr-DZ', { style: 'currency', currency: 'DZD' }).format(payload[1].value as number)}
            </span>
          </p>
        )}
      </div>
    );
  }
  return null;
};

export default function OrderTrendsChart({
  data,
  className,
  timePeriod
}: OrderTrendsChartProps) {
  // Format X-axis ticks based on time period
  const formatXAxis = (dateStr: string) => {
    try {
      const date = parseISO(dateStr);
      if (!isValid(date)) return dateStr;

      switch(timePeriod) {
        case 'week':
          return format(date, 'EEE'); // Mon, Tue, etc.
        case 'month':
          return format(date, 'd'); // 1, 2, 3, etc.
        case 'quarter':
        case 'year':
          return format(date, 'MMM'); // Jan, Feb, etc.
        default:
          return format(date, 'MMM d'); // Jan 1, Feb 2, etc.
      }
    } catch (error) {
      return dateStr;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-1 pt-4">
        <CardTitle className="text-sm font-medium">Order Trends</CardTitle>
      </CardHeader>
      <CardContent className="p-0 pb-4">
        <div className="h-[180px] w-full">
          {data.length === 0 ? (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <p className="text-sm">No data available</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={data}
                margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.2} />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatXAxis}
                  tickLine={false}
                  axisLine={false}
                  minTickGap={15}
                  fontSize={10}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  width={25}
                  fontSize={10}
                  tickFormatter={(value) => value.toLocaleString()}
                />
                <Tooltip content={<CustomTooltip />} />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke="var(--chart-1)"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, strokeWidth: 0 }}
                />
                <Line
                  type="monotone"
                  dataKey="amount"
                  stroke="var(--chart-2)"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, strokeWidth: 0 }}
                  hide={true} // Hidden by default, can be toggled
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
