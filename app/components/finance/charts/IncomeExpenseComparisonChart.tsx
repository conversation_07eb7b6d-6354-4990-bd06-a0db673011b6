"use client"

import React from 'react'
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils/currency"
import { format, subDays } from 'date-fns'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  Line,
  ComposedChart
} from 'recharts'

interface DailyData {
  date: string
  income: number
  expense: number
  profit?: number
}

interface IncomeExpenseComparisonChartProps {
  data: DailyData[]
  className?: string
}

export default function IncomeExpenseComparisonChart({
  data,
  className
}: IncomeExpenseComparisonChartProps) {
  // Validate data before processing
  const validData = Array.isArray(data) ? data.filter(day => {
    return day && typeof day === 'object' &&
           typeof day.date === 'string' &&
           !isNaN(Number(day.income)) &&
           !isNaN(Number(day.expense));
  }) : [];

  // Add profit/loss calculation
  const dataWithProfit = validData.map(day => ({
    ...day,
    profit: (Number(day.income) || 0) - (Number(day.expense) || 0)
  }))

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && Array.isArray(payload) && payload.length) {
      try {
        const income = payload.find((p: any) => p.dataKey === 'income')?.value || 0
        const expense = payload.find((p: any) => p.dataKey === 'expense')?.value || 0
        const profit = income - expense

        // Safely format the date
        let formattedDate = '';
        try {
          formattedDate = format(new Date(label), 'dd MMM yyyy');
        } catch (e) {
          console.error('Error formatting date in tooltip:', e);
          formattedDate = label || 'Unknown date';
        }

        return (
          <div className="bg-background border rounded-md shadow-md p-3 text-sm">
            <p className="font-medium">{formattedDate}</p>
            <p className="text-green-600">Income: {formatCurrency(income)}</p>
            <p className="text-red-600">Expense: {formatCurrency(expense)}</p>
            <div className="h-px bg-muted my-1" />
            <p className={profit >= 0 ? "text-green-600" : "text-red-600"}>
              {profit >= 0 ? 'Profit: ' : 'Loss: '}{formatCurrency(Math.abs(profit))}
            </p>
          </div>
        )
      } catch (error) {
        console.error('Error rendering tooltip:', error);
        return (
          <div className="bg-background border rounded-md shadow-md p-3 text-sm">
            <p>Error displaying data</p>
          </div>
        );
      }
    }
    return null
  }

  // Generate demo data if no data provided
  const generateDemoData = () => {
    const result: DailyData[] = []
    const today = new Date()

    for (let i = 6; i >= 0; i--) {
      const date = subDays(today, i)
      const income = 5000 + Math.random() * 10000
      const expense = 3000 + Math.random() * 8000

      result.push({
        date: format(date, 'yyyy-MM-dd'),
        income,
        expense,
        profit: income - expense
      })
    }

    return result
  }

  // Use demo data if no valid data provided
  const chartData = dataWithProfit.length > 0 ? dataWithProfit : generateDemoData()

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Income vs. Expense (7-Day View)</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart
              data={chartData}
              margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="date"
                tickFormatter={(value) => {
                  try {
                    return format(new Date(value), 'dd MMM');
                  } catch (e) {
                    console.error('Error formatting date in XAxis:', e);
                    return value || '';
                  }
                }}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                tickFormatter={(value) => formatCurrency(value, true)}
                width={60}
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <ReferenceLine y={0} stroke="#666" />
              <Bar dataKey="income" name="Income" fill="#10b981" radius={[4, 4, 0, 0]} />
              <Bar dataKey="expense" name="Expense" fill="#f43f5e" radius={[4, 4, 0, 0]} />
              <Line
                type="monotone"
                dataKey="profit"
                name="Profit/Loss"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ r: 4 }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
        <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
            <span>Income</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
            <span>Expense</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Profit/Loss</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
