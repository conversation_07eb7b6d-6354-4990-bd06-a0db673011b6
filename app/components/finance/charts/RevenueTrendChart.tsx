"use client"

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { formatCurrency } from "@/lib/utils/currency"
import { format, subDays } from 'date-fns'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts'

interface DataPoint {
  date: string
  revenue: number
  movingAverage?: number
}

interface RevenueTrendChartProps {
  data: DataPoint[]
  className?: string
}

export default function RevenueTrendChart({ data, className }: RevenueTrendChartProps) {
  const [view, setView] = useState<'daily' | 'weekly'>('daily')

  // Validate data before processing
  const validData = Array.isArray(data) ? data.filter(point => {
    return point && typeof point === 'object' &&
           typeof point.date === 'string' &&
           !isNaN(Number(point.revenue));
  }) : [];

  // Calculate 7-day moving average
  const dataWithMovingAverage = validData.map((point, index, array) => {
    if (index < 6) return point

    try {
      const last7Days = array.slice(index - 6, index + 1)
      const sum = last7Days.reduce((acc, curr) => acc + (Number(curr.revenue) || 0), 0)
      const movingAverage = sum / 7

      return {
        ...point,
        movingAverage
      }
    } catch (error) {
      console.error('Error calculating moving average:', error);
      return point;
    }
  })

  // Calculate average revenue
  const averageRevenue = validData.length > 0
    ? validData.reduce((sum, point) => sum + (Number(point.revenue) || 0), 0) / validData.length
    : 0

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && Array.isArray(payload) && payload.length) {
      try {
        // Safely format the date if needed
        let formattedLabel = label;
        if (typeof label === 'string' && label.includes('-')) {
          try {
            formattedLabel = format(new Date(label), 'dd MMM yyyy');
          } catch (e) {
            console.error('Error formatting date in tooltip:', e);
          }
        }

        return (
          <div className="bg-background border rounded-md shadow-md p-3 text-sm">
            <p className="font-medium">{formattedLabel}</p>
            <p className="text-green-600">
              Revenue: {formatCurrency(payload[0]?.value || 0)}
            </p>
            {payload[1] && (
              <p className="text-blue-600">
                7-day Avg: {formatCurrency(payload[1]?.value || 0)}
              </p>
            )}
          </div>
        )
      } catch (error) {
        console.error('Error rendering tooltip:', error);
        return (
          <div className="bg-background border rounded-md shadow-md p-3 text-sm">
            <p>Error displaying data</p>
          </div>
        );
      }
    }
    return null
  }

  // Generate demo data if no data provided
  const demoData = () => {
    const result: DataPoint[] = []
    const today = new Date()

    for (let i = 29; i >= 0; i--) {
      const date = subDays(today, i)
      const revenue = 5000 + Math.random() * 10000
      result.push({
        date: format(date, 'yyyy-MM-dd'),
        revenue
      })
    }

    return result.map((point, index, array) => {
      if (index < 6) return point

      const last7Days = array.slice(index - 6, index + 1)
      const sum = last7Days.reduce((acc, curr) => acc + curr.revenue, 0)
      const movingAverage = sum / 7

      return {
        ...point,
        movingAverage
      }
    })
  }

  // Use demo data if no valid data provided
  const chartData = validData.length > 0 ? dataWithMovingAverage : demoData()

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Revenue Trend</CardTitle>
          <Tabs value={view} onValueChange={(v) => setView(v as 'daily' | 'weekly')}>
            <TabsList className="h-8">
              <TabsTrigger value="daily" className="text-xs px-3">Daily</TabsTrigger>
              <TabsTrigger value="weekly" className="text-xs px-3">Weekly</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="date"
                tickFormatter={(value) => {
                  try {
                    return format(new Date(value), 'dd MMM');
                  } catch (e) {
                    console.error('Error formatting date in XAxis:', e);
                    return value || '';
                  }
                }}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                tickFormatter={(value) => formatCurrency(value, true)}
                width={60}
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <ReferenceLine y={averageRevenue} stroke="#888" strokeDasharray="3 3" />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#10b981"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
              <Line
                type="monotone"
                dataKey="movingAverage"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={false}
                strokeDasharray="5 5"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Daily Revenue</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>7-day Moving Average</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-gray-400 rounded-full"></div>
            <span>Average: {formatCurrency(averageRevenue)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
