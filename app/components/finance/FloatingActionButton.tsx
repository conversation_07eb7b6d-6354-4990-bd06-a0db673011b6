"use client"

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { PlusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FloatingActionButtonProps {
  onClick: () => void;
  className?: string;
}

export default function FloatingActionButton({
  onClick,
  className
}: FloatingActionButtonProps) {
  return (
    <Button
      onClick={onClick}
      className={cn(
        "fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg",
        className
      )}
      size="icon"
    >
      <PlusIcon className="h-6 w-6" />
    </Button>
  );
}
