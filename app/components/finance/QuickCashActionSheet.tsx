"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowUpIcon, ArrowDownIcon } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';

// Cash transaction categories
const CASH_IN_CATEGORIES = [
  { value: 'owner_deposit', label: '<PERSON><PERSON><PERSON><PERSON><PERSON> Propriétaire' },
  { value: 'loan', label: 'Prêt' },
  { value: 'refund', label: 'Remboursement' },
  { value: 'other', label: 'Autre' },
];

const CASH_OUT_CATEGORIES = [
  { value: 'owner_withdrawal', label: 'Retrait Propriétaire' },
  { value: 'tip', label: 'Pourboires' },
  { value: 'petty_cash', label: 'Petite Caisse' },
  { value: 'change', label: 'Monnaie' },
  { value: 'other', label: 'Autre' },
];

export interface CashTransactionData {
  type: 'manual_in' | 'manual_out';
  amount: number;
  description: string;
  category?: string;
}

interface QuickCashActionSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: 'in' | 'out';
  onSubmit: (data: CashTransactionData) => Promise<void>;
}

export default function QuickCashActionSheet({
  open,
  onOpenChange,
  type,
  onSubmit
}: QuickCashActionSheetProps) {
  // Form state
  const [amount, setAmount] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const [description, setDescription] = useState<string>('');

  // Loading state
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Reset form
  const resetForm = () => {
    setAmount('');
    setCategory('');
    setDescription('');
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      // Show validation error
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        type: type === 'in' ? 'manual_in' : 'manual_out',
        amount: parseFloat(amount),
        description: description || `${type === 'in' ? 'Dépôt Caisse' : 'Retrait Caisse'}${category ? ` - ${getCategoryLabel(category)}` : ''}`,
        category
      });

      // Reset form and close dialog on success
      resetForm();
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting cash transaction:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get category label from value
  const getCategoryLabel = (value: string): string => {
    const categories = type === 'in' ? CASH_IN_CATEGORIES : CASH_OUT_CATEGORIES;
    const category = categories.find(cat => cat.value === value);
    return category ? category.label : value;
  };

  // Format amount in DZD
  const formattedAmount = () => {
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount) || parsedAmount <= 0) return '';
    return formatCurrency(parsedAmount);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {type === 'in' ? (
              <span className="flex items-center">
                <ArrowUpIcon className="h-5 w-5 mr-2 text-green-600" />
                Entrée de Caisse
              </span>
            ) : (
              <span className="flex items-center">
                <ArrowDownIcon className="h-5 w-5 mr-2 text-red-600" />
                Sortie de Caisse
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            {type === 'in'
              ? "Enregistrez de l'argent entrant dans la caisse"
              : "Enregistrez de l'argent sortant de la caisse"}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="amount">Montant (DA)</Label>
            <div className="relative">
              {type === 'in' ? (
                <ArrowUpIcon className="absolute left-3 top-2.5 h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownIcon className="absolute left-3 top-2.5 h-4 w-4 text-red-500" />
              )}
              <Input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="e.g., 5000"
                className="pl-9"
              />
            </div>
            {amount && parseFloat(amount) > 0 && (
              <p className="text-xs text-muted-foreground mt-1">
                {formattedAmount()}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Catégorie</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner une catégorie" />
              </SelectTrigger>
              <SelectContent>
                {(type === 'in' ? CASH_IN_CATEGORIES : CASH_OUT_CATEGORIES).map((cat) => (
                  <SelectItem key={cat.value} value={cat.value}>
                    {cat.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (optionnel)</Label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description de la transaction"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Annuler
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !amount || parseFloat(amount) <= 0}
            variant={type === 'in' ? "default" : "destructive"}
          >
            {type === 'in' ? (
              <ArrowUpIcon className="h-4 w-4 mr-2" />
            ) : (
              <ArrowDownIcon className="h-4 w-4 mr-2" />
            )}
            {isSubmitting ? 'Enregistrement...' : 'Enregistrer'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
