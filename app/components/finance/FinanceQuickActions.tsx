"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { PlusIcon, TrendingDownIcon, TrendingUpIcon, BanknoteIcon, BarChartIcon } from 'lucide-react'
import ExpenseForm, { ExpenseFormData } from '@/app/components/finance/ExpenseForm'

interface FinanceQuickActionsProps {
  onAddExpense?: (data: ExpenseFormData) => Promise<void>
  onRecordIncome?: () => void
  onCashInOut?: () => void
  onViewReports?: () => void
  className?: string
}

export default function FinanceQuickActions({
  onAddExpense,
  onRecordIncome,
  onCashInOut,
  onViewReports,
  className
}: FinanceQuickActionsProps) {
  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false)
  const [isIncomeDialogOpen, setIsIncomeDialogOpen] = useState(false)
  const [isCashDialogOpen, setIsCashDialogOpen] = useState(false)
  
  const handleAddExpense = async (data: ExpenseFormData) => {
    if (onAddExpense) {
      await onAddExpense(data)
    }
    setIsExpenseDialogOpen(false)
  }
  
  return (
    <>
      {/* Mobile: Floating Action Button with dropdown */}
      <div className={`fixed bottom-6 right-6 md:hidden z-10 ${className}`}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" className="h-14 w-14 rounded-full shadow-lg">
              <PlusIcon className="h-6 w-6" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsExpenseDialogOpen(true)}>
              <TrendingDownIcon className="mr-2 h-4 w-4 text-red-600" />
              Add Expense
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onRecordIncome ? onRecordIncome() : setIsIncomeDialogOpen(true)}>
              <TrendingUpIcon className="mr-2 h-4 w-4 text-green-600" />
              Record Income
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onCashInOut ? onCashInOut() : setIsCashDialogOpen(true)}>
              <BanknoteIcon className="mr-2 h-4 w-4 text-amber-600" />
              Cash In/Out
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onViewReports?.()}>
              <BarChartIcon className="mr-2 h-4 w-4 text-blue-600" />
              View Reports
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Desktop: Button group */}
      <div className={`hidden md:flex space-x-2 ${className}`}>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => setIsExpenseDialogOpen(true)}
          className="flex items-center"
        >
          <TrendingDownIcon className="mr-2 h-4 w-4 text-red-600" />
          Add Expense
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onRecordIncome ? onRecordIncome() : setIsIncomeDialogOpen(true)}
          className="flex items-center"
        >
          <TrendingUpIcon className="mr-2 h-4 w-4 text-green-600" />
          Record Income
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onCashInOut ? onCashInOut() : setIsCashDialogOpen(true)}
          className="flex items-center"
        >
          <BanknoteIcon className="mr-2 h-4 w-4 text-amber-600" />
          Cash In/Out
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onViewReports?.()}
          className="flex items-center"
        >
          <BarChartIcon className="mr-2 h-4 w-4 text-blue-600" />
          View Reports
        </Button>
      </div>
      
      {/* Dialogs */}
      <Dialog open={isExpenseDialogOpen} onOpenChange={setIsExpenseDialogOpen}>
        <DialogContent className="sm:max-w-md p-0 max-h-[90vh] overflow-hidden">
          <ExpenseForm
            onSubmit={handleAddExpense}
            onCancel={() => setIsExpenseDialogOpen(false)}
            title="Add Expense"
            description="Enter the details of your expense"
          />
        </DialogContent>
      </Dialog>
    </>
  )
}
