"use client"

import React, { useState, useMemo, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { formatCurrency } from '@/lib/utils/currency'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BanknoteIcon, 
  CalculatorIcon, 
  PlusIcon, 
  MinusIcon,
  AlertCircleIcon,
  CheckCircleIcon
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Denomination values in DZD
const DENOMINATIONS = [
  { value: 2000, label: '2000 DA', color: 'bg-blue-100' },
  { value: 1000, label: '1000 DA', color: 'bg-green-100' },
  { value: 500, label: '500 DA', color: 'bg-amber-100' },
  { value: 200, label: '200 DA', color: 'bg-purple-100' },
  { value: 100, label: '100 DA', color: 'bg-red-100' },
  { value: 50, label: '50 DA', color: 'bg-teal-100' },
  { value: 20, label: '20 DA', color: 'bg-orange-100' },
  { value: 10, label: '10 DA', color: 'bg-gray-100' },
  { value: 5, label: '5 DA', color: 'bg-pink-100' },
  { value: 2, label: '2 DA', color: 'bg-indigo-100' },
  { value: 1, label: '1 DA', color: 'bg-cyan-100' },
]

// Coin denominations
const COINS = [
  { value: 100, label: '100 DA', color: 'bg-red-100' },
  { value: 50, label: '50 DA', color: 'bg-teal-100' },
  { value: 20, label: '20 DA', color: 'bg-orange-100' },
  { value: 10, label: '10 DA', color: 'bg-gray-100' },
  { value: 5, label: '5 DA', color: 'bg-pink-100' },
  { value: 2, label: '2 DA', color: 'bg-indigo-100' },
  { value: 1, label: '1 DA', color: 'bg-cyan-100' },
]

// Note denominations
const NOTES = [
  { value: 2000, label: '2000 DA', color: 'bg-blue-100' },
  { value: 1000, label: '1000 DA', color: 'bg-green-100' },
  { value: 500, label: '500 DA', color: 'bg-amber-100' },
  { value: 200, label: '200 DA', color: 'bg-purple-100' },
]

// Quick preset amounts
const QUICK_PRESETS = [
  5000,
  10000,
  20000,
  50000,
  100000
]

interface CashCountingFormProps {
  onSubmit: (amount: number, counts: Record<number, number>) => void
  onCancel: () => void
  title?: string
  initialAmount?: number
  description?: string
  expectedAmount?: number
}

export default function CashCountingFormV2({
  onSubmit,
  onCancel,
  title = "Cash Count",
  initialAmount = 0,
  description,
  expectedAmount
}: CashCountingFormProps) {
  // State to track denomination counts
  const [denomCounts, setDenomCounts] = useState<Record<number, number>>(
    DENOMINATIONS.reduce((acc, denom) => ({ ...acc, [denom.value]: 0 }), {})
  )

  // State for manual amount entry
  const [manualAmount, setManualAmount] = useState(initialAmount.toString())
  
  // State for active tab
  const [activeTab, setActiveTab] = useState<'quick' | 'notes' | 'coins'>('quick')

  // Calculate total amount from denominations
  const totalAmount = useMemo(() => {
    return Object.entries(denomCounts).reduce(
      (total, [denom, count]) => total + (parseInt(denom) * (count || 0)),
      0
    )
  }, [denomCounts])
  
  // Calculate discrepancy if expected amount is provided
  const discrepancy = expectedAmount !== undefined ? totalAmount - expectedAmount : undefined
  const discrepancyPercentage = expectedAmount && expectedAmount !== 0 
    ? Math.abs(discrepancy || 0) / expectedAmount * 100 
    : 0
  
  // Determine if discrepancy is significant (more than 2%)
  const hasSignificantDiscrepancy = discrepancyPercentage > 2

  // Set initial manual amount
  useEffect(() => {
    if (initialAmount > 0) {
      setManualAmount(initialAmount.toString())
    }
  }, [initialAmount])

  // Update denomination count
  const updateDenomCount = (denom: number, count: number) => {
    setDenomCounts(prev => ({
      ...prev,
      [denom]: Math.max(0, count)
    }))
  }
  
  // Increment/decrement denomination count
  const adjustDenomCount = (denom: number, adjustment: number) => {
    setDenomCounts(prev => ({
      ...prev,
      [denom]: Math.max(0, (prev[denom] || 0) + adjustment)
    }))
  }

  // Apply quick preset
  const applyPreset = (amount: number) => {
    // Reset current counts
    const newCounts = { ...denomCounts }
    Object.keys(newCounts).forEach(key => newCounts[parseInt(key)] = 0)

    // Distribute preset amount across denominations
    let remainingAmount = amount
    DENOMINATIONS.forEach(denom => {
      const maxCount = Math.floor(remainingAmount / denom.value)
      newCounts[denom.value] = maxCount
      remainingAmount -= maxCount * denom.value
    })

    setDenomCounts(newCounts)
    setManualAmount(amount.toString())
  }

  // Handle form submission
  const handleSubmit = (useManualAmount: boolean = false) => {
    const finalAmount = useManualAmount ? parseFloat(manualAmount) || 0 : totalAmount
    onSubmit(finalAmount, denomCounts)
  }
  
  // Render denomination counter
  const renderDenominationCounter = (denom: typeof DENOMINATIONS[0]) => {
    const count = denomCounts[denom.value] || 0
    const subtotal = count * denom.value
    
    return (
      <div className={cn("flex flex-col border rounded-md overflow-hidden", count > 0 ? denom.color : "bg-background")}>
        <div className="flex justify-between items-center p-2 border-b">
          <span className="font-medium">{denom.label}</span>
          <span className="text-xs text-muted-foreground">
            {count > 0 && `${formatCurrency(subtotal)}`}
          </span>
        </div>
        <div className="flex items-center justify-between p-2">
          <Button 
            type="button"
            variant="ghost" 
            size="icon"
            className="h-7 w-7"
            onClick={() => adjustDenomCount(denom.value, -1)}
            disabled={count === 0}
          >
            <MinusIcon className="h-4 w-4" />
          </Button>
          
          <Input
            type="number"
            min="0"
            className="h-8 w-14 text-center mx-1"
            value={count || ''}
            onChange={(e) => updateDenomCount(denom.value, parseInt(e.target.value) || 0)}
          />
          
          <Button 
            type="button"
            variant="ghost" 
            size="icon"
            className="h-7 w-7"
            onClick={() => adjustDenomCount(denom.value, 1)}
          >
            <PlusIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {description && (
        <p className="text-sm text-muted-foreground mb-2">{description}</p>
      )}

      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'quick' | 'notes' | 'coins')} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quick">
            <CalculatorIcon className="h-4 w-4 mr-2" />
            Quick Entry
          </TabsTrigger>
          <TabsTrigger value="notes">
            <BanknoteIcon className="h-4 w-4 mr-2" />
            Notes
          </TabsTrigger>
          <TabsTrigger value="coins">
            <span className="mr-2">🪙</span>
            Coins
          </TabsTrigger>
        </TabsList>

        {/* Quick Entry Tab */}
        <TabsContent value="quick" className="space-y-4 pt-2">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="manual-amount">Enter Amount</Label>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Input
                    id="manual-amount"
                    type="number"
                    min="0"
                    placeholder="0"
                    className="pl-8"
                    value={manualAmount}
                    onChange={(e) => setManualAmount(e.target.value)}
                  />
                  <span className="absolute left-3 top-2.5 text-muted-foreground">DA</span>
                </div>
                <Button
                  onClick={() => handleSubmit(true)}
                  disabled={!parseFloat(manualAmount)}
                >
                  Confirm
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Quick Amounts</Label>
              <div className="grid grid-cols-3 gap-2">
                {QUICK_PRESETS.map(preset => (
                  <Button
                    key={preset}
                    size="sm"
                    variant="outline"
                    onClick={() => applyPreset(preset)}
                    className="text-sm"
                  >
                    {formatCurrency(preset)}
                  </Button>
                ))}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleSubmit(true)}
                  className="col-span-3 mt-1"
                >
                  Confirm Amount
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Notes Tab */}
        <TabsContent value="notes" className="space-y-4 pt-2">
          <div className="grid grid-cols-2 gap-2">
            {NOTES.map(denom => renderDenominationCounter(denom))}
          </div>
        </TabsContent>
        
        {/* Coins Tab */}
        <TabsContent value="coins" className="space-y-4 pt-2">
          <div className="grid grid-cols-3 gap-2">
            {COINS.map(denom => renderDenominationCounter(denom))}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Total Amount */}
      <div className="flex justify-between items-center p-3 bg-muted rounded-md">
        <span className="font-medium">Total Count</span>
        <span className="text-lg font-bold">
          {formatCurrency(totalAmount)}
        </span>
      </div>
      
      {/* Discrepancy Alert */}
      {discrepancy !== undefined && (
        <div className={cn(
          "flex items-center justify-between p-3 rounded-md",
          hasSignificantDiscrepancy ? "bg-red-100" : "bg-green-100"
        )}>
          <div className="flex items-center">
            {hasSignificantDiscrepancy ? (
              <AlertCircleIcon className="h-5 w-5 text-red-600 mr-2" />
            ) : (
              <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
            )}
            <span className="font-medium">
              {hasSignificantDiscrepancy ? "Discrepancy" : "Reconciled"}
            </span>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className={cn(
                  "font-bold",
                  discrepancy && discrepancy > 0 ? "text-green-600" : "text-red-600"
                )}>
                  {discrepancy && discrepancy > 0 ? "+" : ""}{formatCurrency(discrepancy || 0)}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {discrepancy && discrepancy > 0 
                    ? `${discrepancyPercentage.toFixed(1)}% more than expected` 
                    : `${discrepancyPercentage.toFixed(1)}% less than expected`}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
        <Button 
          onClick={() => handleSubmit(false)} 
          disabled={totalAmount === 0}
          className="flex-1"
        >
          Confirm Count
        </Button>
      </div>
    </div>
  )
}
