"use client"

import React from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  BanknoteIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  RefreshCwIcon,
  CalculatorIcon,
  Loader2,
  CalendarDaysIcon
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface FinanceHeaderV2Props {
  title?: string;
  subtitle?: string;
  currentBalance: number;
  income: number;
  expenditure: number;
  isShiftOpen: boolean;
  shiftOpenedAt?: string;
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  onOpenShift: () => void;
  onCashIn?: () => void;
  onCashOut?: () => void;
  onCloseShift?: () => void;
  onRefresh: () => void;
  isRefreshing?: boolean;
}

export default function FinanceHeaderV2({
  title = "Caisse",
  subtitle = "Système de gestion de caisse",
  currentBalance = 0,
  income = 0,
  expenditure = 0,
  isShiftOpen = false,
  shiftOpenedAt,
  dateRange,
  onDateRangeChange,
  onOpenShift,
  onCashIn,
  onCashOut,
  onCloseShift,
  onRefresh,
  isRefreshing = false
}: FinanceHeaderV2Props) {
  // Predefined date ranges
  const handlePresetClick = (preset: 'today' | 'week' | 'month') => {
    const today = new Date();

    switch (preset) {
      case 'today':
        onDateRangeChange({
          from: today,
          to: today
        });
        break;
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6); // End of week (Saturday)
        onDateRangeChange({
          from: weekStart,
          to: weekEnd
        });
        break;
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        onDateRangeChange({
          from: monthStart,
          to: monthEnd
        });
        break;
    }
  };

  // Format date range for display
  const formatDateRange = () => {
    if (!dateRange?.from) return "";
    
    const fromDate = format(dateRange.from, 'dd/MM/yyyy');
    if (!dateRange.to) return fromDate;
    
    if (dateRange.from.toDateString() === dateRange.to.toDateString()) {
      return fromDate; // Same day, just show one date
    }
    
    const toDate = format(dateRange.to, 'dd/MM/yyyy');
    return `${fromDate} → ${toDate}`;
  };

  return (
    <div className="space-y-4">
      {/* Header with title and date range */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          <p className="text-muted-foreground">{subtitle}</p>
        </div>
        <TooltipProvider>
          <div className="flex flex-wrap items-center gap-2">
            <div className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold bg-background shadow-sm">
              <CalendarDaysIcon className="w-3.5 h-3.5 mr-1" />
              {formatDateRange()}
            </div>
            <DateRangePicker
              value={dateRange}
              onChange={onDateRangeChange}
              locale={fr}
              className="h-8"
            />
            <div className="flex gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePresetClick('today')}
                    className={cn(
                      "h-8 px-2",
                      dateRange?.from && dateRange?.to &&
                      dateRange.from.toDateString() === new Date().toDateString() &&
                      dateRange.to.toDateString() === new Date().toDateString()
                        ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
                        : ""
                    )}
                  >
                    Auj.
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Aujourd'hui</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePresetClick('week')}
                    className="h-8 px-2"
                  >
                    Sem.
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Cette semaine</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePresetClick('month')}
                    className="h-8 px-2"
                  >
                    Mois
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Ce mois</TooltipContent>
              </Tooltip>
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  className="h-8 px-2"
                  disabled={isRefreshing}
                >
                  {isRefreshing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCwIcon className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>Actualiser</TooltipContent>
            </Tooltip>
          </div>
        </TooltipProvider>
      </div>

      {/* Cash Register Card */}
      <Card className="border border-border shadow-sm bg-card">
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col gap-6">
            {/* Cash Register Balance */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-full">
                  <BanknoteIcon className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Solde actuel</h3>
                  <h2 className="text-3xl font-bold">{formatCurrency(currentBalance)}</h2>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={onOpenShift}
                        size="sm"
                        className="w-full md:w-auto"
                      >
                        <CalculatorIcon className="mr-2 h-4 w-4" />
                        Comptage de caisse
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Comptez la caisse</TooltipContent>
                  </Tooltip>

                  {onCashIn && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={onCashIn}
                          className="w-full md:w-auto"
                        >
                          <ArrowUpIcon className="mr-2 h-4 w-4 text-green-600" />
                          Entrée
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Ajouter de l'argent</TooltipContent>
                    </Tooltip>
                  )}

                  {onCashOut && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline" 
                          size="sm"
                          onClick={onCashOut}
                          className="w-full md:w-auto"
                        >
                          <ArrowDownIcon className="mr-2 h-4 w-4 text-red-600" />
                          Sortie
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Retirer de l'argent</TooltipContent>
                    </Tooltip>
                  )}
                </TooltipProvider>
              </div>
            </div>
            
            {/* Cash Flow Overview */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-muted/30 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 bg-green-100 rounded-full">
                    <ArrowUpIcon className="h-4 w-4 text-green-600" />
                  </div>
                  <span className="text-sm font-medium">Entrées</span>
                </div>
                <p className="text-xl font-semibold mt-2">{formatCurrency(income)}</p>
              </div>
              
              <div className="bg-muted/30 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 bg-red-100 rounded-full">
                    <ArrowDownIcon className="h-4 w-4 text-red-600" />
                  </div>
                  <span className="text-sm font-medium">Sorties</span>
                </div>
                <p className="text-xl font-semibold mt-2">{formatCurrency(expenditure)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
