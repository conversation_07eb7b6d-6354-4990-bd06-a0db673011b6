"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatCurrency } from "@/lib/utils/currency"
import { TrendingUpIcon, TrendingDownIcon, InfoIcon } from 'lucide-react'

interface MetricCardProps {
  title: string
  value: number
  icon: React.ReactNode
  trend?: 'up' | 'down' | 'neutral'
  previousValue?: number
  percentChange?: number
  tooltip?: string
  className?: string
}

export default function MetricCard({
  title,
  value,
  icon,
  trend = 'neutral',
  previousValue,
  percentChange,
  tooltip,
  className
}: MetricCardProps) {
  // Determine trend based on percent change if not explicitly provided
  const displayTrend = trend !== 'neutral' ? trend : 
    percentChange ? (percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'neutral') : 'neutral'
  
  // Format percent change for display
  const formattedPercentChange = percentChange !== undefined 
    ? `${percentChange > 0 ? '+' : ''}${percentChange.toFixed(1)}%` 
    : undefined

  // Get color classes based on trend
  const getTrendColor = () => {
    switch (displayTrend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return ''
    }
  }

  // Get background color for icon
  const getIconBgColor = () => {
    switch (displayTrend) {
      case 'up':
        return 'bg-green-100'
      case 'down':
        return 'bg-red-100'
      default:
        return 'bg-blue-100'
    }
  }

  // Get trend indicator
  const getTrendIndicator = () => {
    if (!formattedPercentChange) return null
    
    return (
      <div className={`flex items-center gap-1 text-sm ${getTrendColor()}`}>
        {displayTrend === 'up' ? (
          <TrendingUpIcon className="h-3.5 w-3.5" />
        ) : displayTrend === 'down' ? (
          <TrendingDownIcon className="h-3.5 w-3.5" />
        ) : null}
        <span>{formattedPercentChange}</span>
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-1">
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              {tooltip && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-sm">{tooltip}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <p className={`text-2xl font-bold mt-2 ${getTrendColor()}`}>
              {formatCurrency(value)}
            </p>
            {previousValue !== undefined && (
              <p className="text-xs text-muted-foreground mt-1">
                Previous: {formatCurrency(previousValue)}
              </p>
            )}
            {getTrendIndicator()}
          </div>
          <div className={`p-2 rounded-full ${getIconBgColor()}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
