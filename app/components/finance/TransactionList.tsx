import React from 'react';
import { 
  Card, 
  Card<PERSON>ontent, 
  Card<PERSON>eader, 
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { 
  Table, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableBody, 
  TableCell 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { format } from 'date-fns';
import { formatCurrency } from "@/lib/utils/currency";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  BanknoteIcon,
  ClockIcon,
  DollarSignIcon,
  FileTextIcon,
  CreditCardIcon,
  UserIcon
} from 'lucide-react';

export interface Transaction {
  id: string;
  type: 'opening' | 'sales' | 'expense' | 'manual_in' | 'order';
  amount: number;
  description: string;
  time: string;
  performedBy: string;
  sessionId?: string;
  relatedDocId?: string;
}

interface TransactionListProps {
  transactions: Transaction[];
  title?: string;
  description?: string;
  isLoading?: boolean;
  emptyMessage?: string;
}

export default function TransactionList({
  transactions,
  title = "Recent Transactions",
  description = "Recent financial activity",
  isLoading = false,
  emptyMessage = "No transactions found"
}: TransactionListProps) {
  // Function to get icon for transaction type
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'opening': return <BanknoteIcon className="h-4 w-4 text-blue-500" />;
      case 'sales': return <CreditCardIcon className="h-4 w-4 text-green-500" />;
      case 'expense': return <ArrowDownIcon className="h-4 w-4 text-red-500" />;
      case 'manual_in': return <ArrowUpIcon className="h-4 w-4 text-green-500" />;
      case 'order': return <DollarSignIcon className="h-4 w-4 text-green-500" />;
      default: return <FileTextIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format transaction time
  const formatTransactionTime = (isoString: string) => {
    try {
      return format(new Date(isoString), 'HH:mm');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Format transaction date 
  const formatTransactionDate = (isoString: string) => {
    try {
      return format(new Date(isoString), 'PP');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format transaction type label
  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'opening': return 'Opening Balance';
      case 'sales': return 'Sales';
      case 'expense': return 'Expense';
      case 'manual_in': return 'Cash Deposit';
      case 'order': return 'Order Payment';
      default: return type;
    }
  };

  // Get transaction badge variant based on type
  const getTransactionBadgeVariant = (type: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (type) {
      case 'expense': return 'destructive';
      case 'opening': return 'secondary';
      case 'sales':
      case 'manual_in':
      case 'order': return 'default';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full bg-gray-200"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                    <div className="h-3 w-32 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="h-5 w-16 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!transactions || transactions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center text-muted-foreground">
            <FileTextIcon className="mx-auto h-8 w-8 mb-2 opacity-50" />
            <p>{emptyMessage}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group transactions by date
  const groupedTransactions: { [key: string]: Transaction[] } = {};
  
  transactions.forEach(transaction => {
    const date = transaction.time.split('T')[0]; // Extract YYYY-MM-DD from ISO string
    if (!groupedTransactions[date]) {
      groupedTransactions[date] = [];
    }
    groupedTransactions[date].push(transaction);
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(groupedTransactions)
            .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
            .map(([date, dayTransactions]) => (
              <div key={date} className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {formatTransactionDate(date)}
                </h3>
                <div className="space-y-2">
                  {dayTransactions.map(transaction => (
                    <div 
                      key={transaction.id}
                      className="flex items-center justify-between p-3 bg-card border rounded-lg hover:bg-accent/5 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="h-9 w-9 border">
                          <AvatarFallback className="bg-primary/10 text-primary text-xs">
                            {getUserInitials(transaction.performedBy)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center gap-2">
                            <Badge variant={getTransactionBadgeVariant(transaction.type)} className="text-xs">
                              {getTransactionTypeLabel(transaction.type)}
                            </Badge>
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <ClockIcon className="h-3 w-3" />
                              {formatTransactionTime(transaction.time)}
                            </span>
                          </div>
                          <p className="text-sm mt-1">{transaction.description}</p>
                          <p className="text-xs text-muted-foreground mt-0.5">
                            <span className="font-medium">By:</span> {transaction.performedBy}
                          </p>
                        </div>
                      </div>
                      <span className={`font-mono font-medium ${
                        transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.amount >= 0 ? '+' : ''}
                        {formatCurrency(transaction.amount)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  );
} 