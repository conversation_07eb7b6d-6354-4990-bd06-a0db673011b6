"use client"

import { useState, useEffect } from "react"
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subMonths } from "date-fns"
import { Calendar as CalendarIcon, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"

export type DateRange = {
  from: Date | undefined
  to: Date | undefined
}

export type DateRangePreset = 
  | 'today'
  | 'yesterday'
  | 'this-week'
  | 'last-week'
  | 'this-month'
  | 'last-month'
  | 'custom'

interface DateRangeSelectorProps {
  onRangeChange: (range: DateRange) => void
  className?: string
}

export function DateRangeSelector({ onRangeChange, className }: DateRangeSelectorProps) {
  const [date, setDate] = useState<DateRange>({
    from: new Date(),
    to: new Date(),
  })
  const [preset, setPreset] = useState<DateRangePreset>('today')
  const [isOpen, setIsOpen] = useState(false)

  // Apply preset date range
  const applyPreset = (newPreset: DateRangePreset) => {
    const today = new Date()
    let newRange: DateRange = { from: undefined, to: undefined }

    switch (newPreset) {
      case 'today':
        newRange = { from: today, to: today }
        break
      case 'yesterday':
        const yesterday = subDays(today, 1)
        newRange = { from: yesterday, to: yesterday }
        break
      case 'this-week':
        newRange = {
          from: startOfWeek(today, { weekStartsOn: 1 }),
          to: today
        }
        break
      case 'last-week':
        const lastWeekStart = subDays(startOfWeek(today, { weekStartsOn: 1 }), 7)
        const lastWeekEnd = subDays(endOfWeek(today, { weekStartsOn: 1 }), 7)
        newRange = { from: lastWeekStart, to: lastWeekEnd }
        break
      case 'this-month':
        newRange = {
          from: startOfMonth(today),
          to: today
        }
        break
      case 'last-month':
        const lastMonth = subMonths(today, 1)
        newRange = {
          from: startOfMonth(lastMonth),
          to: endOfMonth(lastMonth)
        }
        break
      case 'custom':
        // Keep current range for custom
        newRange = date
        setIsOpen(true)
        break
    }

    setDate(newRange)
    setPreset(newPreset)
    
    if (newPreset !== 'custom') {
      setIsOpen(false)
      onRangeChange(newRange)
    }
  }

  // Format the date range for display
  const formatDateRange = () => {
    if (!date.from) return "Select date range"
    
    if (date.from && date.to) {
      if (format(date.from, 'PP') === format(date.to, 'PP')) {
        return format(date.from, 'PP')
      }
      return `${format(date.from, 'PP')} - ${format(date.to, 'PP')}`
    }
    
    return format(date.from, 'PP')
  }

  // Handle custom date selection
  const handleCustomDateChange = (newDate: DateRange) => {
    setDate(newDate)
    
    // Only trigger the change if both dates are selected
    if (newDate.from && newDate.to) {
      onRangeChange(newDate)
    }
  }

  // Initialize with today's date
  useEffect(() => {
    applyPreset('today')
  }, [])

  return (
    <div className={cn("flex flex-col sm:flex-row gap-2", className)}>
      <Select value={preset} onValueChange={(value) => applyPreset(value as DateRangePreset)}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select period" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="today">Today</SelectItem>
          <SelectItem value="yesterday">Yesterday</SelectItem>
          <SelectItem value="this-week">This Week</SelectItem>
          <SelectItem value="last-week">Last Week</SelectItem>
          <SelectItem value="this-month">This Month</SelectItem>
          <SelectItem value="last-month">Last Month</SelectItem>
          <SelectItem value="custom">Custom Range</SelectItem>
        </SelectContent>
      </Select>

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "justify-start text-left font-normal",
              !date.from && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateRange()}
            {preset === 'custom' && <ChevronDown className="ml-2 h-4 w-4 opacity-50" />}
          </Button>
        </PopoverTrigger>
        {preset === 'custom' && (
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date.from}
              selected={date}
              onSelect={handleCustomDateChange}
              numberOfMonths={2}
            />
          </PopoverContent>
        )}
      </Popover>
    </div>
  )
}
