"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Label } from "@/components/ui/label"
import { formatCurrency } from '@/lib/utils/currency'
import { format, subDays, isSameDay } from 'date-fns'
import {
  BanknoteIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  DollarSignIcon,
  CreditCardIcon,
  ReceiptIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowRightIcon,
  PrinterIcon,
  DownloadIcon
} from 'lucide-react'
import { useExpenses, useCashRegister } from '@/lib/services/finance-service'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'

interface DailySnapshotProps {
  date?: Date
  onDateChange?: (date: Date) => void
  className?: string
}

export default function DailySnapshot({
  date = new Date(),
  onDateChange,
  className
}: DailySnapshotProps) {
  const { toast } = useToast()
  const [selectedDate, setSelectedDate] = useState<Date>(date)
  const [isLoading, setIsLoading] = useState(false)

  // Create date range for the selected day
  const dateRange = {
    from: selectedDate,
    to: selectedDate
  }

  // Create date range for the previous day
  const previousDay = subDays(selectedDate, 1)
  const previousDateRange = {
    from: previousDay,
    to: previousDay
  }

  // Get expenses for the selected day
  const { expenses, loading: expensesLoading } = useExpenses()

  // Get cash register data for the selected day
  const { transactions, loading: transactionsLoading } = useCashRegister()

  // Handle date change
  const handleDateChange = (newDate: Date | undefined) => {
    if (!newDate) return;
    setSelectedDate(newDate);
    if (onDateChange) {
      onDateChange(newDate);
    }
  }

  // Calculate percentage change
  const calculatePercentChange = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / Math.abs(previous)) * 100
  }

  // Get payment method breakdown
  const getPaymentMethodBreakdown = () => {
    // TODO: v4 does not have paymentMethod on FinancialTransaction, so we use type as a proxy
    const cashPayments = transactions.filter(tx => tx.type === 'sales' && tx.amount > 0);
    const cardPayments = [];
    const otherPayments = transactions.filter(tx => tx.type !== 'sales' && tx.amount > 0);

    const cashTotal = cashPayments.reduce((sum, tx) => sum + tx.amount, 0);
    const cardTotal = 0;
    const otherTotal = otherPayments.reduce((sum, tx) => sum + tx.amount, 0);
    const total = cashTotal + cardTotal + otherTotal;

    return {
      cash: {
        amount: cashTotal,
        percentage: total > 0 ? (cashTotal / total) * 100 : 0,
        count: cashPayments.length
      },
      card: {
        amount: cardTotal,
        percentage: 0,
        count: 0
      },
      other: {
        amount: otherTotal,
        percentage: total > 0 ? (otherTotal / total) * 100 : 0,
        count: otherPayments.length
      },
      total
    };
  }

  // Get expense category breakdown
  const getExpenseCategoryBreakdown = () => {
    const categories: Record<string, { amount: number, count: number }> = {}

    expenses.forEach(expense => {
      const category = expense.category || 'other'
      if (!categories[category]) {
        categories[category] = { amount: 0, count: 0 }
      }
      categories[category].amount += expense.amount
      categories[category].count += 1
    })

    const total = expenses.reduce((sum, expense) => sum + expense.amount, 0)

    return {
      categories: Object.entries(categories).map(([name, data]) => ({
        name,
        amount: data.amount,
        percentage: total > 0 ? (data.amount / total) * 100 : 0,
        count: data.count
      })),
      total
    }
  }

  // Generate PDF report
  const generatePDF = () => {
    toast({
      title: "PDF Generation",
      description: "This feature will be available soon"
    })
  }

  // Check if the selected date is today
  const isToday = isSameDay(selectedDate, new Date())

  // Get loading state
  const isDataLoading = expensesLoading || transactionsLoading

  // Get payment method breakdown
  const paymentBreakdown = getPaymentMethodBreakdown()

  // Get expense category breakdown
  const expenseBreakdown = getExpenseCategoryBreakdown()

  // Calculate metrics
  const totalIncome = 0 // Calculate income from expenses
  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
  const netProfit = totalIncome - totalExpenses
  const cashBalance = 0 // Calculate cash balance from transactions

  const previousIncome = 0 // Calculate previous income from previous expenses
  const previousExpenses = 0 // Calculate previous expenses from previous expenses
  const previousProfit = previousIncome - previousExpenses

  const incomeChange = calculatePercentChange(totalIncome, previousIncome)
  const expensesChange = calculatePercentChange(totalExpenses, previousExpenses)
  const profitChange = calculatePercentChange(netProfit, previousProfit)

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div>
            <CardTitle>Daily Financial Snapshot</CardTitle>
            <CardDescription>
              Financial summary for {format(selectedDate, 'EEEE, MMMM d, yyyy')}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <DatePicker
              date={selectedDate}
              setDate={handleDateChange}
            />
            <Button
              variant="outline"
              size="icon"
              onClick={generatePDF}
              disabled={isDataLoading}
            >
              <PrinterIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {isDataLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {/* Income */}
              <div className="bg-green-50 rounded-lg p-3 border border-green-100">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-xs text-muted-foreground">Income</p>
                    <p className="text-lg font-bold text-green-600">{formatCurrency(totalIncome)}</p>
                    <div className="flex items-center mt-1">
                      {incomeChange > 0 ? (
                        <ArrowUpIcon className="h-3 w-3 text-green-600 mr-1" />
                      ) : incomeChange < 0 ? (
                        <ArrowDownIcon className="h-3 w-3 text-red-600 mr-1" />
                      ) : (
                        <ArrowRightIcon className="h-3 w-3 text-muted-foreground mr-1" />
                      )}
                      <span className={cn(
                        "text-xs",
                        incomeChange > 0 ? "text-green-600" :
                        incomeChange < 0 ? "text-red-600" : "text-muted-foreground"
                      )}>
                        {incomeChange > 0 ? "+" : ""}{incomeChange.toFixed(1)}% vs prev day
                      </span>
                    </div>
                  </div>
                  <div className="bg-green-100 p-1.5 rounded-full">
                    <TrendingUpIcon className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </div>

              {/* Expenses */}
              <div className="bg-red-50 rounded-lg p-3 border border-red-100">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-xs text-muted-foreground">Expenses</p>
                    <p className="text-lg font-bold text-red-600">{formatCurrency(totalExpenses)}</p>
                    <div className="flex items-center mt-1">
                      {expensesChange > 0 ? (
                        <ArrowUpIcon className="h-3 w-3 text-red-600 mr-1" />
                      ) : expensesChange < 0 ? (
                        <ArrowDownIcon className="h-3 w-3 text-green-600 mr-1" />
                      ) : (
                        <ArrowRightIcon className="h-3 w-3 text-muted-foreground mr-1" />
                      )}
                      <span className={cn(
                        "text-xs",
                        expensesChange > 0 ? "text-red-600" :
                        expensesChange < 0 ? "text-green-600" : "text-muted-foreground"
                      )}>
                        {expensesChange > 0 ? "+" : ""}{expensesChange.toFixed(1)}% vs prev day
                      </span>
                    </div>
                  </div>
                  <div className="bg-red-100 p-1.5 rounded-full">
                    <TrendingDownIcon className="h-4 w-4 text-red-600" />
                  </div>
                </div>
              </div>

              {/* Profit */}
              <div className={cn(
                "rounded-lg p-3 border",
                netProfit >= 0 ? "bg-blue-50 border-blue-100" : "bg-red-50 border-red-100"
              )}>
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-xs text-muted-foreground">Profit/Loss</p>
                    <p className={cn(
                      "text-lg font-bold",
                      netProfit >= 0 ? "text-blue-600" : "text-red-600"
                    )}>
                      {formatCurrency(netProfit)}
                    </p>
                    <div className="flex items-center mt-1">
                      {profitChange > 0 ? (
                        <ArrowUpIcon className="h-3 w-3 text-green-600 mr-1" />
                      ) : profitChange < 0 ? (
                        <ArrowDownIcon className="h-3 w-3 text-red-600 mr-1" />
                      ) : (
                        <ArrowRightIcon className="h-3 w-3 text-muted-foreground mr-1" />
                      )}
                      <span className={cn(
                        "text-xs",
                        profitChange > 0 ? "text-green-600" :
                        profitChange < 0 ? "text-red-600" : "text-muted-foreground"
                      )}>
                        {profitChange > 0 ? "+" : ""}{profitChange.toFixed(1)}% vs prev day
                      </span>
                    </div>
                  </div>
                  <div className={cn(
                    "p-1.5 rounded-full",
                    netProfit >= 0 ? "bg-blue-100" : "bg-red-100"
                  )}>
                    <DollarSignIcon className={cn(
                      "h-4 w-4",
                      netProfit >= 0 ? "text-blue-600" : "text-red-600"
                    )} />
                  </div>
                </div>
              </div>

              {/* Cash Balance */}
              <div className="bg-amber-50 rounded-lg p-3 border border-amber-100">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-xs text-muted-foreground">Cash Balance</p>
                    <p className="text-lg font-bold text-amber-600">{formatCurrency(cashBalance)}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      End of day balance
                    </p>
                  </div>
                  <div className="bg-amber-100 p-1.5 rounded-full">
                    <BanknoteIcon className="h-4 w-4 text-amber-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Breakdown */}
            <Tabs defaultValue="payment" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="payment">
                  <CreditCardIcon className="h-4 w-4 mr-2" />
                  Payment Methods
                </TabsTrigger>
                <TabsTrigger value="expenses">
                  <ReceiptIcon className="h-4 w-4 mr-2" />
                  Expense Categories
                </TabsTrigger>
              </TabsList>

              {/* Payment Methods Tab */}
              <TabsContent value="payment" className="space-y-4 pt-4">
                <div className="grid grid-cols-3 gap-3">
                  {/* Cash */}
                  <div className="bg-green-50 rounded-lg p-3 border border-green-100">
                    <p className="text-xs text-muted-foreground">Cash</p>
                    <p className="text-lg font-bold text-green-600">{formatCurrency(paymentBreakdown.cash.amount)}</p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">{paymentBreakdown.cash.count} transactions</span>
                      <span className="text-xs font-medium">{paymentBreakdown.cash.percentage.toFixed(1)}%</span>
                    </div>
                  </div>

                  {/* Card */}
                  <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                    <p className="text-xs text-muted-foreground">Card</p>
                    <p className="text-lg font-bold text-blue-600">{formatCurrency(paymentBreakdown.card.amount)}</p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">{paymentBreakdown.card.count} transactions</span>
                      <span className="text-xs font-medium">{paymentBreakdown.card.percentage.toFixed(1)}%</span>
                    </div>
                  </div>

                  {/* Other */}
                  <div className="bg-purple-50 rounded-lg p-3 border border-purple-100">
                    <p className="text-xs text-muted-foreground">Other</p>
                    <p className="text-lg font-bold text-purple-600">{formatCurrency(paymentBreakdown.other.amount)}</p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">{paymentBreakdown.other.count} transactions</span>
                      <span className="text-xs font-medium">{paymentBreakdown.other.percentage.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>

                {/* Payment Method Visualization */}
                <div className="h-4 w-full rounded-full overflow-hidden bg-gray-100 flex">
                  <div
                    className="h-full bg-green-500"
                    style={{ width: `${paymentBreakdown.cash.percentage}%` }}
                  ></div>
                  <div
                    className="h-full bg-blue-500"
                    style={{ width: `${paymentBreakdown.card.percentage}%` }}
                  ></div>
                  <div
                    className="h-full bg-purple-500"
                    style={{ width: `${paymentBreakdown.other.percentage}%` }}
                  ></div>
                </div>
              </TabsContent>

              {/* Expense Categories Tab */}
              <TabsContent value="expenses" className="space-y-4 pt-4">
                {expenseBreakdown.categories.length > 0 ? (
                  <div className="space-y-2">
                    {expenseBreakdown.categories
                      .sort((a, b) => b.amount - a.amount)
                      .map((category, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div
                            className="h-2 w-2 rounded-full"
                            style={{
                              backgroundColor: `hsl(${(index * 30) % 360}, 70%, 50%)`
                            }}
                          ></div>
                          <div className="flex-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm capitalize">{category.name}</span>
                              <span className="text-sm font-medium">{formatCurrency(category.amount)}</span>
                            </div>
                            <div className="w-full h-1.5 bg-gray-100 rounded-full mt-1">
                              <div
                                className="h-full rounded-full"
                                style={{
                                  width: `${category.percentage}%`,
                                  backgroundColor: `hsl(${(index * 30) % 360}, 70%, 50%)`
                                }}
                              ></div>
                            </div>
                          </div>
                          <span className="text-xs text-muted-foreground w-12 text-right">
                            {category.percentage.toFixed(1)}%
                          </span>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No expenses recorded for this day
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-4">
        <Button variant="outline" size="sm" onClick={() => handleDateChange(subDays(selectedDate, 1))}>
          Previous Day
        </Button>
        {!isToday && (
          <Button variant="outline" size="sm" onClick={() => handleDateChange(new Date())}>
            Today
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
