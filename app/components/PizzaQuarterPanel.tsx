"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Check, Pizza } from 'lucide-react';
import { MenuItem } from '@/lib/db/v4/schemas/menu-schema';
import { PizzaQuarter } from '@/lib/db/v4/schemas/order-schema';
import { cn } from '@/lib/utils';
import { 
  calculateCustomPizzaPrice, 
  generateCustomPizzaName, 
  validateCustomPizza,
  getQuarterLabel,
  getQuarterPath 
} from '@/lib/utils/pizza-utils';

interface PizzaQuarterPanelProps {
  availablePizzas: MenuItem[];
  selectedSize: string;
  quarters: (PizzaQuarter | null)[];
  pricingMethod: 'max' | 'average';
  categoryColor: string;
  onQuarterSelect: (quarterIndex: number, pizza: MenuItem) => void;
  onConfirm: () => void;
  onCancel: () => void;
}

const PizzaQuarterPanel: React.FC<PizzaQuarterPanelProps> = ({
  availablePizzas,
  selectedSize,
  quarters,
  pricingMethod,
  categoryColor,
  onQuarterSelect,
  onConfirm,
  onCancel
}) => {
  const [openPopover, setOpenPopover] = useState<number | null>(null);
  
  // Calculate total price based on pricing method
  const calculatePrice = useCallback(() => {
    const filledQuarters = quarters.filter(q => q !== null) as PizzaQuarter[];
    if (filledQuarters.length === 0) return 0;
    
    if (pricingMethod === 'max') {
      return Math.max(...filledQuarters.map(q => q.price));
    } else {
      return filledQuarters.reduce((sum, q) => sum + q.price, 0) / filledQuarters.length;
    }
  }, [quarters, pricingMethod]);

  // Check if all quarters are filled
  const isComplete = quarters.every(q => q !== null);
  const filledCount = quarters.filter(q => q !== null).length;

  // Quarter positions for SVG circle
  const quarterPaths = [
    "M 50 50 L 50 10 A 40 40 0 0 1 90 50 Z", // Top Right
    "M 50 50 L 90 50 A 40 40 0 0 1 50 90 Z", // Bottom Right  
    "M 50 50 L 50 90 A 40 40 0 0 1 10 50 Z", // Bottom Left
    "M 50 50 L 10 50 A 40 40 0 0 1 50 10 Z"  // Top Left
  ];

  const quarterLabels = ["1/4", "2/4", "3/4", "4/4"];

  const getQuarterColor = (index: number) => {
    const quarter = quarters[index];
    if (!quarter) return '#e5e7eb'; // Gray for empty
    
    // Try to find the pizza's category color, fallback to default
    return categoryColor || '#f3f4f6';
  };

  const handleQuarterClick = (index: number) => {
    setOpenPopover(openPopover === index ? null : index);
  };

  const handlePizzaSelect = (quarterIndex: number, pizza: MenuItem) => {
    onQuarterSelect(quarterIndex, pizza);
    setOpenPopover(null);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Pizza className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Pizza Personnalisée</h3>
          <Badge variant="outline" className="text-xs">
            {selectedSize === 'default' ? 'Classique' : selectedSize}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">
          {filledCount}/4 quarts sélectionnés
        </div>
      </div>

      {/* Pizza Circle Visualization */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center space-y-4">
            <svg width="200" height="200" viewBox="0 0 100 100" className="drop-shadow-sm">
              {/* Background circle */}
              <circle cx="50" cy="50" r="40" fill="#f9fafb" stroke="#e5e7eb" strokeWidth="1"/>
              
              {/* Quarter segments */}
              {quarterPaths.map((path, index) => (
                <g key={index}>
                  <path
                    d={path}
                    fill={getQuarterColor(index)}
                    stroke="#374151"
                    strokeWidth="1"
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => handleQuarterClick(index)}
                  />
                  {/* Quarter label */}
                  <text
                    x={index === 0 ? 65 : index === 1 ? 65 : index === 2 ? 35 : 35}
                    y={index === 0 ? 35 : index === 1 ? 65 : index === 2 ? 65 : 35}
                    fontSize="8"
                    textAnchor="middle"
                    fill="#374141"
                    className="pointer-events-none font-medium"
                  >
                    {quarterLabels[index]}
                  </text>
                </g>
              ))}
              
              {/* Center dot */}
              <circle cx="50" cy="50" r="2" fill="#6b7280"/>
            </svg>

            {/* Quarter selection popovers */}
            {quarters.map((quarter, index) => (
              <Popover 
                key={index} 
                open={openPopover === index} 
                onOpenChange={(open) => setOpenPopover(open ? index : null)}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost" 
                    size="sm"
                    className="absolute opacity-0 pointer-events-none"
                  >
                    Hidden Trigger
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" side="top">
                  <div className="p-3 border-b">
                    <h4 className="font-medium">Sélectionner pizza pour le quart {index + 1}</h4>
                  </div>
                  <ScrollArea className="h-64">
                    <div className="p-2">
                      {availablePizzas.map((pizza) => (
                        <button
                          key={pizza.id}
                          className="w-full p-2 text-left hover:bg-muted rounded-md transition-colors flex items-center justify-between"
                          onClick={() => handlePizzaSelect(index, pizza)}
                        >
                          <div>
                            <div className="font-medium text-sm">{pizza.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {pizza.prices[selectedSize] || Object.values(pizza.prices)[0]} DA
                            </div>
                          </div>
                          {quarter?.menuItemId === pizza.id && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </button>
                      ))}
                    </div>
                  </ScrollArea>
                </PopoverContent>
              </Popover>
            ))}

            {/* Click handlers for SVG quarters */}
            {quarters.map((quarter, index) => {
              const positions = [
                { x: 65, y: 35 }, // Top Right
                { x: 65, y: 65 }, // Bottom Right
                { x: 35, y: 65 }, // Bottom Left
                { x: 35, y: 35 }  // Top Left
              ];
              
              return (
                <button
                  key={`quarter-btn-${index}`}
                  className="absolute w-8 h-8 rounded-full bg-white/80 hover:bg-white border border-gray-300 shadow-sm flex items-center justify-center text-xs font-medium transition-all"
                  style={{
                    left: `${(positions[index].x / 100) * 200 - 16}px`,
                    top: `${(positions[index].y / 100) * 200 - 16}px`,
                  }}
                  onClick={() => handleQuarterClick(index)}
                >
                  {index + 1}
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Selected quarters summary */}
      {filledCount > 0 && (
        <Card>
          <CardContent className="p-4">
            <h4 className="font-medium mb-2">Composition sélectionnée:</h4>
            <div className="grid grid-cols-2 gap-2">
              {quarters.map((quarter, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: getQuarterColor(index) }}></div>
                  <span className="text-muted-foreground">Quart {index + 1}:</span>
                  <span className="font-medium">
                    {quarter ? quarter.name : 'Non sélectionné'}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pricing and actions */}
      <div className="flex items-center justify-between bg-muted/50 p-3 rounded-lg">
        <div>
          <div className="text-sm text-muted-foreground">
            Prix ({pricingMethod === 'max' ? 'Maximum' : 'Moyenne'})
          </div>
          <div className="text-lg font-bold">
            {calculatePrice().toFixed(0)} DA
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            Annuler
          </Button>
          <Button 
            onClick={onConfirm} 
            disabled={!isComplete}
            className="bg-primary hover:bg-primary/90"
          >
            Confirmer Pizza
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PizzaQuarterPanel; 