'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Users, Plus, Trash2, Save, X, Utensils, Tag } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useStaffMenuV4 } from '@/lib/hooks/useStaffMenuV4';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from '@/lib/utils';
import { MenuCategory, MenuItem } from '@/lib/db/v4/schemas/menu-schema';
import { StaffMenuItem } from '@/lib/db/v4/schemas/staff-menu-schema';

interface AddItemForm {
  categoryId: string;
  itemId: string;
  size: string;
  staffPrice: number;
}

export function EquipeMenuManagement() {
  const { categories, isLoading: menuLoading } = useMenuV4();
  const {
    config,
    isLoading: staffMenuLoading,
    error,
    staffMenuItems,
    allowancePerShift,
    updateAllowancePerShift,
    addItem,
    updateItem,
    removeItem,
    refresh
  } = useStaffMenuV4();

  const [newAllowance, setNewAllowance] = useState<string>('');
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editingPrice, setEditingPrice] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [addForm, setAddForm] = useState<AddItemForm>({
    categoryId: '',
    itemId: '',
    size: '',
    staffPrice: 0
  });

  const isLoading = menuLoading || staffMenuLoading;

  // Get all items with sizes for the selected category
  const availableItems = useMemo(() => {
    if (!addForm.categoryId || !categories) return [];
    
    const category = categories.find(cat => cat.id === addForm.categoryId);
    if (!category) return [];

    const items: Array<{ item: MenuItem; size: string; price: number }> = [];
    
    category.items.forEach(item => {
      // Get all sizes/prices for this item
      const sizePairs = Object.entries(item.prices);
      
      if (sizePairs.length > 0) {
        sizePairs.forEach(([sizeName, price]) => {
          items.push({
            item,
            size: sizeName,
            price: price
          });
        });
      } else {
        items.push({
          item,
          size: 'Standard',
          price: 0
        });
      }
    });

    return items;
  }, [categories, addForm.categoryId]);

  // Get selected item details
  const selectedItemDetails = useMemo(() => {
    return availableItems.find(item => `${item.item.id}-${item.size}` === addForm.itemId);
  }, [availableItems, addForm.itemId]);

  // Handle allowance update
  const handleAllowanceUpdate = async () => {
    const allowance = parseInt(newAllowance);
    if (isNaN(allowance) || allowance < 0) return;
    
    try {
      await updateAllowancePerShift(allowance);
      setNewAllowance('');
    } catch (error) {
      console.error('Failed to update allowance:', error);
    }
  };

  // Handle add item
  const handleAddItem = async () => {
    if (!selectedItemDetails) return;

    try {
      const newStaffItem: Omit<StaffMenuItem, 'id'> = {
        menuItemId: selectedItemDetails.item.id,
        itemName: selectedItemDetails.item.name,
        categoryName: categories?.find(cat => cat.id === addForm.categoryId)?.name || '',
        size: selectedItemDetails.size,
        originalPrice: selectedItemDetails.price,
        staffPrice: addForm.staffPrice
      };

      await addItem(newStaffItem);
      
      // Reset form
      setAddForm({
        categoryId: '',
        itemId: '',
        size: '',
        staffPrice: 0
      });
      setShowAddForm(false);
    } catch (error) {
      console.error('Failed to add staff menu item:', error);
    }
  };

  // Handle edit price
  const handleEditPrice = (itemId: string, currentPrice: number) => {
    setEditingItemId(itemId);
    setEditingPrice(currentPrice.toString());
  };

  // Handle save price
  const handleSavePrice = async (itemId: string) => {
    const price = parseFloat(editingPrice);
    if (isNaN(price) || price < 0) return;

    try {
      await updateItem(itemId, { staffPrice: price });
      setEditingItemId(null);
      setEditingPrice('');
    } catch (error) {
      console.error('Failed to update item price:', error);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingItemId(null);
    setEditingPrice('');
  };

  // Handle remove item
  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeItem(itemId);
    } catch (error) {
      console.error('Failed to remove staff menu item:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 flex items-center justify-center">
        <div className="text-muted-foreground">Chargement...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-destructive/15 text-destructive px-3 py-2 rounded-md text-sm">
          Erreur: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3 p-1">
      {/* Allowance Per Shift Configuration */}
      <div className="border rounded-lg p-3 pb-1 shadow-sm">
        <div className="flex items-center gap-1 mb-1">
          <Users className="h-4 w-4 text-muted-foreground" />
          <h2 className="text-sm font-semibold">Paramètres Équipe</h2>
        </div>
        <p className="text-xs text-muted-foreground mb-2">
          Configurez l'allocation de repas par service pour le personnel
        </p>
        <div className="flex items-center gap-2">
          <Label htmlFor="allowance" className="text-xs font-medium whitespace-nowrap">
            Repas par service:
          </Label>
          <Input
            id="allowance"
            type="number"
            min="0"
            value={newAllowance || allowancePerShift}
            onChange={(e) => setNewAllowance(e.target.value)}
            className="w-14 h-7 text-sm"
          />
          {newAllowance && (
            <Button
              size="sm"
              onClick={handleAllowanceUpdate}
              className="whitespace-nowrap h-7 px-2 text-xs"
            >
              <Save className="h-3 w-3 mr-1" />
              Sauvegarder
            </Button>
          )}
        </div>
      </div>

      {/* Staff Menu Items */}
      <div className="border rounded-lg p-3 pb-1 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 mb-1">
            <Utensils className="h-4 w-4 text-muted-foreground" />
            <h2 className="text-sm font-semibold">Menu Équipe</h2>
          </div>
          <Button
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
            className="whitespace-nowrap h-7 px-2 text-xs"
            disabled={showAddForm}
          >
            <Plus className="h-3 w-3 mr-1" />
            Ajouter
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mb-2">
          Articles disponibles pour le personnel avec prix spéciaux
        </p>

        {/* Content wrapper, replacing CardContent */}
        <div className="space-y-2">
          {/* Add Item Form */}
          {showAddForm && (
            <div className="border rounded-lg p-3 bg-muted/30 space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">Nouvel Article</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAddForm(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                {/* Category Selection */}
                <div className="space-y-1">
                  <Label className="text-xs">Catégorie</Label>
                  <Select
                    value={addForm.categoryId}
                    onValueChange={(value) => setAddForm(prev => ({ ...prev, categoryId: value, itemId: '', size: '' }))}
                  >
                    <SelectTrigger className="h-7 px-2 text-sm">
                      <SelectValue placeholder="Choisir..." />
                    </SelectTrigger>
                    <SelectContent>
                      {categories?.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Item Selection */}
                <div className="space-y-1">
                  <Label className="text-xs">Article</Label>
                  <Select
                    value={addForm.itemId}
                    onValueChange={(value) => {
                      setAddForm(prev => ({ ...prev, itemId: value }));
                      setAddForm(prev => ({ ...prev, staffPrice: 0 }));
                    }}
                    disabled={!addForm.categoryId}
                  >
                    <SelectTrigger className="h-7 px-2 text-sm">
                      <SelectValue placeholder="Choisir..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableItems.map(({ item, size, price }) => (
                        <SelectItem key={`${item.id}-${size}`} value={`${item.id}-${size}`}>
                          {item.name} ({size}) - {price} DA
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Add Button */}
                <div className="space-y-1">
                  <Label className="text-xs opacity-0">Action</Label>
                  <Button
                    onClick={handleAddItem}
                    disabled={!addForm.categoryId || !addForm.itemId}
                    className="w-full h-7 text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Ajouter
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Staff Menu Items Table */}
          {staffMenuItems.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="h-7 px-2 text-xs">Article</TableHead>
                    <TableHead className="h-7 px-2 text-xs">Catégorie</TableHead>
                    <TableHead className="h-7 px-2 text-xs">Taille</TableHead>
                    <TableHead className="h-7 px-2 text-xs">Prix Original</TableHead>
                    <TableHead className="h-7 px-2 text-xs">Prix Équipe</TableHead>
                    <TableHead className="w-[50px] h-7 px-2 text-xs">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {staffMenuItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium py-1 px-2 text-sm">{item.itemName}</TableCell>
                      <TableCell className="py-1 px-2 text-sm">
                        <div className="flex items-center gap-1">
                          <Tag className="h-3 w-3 text-muted-foreground" />
                          {item.categoryName}
                        </div>
                      </TableCell>
                      <TableCell className="py-1 px-2 text-sm">{item.size}</TableCell>
                      <TableCell className="py-1 px-2 text-sm">{item.originalPrice} DA</TableCell>
                      <TableCell className="py-1 px-2 text-sm">
                        <span className="px-2 py-1 text-sm text-muted-foreground">
                          0 DA
                        </span>
                      </TableCell>
                      <TableCell className="py-1 px-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleRemoveItem(item.id)}
                          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground">
              <Utensils className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Aucun article dans le menu équipe</p>
              <p className="text-xs">Cliquez sur "Ajouter" pour commencer</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 