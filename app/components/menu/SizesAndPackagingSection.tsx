"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, X, Check, Package, Utensils, Car, ChevronDown, ChevronRight, PencilIcon, TrashIcon } from "lucide-react";
import { Category, MenuItem } from "@/lib/db/v4-menu-service";
import { SearchableStockItemSelect } from '@/components/stock/SearchableStockItemSelect';
import { updateCategoryPackaging, getCategoryPackaging, OrderType } from '@/lib/db/v4/operations/packaging-ops';

// --- Fix: use React.ReactNode for ORDER_TYPES ---
const ORDER_TYPES: [OrderType, string, React.ReactNode][] = [
    ['dine-in', 'Sur place', <Utensils className='h-3 w-3'/>],
    ['takeaway', 'À emporter', <Package className='h-3 w-3'/>],
    ['delivery', 'Livraison', <Car className='h-3 w-3'/>],
];
// --- END ---

interface SizesAndPackagingSectionProps {
    category: Category;
    isAddingSize: boolean;
    setIsAddingSize: React.Dispatch<React.SetStateAction<boolean>>;
    newSize: string;
    setNewSize: React.Dispatch<React.SetStateAction<string>>;
    addSizeToCategory: (categoryId: string, size: string) => Promise<string[]>;
    renameSizeInCategory: (categoryId: string, oldSizeName: string, newSizeName: string) => Promise<Category>;
    deleteSizeFromCategory: (categoryId: string, sizeName: string) => Promise<string[]>;
    refreshCategories: () => Promise<void>;
    stockItems: any[];
    setDeleteConfirmDialog: React.Dispatch<React.SetStateAction<{
        open: boolean;
        itemId?: string;
        categoryId?: string;
        tableId?: string;
        sizeName?: string;
        type: 'item' | 'category' | 'table' | 'size';
    }>>;
}

export function SizesAndPackagingSection({
    category,
    isAddingSize,
    setIsAddingSize,
    newSize,
    setNewSize,
    addSizeToCategory,
    renameSizeInCategory,
    deleteSizeFromCategory,
    refreshCategories,
    stockItems,
    setDeleteConfirmDialog
}: SizesAndPackagingSectionProps) {
    // State for editing sizes
    const [editingSizeName, setEditingSizeName] = useState<string | null>(null);
    const [editedSizeName, setEditedSizeName] = useState<string>("");

    // Add packaging state management
    const [packagingConfig, setPackagingConfig] = useState<{
        [sizeName: string]: {
            'dine-in': Array<{ stockItemId: string; quantity: number }>;
            'takeaway': Array<{ stockItemId: string; quantity: number }>;
            'delivery': Array<{ stockItemId: string; quantity: number }>;
        }
    }>({});
    const [editingPackaging, setEditingPackaging] = useState<{
        size: string | null;
        orderType: 'dine-in' | 'takeaway' | 'delivery' | null;
    }>({ size: null, orderType: null });
    const [newPackagingItem, setNewPackagingItem] = useState<{
        stockItemId: string;
        quantity: number;
    }>({ stockItemId: '', quantity: 1 });
    // Add this state for the add row
    const [addingPackaging, setAddingPackaging] = useState<{ size: string; type: OrderType } | null>(null);
    const [expandedPackaging, setExpandedPackaging] = useState<string | null>(null);

    // Load packaging configuration for the category
    const loadPackagingConfig = async () => {
        if (!category.sizes) return;

        const config: typeof packagingConfig = {};
        for (const size of category.sizes) {
            config[size] = {
                'dine-in': await getCategoryPackaging(category.id, size, 'dine-in'),
                'takeaway': await getCategoryPackaging(category.id, size, 'takeaway'),
                'delivery': await getCategoryPackaging(category.id, size, 'delivery')
            };
        }
        setPackagingConfig(config);
    };

    // Load packaging config when category changes
    useEffect(() => {
        loadPackagingConfig();
    }, [category.id, category.sizes]);

    // Handle opening packaging configuration
    const handleOpenPackagingConfig = (size: string, orderType: OrderType) => {
        // Toggle: if already editing this combination, close it; otherwise open it
        if (editingPackaging.size === size && editingPackaging.orderType === orderType) {
            setEditingPackaging({ size: null, orderType: null });
        } else {
            setEditingPackaging({ size, orderType });
        }
        setNewPackagingItem({ stockItemId: '', quantity: 1 });
    };

    // Handle closing packaging configuration
    const handleClosePackagingConfig = () => {
        setEditingPackaging({ size: null, orderType: null });
        setNewPackagingItem({ stockItemId: '', quantity: 1 });
    };

    // Handle adding packaging item
    const handleAddPackagingItem = async () => {
        // Use addingPackaging if present, otherwise fallback to editingPackaging
        const size = addingPackaging?.size || editingPackaging.size;
        const orderType = addingPackaging?.type || editingPackaging.orderType;
        if (!size || !orderType || !newPackagingItem.stockItemId) return;

        const currentPackaging = packagingConfig[size]?.[orderType] || [];
        const updatedPackaging = [...currentPackaging, newPackagingItem];

        try {
            await updateCategoryPackaging(category.id, size, orderType, updatedPackaging);
            await loadPackagingConfig();
            setNewPackagingItem({ stockItemId: '', quantity: 1 });
            if (addingPackaging) setAddingPackaging(null);
        } catch (error) {
            console.error('Error adding packaging item:', error);
        }
    };

    // Handle removing packaging item
    const handleRemovePackagingItem = async (index: number) => {
        if (!editingPackaging.size || !editingPackaging.orderType) return;

        const currentPackaging = packagingConfig[editingPackaging.size]?.[editingPackaging.orderType] || [];
        const updatedPackaging = currentPackaging.filter((_, i) => i !== index);

        try {
            await updateCategoryPackaging(category.id, editingPackaging.size, editingPackaging.orderType, updatedPackaging);
            await loadPackagingConfig();
        } catch (error) {
            console.error('Error removing packaging item:', error);
        }
    };

    // Handle updating packaging item quantity
    const handleUpdatePackagingQuantity = async (index: number, quantity: number) => {
        if (!editingPackaging.size || !editingPackaging.orderType) return;

        const currentPackaging = packagingConfig[editingPackaging.size]?.[editingPackaging.orderType] || [];
        const updatedPackaging = [...currentPackaging];
        updatedPackaging[index] = { ...updatedPackaging[index], quantity };

        try {
            await updateCategoryPackaging(category.id, editingPackaging.size, editingPackaging.orderType, updatedPackaging);
            await loadPackagingConfig();
        } catch (error) {
            console.error('Error updating packaging quantity:', error);
        }
    };

    // Get packaging count for display
    const getPackagingCount = (size: string, orderType: OrderType): number => {
        return packagingConfig[size]?.[orderType]?.length || 0;
    };

    // Get stock item name
    const getStockItemName = (stockItemId: string): string => {
        const stockItem = stockItems.find(item => item.id === stockItemId);
        return stockItem?.name || 'Unknown Item';
    };

    // Handle adding a size
    const handleAddSize = async () => {
        if (!newSize) return;

        try {
            await addSizeToCategory(category.id, newSize);
            setNewSize("");
            setIsAddingSize(false);
            await refreshCategories();
        } catch (error) {
            console.error('❌ Error adding size:', error);
        }
    };

    // Start editing a size
    const startEditingSize = (size: string) => {
        setEditingSizeName(size);
        setEditedSizeName(size);

        // Count items affected by this size change
        const itemsUsingSize = category.items.filter(item =>
            item.prices && item.prices[size] !== undefined
        );

        console.log(`${itemsUsingSize.length} items will be affected by renaming "${size}"`);
    };

    // Cancel size edit
    const cancelSizeEdit = () => {
        setEditingSizeName(null);
        setEditedSizeName("");
    };

    // Save size edit
    const saveSizeEdit = async () => {
        if (editingSizeName && editedSizeName) {
            try {
                await renameSizeInCategory(category.id, editingSizeName, editedSizeName);
                cancelSizeEdit();
                await refreshCategories();
            } catch (error) {
                console.error('❌ Error renaming size:', error);
            }
        }
    };

    // Handle deleting a size
    const handleDeleteSizeClick = (sizeName: string) => {
        setDeleteConfirmDialog({
            open: true,
            categoryId: category.id,
            sizeName,
            type: 'size'
        });
    };

    return (
        <Card className="overflow-hidden">
            <CardHeader className="py-2 px-3">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-sm">Tailles & Emballage</CardTitle>
                    <Button
                        variant="outline"
                        size="sm"
                        className="h-7 px-2"
                        onClick={() => setIsAddingSize(true)}
                    >
                        <Plus className="h-3 w-3 mr-1" />
                        Ajouter
                    </Button>
                </div>
            </CardHeader>
            <CardContent className="py-1 px-3">
                {isAddingSize ? (
                    <div className="space-y-2 my-1">
                        <div className="flex space-x-1">
                            <Input
                                value={newSize}
                                onChange={(e) => setNewSize(e.target.value)}
                                placeholder="Nom de la taille"
                                className="h-7 text-xs"
                                autoFocus
                            />
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-7 w-7 p-0"
                                onClick={handleAddSize}
                                disabled={!newSize}
                            >
                                <Check className="h-3.5 w-3.5 text-green-600" />
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-7 w-7 p-0"
                                onClick={() => {
                                    setIsAddingSize(false);
                                    setNewSize("");
                                }}
                            >
                                <X className="h-3.5 w-3.5 text-red-600" />
                            </Button>
                        </div>

                        {/* Packaging configuration for new size */}
                        {newSize && (
                            <div className="border-t mt-2 pt-2">
                                <div className="flex items-center gap-2 mb-1">
                                    <Package className="h-4 w-4 text-foreground" />
                                    <span className="text-xs font-semibold text-foreground">Emballage</span>
                                    <Badge variant="outline" className="text-xs">{newSize}</Badge>
                                </div>
                                <div className="flex flex-col gap-2 min-w-[420px]">
                                    {ORDER_TYPES.map(([type, label, icon]) => (
                                        <div key={type} className="flex items-center gap-3 text-xs py-1 border-b last:border-b-0 border-border">
                                            <span className="flex items-center gap-1 text-muted-foreground min-w-[90px]">{icon}{label}</span>
                                            <div className="flex flex-wrap gap-1 flex-1">
                                                {(packagingConfig[newSize] && packagingConfig[newSize][type] && packagingConfig[newSize][type].length > 0) ? (
                                                    packagingConfig[newSize][type].map((item: { stockItemId: string; quantity: number }, idx: number) => (
                                                        <Badge key={idx} variant="outline" className="text-xs px-2 py-0.5 flex items-center gap-1">
                                                            {getStockItemName(item.stockItemId)}
                                                            <span className="text-[10px] text-muted-foreground">×{item.quantity}</span>
                                                            <button type="button" className="ml-1 text-muted-foreground hover:text-foreground" onClick={() => handleRemovePackagingItem(idx)}>
                                                                <Trash2 className="h-3 w-3" />
                                                            </button>
                                                        </Badge>
                                                    ))
                                                ) : (
                                                    <span className="text-muted-foreground">Aucun</span>
                                                )}
                                            </div>
                                            {addingPackaging && addingPackaging.size === newSize && addingPackaging.type === type ? (
                                                <div className="flex items-center gap-2">
                                                    <SearchableStockItemSelect
                                                        value={newPackagingItem.stockItemId}
                                                        onChange={(value: string) => setNewPackagingItem({ ...newPackagingItem, stockItemId: value })}
                                                        stockItems={stockItems}
                                                        placeholder="Sélectionner un article"
                                                        compact={false}
                                                    />
                                                    <Input
                                                        type="number"
                                                        min="0.1"
                                                        step="0.1"
                                                        value={newPackagingItem.quantity}
                                                        onChange={(e) => setNewPackagingItem({ ...newPackagingItem, quantity: parseFloat(e.target.value) || 1 })}
                                                        placeholder="Qté"
                                                        className="w-16 h-7 text-xs"
                                                    />
                                                    <Button
                                                        onClick={async () => {
                                                            await handleAddPackagingItem();
                                                            setAddingPackaging(null);
                                                        }}
                                                        disabled={!newPackagingItem.stockItemId || newPackagingItem.quantity <= 0}
                                                        size="sm"
                                                        className="h-7 px-2"
                                                    >
                                                        <Plus className="h-3 w-3" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                                                        onClick={() => setAddingPackaging(null)}
                                                    >
                                                        <X className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            ) : (
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6 p-0 ml-2 text-muted-foreground hover:text-foreground"
                                                    onClick={() => {
                                                        setAddingPackaging({ size: newSize, type });
                                                        setNewPackagingItem({ stockItemId: '', quantity: 1 });
                                                    }}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="flex flex-col gap-3 mt-1">
                        {category.sizes?.map(size => (
                            <div key={size} className="group relative border border-border rounded-lg bg-card px-3 py-2 shadow-sm hover:shadow-md transition-all">
                                <div className="flex items-center justify-between mb-1">
                                    <div className="flex items-center gap-2">
                                        {editingSizeName === size ? (
                                            <div className="flex items-center gap-2">
                                                <Input
                                                    value={editedSizeName}
                                                    onChange={(e) => setEditedSizeName(e.target.value)}
                                                    placeholder="Size name"
                                                    className="h-7 text-sm w-32"
                                                    autoFocus
                                                />
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-7 w-7 p-0"
                                                    onClick={saveSizeEdit}
                                                    disabled={!editedSizeName || editedSizeName === size}
                                                >
                                                    <Check className="h-3.5 w-3.5 text-green-600" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-7 w-7 p-0"
                                                    onClick={cancelSizeEdit}
                                                >
                                                    <X className="h-3.5 w-3.5 text-red-600" />
                                                </Button>
                                            </div>
                                        ) : (
                                            <>
                                                <span className="font-semibold text-foreground text-sm">{size}</span>
                                                <Badge variant="outline" className="flex items-center gap-1 px-2 py-0.5 text-xs font-medium min-h-0 h-6 w-fit">
                                                    <Package className="h-3 w-3" />
                                                    <span>{getPackagingCount(size, 'dine-in') + getPackagingCount(size, 'takeaway') + getPackagingCount(size, 'delivery')}</span>
                                                </Badge>
                                            </>
                                        )}
                                    </div>
                                    <div className="flex items-center gap-1">
                                        {!editingSizeName && (
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                                                onClick={() => setExpandedPackaging(expandedPackaging === size ? null : size)}
                                            >
                                                {expandedPackaging === size ? (
                                                    <ChevronDown className="h-4 w-4" />
                                                ) : (
                                                    <ChevronRight className="h-4 w-4" />
                                                )}
                                            </Button>
                                        )}
                                        <div className="flex items-center gap-1">
                                            <button
                                                type="button"
                                                onClick={() => startEditingSize(size)}
                                                className="h-6 w-6 flex items-center justify-center rounded hover:bg-accent transition-colors cursor-pointer"
                                                aria-label="Edit size"
                                            >
                                                <PencilIcon className="h-4 w-4 text-foreground" />
                                            </button>
                                            <button
                                                type="button"
                                                onClick={() => handleDeleteSizeClick(size)}
                                                className="h-6 w-6 flex items-center justify-center rounded hover:bg-accent text-foreground transition-colors cursor-pointer"
                                                aria-label="Remove size"
                                            >
                                                <TrashIcon className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {expandedPackaging === size && (
                                    <div className="divide-y divide-border border-t border-border pt-2 mt-2">
                                        {ORDER_TYPES.map(([type, label, icon]) => (
                                            <div key={type} className="flex items-center gap-3 text-xs py-1">
                                                <span className="flex items-center gap-1 text-muted-foreground min-w-[90px]">{icon}{label}</span>
                                                <div className="flex flex-wrap gap-1 flex-1">
                                                    {(packagingConfig[size] && packagingConfig[size][type] && packagingConfig[size][type].length > 0) ? (
                                                        packagingConfig[size][type].map((item: { stockItemId: string; quantity: number }, idx: number) => (
                                                            <Badge key={idx} variant="outline" className="text-xs px-2 py-0.5 flex items-center gap-1">
                                                                {getStockItemName(item.stockItemId)}
                                                                <span className="text-[10px] text-muted-foreground">×{item.quantity}</span>
                                                                <button type="button" className="ml-1 text-muted-foreground hover:text-foreground" onClick={() => handleRemovePackagingItem(idx)}>
                                                                    <Trash2 className="h-3 w-3" />
                                                                </button>
                                                            </Badge>
                                                        ))
                                                    ) : (
                                                        <span className="text-muted-foreground">Aucun</span>
                                                    )}
                                                </div>
                                                {addingPackaging && addingPackaging.size === size && addingPackaging.type === type ? (
                                                    <div className="flex items-center gap-2">
                                                        <SearchableStockItemSelect
                                                            value={newPackagingItem.stockItemId}
                                                            onChange={(value: string) => setNewPackagingItem({ ...newPackagingItem, stockItemId: value })}
                                                            stockItems={stockItems}
                                                            placeholder="Sélectionner un article"
                                                            compact={false}
                                                        />
                                                        <Input
                                                            type="number"
                                                            min="0.1"
                                                            step="0.1"
                                                            value={newPackagingItem.quantity}
                                                            onChange={(e) => setNewPackagingItem({ ...newPackagingItem, quantity: parseFloat(e.target.value) || 1 })}
                                                            placeholder="Qté"
                                                            className="w-16 h-7 text-xs"
                                                        />
                                                        <Button
                                                            onClick={async () => {
                                                                await handleAddPackagingItem();
                                                                setAddingPackaging(null);
                                                            }}
                                                            disabled={!newPackagingItem.stockItemId || newPackagingItem.quantity <= 0}
                                                            size="sm"
                                                            className="h-7 px-2"
                                                        >
                                                            <Plus className="h-3 w-3" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground"
                                                            onClick={() => setAddingPackaging(null)}
                                                        >
                                                            <X className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                ) : (
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-6 w-6 p-0 ml-2 text-muted-foreground hover:text-foreground"
                                                        onClick={() => {
                                                            setAddingPackaging({ size, type });
                                                            setNewPackagingItem({ stockItemId: '', quantity: 1 });
                                                        }}
                                                    >
                                                        <Plus className="h-3 w-3" />
                                                    </Button>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
} 