'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Printer, 
  Settings, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Monitor,
  Smartphone,
  Wifi,
  WifiOff
} from 'lucide-react';
import { printExecutionService } from '@/lib/services/print-execution-service';
import { printStatusMonitor } from '@/lib/services/print-status-monitor';
import { toast } from 'sonner';

interface PrinterInfo {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown';
  type: 'thermal' | 'inkjet' | 'laser';
  isDefault?: boolean;
  isElectronPrinter?: boolean;
}

interface PrintSettings {
  silentPrinting: boolean;
  autoKitchenPrint: boolean;
  autoReceiptPrint: boolean;
  defaultKitchenPrinter: string;
  defaultReceiptPrinter: string;
  printQuality: 'draft' | 'normal' | 'high';
  copies: number;
  enableNotifications: boolean;
  retryAttempts: number;
  offlineSpooling: boolean;
}

export default function PrintSettings() {
  const [printers, setPrinters] = useState<PrinterInfo[]>([]);
  const [settings, setSettings] = useState<PrintSettings>({
    silentPrinting: true,
    autoKitchenPrint: true,
    autoReceiptPrint: true,
    defaultKitchenPrinter: '',
    defaultReceiptPrinter: '',
    printQuality: 'normal',
    copies: 1,
    enableNotifications: true,
    retryAttempts: 3,
    offlineSpooling: true
  });
  const [isElectron, setIsElectron] = useState(false);
  const [testingPrinter, setTestingPrinter] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<any>(null);
  const [queueStats, setQueueStats] = useState<any>(null);

  useEffect(() => {
    // Detect environment
    setIsElectron(!!(window as any).electronAPI);
    
    // Load settings
    loadSettings();
    
    // Load printers
    loadPrinters();
    
    // Load metrics
    loadMetrics();
    
    // Setup periodic updates
    const interval = setInterval(() => {
      loadMetrics();
      loadQueueStats();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const loadSettings = () => {
    try {
      const stored = localStorage.getItem('print-settings');
      if (stored) {
        setSettings({ ...settings, ...JSON.parse(stored) });
      }
    } catch (error) {
      console.error('Failed to load print settings:', error);
    }
  };

  const saveSettings = (newSettings: PrintSettings) => {
    try {
      localStorage.setItem('print-settings', JSON.stringify(newSettings));
      setSettings(newSettings);
      toast.success('Print settings saved successfully');
    } catch (error) {
      console.error('Failed to save print settings:', error);
      toast.error('Failed to save print settings');
    }
  };

  const loadPrinters = async () => {
    try {
      const availablePrinters = await printExecutionService.refreshPrinters();
      setPrinters(availablePrinters);
    } catch (error) {
      console.error('Failed to load printers:', error);
      toast.error('Failed to load printers');
    }
  };

  const loadMetrics = () => {
    const currentMetrics = printStatusMonitor.getMetrics();
    setMetrics(currentMetrics);
  };

  const loadQueueStats = () => {
    const stats = printExecutionService.getQueueStatistics();
    setQueueStats(stats);
  };

  const testPrinter = async (printerName: string) => {
    setTestingPrinter(printerName);
    
    try {
      const result = await printExecutionService.testPrinter(printerName);
      
      if (result.success) {
        toast.success(`Printer "${printerName}" test successful`);
      } else {
        toast.error(`Printer "${printerName}" test failed: ${result.error}`);
      }
    } catch (error) {
      toast.error(`Printer test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setTestingPrinter(null);
    }
  };

  const clearFailedJobs = () => {
    const clearedCount = printExecutionService.clearFailedJobs();
    toast.success(`Cleared ${clearedCount} failed print jobs`);
    loadQueueStats();
  };

  const clearNotifications = () => {
    printStatusMonitor.clearNotifications();
    toast.success('All notifications cleared');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getEnvironmentIcon = () => {
    return isElectron ? <Monitor className="h-4 w-4" /> : <Smartphone className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Environment Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getEnvironmentIcon()}
            Print System Status
          </CardTitle>
          <CardDescription>
            Current environment and connectivity status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <Badge variant={isElectron ? "default" : "secondary"}>
                {isElectron ? "Desktop" : "Browser"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              {queueStats?.network?.online ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm">
                {queueStats?.network?.online ? "Online" : "Offline"}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">
                {printers.length} Printer{printers.length !== 1 ? 's' : ''}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">
                {queueStats?.active?.pending || 0} Queued
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="printers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="printers">Printers</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        {/* Printers Tab */}
        <TabsContent value="printers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Printers</CardTitle>
              <CardDescription>
                Manage and test your printers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button onClick={loadPrinters} variant="outline" size="sm">
                  Refresh Printers
                </Button>
                
                {printers.length === 0 ? (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      No printers found. Make sure your printers are connected and powered on.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid gap-4">
                    {printers.map((printer) => (
                      <div key={printer.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(printer.status)}
                          <div>
                            <div className="font-medium">{printer.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {printer.type} • {printer.isElectronPrinter ? 'System' : 'Virtual'}
                              {printer.isDefault && ' • Default'}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={printer.status === 'online' ? 'default' : 'secondary'}>
                            {printer.status}
                          </Badge>
                          <Button
                            onClick={() => testPrinter(printer.name)}
                            disabled={testingPrinter === printer.name}
                            variant="outline"
                            size="sm"
                          >
                            {testingPrinter === printer.name ? (
                              <>Testing...</>
                            ) : (
                              <>
                                <TestTube className="h-4 w-4 mr-1" />
                                Test
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Print Configuration</CardTitle>
              <CardDescription>
                Configure automatic printing and default settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Automatic Printing */}
              <div className="space-y-4">
                <h4 className="font-medium">Automatic Printing</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="silent-printing">Silent Printing (Electron only)</Label>
                    <Switch
                      id="silent-printing"
                      checked={settings.silentPrinting}
                      onCheckedChange={(checked) => 
                        saveSettings({ ...settings, silentPrinting: checked })
                      }
                      disabled={!isElectron}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-kitchen">Auto Kitchen Print</Label>
                    <Switch
                      id="auto-kitchen"
                      checked={settings.autoKitchenPrint}
                      onCheckedChange={(checked) => 
                        saveSettings({ ...settings, autoKitchenPrint: checked })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-receipt">Auto Receipt Print</Label>
                    <Switch
                      id="auto-receipt"
                      checked={settings.autoReceiptPrint}
                      onCheckedChange={(checked) => 
                        saveSettings({ ...settings, autoReceiptPrint: checked })
                      }
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Default Printers */}
              <div className="space-y-4">
                <h4 className="font-medium">Default Printers</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Kitchen Printer</Label>
                    <Select
                      value={settings.defaultKitchenPrinter}
                      onValueChange={(value) => 
                        saveSettings({ ...settings, defaultKitchenPrinter: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select kitchen printer" />
                      </SelectTrigger>
                      <SelectContent>
                        {printers.map((printer) => (
                          <SelectItem key={printer.id} value={printer.name}>
                            {printer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Receipt Printer</Label>
                    <Select
                      value={settings.defaultReceiptPrinter}
                      onValueChange={(value) => 
                        saveSettings({ ...settings, defaultReceiptPrinter: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select receipt printer" />
                      </SelectTrigger>
                      <SelectContent>
                        {printers.map((printer) => (
                          <SelectItem key={printer.id} value={printer.name}>
                            {printer.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Quality & Copies */}
              <div className="space-y-4">
                <h4 className="font-medium">Print Quality & Options</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Print Quality</Label>
                    <Select
                      value={settings.printQuality}
                      onValueChange={(value: 'draft' | 'normal' | 'high') => 
                        saveSettings({ ...settings, printQuality: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Default Copies</Label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={settings.copies}
                      onChange={(e) => 
                        saveSettings({ ...settings, copies: parseInt(e.target.value) || 1 })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Retry Attempts</Label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={settings.retryAttempts}
                      onChange={(e) => 
                        saveSettings({ ...settings, retryAttempts: parseInt(e.target.value) || 3 })
                      }
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Print Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Print Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                {metrics ? (
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Total Jobs:</span>
                      <span className="font-medium">{metrics.totalJobs}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Success Rate:</span>
                      <span className="font-medium">{metrics.successRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg. Time:</span>
                      <span className="font-medium">{metrics.averageExecutionTime.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Failed Jobs:</span>
                      <span className="font-medium text-red-500">{metrics.failedJobs}</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">Loading statistics...</div>
                )}
              </CardContent>
            </Card>

            {/* Queue Status */}
            <Card>
              <CardHeader>
                <CardTitle>Queue Status</CardTitle>
              </CardHeader>
              <CardContent>
                {queueStats ? (
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Pending Jobs:</span>
                      <span className="font-medium">{queueStats.active.pending}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Offline Jobs:</span>
                      <span className="font-medium">{queueStats.offline.count}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Retries:</span>
                      <span className="font-medium">{queueStats.retries.active}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Processing:</span>
                      <Badge variant={queueStats.active.processing ? "default" : "secondary"}>
                        {queueStats.active.processing ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">Loading queue status...</div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Button onClick={clearFailedJobs} variant="outline" size="sm">
                  Clear Failed Jobs
                </Button>
                <Button onClick={clearNotifications} variant="outline" size="sm">
                  Clear Notifications
                </Button>
                <Button 
                  onClick={() => printExecutionService.forceProcessOfflineQueue()} 
                  variant="outline" 
                  size="sm"
                >
                  Process Offline Queue
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
