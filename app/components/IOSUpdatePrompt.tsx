'use client';

import { useIOSUpdater } from '@/app/hooks/useIOSUpdater';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertCircle, ExternalLink, Smartphone } from 'lucide-react';

export function IOSUpdatePrompt() {
  const {
    isIOS,
    updateAvailable,
    updateInfo,
    error,
    openAppStore,
  } = useIOSUpdater();

  // Don't render on non-iOS platforms
  if (!isIOS) return null;

  // Don't show if no update is available or if there's an error
  if (!updateAvailable || !updateInfo || error) return null;

  const isMandatory = updateInfo.mandatory || false;

  return (
    <Dialog open={updateAvailable} onOpenChange={() => !isMandatory}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5 text-blue-600" />
            App Update Available
          </DialogTitle>
          <DialogDescription>
            A new version ({updateInfo.version}) of the app is available on the App Store.
            {isMandatory && (
              <span className="block mt-2 text-amber-600 font-medium">
                This update is required to continue using the app.
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Release Notes */}
          {updateInfo.releaseNotes && (
            <div className="bg-gray-50 p-3 rounded-md">
              <h4 className="font-medium text-sm mb-2">What's New:</h4>
              <p className="text-sm text-gray-600">{updateInfo.releaseNotes}</p>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 text-red-700 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {isMandatory ? (
            // Mandatory update - only App Store option
            <Button
              onClick={openAppStore}
              className="w-full"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Open App Store
            </Button>
          ) : (
            // Optional update - App Store and later options
            <>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="w-full sm:w-auto"
              >
                Later
              </Button>
              <Button
                onClick={openAppStore}
                className="w-full sm:w-auto"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Update on App Store
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}