"use client"

import { useUpdateDialog, UpdateDialog } from './ui/update-dialog'

/**
 * UpdateManager component that handles the auto-update UI for the Electron app.
 * This component should be placed in the root layout to manage updates globally.
 */
export function UpdateManager() {
  const {
    isOpen,
    updateInfo,
    onClose,
    onInstallUpdate,
    onDismiss
  } = useUpdateDialog()

  // Only render in Electron environment
  if (typeof window === 'undefined' || !window.electronAPI) {
    return null
  }

  return (
    <UpdateDialog
      isOpen={isOpen}
      onClose={onClose}
      updateInfo={updateInfo}
      onInstallUpdate={onInstallUpdate}
      onDismiss={onDismiss}
    />
  )
}

export default UpdateManager