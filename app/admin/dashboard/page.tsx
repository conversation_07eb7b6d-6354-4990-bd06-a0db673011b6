"use client";

import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { UsersTable, type User } from "@/components/ui/users-table";
import { 
  Users, 
  Store, 
  UserCheck, 
  Activity,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { useStaticNavigation } from '@/lib/utils/navigation';

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalOwners: 0,
    totalStaff: 0,
    activeUsers: 0,
    restrictedUsers: 0
  });
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);
  const { toast } = useToast();
  const { navigate } = useStaticNavigation();

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    setLoading(true);
    try {
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const response = await fetch('/api/admin/users', {
        headers: { 'x-admin-email': adminEmail }
      });
      
      const data = await response.json();
      if (data.success) {
        const usersData = data.users || [];
        const owners = usersData.filter((u: any) => u.role === 'owner');
        const staff = usersData.filter((u: any) => u.role !== 'owner');
        const active = usersData.filter((u: any) => !u.restricted);
        const restricted = usersData.filter((u: any) => u.restricted);

        setUsers(usersData);
        setStats({
          totalUsers: usersData.length,
          totalOwners: owners.length,
          totalStaff: staff.length,
          activeUsers: active.length,
          restrictedUsers: restricted.length
        });
      }
    } catch (error) {
      console.error('Error loading stats:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load dashboard data",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleRestriction = async (user: User) => {
    // Only allow toggling restriction for owners
    if (user.role !== 'owner') {
      toast({
        title: "Cannot Restrict Staff",
        description: "Staff restrictions are inherited from their restaurant owner. Restrict the owner instead.",
        variant: "destructive",
      });
      return;
    }

    setUpdating(user._id);
    try {
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const response = await fetch(`/api/admin/users/${user._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-email': adminEmail
        },
        body: JSON.stringify({ restricted: !user.restricted })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to update user`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to update user');
      }

      // Update local state
      setUsers(users.map(u => 
        u._id === user._id ? { ...u, restricted: !u.restricted } : u
      ));

      // Update stats - need to recalculate based on owner restrictions affecting staff
      const updatedUsers = users.map(u => 
        u._id === user._id ? { ...u, restricted: !u.restricted } : u
      );
      
      // Calculate effective restrictions (staff inherit from owners)
      const owners = updatedUsers.filter(u => u.role === 'owner');
      const restrictedOwners = owners.filter(o => o.restricted);
      const restrictedRestaurantIds = new Set(restrictedOwners.map(o => o.restaurantId));
      
      const effectivelyRestricted = updatedUsers.filter(u => 
        u.restricted || (u.role !== 'owner' && restrictedRestaurantIds.has(u.restaurantId))
      );
      
      setStats(prev => ({
        ...prev,
        activeUsers: updatedUsers.length - effectivelyRestricted.length,
        restrictedUsers: effectivelyRestricted.length
      }));

      toast({
        title: `Owner ${!user.restricted ? 'Restricted' : 'Activated'}`,
        description: `${user.name} and their restaurant staff have been ${!user.restricted ? 'restricted' : 'activated'}.`,
        variant: !user.restricted ? "destructive" : "default",
      });
    } catch (error: any) {
      console.error('Error updating user:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || 'Failed to update user',
      });
    } finally {
      setUpdating(null);
    }
  };

  const handleDeleteUser = async (user: User) => {
    if (!confirm(`Are you sure you want to delete ${user.name}? This action cannot be undone.`)) {
      return;
    }

    setUpdating(user._id);
    try {
      const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
      const response = await fetch(`/api/admin/users/${user._id}`, {
        method: 'DELETE',
        headers: {
          'x-admin-email': adminEmail
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete user`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete user');
      }

      // Update local state
      const updatedUsers = users.filter(u => u._id !== user._id);
      setUsers(updatedUsers);

      // Update stats
      const owners = updatedUsers.filter(u => u.role === 'owner');
      const staff = updatedUsers.filter(u => u.role !== 'owner');
      const active = updatedUsers.filter(u => !u.restricted);
      const restricted = updatedUsers.filter(u => u.restricted);

      setStats({
        totalUsers: updatedUsers.length,
        totalOwners: owners.length,
        totalStaff: staff.length,
        activeUsers: active.length,
        restrictedUsers: restricted.length
      });

      toast({
        title: "User Deleted",
        description: `${user.name} has been deleted successfully.`,
      });
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || 'Failed to delete user',
      });
    } finally {
      setUpdating(null);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">Welcome to the Bistro admin panel</p>
        </div>
        <Button variant="outline" size="sm" onClick={loadStats} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? '...' : stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">All registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Owners</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? '...' : stats.totalOwners}</div>
            <p className="text-xs text-muted-foreground">Restaurant owners</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staff</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? '...' : stats.totalStaff}</div>
            <p className="text-xs text-muted-foreground">Staff members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            {stats.restrictedUsers === 0 ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-amber-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? '...' : stats.restrictedUsers === 0 ? 'Healthy' : 'Issues'}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.restrictedUsers} restricted accounts
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Status Overview */}
      <div className="grid gap-6 md:grid-cols-2 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              User Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span className="text-sm">Active Users</span>
              </div>
              <Badge className="bg-green-600">{loading ? '...' : stats.activeUsers}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full" />
                <span className="text-sm">Restricted Users</span>
              </div>
              <Badge variant="destructive">{loading ? '...' : stats.restrictedUsers}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Platform Info
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>System Uptime</span>
              <span className="font-medium">99.9%</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Environment</span>
              <span className="font-medium">{process.env.NODE_ENV || 'development'}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Last Updated</span>
              <span className="font-medium">{new Date().toLocaleDateString()}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <UsersTable 
            data={users}
            loading={loading}
            onToggleRestriction={handleToggleRestriction}
            onDeleteUser={handleDeleteUser}
          />
        </CardContent>
      </Card>
    </div>
  );
}