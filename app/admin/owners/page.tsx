"use client";

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { AlertCircle, CheckCircle, ArrowLeft, RefreshCw, Search } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';

// Helper to fetch all owners
async function fetchOwners() {
  const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
  const res = await fetch('/api/admin/users?role=owner', {
    headers: {
      'x-admin-email': adminEmail
    }
  });
  const data = await res.json();
  if (!data.success) throw new Error(data.error || 'Failed to fetch users');
  return data.users || [];
}

// Helper to update owner restriction
async function setOwnerRestriction(id: string, restricted: boolean) {
  const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>';
  const res = await fetch(`/api/admin/users/${id}`, {
    method: 'PUT',
    headers: { 
      'Content-Type': 'application/json',
      'x-admin-email': adminEmail
    },
    body: JSON.stringify({ restricted }),
  });
  return res.json();
}

export default function OwnersDashboard() {
  const [owners, setOwners] = useState<any[]>([]);
  const [filteredOwners, setFilteredOwners] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();
  const router = useRouter();
  const { navigate } = useStaticNavigation();

  // Load owners on initial render
  useEffect(() => {
    loadOwners();
  }, []);

  // Filter owners when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOwners(owners);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredOwners(
        owners.filter(
          owner => 
            owner.name?.toLowerCase().includes(query) || 
            owner.email?.toLowerCase().includes(query)
        )
      );
    }
  }, [searchQuery, owners]);

  const loadOwners = async () => {
    setLoading(true);
    setError(null);
    try {
      const ownersData = await fetchOwners();
      setOwners(ownersData);
      setFilteredOwners(ownersData);
    } catch (e: any) {
      console.error('Error loading owners:', e);
      setError(e.message || 'Failed to load owners');
      toast({
        variant: "destructive",
        title: "Erreur",
        description: e.message || 'Failed to load owners',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = async (id: string, restricted: boolean) => {
    setUpdating(id);
    setError(null);
    try {
      const result = await setOwnerRestriction(id, restricted);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update restriction status');
      }
      
      // Update local state
      setOwners(owners => owners.map(o => o._id === id ? { ...o, restricted } : o));
      
      // Show success message
      toast({
        title: `Accès ${restricted ? 'Restreint' : 'Activé'}`,
        description: `L'accès de l'owner a été ${restricted ? 'restreint' : 'activé'} avec succès.`,
        variant: restricted ? "destructive" : "default",
      });
    } catch (e: any) {
      console.error('Error updating restriction:', e);
      setError(e.message);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: e.message || 'Failed to update restriction status',
      });
    } finally {
      setUpdating(null);
    }
  };

  return (
    <div className="container mx-auto py-10 px-4 max-w-7xl">
      <div className="flex items-center mb-8">
        <Button 
          variant="ghost" 
          className="mr-4 p-0 h-auto" 
          onClick={() => navigate('admin/dashboard')}
        >
          <ArrowLeft className="h-5 w-5 mr-1" />
          <span>Retour</span>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Propriétaires de restaurants</h1>
          <p className="text-muted-foreground">
            Gérer l'accès pour les propriétaires de restaurants
          </p>
        </div>
      </div>

      <Card className="mb-8">
        <CardHeader className="pb-3">
          <CardTitle>Gestion des accès</CardTitle>
          <CardDescription>
            Autorisez ou restreignez l'accès à la plateforme pour les propriétaires de restaurants.
            Lorsqu'un propriétaire est restreint, tout le personnel de son restaurant l'est aussi.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full sm:w-96">
              <Search className="absolute left-2.5 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher par nom ou e-mail..."
                className="pl-9 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button 
              variant="outline" 
              className="w-full sm:w-auto"
              disabled={loading}
              onClick={loadOwners}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Rafraîchir
            </Button>
          </div>

          {error && (
            <div className="bg-red-50 text-red-800 p-4 rounded-md mb-6 flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Erreur lors du chargement des propriétaires</p>
                <p className="text-sm">{error}</p>
              </div>
            </div>
          )}

          {loading ? (
            <div className="py-8 text-center text-muted-foreground">
              <div className="animate-spin mb-4 mx-auto">
                <RefreshCw className="h-8 w-8" />
              </div>
              <p>Chargement des propriétaires...</p>
            </div>
          ) : filteredOwners.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              <p>Aucun propriétaire trouvé{searchQuery ? ' correspondant à votre recherche' : ''}.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nom</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead className="w-[100px] text-right">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOwners.map(owner => (
                    <TableRow key={owner._id}>
                      <TableCell className="font-medium">{owner.name}</TableCell>
                      <TableCell>{owner.email}</TableCell>
                      <TableCell>
                        {owner.restricted ? (
                          <Badge variant="destructive" className="flex items-center w-fit gap-1">
                            <AlertCircle className="h-3 w-3" />
                            Restreint
                          </Badge>
                        ) : (
                          <Badge variant="default" className="flex items-center w-fit gap-1 bg-green-600">
                            <CheckCircle className="h-3 w-3" />
                            Actif
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          size="sm"
                          variant={owner.restricted ? "default" : "destructive"}
                          className={updating === owner._id ? "opacity-50" : ""}
                          disabled={updating === owner._id}
                          onClick={() => handleToggle(owner._id, !owner.restricted)}
                        >
                          {owner.restricted ? 'Activer' : 'Restreindre'}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="border-amber-200 bg-amber-50">
        <CardHeader>
          <CardTitle className="text-amber-800 flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Notes importantes
          </CardTitle>
        </CardHeader>
        <CardContent className="text-amber-800">
          <ul className="list-disc pl-5 space-y-2">
            <li>
              Restreindre un propriétaire empêchera l'accès à toutes les fonctionnalités de la plateforme pour le propriétaire et tout le personnel de son restaurant.
            </li>
            <li>
              Ceci est généralement fait pour la gestion des abonnements ou la suspension de compte.
            </li>
            <li>
              Les données du restaurant restent intactes pendant la restriction et peuvent être entièrement restaurées en réactivant l'accès.
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
} 