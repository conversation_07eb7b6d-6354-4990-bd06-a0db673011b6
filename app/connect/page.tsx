"use client";

import { useState, useEffect } from "react";
import { QRCodeSVG } from "qrcode.react";
import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Network, Smartphone, Database, Server } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useP2PSync } from "@/hooks/use-p2p-sync";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { PeerInfo } from "@/types/p2p-sync";
import { useRouter } from 'next/navigation';
import { getCurrentRestaurantId } from "@/lib/db/v4/utils/restaurant-id";

// Define an interface that matches the actual peer data structure we're receiving
interface RuntimePeerInfo {
  id: string;
  ip: string;
  port: number;
  hostname: string;
  platform?: 'desktop' | 'mobile' | 'unknown';
}

export default function ConnectPage() {
  const router = useRouter();
  const { isElectron, peers, mdnsStatus, logs } = useP2PSync();
  const [serviceInfo, setServiceInfo] = useState<any>(null);
  const [serverPort, setServerPort] = useState<number | null>(null);
  const [deviceId, setDeviceId] = useState<string | null>(null);
  const [connectionUrl, setConnectionUrl] = useState<string>("");
  const [ipAddress, setIpAddress] = useState<string>("");

  useEffect(() => {
    // Get the electron API
    if (
      isElectron &&
      typeof window !== "undefined" &&
      typeof window.electronAPI !== "undefined" &&
      typeof window.electronAPI.p2pSync !== "undefined"
    ) {
      // Fetch the service info, server port and device ID
      const fetchServiceInfo = async () => {
        try {
          // Use type assertion since we know these methods exist
          const api = window.electronAPI?.p2pSync as any;
          const info = await api.getServiceInfo();
          const port = await api.getServerPort();
          const id = await api.getSystemId();
          
          setServiceInfo(info);
          setServerPort(port);
          setDeviceId(id);
          setIpAddress(info?.ip || "");
          
          // Create connection URL with all necessary info for mobile device
          const restaurantId = getCurrentRestaurantId();
          if (info?.ip && port && id && restaurantId) {
            const connectionData = {
              ip: info.ip,
              port: port,
              deviceId: id,
              hostname: info.name || "desktop-host",
              restaurantId: restaurantId
            };
            setConnectionUrl(JSON.stringify(connectionData));
          }
        } catch (error) {
          console.error("Error fetching service info:", error);
        }
      };
      
      fetchServiceInfo();
      
      // Refresh every 10 seconds to keep the IP address current
      const interval = setInterval(fetchServiceInfo, 10000);
      return () => clearInterval(interval);
    }
  }, [isElectron]);

  // Format connection instructions
  const getConnectionInstructions = () => {
    if (!ipAddress || !serverPort) {
      return "Loading connection information...";
    }
    
    return `http://${ipAddress}:${serverPort}`;
  };

  // Helper function to get the platform icon
  const getPlatformIcon = (peer: RuntimePeerInfo) => {
    if (peer.platform === 'desktop') return '🖥️';
    if (peer.platform === 'mobile') return '📱';
    return '🔄'; // Default for unknown platform
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="mb-4">
        <button
          className="inline-flex items-center px-4 py-2 rounded bg-slate-200 hover:bg-slate-300 text-slate-800 text-sm font-medium"
          onClick={() => router.back()}
        >
          ← Go Back
        </button>
      </div>
      <PageHeader
        heading="Connect Devices"
        description="Connect mobile devices to your desktop PouchDB server"
        icon={<Network className="h-6 w-6" />}
      />
      
      <Tabs defaultValue="qrcode" className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="qrcode" className="flex items-center gap-2">
            <Smartphone className="h-4 w-4" />
            <span>Mobile Connection</span>
          </TabsTrigger>
          <TabsTrigger value="info" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <span>Connection Info</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="qrcode" className="mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Sync QR Code
                </CardTitle>
                <CardDescription>
                  Scan this QR code from your mobile device to connect to this server
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                {connectionUrl ? (
                  <div className="p-4 bg-white rounded-lg">
                    <QRCodeSVG
                      value={connectionUrl}
                      size={250}
                      level="H"
                      includeMargin={true}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 w-64 bg-slate-100 rounded-lg">
                    <p className="text-slate-500">Loading QR code...</p>
                  </div>
                )}
                
                <div className="mt-4 text-center">
                  <p className="text-sm text-muted-foreground">
                    This QR code contains connection information for your PouchDB server
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Connection Status</CardTitle>
                <CardDescription>
                  PouchDB server and mDNS service status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert variant={mdnsStatus === 'running' ? 'default' : 'destructive'}>
                  <AlertTitle>
                    {mdnsStatus === 'running' 
                      ? '✅ mDNS Service Running' 
                      : '❌ mDNS Service Not Running'}
                  </AlertTitle>
                  <AlertDescription>
                    {mdnsStatus === 'running'
                      ? 'Your device is discoverable on the local network'
                      : 'Your device may not be discoverable on the network'}
                  </AlertDescription>
                </Alert>
                
                <div className="pt-2">
                  <h3 className="text-sm font-medium mb-2">Server Details</h3>
                  <div className="text-sm">
                    <p><strong>URL:</strong> {getConnectionInstructions()}</p>
                    <p><strong>Port:</strong> {serverPort || 'Unknown'}</p>
                    <p><strong>Device ID:</strong> {deviceId ? `${deviceId.substring(0, 8)}...` : 'Unknown'}</p>
                  </div>
                </div>
                
                <div className="pt-2">
                  <h3 className="text-sm font-medium mb-2">Connected Peers: {peers.length}</h3>
                  {peers.length > 0 ? (
                    <ul className="text-sm space-y-1">
                      {peers.map((peer, index) => {
                        const runtimePeer = peer as unknown as RuntimePeerInfo;
                        return (
                          <li key={index} className="flex items-center text-sm">
                            {getPlatformIcon(runtimePeer)} {runtimePeer.hostname} ({runtimePeer.ip})
                          </li>
                        );
                      })}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No devices connected</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="info" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Technical Details</CardTitle>
              <CardDescription>
                Detailed information about your PouchDB server
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Connection URL</h3>
                  <div className="p-3 bg-slate-100 rounded-md flex items-center overflow-auto">
                    <code className="text-xs md:text-sm">{getConnectionInstructions()}</code>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="ml-auto"
                      onClick={() => navigator.clipboard.writeText(getConnectionInstructions())}
                    >
                      Copy
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    This is the URL your mobile device will use to connect to the PouchDB server
                  </p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Service Information</h3>
                  <div className="border rounded-md">
                    <div className="bg-slate-50 p-3 border-b">
                      <pre className="text-xs overflow-auto">
                        {JSON.stringify(serviceInfo, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Recent Logs</h3>
                  <div className="border rounded-md h-48 overflow-auto">
                    <ul className="divide-y">
                      {logs.slice(0, 10).map((log, index) => (
                        <li key={index} className="p-2 text-xs font-mono">
                          {log}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 