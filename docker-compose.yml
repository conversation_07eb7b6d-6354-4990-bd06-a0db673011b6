version: '3.8'

services:
  nextjs-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_COUCHDB_URL=${NEXT_PUBLIC_COUCHDB_URL}
        - NEXT_PUBLIC_COUCHDB_USER=${NEXT_PUBLIC_COUCHDB_USER}
        - NEXT_PUBLIC_COUCHDB_PASSWORD=${NEXT_PUBLIC_COUCHDB_PASSWORD}
        - NEXT_PUBLIC_COUCHDB_DB_NAME=${NEXT_PUBLIC_COUCHDB_DB_NAME}
    container_name: bistro-app
    restart: unless-stopped
    ports:
      - "3010:3010"
    environment:
      - NEXT_PUBLIC_MONGO_URL=${NEXT_PUBLIC_MONGO_URL}
      - NEXT_PUBLIC_MONGO_USER=${NEXT_PUBLIC_MONGO_USER}
      - NEXT_PUBLIC_MONGO_PASSWORD=${NEXT_PUBLIC_MONGO_PASSWORD}
      - NEXT_PUBLIC_MONGO_DB_NAME=${NEXT_PUBLIC_MONGO_DB_NAME}
      - NODE_ENV=production
      - PORT=3010
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - MONGODB_URI=${MONGODB_URI}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:3010"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network
      - coolify

networks:
  app-network:
    driver: bridge
  coolify:
    external: true