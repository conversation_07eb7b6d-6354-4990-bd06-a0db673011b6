/**
 * Hook for Autonomous Sync Management
 * 
 * Provides easy access to the autonomous sync system with:
 * - Auto-initialization on mount
 * - Real-time status updates
 * - Control functions
 */

import { useState, useEffect, useCallback } from 'react';
import { autonomousSyncManager, type AutonomousConfig, type AutonomousStatus } from '@/lib/services/autonomous-sync-manager';

interface UseAutonomousSyncReturn {
  status: AutonomousStatus;
  isRunning: boolean;
  isConnected: boolean;
  isSyncing: boolean;
  discoveredServers: number;
  connectedServers: number;
  lastDiscovery: Date | null;
  error: string | null;
  
  // Control functions
  start: () => Promise<void>;
  stop: () => Promise<void>;
  discover: () => Promise<void>;
  updateConfig: (config: Partial<AutonomousConfig>) => void;
}

export function useAutonomousSync(config?: Partial<AutonomousConfig>): UseAutonomousSyncReturn {
  const [status, setStatus] = useState<AutonomousStatus>(autonomousSyncManager.getStatus());
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize autonomous sync manager
  useEffect(() => {
    const initializeManager = async () => {
      if (!isInitialized) {
        console.log('🪝 [useAutonomousSync] Initializing autonomous sync manager...');
        await autonomousSyncManager.initialize(config);
        setIsInitialized(true);
      }
    };

    initializeManager();
  }, [config, isInitialized]);

  // Subscribe to status updates
  useEffect(() => {
    const unsubscribe = autonomousSyncManager.onStatusChange((newStatus) => {
      setStatus(newStatus);
    });

    // Get initial status
    setStatus(autonomousSyncManager.getStatus());

    return unsubscribe;
  }, []);

  // Control functions
  const start = useCallback(async () => {
    try {
      await autonomousSyncManager.start();
    } catch (error) {
      console.error('🪝 [useAutonomousSync] Failed to start:', error);
    }
  }, []);

  const stop = useCallback(async () => {
    try {
      await autonomousSyncManager.stop();
    } catch (error) {
      console.error('🪝 [useAutonomousSync] Failed to stop:', error);
    }
  }, []);

  const discover = useCallback(async () => {
    try {
      await autonomousSyncManager.discover();
    } catch (error) {
      console.error('🪝 [useAutonomousSync] Discovery failed:', error);
    }
  }, []);

  const updateConfig = useCallback((newConfig: Partial<AutonomousConfig>) => {
    autonomousSyncManager.updateConfig(newConfig);
  }, []);

  return {
    status,
    isRunning: status.isRunning,
    isConnected: status.syncStatus.connected,
    isSyncing: status.syncStatus.syncing,
    discoveredServers: status.discoveredServers.length,
    connectedServers: status.connectedServers.length,
    lastDiscovery: status.lastDiscovery,
    error: status.syncStatus.error || null,
    
    start,
    stop,
    discover,
    updateConfig
  };
}