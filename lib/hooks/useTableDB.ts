"use client";

import { useState, useEffect } from 'react';
import { getAllTables, createTable, updateTable, deleteTable, Table } from '../db/v4';
import { useSyncService } from './useSyncService';
import type { SyncState } from './useSyncService';

interface UseTableDBResult {
  tables: Table[];
  isLoading: boolean;
  error: Error | null;
  syncState: SyncState;
  isReady: boolean;
  refreshTables: () => Promise<void>;
  addTable: (table: Omit<Table, 'id'>) => Promise<Table>;
  updateTable: (tableId: string, tableUpdates: Partial<Table>) => Promise<Table>;
  deleteTable: (tableId: string) => Promise<void>;
}

/**
 * Hook to use TableDB with sync service in components
 */
export function useTableDB(): UseTableDBResult {
  const { syncState, isReady } = useSyncService(); // No parameter needed
  const [tables, setTables] = useState<Table[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Load tables when sync service is ready
  useEffect(() => {
    if (isReady) {
      refreshTables();
    }
  }, [isReady]);

  // Refresh tables
  const refreshTables = async () => {
    if (!isReady) return;
    setIsLoading(true);
    try {
      const data = await getAllTables();
      setTables(data);
      setError(null);
    } catch (err) {
      console.error('❌ Error loading tables:', err);
      setError(err instanceof Error ? err : new Error('Failed to load tables'));
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new table
  const addTable = async (table: Omit<Table, 'id'>): Promise<Table> => {
    try {
      const newTable = await createTable(table);
      await refreshTables();
      return newTable;
    } catch (err) {
      console.error('❌ Error adding table:', err);
      const error = err instanceof Error ? err : new Error('Failed to add table');
      setError(error);
      throw error;
    }
  };

  // Update an existing table
  const updateTableFn = async (tableId: string, tableUpdates: Partial<Table>): Promise<Table> => {
    try {
      const updatedTable = await updateTable(tableId, tableUpdates);
      await refreshTables();
      return updatedTable;
    } catch (err) {
      console.error('❌ Error updating table:', err);
      const error = err instanceof Error ? err : new Error('Failed to update table');
      setError(error);
      throw error;
    }
  };

  // Delete a table
  const deleteTableFn = async (tableId: string): Promise<void> => {
    try {
      await deleteTable(tableId);
      await refreshTables();
    } catch (err) {
      console.error('❌ Error deleting table:', err);
      const error = err instanceof Error ? err : new Error('Failed to delete table');
      setError(error);
      throw error;
    }
  };

  return {
    tables,
    isLoading,
    error,
    syncState,
    isReady,
    refreshTables,
    addTable,
    updateTable: updateTableFn,
    deleteTable: deleteTableFn
  };
} 