'use client';

import { useState, useEffect, useCallback } from 'react';
import { MenuServiceV4, Category, MenuItem } from '@/lib/db/v4-menu-service';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getMenu,
  addCategory,
  updateCategory,
  deleteCategory,
  addMenuItem,
  updateMenuItem,
  deleteMenuItem,
  addSizeToCategory,
  renameSizeInCategory,
  deleteSizeFromCategory,
  repairMenuSizes
} from '@/lib/db/v4/operations/menu-ops';

interface UseMenuReturn {
  categories: Category[];
  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
  refreshCategories: () => Promise<void>;
  addCategory: (category: Omit<Category, 'createdAt' | 'updatedAt'>) => Promise<Category>;
  updateCategory: (id: string, data: Partial<Category>) => Promise<Category>;
  deleteCategory: (id: string) => Promise<void>;
  addMenuItem: (categoryId: string, item: Partial<MenuItem>) => Promise<MenuItem>;
  updateMenuItem: (categoryId: string, itemId: string, data: Partial<MenuItem>) => Promise<MenuItem>;
  deleteMenuItem: (categoryId: string, itemId: string) => Promise<void>;
  addSizeToCategory: (categoryId: string, size: string) => Promise<string[]>;
  renameSizeInCategory: (categoryId: string, oldSize: string, newSize: string) => Promise<Category>;
  deleteSizeFromCategory: (categoryId: string, size: string) => Promise<string[]>;
  fixCategoriesStructure: () => Promise<void>;
  repairMenuSizes: () => Promise<void>;
}

/**
 * useMenuV4 hook - provides menu functionality using the v4 database
 */
export function useMenuV4(): UseMenuReturn {
  const { isAuthenticated, user } = useAuth();
  const { isDbInitialized, isLoadingDb, dbInitializeError, currentDbRestaurantId } = useUnifiedDB();

  // MenuServiceV4 might rely on databaseV4.isInitialized and dbIdentifier being set by DatabaseProvider
  const [menuService] = useState<MenuServiceV4>(new MenuServiceV4(null)); 
  const [categories, setCategories] = useState<Category[]>([]);
  // isLoading and error for this hook's specific operations (loading categories, etc.)
  const [isMenuDataLoading, setIsMenuDataLoading] = useState(true);
  const [menuHookError, setMenuHookError] = useState<Error | null>(null);
  // isReady for this hook, indicating it has successfully performed its initial setup (e.g., getMenu, loadCategories)
  const [isMenuHookReady, setIsMenuHookReady] = useState(false);

  // Load categories - defined before useEffect that might call it indirectly
  const loadCategories = useCallback(async () => {
    if (!isDbInitialized) { // Guard against running if DB not ready
      console.warn('[useMenuV4.loadCategories] Skipped: DB not initialized.');
      setIsMenuHookReady(false); // Can't be ready if we can't load categories
      return;
    }
    try {
      setIsMenuDataLoading(true);

      // 🚨 CRITICAL FIX: Add retry mechanism for menu loading
      let retryCount = 0;
      const maxRetries = 3;
      let data = null;

      while (!data && retryCount < maxRetries) {
        try {
          data = await menuService.getCategories();
          if (data && data.length >= 0) { // Accept empty array as valid
            console.log(`[useMenuV4.loadCategories] Successfully loaded ${data.length} categories`);
            break;
          }
        } catch (retryError) {
          retryCount++;
          console.warn(`[useMenuV4.loadCategories] Retry ${retryCount}/${maxRetries} failed:`, retryError);
          if (retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
          }
        }
      }

      if (data) {
        setCategories(data);
        setMenuHookError(null);
      } else {
        throw new Error('Failed to load categories after retries');
      }
    } catch (err) {
      console.error('[useMenuV4.loadCategories] Error loading categories:', err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      setIsMenuHookReady(false);
    } finally {
      setIsMenuDataLoading(false);
    }
  }, [menuService, isDbInitialized]); 

  // Initialize hook-specific data (like initial menu doc and categories) when DB is ready
  useEffect(() => {
    if (!isDbInitialized || !isAuthenticated) {
      setIsMenuHookReady(false);
      setCategories([]);
      setIsMenuDataLoading(true); // Reset loading state
      if (dbInitializeError && !menuHookError) { // Prioritize hook error if already set
        setMenuHookError(new Error(`Database initialization failed: ${dbInitializeError.message}`));
      }
      return;
    }

    // If hook is already ready, no need to re-initialize.
    if (isMenuHookReady) return;

    const initializeMenuHook = async () => {
      console.log(`[useMenuV4] DB initialized for restaurant: ${currentDbRestaurantId}. Initializing Menu hook.`);
      setIsMenuDataLoading(true);
      setMenuHookError(null);
      let menuInitialized = false;
      try {
        // The original logic had retries for getMenu. This is important if the menu doc might not exist immediately.
        let retryCount = 0;
        const maxRetries = 3;
        while (!menuInitialized && retryCount < maxRetries) {
          try {
            console.log(`[useMenuV4] Attempting getMenu (retry: ${retryCount}).`);
            const menu = await getMenu(); // This should use the globally initialized DB
            if (menu) {
              console.log('[useMenuV4] getMenu successful.');
              menuInitialized = true;
            } else {
              console.warn('[useMenuV4] getMenu returned falsy, retrying...');
              retryCount++;
              if (retryCount < maxRetries) await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } catch (getMenuError) {
            console.error('[useMenuV4] Error during getMenu attempt:', getMenuError);
            retryCount++;
            if (retryCount >= maxRetries) {
              throw getMenuError; // Throw to be caught by outer catch
            }
            if (retryCount < maxRetries) await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        if (!menuInitialized) {
          throw new Error('Failed to get/initialize menu document after retries');
        }

        console.log('[useMenuV4] Proceeding to loadCategories.');
        await loadCategories(); // loadCategories sets its own loading/ready states
        // Only set hook ready if loadCategories also succeeded (it sets menuHookError if it fails)
        if (!menuHookError) {
            setIsMenuHookReady(true);
        }
        
      } catch (err) {
        console.error('[useMenuV4] Error during menu hook initialization:', err);
        setMenuHookError(err instanceof Error ? err : new Error(String(err)));
        setIsMenuHookReady(false);
      } finally {
        setIsMenuDataLoading(false);
      }
    };

    initializeMenuHook();
  }, [isDbInitialized, isAuthenticated, currentDbRestaurantId, loadCategories, menuHookError, isMenuHookReady, dbInitializeError]);

  // Refresh categories - this one uses loadCategories
  const refreshCategories = useCallback(async () => {
    await loadCategories();
  }, [loadCategories]);

  // Add category
  const addCategory = useCallback(async (category: Omit<Category, 'createdAt' | 'updatedAt'>) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready. DB might not be initialized or initial menu load failed.');
    try {
      const newCategory = await menuService.addCategory(category);
      await refreshCategories();
      return newCategory;
    } catch (err) {
      console.error('Error adding category:', err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Update category
  const updateCategory = useCallback(async (id: string, data: Partial<Category>) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      const updatedCategory = await menuService.updateCategory(id, data);
      await refreshCategories();
      return updatedCategory;
    } catch (err) {
      console.error(`Error updating category ${id}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Delete category
  const deleteCategory = useCallback(async (id: string) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      await menuService.deleteCategory(id);
      await refreshCategories();
    } catch (err) {
      console.error(`Error deleting category ${id}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Add menu item
  const addMenuItem = useCallback(async (categoryId: string, item: Partial<MenuItem>) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      const newItem = await menuService.addMenuItem(categoryId, item);
      await refreshCategories();
      return newItem;
    } catch (err) {
      console.error(`Error adding item to category ${categoryId}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Update menu item
  const updateMenuItem = useCallback(async (categoryId: string, itemId: string, data: Partial<MenuItem>) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      const updatedItem = await menuService.updateMenuItem(categoryId, itemId, data);
      await refreshCategories();
      return updatedItem;
    } catch (err) {
      console.error(`Error updating item ${itemId} in category ${categoryId}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Delete menu item
  const deleteMenuItem = useCallback(async (categoryId: string, itemId: string) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      await menuService.deleteMenuItem(categoryId, itemId);
      await refreshCategories();
    } catch (err) {
      console.error(`Error deleting item ${itemId} from category ${categoryId}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Add size to category
  const addSizeToCategory = useCallback(async (categoryId: string, size: string) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      const sizes = await menuService.addSizeToCategory(categoryId, size);
      await refreshCategories();
      return sizes;
    } catch (err) {
      console.error(`Error adding size to category ${categoryId}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Rename size in category
  const renameSizeInCategory = useCallback(async (categoryId: string, oldSize: string, newSize: string) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      const category = await menuService.renameSizeInCategory(categoryId, oldSize, newSize);
      await refreshCategories();
      return category;
    } catch (err) {
      console.error(`Error renaming size in category ${categoryId}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Delete size from category
  const deleteSizeFromCategory = useCallback(async (categoryId: string, size: string) => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    try {
      const sizes = await menuService.deleteSizeFromCategory(categoryId, size);
      await refreshCategories();
      return sizes;
    } catch (err) {
      console.error(`Error deleting size from category ${categoryId}:`, err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Fix categories structure
  const fixCategoriesStructure = useCallback(async () => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    setIsMenuDataLoading(true);
    try {
      await menuService.fixCategoriesStructure();
      await refreshCategories();
    } catch (err) {
      console.error('Error fixing categories structure:', err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setIsMenuDataLoading(false);
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  // Repair menu sizes
  const repairMenuSizes = useCallback(async () => {
    if (!isMenuHookReady) throw new Error('Menu hook not ready.');
    setIsMenuDataLoading(true);
    try {
      await menuService.repairMenuSizes();
      await refreshCategories();
    } catch (err) {
      console.error('Error repairing menu sizes:', err);
      setMenuHookError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setIsMenuDataLoading(false);
    }
  }, [menuService, refreshCategories, isMenuHookReady]);

  return {
    categories,
    isLoading: isLoadingDb || isMenuDataLoading,
    error: dbInitializeError || menuHookError,
    isReady: isDbInitialized && isMenuHookReady && !isMenuDataLoading,
    refreshCategories,
    addCategory,
    updateCategory,
    deleteCategory,
    addMenuItem,
    updateMenuItem,
    deleteMenuItem,
    addSizeToCategory,
    renameSizeInCategory,
    deleteSizeFromCategory,
    fixCategoriesStructure,
    repairMenuSizes
  };
}