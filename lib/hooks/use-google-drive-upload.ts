"use client";

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/lib/context/multi-user-auth-provider';

interface UploadOptions {
  transactionId?: string;
  fileType?: 'receipt' | 'product' | 'general';
  maxSize?: number; // in MB
  allowedTypes?: string[];
}

interface UploadResult {
  success: boolean;
  fileId?: string;
  webViewLink?: string;
  downloadUrl?: string;
  error?: string;
}

interface UploadProgress {
  isUploading: boolean;
  progress: number;
  fileName?: string;
}

export function useGoogleDriveUpload() {
  const { toast } = useToast();
  const { restaurantId } = useAuth();
  
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    isUploading: false,
    progress: 0
  });

  // Auto-initialize Google Drive service when restaurantId is available
  useEffect(() => {
    if (restaurantId) {
      console.log('🔧 Auto-initializing Google Drive service for restaurant:', restaurantId);
      
      // Call the API to initialize Google Drive service
      fetch('/api/google-drive/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ restaurantId }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          console.log('✅ Google Drive service auto-initialized successfully');
        } else {
          console.log('📭 No Google Drive configuration found - user needs to set it up');
        }
      })
      .catch(error => {
        console.warn('⚠️ Failed to auto-initialize Google Drive service:', error);
      });
    }
  }, [restaurantId]);

  const uploadFile = useCallback(async (
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> => {
    if (!restaurantId) {
      const error = 'Restaurant ID not available';
      toast({
        title: "❌ Upload Failed",
        description: error,
        variant: "destructive"
      });
      return { success: false, error };
    }

    // Validate file size
    const maxSize = options.maxSize || 10; // Default 10MB
    if (file.size > maxSize * 1024 * 1024) {
      const error = `File size must be less than ${maxSize}MB`;
      toast({
        title: "❌ File Too Large",
        description: error,
        variant: "destructive"
      });
      return { success: false, error };
    }

    // Validate file type
    const allowedTypes = options.allowedTypes || [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/webp',
      'image/gif'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      const error = 'File type not supported. Please upload an image file.';
      toast({
        title: "❌ Invalid File Type",
        description: error,
        variant: "destructive"
      });
      return { success: false, error };
    }

    setUploadProgress({
      isUploading: true,
      progress: 0,
      fileName: file.name
    });

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('restaurantId', restaurantId);
      
      if (options.transactionId) {
        formData.append('transactionId', options.transactionId);
      }
      
      if (options.fileType) {
        formData.append('fileType', options.fileType);
      }

      // Start upload
      setUploadProgress(prev => ({ ...prev, progress: 25 }));

      const response = await fetch('/api/google-drive/upload', {
        method: 'POST',
        body: formData
      });

      setUploadProgress(prev => ({ ...prev, progress: 75 }));

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      // Get download URL
      let downloadUrl: string | undefined;
      if (data.fileId) {
        try {
          const downloadResponse = await fetch(`/api/google-drive/download?fileId=${data.fileId}`);
          const downloadData = await downloadResponse.json();
          if (downloadData.success) {
            downloadUrl = downloadData.url;
          }
        } catch (error) {
          console.warn('Failed to get download URL:', error);
        }
      }

      setUploadProgress(prev => ({ ...prev, progress: 100 }));

      toast({
        title: "✅ Upload Successful",
        description: `${file.name} uploaded to Google Drive`,
      });

      const result = {
        success: true,
        fileId: data.fileId,
        webViewLink: data.webViewLink,
        downloadUrl
      };

      // Reset progress after a short delay
      setTimeout(() => {
        setUploadProgress({
          isUploading: false,
          progress: 0
        });
      }, 1000);

      return result;

    } catch (error) {
      console.error('Upload error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      toast({
        title: "❌ Upload Failed",
        description: errorMessage,
        variant: "destructive"
      });

      setUploadProgress({
        isUploading: false,
        progress: 0
      });

      return { 
        success: false, 
        error: errorMessage 
      };
    }
  }, [restaurantId, toast]);

  const uploadMultipleFiles = useCallback(async (
    files: File[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> => {
    const results: UploadResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      setUploadProgress({
        isUploading: true,
        progress: (i / files.length) * 100,
        fileName: file.name
      });

      const result = await uploadFile(file, options);
      results.push(result);

      // Small delay between uploads to avoid overwhelming the API
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setUploadProgress({
      isUploading: false,
      progress: 0
    });

    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    if (successCount > 0 && failCount === 0) {
      toast({
        title: "✅ All Uploads Successful",
        description: `${successCount} files uploaded successfully`,
      });
    } else if (successCount > 0 && failCount > 0) {
      toast({
        title: "⚠️ Partial Upload Success",
        description: `${successCount} succeeded, ${failCount} failed`,
        variant: "destructive"
      });
    } else {
      toast({
        title: "❌ All Uploads Failed",
        description: `${failCount} files failed to upload`,
        variant: "destructive"
      });
    }

    return results;
  }, [uploadFile, toast]);

  const getDownloadUrl = useCallback(async (fileId: string): Promise<string | null> => {
    try {
      const response = await fetch(`/api/google-drive/download?fileId=${fileId}`);
      const data = await response.json();
      
      if (data.success) {
        return data.url;
      } else {
        console.error('Failed to get download URL:', data.error);
        return null;
      }
    } catch (error) {
      console.error('Error getting download URL:', error);
      return null;
    }
  }, []);

  const isGoogleDriveReady = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/google-drive/test');
      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('Google Drive readiness check failed:', error);
      return false;
    }
  }, []);

  return {
    uploadFile,
    uploadMultipleFiles,
    getDownloadUrl,
    isGoogleDriveReady,
    uploadProgress,
    isUploading: uploadProgress.isUploading
  };
} 