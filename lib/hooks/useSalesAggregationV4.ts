/**
 * 🚀 Sales Aggregation Hook V4
 * 
 * React hook that provides easy access to the sales aggregation system.
 * This hook integrates with order operations to automatically update aggregations
 * and provides fast access to pre-computed dashboard data.
 */

import { useState, useEffect, useCallback } from 'react';
import { OrderDocument } from '../db/v4/schemas/order-schema';
import {
  DailySalesAggregation,
  RealTimeDashboardAggregation,
  ItemPerformanceAggregation,
  SalesAggregationDocument
} from '../db/v4/schemas/sales-aggregation-schemas';
import {
  updateAggregationsForOrder,
  getDashboardData,
  getDailySalesData,
  getItemPerformanceData,
  getSalesDataRange
} from '../db/v4/operations/sales-aggregation-ops';

interface SalesAggregationState {
  dashboardData: RealTimeDashboardAggregation | null;
  dailyData: DailySalesAggregation | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

interface SalesAggregationHook {
  // State
  state: SalesAggregationState;
  
  // Dashboard data access
  getDashboard: () => Promise<RealTimeDashboardAggregation | null>;
  getDailyData: (date: string) => Promise<DailySalesAggregation | null>;
  getItemPerformance: (menuItemId: string) => Promise<ItemPerformanceAggregation | null>;
  getDateRangeData: (startDate: string, endDate: string) => Promise<DailySalesAggregation[]>;
  
  // Order integration
  processOrderChange: (
    order: OrderDocument, 
    operation: 'create' | 'update' | 'delete',
    previousOrder?: OrderDocument
  ) => Promise<void>;
  
  // Refresh functions
  refreshDashboard: () => Promise<void>;
  refreshDailyData: (date?: string) => Promise<void>;
  
  // Utility functions
  isDataStale: (maxAgeMinutes?: number) => boolean;
  getPerformanceMetrics: () => {
    avgLoadTime: number;
    cacheHitRate: number;
    lastRefresh: string | null;
  };
}

export function useSalesAggregationV4(): SalesAggregationHook {
  const [state, setState] = useState<SalesAggregationState>({
    dashboardData: null,
    dailyData: null,
    isLoading: false,
    error: null,
    lastUpdated: null
  });
  
  // Performance tracking
  const [performanceMetrics, setPerformanceMetrics] = useState({
    avgLoadTime: 0,
    cacheHitRate: 0,
    lastRefresh: null as string | null,
    loadTimes: [] as number[]
  });

  /**
   * 🚀 Get dashboard data with performance tracking
   */
  const getDashboard = useCallback(async (): Promise<RealTimeDashboardAggregation | null> => {
    const startTime = performance.now();
    
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const dashboardData = await getDashboardData();
      const loadTime = performance.now() - startTime;
      
      // Update performance metrics
      setPerformanceMetrics(prev => {
        const newLoadTimes = [...prev.loadTimes, loadTime].slice(-10); // Keep last 10 measurements
        const avgLoadTime = newLoadTimes.reduce((sum, time) => sum + time, 0) / newLoadTimes.length;
        
        return {
          ...prev,
          avgLoadTime,
          loadTimes: newLoadTimes,
          lastRefresh: new Date().toISOString()
        };
      });
      
      setState(prev => ({
        ...prev,
        dashboardData,
        isLoading: false,
        lastUpdated: new Date().toISOString()
      }));
      
      console.log(`✅ [useSalesAggregationV4] Dashboard loaded in ${loadTime.toFixed(2)}ms`);
      return dashboardData;
      
    } catch (error) {
      console.error('❌ [useSalesAggregationV4] Failed to get dashboard data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load dashboard data'
      }));
      return null;
    }
  }, []);

  /**
   * Get daily sales data for a specific date
   */
  const getDailyDataForDate = useCallback(async (date: string): Promise<DailySalesAggregation | null> => {
    const startTime = performance.now();
    
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const dailyData = await getDailySalesData(date);
      const loadTime = performance.now() - startTime;
      
      setState(prev => ({
        ...prev,
        dailyData,
        isLoading: false,
        lastUpdated: new Date().toISOString()
      }));
      
      console.log(`✅ [useSalesAggregationV4] Daily data for ${date} loaded in ${loadTime.toFixed(2)}ms`);
      return dailyData;
      
    } catch (error) {
      console.error(`❌ [useSalesAggregationV4] Failed to get daily data for ${date}:`, error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load daily data'
      }));
      return null;
    }
  }, []);

  /**
   * Get item performance data
   */
  const getItemPerformanceForItem = useCallback(async (menuItemId: string): Promise<ItemPerformanceAggregation | null> => {
    try {
      const itemData = await getItemPerformanceData(menuItemId);
      console.log(`✅ [useSalesAggregationV4] Item performance for ${menuItemId} loaded`);
      return itemData;
    } catch (error) {
      console.error(`❌ [useSalesAggregationV4] Failed to get item performance for ${menuItemId}:`, error);
      return null;
    }
  }, []);

  /**
   * Get sales data for a date range
   */
  const getDateRangeData = useCallback(async (startDate: string, endDate: string): Promise<DailySalesAggregation[]> => {
    const startTime = performance.now();
    
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const rangeData = await getSalesDataRange(startDate, endDate);
      const loadTime = performance.now() - startTime;
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        lastUpdated: new Date().toISOString()
      }));
      
      console.log(`✅ [useSalesAggregationV4] Date range ${startDate} to ${endDate} loaded in ${loadTime.toFixed(2)}ms (${rangeData.length} days)`);
      return rangeData;
      
    } catch (error) {
      console.error(`❌ [useSalesAggregationV4] Failed to get date range data:`, error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load date range data'
      }));
      return [];
    }
  }, []);

  /**
   * 🔥 Process order changes and update aggregations
   * This is the key integration point with order operations
   */
  const processOrderChange = useCallback(async (
    order: OrderDocument,
    operation: 'create' | 'update' | 'delete',
    previousOrder?: OrderDocument
  ): Promise<void> => {
    const startTime = performance.now();
    
    try {
      console.log(`[useSalesAggregationV4] Processing ${operation} for order ${order._id}`);
      
      // Update all aggregations
      await updateAggregationsForOrder(order, operation, previousOrder);
      
      const processingTime = performance.now() - startTime;
      console.log(`✅ [useSalesAggregationV4] Order ${operation} processed in ${processingTime.toFixed(2)}ms`);
      
      // Refresh dashboard data if the order affects today's metrics
      const today = new Date().toISOString().split('T')[0];
      const orderDate = order.createdAt.split('T')[0];
      
      if (orderDate === today) {
        console.log('[useSalesAggregationV4] Order affects today\'s metrics, refreshing dashboard...');
        await getDashboard();
      }
      
    } catch (error) {
      console.error(`❌ [useSalesAggregationV4] Failed to process order ${operation}:`, error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to process order change'
      }));
      throw error; // Re-throw so calling code can handle it
    }
  }, [getDashboard]);

  /**
   * Refresh dashboard data
   */
  const refreshDashboard = useCallback(async (): Promise<void> => {
    console.log('[useSalesAggregationV4] Manually refreshing dashboard data...');
    await getDashboard();
  }, [getDashboard]);

  /**
   * Refresh daily data
   */
  const refreshDailyData = useCallback(async (date?: string): Promise<void> => {
    const targetDate = date || new Date().toISOString().split('T')[0];
    console.log(`[useSalesAggregationV4] Manually refreshing daily data for ${targetDate}...`);
    await getDailyDataForDate(targetDate);
  }, [getDailyDataForDate]);

  /**
   * Check if data is stale
   */
  const isDataStale = useCallback((maxAgeMinutes: number = 5): boolean => {
    if (!state.lastUpdated) return true;
    
    const lastUpdated = new Date(state.lastUpdated);
    const now = new Date();
    const ageMinutes = (now.getTime() - lastUpdated.getTime()) / (1000 * 60);
    
    return ageMinutes > maxAgeMinutes;
  }, [state.lastUpdated]);

  /**
   * Get performance metrics
   */
  const getPerformanceMetrics = useCallback(() => {
    return {
      avgLoadTime: Math.round(performanceMetrics.avgLoadTime * 100) / 100, // Round to 2 decimal places
      cacheHitRate: 0, // TODO: Implement cache hit rate tracking
      lastRefresh: performanceMetrics.lastRefresh
    };
  }, [performanceMetrics]);

  /**
   * Auto-refresh dashboard data on mount and periodically
   */
  useEffect(() => {
    // Initial load
    getDashboard();
    
    // Set up periodic refresh (every 5 minutes)
    const refreshInterval = setInterval(() => {
      if (isDataStale(5)) {
        console.log('[useSalesAggregationV4] Data is stale, auto-refreshing...');
        getDashboard();
      }
    }, 5 * 60 * 1000); // 5 minutes
    
    return () => clearInterval(refreshInterval);
  }, [getDashboard, isDataStale]);

  return {
    state,
    getDashboard,
    getDailyData: getDailyDataForDate,
    getItemPerformance: getItemPerformanceForItem,
    getDateRangeData,
    processOrderChange,
    refreshDashboard,
    refreshDailyData,
    isDataStale,
    getPerformanceMetrics
  };
}

/**
 * 🎯 Specialized hooks for specific use cases
 */

/**
 * Hook specifically for dashboard components
 */
export function useDashboardAggregation() {
  const { state, getDashboard, refreshDashboard, isDataStale, getPerformanceMetrics } = useSalesAggregationV4();
  
  return {
    dashboardData: state.dashboardData,
    isLoading: state.isLoading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    refresh: refreshDashboard,
    isStale: isDataStale,
    performance: getPerformanceMetrics
  };
}

/**
 * Hook for daily sales reports
 */
export function useDailySalesAggregation(date?: string) {
  const { getDailyData, refreshDailyData } = useSalesAggregationV4();
  const [dailyData, setDailyData] = useState<DailySalesAggregation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const targetDate = date || new Date().toISOString().split('T')[0];
  
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      const data = await getDailyData(targetDate);
      setDailyData(data);
      setIsLoading(false);
    };
    
    loadData();
  }, [targetDate, getDailyData]);
  
  return {
    dailyData,
    isLoading,
    refresh: () => refreshDailyData(targetDate),
    date: targetDate
  };
}

/**
 * Hook for item performance tracking
 */
export function useItemPerformanceAggregation(menuItemId: string) {
  const { getItemPerformance } = useSalesAggregationV4();
  const [itemData, setItemData] = useState<ItemPerformanceAggregation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    if (!menuItemId) return;
    
    const loadData = async () => {
      setIsLoading(true);
      const data = await getItemPerformance(menuItemId);
      setItemData(data);
      setIsLoading(false);
    };
    
    loadData();
  }, [menuItemId, getItemPerformance]);
  
  return {
    itemData,
    isLoading,
    menuItemId
  };
} 