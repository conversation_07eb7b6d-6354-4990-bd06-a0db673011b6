"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { SyncState, SyncStatus } from '@/lib/db/v4/core/sync-service';

// knowledge: export SyncState for use in other hooks
export type { SyncState };

interface UseSyncServiceResult {
  syncState: SyncState;
  isReady: boolean;
  isLoading: boolean;
  error: Error | null;
  dbName: string | null;
  refreshSync: () => Promise<void>;
  checkPendingChanges: () => Promise<number>;
  forceSyncOnce: () => Promise<{ success: boolean; message: string }>;
  forceFullPush: () => Promise<{ success: boolean; message: string }>;
  testAndRepairConnection: () => Promise<{ success: boolean; message: string }>;
  fixCorsIssues: () => Promise<{ success: boolean; message: string }>;
}

/**
 * Hook to use the sync service in components - updated for restaurant-based DB design
 */
export function useSyncService(): UseSyncServiceResult {
  const { isAuthenticated, loading: authLoading, user } = useAuth();
  const { 
    isDbInitialized, 
    isLoadingDb, 
    dbInitializeError, 
    currentDbRestaurantId 
  } = useUnifiedDB();

  const [syncState, setSyncState] = useState<SyncState>({
    status: 'disconnected',
    lastSync: null,
    error: null,
    isElectron: false
  });
  const [syncError, setSyncError] = useState<Error | null>(null);

  // Function to get the current sync status
  const getSyncStatus = () => {
    try {
      if (!isDbInitialized) {
        return { 
          status: 'disconnected', 
          lastSync: null,
          error: new Error('V4 database not initialized yet'),
          isElectron: false
        };
      }
      // You may want to implement a real sync status check here
      return {
        status: 'connected',
        lastSync: null,
        error: null,
        isElectron: typeof window !== 'undefined' && !!(window as any).IS_DESKTOP_APP
      };
    } catch (err) {
      return { 
        status: 'error', 
        lastSync: null,
        error: err instanceof Error ? err : new Error('Failed to get sync status'),
        isElectron: false
      };
    }
  };

  const updateSyncState = () => {
    const status = getSyncStatus();
    setSyncState({
      status: status.status as SyncStatus,
      lastSync: status.lastSync ? new Date(status.lastSync) : null,
      error: status.error,
      isElectron: status.isElectron
    });
    if (status.status === 'error' && status.error) {
      setSyncError(status.error);
    } else if (status.status !== 'error') {
      setSyncError(null);
    }
  };

  useEffect(() => {
    if (typeof window === 'undefined') return;
    if (authLoading) return;

    if (isDbInitialized) {
      updateSyncState();
    } else {
      setSyncState({
        status: 'disconnected',
        lastSync: null,
        error: null,
        isElectron: typeof window !== 'undefined' && !!(window as any).IS_DESKTOP_APP
      });
      setSyncError(dbInitializeError);
    }

    const handleSyncStateChange = () => {
      updateSyncState();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('v4-pouchdb-initialized', handleSyncStateChange);
      const interval = setInterval(() => {
        if (isDbInitialized) {
          if (syncState.status !== 'synced' && syncState.status !== 'disconnected') {
            updateSyncState();
          } else {
            const now = new Date();
            const lastCheckTime = syncState.lastSync ? syncState.lastSync.getTime() : 0;
            if (now.getTime() - lastCheckTime > 60000) {
              updateSyncState();
            }
          }
        }
      }, 15000);
      return () => {
        window.removeEventListener('v4-pouchdb-initialized', handleSyncStateChange);
        clearInterval(interval);
      };
    }
  }, [isAuthenticated, authLoading, user, isDbInitialized, isLoadingDb, dbInitializeError]);

  const refreshSync = async (): Promise<void> => {
    // Implement v4 sync refresh logic if available
      updateSyncState();
  };

  const checkPendingChanges = async (): Promise<number> => {
    updateSyncState();
    return 0; // V4 sync service doesn't track pending changes
  };

  const forceSyncOnce = async (): Promise<{ success: boolean; message: string }> => {
    // Implement v4 force sync logic if available
      updateSyncState();
    return { success: true, message: 'Force sync (v4) not yet implemented.' };
  };

  const forceFullPush = async (): Promise<{ success: boolean; message: string }> => {
    // Implement v4 full push logic if available
        updateSyncState();
    return { success: true, message: 'Full push (v4) not yet implemented.' };
  };

  const testAndRepairConnection = async (): Promise<{ success: boolean; message: string }> => {
    // Implement v4 test/repair logic if available
      updateSyncState();
    return { success: true, message: 'Test/repair connection (v4) not yet implemented.' };
  };

  const fixCorsIssues = async (): Promise<{ success: boolean; message: string }> => {
    // CORS is not relevant for v4 local PouchDB, so just return success
      updateSyncState();
    return { success: true, message: 'CORS fix not needed for v4.' };
  };

  return {
    syncState,
    isReady: isDbInitialized,
    isLoading: isLoadingDb,
    error: dbInitializeError || syncError,
    dbName: currentDbRestaurantId,
    refreshSync,
    checkPendingChanges,
    forceSyncOnce,
    forceFullPush,
    testAndRepairConnection,
    fixCorsIssues
  };
} 