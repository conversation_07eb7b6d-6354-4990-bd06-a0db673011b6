'use client';

import { useEffect, useRef } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';

interface UseRestrictionMonitorOptions {
  /** Check interval in milliseconds (default: 5 minutes) */
  interval?: number;
  /** Whether to check immediately on mount (default: true) */
  checkOnMount?: boolean;
  /** Whether to check only when online (default: true) */
  onlineOnly?: boolean;
}

/**
 * Hook that monitors user restriction status in the background
 * and automatically checks for updates from the server
 */
export function useRestrictionMonitor(options: UseRestrictionMonitorOptions = {}) {
  const {
    interval = 5 * 60 * 1000, // 5 minutes
    checkOnMount = true,
    onlineOnly = true
  } = options;

  const { 
    user, 
    isRestricted, 
    checkRestrictionStatus, 
    isOfflineMode,
    logout 
  } = useAuth();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckRef = useRef<Date | null>(null);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Start monitoring when user is authenticated
  useEffect(() => {
    if (!user || !checkRestrictionStatus) {
      // Clear any existing interval if user is not authenticated
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Skip if online-only mode and we're offline
    if (onlineOnly && isOfflineMode) {
      console.log('🔒 [RestrictionMonitor] Skipping monitoring in offline mode');
      return;
    }

    const performCheck = async () => {
      try {
        console.log('🔒 [RestrictionMonitor] Checking restriction status...');
        const isCurrentlyRestricted = await checkRestrictionStatus();
        lastCheckRef.current = new Date();
        
        if (isCurrentlyRestricted) {
          console.log('🚫 [RestrictionMonitor] User is restricted - restriction guard will handle UI');
          // If user becomes restricted, force logout after a short delay to ensure UI updates
          setTimeout(() => {
            if (isCurrentlyRestricted) {
              console.log('🚫 [RestrictionMonitor] Auto-logging out restricted user');
              logout();
            }
          }, 2000);
        } else {
          console.log('✅ [RestrictionMonitor] User is not restricted');
        }
      } catch (error) {
        console.error('❌ [RestrictionMonitor] Failed to check restriction status:', error);
      }
    };

    // Initial check if requested
    if (checkOnMount) {
      performCheck();
    }

    // Set up periodic checking
    if (interval > 0) {
      intervalRef.current = setInterval(performCheck, interval);
      console.log(`🔒 [RestrictionMonitor] Started monitoring with ${interval / 1000}s interval`);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        console.log('🔒 [RestrictionMonitor] Stopped monitoring');
      }
    };
  }, [user, checkRestrictionStatus, isOfflineMode, interval, checkOnMount, onlineOnly]);

  // Return monitoring status and manual check function
  return {
    isMonitoring: !!intervalRef.current,
    lastCheck: lastCheckRef.current,
    isRestricted,
    manualCheck: checkRestrictionStatus
  };
}