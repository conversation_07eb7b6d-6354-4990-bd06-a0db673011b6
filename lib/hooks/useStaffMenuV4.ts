'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  getStaffMenuConfig,
  updateStaffMenuConfig,
  addStaffMenuItem,
  updateStaffMenuItem,
  removeStaffMenuItem,
  getActiveStaffMenuItems,
  getStaffAllowance,
  markStaffPresent,
  useStaffAllowance,
  canStaffOrder,
  getStaffAllowancesForDate,
  getStaffAllowanceHistory
} from '../db/v4/operations/staff-menu-ops';
import {
  StaffMenuConfigDocument,
  StaffMenuItem,
  StaffAllowanceDocument
} from '../db/v4/schemas/staff-menu-schema';
import { useToast } from '@/components/ui/use-toast';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';

export function useStaffMenuV4() {
  const [config, setConfig] = useState<StaffMenuConfigDocument | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // 🔐 Database initialization status (avoid premature calls before DB is ready)
  const { isDbInitialized, dbInitializeError } = useUnifiedDB();

  // Load staff menu configuration
  const loadConfig = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const configData = await getStaffMenuConfig();
      
      // 🔍 DEBUG: Log loaded configuration
      console.log('🔍 [useStaffMenuV4] Loaded config:', {
        config: configData,
        isEnabled: configData?.isEnabled,
        staffMenuItemsCount: configData?.staffMenuItems?.length || 0,
        staffMenuItems: configData?.staffMenuItems
      });
      
      setConfig(configData);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to load staff menu configuration';
      setError(errorMessage);
      console.error('Error loading staff menu config:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update allowance per shift
  const updateAllowancePerShift = useCallback(async (allowance: number) => {
    try {
      const updatedConfig = await updateStaffMenuConfig({ allowancePerShift: allowance });
      setConfig(updatedConfig);
      toast({
        title: "✅ Paramètres mis à jour",
        description: `Allocation par service mise à jour: ${allowance}`,
      });
      return updatedConfig;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to update allowance per shift';
      setError(errorMessage);
      toast({
        title: "❌ Erreur",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  }, [toast]);

  // Add staff menu item
  const addItem = useCallback(async (item: Omit<StaffMenuItem, 'id'>) => {
    try {
      const updatedConfig = await addStaffMenuItem(item);
      setConfig(updatedConfig);
      toast({
        title: "✅ Article ajouté",
        description: `${item.itemName} ajouté au menu équipe`,
      });
      return updatedConfig;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to add staff menu item';
      setError(errorMessage);
      toast({
        title: "❌ Erreur",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  }, [toast]);

  // Update staff menu item
  const updateItem = useCallback(async (itemId: string, updates: Partial<Omit<StaffMenuItem, 'id'>>) => {
    try {
      const updatedConfig = await updateStaffMenuItem(itemId, updates);
      setConfig(updatedConfig);
      toast({
        title: "✅ Article mis à jour",
        description: "Article du menu équipe mis à jour avec succès",
      });
      return updatedConfig;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to update staff menu item';
      setError(errorMessage);
      toast({
        title: "❌ Erreur",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  }, [toast]);

  // Remove staff menu item
  const removeItem = useCallback(async (itemId: string) => {
    try {
      const updatedConfig = await removeStaffMenuItem(itemId);
      setConfig(updatedConfig);
      toast({
        title: "✅ Article supprimé",
        description: "Article retiré du menu équipe",
      });
      return updatedConfig;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to remove staff menu item';
      setError(errorMessage);
      toast({
        title: "❌ Erreur",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  }, [toast]);

  // Get active staff menu items
  const getActiveItems = useCallback(async (): Promise<StaffMenuItem[]> => {
    try {
      return await getActiveStaffMenuItems();
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to get active staff menu items';
      setError(errorMessage);
      console.error('Error getting active staff menu items:', err);
      return [];
    }
  }, []);

  // Staff allowance operations
  const getAllowance = useCallback(async (
    staffId: string,
    staffName: string,
    shiftId: string,
    shiftName: string,
    date: string
  ): Promise<StaffAllowanceDocument | null> => {
    try {
      return await getStaffAllowance(staffId, staffName, shiftId, shiftName, date);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to get staff allowance';
      setError(errorMessage);
      console.error('Error getting staff allowance:', err);
      return null;
    }
  }, []);

  // Mark staff as present
  const markPresent = useCallback(async (
    staffId: string,
    staffName: string,
    shiftId: string,
    shiftName: string,
    date: string
  ) => {
    try {
      const allowance = await markStaffPresent(staffId, staffName, shiftId, shiftName, date);
      toast({
        title: "✅ Présence confirmée",
        description: `${staffName} marqué comme présent`,
      });
      return allowance;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to mark staff as present';
      setError(errorMessage);
      toast({
        title: "❌ Erreur",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  }, [toast]);

  // Check if staff can order
  const checkCanOrder = useCallback(async (
    staffId: string,
    staffName: string,
    shiftId: string,
    shiftName: string,
    date: string,
    itemsToOrder: number = 1
  ) => {
    try {
      return await canStaffOrder(staffId, staffName, shiftId, shiftName, date, itemsToOrder);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to check staff order eligibility';
      setError(errorMessage);
      console.error('Error checking staff order eligibility:', err);
      return { canOrder: false, reason: errorMessage };
    }
  }, []);

  // Use staff allowance for an order
  const useAllowance = useCallback(async (
    staffId: string,
    staffName: string,
    shiftId: string,
    shiftName: string,
    date: string,
    orderId: string,
    itemsUsed: number = 1
  ) => {
    try {
      const result = await useStaffAllowance(staffId, staffName, shiftId, shiftName, date, orderId, itemsUsed);
      
      if (result.success) {
        toast({
          title: "✅ Allocation utilisée",
          description: `${itemsUsed} repas utilisé(s) pour ${staffName}`,
        });
      } else {
        toast({
          title: "❌ Allocation insuffisante",
          description: result.error || "Impossible d'utiliser l'allocation",
          variant: "destructive",
        });
      }
      
      return result;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to use staff allowance';
      setError(errorMessage);
      toast({
        title: "❌ Erreur",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  }, [toast]);

  // Get staff allowances for a specific date
  const getAllowancesForDate = useCallback(async (date: string): Promise<StaffAllowanceDocument[]> => {
    try {
      return await getStaffAllowancesForDate(date);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to get staff allowances for date';
      setError(errorMessage);
      console.error('Error getting staff allowances for date:', err);
      return [];
    }
  }, []);

  // Get staff allowance history
  const getAllowanceHistory = useCallback(async (
    staffId: string,
    startDate?: string,
    endDate?: string
  ): Promise<StaffAllowanceDocument[]> => {
    try {
      return await getStaffAllowanceHistory(staffId, startDate, endDate);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to get staff allowance history';
      setError(errorMessage);
      console.error('Error getting staff allowance history:', err);
      return [];
    }
  }, []);

  // Refresh the configuration
  const refresh = useCallback(() => {
    loadConfig();
  }, [loadConfig]);

  // Load configuration on mount
  useEffect(() => {
    // Wait until the V4 database has finished initializing before attempting any reads
    if (!isDbInitialized) {
      // Keep loading state true while DB is initializing
      setIsLoading(true);
      return;
    }

    // If DB initialization failed, surface the error and stop loading
    if (dbInitializeError) {
      setError(dbInitializeError.message);
      setIsLoading(false);
      return;
    }

    // DB ready – load staff menu configuration
    loadConfig();
  }, [isDbInitialized, dbInitializeError, loadConfig]);

  return {
    // State
    config,
    isLoading,
    error,
    
    // Configuration operations
    updateAllowancePerShift,
    refresh,
    
    // Staff menu item operations
    addItem,
    updateItem,
    removeItem,
    getActiveItems,
    
    // Staff allowance operations
    getAllowance,
    markPresent,
    checkCanOrder,
    useAllowance,
    getAllowancesForDate,
    getAllowanceHistory,
    
    // Computed values
    staffMenuItems: config?.staffMenuItems || [],
    allowancePerShift: config?.allowancePerShift || 1,
    isEnabled: config?.isEnabled || false
  };
} 