import { useState, useEffect, useCallback } from 'react';
import { discoverCouchDBServers } from '@/lib/services/ip-discovery';
import { nativeSyncService, type SyncServer, type SyncStatus } from '@/lib/services/native-sync';

interface UseSyncReturn {
  servers: SyncServer[];
  discovering: boolean;
  connected: boolean;
  syncing: boolean;
  status: SyncStatus;
  currentServer: SyncServer | null;
  discover: () => Promise<void>;
  connect: (server: SyncServer) => Promise<boolean>;
  disconnect: () => Promise<void>;
  error: string | null;
}

export function useSync(): UseSyncReturn {
  const [servers, setServers] = useState<SyncServer[]>([]);
  const [discovering, setDiscovering] = useState(false);
  const [status, setStatus] = useState<SyncStatus>(nativeSyncService.getStatus());
  const [currentServer, setCurrentServer] = useState<SyncServer | null>(nativeSyncService.getCurrentServer());
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = nativeSyncService.onStatusChange((newStatus) => {
      setStatus(newStatus);
      setCurrentServer(nativeSyncService.getCurrentServer());
      
      if (newStatus.error) {
        setError(newStatus.error);
      } else if (error && newStatus.connected) {
        setError(null);
      }
    });

    return unsubscribe;
  }, [error]);

  const discover = useCallback(async () => {
    if (discovering) return;
    
    setDiscovering(true);
    setError(null);
    
    try {
      console.log('🔍 Starting server discovery...');
      const discovered = await discoverCouchDBServers({
        timeout: 3000,
        maxConcurrent: 10
      });
      
      const syncServers: SyncServer[] = discovered.map(server => ({
        ip: server.ip,
        port: server.port,
        url: server.url
      }));
      
      setServers(syncServers);
      
      if (syncServers.length === 0) {
        setError('No CouchDB servers found on the network');
      } else {
        console.log(`✅ Discovered ${syncServers.length} server(s)`);
      }
    } catch (err: any) {
      console.error('❌ Discovery failed:', err);
      setError(err.message || 'Server discovery failed');
    } finally {
      setDiscovering(false);
    }
  }, [discovering]);

  const connect = useCallback(async (server: SyncServer): Promise<boolean> => {
    setError(null);
    
    try {
      console.log(`🔗 Connecting to ${server.url}...`);
      const success = await nativeSyncService.startSync(server, {
        live: true,
        retry: true
      });
      
      if (success) {
        console.log(`✅ Connected to ${server.url}`);
      } else {
        setError('Failed to establish sync connection');
      }
      
      return success;
    } catch (err: any) {
      console.error('❌ Connection failed:', err);
      setError(err.message || 'Connection failed');
      return false;
    }
  }, []);

  const disconnect = useCallback(async () => {
    try {
      await nativeSyncService.stopSync();
      console.log('🔌 Disconnected from sync server');
      setError(null);
    } catch (err: any) {
      console.error('❌ Disconnect failed:', err);
      setError(err.message || 'Disconnect failed');
    }
  }, []);

  return {
    servers,
    discovering,
    connected: status.connected,
    syncing: status.syncing,
    status,
    currentServer,
    discover,
    connect,
    disconnect,
    error
  };
}