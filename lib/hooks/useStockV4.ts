import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  InventoryDocument,
  StockItem,
  StockAdjustment,
  StockCount,
  StockCountItem,
  WasteLog,
  ConsumptionLog
} from '@/lib/db/v4/schemas/inventory-schema';
import {
  PurchaseTransaction,
  PurchaseTransactionSummary
} from '@/lib/db/v4/schemas/purchase-transaction-schema';
import {
  createPurchaseTransaction,
  getAllPurchaseTransactions,
  getPurchaseTransactionsForItem,
  getPurchaseTransactionSummaries,
  deletePurchaseTransaction,
  updatePurchaseTransaction,
  attachReceiptImage,
  getReceiptImageUrl
} from '@/lib/db/v4/operations/purchase-transaction-ops';
import {
  getInventory as getInventoryOp,
  updateInventory as updateInventoryOp,
  addStockItem as addStockItemOp,
  updateStockItem as updateStockItemOp,
  deleteStockItem as deleteStockItemOp,
  addStockAdjustment as addStockAdjustmentOp,
  updateStockAdjustment as updateStockAdjustmentOp,
  deleteStockAdjustment as deleteStockAdjustmentOp,
  addStockCount as addStockCountOp,
  updateStockCount as updateStockCountOp,
  deleteStockCount as deleteStockCountOp,
  addStockCountItem as addStockCountItemOp,
  updateStockCountItem as updateStockCountItemOp,
  deleteStockCountItem as deleteStockCountItemOp,
  addWasteLog as addWasteLogOp,
  updateWasteLog as updateWasteLogOp,
  deleteWasteLog as deleteWasteLogOp,
  applyStockCountV4 as applyStockCountOp,
  getConsumptionLogsForOrder,
  getConsumptionLogsInDateRange,
  calculateOrderCOGS
} from '@/lib/db/v4/operations/inventory-ops';

interface UseStockV4Result {
  stockItems: StockItem[];
  refreshStock: () => Promise<void>;
  createStockItem: (item: Omit<StockItem, 'id' | 'createdAt' | 'updatedAt' | 'value'>) => Promise<StockItem>;
  updateStockItem: (id: string, updates: Partial<StockItem>) => Promise<StockItem>;
  deleteStockItem: (id: string) => Promise<void>;

  // Purchase transactions (new clean system)
  purchaseTransactions: PurchaseTransaction[];
  refreshPurchaseTransactions: () => Promise<void>;
  createPurchaseTransaction: (transaction: Omit<PurchaseTransaction, '_id' | '_rev' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt' | 'subtotalCost' | 'totalCost' | 'hasReceiptImage'>) => Promise<PurchaseTransaction>;
  updatePurchaseTransaction: (id: string, updates: Partial<PurchaseTransaction>) => Promise<PurchaseTransaction>;
  deletePurchaseTransaction: (id: string) => Promise<void>;
  getPurchaseTransactionsForItem: (stockItemId: string) => Promise<PurchaseTransaction[]>;
  getPurchaseTransactionSummaries: (options?: {
    startDate?: string;
    endDate?: string;
    supplierId?: string;
    limit?: number;
  }) => Promise<PurchaseTransactionSummary[]>;

  createStockAdjustment: (adjustment: Omit<StockAdjustment, 'id' | 'createdAt' | 'updatedAt'>) => Promise<StockAdjustment>;
  getStockAdjustments: (stockItemId: string) => Promise<StockAdjustment[]>;

  stockCounts: StockCount[];
  refreshStockCounts: () => Promise<void>;
  createStockCount: (count: Omit<StockCount, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>) => Promise<StockCount>;
  updateStockCount: (id: string, updates: Partial<StockCount>) => Promise<StockCount>;
  deleteStockCount: (id: string) => Promise<void>;
  getStockCount: (id: string) => Promise<StockCount>;

  createStockCountItem: (item: Omit<StockCountItem, 'id' | 'createdAt' | 'updatedAt' | 'variance' | 'varianceValue'>) => Promise<StockCountItem>;
  getStockCountItems: (stockCountId: string) => Promise<StockCountItem[]>;
  updateStockCountItem: (id: string, updates: Partial<StockCountItem>) => Promise<StockCountItem>;
  applyStockCount: (stockCountId: string) => Promise<void>;

  wasteLogs: WasteLog[];
  refreshWasteLogs: () => Promise<void>;
  createWasteLog: (wasteLog: Omit<WasteLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>) => Promise<WasteLog>;
  getWasteLogsForItem: (stockItemId: string) => Promise<WasteLog[]>;
  getWasteLogsInDateRange: (startDate: Date, endDate: Date) => Promise<WasteLog[]>;

  // Consumption log functions
  getConsumptionLogsForOrder: (orderId: string) => Promise<ConsumptionLog[]>;
  getConsumptionLogsInDateRange: (startDate: Date, endDate: Date) => Promise<ConsumptionLog[]>;
  calculateOrderCOGS: (orderId: string) => Promise<number>;

  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
}

export function useStockV4(): UseStockV4Result {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { isDbInitialized, currentDbRestaurantId, dbInitializeError, isLoadingDb } = useUnifiedDB();

  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [purchaseTransactions, setPurchaseTransactions] = useState<PurchaseTransaction[]>([]);
  const [stockCounts, setStockCounts] = useState<StockCount[]>([]);
  const [wasteLogs, setWasteLogs] = useState<WasteLog[]>([]);
  
  const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
  const [hookError, setHookError] = useState<Error | null>(null);
  const [isHookReady, setIsHookReady] = useState<boolean>(false);

  const loadInitialData = useCallback(async () => {
    if (!isDbInitialized || !isAuthenticated) {
      console.warn('[useStockV4.loadInitialData] Cannot run: DB not initialized or user not authenticated.');
      setIsHookReady(false);
      return;
    }

    if (isHookReady) {
      console.log('[useStockV4.loadInitialData] Hook already ready for this state, skipping.');
      return;
    }

    console.log('[useStockV4.loadInitialData] STARTING initial inventory data load...');
    setIsDataLoading(true);
    setHookError(null);
    try {
      console.log('[useStockV4.loadInitialData] Loading inventory and purchase transactions...');
      
      // Load inventory data
      const inventoryDoc = await getInventoryOp();
      setStockItems(inventoryDoc.items || []);
      setStockCounts(inventoryDoc.stockCounts || []);
      setWasteLogs(inventoryDoc.wasteLogs || []);
      
      // Load purchase transactions
      const transactions = await getAllPurchaseTransactions({ limit: 100 });
      setPurchaseTransactions(transactions);
      
      console.log('[useStockV4.loadInitialData] Setting isHookReady = true.');
      setIsHookReady(true);
    } catch (err) {
      console.error('[useStockV4.loadInitialData] ERROR loading initial inventory data:', err);
      setHookError(err instanceof Error ? err : new Error('Failed to load initial inventory data'));
      console.log('[useStockV4.loadInitialData] Setting isHookReady = false due to error.');
      setIsHookReady(false);
    } finally {
      console.log('[useStockV4.loadInitialData] Setting isDataLoading = false.');
      setIsDataLoading(false);
    }
  }, [isDbInitialized, isAuthenticated, isHookReady]);

  useEffect(() => {
    console.log(`[useStockV4.useEffect] Triggered. AuthLoading: ${authLoading}, IsAuthenticated: ${isAuthenticated}, IsDbInitialized: ${isDbInitialized}`);
    
    if (dbInitializeError) {
      console.warn('[useStockV4.useEffect] DB initialization error detected, but allowing potential recovery:', dbInitializeError.message);
      setIsHookReady(false);
      setIsDataLoading(false);
      setStockItems([]);
      setPurchaseTransactions([]);
      setStockCounts([]);
      setWasteLogs([]);
      
      if (isDbInitialized) {
        setHookError(new Error(`Database initialization completed with errors: ${dbInitializeError.message}`));
      } else {
        setHookError(null);
      }
      return;
    }

    if (isDbInitialized && isAuthenticated) {
      console.log('[useStockV4.useEffect] Conditions met. Calling loadInitialData.');
      loadInitialData();
    } else {
      console.log('[useStockV4.useEffect] Conditions NOT MET (DB not init or not Auth). Resetting hook state.');
      setStockItems([]);
      setPurchaseTransactions([]);
      setStockCounts([]);
      setWasteLogs([]);
      setIsDataLoading(true); 
      setIsHookReady(false); 
      setHookError(null);
    }
  }, [isDbInitialized, isAuthenticated, dbInitializeError, loadInitialData]);

  const refreshStock = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setStockItems(doc.items || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load stock items'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const refreshPurchaseTransactions = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const transactions = await getAllPurchaseTransactions({ limit: 100 });
      setPurchaseTransactions(transactions);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load purchase transactions'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createStockItem = async (item: Omit<StockItem, 'id' | 'createdAt' | 'updatedAt' | 'value'>): Promise<StockItem> => {
    console.log(`[useStockV4.createStockItem] Called. isHookReady: ${isHookReady} isAuthenticated: ${isAuthenticated}`);
    if (!isHookReady || !isAuthenticated) {
      console.error('[useStockV4.createStockItem] Aborted: Hook not ready or user not authenticated.');
      throw new Error('Inventory system is not ready.');
    }
    try {
      const newItem = await addStockItemOp(item);
      await refreshStock();
      return newItem;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to create stock item');
      setHookError(error);
      throw error;
    }
  };

  const updateStockItem = async (id: string, updates: Partial<StockItem>): Promise<StockItem> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedItem = await updateStockItemOp(id, updates);
      await refreshStock();
      return updatedItem;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to update stock item');
      setHookError(error);
      throw error;
    }
  };

  const deleteStockItem = async (id: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await deleteStockItemOp(id);
      await refreshStock();
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to delete stock item');
      setHookError(error);
      throw error;
    }
  };

  const createPurchaseTransactionHook = async (transaction: Omit<PurchaseTransaction, '_id' | '_rev' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt' | 'subtotalCost' | 'totalCost' | 'hasReceiptImage'>): Promise<PurchaseTransaction> => {
    console.log(`[createPurchaseTransactionHook] Hook state - Ready: ${isHookReady}, Auth: ${isAuthenticated}, DbInit: ${isDbInitialized}`);
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const transactionWithPerformer = {
        ...transaction,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newTransaction = await createPurchaseTransaction(transactionWithPerformer);
      await refreshPurchaseTransactions();
      await refreshStock(); // Refresh stock to reflect quantity changes
      return newTransaction;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to create purchase transaction');
      setHookError(error);
      throw error;
    }
  };

  const updatePurchaseTransactionHook = async (id: string, updates: Partial<PurchaseTransaction>): Promise<PurchaseTransaction> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedTransaction = await updatePurchaseTransaction(id, updates);
      await refreshPurchaseTransactions();
      await refreshStock(); // Refresh stock to reflect quantity changes
      return updatedTransaction;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to update purchase transaction');
      setHookError(error);
      throw error;
    }
  };

  const deletePurchaseTransactionHook = async (id: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await deletePurchaseTransaction(id);
      await refreshPurchaseTransactions();
      await refreshStock(); // Refresh stock to reflect quantity changes
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to delete purchase transaction');
      setHookError(error);
      throw error;
    }
  };

  const getPurchaseTransactionsForItemHook = async (stockItemId: string): Promise<PurchaseTransaction[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      return await getPurchaseTransactionsForItem(stockItemId);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get purchase transactions for item');
      setHookError(error);
      throw error;
    }
  };

  const getPurchaseTransactionSummariesHook = async (options?: {
    startDate?: string;
    endDate?: string;
    supplierId?: string;
    limit?: number;
  }): Promise<PurchaseTransactionSummary[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      return await getPurchaseTransactionSummaries(options);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get purchase transaction summaries');
      setHookError(error);
      throw error;
    }
  };

  const createStockAdjustment = async (adjustment: Omit<StockAdjustment, 'id' | 'createdAt' | 'updatedAt'>): Promise<StockAdjustment> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const adjWithPerformer = {
        ...adjustment,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newAdj = await addStockAdjustmentOp(adjWithPerformer);
      await refreshStock();
      return newAdj;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to create stock adjustment');
      setHookError(error);
      throw error;
    }
  };

  const getStockAdjustments = async (stockItemId: string): Promise<StockAdjustment[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.adjustments || []).filter(a => a.stockItemId === stockItemId);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get stock adjustments');
      setHookError(error);
      throw error;
    }
  };

  const refreshStockCounts = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setStockCounts(doc.stockCounts || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load stock counts'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createStockCount = async (count: Omit<StockCount, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>): Promise<StockCount> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const countWithPerformer = {
        ...count,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newCount = await addStockCountOp(countWithPerformer);
      await refreshStockCounts();
      return newCount;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to create stock count');
      setHookError(error);
      throw error;
    }
  };

  const updateStockCount = async (id: string, updates: Partial<StockCount>): Promise<StockCount> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedCount = await updateStockCountOp(id, updates);
      await refreshStockCounts();
      return updatedCount;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to update stock count');
      setHookError(error);
      throw error;
    }
  };

  const deleteStockCount = async (id: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await deleteStockCountOp(id);
      await refreshStockCounts();
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to delete stock count');
      setHookError(error);
      throw error;
    }
  };

  const getStockCount = async (id: string): Promise<StockCount> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      const found = (doc.stockCounts || []).find(c => c.id === id);
      if (!found) throw new Error('Stock count not found');
      return found;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get stock count');
      setHookError(error);
      throw error;
    }
  };

  const createStockCountItem = async (item: Omit<StockCountItem, 'id' | 'createdAt' | 'updatedAt' | 'variance' | 'varianceValue'>): Promise<StockCountItem> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const newItem = await addStockCountItemOp(item);
      await refreshStockCounts();
      return newItem;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to create stock count item');
      setHookError(error);
      throw error;
    }
  };

  const getStockCountItems = async (stockCountId: string): Promise<StockCountItem[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.stockCountItems || []).filter(i => i.stockCountId === stockCountId);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get stock count items');
      setHookError(error);
      throw error;
    }
  };

  const updateStockCountItem = async (id: string, updates: Partial<StockCountItem>): Promise<StockCountItem> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedItem = await updateStockCountItemOp(id, updates);
      await refreshStockCounts();
      return updatedItem;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to update stock count item');
      setHookError(error);
      throw error;
    }
  };

  const applyStockCount = async (stockCountId: string): Promise<void> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await applyStockCountOp(stockCountId);
      await refreshStockCounts();
      await refreshStock();
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to apply stock count');
      setHookError(error);
      throw error;
    }
  };

  const refreshWasteLogs = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getInventoryOp();
      setWasteLogs(doc.wasteLogs || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load waste logs'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createWasteLog = async (wasteLog: Omit<WasteLog, 'id' | 'createdAt' | 'updatedAt' | 'performedBy'>): Promise<WasteLog> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const logWithPerformer = {
        ...wasteLog,
        performedBy: user?.name || (user as any)?.username || 'unknown'
      };
      const newLog = await addWasteLogOp(logWithPerformer);
      await refreshWasteLogs();
      await refreshStock();
      return newLog;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to create waste log');
      setHookError(error);
      throw error;
    }
  };

  const getWasteLogsForItem = async (stockItemId: string): Promise<WasteLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.wasteLogs || []).filter(w => w.stockItemId === stockItemId);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get waste logs for item');
      setHookError(error);
      throw error;
    }
  };

  const getWasteLogsInDateRange = async (startDate: Date, endDate: Date): Promise<WasteLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const doc = await getInventoryOp();
      return (doc.wasteLogs || []).filter(w => {
        const d = new Date(w.date);
        return d >= startDate && d <= endDate;
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get waste logs in date range');
      setHookError(error);
      throw error;
    }
  };

  const getConsumptionLogsForOrderHook = async (orderId: string): Promise<ConsumptionLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const logs = await getConsumptionLogsForOrder(orderId);
      setHookError(null);
      return logs;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get consumption logs for order');
      setHookError(error);
      throw error;
    }
  };

  const getConsumptionLogsInDateRangeHook = async (startDate: Date, endDate: Date): Promise<ConsumptionLog[]> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const logs = await getConsumptionLogsInDateRange(startDate, endDate);
      setHookError(null);
      return logs;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to get consumption logs in date range');
      setHookError(error);
      throw error;
    }
  };

  const calculateOrderCOGSHook = async (orderId: string): Promise<number> => {
    if (!isHookReady || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const cogs = await calculateOrderCOGS(orderId);
      setHookError(null);
      return cogs;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(err ? String(err) : 'Failed to calculate order COGS');
      setHookError(error);
      throw error;
    }
  };

  return {
    stockItems,
    refreshStock,
    createStockItem,
    updateStockItem,
    deleteStockItem,
    
    purchaseTransactions,
    refreshPurchaseTransactions,
    createPurchaseTransaction: createPurchaseTransactionHook,
    updatePurchaseTransaction: updatePurchaseTransactionHook,
    deletePurchaseTransaction: deletePurchaseTransactionHook,
    getPurchaseTransactionsForItem: getPurchaseTransactionsForItemHook,
    getPurchaseTransactionSummaries: getPurchaseTransactionSummariesHook,
    
    createStockAdjustment,
    getStockAdjustments,
    stockCounts,
    refreshStockCounts,
    createStockCount,
    updateStockCount,
    deleteStockCount,
    getStockCount,
    createStockCountItem,
    getStockCountItems,
    updateStockCountItem,
    applyStockCount,
    wasteLogs,
    refreshWasteLogs,
    createWasteLog,
    getWasteLogsForItem,
    getWasteLogsInDateRange,
    getConsumptionLogsForOrder: getConsumptionLogsForOrderHook,
    getConsumptionLogsInDateRange: getConsumptionLogsInDateRangeHook,
    calculateOrderCOGS: calculateOrderCOGSHook,
    isLoading: isLoadingDb || isDataLoading,
    error: dbInitializeError || hookError,
    isReady: isDbInitialized && isHookReady && !isDataLoading
  };
}