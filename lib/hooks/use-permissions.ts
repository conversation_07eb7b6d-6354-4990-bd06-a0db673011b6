import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { getStaffMember, initializeV4Database } from '@/lib/db/v4';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';

// 📋 Permission types based on the actual PouchDB document structure
export type PagePermissions = {
  menu: boolean;
  orders: boolean;
  finance: boolean;
  analytics: boolean;
  inventory: boolean;
  staff: boolean;
  settings: boolean;
  suppliers: boolean;
};

export type TabPermissions = {
  inventory: {
    inventory: boolean;
    subrecipes: boolean;
    counts: boolean;
    waste: boolean;
  };
  staff: {
    shifts_schedule: boolean;
    attendance: boolean;
    payments: boolean;
  };
  orders: {
    collection: boolean;
  };
};

export type UserPermissions = {
  pages: PagePermissions;
  tabs: TabPermissions;
};

// 🔒 Default permissions for new staff (all false)
const DEFAULT_PERMISSIONS: UserPermissions = {
  pages: {
    menu: false,
    orders: false,
    finance: false,
    analytics: false,
    inventory: false,
    staff: false,
    settings: false,
    suppliers: false,
  },
  tabs: {
    inventory: {
      inventory: false,
      subrecipes: false,
      counts: false,
      waste: false,
    },
    staff: {
      shifts_schedule: false,
      attendance: false,
      payments: false,
    },
    orders: {
      collection: false,
    },
  },
};

// 👑 Owner permissions (all true)
const OWNER_PERMISSIONS: UserPermissions = {
  pages: {
    menu: true,
    orders: true,
    finance: true,
    analytics: true,
    inventory: true,
    staff: true,
    settings: true,
    suppliers: true,
  },
  tabs: {
    inventory: {
      inventory: true,
      subrecipes: true,
      counts: true,
      waste: true,
    },
    staff: {
      shifts_schedule: true,
      attendance: true,
      payments: true,
    },
    orders: {
      collection: true,
    },
  },
};

export function usePermissions() {
  const { user, isAuthenticated } = useAuth();
  const [permissions, setPermissions] = useState<UserPermissions>(DEFAULT_PERMISSIONS);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 🔍 Check if user is owner/admin
  const isOwner = user?.role === 'owner' || user?.role === 'admin';

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!isAuthenticated || !user?.id) {
        setPermissions(DEFAULT_PERMISSIONS);
        setIsLoading(false);
        return;
      }

      // 👑 Owners get all permissions immediately
      if (isOwner) {
        setPermissions(OWNER_PERMISSIONS);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // 🎯 Initialize database first
        const restaurantId = getCurrentRestaurantId();
        if (!restaurantId) {
          console.warn('⚠️ No restaurant ID found, using default permissions');
          setPermissions(DEFAULT_PERMISSIONS);
          setIsLoading(false);
          return;
        }

        try {
          console.log('🔧 Initializing database for permissions fetch...');
          await initializeV4Database(restaurantId);
        } catch (initError) {
          console.warn('⚠️ Database initialization failed, using default permissions:', initError);
          setPermissions(DEFAULT_PERMISSIONS);
          setIsLoading(false);
          return;
        }

        // 📄 Fetch staff document directly from PouchDB
        console.log('🔍 Fetching permissions for user:', user.id);
        const staffDoc = await getStaffMember(user.id);

        if (staffDoc?.permissions) {
          console.log('✅ Found permissions in staff document:', staffDoc.permissions);
          
          // 🔄 Convert PouchDB permissions to our format
          const userPermissions: UserPermissions = {
            pages: {
              menu: !!staffDoc.permissions.pages?.menu,
              orders: !!staffDoc.permissions.pages?.orders,
              finance: !!staffDoc.permissions.pages?.finance,
              analytics: !!staffDoc.permissions.pages?.analytics,
              inventory: !!staffDoc.permissions.pages?.inventory,
              staff: !!staffDoc.permissions.pages?.staff,
              settings: !!staffDoc.permissions.pages?.settings,
              suppliers: !!staffDoc.permissions.pages?.suppliers,
            },
            tabs: {
              inventory: {
                inventory: !!staffDoc.permissions.tabs?.inventory?.inventory,
                subrecipes: !!staffDoc.permissions.tabs?.inventory?.subrecipes,
                counts: !!staffDoc.permissions.tabs?.inventory?.counts,
                waste: !!staffDoc.permissions.tabs?.inventory?.waste,
              },
              staff: {
                shifts_schedule: !!staffDoc.permissions.tabs?.staff?.shifts_schedule,
                attendance: !!staffDoc.permissions.tabs?.staff?.attendance,
                payments: !!staffDoc.permissions.tabs?.staff?.payments,
              },
              orders: {
                collection: !!staffDoc.permissions.tabs?.orders?.collection,
              },
            },
          };

          setPermissions(userPermissions);
        } else {
          console.log('⚠️ No permissions found in staff document');
          setPermissions(DEFAULT_PERMISSIONS);
        }
      } catch (err) {
        console.error('❌ Error fetching permissions:', err);
        
        // Special handling for database errors
        if (err instanceof Error && err.message.includes('Database not initialized')) {
          console.warn('🔧 Database initialization issue, retrying...');
          setError('Database initializing, please wait...');
          // Don't set default permissions yet, keep loading state
          return;
        }
        
        setError(err instanceof Error ? err.message : 'Failed to fetch permissions');
        setPermissions(DEFAULT_PERMISSIONS);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPermissions();
  }, [user?.id, isAuthenticated, isOwner]);

  // 🔐 Permission check functions
  const hasPageAccess = (page: keyof PagePermissions): boolean => {
    if (isOwner) return true;
    return permissions.pages[page];
  };

  const hasTabAccess = (page: string, tab: string): boolean => {
    if (isOwner) return true;
    
    // First check page access
    if (!hasPageAccess(page as keyof PagePermissions)) {
      return false;
    }

    // Then check tab access
    const pageTabPermissions = permissions.tabs[page as keyof TabPermissions];
    if (!pageTabPermissions) return true; // Default to true if no tab restrictions

    return pageTabPermissions[tab as keyof typeof pageTabPermissions] ?? true;
  };

  return {
    permissions,
    isLoading,
    error,
    isOwner,
    hasPageAccess,
    hasTabAccess,
    // 🔄 Refresh function to manually reload permissions
    refresh: () => {
      if (isAuthenticated && user?.id) {
        setIsLoading(true);
        // Trigger useEffect by updating a dependency
        setError(null);
      }
    },
  };
} 