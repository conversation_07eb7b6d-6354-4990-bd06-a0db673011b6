'use client';

import { useState, useEffect, useCallback } from 'react';
import { Supplement } from '@/lib/db/v4/schemas/menu-schema';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getAllSupplements,
  getActiveSupplements,
  createSupplement,
  updateSupplement,
  deleteSupplement,
  getCategorySupplementConfig,
  updateCategorySupplementConfig,
  getAllSupplementPrices,
  toggleSupplementStatus
} from '@/lib/db/v4/operations/supplement-ops';

interface UseSupplementsReturn {
  supplements: Supplement[];
  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
  refreshSupplements: () => Promise<void>;
  createSupplement: (supplementData: {
    name: string;
    description?: string;
    stockConsumption?: {
      stockItemId: string;
      quantities: { [sizeName: string]: number };
    };
    color?: string;
    image?: string;
    isActive?: boolean;
  }) => Promise<Supplement>;
  updateSupplement: (
    supplementId: string,
    updates: Partial<Omit<Supplement, 'id'>>
  ) => Promise<void>;
  deleteSupplement: (supplementId: string) => Promise<void>;
  toggleSupplementStatus: (supplementId: string, isActive: boolean) => Promise<Supplement>;
  getCategorySupplementConfig: () => Promise<any>;
  updateCategorySupplementConfig: (config: any) => Promise<void>;
  getAllSupplementPrices: () => Promise<{ [sizeName: string]: number }>;
}

/**
 * useSupplements hook - provides category-specific supplement functionality using the v4 database
 */
export function useSupplements(categoryId: string): UseSupplementsReturn {
  const { isAuthenticated, user } = useAuth();
  const { isDbInitialized, isLoadingDb, dbInitializeError, currentDbRestaurantId } = useUnifiedDB();

  const [supplements, setSupplements] = useState<Supplement[]>([]);
  const [isSupplementsLoading, setIsSupplementsLoading] = useState(true);
  const [supplementsError, setSupplementsError] = useState<Error | null>(null);
  const [isSupplementsReady, setIsSupplementsReady] = useState(false);

  // Load supplements for the specific category
  const loadSupplements = useCallback(async () => {
    if (!isDbInitialized || !categoryId) {
      console.warn('[useSupplements.loadSupplements] Skipped: DB not initialized or no categoryId.');
      setIsSupplementsReady(false);
      return;
    }
    try {
      setIsSupplementsLoading(true);
      const data = await getAllSupplements(categoryId);
      setSupplements(data);
      setSupplementsError(null);
    } catch (err) {
      console.error('[useSupplements.loadSupplements] Error loading supplements:', err);
      setSupplementsError(err instanceof Error ? err : new Error(String(err)));
      setIsSupplementsReady(false);
    } finally {
      setIsSupplementsLoading(false);
    }
  }, [isDbInitialized, categoryId]);

  // Initialize supplements when DB is ready
  useEffect(() => {
    if (!isDbInitialized || !isAuthenticated || !categoryId) {
      setIsSupplementsReady(false);
      setSupplements([]);
      setIsSupplementsLoading(true);
      if (dbInitializeError && !supplementsError) {
        setSupplementsError(new Error(`Database initialization failed: ${dbInitializeError.message}`));
      }
      return;
    }

    if (isSupplementsReady) return;

    const initializeSupplements = async () => {
      console.log(`[useSupplements] DB initialized for restaurant: ${currentDbRestaurantId}, category: ${categoryId}. Initializing Supplements hook.`);
      setIsSupplementsLoading(true);
      setSupplementsError(null);
      
      try {
        await loadSupplements();
        if (!supplementsError) {
          setIsSupplementsReady(true);
        }
      } catch (err) {
        console.error('[useSupplements] Error during supplements hook initialization:', err);
        setSupplementsError(err instanceof Error ? err : new Error(String(err)));
        setIsSupplementsReady(false);
      } finally {
        setIsSupplementsLoading(false);
      }
    };

    initializeSupplements();
  }, [isDbInitialized, isAuthenticated, currentDbRestaurantId, categoryId, loadSupplements, supplementsError, isSupplementsReady, dbInitializeError]);

  // Refresh supplements
  const refreshSupplements = useCallback(async () => {
    await loadSupplements();
  }, [loadSupplements]);

  // Create supplement
  const createSupplementWrapper = useCallback(async (supplementData: {
    name: string;
    description?: string;
    stockConsumption?: {
      stockItemId: string;
      quantities: { [sizeName: string]: number };
    };
    color?: string;
    image?: string;
    isActive?: boolean;
  }) => {
    if (!isSupplementsReady || !categoryId) throw new Error('Supplements hook not ready or no categoryId provided.');
    try {
      const newSupplement = await createSupplement(categoryId, supplementData);
      await refreshSupplements();
      return newSupplement;
    } catch (err) {
      console.error('Error creating supplement:', err);
      setSupplementsError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [refreshSupplements, isSupplementsReady, categoryId]);

  // Update supplement
  const updateSupplementWrapper = useCallback(async (
    supplementId: string,
    updates: Partial<Omit<Supplement, 'id'>>
  ) => {
    if (!isSupplementsReady || !categoryId) throw new Error('Supplements hook not ready or no categoryId provided.');
    try {
      await updateSupplement(categoryId, supplementId, updates);
      await refreshSupplements();
    } catch (err) {
      console.error(`Error updating supplement ${supplementId}:`, err);
      setSupplementsError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [refreshSupplements, isSupplementsReady, categoryId]);

  // Delete supplement
  const deleteSupplementWrapper = useCallback(async (supplementId: string) => {
    if (!isSupplementsReady || !categoryId) throw new Error('Supplements hook not ready or no categoryId provided.');
    try {
      await deleteSupplement(categoryId, supplementId);
      await refreshSupplements();
    } catch (err) {
      console.error(`Error deleting supplement ${supplementId}:`, err);
      setSupplementsError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [refreshSupplements, isSupplementsReady, categoryId]);

  // Toggle supplement status
  const toggleSupplementStatusWrapper = useCallback(async (supplementId: string, isActive: boolean) => {
    if (!isSupplementsReady || !categoryId) throw new Error('Supplements hook not ready or no categoryId provided.');
    try {
      const updatedSupplement = await toggleSupplementStatus(categoryId, supplementId, isActive);
      await refreshSupplements();
      return updatedSupplement;
    } catch (err) {
      console.error(`Error toggling supplement status ${supplementId}:`, err);
      setSupplementsError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    }
  }, [refreshSupplements, isSupplementsReady, categoryId]);

  // Get category supplement config
  const getCategorySupplementConfigWrapper = useCallback(async () => {
    if (!categoryId) throw new Error('No categoryId provided.');
    return await getCategorySupplementConfig(categoryId);
  }, [categoryId]);

  // Update category supplement config
  const updateCategorySupplementConfigWrapper = useCallback(async (config: any) => {
    if (!categoryId) throw new Error('No categoryId provided.');
    await updateCategorySupplementConfig(categoryId, config);
  }, [categoryId]);

  // Get all supplement prices for this category
  const getAllSupplementPricesWrapper = useCallback(async () => {
    if (!categoryId) throw new Error('No categoryId provided.');
    return await getAllSupplementPrices(categoryId);
  }, [categoryId]);

  return {
    supplements,
    isLoading: isSupplementsLoading,
    error: supplementsError,
    isReady: isSupplementsReady,
    refreshSupplements,
    createSupplement: createSupplementWrapper,
    updateSupplement: updateSupplementWrapper,
    deleteSupplement: deleteSupplementWrapper,
    toggleSupplementStatus: toggleSupplementStatusWrapper,
    getCategorySupplementConfig: getCategorySupplementConfigWrapper,
    updateCategorySupplementConfig: updateCategorySupplementConfigWrapper,
    getAllSupplementPrices: getAllSupplementPricesWrapper,
  };
} 