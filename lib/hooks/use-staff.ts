'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  getAllStaff, 
  addStaffMember as addStaffMemberDB, 
  updateStaffMember as updateStaffMemberDB, 
  deleteStaffMember as deleteStaffMemberDB,
  getStaffMember,
  StaffDocument
} from '@/lib/db/v4';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { toast } from 'sonner';

interface UseStaffReturn {
  staff: StaffDocument[];
  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
  refreshStaff: () => Promise<void>;
  addStaffMember: (staffMember: Partial<StaffDocument>) => Promise<StaffDocument>;
  updateStaffMember: (id: string, data: Partial<StaffDocument>) => Promise<StaffDocument>;
  deleteStaffMember: (id: string) => Promise<void>;
  updateStaffPermissions: (staffId: string, permissions: StaffDocument['permissions']) => Promise<void>;
  getStaffPermissions: (staffId: string) => Promise<any>;
  hasUserAccount: (staffId: string) => boolean;
  addStaffAuth: (staffId: string, credentials: {username: string, password: string, role?: string}) => Promise<any>;
  updateStaffAuth: (staffId: string, credentials: {username: string, password?: string}) => Promise<any>;
  isUsernameAvailable: (username: string) => Promise<boolean>;
}

export function useStaff(): UseStaffReturn {
  const [staff, setStaff] = useState<StaffDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isReady, setIsReady] = useState(false);
  const { isAuthenticated } = useAuth();
  const { status } = useUnifiedDB();
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const refreshStaff = useCallback(async () => {
    if (!isAuthenticated) {
      setStaff([]);
      setIsLoading(false);
      setIsReady(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('useStaff: Fetching staff from per-staff database');
      const staffData = await getAllStaff();
      console.log(`useStaff: Retrieved ${staffData.length} staff members`);
      
      setStaff(staffData);
      setIsReady(true);
    } catch (err) {
      console.error('useStaff: Error fetching staff:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch staff'));
      setStaff([]);
      setIsReady(false);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Auto-refresh when authentication status changes
  useEffect(() => {
    refreshStaff();
  }, [refreshStaff]);

  // Auto-refresh when database status changes
  useEffect(() => {
    if (status.status === 'complete' && isAuthenticated) {
      // Debounce the refresh to avoid multiple calls
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      refreshTimeoutRef.current = setTimeout(() => {
        refreshStaff();
      }, 500);
    }

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [status.status, isAuthenticated, refreshStaff]);

  const addStaffMember = useCallback(async (staffMember: Partial<StaffDocument>): Promise<StaffDocument> => {
    try {
      console.log('useStaff: Adding staff member:', staffMember.name);
      const newStaff = await addStaffMemberDB(staffMember);
      
      // Update local state
      setStaff(prev => [...prev, newStaff]);
      
      return newStaff;
    } catch (err) {
      console.error('useStaff: Error adding staff member:', err);
      throw err;
    }
  }, []);

  const updateStaffMember = useCallback(async (id: string, data: Partial<StaffDocument>): Promise<StaffDocument> => {
    try {
      console.log('useStaff: Updating staff member:', id);
      const updatedStaff = await updateStaffMemberDB(id, data);
      
      // Update local state
      setStaff(prev => prev.map(s => s.id === id ? updatedStaff : s));
      
      return updatedStaff;
    } catch (err) {
      console.error('useStaff: Error updating staff member:', err);
      throw err;
    }
  }, []);

  const deleteStaffMember = useCallback(async (id: string): Promise<void> => {
    try {
      console.log('useStaff: Deleting staff member:', id);
      await deleteStaffMemberDB(id);
      
      // Update local state
      setStaff(prev => prev.filter(s => s.id !== id));
    } catch (err) {
      console.error('useStaff: Error deleting staff member:', err);
      throw err;
    }
  }, []);

  const updateStaffPermissions = useCallback(async (staffId: string, permissions: StaffDocument['permissions']): Promise<void> => {
    try {
      console.log('useStaff: Updating staff permissions:', staffId);
      await updateStaffMemberDB(staffId, { permissions });
      
      // Update local state
      setStaff(prev => prev.map(s => 
        s.id === staffId ? { ...s, permissions } : s
      ));
    } catch (err) {
      console.error('useStaff: Error updating staff permissions:', err);
      throw err;
    }
  }, []);

  const getStaffPermissions = useCallback(async (staffId: string): Promise<any> => {
    try {
      console.log('useStaff: Getting staff permissions directly from PouchDB:', staffId);
      const staffMember = await getStaffMember(staffId);
      
      if (staffMember?.permissions) {
        console.log('useStaff: Found permissions in PouchDB:', staffMember.permissions);
        return staffMember.permissions;
      } else {
        console.log('useStaff: No permissions found in PouchDB for staff:', staffId);
        return null;
      }
    } catch (err) {
      console.error('useStaff: Error getting staff permissions from PouchDB:', err);
      throw err;
    }
  }, []);

  const hasUserAccount = useCallback((staffId: string): boolean => {
    const staffMember = staff.find(s => s.id === staffId);
    return staffMember?.hasUserAccount || false;
  }, [staff]);

  // Placeholder functions for auth operations (to be implemented)
  const addStaffAuth = useCallback(async (staffId: string, credentials: {username: string, password: string, role?: string}): Promise<any> => {
    console.log('useStaff: Adding staff auth (placeholder):', staffId);
    // TODO: Implement staff auth creation
    throw new Error('Staff auth creation not yet implemented');
  }, []);

  const updateStaffAuth = useCallback(async (staffId: string, credentials: {username: string, password?: string}): Promise<any> => {
    console.log('useStaff: Updating staff auth (placeholder):', staffId);
    // TODO: Implement staff auth update
    throw new Error('Staff auth update not yet implemented');
  }, []);

  const isUsernameAvailable = useCallback(async (username: string): Promise<boolean> => {
    console.log('useStaff: Checking username availability (placeholder):', username);
    // TODO: Implement username availability check
    return true;
  }, []);

  return {
    staff,
    isLoading,
    error,
    isReady,
    refreshStaff,
    addStaffMember,
    updateStaffMember,
    deleteStaffMember,
    updateStaffPermissions,
    getStaffPermissions,
    hasUserAccount,
    addStaffAuth,
    updateStaffAuth,
    isUsernameAvailable
  };
}