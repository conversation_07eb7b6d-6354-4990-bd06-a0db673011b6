import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getSuppliers,
  addSupplier,
  updateSupplier,
  deleteSupplier
} from '@/lib/db/v4/operations/supplier-ops';
import { Supplier } from '@/lib/db/v4/schemas/supplier-schema';

interface UseSuppliersV4Result {
  suppliers: Supplier[];
  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
  refreshSuppliers: () => Promise<void>;
  createSupplier: (supplier: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Supplier>;
  updateSupplier: (id: string, updates: Partial<Supplier>) => Promise<Supplier>;
  deleteSupplier: (id: string) => Promise<void>;
}

export function useSuppliersV4(): UseSuppliersV4Result {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { isDbInitialized, currentDbRestaurantId, dbInitializeError, isLoadingDb } = useUnifiedDB();

  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
  const [hookError, setHookError] = useState<Error | null>(null);
  const [isHookReady, setIsHookReady] = useState<boolean>(false);

  const loadInitialData = async () => {
    if (!isDbInitialized || !isAuthenticated) {
      setIsHookReady(false);
      return;
    }

    if (isHookReady) {
      return;
    }

    setIsDataLoading(true);
    setHookError(null);
    try {
      const suppliersDoc = await getSuppliers();
      setSuppliers(suppliersDoc.suppliers || []);
      setIsHookReady(true);
    } catch (err) {
      console.error('Error loading initial suppliers data:', err);
      setHookError(err instanceof Error ? err : new Error('Failed to load initial suppliers data'));
      setIsHookReady(false);
    } finally {
      setIsDataLoading(false);
    }
  };

  useEffect(() => {
    if (dbInitializeError) {
      setHookError(new Error(`Database initialization failed: ${dbInitializeError.message}`));
      setIsHookReady(false);
      setIsDataLoading(false);
      setSuppliers([]);
      return;
    }

    if (isDbInitialized && isAuthenticated) {
      loadInitialData();
    } else {
      setSuppliers([]);
      setIsDataLoading(true);
      setIsHookReady(false);
      setHookError(null);
    }
  }, [isDbInitialized, isAuthenticated, dbInitializeError]);

  const refreshSuppliers = async () => {
    if (!isHookReady) return;
    setIsDataLoading(true);
    try {
      const doc = await getSuppliers();
      setSuppliers(doc.suppliers || []);
      setHookError(null);
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to load suppliers'));
    } finally {
      setIsDataLoading(false);
    }
  };

  const createSupplier = async (supplier: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>): Promise<Supplier> => {
    if (!isDbInitialized || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const newSupplier = await addSupplier(supplier);
      await refreshSuppliers();
      return newSupplier;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to create supplier'));
      throw err;
    }
  };

  const updateSupplierFn = async (id: string, updates: Partial<Supplier>): Promise<Supplier> => {
    if (!isDbInitialized || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      const updatedSupplier = await updateSupplier(id, updates);
      await refreshSuppliers();
      return updatedSupplier;
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to update supplier'));
      throw err;
    }
  };

  const deleteSupplierFn = async (id: string): Promise<void> => {
    if (!isDbInitialized || !isAuthenticated) throw new Error('Database not ready or user not authenticated');
    try {
      await deleteSupplier(id);
      await refreshSuppliers();
    } catch (err) {
      setHookError(err instanceof Error ? err : new Error('Failed to delete supplier'));
      throw err;
    }
  };

  return {
    suppliers,
    isLoading: isLoadingDb || isDataLoading,
    error: dbInitializeError || hookError,
    isReady: isDbInitialized && !isDataLoading && !hookError,
    refreshSuppliers,
    createSupplier,
    updateSupplier: updateSupplierFn,
    deleteSupplier: deleteSupplierFn
  };
}