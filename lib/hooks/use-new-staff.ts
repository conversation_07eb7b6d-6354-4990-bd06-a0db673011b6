'use client';

import { useState, useEffect, useCallback } from 'react';
import { StaffDocument as StaffMember } from '@/lib/db/v4/schemas/per-staff-schemas';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  createStaffLocal,
  getAllStaffMembers,
  getStaffById,
  updateStaffById,
  deleteStaffById
} from '@/lib/services/new-staff-service';

interface UseSimpleStaffReturn {
  staff: StaffMember[];
  isLoading: boolean;
  error: Error | null;
  isReady: boolean;
  refreshStaff: () => Promise<void>;
  createStaff: (staffData: Partial<StaffMember>) => Promise<StaffMember>;
  updateStaff: (id: string, data: Partial<StaffMember>) => Promise<StaffMember>;
  deleteStaff: (id: string) => Promise<void>;
}

export function useSimpleStaff(): UseSimpleStaffReturn {
  const { db, isReady: isDbReady, error: dbError } = useUnifiedDB();
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Load staff members when database is ready
  useEffect(() => {
    if (isDbReady && db) {
      loadStaffMembers();
    }
  }, [isDbReady, db]);

  // Handle database errors
  useEffect(() => {
    if (dbError) {
      setError(dbError);
    }
  }, [dbError]);

  // Load staff members
  const loadStaffMembers = useCallback(async () => {
    if (!isDbReady) {
      console.warn('useSimpleStaff: Tried to load staff before DB is ready!');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await getAllStaffMembers();
      setStaff(result);
      setIsReady(true);
    } catch (err) {
      console.error('Error loading staff:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  }, [isDbReady]);

  // Refresh staff
  const refreshStaff = useCallback(async () => {
    await loadStaffMembers();
  }, [loadStaffMembers]);

  // Create staff
  const createStaff = useCallback(async (staffData: Partial<StaffMember>): Promise<StaffMember> => {
    // Runtime check for required fields
    if (!staffData.name || typeof staffData.name !== 'string') {
      throw new Error('Staff name is required and must be a string');
    }
    if (!staffData.role || typeof staffData.role !== 'string') {
      throw new Error('Staff role is required and must be a string');
    }
    if (!staffData.status || (staffData.status !== 'ACTIVE' && staffData.status !== 'INACTIVE')) {
      throw new Error('Staff status is required and must be "ACTIVE" or "INACTIVE"');
    }
    if (!staffData.paymentConfig || typeof staffData.paymentConfig !== 'object') {
      throw new Error('Staff paymentConfig is required');
    }
    try {
      const newStaff = await createStaffLocal(staffData as StaffMember);
      await refreshStaff();
      return newStaff;
    } catch (err) {
      console.error('Error creating staff:', err);
      throw err;
    }
  }, [refreshStaff]);

  // Update staff
  const updateStaff = useCallback(async (id: string, data: Partial<StaffMember>): Promise<StaffMember> => {
    try {
      const updatedStaff = await updateStaffById(id, data);
      await refreshStaff();
      return updatedStaff;
    } catch (err) {
      console.error('Error updating staff:', err);
      throw err;
    }
  }, [refreshStaff]);

  // Delete staff
  const deleteStaff = useCallback(async (id: string): Promise<void> => {
    try {
      await deleteStaffById(id);
      await refreshStaff();
    } catch (err) {
      console.error('Error deleting staff:', err);
      throw err;
    }
  }, [refreshStaff]);

  return {
    staff,
    isLoading,
    error,
    isReady,
    refreshStaff,
    createStaff,
    updateStaff,
    deleteStaff
  };
}
