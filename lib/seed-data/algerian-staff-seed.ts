import { v4 as uuidv4 } from 'uuid';

// Helper function to create unique IDs
const createId = () => uuidv4();

// Define shifts for Algerian restaurant
export const algerianShifts = [
  {
    id: createId(),
    name: 'Matin',
    startTime: '08:00',
    endTime: '16:00',
    color: '#4CAF50'
  },
  {
    id: createId(),
    name: 'Soir',
    startTime: '16:00',
    endTime: '00:00',
    color: '#2196F3'
  },
  {
    id: createId(),
    name: 'Service du Midi',
    startTime: '11:00',
    endTime: '15:00',
    color: '#FF9800'
  },
  {
    id: createId(),
    name: 'Service du Soir',
    startTime: '18:00',
    endTime: '22:00',
    color: '#F44336'
  }
];

// Create sample Algerian staff data
export const algerianStaff = [
  {
    id: createId(),
    name: '<PERSON>',
    role: 'MANAGER',
    email: '<EMAIL>',
    phone: '0550123456',
    status: 'ACTIVE',
    startDate: new Date(2022, 0, 15).toISOString(),
    paymentConfig: {
      type: 'MONTHLY',
      baseSalary: 60000,
      paymentDay: 1
    },
    presence: {
      isPresent: false,
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0,
      attendanceHistory: []
    },
    canAccessApp: true,
    permissions: {
      pages: {
        menu: true,
        orders: true,
        finance: true,
        inventory: true,
        staff: true,
        settings: true,
        suppliers: true
      }
    }
  },
  {
    id: createId(),
    name: 'Amina Khelifi',
    role: 'CHEF',
    email: '<EMAIL>',
    phone: '0661234567',
    status: 'ACTIVE',
    startDate: new Date(2022, 2, 10).toISOString(),
    paymentConfig: {
      type: 'MONTHLY',
      baseSalary: 50000,
      paymentDay: 1
    },
    presence: {
      isPresent: false,
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0,
      attendanceHistory: []
    },
    canAccessApp: true,
    permissions: {
      pages: {
        menu: true,
        orders: true,
        finance: false,
        inventory: true,
        staff: false,
        settings: false,
        suppliers: true
      }
    }
  },
  {
    id: createId(),
    name: 'Fatima Zahra',
    role: 'WAITER',
    email: '<EMAIL>',
    phone: '0551234567',
    status: 'ACTIVE',
    startDate: new Date(2022, 6, 20).toISOString(),
    paymentConfig: {
      type: 'WEEKLY',
      baseSalary: 8000,
      paymentDay: 5 // Friday
    },
    presence: {
      isPresent: false,
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0,
      attendanceHistory: []
    },
    canAccessApp: true,
    permissions: {
      pages: {
        menu: false,
        orders: true,
        finance: false,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    }
  },
  {
    id: createId(),
    name: 'Samira Hadj',
    role: 'CASHIER',
    email: '<EMAIL>',
    phone: '0771987654',
    status: 'ACTIVE',
    startDate: new Date(2022, 10, 5).toISOString(),
    paymentConfig: {
      type: 'MONTHLY',
      baseSalary: 35000,
      paymentDay: 1
    },
    presence: {
      isPresent: false,
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0,
      attendanceHistory: []
    },
    canAccessApp: true,
    permissions: {
      pages: {
        menu: false,
        orders: true,
        finance: true,
        inventory: false,
        staff: false,
        settings: false,
        suppliers: false
      }
    }
  }
];
