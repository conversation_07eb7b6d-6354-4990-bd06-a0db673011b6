import { NextRequest, NextResponse } from 'next/server';
import { jwtDecode } from 'jwt-decode';
import { JwtPayload } from './new-auth-service';

/**
 * Check if a user is restricted based on their JWT token
 */
export function checkRestrictionFromToken(token: string): boolean {
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    return decoded.restricted === true;
  } catch (error) {
    console.error('Error decoding token for restriction check:', error);
    // If we can't decode the token, consider it restricted for security
    return true;
  }
}

/**
 * Middleware to enforce restrictions on API routes
 */
export function enforceRestrictions(request: NextRequest): NextResponse | null {
  // Skip restriction enforcement for static/electron builds in offline mode
  if (process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static') {
    return null;
  }

  // Skip restriction enforcement for certain routes
  const path = request.nextUrl.pathname;
  const exemptRoutes = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/auth/check-restriction',
    '/api/health',
    '/api/admin' // Admin routes have their own authorization
  ];

  // Check if this route is exempt from restriction enforcement
  if (exemptRoutes.some(route => path.startsWith(route))) {
    return null;
  }

  // Get token from Authorization header or cookies
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') || request.cookies.get('token')?.value;

  if (!token) {
    // No token, let the main auth middleware handle this
    return null;
  }

  // Check if user is restricted
  if (checkRestrictionFromToken(token)) {
    console.log(`[Restriction Middleware] Blocking restricted user access to: ${path}`);
    return NextResponse.json(
      { 
        error: 'Access restricted', 
        message: 'Your account access has been restricted. Please contact support.',
        restricted: true 
      },
      { status: 403 }
    );
  }

  // User is not restricted, allow the request
  return null;
}

/**
 * Check if a user should be redirected to the subscription page
 */
export function shouldRedirectToSubscription(request: NextRequest): NextResponse | null {
  // Skip for static/electron builds
  if (process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static') {
    return null;
  }

  const path = request.nextUrl.pathname;
  
  // Don't redirect if already on subscription page or auth pages
  if (path === '/subscription' || path.startsWith('/auth') || path.startsWith('/api/')) {
    return null;
  }

  // Get token from Authorization header or cookies
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') || request.cookies.get('token')?.value;

  if (!token) {
    return null;
  }

  // Check if user is restricted
  if (checkRestrictionFromToken(token)) {
    console.log(`[Restriction Middleware] Redirecting restricted user to subscription page from: ${path}`);
    return NextResponse.redirect(new URL('/subscription', request.url));
  }

  return null;
}