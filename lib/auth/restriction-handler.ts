/**
 * Client-side restriction handling utilities
 */

export function handleRestrictionResponse(response: any) {
  if (response.restricted || response.error === 'Access restricted') {
    // Clear local auth data
    localStorage.removeItem('auth_data');
    localStorage.removeItem('auth_token');
    
    // Redirect to subscription page
    window.location.href = '/subscription';
    return true;
  }
  return false;
}

export function checkTokenRestriction(): boolean {
  try {
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      const parsed = JSON.parse(authData);
      return parsed.user?.restricted === true;
    }
  } catch (error) {
    console.error('Error checking token restriction:', error);
  }
  return false;
}

export function redirectIfRestricted(): boolean {
  if (checkTokenRestriction()) {
    window.location.href = '/subscription';
    return true;
  }
  return false;
}