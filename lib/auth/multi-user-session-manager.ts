/**
 * 🔄 Multi-User Session Manager
 * 
 * Manages multiple authenticated users on the same device/restaurant
 * Features:
 * - Persistent authentication (no timeout)
 * - Offline user switching
 * - Same restaurant database for all users
 * - Secure session storage
 */

import { jwtDecode } from 'jwt-decode';
import { JwtPayload, User } from './new-auth-service';

export interface UserSession {
  id: string;
  user: User;
  token: string;
  refreshToken?: string;
  lastActive: string;
  isOnline: boolean;
  loginTimestamp: string;
  deviceFingerprint: string;
}

export interface SessionStorage {
  activeSessionId: string | null;
  sessions: Record<string, UserSession>;
  restaurantId: string;
  deviceId: string;
  lastSync: string;
}

export class MultiUserSessionManager {
  private static instance: MultiUserSessionManager;
  private storageKey = 'multi_user_sessions';
  private storage: SessionStorage;
  private listeners: Set<(sessions: UserSession[], activeSession: UserSession | null) => void> = new Set();

  private constructor() {
    this.storage = this.loadStorage();
    this.generateDeviceFingerprint();
  }

  static getInstance(): MultiUserSessionManager {
    if (!MultiUserSessionManager.instance) {
      MultiUserSessionManager.instance = new MultiUserSessionManager();
    }
    return MultiUserSessionManager.instance;
  }

  /**
   * 🔐 Add or update a user session
   */
  async addSession(user: User, token: string, refreshToken?: string): Promise<void> {
    const deviceFingerprint = await this.getDeviceFingerprint();
    const sessionId = `session_${user.id}_${Date.now()}`;

    const session: UserSession = {
      id: sessionId,
      user,
      token,
      refreshToken,
      lastActive: new Date().toISOString(),
      isOnline: true,
      loginTimestamp: new Date().toISOString(),
      deviceFingerprint
    };

    // Check if we're switching to a different restaurant
    const currentRestaurantId = this.storage.restaurantId;
    const newRestaurantId = user.restaurantId;
    const isRestaurantSwitch = currentRestaurantId && newRestaurantId && currentRestaurantId !== newRestaurantId;

    if (isRestaurantSwitch) {
      console.log(`🏪 [addSession] Restaurant switch detected: ${currentRestaurantId} -> ${newRestaurantId}`);

      // Import and call the restaurant ID cleanup function
      if (typeof window !== 'undefined') {
        import('@/lib/db/v4/utils/restaurant-id').then(({ updateRestaurantIdForAccountSwitch }) => {
          updateRestaurantIdForAccountSwitch(newRestaurantId);
        }).catch(error => {
          console.error('❌ [addSession] Failed to update restaurant ID:', error);
        });
      }
    }

    // Set restaurant ID from first user if not set, or update if switching
    if (!this.storage.restaurantId && user.restaurantId) {
      this.storage.restaurantId = user.restaurantId;
    } else if (isRestaurantSwitch) {
      this.storage.restaurantId = newRestaurantId;
    }

    // Remove any existing session for this user
    this.removeUserSessions(user.id);

    // Add new session
    this.storage.sessions[sessionId] = session;
    this.storage.activeSessionId = sessionId;

    this.saveStorage();
    this.notifyListeners();

    console.log(`🔄 Added session for user: ${user.name} (${user.role})`);
    if (isRestaurantSwitch) {
      console.log(`🏪 Restaurant context updated to: ${newRestaurantId}`);
    }
  }

  /**
   * 🔄 Switch to a different user session
   */
  switchToUser(userId: string): boolean {
    const session = this.findSessionByUserId(userId);
    if (!session) {
      console.warn(`❌ No session found for user: ${userId}`);
      return false;
    }

    // Get current and new restaurant IDs
    const currentSession = this.getActiveSession();
    const currentRestaurantId = currentSession?.user?.restaurantId;
    const newRestaurantId = session.user.restaurantId;

    // Check if we're switching to a different restaurant
    const isRestaurantSwitch = currentRestaurantId && newRestaurantId && currentRestaurantId !== newRestaurantId;

    if (isRestaurantSwitch) {
      console.log(`🏪 [switchToUser] Restaurant switch detected: ${currentRestaurantId} -> ${newRestaurantId}`);

      // Import and call the restaurant ID cleanup function
      // This ensures the database system uses the correct restaurant ID
      if (typeof window !== 'undefined') {
        import('@/lib/db/v4/utils/restaurant-id').then(({ updateRestaurantIdForAccountSwitch }) => {
          updateRestaurantIdForAccountSwitch(newRestaurantId);
        }).catch(error => {
          console.error('❌ [switchToUser] Failed to update restaurant ID:', error);
        });
      }
    }

    // Update last active time
    session.lastActive = new Date().toISOString();
    this.storage.activeSessionId = session.id;

    // Update restaurant ID in storage if switching restaurants
    if (newRestaurantId && (isRestaurantSwitch || !this.storage.restaurantId)) {
      this.storage.restaurantId = newRestaurantId;
    }

    // Set cookies for middleware compatibility
    this.setCookiesForSession(session);

    this.saveStorage();
    this.notifyListeners();

    console.log(`🔄 Switched to user: ${session.user.name} (${session.user.role})`);
    if (isRestaurantSwitch) {
      console.log(`🏪 Restaurant context updated to: ${newRestaurantId}`);
    }
    return true;
  }

  /**
   * 👤 Get current active session
   */
  getActiveSession(): UserSession | null {
    if (!this.storage.activeSessionId) return null;
    return this.storage.sessions[this.storage.activeSessionId] || null;
  }

  /**
   * 👥 Get all user sessions
   */
  getAllSessions(): UserSession[] {
    return Object.values(this.storage.sessions);
  }

  /**
   * 🗑️ Remove a user session
   */
  removeSession(sessionId: string): void {
    if (this.storage.sessions[sessionId]) {
      const user = this.storage.sessions[sessionId].user;
      delete this.storage.sessions[sessionId];
      
      // If this was the active session, clear it
      if (this.storage.activeSessionId === sessionId) {
        this.storage.activeSessionId = null;
      }
      
      this.saveStorage();
      this.notifyListeners();
      
      console.log(`🗑️ Removed session for user: ${user.name}`);
    }
  }

  /**
   * 🗑️ Remove all sessions for a specific user
   */
  removeUserSessions(userId: string): void {
    const sessionsToRemove = Object.entries(this.storage.sessions)
      .filter(([_, session]) => session.user.id === userId)
      .map(([sessionId]) => sessionId);

    sessionsToRemove.forEach(sessionId => {
      delete this.storage.sessions[sessionId];
      if (this.storage.activeSessionId === sessionId) {
        this.storage.activeSessionId = null;
      }
    });

    if (sessionsToRemove.length > 0) {
      this.saveStorage();
      this.notifyListeners();
    }
  }

  /**
   * 🔍 Find session by user ID
   */
  findSessionByUserId(userId: string): UserSession | null {
    return Object.values(this.storage.sessions)
      .find(session => session.user.id === userId) || null;
  }

  /**
   * 🔍 Check if user has an active session
   */
  hasUserSession(userId: string): boolean {
    return this.findSessionByUserId(userId) !== null;
  }

  /**
   * 🔄 Update session token (for token refresh)
   */
  updateSessionToken(sessionId: string, token: string, refreshToken?: string): void {
    const session = this.storage.sessions[sessionId];
    if (session) {
      session.token = token;
      if (refreshToken) {
        session.refreshToken = refreshToken;
      }
      session.lastActive = new Date().toISOString();
      session.isOnline = true;
      
      this.saveStorage();
      this.notifyListeners();
    }
  }

  /**
   * 📱 Mark session as offline (for offline mode)
   */
  markSessionOffline(sessionId: string): void {
    const session = this.storage.sessions[sessionId];
    if (session) {
      session.isOnline = false;
      session.lastActive = new Date().toISOString();
      
      this.saveStorage();
      this.notifyListeners();
    }
  }

  /**
   * 🌐 Mark session as online
   */
  markSessionOnline(sessionId: string): void {
    const session = this.storage.sessions[sessionId];
    if (session) {
      session.isOnline = true;
      session.lastActive = new Date().toISOString();
      
      this.saveStorage();
      this.notifyListeners();
    }
  }

  /**
   * 🏪 Get restaurant ID (same for all users)
   */
  getRestaurantId(): string | null {
    return this.storage.restaurantId || null;
  }

  /**
   * 🔧 Validate session token
   */
  isSessionValid(session: UserSession): boolean {
    if (!session.token) return false;

    try {
      const decoded = jwtDecode<JwtPayload>(session.token);
      const currentTime = Math.floor(Date.now() / 1000);
      
      // For persistent sessions, we don't check expiration
      // Instead, we rely on refresh tokens or offline mode
      return true;
    } catch (error) {
      console.warn(`❌ Invalid token for session: ${session.id}`);
      return false;
    }
  }

  /**
   * 🧹 Clean up invalid sessions
   */
  cleanupInvalidSessions(): void {
    const validSessions: Record<string, UserSession> = {};
    let hasChanges = false;

    Object.entries(this.storage.sessions).forEach(([sessionId, session]) => {
      if (this.isSessionValid(session)) {
        validSessions[sessionId] = session;
      } else {
        hasChanges = true;
        console.log(`🧹 Removing invalid session: ${session.user.name}`);
      }
    });

    if (hasChanges) {
      this.storage.sessions = validSessions;
      
      // Check if active session was removed
      if (this.storage.activeSessionId && !validSessions[this.storage.activeSessionId]) {
        this.storage.activeSessionId = null;
      }
      
      this.saveStorage();
      this.notifyListeners();
    }
  }

  /**
   * 👂 Subscribe to session changes
   */
  subscribe(listener: (sessions: UserSession[], activeSession: UserSession | null) => void): () => void {
    this.listeners.add(listener);
    
    // Immediately call with current state
    listener(this.getAllSessions(), this.getActiveSession());
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 🍪 Set cookies for session (middleware compatibility)
   */
  private setCookiesForSession(session: UserSession): void {
    if (typeof document !== 'undefined') {
      const maxAge = 7 * 24 * 60 * 60; // 7 days in seconds
      
      // Set auth token cookie
      if (session.token) {
        document.cookie = `auth_token=${session.token}; path=/; max-age=${maxAge}; SameSite=Strict`;
        console.log('🍪 Set auth_token cookie for middleware');
      }
      
      // Set refresh token cookie if available
      if (session.refreshToken) {
        document.cookie = `refresh_token=${session.refreshToken}; path=/; max-age=${maxAge}; SameSite=Strict`;
        console.log('🍪 Set refresh_token cookie');
      }
    }
  }

  /**
   * 🔔 Notify all listeners of changes
   */
  private notifyListeners(): void {
    const sessions = this.getAllSessions();
    const activeSession = this.getActiveSession();
    
    this.listeners.forEach(listener => {
      try {
        listener(sessions, activeSession);
      } catch (error) {
        console.error('❌ Error in session listener:', error);
      }
    });
  }

  /**
   * 💾 Load storage from localStorage
   */
  private loadStorage(): SessionStorage {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      // Return default storage for server-side rendering
      return {
        activeSessionId: null,
        sessions: {},
        restaurantId: '',
        deviceId: this.generateDeviceId(),
        lastSync: new Date().toISOString()
      };
    }

    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          activeSessionId: parsed.activeSessionId || null,
          sessions: parsed.sessions || {},
          restaurantId: parsed.restaurantId || '',
          deviceId: parsed.deviceId || this.generateDeviceId(),
          lastSync: parsed.lastSync || new Date().toISOString()
        };
      }
    } catch (error) {
      console.warn('❌ Error loading session storage:', error);
    }

    return {
      activeSessionId: null,
      sessions: {},
      restaurantId: '',
      deviceId: this.generateDeviceId(),
      lastSync: new Date().toISOString()
    };
  }

  /**
   * 💾 Save storage to localStorage
   */
  private saveStorage(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return; // Skip saving on server side
    }

    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.storage));
    } catch (error) {
      console.error('❌ Error saving session storage:', error);
    }
  }

  /**
   * 🆔 Generate unique device ID
   */
  private generateDeviceId(): string {
    return `device_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * 👆 Generate device fingerprint
   */
  private async generateDeviceFingerprint(): Promise<void> {
    if (!this.storage.deviceId) {
      this.storage.deviceId = this.generateDeviceId();
      this.saveStorage();
    }
  }

  /**
   * 👆 Get device fingerprint
   */
  private async getDeviceFingerprint(): Promise<string> {
    return this.storage.deviceId;
  }

  /**
   * 🧹 Clear all sessions (logout all users)
   */
  clearAllSessions(): void {
    this.storage.sessions = {};
    this.storage.activeSessionId = null;
    this.saveStorage();
    this.notifyListeners();
    
    console.log('🧹 Cleared all user sessions');
  }

  /**
   * 📊 Get session statistics
   */
  getSessionStats(): {
    totalSessions: number;
    onlineSessions: number;
    offlineSessions: number;
    roles: Record<string, number>;
    lastActiveSession: string | null;
  } {
    const sessions = this.getAllSessions();
    const roles: Record<string, number> = {};
    
    sessions.forEach(session => {
      roles[session.user.role] = (roles[session.user.role] || 0) + 1;
    });

    const lastActiveSession = sessions
      .sort((a, b) => new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime())[0];

    return {
      totalSessions: sessions.length,
      onlineSessions: sessions.filter(s => s.isOnline).length,
      offlineSessions: sessions.filter(s => !s.isOnline).length,
      roles,
      lastActiveSession: lastActiveSession?.user.name || null
    };
  }
}

// Export singleton instance
export const multiUserSessionManager = MultiUserSessionManager.getInstance(); 