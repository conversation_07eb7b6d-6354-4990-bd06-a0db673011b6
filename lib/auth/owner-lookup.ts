import clientPromise from '@/lib/mongodb';
import { AuthUser } from '@/lib/db/v4/schemas/auth-schema';

// Cache for owner restriction status to avoid frequent DB queries
const ownerRestrictionCache = new Map<string, { restricted: boolean; timestamp: number }>();
const CACHE_DURATION = 30 * 1000; // 30 seconds cache

/**
 * Get the restaurant owner for a given restaurant ID
 */
export async function getRestaurantOwner(restaurantId: string): Promise<AuthUser | null> {
  try {
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const usersCollection = db.collection<AuthUser>('users');

    const owner = await usersCollection.findOne({
      restaurantId: restaurantId,
      role: 'owner'
    });

    return owner;
  } catch (error) {
    console.error('❌ [OwnerLookup] Error fetching restaurant owner:', error);
    return null;
  }
}

/**
 * Check if a restaurant owner is restricted (with caching)
 */
export async function isRestaurantOwnerRestricted(restaurantId: string): Promise<boolean> {
  try {
    // Check cache first
    const cached = ownerRestrictionCache.get(restaurantId);
    const now = Date.now();
    
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      console.log('🔒 [OwnerLookup] Using cached owner restriction status:', cached.restricted);
      return cached.restricted;
    }

    // Fetch from database
    const owner = await getRestaurantOwner(restaurantId);
    const isRestricted = owner?.restricted === true;

    // Update cache
    ownerRestrictionCache.set(restaurantId, {
      restricted: isRestricted,
      timestamp: now
    });

    console.log(`🔒 [OwnerLookup] Owner restriction status for restaurant ${restaurantId}:`, isRestricted);
    return isRestricted;
  } catch (error) {
    console.error('❌ [OwnerLookup] Error checking owner restriction:', error);
    return false; // Default to not restricted on error
  }
}

/**
 * Clear the owner restriction cache for a specific restaurant
 */
export function clearOwnerRestrictionCache(restaurantId?: string): void {
  if (restaurantId) {
    ownerRestrictionCache.delete(restaurantId);
    console.log(`🔒 [OwnerLookup] Cleared cache for restaurant ${restaurantId}`);
  } else {
    ownerRestrictionCache.clear();
    console.log('🔒 [OwnerLookup] Cleared all owner restriction cache');
  }
}

/**
 * Check if a user should be restricted based on their role and restaurant owner status
 */
export async function checkUserRestriction(user: AuthUser): Promise<{
  isRestricted: boolean;
  reason: 'user' | 'owner' | null;
  ownerName?: string;
}> {
  try {
    console.log(`🔒 [OwnerLookup] Checking restriction for user: ${user.name}, role: ${user.role}`);
    
    // If user is owner, check their own restriction status
    if (user.role === 'owner') {
      const isRestricted = user.restricted === true;
      console.log(`🔒 [OwnerLookup] Owner ${user.name} restriction status: ${isRestricted}`);
      return {
        isRestricted,
        reason: isRestricted ? 'user' : null
      };
    }

    // If user is staff, check both their own status and owner status
    const userRestricted = user.restricted === true;
    console.log(`🔒 [OwnerLookup] Staff user ${user.name} direct restriction: ${userRestricted}`);
    
    if (!user.restaurantId) {
      console.log(`🔒 [OwnerLookup] Staff user ${user.name} has no restaurantId, cannot check owner`);
      return {
        isRestricted: userRestricted,
        reason: userRestricted ? 'user' : null
      };
    }

    const ownerRestricted = await isRestaurantOwnerRestricted(user.restaurantId);
    console.log(`🔒 [OwnerLookup] Owner restriction for restaurant ${user.restaurantId}: ${ownerRestricted}`);

    let ownerName: string | undefined;
    if (ownerRestricted) {
      const owner = await getRestaurantOwner(user.restaurantId);
      ownerName = owner?.name;
      console.log(`🔒 [OwnerLookup] Restricted owner name: ${ownerName}`);
    }

    const finalRestricted = userRestricted || ownerRestricted;
    const finalReason = userRestricted ? 'user' : ownerRestricted ? 'owner' : null;
    
    console.log(`🔒 [OwnerLookup] Final restriction result: ${finalRestricted}, reason: ${finalReason}`);

    return {
      isRestricted: finalRestricted,
      reason: finalReason,
      ownerName
    };
  } catch (error) {
    console.error('❌ [OwnerLookup] Error checking user restriction:', error);
    // On error, default to not restricted to avoid blocking legitimate users
    return {
      isRestricted: false,
      reason: null
    };
  }
}