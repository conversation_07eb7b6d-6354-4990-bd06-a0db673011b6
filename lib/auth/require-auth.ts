import { NextRequest } from 'next/server';
import { verifyJwtAuth, AuthResult } from '@/lib/api-middleware';

// User object returned after authentication
export interface AuthUser {
  id: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
}

/**
 * Require authentication for API routes
 * This function extracts and verifies the JWT token from the request
 * @param req Optional NextRequest object (will create one if not provided)
 * @returns User data if authenticated, null otherwise
 */
export async function requireAuth(req?: NextRequest): Promise<AuthUser | null> {
  try {
    // If no request is provided, create one from the current request context
    const request = req || new NextRequest(new Request(globalThis.location.href));
    
    // Use the existing verifyJwtAuth function
    const authResult = await verifyJwtAuth(request);
    
    if (!authResult.success || !authResult.user) {
      return null;
    }
    
    // Return user information
    return authResult.user;
  } catch (error) {
    console.error('Error in requireAuth:', error);
    return null;
  }
} 