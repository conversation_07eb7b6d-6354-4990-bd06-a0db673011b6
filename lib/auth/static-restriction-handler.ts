/**
 * Restriction handling for static/offline apps
 * Since static apps have no API routes, restrictions are enforced through:
 * 1. Sync blocking when connecting to remote server
 * 2. Client-side checks against local user data
 * 3. Remote auth checks when online
 */

import { AuthUser } from '@/lib/db/v4/schemas/auth-schema';

export interface StaticRestrictionCheck {
  isRestricted: boolean;
  reason?: string;
  shouldRedirect?: boolean;
}

/**
 * Check if user is restricted in static/offline mode
 */
export function checkStaticRestriction(user: AuthUser | null): StaticRestrictionCheck {
  if (!user) {
    return { isRestricted: true, reason: 'No user data', shouldRedirect: true };
  }

  // Check local user restriction status
  if (user.restricted === true) {
    return { 
      isRestricted: true, 
      reason: 'User account is restricted', 
      shouldRedirect: true 
    };
  }

  return { isRestricted: false };
}

/**
 * Check restriction against remote server (when online)
 */
export async function checkRemoteRestriction(
  userId: string, 
  authToken?: string
): Promise<StaticRestrictionCheck> {
  try {
    // Try to reach the remote auth server
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://bistro.icu'}/api/auth/check-restriction`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { 'Authorization': `Bearer ${authToken}` })
      },
      body: JSON.stringify({ userId, role: 'owner' }),
      // Short timeout for offline-first apps
      signal: AbortSignal.timeout(5000)
    });

    if (!response.ok) {
      // Server error, fall back to local check
      return { isRestricted: false, reason: 'Server unavailable, using local data' };
    }

    const data = await response.json();
    
    if (data.isRestricted) {
      return { 
        isRestricted: true, 
        reason: 'Account restricted by admin', 
        shouldRedirect: true 
      };
    }

    return { isRestricted: false };

  } catch (error) {
    console.log('[Static Restriction] Remote check failed, using local data:', error);
    // Network error, fall back to local check
    return { isRestricted: false, reason: 'Offline mode, using local data' };
  }
}

/**
 * Block PouchDB sync for restricted users
 */
export function shouldBlockSync(user: AuthUser | null): boolean {
  const check = checkStaticRestriction(user);
  return check.isRestricted;
}

/**
 * Handle restriction in static app
 */
export function handleStaticRestriction(check: StaticRestrictionCheck): void {
  if (check.isRestricted && check.shouldRedirect) {
    // Clear local auth data
    localStorage.removeItem('auth_data');
    localStorage.removeItem('auth_token');
    
    // Show restriction message
    console.log('[Static Restriction] User restricted:', check.reason);
    
    // In static apps, redirect to subscription page
    if (typeof window !== 'undefined') {
      window.location.href = '/subscription';
    }
  }
}

/**
 * Periodic restriction check for static apps
 */
export function startPeriodicRestrictionCheck(userId: string, intervalMs: number = 300000): () => void {
  const interval = setInterval(async () => {
    try {
      const check = await checkRemoteRestriction(userId);
      if (check.isRestricted) {
        handleStaticRestriction(check);
        clearInterval(interval);
      }
    } catch (error) {
      console.log('[Static Restriction] Periodic check failed:', error);
    }
  }, intervalMs);

  // Return cleanup function
  return () => clearInterval(interval);
}