/**
 * Utility functions for handling user roles consistently
 */

import { User } from './new-auth-service';

/**
 * Check if a user is an admin
 */
export function isAdmin(user?: User | null): boolean {
  return user?.role === 'admin';
}

/**
 * Check if a user is an owner
 */
export function isOwner(user?: User | null): boolean {
  return user?.role === 'owner';
}

/**
 * Check if a user is a staff member
 */
export function isStaff(user?: User | null): boolean {
  return user?.role === 'staff';
}

/**
 * Check if a user can manage staff (admin or owner)
 */
export function canManageStaff(user?: User | null): boolean {
  return isAdmin(user) || isOwner(user);
}

/**
 * Check if a user can manage restaurant settings (admin or owner)
 */
export function canManageSettings(user?: User | null): boolean {
  return isAdmin(user) || isOwner(user);
}

/**
 * Get a display name for a role
 */
export function getRoleDisplayName(role?: string): string {
  switch (role) {
    case 'owner':
      return 'Owner';
    case 'admin':
      return 'Admin';
    case 'staff':
      return 'Staff';
    default:
      return 'User';
  }
} 