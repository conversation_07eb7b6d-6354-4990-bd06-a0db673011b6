import { NextResponse } from 'next/server';
import { getMongoUserById } from '@/lib/auth/mongo-auth-ops';
import { StaffDocument } from '@/lib/types/unified-staff';
import { getStaffMember } from '@/lib/db/v4/operations/per-staff-ops';
import { jwtDecode } from 'jwt-decode';

/**
 * Extract user from request using token
 */
async function getUserFromRequest(req: Request) {
  try {
    // Get authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    // Extract token
    const token = authHeader.substring(7);
    if (!token) {
      return null;
    }

    // Decode token
    const decoded = jwtDecode<{sub: string; name: string}>(token);
    if (!decoded || !decoded.sub) {
      return null;
    }

    return {
      id: decoded.sub,
      name: decoded.name
    };
  } catch (error) {
    console.error("Error extracting user from request:", error);
    return null;
  }
}

/**
 * Check if the user has permission to access a specific page/feature
 * @param userId The user ID to check permissions for
 * @param page The page name to check permission for
 * @returns True if the user has permission, false otherwise
 */
export async function checkUserPermission(userId: string, page: string): Promise<boolean> {
  try {
    // Get the user document
    const user = await getMongoUserById(userId);

    if (!user) {
      console.warn(`API-PERMISSIONS: User with ID ${userId} not found`);
      return false;
    }

    // If user is an owner or admin, they have access to everything
    if (user.role === 'owner' || user.role === 'admin') {
      console.log(`API-PERMISSIONS: User ${userId} is ${user.role}, granting full access`);
      return true;
    }

    // For staff users, we need to fetch permissions from the restaurant database
    if (user.role === 'staff' && user.restaurantId) {
      try {
        // Get the staffId from metadata - this should match the staff document ID
        const staffId = user.metadata?.staffId || user._id;

        if (!staffId) {
          console.warn(`API-PERMISSIONS: Staff user ${userId} has no staffId in metadata`);
          return false;
        }

        // V4: Get the staff member and check permissions
        const staffMember = await getStaffMember(staffId);
        if (!staffMember) {
          console.warn(`API-PERMISSIONS: No staff member found with ID ${staffId}`);
          return false;
        }
        // Check if the staff member has permissions for this page
        const hasPermission = !!(staffMember.permissions?.pages?.[page as keyof typeof staffMember.permissions.pages]);
        console.log(`API-PERMISSIONS: Permission check for ${userId} on page ${page}: ${hasPermission ? 'GRANTED' : 'DENIED'}`);
        return hasPermission;
      } catch (error) {
        console.error(`API-PERMISSIONS: Error fetching staff permissions for user ${userId}:`, error);
        return false;
      }
    }

    // If we get here, the user is not an owner/admin and not a staff member with permissions
    console.warn(`API-PERMISSIONS: User ${userId} has no valid role or restaurant ID`);
    return false;
  } catch (error) {
    console.error(`API-PERMISSIONS: Error checking permissions for user ${userId}:`, error);
    return false;
  }
}

/**
 * Check permissions in a route handler
 * @param request The request object
 * @param requiredPermission The permission required to access the endpoint
 * @returns A Response object if unauthorized, null if authorized
 */
export async function checkApiPermission(request: Request, requiredPermission: string) {
  try {
    // Get user from request
    const user = await getUserFromRequest(request);

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user has the required permission
    const hasPermission = await checkUserPermission(user.id, requiredPermission);

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden - Insufficient permissions' }, { status: 403 });
    }

    // If we reach here, the user has permission
    return null;
  } catch (error) {
    console.error('Error in API permission check:', error);
    return NextResponse.json({ error: 'Server error during permission check' }, { status: 500 });
  }
}