/**
 * Permission-based authentication system types
 */

// Restaurant entity
export interface Restaurant {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

// Page-level permissions
export interface PagePermission {
  page: string;     // e.g., 'menu', 'orders', 'staff', 'settings'
  access: boolean;  // Whether user can access this page
}

// Component-level permissions
export interface ComponentPermission {
  page: string;       // Parent page
  component: string;  // Component identifier, e.g., 'add-menu-item', 'delete-order'
  access: boolean;    // Whether user can access this component
}

// Action-level permissions
export interface ActionPermission {
  action: string;     // Action identifier, e.g., 'create', 'read', 'update', 'delete'
  resource: string;   // Resource type, e.g., 'menu-item', 'order', 'staff'
  access: boolean;    // Whether user can perform this action
}

// Combined permissions
export interface Permissions {
  pages: PagePermission[];
  components: ComponentPermission[];
  actions: ActionPermission[];
}

// Default permission sets
export const DEFAULT_OWNER_PERMISSIONS: Permissions = {
  pages: [
    { page: 'menu', access: true },
    { page: 'orders', access: true },
    { page: 'inventory', access: true },
    { page: 'suppliers', access: true },
    { page: 'staff', access: true },
    { page: 'settings', access: true },
    { page: 'analytics', access: true },
  ],
  components: [
    // Menu components
    { page: 'menu', component: 'add-category', access: true },
    { page: 'menu', component: 'edit-category', access: true },
    { page: 'menu', component: 'delete-category', access: true },
    { page: 'menu', component: 'add-item', access: true },
    { page: 'menu', component: 'edit-item', access: true },
    { page: 'menu', component: 'delete-item', access: true },
    { page: 'menu', component: 'configure-prices', access: true },
    
    // Order components
    { page: 'orders', component: 'create-order', access: true },
    { page: 'orders', component: 'edit-order', access: true },
    { page: 'orders', component: 'cancel-order', access: true },
    { page: 'orders', component: 'process-payment', access: true },
    
    // Staff components
    { page: 'staff', component: 'add-staff', access: true },
    { page: 'staff', component: 'edit-staff', access: true },
    { page: 'staff', component: 'delete-staff', access: true },
    { page: 'staff', component: 'manage-permissions', access: true },
    
    // Settings components
    { page: 'settings', component: 'edit-restaurant', access: true },
    { page: 'settings', component: 'manage-integrations', access: true },
    { page: 'settings', component: 'backup-restore', access: true },
  ],
  actions: [
    // Menu actions
    { action: 'create', resource: 'menu-category', access: true },
    { action: 'read', resource: 'menu-category', access: true },
    { action: 'update', resource: 'menu-category', access: true },
    { action: 'delete', resource: 'menu-category', access: true },
    
    { action: 'create', resource: 'menu-item', access: true },
    { action: 'read', resource: 'menu-item', access: true },
    { action: 'update', resource: 'menu-item', access: true },
    { action: 'delete', resource: 'menu-item', access: true },
    
    // Order actions
    { action: 'create', resource: 'order', access: true },
    { action: 'read', resource: 'order', access: true },
    { action: 'update', resource: 'order', access: true },
    { action: 'delete', resource: 'order', access: true },
    
    // Staff actions
    { action: 'create', resource: 'staff', access: true },
    { action: 'read', resource: 'staff', access: true },
    { action: 'update', resource: 'staff', access: true },
    { action: 'delete', resource: 'staff', access: true },
    
    // Settings actions
    { action: 'update', resource: 'restaurant-settings', access: true },
  ],
};

// Default staff permissions - read only for most features
export const DEFAULT_STAFF_PERMISSIONS: Permissions = {
  pages: [
    { page: 'menu', access: true },
    { page: 'orders', access: true },
    { page: 'inventory', access: true },
    { page: 'suppliers', access: false },
    { page: 'staff', access: false },
    { page: 'settings', access: false },
    { page: 'analytics', access: false },
  ],
  components: [
    // Menu components - view only
    { page: 'menu', component: 'add-category', access: false },
    { page: 'menu', component: 'edit-category', access: false },
    { page: 'menu', component: 'delete-category', access: false },
    { page: 'menu', component: 'add-item', access: false },
    { page: 'menu', component: 'edit-item', access: false },
    { page: 'menu', component: 'delete-item', access: false },
    { page: 'menu', component: 'configure-prices', access: false },
    
    // Order components - can create/edit orders
    { page: 'orders', component: 'create-order', access: true },
    { page: 'orders', component: 'edit-order', access: true },
    { page: 'orders', component: 'cancel-order', access: false },
    { page: 'orders', component: 'process-payment', access: true },
  ],
  actions: [
    // Menu actions
    { action: 'create', resource: 'menu-category', access: false },
    { action: 'read', resource: 'menu-category', access: true },
    { action: 'update', resource: 'menu-category', access: false },
    { action: 'delete', resource: 'menu-category', access: false },
    
    { action: 'create', resource: 'menu-item', access: false },
    { action: 'read', resource: 'menu-item', access: true },
    { action: 'update', resource: 'menu-item', access: false },
    { action: 'delete', resource: 'menu-item', access: false },
    
    // Order actions
    { action: 'create', resource: 'order', access: true },
    { action: 'read', resource: 'order', access: true },
    { action: 'update', resource: 'order', access: true },
    { action: 'delete', resource: 'order', access: false },
  ],
};

// User entity with permissions
export interface User {
  id: string;
  name: string;
  email?: string;
  username?: string;
  passwordHash: string;
  restaurantId: string;
  position?: string;  // e.g., 'Server', 'Chef', 'Manager'
  role?: string;      // For backward compatibility
  active: boolean;
  permissions: Permissions;
  createdAt: string;
  updatedAt: string;
}

// Combined session type
export interface AuthSession {
  restaurant: Restaurant;
  user?: User;  // Optional during first auth step
  authLevel: 'restaurant' | 'user';
}

// Helper function to check if a user has access to a page
export function hasPageAccess(user: User, pageName: string): boolean {
  return user.permissions.pages.some(p => p.page === pageName && p.access);
}

// Helper function to check if a user has access to a component
export function hasComponentAccess(user: User, pageName: string, componentName: string): boolean {
  return user.permissions.components.some(c => 
    c.page === pageName && c.component === componentName && c.access
  );
}

// Helper function to check if a user has access to perform an action
export function hasActionAccess(user: User, actionName: string, resourceName: string): boolean {
  return user.permissions.actions.some(a => 
    a.action === actionName && a.resource === resourceName && a.access
  );
} 