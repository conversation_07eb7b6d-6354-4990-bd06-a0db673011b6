/**
 * Unified Staff Schema - Single Source of Truth
 * 
 * This file contains the definitive staff schema that replaces all conflicting schemas
 * across the codebase. It supports web, mobile, desktop platforms and all database
 * systems (PouchDB, CouchDB, MongoDB).
 * 
 * Key unifications:
 * - Consistent ID generation: UUID v4 format
 * - Standardized role definitions 
 * - Unified payment configuration structure
 * - Cross-platform compatible field definitions
 * - Consistent validation rules
 */

// ===== CORE TYPES =====

export type StaffRole = 
  | 'MANAGER' 
  | 'CHEF' 
  | 'WAITER' 
  | 'BARTENDER' 
  | 'HOST' 
  | 'KITCHEN_HELPER' 
  | 'CASHIER' 
  | 'CLEANER' 
  | 'DELIVERY'
  | 'OWNER'
  | 'ADMIN'
  | 'STAFF'
  | 'USER';

export type PaymentType = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'PER_SHIFT';

export type StaffStatus = 'ACTIVE' | 'INACTIVE';

export type AttendanceStatus = 'present' | 'late' | 'absent';

export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'CANCELLED';

export type BalanceType = 'ADVANCE' | 'DEDUCTION' | 'BONUS';

export type BaseLogic = 'ADDITIVE' | 'MINIMUM' | 'NONE';

// ===== ID PATTERNS =====

export const ID_PATTERNS = {
  STAFF_ID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
  STAFF_DOCUMENT_ID: /^staff:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
  SCHEDULE_DOCUMENT_ID: /^schedule:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
  ATTENDANCE_DOCUMENT_ID: /^attendance:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
  PAYMENT_DOCUMENT_ID: /^payment:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
  BALANCE_DOCUMENT_ID: /^staff_balance:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
  ALLOWANCE_DOCUMENT_ID: /^staff-allowance:[0-9a-f-]+:[0-9a-f-]+:[0-9]{4}-[0-9]{2}-[0-9]{2}$/
};

// ===== CORE INTERFACES =====

export interface PaymentConfig {
  type: PaymentType;
  baseSalary: number;
  shiftRate?: number;
  shiftRates?: Record<string, number>;
  paymentDay?: number;
  baseLogic?: BaseLogic;
  advanceRepayment?: {
    percentage: number;
    maxAmount: number;
  };
  nextPaymentDueDate?: string;
}

export interface ShiftConfig {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  color?: string;
}

export interface WeeklySchedule {
  monday: string[];
  tuesday: string[];
  wednesday: string[];
  thursday: string[];
  friday: string[];
  saturday: string[];
  sunday: string[];
}

export interface StaffPermissions {
  pages: {
    menu: boolean;
    orders: boolean;
    finance: boolean;
    analytics?: boolean;
    inventory: boolean;
    staff: boolean;
    settings: boolean;
    suppliers: boolean;
  };
  tabs?: {
    inventory?: {
      inventory?: boolean;
      subrecipes?: boolean;
      counts?: boolean;
      waste?: boolean;
    };
    staff?: {
      staff?: boolean;
      shifts_schedule?: boolean;
      attendance?: boolean;
      payments?: boolean;
    };
  };
}

export interface AttendanceRecord {
  id: string;
  date: string;
  staffId: string;
  staffName: string;
  shiftId?: string;
  shiftName?: string;
  attended?: boolean;
  status: AttendanceStatus;
  notes?: string;
  isPaid?: boolean;
}

export interface StaffPresence {
  staffId: string;
  isPresent: boolean;
  attendanceHistory: AttendanceRecord[];
  totalHoursThisWeek: number;
  totalHoursThisMonth: number;
}

export interface StaffSchedule {
  staffId: string;
  weeklySchedule: WeeklySchedule;
  effectiveFrom: string;
  effectiveTo?: string;
  isActive: boolean;
}

// ===== MAIN STAFF INTERFACE =====

export interface UnifiedStaffMember {
  // Core identity
  id: string;
  name: string;
  role: StaffRole;
  status: StaffStatus;
  
  // Contact information
  email?: string;
  phone?: string;
  contact?: string; // Backwards compatibility
  
  // Payment configuration
  paymentConfig: PaymentConfig;
  
  // Authentication
  userId?: string;
  hasUserAccount: boolean;
  username?: string;
  pendingAuth?: boolean;
  
  // Permissions
  permissions: StaffPermissions;
  
  // Optional related data (populated dynamically)
  presence?: StaffPresence;
  schedule?: StaffSchedule;
  shift?: ShiftConfig;
  
  // Database fields
  _id?: string;
  _rev?: string;
  type?: 'staff';
  
  // Legacy/compatibility fields
  startDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

// ===== DOCUMENT SCHEMAS =====

export interface StaffDocument {
  _id: string; // Format: staff:{uuid}
  _rev?: string;
  type: 'staff';
  id: string; // UUID v4
  name: string;
  role: StaffRole;
  contact?: string;
  email?: string;
  phone?: string;
  status: StaffStatus;
  paymentConfig: PaymentConfig;
  userId?: string;
  hasUserAccount: boolean;
  username?: string;
  pendingAuth?: boolean;
  permissions: StaffPermissions;
  createdAt?: string;
  updatedAt?: string;
}

export interface ScheduleDocument {
  _id: string; // Format: schedule:{uuid}
  _rev?: string;
  type: 'schedule';
  staffId: string;
  weeklySchedule: WeeklySchedule;
  effectiveFrom: string;
  effectiveTo?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface AttendanceDocument {
  _id: string; // Format: attendance:{uuid}
  _rev?: string;
  type: 'attendance';
  staffId: string;
  records: AttendanceRecord[];
  schemaVersion: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentDocument {
  _id: string; // Format: payment:YYYY-MM-DD-{uuid}
  _rev?: string;
  type: 'payment';
  staffId: string;
  amount: number;
  paymentType: 'SALARY' | 'BONUS' | 'ADVANCE' | 'DEDUCTION' | 'SHIFT_PAYMENT';
  paymentDate: string;
  periodStart?: string;
  periodEnd?: string;
  status: PaymentStatus;
  notes?: string;
  metadata?: {
    paidAttendanceIds?: string[];
    baseLogic?: BaseLogic;
    baseAmount?: number;
    shiftAmount?: number;
    totalShifts?: number;
    totalDays?: number;
    shiftBreakdown?: Array<{
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
      amount: number;
    }>;
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    advanceRepayment?: {
      amount: number;
      percentage: number;
      balanceBefore: number;
      balanceAfter: number;
    };
    calculationBreakdown?: {
      baseSalary: number;
      shiftEarnings: number;
      bonusAmount: number;
      deductionAmount: number;
      advanceDeduction: number;
      finalAmount: number;
    };
    linkedPayrollId?: string;
    completedAt?: string;
    // Edit tracking
    edited?: boolean;
    editedAt?: string;
    originalAmount?: number;
    editReason?: string;
    // Correction tracking
    isCorrection?: boolean;
    originalPaymentId?: string;
    correctionReason?: string;
    hasCorrectionIds?: string[];
    // Void tracking
    voided?: boolean;
    voidedAt?: string;
    voidReason?: string;
    // Replacement tracking
    isReplacement?: boolean;
    replacesPaymentId?: string;
    replacementPaymentId?: string;
    // Reversal tracking
    isReversal?: boolean;
    reversalOf?: string;
    reversalReason?: string;
    mirrorPaymentId?: string;
    // Adjustment tracking
    isAdjustment?: boolean;
    adjustsPaymentId?: string;
    adjustmentReason?: string;
    adjustmentAmount?: number;
    adjustmentIds?: string[];
    // Advance repayment flag
    isAdvanceRepayment?: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface BalanceDocument {
  _id: string; // Format: staff_balance:YYYY-MM-DD-{uuid}
  _rev?: string;
  type: 'staff_balance';
  staffId: string;
  balanceType: BalanceType;
  amount: number;
  reason: string;
  date: string;
  isUsed: boolean;
  usedInPaymentId?: string;
  usedAt?: string;
  createdAt: string;
  updatedAt?: string;
}

// ===== JSON SCHEMAS FOR VALIDATION =====

export const unifiedStaffDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'id', 'name', 'role', 'status', 'paymentConfig', 'permissions', 'hasUserAccount'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^staff:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'staff' },
    id: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    name: { type: 'string', minLength: 1 },
    role: {
      type: 'string',
      enum: ['MANAGER', 'CHEF', 'WAITER', 'BARTENDER', 'HOST', 'KITCHEN_HELPER', 'CASHIER', 'CLEANER', 'DELIVERY', 'OWNER', 'ADMIN', 'STAFF', 'USER']
    },
    contact: { type: 'string' },
    email: { type: 'string' },
    phone: { type: 'string' },
    status: { type: 'string', enum: ['ACTIVE', 'INACTIVE'] },
    paymentConfig: {
      type: 'object',
      required: ['type', 'baseSalary'],
      properties: {
        type: { type: 'string', enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'PER_SHIFT'] },
        baseSalary: { type: 'number' },
        shiftRate: { type: 'number' },
        shiftRates: { type: 'object' },
        paymentDay: { type: 'number' },
        baseLogic: { type: 'string', enum: ['ADDITIVE', 'MINIMUM', 'NONE'] },
        advanceRepayment: {
          type: 'object',
          properties: {
            percentage: { type: 'number' },
            maxAmount: { type: 'number' }
          }
        },
        nextPaymentDueDate: { type: 'string' }
      }
    },
    userId: { type: 'string' },
    hasUserAccount: { type: 'boolean' },
    username: { type: 'string' },
    pendingAuth: { type: 'boolean' },
    permissions: {
      type: 'object',
      required: ['pages'],
      properties: {
        pages: {
          type: 'object',
          required: ['menu', 'orders', 'finance', 'inventory', 'staff', 'settings', 'suppliers'],
          properties: {
            menu: { type: 'boolean' },
            orders: { type: 'boolean' },
            finance: { type: 'boolean' },
            analytics: { type: 'boolean' },
            inventory: { type: 'boolean' },
            staff: { type: 'boolean' },
            settings: { type: 'boolean' },
            suppliers: { type: 'boolean' }
          }
        },
        tabs: {
          type: 'object',
          properties: {
            inventory: {
              type: 'object',
              properties: {
                inventory: { type: 'boolean' },
                subrecipes: { type: 'boolean' },
                counts: { type: 'boolean' },
                waste: { type: 'boolean' }
              }
            },
            staff: {
              type: 'object',
              properties: {
                staff: { type: 'boolean' },
                shifts_schedule: { type: 'boolean' },
                attendance: { type: 'boolean' },
                payments: { type: 'boolean' }
              }
            }
          }
        }
      }
    },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

export const unifiedScheduleDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'weeklySchedule', 'effectiveFrom', 'isActive'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^schedule:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'schedule' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    weeklySchedule: {
      type: 'object',
      required: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
      properties: {
        monday: { type: 'array', items: { type: 'string' } },
        tuesday: { type: 'array', items: { type: 'string' } },
        wednesday: { type: 'array', items: { type: 'string' } },
        thursday: { type: 'array', items: { type: 'string' } },
        friday: { type: 'array', items: { type: 'string' } },
        saturday: { type: 'array', items: { type: 'string' } },
        sunday: { type: 'array', items: { type: 'string' } }
      }
    },
    effectiveFrom: { type: 'string' },
    effectiveTo: { type: 'string' },
    isActive: { type: 'boolean' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

export const unifiedAttendanceDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'records', 'schemaVersion', 'createdAt', 'updatedAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^attendance:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'attendance' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    records: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'date', 'staffId', 'staffName', 'status'],
        properties: {
          id: { type: 'string' },
          date: { type: 'string', pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' },
          staffId: { type: 'string' },
          staffName: { type: 'string' },
          status: { type: 'string', enum: ['present', 'late', 'absent'] },
          shiftId: { type: 'string' },
          shiftName: { type: 'string' },
          attended: { type: 'boolean' },
          notes: { type: 'string' },
          isPaid: { type: 'boolean' }
        }
      }
    },
    schemaVersion: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

export const unifiedPaymentDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'amount', 'paymentType', 'paymentDate', 'status', 'createdAt', 'updatedAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^payment:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'payment' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    amount: { type: 'number' },
    paymentType: {
      type: 'string',
      enum: ['SALARY', 'BONUS', 'ADVANCE', 'DEDUCTION', 'SHIFT_PAYMENT']
    },
    paymentDate: { type: 'string' },
    periodStart: { type: 'string' },
    periodEnd: { type: 'string' },
    status: {
      type: 'string',
      enum: ['PENDING', 'COMPLETED', 'CANCELLED'],
      default: 'COMPLETED'
    },
    notes: { type: 'string' },
    metadata: { type: 'object' }, // Flexible object for extended metadata
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

export const unifiedBalanceDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'balanceType', 'amount', 'reason', 'date', 'isUsed', 'createdAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^staff_balance:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'staff_balance' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    balanceType: {
      type: 'string',
      enum: ['ADVANCE', 'DEDUCTION', 'BONUS']
    },
    amount: { 
      type: 'number',
      minimum: 0
    },
    reason: { 
      type: 'string',
      minLength: 1,
      maxLength: 500
    },
    date: { 
      type: 'string',
      format: 'date-time'
    },
    isUsed: { 
      type: 'boolean',
      default: false
    },
    usedInPaymentId: { type: 'string' },
    usedAt: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// ===== LEGACY COMPATIBILITY =====

// Re-exports for backwards compatibility
export type { UnifiedStaffMember as StaffMember };

// Legacy staff document structure (array-based)
export interface LegacyStaffDocument {
  _id: 'staff';
  _rev?: string;
  members: UnifiedStaffMember[];
  shifts?: ShiftConfig[];
  payments?: Array<{
    id: string;
    staffId: string;
    staffName: string;
    amount: number;
    currency: string;
    paymentDate: string;
    paymentType: 'salary' | 'bonus' | 'advance' | 'deduction' | 'shift_payment';
    period?: {
      startDate: string;
      endDate: string;
    };
    notes?: string;
    createdAt: string;
  }>;
}

// Form data interfaces for UI components
export interface StaffFormData {
  name: string;
  role: string;
  email?: string;
  phone?: string;
  status: StaffStatus;
  userId?: string;
  paymentConfig: PaymentConfig;
  presence?: StaffPresence;
  schedule?: StaffSchedule;
  hasUserAccount?: boolean;
  hasAppAccess?: boolean;
  appAccessCredentials?: {
    username: string;
    password: string;
  };
  type?: 'staff';
  permissions?: StaffPermissions;
  weeklySchedule?: WeeklySchedule;
  isDeliveryGuy?: boolean; // Compatibility field
}

export interface StaffAuthCredentials {
  username: string;
  password: string;
  role?: string;
  permissions?: StaffPermissions;
}

export interface StaffAuthDocument {
  _id: string;
  type: 'user';
  name: string;
  username: string;
  password: string;
  role: string;
  restaurantId: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    staffId: string;
    originalRole?: string;
    [key: string]: any;
  };
}

// ===== DEFAULT VALUES =====

export const DEFAULT_STAFF_PERMISSIONS: StaffPermissions = {
  pages: {
    menu: false,
    orders: true,
    finance: false,
    analytics: false,
    inventory: false,
    staff: false,
    settings: false,
    suppliers: false
  },
  tabs: {
    inventory: {
      inventory: false,
      subrecipes: false,
      counts: false,
      waste: false
    },
    staff: {
      staff: false,
      shifts_schedule: false,
      attendance: false,
      payments: false
    }
  }
};

export const DEFAULT_PAYMENT_CONFIG: PaymentConfig = {
  type: 'MONTHLY',
  baseSalary: 0,
  baseLogic: 'ADDITIVE'
};

export const DEFAULT_WEEKLY_SCHEDULE: WeeklySchedule = {
  monday: [],
  tuesday: [],
  wednesday: [],
  thursday: [],
  friday: [],
  saturday: [],
  sunday: []
};

// ===== UTILITY FUNCTIONS =====

export function createStaffDocumentId(staffId: string): string {
  return `staff:${staffId}`;
}

export function createScheduleDocumentId(staffId: string): string {
  return `schedule:${staffId}`;
}

export function createAttendanceDocumentId(staffId: string): string {
  return `attendance:${staffId}`;
}

export function createPaymentDocumentId(date: string, uuid: string): string {
  const datePrefix = date.split('T')[0]; // Extract YYYY-MM-DD
  return `payment:${datePrefix}-${uuid}`;
}

export function createBalanceDocumentId(date: string, uuid: string): string {
  const datePrefix = date.split('T')[0]; // Extract YYYY-MM-DD
  return `staff_balance:${datePrefix}-${uuid}`;
}

export function extractStaffIdFromDocumentId(documentId: string): string | null {
  const match = documentId.match(/^(staff|schedule|attendance):([0-9a-f-]+)$/);
  return match ? match[2] : null;
}

export function isValidStaffId(id: string): boolean {
  return ID_PATTERNS.STAFF_ID.test(id);
}

export function isValidStaffRole(role: string): role is StaffRole {
  const validRoles: StaffRole[] = [
    'MANAGER', 'CHEF', 'WAITER', 'BARTENDER', 'HOST', 
    'KITCHEN_HELPER', 'CASHIER', 'CLEANER', 'DELIVERY',
    'OWNER', 'ADMIN', 'STAFF', 'USER'
  ];
  return validRoles.includes(role as StaffRole);
}

export function normalizeStaffRole(role: string): StaffRole {
  const upperRole = role.toUpperCase();
  return isValidStaffRole(upperRole) ? upperRole : 'STAFF';
}

// ===== MIGRATION HELPERS =====

export function migrateFromLegacyStaffMember(legacy: any): UnifiedStaffMember {
  return {
    id: legacy.id,
    name: legacy.name,
    role: normalizeStaffRole(legacy.role || 'STAFF'),
    status: legacy.status || 'ACTIVE',
    email: legacy.email,
    phone: legacy.phone,
    contact: legacy.contact,
    paymentConfig: legacy.paymentConfig || DEFAULT_PAYMENT_CONFIG,
    userId: legacy.userId,
    hasUserAccount: legacy.hasUserAccount || false,
    username: legacy.username,
    pendingAuth: legacy.pendingAuth,
    permissions: legacy.permissions || DEFAULT_STAFF_PERMISSIONS,
    presence: legacy.presence,
    schedule: legacy.schedule,
    shift: legacy.shift,
    _id: legacy._id,
    _rev: legacy._rev,
    type: 'staff',
    startDate: legacy.startDate,
    createdAt: legacy.createdAt || new Date().toISOString(),
    updatedAt: legacy.updatedAt || new Date().toISOString()
  };
}

export function convertToStaffDocument(unified: UnifiedStaffMember): StaffDocument {
  return {
    _id: unified._id || createStaffDocumentId(unified.id),
    _rev: unified._rev,
    type: 'staff',
    id: unified.id,
    name: unified.name,
    role: unified.role,
    contact: unified.contact,
    email: unified.email,
    phone: unified.phone,
    status: unified.status,
    paymentConfig: unified.paymentConfig,
    userId: unified.userId,
    hasUserAccount: unified.hasUserAccount,
    username: unified.username,
    pendingAuth: unified.pendingAuth,
    permissions: unified.permissions,
    createdAt: unified.createdAt,
    updatedAt: unified.updatedAt || new Date().toISOString()
  };
}