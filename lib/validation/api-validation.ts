import { z } from 'zod';
import { NextRequest, NextResponse } from 'next/server';

// Common validation schemas
export const idSchema = z.string().min(1, 'ID is required');
export const emailSchema = z.string().email('Invalid email format');
export const phoneSchema = z.string().min(10, 'Phone must be at least 10 digits');
export const positiveNumberSchema = z.number().positive('Must be a positive number');
export const nonNegativeNumberSchema = z.number().min(0, 'Must be non-negative');

// Restaurant validation
export const restaurantIdSchema = z.string().min(1, 'Restaurant ID is required');

// User validation schemas
export const loginSchema = z.object({
  identifier: z.string().min(1, 'Email or username is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
}).or(z.object({
  email: emailSchema,
  password: z.string().min(6, 'Password must be at least 6 characters'),
}));

export const userCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: emailSchema,
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['admin', 'owner', 'manager', 'waiter', 'kitchen', 'cashier']),
  restaurantId: restaurantIdSchema.optional(),
});

export const userUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  email: emailSchema.optional(),
  role: z.enum(['admin', 'owner', 'manager', 'waiter', 'kitchen', 'cashier']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
});

// NOTE: Order validation schemas removed - restaurant logic is client-side only (PouchDB/CouchDB)
// Order validation is handled by lib/db/v4/schemas/order-schema.ts and lib/db/v4/utils/order-status-validation.ts

// Staff validation schemas
export const staffCreateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.enum(['manager', 'waiter', 'kitchen', 'cashier', 'cleaner']),
  phone: phoneSchema.optional(),
  email: emailSchema.optional(),
  salary: nonNegativeNumberSchema.optional(),
  status: z.enum(['active', 'inactive', 'terminated']).optional(),
});

export const staffUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  role: z.enum(['manager', 'waiter', 'kitchen', 'cashier', 'cleaner']).optional(),
  phone: phoneSchema.optional(),
  email: emailSchema.optional(),
  salary: nonNegativeNumberSchema.optional(),
  status: z.enum(['active', 'inactive', 'terminated']).optional(),
});

// NOTE: Inventory validation schemas removed - restaurant logic is client-side only (PouchDB/CouchDB)
// Inventory validation is handled by lib/db/v4/schemas/inventory-schema.ts

// NOTE: Payment validation schemas removed - restaurant logic is client-side only (PouchDB/CouchDB)
// Payment validation is handled by lib/db/v4/operations/order-ops.ts and related services

// Generic validation wrapper
export function validateRequest<T>(
  req: NextRequest,
  schema: z.ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; error: NextResponse }> {
  return new Promise(async (resolve) => {
    try {
      const body = await req.json();
      const result = schema.safeParse(body);

      if (!result.success) {
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        }));

        resolve({
          success: false,
          error: NextResponse.json(
            { error: 'Validation failed', details: errors },
            { status: 400 }
          ),
        });
        return;
      }

      resolve({ success: true, data: result.data });
    } catch (error) {
      resolve({
        success: false,
        error: NextResponse.json(
          { error: 'Invalid JSON in request body' },
          { status: 400 }
        ),
      });
    }
  });
}

// Query parameter validation
export function validateQueryParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; errors: string[] } {
  const params: Record<string, any> = {};

  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }

  const result = schema.safeParse(params);

  if (!result.success) {
    const errors = result.error.errors.map(err =>
      `${err.path.join('.')}: ${err.message}`
    );
    return { success: false, errors };
  }

  return { success: true, data: result.data };
}

// Path parameter validation
export function validatePathParam(
  param: string | string[] | undefined,
  schema: z.ZodSchema
): { success: true; data: any } | { success: false; error: string } {
  const result = schema.safeParse(param);

  if (!result.success) {
    return {
      success: false,
      error: result.error.errors[0]?.message || 'Invalid parameter',
    };
  }

  return { success: true, data: result.data };
}

// Sanitization helpers
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function sanitizeNumber(input: any): number | null {
  const num = Number(input);
  return isNaN(num) ? null : num;
}

// Rate limiting validation
export const rateLimitSchema = z.object({
  windowMs: z.number().positive(),
  maxRequests: z.number().positive(),
  identifier: z.string().min(1),
});

// File upload validation
export const fileUploadSchema = z.object({
  filename: z.string().min(1, 'Filename is required'),
  mimetype: z.string().regex(/^(image|application)\//, 'Invalid file type'),
  size: z.number().max(10 * 1024 * 1024, 'File too large (max 10MB)'),
});

export default {
  validateRequest,
  validateQueryParams,
  validatePathParam,
  sanitizeString,
  sanitizeNumber,
  // Export only schemas that are actually used by API endpoints
  loginSchema,
  userCreateSchema,
  userUpdateSchema,
  staffCreateSchema,
  staffUpdateSchema,
  fileUploadSchema,
};