'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { Restaurant, User, hasPageAccess, hasComponentAccess, hasActionAccess } from "../auth/types";
import { useAuth as useCustomAuth } from '@/lib/context/multi-user-auth-provider';

// Define the extended session type
interface ExtendedSession {
  restaurant?: Restaurant;
  user?: User;
  authLevel?: 'restaurant' | 'user';
}

// Define the context shape
interface AuthContextType {
  // Auth state
  isAuthenticated: boolean;
  isLoading: boolean;
  authLevel: 'restaurant' | 'user' | null;
  restaurant: Restaurant | null;
  user: User | null;
  error: string | null;
  status: string;
  
  // Auth actions
  loginRestaurant: (email: string, password: string) => Promise<boolean>;
  loginUser: (usernameOrEmail: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  
  // Permission helpers
  canAccessPage: (pageName: string) => boolean;
  canAccessComponent: (pageName: string, componentName: string) => boolean;
  canPerformAction: (actionName: string, resourceName: string) => boolean;
  
  // Legacy support
  session: any;
  isAdmin: boolean;
  isOwner: boolean;
  signOut: () => Promise<void>;
  updateProfile: (data?: { name?: string; email?: string }) => Promise<{ success: boolean; error?: string }>;
  changePassword: (data?: { currentPassword?: string; newPassword?: string }) => Promise<{ success: boolean; error?: string }>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { 
    user: newAuthUser, 
    isAuthenticated, 
    loading: isLoading, 
    error: newAuthError,
    login: newAuthLogin, 
    logout: newAuthLogout
  } = useCustomAuth();
  
  const [error, setError] = useState<string | null>(null);
  
  // Update error state when new auth error changes
  useEffect(() => {
    if (newAuthError) {
      setError(newAuthError);
    }
  }, [newAuthError]);
  
  // Map new auth user to our expected format
  const restaurant: Restaurant | null = newAuthUser ? {
    id: newAuthUser.restaurantId || '',
    name: '',
    email: newAuthUser.email || '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  } : null;
  
  const user: User | null = newAuthUser ? {
    id: newAuthUser.id,
    name: newAuthUser.name,
    email: newAuthUser.email,
    role: newAuthUser.role,
    permissions: newAuthUser.permissions || {
      pages: [],
      components: [],
      actions: []
    },
    passwordHash: '',
    restaurantId: newAuthUser.restaurantId || '',
    active: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  } : null;
  
  const authLevel = newAuthUser ? 
    (newAuthUser.role === 'owner' || newAuthUser.role === 'admin' ? 'restaurant' : 'user') 
    : null;
  
  const status = isLoading ? 'loading' : (isAuthenticated ? 'authenticated' : 'unauthenticated');
  
  // Derived properties
  const isAdmin = user?.role === "admin" || false;
  const isOwner = user?.role === "owner" || authLevel === "restaurant" || false;
  
  // Restaurant login
  const loginRestaurant = async (email: string, password: string): Promise<boolean> => {
    try {
      setError(null);
      
      const success = await newAuthLogin({
        identifier: email,
        password
      });
      
      if (!success) {
        setError("Failed to login");
        return false;
      }
      
      // Navigate to user authentication page
      router.push("/auth");
      return true;
    } catch (error: any) {
      setError(error.message || "An unexpected error occurred during login");
      console.error("Restaurant login error:", error);
      return false;
    }
  };
  
  // User login
  const loginUser = async (usernameOrEmail: string, password: string): Promise<boolean> => {
    try {
      setError(null);
      
      if (!restaurant) {
        setError("Restaurant authentication is required first");
        return false;
      }
      
      const success = await newAuthLogin({
        identifier: usernameOrEmail,
        password,
        restaurantId: restaurant.id
      });
      
      if (!success) {
        setError("Failed to login");
        return false;
      }
      
      // Navigate to menu or dashboard
      router.push("/menu");
      return true;
    } catch (error: any) {
      setError(error.message || "An unexpected error occurred during login");
      console.error("User login error:", error);
      return false;
    }
  };
  
  // Logout
  const logout = async (): Promise<void> => {
    try {
      newAuthLogout();
      router.push("/auth");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };
  
  // Permission check helpers
  const canAccessPage = (pageName: string): boolean => {
    if (!user) return false;
    return hasPageAccess(user, pageName);
  };
  
  const canAccessComponent = (pageName: string, componentName: string): boolean => {
    if (!user) return false;
    return hasComponentAccess(user, pageName, componentName);
  };
  
  const canPerformAction = (actionName: string, resourceName: string): boolean => {
    if (!user) return false;
    return hasActionAccess(user, actionName, resourceName);
  };
  
  // Compatibility methods for old API
  const updateProfile = async (data?: { name?: string; email?: string }) => {
    console.warn("updateProfile is deprecated", data);
    return { success: false, error: "Not implemented" };
  };
  
  const changePassword = async (data?: { currentPassword?: string; newPassword?: string }) => {
    console.warn("changePassword is deprecated", data);
    return { success: false, error: "Not implemented" };
  };
  
  // Create a mock session object for backward compatibility
  const session = {
    user: newAuthUser ? {
      id: newAuthUser.id,
      name: newAuthUser.name,
      email: newAuthUser.email,
      role: newAuthUser.role
    } : null
  };
  
  // Context value
  const value: AuthContextType = {
    // Auth state
    isAuthenticated,
    isLoading,
    authLevel,
    restaurant,
    user,
    error,
    status,
    
    // Auth actions
    loginRestaurant,
    loginUser,
    logout,
    
    // Permission helpers
    canAccessPage,
    canAccessComponent,
    canPerformAction,
    
    // Legacy properties and methods (for backward compatibility)
    session,
    isAdmin,
    isOwner,
    signOut: logout,
    updateProfile,
    changePassword
  };
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  
  return context;
} 