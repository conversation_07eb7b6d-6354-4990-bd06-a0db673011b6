import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from "react";
import { getSettings, updateSettings } from "@/lib/db/v4";
import type { RestaurantSettings } from "@/lib/db/v4";
import { useUnifiedDB } from "./unified-db-provider";
import { databaseV4 } from "@/lib/db/v4/core/db-instance";

interface SettingsContextValue {
  settings: RestaurantSettings | null;
  isCogsEnabled: boolean;
  updateCogsSettings: (cogsSettings: any) => Promise<void>;
  refreshSettings: () => Promise<void>;
  loading: boolean;
  error: unknown;
}

const SettingsContext = createContext<SettingsContextValue | undefined>(undefined);

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<RestaurantSettings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<unknown>(null);
  const { isReady: dbIsReady, waitForInitialization } = useUnifiedDB();

  const fetchSettings = useCallback(async () => {
    setLoading(true);
    try {
      // First, ensure the database is initialized
      if (!databaseV4.isInitialized) {
        console.log('[SettingsContext] Database not initialized, waiting for initialization...');
        try {
          await waitForInitialization();
          console.log('[SettingsContext] Database initialization complete, proceeding to fetch settings');
        } catch (initError) {
          console.error('[SettingsContext] Database initialization failed:', initError);
          setError(initError);
          setLoading(false);
          return;
        }
      }

      // Now fetch the settings
      console.log('[SettingsContext] Fetching settings...');
      const s = await getSettings();
      console.log('[SettingsContext] Settings fetched successfully:', s);
      setSettings(s);
      setError(null);
    } catch (e: any) {
      console.error('[SettingsContext] Error fetching settings:', e);
      
      // If it's a "missing" error, try to manually initialize settings
      if (e.status === 404 || (e.name === 'not_found' && e.message?.includes('missing'))) {
        console.log('[SettingsContext] Settings missing, attempting manual initialization...');
        try {
          const { ensureDefaultSettings } = await import('@/lib/db/v4/operations/settings-ops');
          const defaultSettings = await ensureDefaultSettings();
          console.log('[SettingsContext] Manual settings initialization successful:', defaultSettings);
          setSettings(defaultSettings);
          setError(null);
        } catch (manualError) {
          console.error('[SettingsContext] Manual settings initialization failed:', manualError);
          setError(manualError);
        }
      } else {
        setError(e);
      }
    } finally {
      setLoading(false);
    }
  }, [waitForInitialization]);

  // Fetch settings when the database is ready or when the component mounts
  useEffect(() => {
    if (dbIsReady) {
      console.log('[SettingsContext] Database is ready, fetching settings...');
      fetchSettings();
    }

    // Listen for localStorage events to sync across tabs
    const onStorage = (event: StorageEvent) => {
      if (event.key === 'cogs-settings-updated') {
        console.log('[SettingsContext] Settings updated in another tab, refreshing...');
        fetchSettings();
      }
    };

    window.addEventListener('storage', onStorage);
    return () => window.removeEventListener('storage', onStorage);
  }, [fetchSettings, dbIsReady]);

  const refreshSettings = fetchSettings;

  const updateCogsSettings = async (cogsSettings: any) => {
    setLoading(true);
    try {
      console.log('[SettingsContext] updateCogsSettings called with:', cogsSettings);

      // First, ensure the database is initialized
      if (!databaseV4.isInitialized) {
        console.log('[SettingsContext] Database not initialized, waiting for initialization...');
        try {
          await waitForInitialization();
          console.log('[SettingsContext] Database initialization complete, proceeding to update settings');
        } catch (initError) {
          console.error('[SettingsContext] Database initialization failed:', initError);
          setError(initError);
          setLoading(false);
          return;
        }
      }

      // Handle the case where enableCOGS is directly provided
      const enableCOGS = typeof cogsSettings.enableCOGS === 'boolean'
        ? cogsSettings.enableCOGS
        : (settings?.enableCOGS || false);

      // Prepare the update object
      const updateObj: Partial<RestaurantSettings> = { enableCOGS };

      // Handle the case where cogsSettings is provided as a nested object
      if (cogsSettings.cogsSettings) {
        updateObj.cogsSettings = cogsSettings.cogsSettings;
      }
      // Handle the case where cogsSettings properties are provided directly
      else if (
        typeof cogsSettings.targetFoodCostPercentage === 'number' ||
        typeof cogsSettings.defaultProfitMargin === 'number' ||
        typeof cogsSettings.autoUpdatePrices === 'boolean'
      ) {
        updateObj.cogsSettings = {
          targetFoodCostPercentage: cogsSettings.targetFoodCostPercentage ?? settings?.cogsSettings?.targetFoodCostPercentage ?? 30,
          defaultProfitMargin: cogsSettings.defaultProfitMargin ?? settings?.cogsSettings?.defaultProfitMargin ?? 50,
          autoUpdatePrices: cogsSettings.autoUpdatePrices ?? settings?.cogsSettings?.autoUpdatePrices ?? false
        };
      }

      console.log('[SettingsContext] Updating settings with:', updateObj);
      const updated = await updateSettings(updateObj);
      console.log('[SettingsContext] Settings updated successfully:', updated);

      setSettings(updated);
      localStorage.setItem('cogs-settings-updated', Date.now().toString());
      setError(null);
    } catch (e) {
      console.error('[SettingsContext] Error updating COGS settings:', e);
      setError(e);
    } finally {
      setLoading(false);
    }
  };

  const isCogsEnabled = !!settings?.enableCOGS;

  return (
    <SettingsContext.Provider value={{ settings, isCogsEnabled, updateCogsSettings, refreshSettings, loading, error }}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings(): SettingsContextValue {
  const ctx = useContext(SettingsContext);
  if (!ctx) throw new Error("useSettings must be used within a SettingsProvider");
  return ctx;
}