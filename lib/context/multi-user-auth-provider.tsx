'use client';

import React, { createContext, useContext, ReactNode, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useMultiUserAuth, UseMultiUserAuthReturn } from '@/lib/hooks/use-multi-user-auth';
import { User } from '@/lib/auth/new-auth-service';
import { UserSession } from '@/lib/auth/multi-user-session-manager';
import { updateRestaurantIdForAccountSwitch } from '@/lib/db/v4/utils/restaurant-id';

// Extended context type that includes multi-user capabilities
interface MultiUserAuthContextType extends UseMultiUserAuthReturn {
  // Legacy compatibility properties
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Enhanced multi-user properties
  sessions: UserSession[];
  activeSession: UserSession | null;
  
  // Utility methods
  hasMultipleUsers: boolean;
  canSwitchUsers: boolean;
}

// Create context
const MultiUserAuthContext = createContext<MultiUserAuthContextType | undefined>(undefined);

// Provider component
export function MultiUserAuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const multiUserAuth = useMultiUserAuth();

  // Destructure for easier access
  const {
    currentUser,
    availableUsers,
    isAuthenticated,
    loading,
    switchToUser,
    addUserSession,
    removeUserSession,
    logout,
    logoutAll,
    offlineLogin,
    refreshCurrentSession,
    isAdmin,
    isOwner,
    canManageStaff,
    getSessionStats,
    isOfflineMode,
    restaurantId,
    error
  } = multiUserAuth;

  // Find active session
  const activeSession = availableUsers.find(session => 
    session.user.id === currentUser?.id
  ) || null;

  // Derived properties
  const hasMultipleUsers = availableUsers.length > 1;
  const canSwitchUsers = availableUsers.length > 0;

  // Handle navigation based on user role and authentication state
  useEffect(() => {
    // knowledge:start remove forced owner redirect to finance
    if (!loading && isAuthenticated && currentUser) {
      // Auto-navigate based on role if we're on the auth page
      if (window.location.pathname === '/auth') {
        // No forced redirect for owner anymore
      }
    }
    // knowledge:end remove forced owner redirect to finance
  }, [isAuthenticated, currentUser, loading, router]);

  // Enhanced context value with backward compatibility
  const contextValue: MultiUserAuthContextType = {
    // Multi-user auth properties
    ...multiUserAuth,
    
    // Legacy compatibility (mapped from multi-user auth)
    user: currentUser,
    isLoading: loading,
    
    // Enhanced properties
    sessions: availableUsers,
    activeSession,
    hasMultipleUsers,
    canSwitchUsers,
  };

  return (
    <MultiUserAuthContext.Provider value={contextValue}>
      {children}
    </MultiUserAuthContext.Provider>
  );
}

// Hook to use the multi-user auth context
export function useMultiUserAuthContext(): MultiUserAuthContextType {
  const context = useContext(MultiUserAuthContext);
  if (context === undefined) {
    throw new Error('useMultiUserAuthContext must be used within a MultiUserAuthProvider');
  }
  return context;
}

// Backward compatibility hook that mimics the old useAuth interface
export function useAuth() {
  const context = useMultiUserAuthContext();
  
  return {
    // Core auth state
    user: context.currentUser,
    isAuthenticated: context.isAuthenticated,
    loading: context.loading,
    error: context.error,
    isRestricted: context.isRestricted,
    
    // Add permissions to the user object
    permissions: context.currentUser?.permissions,

    // Auth actions (mapped to multi-user equivalents)
    login: context.login,
    logout: context.logout,
    register: context.register,
    
    // Role-based properties
    isAdmin: context.isAdmin,
    isOwner: context.isOwner,
    canManageStaff: context.canManageStaff,
    
    // Offline capabilities
    offlineLogin: context.offlineLogin,
    isOfflineMode: context.isOfflineMode,
    
    // Multi-user specific (new properties)
    availableUsers: context.availableUsers,
    switchToUser: context.switchToUser,
    addUserSession: context.addUserSession,
    removeUserSession: context.removeUserSession,
    logoutAll: context.logoutAll,
    hasMultipleUsers: context.hasMultipleUsers,
    canSwitchUsers: context.canSwitchUsers,
    getSessionStats: context.getSessionStats,
    refreshCurrentSession: context.refreshCurrentSession,
    checkRestrictionStatus: context.checkRestrictionStatus,
    restaurantId: context.restaurantId,
  };
}

// Export the provider as default for easy importing
export default MultiUserAuthProvider; 