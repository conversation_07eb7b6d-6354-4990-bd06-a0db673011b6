'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { databaseV4, initializeV4Database } from '@/lib/db/v4';
import { cleanRestaurantId } from '@/lib/db/db-utils';

// Unified interface combining both previous providers
interface UnifiedDBContextType {
  // Database instance access
  db: any | null;
  
  // Initialization state
  isDbInitialized: boolean;
  currentDbRestaurantId: string | null;
  isLoadingDb: boolean;
  
  // Error handling
  dbInitializeError: Error | null;
  error: Error | null;
  
  // Sync status (for compatibility)
  status: {
    status: 'idle' | 'syncing' | 'error' | 'paused' | 'complete';
    error?: Error;
    progress?: {
      docs_read: number;
      docs_written: number;
      pending: number;
    };
  };
  
  // Ready state
  isReady: boolean;
  
  // Methods
  initialize: (restaurantId: string) => Promise<void>;
  waitForInitialization: () => Promise<void>;
  resetInitialization: () => void;
}

const UnifiedDBContext = createContext<UnifiedDBContextType | undefined>(undefined);

interface UnifiedDBProviderProps {
  children: ReactNode;
  verbose?: boolean; // Control logging verbosity
}

export const UnifiedDBProvider: React.FC<UnifiedDBProviderProps> = ({ 
  children, 
  verbose = false 
}) => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  
  // State management
  const [db, setDb] = useState<any | null>(null);
  const [isDbInitialized, setIsDbInitialized] = useState(false);
  const [currentDbRestaurantId, setCurrentDbRestaurantId] = useState<string | null>(null);
  const [isLoadingDb, setIsLoadingDb] = useState(false);
  const [dbInitializeError, setDbInitializeError] = useState<Error | null>(null);
  const [status, setStatus] = useState<UnifiedDBContextType['status']>({ status: 'idle' });
  const [initializationAttempted, setInitializationAttempted] = useState(false);

  // Removed verbose logging for performance


  // Listen for database initialization events
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleDbInitialized = (event: Event) => {
      const customEvent = event as CustomEvent;
      const initStatus = customEvent.detail || {};

      if (initStatus.success && initStatus.restaurantId) {
        try {
          // Get database instance
          const dbInstance = databaseV4.getDatabase();
          setDb(dbInstance);
          setIsDbInitialized(true);
          setCurrentDbRestaurantId(databaseV4.getCurrentRestaurantId());
          setStatus({ status: 'complete' });
          setDbInitializeError(null);
          setIsLoadingDb(false);
        } catch (err) {
          const error = err instanceof Error ? err : new Error(String(err));
          setDbInitializeError(error);
          setStatus({ status: 'error', error });
          setIsLoadingDb(false);
        }
      } else if (initStatus.error) {
        setDbInitializeError(initStatus.error);
        setStatus({ status: 'error', error: initStatus.error });
        setIsDbInitialized(false);
        setIsLoadingDb(false);
      } else if (initStatus.isInitializing) {
        setStatus({ status: 'syncing' });
        setIsLoadingDb(true);
      }
    };

    // Listen for restaurant ID changes (account switching)
    const handleRestaurantIdChanged = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { newRestaurantId, source } = customEvent.detail || {};

      if (newRestaurantId && source === 'account-switch') {
        console.log(`🏪 [UnifiedDB] Restaurant ID changed to: ${newRestaurantId}, forcing reinitialization...`);

        // Reset all state to force reinitialization
        setInitializationAttempted(false);
        setIsDbInitialized(false);
        setCurrentDbRestaurantId(null);
        setDbInitializeError(null);
        setDb(null);
        setStatus({ status: 'idle' });
        setIsLoadingDb(false);

        // The main initialization effect will pick up the change and reinitialize
        console.log(`🔄 [UnifiedDB] State reset for restaurant switch, reinitialization will begin automatically`);
      }
    };

    document.addEventListener('v4-pouchdb-initialized', handleDbInitialized);
    document.addEventListener('restaurant-id-changed', handleRestaurantIdChanged);

    return () => {
      document.removeEventListener('v4-pouchdb-initialized', handleDbInitialized);
      document.removeEventListener('restaurant-id-changed', handleRestaurantIdChanged);
    };
  }, []);

  // Listen for sync status updates
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleSyncStatusUpdate = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail) {
        const { status: syncStatus, error: syncError, progress } = customEvent.detail;

        setStatus({
          status: syncStatus as 'idle' | 'syncing' | 'error' | 'paused' | 'complete',
          error: syncError,
          progress
        });
      }
    };

    document.addEventListener('sync-status-update', handleSyncStatusUpdate);
    return () => document.removeEventListener('sync-status-update', handleSyncStatusUpdate);
  }, []);

  // Main initialization effect - OPTIMIZED FOR SPEED
  useEffect(() => {
    const manageInitialization = async () => {
      // Skip if server-side or auth still loading
      if (typeof window === 'undefined' || authLoading) {
        console.log('[UnifiedDB] Skipping initialization: server-side or auth loading');
        return;
      }

      const targetRestaurantId = user?.restaurantId;
      console.log('[UnifiedDB] Initialization check:', {
        isAuthenticated,
        authLoading,
        user: user ? {
          id: user.id,
          name: user.name,
          restaurantId: user.restaurantId,
          role: user.role
        } : null,
        targetRestaurantId,
        initializationAttempted,
        isDbInitialized,
        isLoadingDb
      });

      // Only proceed if authenticated and we have a restaurant ID
      if (!isAuthenticated || !targetRestaurantId) {
        console.log('[UnifiedDB] Not authenticated or no restaurant ID, skipping DB initialization');
        setIsLoadingDb(false);
        return;
      }

      // Skip if already initialized for this restaurant
      if (isDbInitialized && currentDbRestaurantId === targetRestaurantId) {
        console.log('[UnifiedDB] Already initialized for restaurant:', targetRestaurantId);
        setIsLoadingDb(false);
        return;
      }

      // Skip if already attempting initialization
      if (initializationAttempted) {
        console.log('[UnifiedDB] Initialization already attempted, waiting...');
        return;
      }

      console.log('[UnifiedDB] Starting database initialization for restaurant:', targetRestaurantId);
      setInitializationAttempted(true);
      setIsLoadingDb(true);
      setDbInitializeError(null);

      try {
        console.log('[UnifiedDB] 🚀 Starting database initialization for:', targetRestaurantId);
        
        // 🚨 OPTIMIZED: Shorter timeout for faster user feedback
        const initPromise = initializeV4Database(targetRestaurantId);
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Database initialization timeout after 15 seconds')), 15000);
        });

        await Promise.race([initPromise, timeoutPromise]);
        console.log('[UnifiedDB] ✅ Database initialization completed');
        
        // 🔍 Wait for the database to fully settle and for any events to be processed
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 🔍 Check if the event listener already handled the initialization
        if (isDbInitialized && currentDbRestaurantId === targetRestaurantId) {
          console.log('[UnifiedDB] ✅ Database already marked as initialized by event listener');
          setIsLoadingDb(false);
          return;
        }
        
        // 🔍 Verify database is actually ready using multiple checks
        let currentRestaurantId = databaseV4.getCurrentRestaurantId();
        console.log('[UnifiedDB] 🔍 Post-init check - currentRestaurantId:', currentRestaurantId);
        
        // If no restaurant ID immediately, wait a bit more and try again
        if (!currentRestaurantId) {
          console.log('[UnifiedDB] 🔄 No restaurant ID found, waiting additional 200ms for state to settle...');
          await new Promise(resolve => setTimeout(resolve, 200));
          currentRestaurantId = databaseV4.getCurrentRestaurantId();
          console.log('[UnifiedDB] 🔍 Second check - currentRestaurantId:', currentRestaurantId);
        }
        
        if (!currentRestaurantId) {
          console.warn('[UnifiedDB] ⚠️ No current restaurant ID after initialization, but continuing...');
          // Don't fail here - some initialization modes might not set this immediately
        }
        
        // Get database instance (may be null in Electron mode, which is expected)
        try {
          const dbInstance = databaseV4.getDatabase();
          setDb(dbInstance);
          console.log('[UnifiedDB] 📦 Database instance retrieved:', dbInstance ? 'Available' : 'Null (Electron mode)');

          // 🚨 CRITICAL FIX: Add database health check
          if (dbInstance || databaseV4.isInitialized) {
            console.log('[UnifiedDB] ✅ Database health check passed');
          } else {
            console.warn('[UnifiedDB] ⚠️ Database health check failed - attempting recovery');
            // Don't fail initialization, but log for monitoring
          }
        } catch (dbError) {
          console.warn('[UnifiedDB] ⚠️ Could not get database instance (may be normal in Electron):', dbError);
          setDb(null);

          // 🚨 CRITICAL FIX: Don't fail if database instance is null in Electron mode
          if (databaseV4.isInitialized) {
            console.log('[UnifiedDB] 📡 Electron mode - database operations will use IPC');
          }
        }
        
        setIsDbInitialized(true);
        setCurrentDbRestaurantId(targetRestaurantId);
        setIsLoadingDb(false);
        console.log('[UnifiedDB] ✅ Database initialized successfully for restaurant:', targetRestaurantId);
      } catch (error) {
        console.error('[UnifiedDB] ❌ Database initialization failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Database initialization failed';
        
        // 🔧 Enhanced error handling with specific user guidance
        if (errorMessage.includes('timeout')) {
          setDbInitializeError(new Error('⏱️ Délai d\'attente dépassé. Veuillez actualiser la page.'));
        } else if (errorMessage.includes('CRITICAL_ELECTRON_API_MISSING')) {
          setDbInitializeError(new Error('🔧 API Electron manquante. Redémarrez l\'application.'));
        } else {
          setDbInitializeError(new Error(`💾 Erreur d\'initialisation: ${errorMessage}`));
        }
        
        setIsLoadingDb(false);
        setInitializationAttempted(false); // Allow retry
        
        console.log('[UnifiedDB] 🔄 Database initialization failed - user can retry');
      }
    };

    manageInitialization();
  }, [isAuthenticated, authLoading, user, initializationAttempted, isDbInitialized, currentDbRestaurantId, isLoadingDb]);

  // Reset initialization flag when user changes
  useEffect(() => {
    if (!isAuthenticated || !user) {
      setInitializationAttempted(false);
    }
  }, [isAuthenticated, user]);

  // Manual initialization function
  const initialize = useCallback(async (restaurantId: string): Promise<void> => {
    const cleanId = cleanRestaurantId(restaurantId);
    setStatus({ status: 'syncing' });
    setIsLoadingDb(true);
    
    try {
      await initializeV4Database(cleanId);
      
      setIsDbInitialized(true);
      setCurrentDbRestaurantId(databaseV4.getCurrentRestaurantId());
      setDbInitializeError(null);
      setStatus({ status: 'complete' });
      
      // Get database instance (may be null in Electron mode, which is expected)
      try {
        const dbInstance = databaseV4.getDatabase();
        setDb(dbInstance);
      } catch (err) {
        console.warn('Error getting database instance:', err);
        // Don't fail initialization if getDatabase fails in Electron mode
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      setDbInitializeError(err);
      setStatus({ status: 'error', error: err });
      setIsDbInitialized(false);
    } finally {
      setIsLoadingDb(false);
    }
  }, [cleanRestaurantId]);

  // Wait for initialization function
  const waitForInitialization = useCallback(async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (isDbInitialized) {
        resolve();
        return;
      }

      if (dbInitializeError) {
        reject(dbInitializeError);
        return;
      }

      // Set up a listener for initialization completion
      const checkInitialization = () => {
        if (isDbInitialized) {
          resolve();
        } else if (dbInitializeError) {
          reject(dbInitializeError);
        } else {
          // Check again in 100ms
          setTimeout(checkInitialization, 100);
        }
      };

      checkInitialization();
    });
  }, [isDbInitialized, dbInitializeError]);

  // Reset initialization function
  const resetInitialization = useCallback(() => {
    setInitializationAttempted(false);
    setIsDbInitialized(false);
    setCurrentDbRestaurantId(null);
    setDbInitializeError(null);
    setDb(null);
    setStatus({ status: 'idle' });
    setIsLoadingDb(false);
  }, []);

  // Compute derived state
  const isReady = isDbInitialized && !isLoadingDb && !dbInitializeError;
  const error = dbInitializeError || status.error || null;

  const contextValue: UnifiedDBContextType = {
    // Database instance access
    db,
    
    // Initialization state
    isDbInitialized,
    currentDbRestaurantId,
    isLoadingDb,
    
    // Error handling
    dbInitializeError,
    error,
    
    // Sync status
    status,
    
    // Ready state
    isReady,
    
    // Methods
    initialize,
    waitForInitialization,
    resetInitialization,
  };

  return (
    <UnifiedDBContext.Provider value={contextValue}>
      {children}
    </UnifiedDBContext.Provider>
  );
};

// Hook to use the unified database context
export const useUnifiedDB = (): UnifiedDBContextType => {
  const context = useContext(UnifiedDBContext);
  if (context === undefined) {
    throw new Error('useUnifiedDB must be used within a UnifiedDBProvider');
  }
  return context;
};

// Legacy compatibility hooks
export const useDatabase = () => {
  const context = useUnifiedDB();
  return {
    isInitialized: context.isDbInitialized,
  };
};

export const useRestaurantDB = () => {
  const context = useUnifiedDB();
  return {
    db: context.db,
    isReady: context.isReady,
    status: context.status,
    waitForInitialization: context.waitForInitialization,
  };
};