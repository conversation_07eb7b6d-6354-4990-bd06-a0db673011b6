'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

type PlatformType = 'electron' | 'capacitor' | 'browser' | 'unknown';

interface PlatformContextType {
  platform: PlatformType;
  isElectron: boolean;
  isCapacitor: boolean;
  isBrowser: boolean;
  isStatic: boolean;
  isOnline: boolean;
}

const PlatformContext = createContext<PlatformContextType>({
  platform: 'unknown',
  isElectron: false,
  isCapacitor: false,
  isBrowser: false,
  isStatic: false,
  isOnline: true,
});

export function PlatformProvider({ children }: { children: ReactNode }) {
  const [platform, setPlatform] = useState<PlatformType>('unknown');
  const [isOnline, setIsOnline] = useState<boolean>(true);
  const [isStatic, setIsStatic] = useState<boolean>(false);
  
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }
    
    // Detect platform
    const detectPlatform = () => {
      // Check for Electron
      if (window.navigator.userAgent.toLowerCase().includes('electron')) {
        setPlatform('electron');
        return;
      }
      
      // Check for Capacitor
      if (
        window.navigator.userAgent.includes('capacitor') ||
        typeof (window as any).Capacitor !== 'undefined'
      ) {
        setPlatform('capacitor');
        return;
      }
      
      // Otherwise, it's a standard browser
      setPlatform('browser');
    };
    
    // Detect static export (no server API)
    const detectStatic = async () => {
      try {
        // For Electron, check if we're in static mode based on build target
        if (window.navigator.userAgent.toLowerCase().includes('electron')) {
          // In Electron, we're static if we're not connecting to localhost:3000 (dev mode)
          const isDevMode = window.location.href.includes('localhost:3000');
          const isStaticElectron = !isDevMode;
          console.log(`🖥️ [Platform] Electron detected - ${isStaticElectron ? 'STATIC' : 'DEV'} mode`);
          setIsStatic(isStaticElectron);
          return;
        }
        
        // Check if we're in Capacitor
        if (window.navigator.userAgent.includes('capacitor') ||
            typeof (window as any).Capacitor !== 'undefined') {
          console.log('📱 [Platform] Capacitor detected - using static mode');
          setIsStatic(true); // Capacitor is always static
          return;
        }
        
        // For web, check if API is available
        const response = await fetch('/api/health', { 
          method: 'GET',
          signal: AbortSignal.timeout(2000)
        });
        
        if (response.ok) {
          console.log('🌐 [Platform] Web with API server detected');
          setIsStatic(false);
        } else {
          console.log('📦 [Platform] Static web build detected (no API)');
          setIsStatic(true);
        }
      } catch (error) {
        console.log('📦 [Platform] Static build detected (API check failed)');
        setIsStatic(true);
      }
    };
    
    // Online status handling
    const handleOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };
    
    // Run detections
    detectPlatform();
    detectStatic().catch(e => console.error('Error in detectStatic:', e));
    handleOnlineStatus();
    
    // Listen for online/offline events
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOnlineStatus);
    
    return () => {
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOnlineStatus);
    };
  }, []);
  
  const value = {
    platform,
    isElectron: platform === 'electron',
    isCapacitor: platform === 'capacitor',
    isBrowser: platform === 'browser',
    isStatic,
    isOnline,
  };
  
  return (
    <PlatformContext.Provider value={value}>
      {children}
    </PlatformContext.Provider>
  );
}

export function usePlatform(): PlatformContextType {
  const context = useContext(PlatformContext);
  
  if (context === undefined) {
    throw new Error('usePlatform must be used within a PlatformProvider');
  }
  
  return context;
} 