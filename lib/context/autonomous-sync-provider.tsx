'use client';

import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import type { AutonomousStatus, AutonomousConfig } from '@/lib/services/autonomous-sync-manager';

interface AutonomousSyncContextType {
  status: AutonomousStatus;
  isRunning: boolean;
  isConnected: boolean;
  isSyncing: boolean;
  discoveredServers: number;
  connectedServers: number;
  lastDiscovery: Date | null;
  error: string | null;
  
  // Control functions
  start: () => Promise<void>;
  stop: () => Promise<void>;
  discover: () => Promise<void>;
  updateConfig: (config: Partial<AutonomousConfig>) => void;
}

const AutonomousSyncContext = createContext<AutonomousSyncContextType | undefined>(undefined);

interface AutonomousSyncProviderProps {
  children: ReactNode;
  config?: Partial<AutonomousConfig>;
}

export function AutonomousSyncProvider({ children, config }: AutonomousSyncProviderProps) {
  const autonomousSync = useAutonomousSync({
    autoStart: true,
    discoveryInterval: 30000,      // 30 seconds
    reconnectInterval: 60000,      // 1 minute
    maxReconnectAttempts: 5,
    preferredServers: [],
    ...config
  });

  // Auto-start on mount if not already running
  useEffect(() => {
    if (!autonomousSync.isRunning) {
      console.log('🔄 [AutonomousSyncProvider] Auto-starting autonomous sync...');
      autonomousSync.start().catch(error => {
        console.error('🔄 [AutonomousSyncProvider] Auto-start failed:', error);
      });
    }
  }, [autonomousSync]);

  return (
    <AutonomousSyncContext.Provider value={autonomousSync}>
      {children}
    </AutonomousSyncContext.Provider>
  );
}

export function useAutonomousSyncContext(): AutonomousSyncContextType {
  const context = useContext(AutonomousSyncContext);
  if (context === undefined) {
    throw new Error('useAutonomousSyncContext must be used within an AutonomousSyncProvider');
  }
  return context;
}