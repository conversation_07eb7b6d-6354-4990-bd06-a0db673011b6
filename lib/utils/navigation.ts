// import { useRouter } from 'next/navigation'; // Removed for static export compatibility
import { usePlatform } from '@/lib/context/platform-context';

// Navigation utility that works in both static and dynamic modes
export function useStaticNavigation() {
  // const router = useRouter(); // Removed for static export compatibility
  const { isStatic } = usePlatform();
  
  const navigate = (path: string) => {
    // Remove leading slash for consistency
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    
    // For static exports, use proper path resolution
    const targetPath = cleanPath === '' ? '../index.html' : `../${cleanPath}/index.html`;
    console.log(`🔗 [StaticNav] Navigating to: ${targetPath}`);
    window.location.href = targetPath;
  };
  
  const replace = (path: string) => {
    // Remove leading slash for consistency
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;

    // For static exports, use proper path resolution
    const targetPath = cleanPath === '' ? '../index.html' : `../${cleanPath}/index.html`;
    console.log(`🔗 [StaticNav] Replacing with: ${targetPath}`);
    window.location.replace(targetPath);
  };
  
  return { navigate, replace };
}

// Direct function for use outside of React components
export function navigateStatic(path: string) {
  // Remove leading slash for consistency
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // Use the same detection logic as isStaticMode()
  const isLikelyStatic = isStaticMode();
  
  if (isLikelyStatic) {
    // Static mode with trailingSlash export: use relative path for Electron
    const targetPath = cleanPath === '' ? '../index.html' : `../${cleanPath}/index.html`;
    console.log(`🔗 [StaticNav] Direct navigation to: ${targetPath}`);
    window.location.href = targetPath;
  } else {
    // Dynamic mode: use window.location for simplicity
    console.log(`🔗 [DynamicNav] Direct navigation to: /${cleanPath}`);
    window.location.href = `/${cleanPath}`;
  }
}

// Helper to check if we're in static mode
export function isStaticMode(): boolean {
  if (typeof window === 'undefined') return false;
  
  // For Electron: check if we're being served by electron-serve (static files)
  // vs. dev server (localhost:3000) or CouchDB server (random localhost port)
  if (window.navigator.userAgent.toLowerCase().includes('electron')) {
    // In dev mode, we're served from localhost:3000
    // In static packaged mode, we're served from app:// protocol or file:// protocol
    const isDevMode = window.location.href.includes('localhost:3000');
    const isStaticElectron = !isDevMode;
    console.log(`🖥️ [isStaticMode] Electron detected: ${isStaticElectron ? 'STATIC' : 'DEV'} mode`);
    return isStaticElectron;
  }
  
  // For web/Capacitor: check original logic
  return window.location.protocol === 'file:' || 
         !window.location.href.includes('localhost:3000');
} 