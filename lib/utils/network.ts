import { toast } from 'sonner';

/**
 * Error thrown when device is offline
 */
export class OfflineError extends Error {
  constructor(message = 'You are offline. This feature requires an internet connection.') {
    super(message);
    this.name = 'OfflineError';
  }
}

/**
 * Options for safeFetch
 */
export interface SafeFetchOptions extends RequestInit {
  showOfflineToast?: boolean;
  offlineMessage?: string;
  retryOnOnline?: boolean;
}

/**
 * Check if we're in Electron
 */
export function isElectron(): boolean {
  if (typeof window === 'undefined') return false;
  return Boolean(window.IS_DESKTOP_APP);
}

/**
 * Check if we're online
 */
export function isOnline(): boolean {
  if (typeof navigator === 'undefined') return true;
  return navigator.onLine;
}

/**
 * Fetch wrapper that checks for online status first
 * 
 * @param url URL to fetch
 * @param options Fetch options + offline handling options
 * @returns Promise with fetch response
 * @throws OfflineError if offline
 */
export async function safeFetch(
  url: string, 
  options: SafeFetchOptions = {}
): Promise<Response> {
  const { 
    showOfflineToast = true,
    offlineMessage = 'You are offline. This feature requires an internet connection.',
    retryOnOnline = false,
    ...fetchOptions 
  } = options;

  // Note: In Electron, API calls are already intercepted in providers.tsx
  // So this mainly affects web mode or non-API calls

  // Check if online before making request
  if (!isOnline()) {
    if (showOfflineToast) {
      toast.error('⚠️ ' + offlineMessage);
    }
    
    // If retry is requested, wait for online status and retry
    if (retryOnOnline) {
      return new Promise((resolve, reject) => {
        const onOnline = async () => {
          window.removeEventListener('online', onOnline);
          try {
            const response = await fetch(url, fetchOptions);
            resolve(response);
          } catch (error) {
            reject(error);
          }
        };
        
        window.addEventListener('online', onOnline);
      });
    }
    
    throw new OfflineError(offlineMessage);
  }

  // Make the fetch request
  try {
    return await fetch(url, fetchOptions);
  } catch (error) {
    // Re-check online status in case network dropped during fetch
    if (!isOnline()) {
      if (showOfflineToast) {
        toast.error('⚠️ ' + offlineMessage);
      }
      throw new OfflineError(offlineMessage);
    }
    
    // Forward any other errors
    throw error;
  }
}

/**
 * Wraps a function that makes API calls with offline checking
 * 
 * @param fn Function that makes API calls
 * @param options Offline handling options
 * @returns Wrapped function that checks online status first
 */
export function withOfflineCheck<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: {
    showOfflineToast?: boolean;
    offlineMessage?: string;
  } = {}
): T {
  const { 
    showOfflineToast = true,
    offlineMessage = 'You are offline. This feature requires an internet connection.',
  } = options;
  
  const wrappedFn = async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    if (!isOnline()) {
      if (showOfflineToast) {
        toast.error('⚠️ ' + offlineMessage);
      }
      throw new OfflineError(offlineMessage);
    }
    
    try {
      return await fn(...args) as ReturnType<T>;
    } catch (error) {
      // Re-check online status in case network dropped during execution
      if (!isOnline()) {
        if (showOfflineToast) {
          toast.error('⚠️ ' + offlineMessage);
        }
        throw new OfflineError(offlineMessage);
      }
      
      // Forward the original error
      throw error;
    }
  };
  
  return wrappedFn as T;
} 