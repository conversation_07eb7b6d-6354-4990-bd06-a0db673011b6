/**
 * Platform-aware HTTP client
 * Uses Capacitor HTTP plugin on mobile platforms for better network handling
 */

import { Capacitor, CapacitorHttp } from '@capacitor/core';

// Type definitions for Capacitor HTTP
interface HttpOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  data?: any;
  connectTimeout?: number;
  readTimeout?: number;
}

interface HttpResponse {
  status: number;
  data: any;
  headers: Record<string, string>;
}

export interface HttpClientOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
}

export interface HttpClientResponse {
  status: number;
  data: any;
  headers: Record<string, string>;
  ok: boolean;
}

/**
 * Platform-aware HTTP client that uses Capacitor HTTP on mobile
 */
export class PlatformHttpClient {
  private static isMobile(): boolean {
    return Capacitor.isNativePlatform();
  }

  static async request(url: string, options: HttpClientOptions = {}): Promise<HttpClientResponse> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = 10000
    } = options;

    // Add default headers
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...headers
    };

    if (this.isMobile()) {
      // Use Capacitor HTTP plugin for mobile platforms
      console.log(`[Mobile HTTP] Making ${method} request to ${url}`);
      
      const httpOptions: HttpOptions = {
        url,
        method,
        headers: defaultHeaders,
        data: body ? JSON.parse(body) : undefined,
        connectTimeout: timeout,
        readTimeout: timeout
      };

      try {
        const response: HttpResponse = await CapacitorHttp.request(httpOptions);
        
        console.log(`[Mobile HTTP] Response status: ${response.status}`);
        
        return {
          status: response.status,
          data: response.data,
          headers: response.headers,
          ok: response.status >= 200 && response.status < 300
        };
      } catch (error) {
        console.error('[Mobile HTTP] Request failed:', error);
        throw error;
      }
    } else {
      // Use regular fetch for web/electron
      console.log(`[Web HTTP] Making ${method} request to ${url}`);
      
      const fetchOptions: RequestInit = {
        method,
        headers: defaultHeaders,
        body: body,
        signal: AbortSignal.timeout(timeout)
      };

      try {
        const response = await fetch(url, fetchOptions);
        const data = await response.json();
        
        console.log(`[Web HTTP] Response status: ${response.status}`);
        
        return {
          status: response.status,
          data,
          headers: Object.fromEntries(response.headers.entries()),
          ok: response.ok
        };
      } catch (error) {
        console.error('[Web HTTP] Request failed:', error);
        throw error;
      }
    }
  }

  static async get(url: string, options: Omit<HttpClientOptions, 'method'> = {}): Promise<HttpClientResponse> {
    return this.request(url, { ...options, method: 'GET' });
  }

  static async post(url: string, data?: any, options: Omit<HttpClientOptions, 'method' | 'body'> = {}): Promise<HttpClientResponse> {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  static async put(url: string, data?: any, options: Omit<HttpClientOptions, 'method' | 'body'> = {}): Promise<HttpClientResponse> {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  static async delete(url: string, options: Omit<HttpClientOptions, 'method'> = {}): Promise<HttpClientResponse> {
    return this.request(url, { ...options, method: 'DELETE' });
  }
}

/**
 * Convenience function for making HTTP requests with platform awareness
 */
export const httpClient = PlatformHttpClient;