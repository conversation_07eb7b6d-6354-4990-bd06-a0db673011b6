/**
 * Simple Performance Monitoring Utility
 * 
 * Provides basic performance tracking for critical operations
 * without external dependencies.
 */

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: string;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 metrics

  /**
   * Start timing an operation
   */
  startTimer(operation: string): () => void {
    const startTime = performance.now();
    const timestamp = new Date().toISOString();

    return (success: boolean = true, error?: string, metadata?: Record<string, any>) => {
      const duration = performance.now() - startTime;
      
      const metric: PerformanceMetric = {
        operation,
        duration,
        timestamp,
        success,
        error,
        metadata
      };

      this.addMetric(metric);
      
      // Log slow operations
      if (duration > 1000) { // > 1 second
        console.warn(`[PerformanceMonitor] Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`);
      }
    };
  }

  /**
   * Time an async operation
   */
  async timeOperation<T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const endTimer = this.startTimer(operation);
    
    try {
      const result = await fn();
      endTimer(true, undefined, metadata);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      endTimer(false, errorMessage, metadata);
      throw error;
    }
  }

  /**
   * Add a metric to the collection
   */
  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * Get performance statistics for an operation
   */
  getStats(operation: string): {
    count: number;
    averageDuration: number;
    successRate: number;
    slowestOperation: number;
    fastestOperation: number;
  } {
    const operationMetrics = this.metrics.filter(m => m.operation === operation);
    
    if (operationMetrics.length === 0) {
      return {
        count: 0,
        averageDuration: 0,
        successRate: 0,
        slowestOperation: 0,
        fastestOperation: 0
      };
    }

    const durations = operationMetrics.map(m => m.duration);
    const successCount = operationMetrics.filter(m => m.success).length;

    return {
      count: operationMetrics.length,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      successRate: (successCount / operationMetrics.length) * 100,
      slowestOperation: Math.max(...durations),
      fastestOperation: Math.min(...durations)
    };
  }

  /**
   * Get all performance metrics
   */
  getAllMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Get recent metrics (last N)
   */
  getRecentMetrics(count: number = 50): PerformanceMetric[] {
    return this.metrics.slice(-count);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get summary of all operations
   */
  getSummary(): Record<string, ReturnType<typeof this.getStats>> {
    const operations = [...new Set(this.metrics.map(m => m.operation))];
    const summary: Record<string, ReturnType<typeof this.getStats>> = {};
    
    operations.forEach(operation => {
      summary[operation] = this.getStats(operation);
    });
    
    return summary;
  }

  /**
   * Log performance summary to console
   */
  logSummary(): void {
    const summary = this.getSummary();
    
    console.group('🚀 Performance Summary');
    Object.entries(summary).forEach(([operation, stats]) => {
      console.log(`📊 ${operation}:`, {
        count: stats.count,
        avgDuration: `${stats.averageDuration.toFixed(2)}ms`,
        successRate: `${stats.successRate.toFixed(1)}%`,
        range: `${stats.fastestOperation.toFixed(2)}ms - ${stats.slowestOperation.toFixed(2)}ms`
      });
    });
    console.groupEnd();
  }
}

// Global instance
export const performanceMonitor = new PerformanceMonitor();

// Attach to window in development for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).performanceMonitor = performanceMonitor;
  console.log('🚀 Performance monitor available as window.performanceMonitor');
}

/**
 * Decorator for monitoring function performance
 */
export function monitored(operation: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;
    
    descriptor.value = (async function (this: any, ...args: any[]) {
      return performanceMonitor.timeOperation(
        `${target.constructor.name}.${propertyName}`,
        () => method.apply(this, args),
        { operation }
      );
    }) as T;
  };
}