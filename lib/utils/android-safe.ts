/**
 * Android-Safe Utilities
 * 
 * Utilities to prevent crashes and improve performance on Android devices
 */

import * as React from 'react';

/**
 * Detect if running on Android
 */
export const isAndroidEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const isAndroidUA = /Android/i.test(navigator.userAgent);
  const isCapacitor = !!(window as any).Capacitor;
  
  return isAndroidUA || isCapacitor;
};

/**
 * Android-safe array operations
 */
export const androidSafeFilter = <T>(
  array: T[], 
  predicate: (item: T, index: number) => boolean
): T[] => {
  try {
    if (!Array.isArray(array)) {
      console.warn('androidSafeFilter: Input is not an array:', array);
      return [];
    }
    
    return array.filter((item, index) => {
      try {
        return predicate(item, index);
      } catch (error) {
        console.warn('androidSafeFilter: Predicate error for item:', item, error);
        return false;
      }
    });
  } catch (error) {
    console.error('androidSafeFilter: Critical error:', error);
    return [];
  }
};

/**
 * Android-safe array sorting
 */
export const androidSafeSort = <T>(
  array: T[], 
  compareFn?: (a: T, b: T) => number
): T[] => {
  try {
    if (!Array.isArray(array)) {
      console.warn('androidSafeSort: Input is not an array:', array);
      return [];
    }
    
    return array.slice().sort((a, b) => {
      try {
        return compareFn ? compareFn(a, b) : 0;
      } catch (error) {
        console.warn('androidSafeSort: Compare function error:', error);
        return 0;
      }
    });
  } catch (error) {
    console.error('androidSafeSort: Critical error:', error);
    return [];
  }
};

/**
 * Android-safe date operations
 */
export const androidSafeDate = (dateInput: string | number | Date): Date => {
  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      console.warn('androidSafeDate: Invalid date input:', dateInput);
      return new Date(); // Return current date as fallback
    }
    return date;
  } catch (error) {
    console.error('androidSafeDate: Error creating date:', error);
    return new Date(); // Return current date as fallback
  }
};

/**
 * Android-safe localStorage operations
 */
export const androidSafeLocalStorage = {
  getItem: (key: string): string | null => {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return null;
      }
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('androidSafeLocalStorage.getItem error:', error);
      return null;
    }
  },
  
  setItem: (key: string, value: string): boolean => {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return false;
      }
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.warn('androidSafeLocalStorage.setItem error:', error);
      return false;
    }
  },
  
  removeItem: (key: string): boolean => {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return false;
      }
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn('androidSafeLocalStorage.removeItem error:', error);
      return false;
    }
  }
};

/**
 * Android-safe async operation wrapper
 */
export const androidSafeAsync = async <T>(
  operation: () => Promise<T>,
  fallback: T,
  operationName: string = 'unknown'
): Promise<T> => {
  try {
    const result = await operation();
    return result;
  } catch (error) {
    console.error(`androidSafeAsync: ${operationName} failed:`, error);
    return fallback;
  }
};

/**
 * Android-safe memory cleanup utility
 */
export const androidMemoryCleanup = (delay: number = 100): Promise<void> => {
  return new Promise((resolve) => {
    if (isAndroidEnvironment()) {
      // Small delay to allow garbage collection
      setTimeout(() => {
        // Force garbage collection if available (development only)
        if (typeof window !== 'undefined' && (window as any).gc) {
          try {
            (window as any).gc();
          } catch (e) {
            // Ignore gc errors
          }
        }
        resolve();
      }, delay);
    } else {
      resolve();
    }
  });
};

/**
 * Android-safe component error boundary
 */
export const createAndroidSafeWrapper = (
  Component: React.ComponentType<any>,
  fallbackComponent?: React.ComponentType<any>
) => {
  return (props: any) => {
    const [hasError, setHasError] = React.useState(false);
    const [errorInfo, setErrorInfo] = React.useState<string>('');

    React.useEffect(() => {
      const handleError = (error: ErrorEvent) => {
        console.error('Android-safe wrapper caught error:', error);
        setHasError(true);
        setErrorInfo(error.message || 'Unknown error');
      };

      const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
        console.error('Android-safe wrapper caught promise rejection:', event.reason);
        setHasError(true);
        setErrorInfo(event.reason?.message || 'Promise rejection');
      };

      window.addEventListener('error', handleError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      return () => {
        window.removeEventListener('error', handleError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }, []);

    const resetError = React.useCallback(() => {
      setHasError(false);
      setErrorInfo('');
    }, []);

    if (hasError) {
      if (fallbackComponent) {
        const FallbackComponent = fallbackComponent;
        return React.createElement(FallbackComponent, { 
          error: errorInfo, 
          onReset: resetError 
        });
      }

      return React.createElement('div', {
        className: "flex items-center justify-center min-h-[200px] p-6"
      }, React.createElement('div', {
        className: "text-center space-y-4"
      }, [
        React.createElement('div', { 
          key: 'icon',
          className: "text-2xl" 
        }, '⚠️'),
        React.createElement('div', { key: 'content' }, [
          React.createElement('h3', { 
            key: 'title',
            className: "font-semibold" 
          }, 'Une erreur est survenue'),
          React.createElement('p', { 
            key: 'message',
            className: "text-muted-foreground text-sm mt-1" 
          }, errorInfo)
        ]),
        React.createElement('button', {
          key: 'button',
          onClick: resetError,
          className: "px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm"
        }, 'Réessayer')
      ]));
    }

    return React.createElement(Component, props);
  };
};