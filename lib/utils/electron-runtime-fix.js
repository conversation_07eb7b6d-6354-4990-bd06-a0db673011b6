// Fix webpack public path for Electron static builds
// This ensures all dynamic imports use the correct relative path
if (typeof window !== 'undefined' && typeof __webpack_require__ !== 'undefined') {
  // Override the webpack public path to use relative paths
  __webpack_require__.p = './_next/';
  
  // Also fix any existing chunk loading that might use the old path
  if (typeof window.__next_set_public_path__ === 'function') {
    window.__next_set_public_path__('./_next/');
  }
  
  console.log('🔧 [electron-runtime-fix] Set webpack publicPath to:', __webpack_require__.p);
}