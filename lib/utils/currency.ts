/**
 * Format a number as Algerian Dinar currency
 * 
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', { 
    style: 'currency', 
    currency: 'DZD',
    maximumFractionDigits: 0, // No decimal places for DZD
  }).format(amount);
}

/**
 * Format a currency amount with a + or - sign
 * 
 * @param amount - The amount to format
 * @returns Formatted currency string with sign
 */
export function formatCurrencyWithSign(amount: number): string {
  const formatted = formatCurrency(Math.abs(amount));
  return amount >= 0 ? `+${formatted}` : `-${formatted}`;
}

/**
 * Parse a currency string into a number
 * 
 * @param currencyString - The currency string to parse
 * @returns Parsed number value
 */
export function parseCurrency(currencyString: string): number {
  // Remove currency symbol, commas and spaces
  const cleanedString = currencyString.replace(/[^\d.-]/g, '');
  return parseFloat(cleanedString) || 0;
} 