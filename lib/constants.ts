// Predefined palette of pastel colors
export const PRESET_COLORS = [
  "#FF9E9E", // Light Red
  "#FFDDC7", // Peach
  "#E6F2D4", // Light Green
  "#B3EABE", // Mint Green
  "#D1C2F4", // Light Purple
  "#B8F4EA", // Light Teal
  "#F2D388", // Light Yellow
  "#E6E6FA", // Lavender
  "#FFC694", // Light Orange
  "#A8D6E6", // Light Blue
  "#F9F3BE", // Pale Yellow
  "#98FF98", // Bright Green
  "#FFC2C2", // Pink
  "#E4E4E4", // Light Gray
  "#FFF8D6", // Cream
  "#F0FFF0", // Honeydew
  "#FBCEB1", // Apricot
  "#BDFCC9"  // Pale Green
];

/**
 * Get a random color from the palette
 */
export const getRandomPresetColor = (): string => {
  const randomIndex = Math.floor(Math.random() * PRESET_COLORS.length);
  return PRESET_COLORS[randomIndex];
};

/**
 * Get a color that is different from the last used colors
 * @param lastColors Array of recently used colors to avoid
 * @returns A color from the palette that is not in the lastColors array if possible
 */
export const getDifferentColor = (lastColors: string[] = []): string => {
  // If there are no colors to avoid, just return a random one
  if (!lastColors.length) {
    return getRandomPresetColor();
  }

  // Find colors that haven't been used recently
  const availableColors = PRESET_COLORS.filter(color => !lastColors.includes(color));
  
  // If all colors have been used, just return a random one
  if (!availableColors.length) {
    return getRandomPresetColor();
  }
  
  // Return a random color from the available ones
  const randomIndex = Math.floor(Math.random() * availableColors.length);
  return availableColors[randomIndex];
};

/**
 * Get unique colors for items within a category
 * @param itemCount Number of items that need colors
 * @returns Array of unique colors for the items
 */
export const getUniqueColorsForCategory = (itemCount: number): string[] => {
  const colors: string[] = [];
  const usedColors: string[] = [];
  
  for (let i = 0; i < itemCount; i++) {
    const color = getDifferentColor(usedColors);
    colors.push(color);
    usedColors.push(color);
    
    // If we've used all available colors, reset the used colors array
    if (usedColors.length >= PRESET_COLORS.length) {
      usedColors.length = 0;
    }
  }
  
  return colors;
};