import { NextRequest } from 'next/server';
import { verifyToken, type JwtPayload } from './auth/new-auth-service';
import { jwtDecode } from 'jwt-decode';

export interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    name: string;
    email?: string;
    role: string;
    restaurantId: string;
    permissions?: any;
    restricted?: boolean;
    admin?: boolean; // admin-session token
  };
  error?: string;
}

/**
 * Verify JWT authentication from request headers or cookies
 */
export async function verifyJwtAuth(req: NextRequest): Promise<AuthResult> {
  try {
    // Get token from Authorization header or cookies
    const authHeader = req.headers.get('Authorization');
    const token = authHeader
      ? authHeader.replace('Bearer ', '')
      : req.cookies.get('auth_token')?.value;

    if (!token) {
      return {
        success: false,
        error: 'Authentication required',
      };
    }

    // Verify token (signature when possible; decode fallback for static/electron)
    const payload = verifyToken(token);

    if (!payload) {
      return {
        success: false,
        error: 'Invalid authentication token',
      };
    }

    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp < currentTime) {
      return {
        success: false,
        error: 'Authentication token expired',
      };
    }

    // Return user info from token
    const isAdminSession = (payload as any)?.admin === true;
    return {
      success: true,
      user: {
        id: payload.sub,
        name: payload.name,
        email: payload.email,
        role: payload.role,
        restaurantId: payload.restaurantId,
        permissions: payload.permissions,
        restricted: payload.restricted === true,
        admin: isAdminSession,
      },
    };
  } catch (error) {
    console.error('Error verifying JWT:', error);
    return {
      success: false,
      error: 'Authentication error',
    };
  }
}

/**
 * Authorization helper: enforce restricted flag and optional role/permission checks
 * deny-by-default style: if any check fails -> {authorized:false, status, error}
 */
export function authorize(opts: {
  auth: AuthResult;
  roles?: string[]; // allowed roles
  requireNotRestricted?: boolean; // default true
  requireAdminSession?: boolean; // require admin login session
  requirePermissions?: {
    pages?: (keyof NonNullable<NonNullable<JwtPayload['permissions']>['pages']>)[];
  };
}): { authorized: true } | { authorized: false; status: number; error: string } {
  const { auth, roles, requireNotRestricted = true, requireAdminSession = false, requirePermissions } = opts;

  if (!auth.success || !auth.user) {
    return { authorized: false, status: 401, error: 'Unauthorized' };
  }

  const { role, permissions, restricted, admin } = auth.user;

  if (requireNotRestricted && restricted) {
    return { authorized: false, status: 403, error: 'Account restricted' };
  }

  if (requireAdminSession && admin !== true) {
    return { authorized: false, status: 403, error: 'Admin session required' };
  }

  if (roles && roles.length > 0 && !roles.includes(role)) {
    return { authorized: false, status: 403, error: 'Insufficient role' };
  }

  if (requirePermissions?.pages && requirePermissions.pages.length > 0) {
    const pagePerms = permissions?.pages || {};
    const missing = requirePermissions.pages.filter((p) => pagePerms[p] !== true);
    if (missing.length > 0) {
      return {
        authorized: false,
        status: 403,
        error: `Missing page permissions: ${missing.join(', ')}`,
      };
    }
  }

  return { authorized: true };
}