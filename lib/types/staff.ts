/**
 * Staff Types - Single Source of Truth
 * 
 * This file contains all staff-related type definitions.
 * Use these types consistently across the application.
 */

export type StaffRole = 
  | 'MANAGER' 
  | 'CHEF' 
  | 'WAITER' 
  | 'BARTENDER' 
  | 'HOST' 
  | 'KITCHEN_HELPER' 
  | 'CASHIER' 
  | 'CLEANER' 
  | 'DELIVERY';

export type PaymentType = 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'PER_SHIFT';

export interface PaymentConfig {
  type: PaymentType;
  baseSalary: number;
  shiftRate?: number;
  shiftRates?: Record<string, number>; // Specific rates per shift ID
  paymentDay?: number; // Day of month (1-31) for monthly, day of week (0-6) for weekly
  nextPaymentDueDate?: string; // ISO date for next payment due
}

export interface ShiftConfig {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  color?: string;
}

export interface WeeklySchedule {
  monday: string[];
  tuesday: string[];
  wednesday: string[];
  thursday: string[];
  friday: string[];
  saturday: string[];
  sunday: string[];
}

export interface StaffSchedule {
  staffId: string;
  weeklySchedule: WeeklySchedule;
  effectiveFrom: string;
  effectiveTo?: string;
  isActive: boolean;
}

export interface StaffPresence {
  staffId: string;
  isPresent: boolean;
  attendanceHistory: AttendanceRecord[];
  totalHoursThisWeek: number;
  totalHoursThisMonth: number;
}

export interface AttendanceRecord {
  id: string;
  date: string;
  staffId: string;
  staffName: string;
  shiftId: string;
  shiftName: string;
  attended: boolean;
  status: 'present' | 'late' | 'absent';
  notes?: string;
  isPaid?: boolean; // Track if this shift has been paid for
}

export interface StaffMember {
  id: string;
  name: string;
  role: StaffRole;
  email?: string;
  phone?: string;
  contact?: string; // For backwards compatibility
  status: 'ACTIVE' | 'INACTIVE';
  paymentConfig: PaymentConfig;
  presence?: StaffPresence;
  schedule?: StaffSchedule;
  shift?: ShiftConfig;
  
  // Auth-related fields
  userId?: string;
  hasUserAccount: boolean;
  username?: string;
  
  // Permissions
  permissions?: {
    pages: {
      menu: boolean;
      orders: boolean;
      finance: boolean;
      inventory: boolean;
      staff: boolean;
      settings: boolean;
      suppliers: boolean;
    };
    tabs?: {
      inventory?: {
        inventory?: boolean;
        subrecipes?: boolean;
        counts?: boolean;
        waste?: boolean;
      };
      staff?: {
        shifts_schedule?: boolean;
        attendance?: boolean;
        payments?: boolean;
      };
    };
  };
  
  // Database fields
  _id?: string;
  _rev?: string;
  type?: 'staff';
  // Optional legacy field used by some UI components
  startDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Payment {
  id: string;
  staffId: string;
  staffName: string;
  amount: number;
  currency: string;
  paymentDate: string;
  paymentType: 'salary' | 'bonus' | 'advance' | 'deduction' | 'shift_payment';
  period?: {
    startDate: string;
    endDate: string;
  };
  notes?: string;
  createdAt: string;
}

export interface StaffDocument {
  _id: string;
  members: StaffMember[];
  shifts: ShiftConfig[];
  payments: Payment[];
} 