// Build configuration and environment detection
export const BUILD_CONFIG = {
  // Properly detect if we're in a static build vs dev mode
  isStatic: typeof window !== 'undefined' && (
    process.env.BUILD_TARGET === 'static' || 
    process.env.BUILD_TARGET === 'electron' || 
    process.env.BUILD_TARGET === 'mobile'
  ),
  
  // Platform detection
  isElectron: typeof window !== 'undefined' && window.navigator.userAgent.includes('Electron'),
  isMobile: typeof window !== 'undefined' && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
  
  // Environment detection - properly detect dev vs production
  isDevelopment: typeof window !== 'undefined' ? 
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') :
    process.env.NODE_ENV === 'development',
  isProduction: typeof window !== 'undefined' ? 
    (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') :
    process.env.NODE_ENV === 'production',
  
  // Server configuration - use localhost in dev, remote in production
  remoteServerUrl: (() => {
    if (typeof window !== 'undefined') {
      // In browser: check if we're on localhost
      const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      return isLocalhost ? 'http://localhost:3000' : 'https://bistro.icu';
    }
    // On server: use environment or default
    return process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://bistro.icu';
  })(),
  fallbackServerUrl: 'https://bistro.icu',
  
  // Feature flags based on actual build target and environment
  features: (() => {
    const isStaticBuild = typeof window !== 'undefined' && (
      process.env.BUILD_TARGET === 'static' || 
      process.env.BUILD_TARGET === 'electron' || 
      process.env.BUILD_TARGET === 'mobile'
    );
    
    const isLocalhost = typeof window !== 'undefined' && 
      (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
    
    const hasServerFeatures = typeof window === 'undefined' || (isLocalhost && !isStaticBuild);
    
    return {
      // Server-side features available in dev mode and server builds
      serverAuth: hasServerFeatures,
      apiRoutes: hasServerFeatures,
      ssr: typeof window === 'undefined',
      
      // Remote API access for static builds when online
      remoteApi: true,
      
      // Client-side features available in all builds
      offlineSync: true,
      pouchdb: true,
      localDb: true,
    };
  })()
};

// Helper to check if server-side features are available
export const canUseServerFeatures = () => {
  return BUILD_CONFIG.features.serverAuth && typeof window === 'undefined';
};

// Helper to get the appropriate database adapter
export const getDbAdapter = () => {
  if (BUILD_CONFIG.isElectron) {
    return 'http'; // For Electron with CouchDB
  }
  if (BUILD_CONFIG.isMobile) {
    return 'memory'; // For Capacitor mobile apps
  }
  return 'idb'; // For web browsers
};

// API URL helper that works in all environments
export const getApiUrl = (endpoint: string) => {
  // Dynamic detection - check if we're on localhost at runtime
  if (typeof window !== 'undefined') {
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    if (isLocalhost) {
      console.log('🏠 Using localhost for API:', `http://localhost:3000/api/${endpoint}`);
      return `http://localhost:3000/api/${endpoint}`;
    } else {
      console.log('🌐 Using remote server for API:', `https://bistro.icu/api/${endpoint}`);
      return `https://bistro.icu/api/${endpoint}`;
    }
  }
  
  // Server-side: use environment-based detection
  const serverUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://bistro.icu';
  return `${serverUrl}/api/${endpoint}`;
};

// Helper to get fallback URL
export const getFallbackApiUrl = (endpoint: string) => {
  return `${BUILD_CONFIG.fallbackServerUrl}/api/${endpoint}`;
};

// Helper to check if we can reach the remote server
export const checkRemoteConnectivity = async (): Promise<boolean> => {
  // Dynamic URL detection
  const serverUrl = typeof window !== 'undefined' && 
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') ?
    'http://localhost:3000' : 'https://bistro.icu';
    
  // Try primary server first
  try {
    const response = await fetch(`${serverUrl}/api/health`, {
      method: 'GET',
      mode: 'cors',
      credentials: 'omit', // Don't send cookies for health check
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000) // Increased timeout
    });
    if (response.ok) {
      console.log(`✅ ${serverUrl} is reachable`);
      return true;
    }
    console.log(`❌ ${serverUrl} responded with status: ${response.status}`);
  } catch (error) {
    console.log(`❌ ${serverUrl} not reachable`, error instanceof Error ? error.message : error);
  }
  
  // Only try fallback if we're not already trying localhost
  if (serverUrl.includes('localhost')) {
    console.log('🌐 Localhost failed, trying bistro.icu fallback...');
    try {
      const response = await fetch('https://bistro.icu/api/health', {
      method: 'GET',
      mode: 'cors',
      credentials: 'omit', // Don't send cookies for health check
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
        signal: AbortSignal.timeout(8000) // Longer timeout for remote server
      });
      if (response.ok) {
        console.log('✅ bistro.icu fallback is reachable');
        return true;
      }
      console.log(`❌ bistro.icu responded with status: ${response.status}`);
    } catch (error) {
      console.log('❌ bistro.icu fallback also not reachable', error instanceof Error ? error.message : error);
    }
  }
  
  console.log('🔌 Server not reachable - user can choose offline mode if needed');
  return false;
}; 