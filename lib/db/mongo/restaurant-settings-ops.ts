import clientPromise from '@/lib/mongodb';

// Interface for Google Drive settings in MongoDB
export interface MongoRestaurantGoogleDrive {
  _id?: string; // MongoDB ObjectId
  restaurantId: string; // Restaurant identifier
  googleDrive: {
    enabled: boolean;
    type: 'oauth' | 'service_account';
    encodedCredentials: string; // Base64 encoded JSON string (we'll add encryption later)
    setupBy: string; // User ID who set it up
    setupDate: string; // ISO date string
    lastTested?: string; // ISO date string
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Encode sensitive data (base64 for now, encryption can be added later)
 */
function encodeData(data: string): string {
  try {
    return Buffer.from(data, 'utf8').toString('base64');
  } catch (error) {
    console.error('❌ Encoding failed:', error);
    throw new Error('Failed to encode data');
  }
}

/**
 * Decode sensitive data
 */
function decodeData(encodedData: string): string {
  try {
    return Buffer.from(encodedData, 'base64').toString('utf8');
  } catch (error) {
    console.error('❌ Decoding failed:', error);
    throw new Error('Failed to decode data');
  }
}

/**
 * Save Google Drive configuration to MongoDB
 */
export async function saveGoogleDriveConfig(
  restaurantId: string,
  config: {
    type: 'oauth' | 'service_account';
    credentials: any; // The actual credentials object
    setupBy: string;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('💾 Saving Google Drive config to MongoDB for restaurant:', restaurantId);
    
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const collection = db.collection<MongoRestaurantGoogleDrive>('restaurant_google_drive');

    // Encode the credentials
    const encodedCredentials = encodeData(JSON.stringify(config.credentials));
    
    const now = new Date().toISOString();
    const document: MongoRestaurantGoogleDrive = {
      restaurantId,
      googleDrive: {
        enabled: true,
        type: config.type,
        encodedCredentials,
        setupBy: config.setupBy,
        setupDate: now,
        lastTested: now
      },
      createdAt: now,
      updatedAt: now
    };

    // Upsert the document (update if exists, insert if not)
    const result = await collection.replaceOne(
      { restaurantId },
      document,
      { upsert: true }
    );

    console.log('✅ Google Drive config saved to MongoDB:', result.upsertedId || 'updated');
    return { success: true };

  } catch (error) {
    console.error('❌ Failed to save Google Drive config to MongoDB:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Get Google Drive configuration from MongoDB
 */
export async function getGoogleDriveConfig(
  restaurantId: string
): Promise<{ 
  success: boolean; 
  config?: { 
    type: 'oauth' | 'service_account'; 
    credentials: any; 
    setupBy: string;
    setupDate: string;
    lastTested?: string;
  }; 
  error?: string 
}> {
  try {
    console.log('📖 Getting Google Drive config from MongoDB for restaurant:', restaurantId);
    
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const collection = db.collection<MongoRestaurantGoogleDrive>('restaurant_google_drive');

    const document = await collection.findOne({ restaurantId });
    
    if (!document || !document.googleDrive.enabled) {
      console.log('📭 No Google Drive config found for restaurant:', restaurantId);
      return { success: false, error: 'No Google Drive configuration found' };
    }

    // Decode the credentials
    const credentialsJson = decodeData(document.googleDrive.encodedCredentials);
    const credentials = JSON.parse(credentialsJson);

    console.log('✅ Google Drive config retrieved from MongoDB');
    return {
      success: true,
      config: {
        type: document.googleDrive.type,
        credentials,
        setupBy: document.googleDrive.setupBy,
        setupDate: document.googleDrive.setupDate,
        lastTested: document.googleDrive.lastTested
      }
    };

  } catch (error) {
    console.error('❌ Failed to get Google Drive config from MongoDB:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Update last tested timestamp
 */
export async function updateGoogleDriveLastTested(
  restaurantId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('⏰ Updating Google Drive last tested timestamp for restaurant:', restaurantId);
    
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const collection = db.collection<MongoRestaurantGoogleDrive>('restaurant_google_drive');

    const result = await collection.updateOne(
      { restaurantId },
      { 
        $set: { 
          'googleDrive.lastTested': new Date().toISOString(),
          updatedAt: new Date().toISOString()
        } 
      }
    );

    if (result.matchedCount === 0) {
      return { success: false, error: 'No Google Drive configuration found to update' };
    }

    console.log('✅ Google Drive last tested timestamp updated');
    return { success: true };

  } catch (error) {
    console.error('❌ Failed to update Google Drive last tested:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Delete Google Drive configuration
 */
export async function deleteGoogleDriveConfig(
  restaurantId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🗑️ Deleting Google Drive config from MongoDB for restaurant:', restaurantId);
    
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const collection = db.collection<MongoRestaurantGoogleDrive>('restaurant_google_drive');

    const result = await collection.deleteOne({ restaurantId });

    if (result.deletedCount === 0) {
      return { success: false, error: 'No Google Drive configuration found to delete' };
    }

    console.log('✅ Google Drive config deleted from MongoDB');
    return { success: true };

  } catch (error) {
    console.error('❌ Failed to delete Google Drive config from MongoDB:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Check if Google Drive is configured for a restaurant
 */
export async function isGoogleDriveConfigured(
  restaurantId: string
): Promise<boolean> {
  try {
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const collection = db.collection<MongoRestaurantGoogleDrive>('restaurant_google_drive');

    const document = await collection.findOne(
      { restaurantId }, 
      { projection: { 'googleDrive.enabled': 1 } }
    );

    return document?.googleDrive?.enabled === true;
  } catch (error) {
    console.error('❌ Failed to check Google Drive configuration:', error);
    return false;
  }
} 