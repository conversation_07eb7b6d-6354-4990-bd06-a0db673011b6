/**
 * Server-side database functions
 *
 * This file contains server-side implementations of database functions
 * that are needed for staff creation and management.
 */

import { cleanRestaurantId } from "@/lib/db/db-utils";
import PouchDB from 'pouchdb-core';
import PouchDBMemoryAdapter from 'pouchdb-adapter-memory';
// Import find plugin if needed
// import PouchDBFind from 'pouchdb-find';
// PouchDB.plugin(PouchDBFind);

// Configure PouchDB to use the memory adapter for server-side instances
PouchDB.plugin(PouchDBMemoryAdapter);

// Configure PouchDB for server-side use
const SERVER_DB_PATH = process.env.SERVER_DB_PATH || './data';

// Define type for staff member
interface StaffMember {
  id: string;
  name: string;
  role: string;
  [key: string]: any; // Allow additional properties
}

// Define type for staff document
interface StaffDoc {
  _id: string;
  _rev?: string;
  members: StaffMember[];
  [key: string]: any; // Allow additional properties
}

/**
 * Get a PouchDB instance for a restaurant
 */
function getRestaurantDB(restaurantId: string) {
  const cleanedRestaurantId = cleanRestaurantId(restaurantId);
  const dbName = `resto-${cleanedRestaurantId}`;
  // For server-side API routes, use the memory adapter to avoid native build issues.
  // The dbName is still used to keep instances separate if multiple are created.
  return new PouchDB(dbName, { adapter: 'memory' });
}

/**
 * Add a staff member to the restaurant database
 */
export async function serverAddStaffMember(restaurantId: string, staffMember: StaffMember) {
  try {
    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);
    const db = getRestaurantDB(cleanedRestaurantId);

    // Get the staff document
    try {
      const staffDoc = await db.get('staff') as StaffDoc;
      
      // Add the staff member, but first check if a staff member with the same ID already exists
      staffDoc.members = staffDoc.members || [];

      // Check if a staff member with the same ID already exists
      const existingStaffIndex = staffDoc.members.findIndex((m: StaffMember) => m.id === staffMember.id);

      if (existingStaffIndex !== -1) {
        console.log(`Server DB: Staff member with ID ${staffMember.id} already exists, updating instead of adding`);
        // Update the existing staff member instead of adding a new one
        staffDoc.members[existingStaffIndex] = {
          ...staffDoc.members[existingStaffIndex],
          ...staffMember
        };
      } else {
        // Add the new staff member
        staffDoc.members.push(staffMember);
      }

      // Update the staff document
      await db.put(staffDoc);
      return staffMember;
    } catch (e: any) {
      if (e.name === 'not_found') {
        // Staff document doesn't exist, create it
        const newStaffDoc: StaffDoc = {
          _id: 'staff',
          members: [staffMember]
        };
        await db.put(newStaffDoc);
        return staffMember;
      } else {
        throw e;
      }
    }
  } catch (error) {
    console.error(`Server DB: Error adding staff member:`, error);
    throw error;
  }
}

/**
 * Update a staff member in the restaurant database
 */
export async function serverUpdateStaffMember(restaurantId: string, staffId: string, updates: Partial<StaffMember>) {
  try {
    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);
    const db = getRestaurantDB(cleanedRestaurantId);

    // Get the staff document
    const staffDoc = await db.get('staff') as StaffDoc;

    // Find the staff member
    const staffIndex = staffDoc.members.findIndex((m: StaffMember) => m.id === staffId);
    if (staffIndex === -1) {
      throw new Error(`Staff member with ID ${staffId} not found`);
    }

    // Update the staff member
    staffDoc.members[staffIndex] = {
      ...staffDoc.members[staffIndex],
      ...updates
    };

    // Update the staff document
    await db.put(staffDoc);

    return staffDoc.members[staffIndex];
  } catch (error) {
    console.error(`Server DB: Error updating staff member:`, error);
    throw error;
  }
}

/**
 * Get the staff document from the restaurant database
 */
export async function serverGetStaff(restaurantId: string): Promise<StaffDoc> {
  try {
    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);
    const db = getRestaurantDB(cleanedRestaurantId);

    // Get the staff document
    try {
      return await db.get('staff') as StaffDoc;
    } catch (e: any) {
      if (e.name === 'not_found') {
        // Staff document doesn't exist, create it
        const newStaffDoc: StaffDoc = {
          _id: 'staff',
          members: []
        };
        await db.put(newStaffDoc);
        return newStaffDoc;
      } else {
        throw e;
      }
    }
  } catch (error) {
    console.error(`Server DB: Error getting staff:`, error);
    throw error;
  }
}
