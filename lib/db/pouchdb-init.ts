"use client";

import { isElectronEnvironment } from './electron-db';

/**
 * 🔧 Clean Mobile PouchDB Initialization
 *
 * Simplified, reliable PouchDB loading for mobile environments
 * No over-engineering, no complex fallbacks, just what works
 */

/**
 * Detect mobile environment reliably
 */
export const isCapacitorEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Primary check: Capacitor object exists
  if ((window as any).Capacitor) {
    return true;
  }

  // Secondary check: Capacitor in user agent
  if (typeof navigator !== 'undefined' && navigator.userAgent.includes('capacitor')) {
    return true;
  }

  // For development: only return true for mobile UA if we're NOT in Electron
  // and NOT in a regular browser (avoid false positives in dev tools)
  const hasElectronAPI = !!(window as any).electronAPI;
  const hasDesktopFlag = !!(window as any).IS_DESKTOP_APP;
  const isElectron = hasElectronAPI || hasDesktopFlag;

  if (isElectron) {
    return false; // Definitely not mobile if we're in Electron
  }

  // Only consider mobile UA if we have other mobile indicators
  const isMobileUA = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const hasTouch = 'ontouchstart' in window;
  const hasOrientation = 'orientation' in window;

  // Be more conservative - require multiple mobile indicators
  return isMobileUA && (hasTouch || hasOrientation);
};

/**
 * Load PouchDB for mobile environments
 */
async function loadMobilePouchDB(): Promise<any> {
  console.log('📱 Loading PouchDB for mobile...');

  try {
    // Import PouchDB core
    const PouchDBModule = await import('pouchdb');
    let PouchDB = PouchDBModule.default || PouchDBModule;

    // Handle different module formats
    if (typeof PouchDB !== 'function' && PouchDB.default) {
      PouchDB = PouchDB.default;
    }

    // Import and apply pouchdb-find plugin
    const PouchDBFindModule = await import('pouchdb-find');
    const PouchDBFind = PouchDBFindModule.default || PouchDBFindModule;
    PouchDB.plugin(PouchDBFind);

    // Test basic functionality
    console.log('📱 Testing PouchDB mobile functionality...');
    const testDb = new (PouchDB as any)('mobile_test', {
      adapter: 'idb',  // Force IndexedDB for mobile
      auto_compaction: true
    });

    // Verify sync methods exist
    const hasSync = typeof testDb.sync === 'function';
    const hasReplicate = typeof testDb.replicate === 'object';

    console.log('📱 PouchDB mobile capabilities:', { hasSync, hasReplicate });

    // Clean up test
    await testDb.destroy();

    if (!hasSync) {
      throw new Error('PouchDB sync method not available');
    }

    // Store globally
    (window as any).PouchDB = PouchDB;
    console.log('📱 ✅ Mobile PouchDB loaded successfully');

    return PouchDB;

  } catch (error) {
    console.error('📱 ❌ Mobile PouchDB loading failed:', error);
    throw new Error(`Mobile PouchDB initialization failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Load PouchDB for desktop/browser environments
 */
async function loadDesktopPouchDB(isDesktop: boolean): Promise<any> {
  console.log('🖥️ Loading PouchDB for desktop/browser...');

  try {
    // Import PouchDB core
    const PouchDBModule = await import('pouchdb');
    let PouchDB = PouchDBModule.default || PouchDBModule;

    // Handle different module formats
    if (typeof PouchDB !== 'function' && PouchDB.default) {
      PouchDB = PouchDB.default;
    }

    // Import and apply pouchdb-find plugin
    const PouchDBFindModule = await import('pouchdb-find');
    const PouchDBFind = PouchDBFindModule.default || PouchDBFindModule;
    PouchDB.plugin(PouchDBFind);

    // Store globally
    (window as any).PouchDB = PouchDB;

    if (isDesktop) {
      console.log('🖥️ ✅ Desktop PouchDB loaded (will use IPC to CouchDB)');
    } else {
      console.log('🌐 ✅ Browser PouchDB loaded (will use IndexedDB)');
    }

    return PouchDB;

  } catch (error) {
    console.error('🖥️ ❌ Desktop PouchDB loading failed:', error);
    throw error;
  }
}

/**
 * Main PouchDB initialization function
 */
export const initPouchDB = async (): Promise<any> => {
  // Server-side guard
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Check if already loaded
    if ((window as any).PouchDB) {
      console.log('✅ PouchDB already loaded');
      return (window as any).PouchDB;
    }

    const isMobile = isCapacitorEnvironment();
    const isDesktop = isElectronEnvironment();

    console.log('🔍 Environment detected:', { isMobile, isDesktop });

    // Load based on environment
    if (isMobile) {
      return await loadMobilePouchDB();
    } else {
      return await loadDesktopPouchDB(isDesktop);
    }

  } catch (error) {
    console.error('❌ PouchDB initialization failed:', error);
    throw error;
  }
};