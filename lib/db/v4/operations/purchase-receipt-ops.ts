/**
 * 🚀 Purchase Receipt Operations
 * 
 * Clean, unified system for managing receipt images on purchase transactions.
 * Uses PouchDB attachments with consistent naming and error handling.
 */

import { databaseV4 } from '../core/db-instance';
import { updateTransactionReceiptStatus } from './purchase-transaction-ops';
import {
  addImageAttachment,
  getImageAttachment,
  removeImageAttachment,
  hasImageAttachment
} from '../utils/image-attachment-utils';

/**
 * Generate standardized receipt attachment filename
 */
export function generateReceiptFilename(transactionId: string, extension: string = 'webp'): string {
  const sanitizedId = transactionId.replace(/[^a-zA-Z0-9]/g, '_');
  return `receipt_${sanitizedId}.${extension}`;
}

/**
 * Add a receipt image to a purchase transaction
 * @deprecated Use attachReceiptImage from purchase-transaction-ops.ts instead
 */
export async function addReceiptToTransaction(
  transactionId: string,
  imageFile: File,
  options?: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }
): Promise<string> {
  // This function is deprecated - use the new atomic approach
  throw new Error('addReceiptToTransaction is deprecated. Use attachReceiptImage from purchase-transaction-ops.ts instead.');
}

/**
 * Get receipt image data from a purchase transaction
 */
export async function getReceiptFromTransaction(
  transactionId: string,
  receiptFilename?: string
): Promise<{
  dataUrl: string;
  contentType: string;
  filename: string;
} | null> {
  try {
    console.log(`[getReceiptFromTransaction] Starting retrieval for transaction: ${transactionId}`);
    console.log(`[getReceiptFromTransaction] Provided filename: ${receiptFilename}`);
    
    let filename = receiptFilename;
    
    // If no filename provided, get it from the transaction document
    if (!filename) {
      console.log(`[getReceiptFromTransaction] No filename provided, checking transaction document...`);
      try {
        const transaction = await databaseV4.getDoc(transactionId);
        if (transaction.hasReceiptImage && transaction.receiptImage) {
          filename = transaction.receiptImage;
          console.log(`[getReceiptFromTransaction] Found filename in transaction: ${filename}`);
        } else {
          console.log(`[getReceiptFromTransaction] Transaction has no receipt image`);
          return null;
        }
      } catch (error) {
        console.error(`[getReceiptFromTransaction] Failed to get transaction document:`, error);
        return null;
      }
    }
    
    console.log(`[getReceiptFromTransaction] Using filename: ${filename}`);
    
    // Check if attachment exists
    console.log(`[getReceiptFromTransaction] Checking if attachment exists...`);
    const hasAttachment = await hasImageAttachment(transactionId, filename);
    console.log(`[getReceiptFromTransaction] Attachment exists: ${hasAttachment}`);
    
    if (!hasAttachment) {
      console.log(`[getReceiptFromTransaction] No receipt found for transaction: ${transactionId}`);
      return null;
    }

    // Get attachment data
    console.log(`[getReceiptFromTransaction] Getting attachment data...`);
    const dataUrl = await getImageAttachment(transactionId, filename);
    console.log(`[getReceiptFromTransaction] Retrieved data URL length: ${dataUrl?.length || 0}`);
    
    if (!dataUrl) {
      console.warn(`[getReceiptFromTransaction] Failed to retrieve receipt data for: ${transactionId}`);
      return null;
    }

    // Extract content type from data URL
    const contentTypeMatch = dataUrl.match(/^data:([^;]+);base64,/);
    const contentType = contentTypeMatch ? contentTypeMatch[1] : 'image/webp';
    console.log(`[getReceiptFromTransaction] Detected content type: ${contentType}`);

    console.log(`[getReceiptFromTransaction] ✅ Retrieved receipt: ${filename}`);
    return {
      dataUrl,
      contentType,
      filename
    };
  } catch (error) {
    console.error(`[getReceiptFromTransaction] Error retrieving receipt from ${transactionId}:`, error);
    return null;
  }
}

/**
 * Remove receipt image from a purchase transaction
 */
export async function removeReceiptFromTransaction(
  transactionId: string,
  receiptFilename?: string
): Promise<void> {
  try {
    const filename = receiptFilename || generateReceiptFilename(transactionId);
    
    console.log(`[removeReceiptFromTransaction] Removing receipt: ${filename}`);
    
    // Remove the attachment
    await removeImageAttachment(transactionId, filename);

    // Update transaction to reflect no receipt
    await updateTransactionReceiptStatus(transactionId, false);

    console.log(`[removeReceiptFromTransaction] ✅ Receipt removed: ${filename}`);
  } catch (error) {
    console.error(`[removeReceiptFromTransaction] Error removing receipt from ${transactionId}:`, error);
    throw error;
  }
}

/**
 * Update receipt image for a purchase transaction (replace existing)
 */
export async function updateReceiptForTransaction(
  transactionId: string,
  newImageFile: File,
  options?: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }
): Promise<string> {
  try {
    console.log(`[updateReceiptForTransaction] Updating receipt for transaction: ${transactionId}`);
    
    // Remove existing receipt if it exists
    const filename = generateReceiptFilename(transactionId, options?.format || 'webp');
    if (await hasImageAttachment(transactionId, filename)) {
      await removeReceiptFromTransaction(transactionId, filename);
    }

    // Add new receipt
    return await addReceiptToTransaction(transactionId, newImageFile, options);
  } catch (error) {
    console.error(`[updateReceiptForTransaction] Error updating receipt for ${transactionId}:`, error);
    throw error;
  }
}

/**
 * Check if a transaction has a receipt image
 */
export async function hasReceiptImage(transactionId: string, receiptFilename?: string): Promise<boolean> {
  try {
    const filename = receiptFilename || generateReceiptFilename(transactionId);
    return await hasImageAttachment(transactionId, filename);
  } catch (error) {
    console.error(`[hasReceiptImage] Error checking receipt for ${transactionId}:`, error);
    return false;
  }
}

/**
 * Get receipt image URL for display (creates blob URL)
 */
export async function getReceiptDisplayUrl(
  transactionId: string,
  receiptFilename?: string
): Promise<string | null> {
  try {
    const receiptData = await getReceiptFromTransaction(transactionId, receiptFilename);
    
    if (!receiptData) return null;

    // Convert data URL to blob and create object URL for better memory management
    const response = await fetch(receiptData.dataUrl);
    const blob = await response.blob();
    
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error(`[getReceiptDisplayUrl] Error creating display URL for ${transactionId}:`, error);
    return null;
  }
}

/**
 * Bulk operations for receipt management
 */
export async function bulkRemoveReceiptsFromTransactions(transactionIds: string[]): Promise<{
  successful: string[];
  failed: Array<{ transactionId: string; error: string }>;
}> {
  const successful: string[] = [];
  const failed: Array<{ transactionId: string; error: string }> = [];

  console.log(`[bulkRemoveReceiptsFromTransactions] Removing receipts from ${transactionIds.length} transactions`);

  for (const transactionId of transactionIds) {
    try {
      await removeReceiptFromTransaction(transactionId);
      successful.push(transactionId);
    } catch (error) {
      failed.push({
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  console.log(`[bulkRemoveReceiptsFromTransactions] ✅ Completed: ${successful.length} successful, ${failed.length} failed`);
  return { successful, failed };
}

/**
 * Get all transactions with receipt images (for cleanup/analysis)
 */
export async function getTransactionsWithReceipts(): Promise<Array<{
  transactionId: string;
  filename: string;
  hasAttachment: boolean;
}>> {
  try {
    console.log('[getTransactionsWithReceipts] Finding transactions with receipt images');
    
    const result = await databaseV4.findDocs({
      selector: {
        type: 'purchase_transaction',
        hasReceiptImage: true
      }
    });

    const transactionsWithReceipts = [];
    
    for (const transaction of result.docs) {
      const filename = generateReceiptFilename(transaction._id);
      const hasAttachment = await hasImageAttachment(transaction._id, filename);
      
      transactionsWithReceipts.push({
        transactionId: transaction._id,
        filename,
        hasAttachment
      });
    }

    console.log(`[getTransactionsWithReceipts] ✅ Found ${transactionsWithReceipts.length} transactions with receipts`);
    return transactionsWithReceipts;
  } catch (error) {
    console.error('[getTransactionsWithReceipts] Error:', error);
    throw error;
  }
}