import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import { InventoryDocument, StockItem, DEFAULT_INVENTORY_DOCUMENT } from '../schemas/inventory-schema';
import { StockAdjustment, StockCount, StockCountItem, WasteLog, ConsumptionLog, PurchaseLog } from '../schemas/inventory-schema';
// Import snapshot types
import { InventorySnapshot, SnapshotItem } from '../../../../types/inventorySnapshots';
// Import recipe types for consumption calculation
import { MenuItemRecipe } from '../schemas/menu-item-recipe-schema';
import { SubRecipe } from '../schemas/sub-recipe-schema';
import { getAllMenuItemRecipes, getEffectiveCost } from './menu-item-recipe-ops';
import { getAllSubRecipes } from './sub-recipe-ops';
// 🚀 Import the new conflict resolution utilities
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument,
  safeEnsureDocument,
  safeUpdateArrayDocument,
  safeAddToArrayDocument,
  safeUpdateArrayItem,
  safeRemoveFromArrayDocument
} from '../core/conflict-resolution';

const SNAPSHOT_DB_IDENTIFIER = 'snapshotsDB'; // Placeholder for actual identifier strategy

export async function getInventory(): Promise<InventoryDocument> {
  return safeEnsureDocument<InventoryDocument>(
    'inventory',
    () => ({ ...DEFAULT_INVENTORY_DOCUMENT }),
    'getInventory'
  );
}

export async function updateInventory(inventory: InventoryDocument): Promise<InventoryDocument> {
  return safeUpsertDocument<InventoryDocument>(
    {
      ...inventory,
      updatedAt: new Date().toISOString()
    },
    'updateInventory'
  );
}

export async function addStockItem(item: Omit<StockItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<StockItem> {
  // Calculate cost per unit from total value if both quantity and costPerUnit (total value) are provided
  let calculatedCostPerUnit = item.costPerUnit;
  if (item.quantity && item.costPerUnit && item.quantity > 0) {
    calculatedCostPerUnit = item.costPerUnit / item.quantity;
  }

  const newItem: StockItem = {
    id: `stock_${uuidv4()}`,
    ...item,
    costPerUnit: calculatedCostPerUnit,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  await safeAddToArrayDocument<InventoryDocument, StockItem>(
    'inventory',
    'items',
    newItem,
    'addStockItem'
  );
  
  return newItem;
}

export async function updateStockItem(itemId: string, updates: Partial<StockItem>): Promise<StockItem> {
  const updatedDoc = await safeUpdateArrayItem<InventoryDocument, StockItem>(
    'inventory',
    'items',
    itemId,
    updates,
    `updateStockItem(${itemId})`
  );
  
  const updatedItem = updatedDoc.items?.find(i => i.id === itemId);
  if (!updatedItem) {
    throw new Error(`Stock item with ID ${itemId} not found after update`);
  }
  
  return updatedItem;
}

export async function deleteStockItem(itemId: string): Promise<boolean> {
  await safeRemoveFromArrayDocument<InventoryDocument, StockItem>(
    'inventory',
    'items',
    itemId,
    `deleteStockItem(${itemId})`
  );
  
  return true;
}

// StockAdjustment CRUD with conflict resolution
export async function addStockAdjustment(adjustment: Omit<StockAdjustment, 'id' | 'createdAt' | 'updatedAt'>): Promise<StockAdjustment> {
  const newAdjustment: StockAdjustment = {
    id: `adjustment_${uuidv4()}`,
    ...adjustment,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  await safeAddToArrayDocument<InventoryDocument, StockAdjustment>(
    'inventory',
    'adjustments',
    newAdjustment,
    'addStockAdjustment'
  );
  
  return newAdjustment;
}

export async function updateStockAdjustment(adjustmentId: string, updates: Partial<StockAdjustment>): Promise<StockAdjustment> {
  const updatedDoc = await safeUpdateArrayItem<InventoryDocument, StockAdjustment>(
    'inventory',
    'adjustments',
    adjustmentId,
    updates,
    `updateStockAdjustment(${adjustmentId})`
  );
  
  const updatedAdjustment = updatedDoc.adjustments?.find(a => a.id === adjustmentId);
  if (!updatedAdjustment) {
    throw new Error(`Stock adjustment with ID ${adjustmentId} not found after update`);
  }
  
  return updatedAdjustment;
}

export async function deleteStockAdjustment(adjustmentId: string): Promise<boolean> {
  await safeRemoveFromArrayDocument<InventoryDocument, StockAdjustment>(
    'inventory',
    'adjustments',
    adjustmentId,
    `deleteStockAdjustment(${adjustmentId})`
  );
  
  return true;
}


// StockCount CRUD
export async function addStockCount(count: Omit<StockCount, 'id' | 'createdAt' | 'updatedAt'>): Promise<StockCount> {
  try {
    const inventory = await getInventory();
    const newCount: StockCount = {
      id: `count_${uuidv4()}`,
      ...count,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    inventory.stockCounts = inventory.stockCounts || [];
    inventory.stockCounts.push(newCount);
    await updateInventory(inventory);
    return newCount;
  } catch (error) {
    console.error('Error adding stock count:', error);
    throw error;
  }
}

export async function updateStockCount(countId: string, updates: Partial<StockCount>): Promise<StockCount> {
  try {
    const inventory = await getInventory();
    inventory.stockCounts = inventory.stockCounts || [];
    const idx = inventory.stockCounts.findIndex(c => c.id === countId);
    if (idx === -1) throw new Error(`Stock count with ID ${countId} not found`);
    const updatedCount = { ...inventory.stockCounts[idx], ...updates, updatedAt: new Date().toISOString() };
    inventory.stockCounts[idx] = updatedCount;
    await updateInventory(inventory);
    return updatedCount;
  } catch (error) {
    console.error('Error updating stock count:', error);
    throw error;
  }
}

export async function deleteStockCount(countId: string): Promise<boolean> {
  try {
    const inventory = await getInventory();
    inventory.stockCounts = inventory.stockCounts || [];
    const idx = inventory.stockCounts.findIndex(c => c.id === countId);
    if (idx === -1) throw new Error(`Stock count with ID ${countId} not found`);
    inventory.stockCounts.splice(idx, 1);
    await updateInventory(inventory);
    return true;
  } catch (error) {
    console.error('Error deleting stock count:', error);
    throw error;
  }
}

// StockCountItem CRUD
export async function addStockCountItem(item: Omit<StockCountItem, 'id' | 'createdAt' | 'updatedAt' | 'variance' | 'varianceValue'>): Promise<StockCountItem> {
  try {
    const inventory = await getInventory();
    const newItem: StockCountItem = {
      id: `countitem_${uuidv4()}`,
      ...item,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    inventory.stockCountItems = inventory.stockCountItems || [];
    inventory.stockCountItems.push(newItem);
    await updateInventory(inventory);
    return newItem;
  } catch (error) {
    console.error('Error adding stock count item:', error);
    throw error;
  }
}

export async function updateStockCountItem(itemId: string, updates: Partial<StockCountItem>): Promise<StockCountItem> {
  try {
    const inventory = await getInventory();
    inventory.stockCountItems = inventory.stockCountItems || [];
    const idx = inventory.stockCountItems.findIndex(i => i.id === itemId);
    if (idx === -1) throw new Error(`Stock count item with ID ${itemId} not found`);
    const updatedItem = { ...inventory.stockCountItems[idx], ...updates, updatedAt: new Date().toISOString() };
    inventory.stockCountItems[idx] = updatedItem;
    await updateInventory(inventory);
    return updatedItem;
  } catch (error) {
    console.error('Error updating stock count item:', error);
    throw error;
  }
}

export async function deleteStockCountItem(itemId: string): Promise<boolean> {
  try {
    const inventory = await getInventory();
    inventory.stockCountItems = inventory.stockCountItems || [];
    const idx = inventory.stockCountItems.findIndex(i => i.id === itemId);
    if (idx === -1) throw new Error(`Stock count item with ID ${itemId} not found`);
    inventory.stockCountItems.splice(idx, 1);
    await updateInventory(inventory);
    return true;
  } catch (error) {
    console.error('Error deleting stock count item:', error);
    throw error;
  }
}

// WasteLog CRUD
export async function addWasteLog(log: Omit<WasteLog, 'id' | 'createdAt' | 'updatedAt'>): Promise<WasteLog> {
  // knowledge: waste should always reduce inventory quantity atomically
  try {
    const inventory = await getInventory();
    const newLog: WasteLog = {
      id: `waste_${uuidv4()}`,
      ...log,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    inventory.wasteLogs = inventory.wasteLogs || [];
    inventory.wasteLogs.push(newLog);
    // Update inventory quantity for the wasted item
    inventory.items = inventory.items || [];
    const idx = inventory.items.findIndex(i => i.id === log.stockItemId);
    if (idx !== -1 && typeof log.quantity === 'number' && log.quantity > 0) {
      const oldQty = typeof inventory.items[idx].quantity === 'number' ? inventory.items[idx].quantity : 0;
      inventory.items[idx].quantity = Math.max(0, oldQty - log.quantity);
      inventory.items[idx].updatedAt = new Date().toISOString();
    }
    await updateInventory(inventory);
    return newLog;
  } catch (error) {
    console.error('Error adding waste log:', error);
    throw error;
  }
}

export async function updateWasteLog(logId: string, updates: Partial<WasteLog>): Promise<WasteLog> {
  try {
    const inventory = await getInventory();
    inventory.wasteLogs = inventory.wasteLogs || [];
    const idx = inventory.wasteLogs.findIndex(l => l.id === logId);
    if (idx === -1) throw new Error(`Waste log with ID ${logId} not found`);
    const updatedLog = { ...inventory.wasteLogs[idx], ...updates, updatedAt: new Date().toISOString() };
    inventory.wasteLogs[idx] = updatedLog;
    await updateInventory(inventory);
    return updatedLog;
  } catch (error) {
    console.error('Error updating waste log:', error);
    throw error;
  }
}

export async function deleteWasteLog(logId: string): Promise<boolean> {
  try {
    const inventory = await getInventory();
    inventory.wasteLogs = inventory.wasteLogs || [];
    const idx = inventory.wasteLogs.findIndex(l => l.id === logId);
    if (idx === -1) throw new Error(`Waste log with ID ${logId} not found`);
    inventory.wasteLogs.splice(idx, 1);
    await updateInventory(inventory);
    return true;
  } catch (error) {
    console.error('Error deleting waste log:', error);
    throw error;
  }
}

/**
 * knowledge: Applies a stock count by calculating variances, updating inventory quantities,
 * and marking the count as completed. All operations are atomic and use only v4 direct logic.
 * This is the only place where inventory is updated from a stock count.
 *
 * @param stockCountId - The ID of the stock count to apply
 */
export async function applyStockCountV4(stockCountId: string): Promise<void> {
  // knowledge: wrap everything in knowledge for safety
  const inventory = await getInventory();
  inventory.stockCounts = inventory.stockCounts || [];
  inventory.stockCountItems = inventory.stockCountItems || [];
  inventory.items = inventory.items || [];

  // Find the stock count
  const countIdx = inventory.stockCounts.findIndex(c => c.id === stockCountId);
  if (countIdx === -1) throw new Error(`Stock count with ID ${stockCountId} not found`);
  const stockCount = inventory.stockCounts[countIdx];

  // Get all items for this count
  const countItems = inventory.stockCountItems.filter(i => i.stockCountId === stockCountId);

  // For each count item, calculate variance and update inventory
  for (const item of countItems) {
    // Find the inventory item
    const invIdx = inventory.items.findIndex(i => i.id === item.stockItemId);
    if (invIdx === -1) continue; // skip if item not found
    const invItem = inventory.items[invIdx];

    // If the user did NOT provide a counted quantity for this item, we skip it.
    // This allows completing a stock count even when only a subset of items has
    // been entered, preventing unintended zero-ing of untouched items.
    if (typeof item.countedQuantity !== 'number') {
      continue;
    }

    // Calculate variance against theoretical quantity
    const counted = item.countedQuantity;
    const theoretical = typeof item.theoreticalQuantity === 'number' ? item.theoreticalQuantity : 0;
    const variance = counted - theoretical;
    const cost = typeof invItem.costPerUnit === 'number' ? invItem.costPerUnit : 0;
    const varianceValue = variance * cost;

    // Update the count item with variance information
    item.variance = variance;
    item.varianceValue = varianceValue;
    item.updatedAt = new Date().toISOString();

    // Reflect the counted quantity in inventory
    invItem.quantity = counted;
    invItem.updatedAt = new Date().toISOString();
  }

  // Mark the count as completed
  inventory.stockCounts[countIdx] = {
    ...stockCount,
    status: 'completed',
    updatedAt: new Date().toISOString(),
  };

  // Save all changes atomically
  await updateInventory(inventory);
  // endknowledge
}

// --- Inventory Snapshot Operations ---
export async function createInventorySnapshot(snapshotData: { performedBy?: string; notes?: string }): Promise<InventorySnapshot> {
  try {
    const inventory = await getInventory();
    const now = new Date().toISOString();
    const snapshotId = `snapshot_${now.replace(/[:.]/g, '-')}_${uuidv4()}`;

    const snapshotItems: SnapshotItem[] = [];
    let totalSnapshotValue = 0;

    for (const item of inventory.items) {
      const quantityAtSnapshot = typeof item.quantity === 'number' ? item.quantity : 0;
      const costPerUnitAtSnapshot = typeof item.costPerUnit === 'number' ? item.costPerUnit : 0;
      const totalValueAtSnapshot = quantityAtSnapshot * costPerUnitAtSnapshot;

      snapshotItems.push({
        stockItemId: item.id,
        itemName: item.name,
        unit: item.unit,
        quantityAtSnapshot,
        costPerUnitAtSnapshot,
        totalValueAtSnapshot,
      });
      totalSnapshotValue += totalValueAtSnapshot;
    }

    const newSnapshot: InventorySnapshot = {
      _id: snapshotId,
      date: now,
      items: snapshotItems,
      totalValue: totalSnapshotValue,
      performedBy: snapshotData.performedBy,
      notes: snapshotData.notes,
      createdAt: now,
      updatedAt: now,
    };

    if (!(window as any).electronAPI?.database?.put) {
        console.error('[createInventorySnapshot] electronAPI.database.put is not available.');
        throw new Error('electronAPI.database.put is not available.');
    }
    // Use the existing electronAPI.database.put with the specific snapshot DB identifier
    await (window as any).electronAPI.database.put(SNAPSHOT_DB_IDENTIFIER, newSnapshot);

    console.log(`[createInventorySnapshot] Snapshot ${snapshotId} IPC call made to ${SNAPSHOT_DB_IDENTIFIER} with ${snapshotItems.length} items and total value ${totalSnapshotValue}.`);
    return newSnapshot;
  } catch (error) {
    console.error('[createInventorySnapshot] Error creating inventory snapshot:', error);
    throw error;
  }
}

export async function getInventorySnapshots(filters?: { startDate?: string; endDate?: string; limit?: number }): Promise<InventorySnapshot[]> {
  try {
    const selector: any = {};
    if (filters?.startDate && filters?.endDate) {
      selector.date = { $gte: filters.startDate, $lte: filters.endDate };
    } else if (filters?.startDate) {
      selector.date = { $gte: filters.startDate };
    } else if (filters?.endDate) {
      selector.date = { $lte: filters.endDate };
    }
    
    const findOptions: any = { selector };
    if (filters?.limit) {
      findOptions.limit = filters.limit;
    }
    findOptions.sort = [{ date: 'desc' }];

    if (!(window as any).electronAPI?.database?.find) {
        console.error('[getInventorySnapshots] electronAPI.database.find is not available.');
        throw new Error('electronAPI.database.find is not available.');
    }
    // Use the existing electronAPI.database.find with the specific snapshot DB identifier
    const result = await (window as any).electronAPI.database.find(SNAPSHOT_DB_IDENTIFIER, findOptions);
    // The IPC call for find in preload.ts returns the whole response, docs are in result.docs
    return result.docs || []; 
  } catch (error) {
    console.error('[getInventorySnapshots] Error fetching inventory snapshots:', error);
    throw error;
  }
}

// --- END Inventory Snapshot Operations ---

// --- Stock Count Application ---
// ... existing code ... 

// ConsumptionLog CRUD
export async function addConsumptionLog(log: Omit<ConsumptionLog, 'id' | 'createdAt' | 'updatedAt'>): Promise<ConsumptionLog> {
  try {
    const inventory = await getInventory();
    const newLog: ConsumptionLog = {
      id: `consumption_${uuidv4()}`,
      ...log,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    inventory.consumptionLogs = inventory.consumptionLogs || [];
    inventory.consumptionLogs.push(newLog);

    // Update stock item quantity (deduct consumed amount)
    inventory.items = inventory.items || [];
    const idx = inventory.items.findIndex(i => i.id === log.stockItemId);
    if (idx !== -1) {
      const item = inventory.items[idx];
      const currentQty = typeof item.quantity === 'number' ? item.quantity : 0;
      const newQty = Math.max(0, currentQty - log.quantity); // Prevent negative stock
      
      inventory.items[idx] = {
        ...item,
        quantity: newQty,
        updatedAt: new Date().toISOString(),
      };
    }

    await updateInventory(inventory);
    return newLog;
  } catch (error) {
    console.error('Error adding consumption log:', error);
    throw error;
  }
}

export async function getConsumptionLogsForOrder(orderId: string): Promise<ConsumptionLog[]> {
  try {
    const inventory = await getInventory();
    return (inventory.consumptionLogs || []).filter(log => log.orderId === orderId);
  } catch (error) {
    console.error('Error getting consumption logs for order:', error);
    throw error;
  }
}

export async function getConsumptionLogsInDateRange(startDate: Date, endDate: Date): Promise<ConsumptionLog[]> {
  try {
    const inventory = await getInventory();
    const start = startDate.toISOString();
    const end = endDate.toISOString();
    return (inventory.consumptionLogs || []).filter(log => 
      log.date >= start && log.date <= end
    );
  } catch (error) {
    console.error('Error getting consumption logs in date range:', error);
    throw error;
  }
}

// Recipe explosion and consumption calculation
interface IngredientConsumption {
  stockItemId: string;
  quantity: number;
}

export async function explodeMenuItemToStockConsumption(
  menuItemId: string, 
  orderQuantity: number
): Promise<IngredientConsumption[]> {
  try {
    // console.log(`[explodeMenuItemToStockConsumption] Processing menu item ${menuItemId} with quantity ${orderQuantity}`);
    
    // Get necessary data
    const inventory = await getInventory();
    const recipes = await getAllMenuItemRecipes();
    // 🚀 FIX: Use getAllSubRecipes to get fresh sub-recipe data
    const subRecipes = await getAllSubRecipes();
    
    // console.log(`[explodeMenuItemToStockConsumption] Found ${recipes.length} recipes and ${subRecipes.length} sub-recipes`);

    // Find the recipe for this menu item
    const recipe = recipes.find(r => r.menuItemId === menuItemId);
    if (!recipe) {
      // console.warn(`[explodeMenuItemToStockConsumption] No recipe found for menu item ${menuItemId}`);
      return [];
    }

    // console.log(`[explodeMenuItemToStockConsumption] Found recipe for ${recipe.menuItemName} with ${recipe.ingredients.length} ingredients`);

    // Track stock consumption using a Map to aggregate quantities
    const ingredientConsumptions = new Map<string, IngredientConsumption>();

    // Process each ingredient in the recipe
    for (const ingredient of recipe.ingredients) {
      if ('stockItemId' in ingredient) {
        // Direct stock ingredient
        const totalQty = ingredient.quantity * orderQuantity;
          
        const existing = ingredientConsumptions.get(ingredient.stockItemId);
        if (existing) {
          existing.quantity += totalQty;
        } else {
          ingredientConsumptions.set(ingredient.stockItemId, {
            stockItemId: ingredient.stockItemId,
            quantity: totalQty,
          });
        }
      } else if ('subRecipeId' in ingredient) {
        // Sub-recipe - need to explode further
        const subRecipe = subRecipes.find(sr => sr._id === ingredient.subRecipeId);
        if (subRecipe) {
          // console.log(`[explodeMenuItemToStockConsumption] Processing sub-recipe: ${subRecipe.name}`);
          // Calculate how many sub-recipe units we need
          const subRecipeUnitsNeeded = ingredient.quantity * orderQuantity;
          // Calculate how many batches we need (based on yield)
          const batchesNeeded = subRecipeUnitsNeeded / subRecipe.yield.quantity;
          
          // Process each ingredient in the sub-recipe
          for (const subIngredient of subRecipe.ingredients) {
            const totalQty = subIngredient.quantity * batchesNeeded;
              
            const existing = ingredientConsumptions.get(subIngredient.stockItemId);
            if (existing) {
              existing.quantity += totalQty;
            } else {
              ingredientConsumptions.set(subIngredient.stockItemId, {
                stockItemId: subIngredient.stockItemId,
                quantity: totalQty,
              });
            }
          }
        } else {
          console.warn(`[explodeMenuItemToStockConsumption] Sub-recipe not found for ID: ${ingredient.subRecipeId}`);
        }
      }
    }

    const result = Array.from(ingredientConsumptions.values());
    // console.log(`[explodeMenuItemToStockConsumption] Final stock consumption:`, result);
    return result;
  } catch (error) {
    console.error('Error exploding menu item to stock consumption:', error);
    throw error;
  }
}

import { getMenu } from './menu-ops'; // Ensure getMenu is imported

export async function createConsumptionLogsForOrder(
  orderId: string,
  orderItems: Array<{ 
    menuItemId: string; 
    name: string; 
    quantity: number; 
    size?: string; 
    compositeType?: string; 
    quarters?: Array<{ menuItemId: string; name: string; price: number; size?: string }> 
  }>,
  orderDate: string
): Promise<ConsumptionLog[]> {
  try {
    const consumptionLogs: ConsumptionLog[] = [];

    for (const orderItem of orderItems) {
      // 🍕 Handle Custom Pizza Stock Consumption
      if (orderItem.compositeType === 'pizza_quarters' && orderItem.quarters && orderItem.quarters.length > 0) {
        console.log(`[createConsumptionLogsForOrder] Processing custom pizza: ${orderItem.name} with ${orderItem.quarters.length} quarters`);
        
        // Process each quarter separately
        for (const quarter of orderItem.quarters) {
          // Each quarter represents 1/4 of a full pizza
          const quarterQuantity = orderItem.quantity * 0.25;
          
          // Get stock consumption for this quarter's menu item
          const explodedIngredients = await explodeMenuItemToStockConsumption(
            quarter.menuItemId,
            quarterQuantity
          );

          // Create consumption logs for each ingredient
          for (const ingredient of explodedIngredients) {
            const log = await addConsumptionLog({
              orderId,
              stockItemId: ingredient.stockItemId,
              quantity: ingredient.quantity,
              costPerUnit: 0, // Will be calculated by the system
              totalCost: 0, // Will be calculated by the system
              menuItemId: quarter.menuItemId, // Use the actual pizza menu item ID
              menuItemName: `${orderItem.name} - Quarter: ${quarter.name}`, // Descriptive name
              date: orderDate,
            });
            consumptionLogs.push(log);
          }
        }
      } else {
        // Handle regular menu items with recipes (supplements are no longer menu items)
        const explodedIngredients = await explodeMenuItemToStockConsumption(
          orderItem.menuItemId,
          orderItem.quantity
        );

        for (const ingredient of explodedIngredients) {
          const log = await addConsumptionLog({
            orderId,
            stockItemId: ingredient.stockItemId,
            quantity: ingredient.quantity,
            costPerUnit: 0, // Will be calculated by the system
            totalCost: 0, // Will be calculated by the system
            menuItemId: orderItem.menuItemId,
            menuItemName: orderItem.name,
            date: orderDate,
          });
          consumptionLogs.push(log);
        }
      }
    }
    
    return consumptionLogs;
  } catch (error) {
    console.error('Error creating consumption logs for order:', error);
    throw error;
  }
}

export async function calculateOrderCOGS(orderId: string): Promise<number> {
  try {
    const consumptionLogs = await getConsumptionLogsForOrder(orderId);
    return consumptionLogs.reduce((total, log) => total + log.totalCost, 0);
  } catch (error) {
    console.error('Error calculating order COGS:', error);
    throw error;
  }
}

// 🚀 ENHANCED: Calculate menu item cost using new recipe cost logic (fixed vs calculated)
export async function calculateMenuItemCost(
  menuItemId: string,
  quantity: number,
  addons?: Array<{ id: string; type?: string; name: string }>,
  quarters?: Array<{ menuItemId: string; name: string; price: number; size?: string }>,
  size?: string
): Promise<number> {
  try {
    let totalCost = 0;
    
    // 🍕 Handle Custom Pizza Cost Calculation
    if (menuItemId === 'custom_pizza' && quarters && quarters.length > 0) {
      console.log(`[calculateMenuItemCost] Calculating custom pizza cost with ${quarters.length} quarters`);
      
      // Calculate cost for each quarter separately
      for (const quarter of quarters) {
        // Each quarter represents 1/4 of a full pizza
        const quarterQuantity = quantity * 0.25;
        
        // Get ingredient cost for this quarter's menu item
        const ingredients = await explodeMenuItemToStockConsumption(quarter.menuItemId, quarterQuantity);
        const inventory = await getInventory();
        
        for (const ingredient of ingredients) {
          const stockItem = inventory.items.find(i => i.id === ingredient.stockItemId);
          if (stockItem && stockItem.costPerUnit) {
            totalCost += stockItem.costPerUnit * ingredient.quantity;
          }
        }
      }
    } else {
      // 1. Try to use recipe-based cost calculation (new logic)
      const recipes = await getAllMenuItemRecipes();
      const recipe = recipes.find(r => r.menuItemId === menuItemId && (size ? r.size === size : true));
      
      if (recipe) {
        // Use the effective cost from the recipe (fixed or calculated)
        const effectiveCostPerUnit = getEffectiveCost(recipe);
        totalCost = effectiveCostPerUnit * quantity;
        console.log(`[calculateMenuItemCost] Using effective cost from recipe: ${effectiveCostPerUnit} for ${menuItemId}`);
      } else {
        // Fallback to old ingredient-based calculation
        console.log(`[calculateMenuItemCost] No recipe found, falling back to ingredient calculation for ${menuItemId}`);
        const ingredients = await explodeMenuItemToStockConsumption(menuItemId, quantity);
        const inventory = await getInventory();
        
        for (const ingredient of ingredients) {
          const stockItem = inventory.items.find(i => i.id === ingredient.stockItemId);
          if (stockItem && stockItem.costPerUnit) {
            totalCost += stockItem.costPerUnit * ingredient.quantity;
          }
        }
      }
    }
    
    // 2. Calculate supplement costs for addons
    if (addons && addons.length > 0) {
      try {
        const { findSupplementById } = await import('../operations/supplement-ops');
        
        for (const addon of addons) {
          if (addon.type === 'supplement' && addon.id) {
            const result = await findSupplementById(addon.id);
            if (result && result.supplement.stockConsumption) {
              const supplement = result.supplement;
              const inventory = await getInventory();
              const stockItem = inventory.items.find(i => i.id === supplement.stockConsumption!.stockItemId);
              if (stockItem && stockItem.costPerUnit) {
                // Calculate supplement quantity for a standard size
                const sizeQuantity = supplement.stockConsumption!.quantities['Normale'] || 
                                   supplement.stockConsumption!.quantities['default'] ||
                                   Object.values(supplement.stockConsumption!.quantities)[0] || 0;
                
                totalCost += stockItem.costPerUnit * sizeQuantity * quantity;
              }
            }
          }
        }
      } catch (supplementError) {
        console.error('Error calculating supplement costs:', supplementError);
        // Don't fail the entire operation if supplement cost calculation fails
      }
    }
    
    return totalCost;
  } catch (error) {
    console.error('Error calculating menu item cost:', error);
    return 0; // Return 0 cost if calculation fails
  }
}

// 🚀 UPDATED: Process waste for menu items - uses COST values instead of selling prices, no caisse transaction
export async function processMenuItemWaste(
  orderId: string,
  wastedMenuItems: Array<{
    name: string;
    quantity: number;
    originalQuantity: number;
    price: number;
    addons?: any[];
    menuItemId?: string; // Add this to pass menu item ID for cost calculation
  }>,
  wasteReason: string,
  totalWasteValue: number // This is selling price - we'll recalculate as cost
): Promise<void> {
  try {
    console.log(`[processMenuItemWaste] Processing waste for order ${orderId}`);
    
    // Create a single waste log entry for each unique menu item
    for (const menuItem of wastedMenuItems) {
      // Create display name with addons
      let displayName = menuItem.name;
      if (menuItem.addons && menuItem.addons.length > 0) {
        const addonNames = menuItem.addons.map(addon => addon.name).join(', ');
        displayName += ` + ${addonNames}`;
      }
      
      // 🚀 FIX: Calculate COST value instead of selling price
      let itemCostValue = 0;
      if (menuItem.menuItemId) {
        itemCostValue = await calculateMenuItemCost(
          menuItem.menuItemId, 
          menuItem.quantity, 
          menuItem.addons
        );
      }
      
      // Store menu item data in the notes field as JSON
      const menuItemWasteData = {
        type: 'menu-item-waste',
        menuItemName: displayName,
        originalQuantity: menuItem.originalQuantity,
        costValue: itemCostValue, // 🚀 Use cost value instead of selling price
        orderId: orderId
      };
      
      await addWasteLog({
        stockItemId: 'menu-item-waste', // Special identifier for menu item waste
        quantity: menuItem.quantity,
        reason: 'cooking_error',
        notes: `Menu Item Waste: ${displayName} (Order #${orderId.slice(-6)}) - ${wasteReason} | DATA: ${JSON.stringify(menuItemWasteData)}`,
        date: new Date().toISOString(),
        performedBy: 'system'
      });
    }

    // 🚀 REMOVED: No more caisse waste transaction - caisse only tracks actual money received
    
    console.log(`[processMenuItemWaste] Completed processing waste for ${wastedMenuItems.length} menu items`);
  } catch (error) {
    console.error('Error processing menu item waste:', error);
    throw error;
  }
}

// 🚀 NEW: Process stock consumption for wasted menu items (behind the scenes)
export async function processWastedMenuItemStockConsumption(
  wasteItems: Array<{ itemIndex: number; quantity: number }>,
  orderItems: Array<{ menuItemId: string; name: string; quantity: number; size?: string; addons?: any[] }>
): Promise<void> {
  try {
    console.log(`[processWastedMenuItemStockConsumption] Processing stock consumption for ${wasteItems.length} wasted items`);
    
    for (const wasteItem of wasteItems) {
      const orderItem = orderItems[wasteItem.itemIndex];
      if (!orderItem) continue;
      
      // 1. Process menu item stock consumption
      const ingredients = await explodeMenuItemToStockConsumption(
        orderItem.menuItemId, 
        wasteItem.quantity
      );
      
      // Create individual stock waste logs for menu item (hidden from UI)
      for (const ingredient of ingredients) {
        await addWasteLog({
          stockItemId: ingredient.stockItemId,
          quantity: ingredient.quantity,
          reason: 'cooking_error',
          notes: `Stock consumption for wasted menu item: ${orderItem.name} (Hidden from UI) | HIDDEN_STOCK_WASTE: true`,
          date: new Date().toISOString(),
          performedBy: 'system'
        });
      }
      
      // 🚀 2. Process supplement stock consumption for addons
      if (orderItem.addons && orderItem.addons.length > 0) {
        try {
          const { processSupplementConsumption, findSupplementById } = await import('../operations/supplement-ops');
          
          for (const addon of orderItem.addons) {
            // Check if this addon is a supplement with stock consumption
            if (addon.type === 'supplement' && addon.id) {
              const result = await findSupplementById(addon.id);
              
              if (result && result.supplement.stockConsumption) {
                const supplement = result.supplement;
                console.log(`[processWastedMenuItemStockConsumption] Processing supplement stock consumption: ${supplement.name}`);
                
                const size = orderItem.size || 'default';
                const sizeQuantity = supplement.stockConsumption.quantities[size];
                
                if (sizeQuantity && sizeQuantity > 0) {
                  const totalSupplementQuantity = sizeQuantity * wasteItem.quantity;
                  
                  // Create supplement stock waste log (hidden from UI)
                  await addWasteLog({
                    stockItemId: supplement.stockConsumption.stockItemId,
                    quantity: totalSupplementQuantity,
                    reason: 'cooking_error',
                    notes: `Stock consumption for wasted supplement: ${supplement.name} from ${orderItem.name} (Hidden from UI) | HIDDEN_STOCK_WASTE: true`,
                    date: new Date().toISOString(),
                    performedBy: 'system'
                  });
                  
                  console.log(`[processWastedMenuItemStockConsumption] Processed supplement ${supplement.name}: ${totalSupplementQuantity} units`);
                }
              }
            }
          }
        } catch (supplementError) {
          console.error('Error processing supplement consumption for waste:', supplementError);
          // Don't fail the entire operation if supplement processing fails
        }
      }
    }
    
    console.log(`[processWastedMenuItemStockConsumption] Completed processing stock consumption for wasted menu items`);
  } catch (error) {
    console.error('Error processing wasted menu item stock consumption:', error);
    throw error;
  }
} 

// PurchaseLog CRUD operations
export async function addPurchaseLog(log: Omit<PurchaseLog, 'id' | 'createdAt' | 'updatedAt'>): Promise<PurchaseLog> {
  const newLog: PurchaseLog = {
    id: `purchase_${uuidv4()}`,
    ...log,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  await safeAddToArrayDocument<InventoryDocument, PurchaseLog>(
    'inventory',
    'purchaseLogs',
    newLog,
    'addPurchaseLog'
  );
  
  return newLog;
}

export async function updatePurchaseLog(logId: string, updates: Partial<PurchaseLog>): Promise<PurchaseLog> {
  const updatedDoc = await safeUpdateArrayItem<InventoryDocument, PurchaseLog>(
    'inventory',
    'purchaseLogs',
    logId,
    updates,
    `updatePurchaseLog(${logId})`
  );
  
  const updatedLog = updatedDoc.purchaseLogs?.find(l => l.id === logId);
  if (!updatedLog) {
    throw new Error(`Purchase log with ID ${logId} not found after update`);
  }
  
  return updatedLog;
}

export async function deletePurchaseLog(logId: string): Promise<boolean> {
  await safeRemoveFromArrayDocument<InventoryDocument, PurchaseLog>(
    'inventory',
    'purchaseLogs',
    logId,
    `deletePurchaseLog(${logId})`
  );
  
  return true;
}