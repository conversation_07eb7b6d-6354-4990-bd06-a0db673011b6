// CRUD operations for order completion tracking in PouchDB

import { databaseV4 } from '../core/db-instance';
import { 
  OrderCompletionStatus, 
  OrderCompletionUpdate,
  createDefaultCompletionStatus,
  BarcodeData
} from '../schemas/order-completion-schema';

/**
 * Get or create order completion status
 */
export async function getOrCreateCompletionStatus(orderId: string): Promise<OrderCompletionStatus> {
  try {
    const completionDoc = await databaseV4.getDoc<OrderCompletionStatus>(`completion-${orderId}`);
    return completionDoc;
  } catch (error: any) {
    if (error.status === 404) {
      // Create new completion status
      const now = new Date().toISOString();
      const newStatus: OrderCompletionStatus = {
        _id: `completion-${orderId}`,
        ...createDefaultCompletionStatus(orderId),
        createdAt: now,
        updatedAt: now
      };
      
      const result = await databaseV4.putDoc(newStatus);
      return {
        ...newStatus,
        _rev: result.rev
      };
    }
    console.error('❌ Error getting completion status:', error);
    throw error;
  }
}

/**
 * Parse barcode data - Updated to handle simplified format
 */
export function parseBarcodeData(barcode: string): BarcodeData {
  const parts = barcode.split('-');
  if (parts.length < 3) {
    throw new Error('Invalid barcode format');
  }
  
  // 🎯 Handle simplified barcode format: "047-KITCHEN-1"
  if (parts.length === 3 && parts[1] === 'KITCHEN') {
    const dailySequence = parts[0];
    const itemIndex = parts[2];
    
    // 🔧 RELIABILITY FIX: Reconstruct full order ID from daily sequence
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
    const fullOrderId = `order:${today}-${dailySequence}`;
    
    return {
      orderId: fullOrderId,
      stationId: 'KITCHEN', // Default station for simplified format
      itemIndex: itemIndex,
      timestamp: new Date().toISOString()
    };
  }
  
  // 🔄 Fallback: Handle legacy complex format for backward compatibility
  return {
    orderId: parts[0],
    stationId: parts[1],
    itemIndex: parts[2],
    timestamp: new Date().toISOString()
  };
}

/**
 * Generate barcode for order item
 */
export function generateItemBarcode(orderId: string, stationId: string, itemIndex: number): string {
  return `${orderId}-${stationId}-${itemIndex}`;
}

/**
 * Mark item as complete via barcode scan
 */
export async function markItemComplete(
  orderId: string, 
  stationId: string, 
  itemIndex: string,
  scannedBy?: string
): Promise<OrderCompletionStatus> {
  console.log(`🏷️ Marking item complete: ${orderId}-${stationId}-${itemIndex}`);
  
  const completionDoc = await getOrCreateCompletionStatus(orderId);
  
  // Initialize station if not exists
  if (!completionDoc.stationItems[stationId]) {
    completionDoc.stationItems[stationId] = {};
  }
  
  // Mark item as complete
  const barcode = generateItemBarcode(orderId, stationId, parseInt(itemIndex));
  completionDoc.stationItems[stationId][itemIndex] = {
    completed: true,
    completedAt: new Date().toISOString(),
    scannedBy: scannedBy || 'unknown',
    barcode
  };
  
  // Check if all items are complete
  completionDoc.allItemsComplete = checkAllItemsComplete(completionDoc);
  completionDoc.updatedAt = new Date().toISOString();
  
  const result = await databaseV4.putDoc(completionDoc);
  
  return {
    ...completionDoc,
    _rev: result.rev
  };
}

/**
 * Check if all items in order are complete
 */
export function checkAllItemsComplete(completionDoc: OrderCompletionStatus): boolean {
  const stationItems = completionDoc.stationItems;
  
  // If no stations, order is not complete
  if (Object.keys(stationItems).length === 0) {
    return false;
  }
  
  // Check all stations have at least one completed item
  for (const stationId in stationItems) {
    const items = stationItems[stationId];
    const hasCompletedItems = Object.values(items).some(item => item.completed);
    
    if (!hasCompletedItems) {
      return false;
    }
  }
  
  return true;
}

/**
 * Get order completion status
 */
export async function getOrderCompletionStatus(orderId: string): Promise<OrderCompletionStatus | null> {
  try {
    return await databaseV4.getDoc<OrderCompletionStatus>(`completion-${orderId}`);
  } catch (error: any) {
    if (error.status === 404) {
      return null;
    }
    console.error('❌ Error getting order completion status:', error);
    throw error;
  }
}

/**
 * Check if order is complete
 */
export async function isOrderComplete(orderId: string): Promise<boolean> {
  const completionStatus = await getOrderCompletionStatus(orderId);
  return completionStatus?.allItemsComplete || false;
}

/**
 * Mark order as expo notified
 */
export async function markExpoNotified(orderId: string): Promise<OrderCompletionStatus> {
  console.log(`📢 Marking expo notified for order: ${orderId}`);
  
  const completionDoc = await getOrCreateCompletionStatus(orderId);
  completionDoc.expoNotified = true;
  completionDoc.updatedAt = new Date().toISOString();
  
  const result = await databaseV4.putDoc(completionDoc);
  
  return {
    ...completionDoc,
    _rev: result.rev
  };
}

/**
 * Get pending orders (not all items complete)
 */
export async function getPendingOrders(): Promise<OrderCompletionStatus[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'order-completion',
        allItemsComplete: false
      },
      sort: [{ createdAt: 'desc' }]
    });
    
    return result.docs as OrderCompletionStatus[];
  } catch (error) {
    console.error('❌ Error getting pending orders:', error);
    return [];
  }
}

/**
 * Get completed orders ready for expo
 */
export async function getOrdersReadyForExpo(): Promise<OrderCompletionStatus[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'order-completion',
        allItemsComplete: true,
        expoNotified: false
      },
      sort: [{ updatedAt: 'desc' }]
    });
    
    return result.docs as OrderCompletionStatus[];
  } catch (error) {
    console.error('❌ Error getting orders ready for expo:', error);
    return [];
  }
}

/**
 * Initialize completion tracking for new order
 */
export async function initializeOrderCompletion(
  orderId: string,
  stationItems: { [stationId: string]: number } // number of items per station
): Promise<OrderCompletionStatus> {
  console.log(`🆕 Initializing completion tracking for order: ${orderId}`);
  
  const completionDoc = await getOrCreateCompletionStatus(orderId);
  
  // Initialize station items structure
  for (const stationId in stationItems) {
    const itemCount = stationItems[stationId];
    completionDoc.stationItems[stationId] = {};
    
    // Create entries for each item
    for (let i = 0; i < itemCount; i++) {
      const barcode = generateItemBarcode(orderId, stationId, i);
      completionDoc.stationItems[stationId][i.toString()] = {
        completed: false,
        barcode
      };
    }
  }
  
  completionDoc.updatedAt = new Date().toISOString();
  
  const result = await databaseV4.putDoc(completionDoc);
  
  return {
    ...completionDoc,
    _rev: result.rev
  };
}