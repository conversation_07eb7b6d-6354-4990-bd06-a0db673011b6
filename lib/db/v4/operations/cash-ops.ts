/**
 * Cash Operations for V4 Database
 * 
 * REFACTORED: Integrated with new session management
 * - All transactions are automatically linked to current session
 * - Sessions always exist, no need to start them manually
 * - Clean separation between drawer operations and session tracking
 */

"use client";

import { databaseV4 } from '../core/db-instance';
import { CashTransactionDocument } from '../schemas/cash-session-schema';
import { retryWithConflictResolution } from '../core/conflict-resolution';

/**
 * Create a cash transaction and integrate with session system
 */
export async function createCashTransaction(data: {
  type: 'sales' | 'manual_in' | 'manual_out' | 'expense';
  amount: number;
  description: string;
  time: string;
  performedBy: string;
  relatedDocId?: string;
  metadata?: Record<string, any>;
}): Promise<CashTransactionDocument> {
  try {
    console.log('[createCashTransaction] Creating transaction:', {
      type: data.type,
      amount: data.amount,
      description: data.description.substring(0, 50) + '...'
    });

    const transactionId = `cash_transaction:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    
    const transaction: CashTransactionDocument = {
      _id: transactionId,
      type: 'cash_transaction',
      transactionType: data.type,
      amount: data.amount,
      description: data.description,
      time: data.time,
      performedBy: data.performedBy,
      relatedDocId: data.relatedDocId,
      metadata: data.metadata || {}
    };

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(transaction);
    });

    console.log('[createCashTransaction] Transaction created:', transactionId);

    // Link transaction to current session (session always exists now)
    try {
      const { addTransactionToSession } = await import('./cash-session-ops');
      await addTransactionToSession(transactionId);
      console.log('[createCashTransaction] Transaction linked to session:', transactionId);
    } catch (sessionError) {
      // Don't fail the transaction if session linking fails
      console.warn('[createCashTransaction] Session integration failed (non-critical):', sessionError);
    }

    return transaction;
  } catch (error) {
    console.error('[createCashTransaction] Error:', error);
    throw error;
  }
}

/**
 * Get all cash transactions
 */
export async function getAllCashTransactions(): Promise<CashTransactionDocument[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'cash_transaction'
      }
    });

    const transactions = result.docs as CashTransactionDocument[];
    
    // Sort by time (most recent first)
    return transactions.sort((a, b) => {
      const timeA = new Date(a.time || a.createdAt || 0).getTime();
      const timeB = new Date(b.time || b.createdAt || 0).getTime();
      return timeB - timeA;
    });
  } catch (error) {
    console.error('[getAllCashTransactions] Error:', error);
    throw error;
  }
}

/**
 * Get cash transactions by type
 */
export async function getCashTransactionsByType(
  type: 'sales' | 'manual_in' | 'manual_out' | 'expense'
): Promise<CashTransactionDocument[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'cash_transaction',
        transactionType: type
      }
    });

    const transactions = result.docs as CashTransactionDocument[];
    
    return transactions.sort((a, b) => {
      const timeA = new Date(a.time || a.createdAt || 0).getTime();
      const timeB = new Date(b.time || b.createdAt || 0).getTime();
      return timeB - timeA;
    });
  } catch (error) {
    console.error('[getCashTransactionsByType] Error:', error);
    throw error;
  }
}

/**
 * Get cash transactions within a date range
 */
export async function getCashTransactionsInRange(
  startDate: string,
  endDate: string
): Promise<CashTransactionDocument[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'cash_transaction',
        time: {
          $gte: startDate,
          $lte: endDate
        }
      }
    });

    const transactions = result.docs as CashTransactionDocument[];
    
    return transactions.sort((a, b) => {
      const timeA = new Date(a.time || a.createdAt || 0).getTime();
      const timeB = new Date(b.time || b.createdAt || 0).getTime();
      return timeB - timeA;
    });
  } catch (error) {
    console.error('[getCashTransactionsInRange] Error:', error);
    throw error;
  }
}

/**
 * Get recent cash transactions (for debugging and display)
 */
export async function getRecentCashTransactions(limit: number = 10): Promise<CashTransactionDocument[]> {
  try {
    const allTransactions = await getAllCashTransactions();
    return allTransactions.slice(0, limit);
  } catch (error) {
    console.error('[getRecentCashTransactions] Error:', error);
    throw error;
  }
}

/**
 * Delete a cash transaction (admin only)
 */
export async function deleteCashTransaction(transactionId: string): Promise<void> {
  try {
    console.log('[deleteCashTransaction] Deleting transaction:', transactionId);
    
    const transaction = await databaseV4.getDoc(transactionId) as CashTransactionDocument;
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    await retryWithConflictResolution(async () => {
      await databaseV4.deleteDoc(transaction._id, transaction._rev!);
    });

    console.log('[deleteCashTransaction] Transaction deleted:', transactionId);
  } catch (error) {
    console.error('[deleteCashTransaction] Error:', error);
    throw error;
  }
}

/**
 * Update a cash transaction (admin only)
 */
export async function updateCashTransaction(
  transactionId: string,
  updates: Partial<Pick<CashTransactionDocument, 'amount' | 'description' | 'metadata'>>
): Promise<CashTransactionDocument> {
  try {
    console.log('[updateCashTransaction] Updating transaction:', transactionId, updates);
    
    const transaction = await databaseV4.getDoc(transactionId) as CashTransactionDocument;
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    const updatedTransaction: CashTransactionDocument = {
      ...transaction,
      ...updates,
      // Preserve core fields
      _id: transaction._id,
      type: transaction.type,
      transactionType: transaction.transactionType,
      time: transaction.time,
      performedBy: transaction.performedBy
    };

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(updatedTransaction);
    });

    console.log('[updateCashTransaction] Transaction updated:', transactionId);
    return updatedTransaction;
  } catch (error) {
    console.error('[updateCashTransaction] Error:', error);
    throw error;
  }
}

/**
 * Get cash transaction statistics
 */
export async function getCashTransactionStats(
  startDate?: string,
  endDate?: string
): Promise<{
  totalSales: number;
  totalManualIn: number;
  totalManualOut: number;
  totalExpenses: number;
  netCashFlow: number;
  transactionCount: number;
}> {
  try {
    let transactions: CashTransactionDocument[];
    
    if (startDate && endDate) {
      transactions = await getCashTransactionsInRange(startDate, endDate);
    } else {
      transactions = await getAllCashTransactions();
    }

    const stats = transactions.reduce((acc, tx) => {
      switch (tx.transactionType) {
        case 'sales':
          acc.totalSales += tx.amount;
          break;
        case 'manual_in':
          acc.totalManualIn += tx.amount;
          break;
        case 'manual_out':
          acc.totalManualOut += Math.abs(tx.amount); // Store as positive for display
          break;
        case 'expense':
          acc.totalExpenses += Math.abs(tx.amount); // Store as positive for display
          break;
      }
      acc.transactionCount++;
      return acc;
    }, {
      totalSales: 0,
      totalManualIn: 0,
      totalManualOut: 0,
      totalExpenses: 0,
      transactionCount: 0
    });

    const netCashFlow = stats.totalSales + stats.totalManualIn - stats.totalManualOut - stats.totalExpenses;

    return {
      ...stats,
      netCashFlow
    };
  } catch (error) {
    console.error('[getCashTransactionStats] Error:', error);
    throw error;
  }
} 