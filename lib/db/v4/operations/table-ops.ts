"use client";

import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import {
  Table,
  TableDocument,
  DEFAULT_TABLE_DOCUMENT,
  createDefaultTableDocument
} from '../schemas/table-schema';

/**
 * Table Operations for V4 Database
 *
 * This file contains all the database operations related to table management.
 */

/**
 * Create table indexes
 */
export async function createTableIndexes(): Promise<void> {
  console.log('[createTableIndexes] Starting table index creation');
  
  // Track success/failure of each index
  const results: {field: string, success: boolean, error?: any}[] = [];
  
  // Helper function for creating an index with retries
  async function createIndexWithRetry(fields: string[], description: string) {
    const maxRetries = 3;
    let lastError: any = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[createTableIndexes] Creating index for ${description} (attempt ${attempt}/${maxRetries})`);
        await databaseV4.createIndex({ index: { fields } });
        console.log(`[createTableIndexes] ✅ Successfully created index for ${description}`);
        return { field: description, success: true };
      } catch (error: any) {
        lastError = error;
        console.warn(`[createTableIndexes] ⚠️ Failed to create index for ${description} (attempt ${attempt}/${maxRetries}): ${error.message}`);
        
        // 409 means index already exists, which is fine
        if (error.status === 409) {
          console.log(`[createTableIndexes] Index for ${description} already exists (409), continuing`);
          return { field: description, success: true };
        }
        
        // For other errors, retry after a delay
        if (attempt < maxRetries) {
          const delay = 500 * attempt;
          console.log(`[createTableIndexes] Retrying after ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // All retries failed
    return { field: description, success: false, error: lastError };
  }
  
  // Create each index with proper error handling and retries
  try {
    // Define the indexes
    const indexes = [
      { fields: ['type'], description: 'type' },
      { fields: ['type', 'tables.id'], description: 'type+tables.id' },
      { fields: ['type', 'tables.name'], description: 'type+tables.name' },
      { fields: ['type', 'tables.status'], description: 'type+tables.status' }
    ];
    
    // Create all indexes and gather results
    for (const index of indexes) {
      results.push(await createIndexWithRetry(index.fields, index.description));
    }
    
    // Check results and report
    const failedIndexes = results.filter(r => !r.success);
    if (failedIndexes.length > 0) {
      console.error(`[createTableIndexes] ❌ ${failedIndexes.length}/${results.length} table indexes failed to create:`);
      failedIndexes.forEach(r => console.error(`  - ${r.field}: ${r.error?.message || 'Unknown error'}`));
    } else {
      console.log(`[createTableIndexes] ✅ All ${results.length} table indexes created successfully`);
    }
  } catch (error) {
    console.error('[createTableIndexes] ❌ Unexpected error during table index creation:', error);
    throw error;
  }
}

/**
 * Get the table document (all tables)
 */
export async function getAllTables(): Promise<Table[]> {
  console.log('[getAllTables] Attempting to fetch \'tables\' document.');
  try {
    // knowledge: always only one table document with _id 'tables'
    const doc = await databaseV4.getDoc<TableDocument>('tables');
    console.log('[getAllTables] Received doc:', typeof doc, doc);
    if (doc === undefined) {
      console.error('[getAllTables] CRITICAL: doc is undefined AFTER await databaseV4.getDoc. This should not happen!');
    }
    return doc.tables;
  } catch (error: any) {
    console.error('[getAllTables] Error caught in getAllTables:', error);
    if (error.status === 404) {
      // If not found, create default with fresh timestamps
      const defaultDoc = createDefaultTableDocument();
      await databaseV4.putDoc(defaultDoc);
      return [];
    }
    throw error;
  }
}

/**
 * Get a single table by id
 */
export async function getTable(tableId: string): Promise<Table | undefined> {
  const tables = await getAllTables();
  return tables.find(t => t.id === tableId);
}

/**
 * Create a new table
 */
export async function createTable(tableData: Omit<Table, 'id'>): Promise<Table> {
  // knowledge: add a new table to the tables array in the document
  const doc = await getOrCreateTableDocument();
  // knowledge: ensure doc.tables is always an array
  const tablesArr = Array.isArray(doc.tables) ? doc.tables : [];
  const newTable: Table = {
    id: `table_${uuidv4()}`,
    ...tableData
  };
  const updatedDoc: TableDocument = {
    ...doc,
    tables: [...tablesArr, newTable],
    updatedAt: new Date().toISOString()
  };
  await databaseV4.putDoc(updatedDoc);
  return newTable;
}

/**
 * Update a table by id with proper conflict resolution
 */
export async function updateTable(tableId: string, updates: Partial<Table>): Promise<Table> {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const doc = await getOrCreateTableDocument();
      const idx = doc.tables.findIndex(t => t.id === tableId);
      
      if (idx === -1) {
        throw new Error(`Table with ID ${tableId} not found`);
      }

      const updatedTable = { ...doc.tables[idx], ...updates };
      const updatedDoc: TableDocument = {
        ...doc,
        tables: doc.tables.map((t, i) => (i === idx ? updatedTable : t)),
        updatedAt: new Date().toISOString()
      };
      
      await databaseV4.putDoc(updatedDoc);
      return updatedTable;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[updateTable] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        // Small delay before retry
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      // For other errors or no retries left, throw
      throw error;
    }
  }
  
  throw lastError;
}

/**
 * Delete a table by id
 */
export async function deleteTable(tableId: string): Promise<void> {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const doc = await getOrCreateTableDocument();
      const updatedDoc: TableDocument = {
        ...doc,
        tables: doc.tables.filter(t => t.id !== tableId),
        updatedAt: new Date().toISOString()
      };
      
      await databaseV4.putDoc(updatedDoc);
      return;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[deleteTable] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      throw error;
    }
  }
  
  throw lastError;
}

/**
 * Update table status
 */
export async function updateTableStatus(tableId: string, status: 'free' | 'occupied'): Promise<Table> {
  return updateTable(tableId, { status });
}

/**
 * Helper: get or create the table document with proper conflict handling
 */
async function getOrCreateTableDocument(): Promise<TableDocument> {
  try {
    const doc = await databaseV4.getDoc<TableDocument>('tables');
    // knowledge: ensure tables is always an array
    if (!Array.isArray(doc.tables)) doc.tables = [];
    return doc;
  } catch (error: any) {
    if (error.status === 404) {
      // Create with fresh timestamps
      const defaultDoc = createDefaultTableDocument();
      await databaseV4.putDoc(defaultDoc);
      return { ...defaultDoc };
    }
    throw error;
  }
} 