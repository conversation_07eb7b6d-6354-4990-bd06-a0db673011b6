"use client";

import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import { 
  MenuDocument, 
  MenuCategory, 
  MenuItem, 
  DEFAULT_MENU_DOCUMENT 
} from '../schemas/menu-schema';
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument,
  safeEnsureDocument
} from '../core/conflict-resolution';

/**
 * Menu Operations for V4 Database
 * 
 * This file contains all the database operations related to menu management.
 */

/**
 * Create menu indexes
 */
export async function createMenuIndexes(): Promise<void> {
  try {
    // No specific indexes needed for menu document as it's a singleton
    console.log('Menu indexes created successfully');
  } catch (error) {
    console.error('Error creating menu indexes:', error);
    throw error;
  }
}

/**
 * Get menu document with conflict resolution
 */
export async function getMenu(): Promise<MenuDocument> {
  try {
    return await safeEnsureDocument<MenuDocument>(
      'menu',
      () => ({
        ...DEFAULT_MENU_DOCUMENT,
        _id: 'menu',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }),
      'getMenu'
    );
  } catch (error: any) {
    console.error('Error getting menu:', error);
    throw error;
  }
}

/**
 * Update menu document with robust conflict resolution
 */
export async function updateMenu(menu: MenuDocument): Promise<any> {
  try {
    // Use safe update with conflict resolution
    return await safeUpdateDocument<MenuDocument>(
      'menu',
      (currentMenu) => ({
        ...currentMenu,
        ...menu,
        _id: 'menu', // Ensure ID doesn't change
        type: 'menu_document', // Ensure type doesn't change
        updatedAt: new Date().toISOString()
      }),
      'updateMenu'
    );
  } catch (error) {
    console.error('Error updating menu:', error);
    throw error;
  }
}

/**
 * Add category to menu with conflict resolution
 */
export async function addCategory(category: MenuCategory): Promise<MenuDocument> {
  return await safeUpdateDocument<MenuDocument>(
    'menu',
    (menu) => ({
      ...menu,
      categories: [...(menu.categories || []), category],
      updatedAt: new Date().toISOString()
    }),
    'addCategory'
  );
}

/**
 * Update category in menu with conflict resolution
 */
export async function updateCategory(categoryId: string, updates: Partial<MenuCategory>): Promise<MenuDocument> {
  return await safeUpdateDocument<MenuDocument>(
    'menu',
    (menu) => ({
      ...menu,
      categories: (menu.categories || []).map(cat => 
        cat.id === categoryId 
          ? { ...cat, ...updates, updatedAt: new Date().toISOString() }
          : cat
      ),
      updatedAt: new Date().toISOString()
    }),
    'updateCategory'
  );
}

/**
 * Remove category from menu with conflict resolution
 */
export async function removeCategory(categoryId: string): Promise<MenuDocument> {
  return await safeUpdateDocument<MenuDocument>(
    'menu',
    (menu) => ({
      ...menu,
      categories: (menu.categories || []).filter(cat => cat.id !== categoryId),
      updatedAt: new Date().toISOString()
    }),
    'removeCategory'
  );
}

/**
 * Add item to category with conflict resolution
 */
export async function addItemToCategory(categoryId: string, item: MenuItem): Promise<MenuDocument> {
  return await safeUpdateDocument<MenuDocument>(
    'menu',
    (menu) => ({
      ...menu,
      categories: (menu.categories || []).map(cat => 
        cat.id === categoryId 
          ? { 
              ...cat, 
              items: [...(cat.items || []), item],
              updatedAt: new Date().toISOString()
            }
          : cat
      ),
      updatedAt: new Date().toISOString()
    }),
    'addItemToCategory'
  );
}

/**
 * Update item in category with conflict resolution
 */
export async function updateMenuItem(categoryId: string, itemId: string, updates: Partial<MenuItem>): Promise<MenuDocument> {
  return await safeUpdateDocument<MenuDocument>(
    'menu',
    (menu) => ({
      ...menu,
      categories: (menu.categories || []).map(cat => 
        cat.id === categoryId 
          ? {
              ...cat,
              items: (cat.items || []).map(item => 
                item.id === itemId 
                  ? { ...item, ...updates, updatedAt: new Date().toISOString() }
                  : item
              ),
              updatedAt: new Date().toISOString()
            }
          : cat
      ),
      updatedAt: new Date().toISOString()
    }),
    'updateMenuItem'
  );
}

/**
 * Remove item from category with conflict resolution
 */
export async function removeMenuItem(categoryId: string, itemId: string): Promise<MenuDocument> {
  return await safeUpdateDocument<MenuDocument>(
    'menu',
    (menu) => ({
      ...menu,
      categories: (menu.categories || []).map(cat => 
        cat.id === categoryId 
          ? {
              ...cat,
              items: (cat.items || []).filter(item => item.id !== itemId),
              updatedAt: new Date().toISOString()
            }
          : cat
      ),
      updatedAt: new Date().toISOString()
    }),
    'removeMenuItem'
  );
}

/**
 * Add a size to a category
 */
export async function addSizeToCategory(categoryId: string, size: string): Promise<string[]> {
  try {
    // 1. Get the latest menu document
    const menu = await getMenu();

    // 2. Perform modifications
    const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
    if (categoryIndex === -1) {
      throw new Error(`Category with ID ${categoryId} not found`);
    }
    if (!menu.categories[categoryIndex].sizes) {
      menu.categories[categoryIndex].sizes = [];
    }
    if (!menu.categories[categoryIndex].sizes!.includes(size)) {
      menu.categories[categoryIndex].sizes!.push(size); // Modify the local copy
    } else {
      console.warn(`Size '${size}' already exists in category ${categoryId}`);
      // Return current sizes even if no change
      return menu.categories[categoryIndex].sizes!;
    }

    // 3. Call the robust updateMenu function
    await updateMenu(menu);

    // 4. Return the updated sizes array
    return menu.categories[categoryIndex].sizes!;
  } catch (error) {
    console.error('Error adding size to category:', error);
    throw error;
  }
}

/**
 * Rename a size within a category and update item prices
 */
export async function renameSizeInCategory(categoryId: string, oldSize: string, newSize: string): Promise<string[]> {
  try {
    // 1. Get the latest menu document
    const menu = await getMenu();

    // 2. Perform modifications
    const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
    if (categoryIndex === -1) {
      throw new Error(`Category with ID ${categoryId} not found`);
    }

    const category = menu.categories[categoryIndex];
    if (!category.sizes || !category.sizes.includes(oldSize)) {
      throw new Error(`Old size '${oldSize}' not found in category ${categoryId}`);
    }
    if (category.sizes.includes(newSize)) {
      throw new Error(`New size '${newSize}' already exists in category ${categoryId}`);
    }

    // Update sizes array
    category.sizes = category.sizes.map(s => s === oldSize ? newSize : s);

    // Update item prices that used the old size
    category.items.forEach(item => {
      if (item.prices && item.prices.hasOwnProperty(oldSize)) {
        item.prices[newSize] = item.prices[oldSize];
        delete item.prices[oldSize];
      }
    });

    // Modify the menu object directly
    menu.categories[categoryIndex] = category;

    // 3. Call the robust updateMenu function
    await updateMenu(menu);

    // 4. Return the updated sizes array
    return category.sizes;
  } catch (error) {
    console.error('Error renaming size in category:', error);
    throw error;
  }
}

/**
 * Delete a size from a category
 */
export async function deleteSizeFromCategory(categoryId: string, sizeName: string): Promise<string[]> {
  try {
    // 1. Get the latest menu document
    const menu = await getMenu();

    // 2. Perform modifications
    const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
    if (categoryIndex === -1) {
      throw new Error(`Category with ID ${categoryId} not found`);
    }

    if (!menu.categories[categoryIndex].sizes) {
      return [];
    }

    // Remove the size
    menu.categories[categoryIndex].sizes = menu.categories[categoryIndex].sizes!.filter(s => s !== sizeName);

    // Remove pricing for this size from all items in the category
    menu.categories[categoryIndex].items.forEach(item => {
      if (item.prices && item.prices[sizeName]) {
        delete item.prices[sizeName];
      }
    });

    // 3. Call the robust updateMenu function
    await updateMenu(menu);

    // 4. Return the updated sizes array
    return menu.categories[categoryIndex].sizes || [];
  } catch (error) {
    console.error('Error deleting size from category:', error);
    throw error;
  }
}

/**
 * Repair menu sizes based on item prices.
 * Ensures all unique keys used in item prices within a category are present in the category's sizes array.
 */
export async function repairMenuSizes(): Promise<boolean> {
  let changesMade = false;
  try {
    // 1. Get the latest menu document
    const menu = await getMenu();

    // 2. Perform modifications (check and repair sizes)
    menu.categories.forEach(category => {
      const itemPriceKeys = new Set<string>();
      category.items.forEach(item => {
        if (item.prices) {
          Object.keys(item.prices).forEach(key => itemPriceKeys.add(key));
        }
      });

      if (!category.sizes) {
        category.sizes = [];
      }

      const initialSizeCount = category.sizes.length;
      itemPriceKeys.forEach(key => {
        if (!category.sizes!.includes(key)) {
          category.sizes!.push(key);
          changesMade = true;
        }
      });

      if (category.sizes.length > initialSizeCount) {
          console.log(`Repaired sizes for category '${category.name}' (${category.id}). Added missing sizes based on item prices. New sizes:`, category.sizes);
      }
    });

    // 3. Call updateMenu ONLY if changes were made
    if (changesMade) {
      console.log("repairMenuSizes found inconsistencies. Updating menu document...");
      await updateMenu(menu);
      return true; // Indicate changes were made and saved
    } else {
        console.log("repairMenuSizes found no inconsistencies in sizes.");
        return false; // Indicate no changes needed
    }

  } catch (error) {
    console.error('Error repairing menu sizes:', error);
    throw error;
  }
}

/**
 * Ensures all categories have the basic required structure (items, addons, sizes arrays).
 * Useful for migrating older data or fixing potential inconsistencies.
 */
export async function ensureCategoriesStructure(): Promise<boolean> {
    let changesMade = false;
    try {
        // 1. Get the latest menu document
        const menu = await getMenu();

        // 2. Perform modifications (ensure structure)
        menu.categories.forEach(category => {
            if (!Array.isArray(category.items)) {
                console.warn(`Category '${category.name}' (${category.id}) missing 'items' array. Initializing.`);
                category.items = [];
                changesMade = true;
            }
            if (!Array.isArray(category.sizes)) {
                console.warn(`Category '${category.name}' (${category.id}) missing 'sizes' array. Initializing.`);
                category.sizes = [];
                changesMade = true;
            }

            // Optionally ensure item structure too (e.g., addons array)
             category.items.forEach(item => {
                 if (typeof item.prices !== 'object' || item.prices === null) {
                      console.warn(`Item '${item.name}' (${item.id}) in category ${category.id} missing 'prices' object. Initializing.`);
                      item.prices = {};
                      changesMade = true;
                 }
             });
        });

        // 3. Call updateMenu ONLY if changes were made
        if (changesMade) {
            console.log("ensureCategoriesStructure found structural issues. Updating menu document...");
            await updateMenu(menu);
            return true; // Indicate changes were made and saved
        } else {
            console.log("ensureCategoriesStructure found no structural issues.");
            return false; // Indicate no changes needed
        }

    } catch (error) {
        console.error('Error ensuring category structure:', error);
        throw error;
    }
}
