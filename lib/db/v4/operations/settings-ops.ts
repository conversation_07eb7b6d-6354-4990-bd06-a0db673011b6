// knowledge: v4 settings operations
import { databaseV4 } from '../core/db-instance';
import { RestaurantSettings, DEFAULT_RESTAURANT_SETTINGS } from '../schemas/restaurant-settings-schema';
// 🚀 Import the new conflict resolution utilities
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument,
  safeEnsureDocument
} from '../core/conflict-resolution';

/**
 * Get settings from the database with proper initialization handling
 */
export async function getSettings(): Promise<RestaurantSettings> {
  // Check if database is initialized
  if (!databaseV4.isInitialized) {
    console.error('[getSettings] Database not initialized. Waiting for initialization...');
    // Wait for initialization with proper error handling
    await databaseV4.waitForInitialization(databaseV4.getCurrentRestaurantId() || 'default', 15000);
  }

  return safeEnsureDocument<RestaurantSettings>(
    'restaurant-settings',
    () => ({
      ...DEFAULT_RESTAURANT_SETTINGS,
      _id: 'restaurant-settings',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }),
    'getSettings'
  );
}

/**
 * Update settings in the database with conflict resolution
 */
export async function updateSettings(updates: Partial<RestaurantSettings>): Promise<RestaurantSettings> {
  if (!databaseV4.isInitialized) {
    throw new Error('V4 Database not initialized. Call initialize() first.');
  }

  return safeUpdateDocument<RestaurantSettings>(
    'restaurant-settings',
    (currentSettings) => ({
      ...currentSettings,
      ...updates,
      _id: 'restaurant-settings', // Ensure _id is always present
      updatedAt: new Date().toISOString()
    }),
    'updateSettings'
  );
}

/**
 * Ensure default settings exist in the database with conflict resolution
 */
export async function ensureDefaultSettings(): Promise<RestaurantSettings> {
  console.log('[ensureDefaultSettings] Starting to ensure default settings exist');
  
  if (!databaseV4.isInitialized) {
    console.error('[ensureDefaultSettings] Database not initialized. Cannot create default settings.');
    throw new Error('V4 Database not initialized. Call initialize() first.');
  }
  
  return safeEnsureDocument<RestaurantSettings>(
    'restaurant-settings',
    () => ({
      ...DEFAULT_RESTAURANT_SETTINGS,
      _id: 'restaurant-settings',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }),
    'ensureDefaultSettings'
  );
}

/**
 * Initialize settings - ensures settings exist on app startup
 */
export async function initializeSettings(): Promise<void> {
  console.log('[initializeSettings] Initializing restaurant settings...');
  
  if (!databaseV4.isInitialized) {
    console.error('[initializeSettings] Database not initialized. Cannot initialize settings.');
    return; // Don't throw, just log and return
  }
  
  try {
    await ensureDefaultSettings();
    console.log('[initializeSettings] Settings initialization completed successfully');
  } catch (error) {
    console.error('[initializeSettings] Failed to initialize settings:', error);
    // Don't throw here - we want the app to continue even if settings init fails
  }
}
// endknowledge