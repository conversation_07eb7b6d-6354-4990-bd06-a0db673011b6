"use client";

import { v4 as uuidv4 } from 'uuid';
import { PackagingItem, Table } from '../schemas/table-schema';
import { PackagingConfigByOrderType } from '../schemas/menu-schema';
import { getAllTables } from './table-ops';
import { getMenu } from './menu-ops';
import { addConsumptionLog } from './inventory-ops';
import { OrderType, normalizeOrderType } from '@/lib/types/order-types';

/**
 * Packaging Consumption Operations for V4 Database
 * 
 * This file contains all the database operations related to packaging consumption
 * based on table configuration and menu category rules.
 */

export type { OrderType } from '@/lib/types/order-types';

export interface OrderItem {
  menuItemId: string;
  categoryId: string;
  size: string;
  quantity: number;
}

export interface PackagingConsumptionOrder {
  tableId?: string; // Required for dine-in orders
  orderType: OrderType;
  items: OrderItem[];
}

export interface PackagingConsumptionResult {
  tableConsumption: Array<{
    stockItemId: string;
    quantity: number;
    source: 'table';
    tableName?: string;
  }>;
  menuConsumption: Array<{
    stockItemId: string;
    quantity: number;
    source: 'menu';
    categoryName?: string;
    size?: string;
    orderType?: OrderType;
  }>;
  totalConsumption: Array<{
    stockItemId: string;
    totalQuantity: number;
  }>;
  success: boolean;
  errors: string[];
}

/**
 * Process packaging consumption for an order
 * This is the main function that handles both table-level and menu-level consumption
 */
export async function processPackagingConsumption(
  order: PackagingConsumptionOrder
): Promise<PackagingConsumptionResult> {
  const result: PackagingConsumptionResult = {
    tableConsumption: [],
    menuConsumption: [],
    totalConsumption: [],
    success: true,
    errors: []
  };

  try {
    // 1. Table-level consumption (only for dine-in orders)
    if (order.orderType === 'dine-in' && order.tableId) {
      const tableConsumption = await processTablePackagingConsumption(order.tableId);
      result.tableConsumption = tableConsumption;
    }

    // 2. Menu-level consumption (for all order types)
    const menuConsumption = await processMenuPackagingConsumption(order.items, order.orderType);
    result.menuConsumption = menuConsumption;

    // 3. Aggregate total consumption
    result.totalConsumption = aggregatePackagingConsumption([
      ...result.tableConsumption,
      ...result.menuConsumption
    ]);

    // 4. Log packaging consumption 
    const orderId = `order_${uuidv4()}`;
    for (const consumption of result.totalConsumption) {
      try {
        await addConsumptionLog({
          orderId: orderId,
          stockItemId: consumption.stockItemId,
          quantity: consumption.totalQuantity,
          costPerUnit: 0, // Packaging items have no cost
          totalCost: 0,
          menuItemId: 'packaging',
          menuItemName: 'Packaging Consumption',
          date: new Date().toISOString()
        });
      } catch (error) {
        result.errors.push(`Failed to log consumption for ${consumption.totalQuantity} units of stock item ${consumption.stockItemId}: ${error}`);
        result.success = false;
      }
    }

    console.log('📦 Packaging consumption processed:', {
      orderType: order.orderType,
      tableId: order.tableId,
      itemCount: order.items.length,
      tableConsumption: result.tableConsumption.length,
      menuConsumption: result.menuConsumption.length,
      totalItems: result.totalConsumption.length,
      success: result.success
    });

  } catch (error) {
    result.errors.push(`Packaging consumption failed: ${error}`);
    result.success = false;
    console.error('❌ Packaging consumption error:', error);
  }

  return result;
}

/**
 * Process table-level packaging consumption
 */
async function processTablePackagingConsumption(
  tableId: string
): Promise<Array<{
  stockItemId: string;
  quantity: number;
  source: 'table';
  tableName?: string;
}>> {
  try {
    const tables = await getAllTables();
    const table = tables.find((t: Table) => t.id === tableId);
    
    if (!table) {
      console.warn(`Table ${tableId} not found for packaging consumption`);
      return [];
    }

    if (!table.packaging || table.packaging.length === 0) {
      console.log(`Table ${table.name} has no packaging configuration`);
      return [];
    }

    return table.packaging.map((item: PackagingItem) => ({
      stockItemId: item.stockItemId,
      quantity: item.quantity,
      source: 'table' as const,
      tableName: table.name
    }));

  } catch (error) {
    console.error('Error processing table packaging consumption:', error);
    throw error;
  }
}

/**
 * Process menu-level packaging consumption
 */
async function processMenuPackagingConsumption(
  orderItems: OrderItem[],
  orderType: OrderType
): Promise<Array<{
  stockItemId: string;
  quantity: number;
  source: 'menu';
  categoryName?: string;
  size?: string;
  orderType?: OrderType;
}>> {
  try {
    const menu = await getMenu();
    const menuConsumption: Array<{
      stockItemId: string;
      quantity: number;
      source: 'menu';
      categoryName?: string;
      size?: string;
      orderType?: OrderType;
    }> = [];

    for (const orderItem of orderItems) {
      const category = menu.categories.find(c => c.id === orderItem.categoryId);
      
      if (!category) {
        console.warn(`Category ${orderItem.categoryId} not found for packaging consumption`);
        continue;
      }

      // Check if this category has packaging rules for this size and order type
      if (!category.packaging || !category.packaging[orderItem.size]) {
        console.log(`Category ${category.name} has no packaging configuration for size ${orderItem.size}`);
        continue;
      }

      const sizePackaging = category.packaging[orderItem.size];
      const orderTypePackaging = sizePackaging[orderType];

      if (!orderTypePackaging || orderTypePackaging.length === 0) {
        console.log(`Category ${category.name} size ${orderItem.size} has no packaging for ${orderType}`);
        continue;
      }

      // Add packaging consumption for this order item
      for (const packagingItem of orderTypePackaging) {
        menuConsumption.push({
          stockItemId: packagingItem.stockItemId,
          quantity: packagingItem.quantity * orderItem.quantity,
          source: 'menu',
          categoryName: category.name,
          size: orderItem.size,
          orderType: orderType
        });
      }
    }

    return menuConsumption;

  } catch (error) {
    console.error('Error processing menu packaging consumption:', error);
    throw error;
  }
}

/**
 * Aggregate packaging consumption by stock item
 */
function aggregatePackagingConsumption(
  consumption: Array<{
    stockItemId: string;
    quantity: number;
    source: 'table' | 'menu';
  }>
): Array<{
  stockItemId: string;
  totalQuantity: number;
}> {
  const aggregated = new Map<string, number>();

  for (const item of consumption) {
    const currentQuantity = aggregated.get(item.stockItemId) || 0;
    aggregated.set(item.stockItemId, currentQuantity + item.quantity);
  }

  return Array.from(aggregated.entries()).map(([stockItemId, totalQuantity]) => ({
    stockItemId,
    totalQuantity
  }));
}

/**
 * Update table packaging configuration
 */
export async function updateTablePackaging(
  tableId: string,
  packaging: PackagingItem[]
): Promise<void> {
  try {
    const { updateTable } = await import('./table-ops');
    await updateTable(tableId, { packaging });
    console.log(`Updated packaging for table ${tableId}:`, packaging);
  } catch (error) {
    console.error('Error updating table packaging:', error);
    throw error;
  }
}

/**
 * Update category packaging rules
 */
export async function updateCategoryPackaging(
  categoryId: string,
  sizeName: string,
  orderType: OrderType,
  packaging: PackagingItem[]
): Promise<void> {
  try {
    const { updateCategory } = await import('./menu-ops');
    const menu = await getMenu();
    const category = menu.categories.find(c => c.id === categoryId);
    
    if (!category) {
      throw new Error(`Category ${categoryId} not found`);
    }

    // Initialize packaging object if it doesn't exist
    if (!category.packaging) {
      category.packaging = {};
    }

    // Initialize size packaging if it doesn't exist
    if (!category.packaging[sizeName]) {
      category.packaging[sizeName] = {};
    }

    // Set the packaging for this size and order type
    category.packaging[sizeName][orderType] = packaging;

    await updateCategory(categoryId, { packaging: category.packaging });
    console.log(`Updated packaging for category ${categoryId}, size ${sizeName}, order type ${orderType}:`, packaging);
  } catch (error) {
    console.error('Error updating category packaging:', error);
    throw error;
  }
}

/**
 * Get packaging configuration for a table
 */
export async function getTablePackaging(tableId: string): Promise<PackagingItem[]> {
  try {
    const tables = await getAllTables();
    const table = tables.find((t: Table) => t.id === tableId);
    return table?.packaging || [];
  } catch (error) {
    console.error('Error getting table packaging:', error);
    return [];
  }
}

/**
 * Get packaging configuration for a category size and order type
 */
export async function getCategoryPackaging(
  categoryId: string,
  sizeName: string,
  orderType: OrderType
): Promise<PackagingItem[]> {
  try {
    const menu = await getMenu();
    const category = menu.categories.find(c => c.id === categoryId);
    
    if (!category?.packaging?.[sizeName]?.[orderType]) {
      return [];
    }

    return category.packaging[sizeName][orderType] || [];
  } catch (error) {
    console.error('Error getting category packaging:', error);
    return [];
  }
}

/**
 * Demo function to test packaging consumption
 */
export async function demoPackagingConsumption(): Promise<void> {
  console.log('🧪 Testing packaging consumption system...');
  
  try {
    // Sample order for testing
    const sampleOrder: PackagingConsumptionOrder = {
      tableId: 'table_1', // Assuming a table exists
      orderType: 'dine-in',
      items: [
        {
          menuItemId: 'item_1',
          categoryId: 'category_1',
          size: 'Medium',
          quantity: 2
        },
        {
          menuItemId: 'item_2',
          categoryId: 'category_1',
          size: 'Large',
          quantity: 1
        }
      ]
    };

    const result = await processPackagingConsumption(sampleOrder);
    
    console.log('📦 Packaging consumption test results:', {
      success: result.success,
      tableConsumption: result.tableConsumption,
      menuConsumption: result.menuConsumption,
      totalConsumption: result.totalConsumption,
      errors: result.errors
    });

    if (result.success) {
      console.log('✅ Packaging consumption test passed!');
    } else {
      console.log('❌ Packaging consumption test failed:', result.errors);
    }
  } catch (error) {
    console.error('❌ Packaging consumption test error:', error);
  }
}

/**
 * Map OrderDocument orderType to PackagingConsumptionOrder OrderType
 * Handles legacy order types for compatibility
 */
function mapOrderType(orderType: string): OrderType {
  return normalizeOrderType(orderType);
}

/**
 * Process packaging consumption for an order document
 * This wrapper handles OrderDocument to PackagingConsumptionOrder conversion
 */
export async function processOrderPackagingConsumption(orderDoc: any): Promise<PackagingConsumptionResult> {
  // Convert OrderDocument to PackagingConsumptionOrder
  const packagingOrder: PackagingConsumptionOrder = {
    tableId: orderDoc.tableId,
    orderType: mapOrderType(orderDoc.orderType),
    items: orderDoc.items.map((item: any) => ({
      menuItemId: item.menuItemId || item.id,
      categoryId: item.categoryId,
      size: item.size || 'default',
      quantity: item.quantity
    }))
  };

  return await processPackagingConsumption(packagingOrder);
} 