/**
 * Staff Menu Operations - Database operations for staff meal management
 */

import { databaseV4 } from '../core/db-instance';
import { 
  StaffMenuConfigDocument, 
  StaffAllowanceDocument, 
  StaffMenuItem,
  DEFAULT_STAFF_MENU_CONFIG
} from '../schemas/staff-menu-schema';
import { v4 as uuidv4 } from 'uuid';

// =============================================
// STAFF MENU CONFIGURATION OPERATIONS
// =============================================

/**
 * Get or create staff menu configuration
 */
export async function getStaffMenuConfig(): Promise<StaffMenuConfigDocument> {
  try {
    const doc = await databaseV4.getDoc<StaffMenuConfigDocument>('staff-menu-config');
    return doc;
  } catch (error: any) {
    if (error.status === 404) {
      // Create default configuration
      const defaultConfig: StaffMenuConfigDocument = {
        _id: 'staff-menu-config',
        ...DEFAULT_STAFF_MENU_CONFIG
      };
      
      await databaseV4.putDoc(defaultConfig);
      return defaultConfig;
    }
    throw error;
  }
}

/**
 * Update staff menu configuration
 */
export async function updateStaffMenuConfig(
  updates: Partial<Pick<StaffMenuConfigDocument, 'allowancePerShift' | 'staffMenuItems' | 'isEnabled'>>
): Promise<StaffMenuConfigDocument> {
  const config = await getStaffMenuConfig();
  
  const updatedConfig: StaffMenuConfigDocument = {
    ...config,
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  await databaseV4.putDoc(updatedConfig);
  return updatedConfig;
}

/**
 * Add a staff menu item
 */
export async function addStaffMenuItem(item: Omit<StaffMenuItem, 'id'>): Promise<StaffMenuConfigDocument> {
  const config = await getStaffMenuConfig();
  
  const newItem: StaffMenuItem = {
    id: uuidv4(),
    ...item,
    isActive: true
  };
  
  const updatedItems = [...config.staffMenuItems, newItem];
  
  return updateStaffMenuConfig({ staffMenuItems: updatedItems });
}

/**
 * Update a staff menu item
 */
export async function updateStaffMenuItem(
  itemId: string, 
  updates: Partial<Omit<StaffMenuItem, 'id'>>
): Promise<StaffMenuConfigDocument> {
  const config = await getStaffMenuConfig();
  
  const updatedItems = config.staffMenuItems.map(item =>
    item.id === itemId ? { ...item, ...updates } : item
  );
  
  return updateStaffMenuConfig({ staffMenuItems: updatedItems });
}

/**
 * Remove a staff menu item
 */
export async function removeStaffMenuItem(itemId: string): Promise<StaffMenuConfigDocument> {
  const config = await getStaffMenuConfig();
  
  const updatedItems = config.staffMenuItems.filter(item => item.id !== itemId);
  
  return updateStaffMenuConfig({ staffMenuItems: updatedItems });
}

/**
 * Get active staff menu items
 */
export async function getActiveStaffMenuItems(): Promise<StaffMenuItem[]> {
  const config = await getStaffMenuConfig();
  return config.staffMenuItems.filter(item => item.isActive !== false);
}

// =============================================
// STAFF ALLOWANCE OPERATIONS
// =============================================

/**
 * Generate staff allowance document ID
 */
function generateAllowanceId(staffId: string, shiftId: string, date: string): string {
  return `staff-allowance:${staffId}:${shiftId}:${date}`;
}

/**
 * Get or create staff allowance for a specific shift
 */
export async function getStaffAllowance(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string,
  date: string
): Promise<StaffAllowanceDocument> {
  // 🚩 Special bypass for collective staff orders
  if (staffId === 'staff-collective') {
    return {
      _id: 'staff-collective',
      type: 'staff_allowance',
      schemaVersion: 'v4.0',
      staffId,
      staffName,
      shiftId,
      shiftName,
      date,
      maxAllowance: Infinity,
      usedAllowance: 0,
      remainingAllowance: Infinity,
      orders: [],
      isPresent: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }

  const config = await getStaffMenuConfig();
  const allowanceId = generateAllowanceId(staffId, shiftId, date);
  
  try {
    const doc = await databaseV4.getDoc<StaffAllowanceDocument>(allowanceId);
    return doc;
  } catch (error: any) {
    if (error.status === 404) {
      // Create new allowance document
      const newAllowance: StaffAllowanceDocument = {
        _id: allowanceId,
        type: 'staff_allowance',
        schemaVersion: 'v4.0',
        staffId,
        staffName,
        shiftId,
        shiftName,
        date,
        maxAllowance: config.allowancePerShift,
        usedAllowance: 0,
        remainingAllowance: config.allowancePerShift,
        orders: [],
        isPresent: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      await databaseV4.putDoc(newAllowance);
      return newAllowance;
    }
    throw error;
  }
}

/**
 * Mark staff as present for their shift
 */
export async function markStaffPresent(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string,
  date: string
): Promise<StaffAllowanceDocument> {
  const allowance = await getStaffAllowance(staffId, staffName, shiftId, shiftName, date);
  
  const updatedAllowance: StaffAllowanceDocument = {
    ...allowance,
    isPresent: true,
    updatedAt: new Date().toISOString()
  };
  
  await databaseV4.putDoc(updatedAllowance);
  return updatedAllowance;
}

/**
 * Use staff allowance for an order
 */
export async function useStaffAllowance(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string,
  date: string,
  orderId: string,
  itemsUsed: number = 1
): Promise<{ success: boolean; allowance?: StaffAllowanceDocument; error?: string }> {
  const allowance = await getStaffAllowance(staffId, staffName, shiftId, shiftName, date);
  
  // Check if staff is present
  if (!allowance.isPresent) {
    return { 
      success: false, 
      error: 'Staff member must be marked as present to use meal allowance' 
    };
  }
  
  // Check if enough allowance remains
  if (allowance.remainingAllowance < itemsUsed) {
    return { 
      success: false, 
      error: `Insufficient allowance. Remaining: ${allowance.remainingAllowance}, Required: ${itemsUsed}` 
    };
  }
  
  // Update allowance
  const updatedAllowance: StaffAllowanceDocument = {
    ...allowance,
    usedAllowance: allowance.usedAllowance + itemsUsed,
    remainingAllowance: allowance.remainingAllowance - itemsUsed,
    orders: [...allowance.orders, orderId],
    updatedAt: new Date().toISOString()
  };
  
  await databaseV4.putDoc(updatedAllowance);
  return {
    success: true,
    allowance: updatedAllowance
  };
}

/**
 * Get all staff allowances for a specific date
 */
export async function getStaffAllowancesForDate(date: string): Promise<StaffAllowanceDocument[]> {
  const result = await databaseV4.findDocs({
    selector: {
      type: 'staff_allowance',
      date: date
    }
  });
  
  return result.docs as StaffAllowanceDocument[];
}

/**
 * Get staff allowance history for a specific staff member
 */
export async function getStaffAllowanceHistory(
  staffId: string,
  startDate?: string,
  endDate?: string
): Promise<StaffAllowanceDocument[]> {
  const selector: any = {
    type: 'staff_allowance',
    staffId: staffId
  };
  
  if (startDate && endDate) {
    selector.date = {
      $gte: startDate,
      $lte: endDate
    };
  } else if (startDate) {
    selector.date = { $gte: startDate };
  } else if (endDate) {
    selector.date = { $lte: endDate };
  }
  
  const result = await databaseV4.findDocs({
    selector,
    sort: [{ date: 'desc' }]
  });
  
  return result.docs as StaffAllowanceDocument[];
}

/**
 * Check if staff can order (has remaining allowance and is present)
 */
export async function canStaffOrder(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string,
  date: string,
  itemsToOrder: number = 1
): Promise<{ canOrder: boolean; reason?: string; allowance?: StaffAllowanceDocument }> {
  // 🚩 Special bypass for collective staff orders: always allowed, no allowance tracking
  if (staffId === 'staff-collective') {
    return { canOrder: true, allowance: { 
      _id: 'staff-collective', type: 'staff_allowance', schemaVersion: 'v4.0',
      staffId, staffName, shiftId, shiftName, date, 
      maxAllowance: Infinity, usedAllowance: 0, remainingAllowance: Infinity, 
      orders: [], isPresent: true, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() 
    } };
  }
  
  const allowance = await getStaffAllowance(staffId, staffName, shiftId, shiftName, date);
  
  // Check if staff is present either in allowance system OR attendance system
  let isStaffPresent = allowance.isPresent;
  
  if (!isStaffPresent) {
    // Check attendance system as fallback
    try {
      const { getStaffAttendanceRecords } = await import('./per-staff-ops');
      const attendanceRecords = await getStaffAttendanceRecords(staffId, date, date);
      
      // Check if staff has present/late status for this shift today
      const hasAttendanceToday = attendanceRecords.some(record => 
        record.date === date && 
        record.shiftId === shiftId && 
        (record.status === 'present' || record.status === 'late')
      );
      
      if (hasAttendanceToday) {
        // Auto-sync: mark as present in allowance system too
        const updatedAllowance: StaffAllowanceDocument = {
          ...allowance,
          isPresent: true,
          updatedAt: new Date().toISOString()
        };
        
        await databaseV4.putDoc(updatedAllowance);
        isStaffPresent = true;
        
        console.log(`🔄 Auto-synced staff presence: ${staffName} marked present in allowance system based on attendance`);
      }
    } catch (error) {
      console.warn('Could not check attendance system for staff presence:', error);
    }
  }
  
  if (!isStaffPresent) {
    return { 
      canOrder: false, 
      reason: 'Staff member is not marked as present for this shift',
      allowance 
    };
  }
  
  if (allowance.remainingAllowance < itemsToOrder) {
    return { 
      canOrder: false, 
      reason: `Insufficient allowance. Remaining: ${allowance.remainingAllowance}, Required: ${itemsToOrder}`,
      allowance 
    };
  }
  
  return { canOrder: true, allowance };
}

/**
 * Reset all allowances for a new day
 */
export async function resetAllowancesForNewDay(date: string): Promise<void> {
  const config = await getStaffMenuConfig();
  
  // Get all allowances for the previous day
  const previousDate = new Date(date);
  previousDate.setDate(previousDate.getDate() - 1);
  const prevDateStr = previousDate.toISOString().split('T')[0];
  
  const prevAllowances = await getStaffAllowancesForDate(prevDateStr);
  
  // Create new allowances for present staff
  const newAllowances = prevAllowances
    .filter(allowance => allowance.isPresent)
    .map(prevAllowance => ({
      ...prevAllowance,
      _id: generateAllowanceId(prevAllowance.staffId, prevAllowance.shiftId, date),
      _rev: undefined,
      date,
      maxAllowance: config.allowancePerShift,
      usedAllowance: 0,
      remainingAllowance: config.allowancePerShift,
      orders: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));
  
  // Batch insert new allowances
  if (newAllowances.length > 0) {
    await databaseV4.bulkDocs(newAllowances);
  }
} 