// knowledge: v4 production batch CRUD operations
import { databaseV4 } from '../core/db-instance';
import { ProductionBatch, DEFAULT_PRODUCTION_BATCH } from '../schemas/production-batch-schema';
import { getAllSubRecipes } from './sub-recipe-ops';
import { getInventory, updateInventory } from './inventory-ops';
import { 
  retryWithConflictResolution, 
  safeUpdateDocument 
} from '../core/conflict-resolution';

export async function createProductionBatch(data: Omit<ProductionBatch, '_id' | 'type' | 'createdAt' | 'updatedAt'>): Promise<ProductionBatch> {
  // knowledge: deduct ingredient stock from inventory when producing a batch
  const now = new Date().toISOString();
  const batch: ProductionBatch = {
    ...DEFAULT_PRODUCTION_BATCH,
    ...data,
    _id: `production-batch:${crypto.randomUUID()}`,
    type: 'production-batch',
    createdAt: now,
    updatedAt: now,
  };

  // Fetch all sub-recipes and find the one we need to avoid 404
  console.log(`[createProductionBatch] Looking for sub-recipe: ${data.subRecipeId}`);
  const allSubRecipes = await getAllSubRecipes();
  const subRecipe = allSubRecipes.find(sr => sr._id === data.subRecipeId);
  
  if (!subRecipe) {
    console.error(`[createProductionBatch] Sub-recipe not found: ${data.subRecipeId}`);
    throw new Error('Sub-recipe not found');
  }
  
  console.log(`[createProductionBatch] Found sub-recipe: ${subRecipe.name} with ${subRecipe.ingredients.length} ingredients`);

  // --- knowledge: Calculate and lock in costPerUnit for this batch ---
  const inventory = await getInventory();
  let totalCost = 0;
  for (const ingredient of subRecipe.ingredients) {
    const stock = inventory.items.find(i => i.id === ingredient.stockItemId);
    if (stock && typeof stock.costPerUnit === 'number') {
      totalCost += stock.costPerUnit * ingredient.quantity * data.batchSize;
    }
  }
  const costPerUnit = data.batchSize > 0 ? totalCost / (data.batchSize) : 0;
  batch.costPerUnit = costPerUnit;
  // --- end knowledge ---

  // --- knowledge: Accumulate sub-recipe stock ---
  const producedQty = data.producedQuantity || data.batchSize;
  const newStock = (subRecipe.currentStock || 0) + producedQty;
  
  // Use conflict resolution for sub-recipe update
  await retryWithConflictResolution(async () => {
    await databaseV4.putDoc({
      ...subRecipe,
      currentStock: newStock,
      lastProduced: now,
      updatedAt: now
    });
  });

  // --- knowledge: Deduct ingredient stock from inventory ---
  for (const ingredient of subRecipe.ingredients) {
    const requiredQty = ingredient.quantity * data.batchSize;
    console.log(`[createProductionBatch] Deducting ${requiredQty} units of ${ingredient.stockItemId}`);
    
    const stockIdx = inventory.items.findIndex(i => i.id === ingredient.stockItemId);
    if (stockIdx !== -1) {
      const currentQty = inventory.items[stockIdx].quantity || 0;
      const newQty = Math.max(0, currentQty - requiredQty);
      
      inventory.items[stockIdx] = {
        ...inventory.items[stockIdx],
        quantity: newQty,
        updatedAt: now
      };
      
      console.log(`[createProductionBatch] Updated stock for ${ingredient.stockItemId}: ${currentQty} → ${newQty}`);
    }
  }
  
  await updateInventory(inventory);
  // --- end knowledge ---

  await databaseV4.putDoc(batch);
  return batch;
}

export async function getProductionBatch(id: string): Promise<ProductionBatch | null> {
  try {
    console.log(`[getProductionBatch] Fetching production batch by ID: ${id} using getAllProductionBatches + filter pattern`);
    const allBatches = await getAllProductionBatches();
    const batch = allBatches.find(b => b._id === id);
    
    if (batch) {
      console.log(`[getProductionBatch] Found production batch: ${batch._id}`);
      return batch;
    } else {
      console.log(`[getProductionBatch] Production batch not found: ${id}`);
      return null;
    }
  } catch (error: any) {
    console.error(`[getProductionBatch] Error fetching production batch ${id}:`, error);
    return null; // Return null on any error to be consistent
  }
}

export async function getAllProductionBatches(): Promise<ProductionBatch[]> {
  const result = await databaseV4.findDocs<ProductionBatch>({ selector: { type: 'production-batch' } });
  return result.docs;
}

export async function updateProductionBatch(id: string, updates: Partial<ProductionBatch>): Promise<ProductionBatch | null> {
  const current = await getProductionBatch(id);
  if (!current) return null;
  
  // Use safe update with conflict resolution
  return await safeUpdateDocument<ProductionBatch>(id, (doc) => ({
    ...doc,
    ...updates,
    updatedAt: new Date().toISOString(),
  }));
}

export async function deleteProductionBatch(id: string): Promise<void> {
  try {
    // 🚀 FIX: Use getProductionBatch which now uses getAllProductionBatches + filter pattern
    const current = await getProductionBatch(id);
    if (!current || !current._rev) {
        console.warn(`[deleteProductionBatch] Document ${id} not found or missing revision.`);
        return;
    }
    await databaseV4.deleteDoc(id, current._rev);
  } catch (error: any) {
     if (error.status === 404 || error.name === 'not_found') {
          console.warn(`[deleteProductionBatch] Document ${id} already deleted or not found.`);
          return; // Already gone
      }
      console.error(`[deleteProductionBatch] Error deleting document ${id}:`, error);
      throw error; // Re-throw other errors
  }
}

// knowledge: get all production batches for a specific sub-recipe (v4 direct)
export async function getProductionBatchesForSubRecipe(subRecipeId: string): Promise<ProductionBatch[]> {
  const result = await databaseV4.findDocs<ProductionBatch>({
    selector: {
      type: 'production-batch',
      subRecipeId
    }
  });
  return result.docs;
}
// endknowledge 