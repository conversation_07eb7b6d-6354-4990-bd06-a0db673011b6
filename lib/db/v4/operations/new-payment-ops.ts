"use client";

/**
 * NEW PAYMENT SYSTEM DATABASE OPERATIONS
 * 
 * This file contains database operations for the new separate balance payment system:
 * - Staff balance operations (CRUD)
 * - Payment snapshot operations (CRUD)
 * - Index creation and management
 * - Query operations for efficient data retrieval
 */

import { v4 as uuidv4 } from 'uuid';
import {
  StaffBalanceDocument,
  PaymentSnapshotDocument,
  staffBalanceDesignDoc,
  paymentSnapshotDesignDoc
} from '../schemas/new-payment-schemas';

// ===== DATABASE HELPER =====

/**
 * Get the database instance dynamically to ensure we get the initialized instance
 */
async function getDatabase() {
  const { databaseV4 } = await import('../core/db-instance');
  return databaseV4;
}

// ===== INDEX CREATION =====

/**
 * Initialize new payment system indexes
 */
export async function createNewPaymentIndexes(): Promise<void> {
  try {
    console.log('🔧 Creating new payment system indexes');

    const databaseV4 = await getDatabase();

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Create critical indexes for performance
    console.log('🔧 Creating performance indexes for new payment system');

    // Index for staff balance queries
    await databaseV4.createIndex({
      index: { fields: ['type', 'staffId', 'balanceType'] }
    });

    await databaseV4.createIndex({
      index: { fields: ['type', 'staffId', 'isUsed'] }
    });

    await databaseV4.createIndex({
      index: { fields: ['type', 'staffId', 'date'] }
    });

    // Index for payment snapshot queries
    await databaseV4.createIndex({
      index: { fields: ['type', 'staffId', 'paymentDate'] }
    });

    await databaseV4.createIndex({
      index: { fields: ['type', 'paymentDate'] }
    });

    console.log('✅ New payment system indexes created successfully');
  } catch (error) {
    console.error('❌ Error creating new payment system indexes:', error);
    throw error;
  }
}

// ===== STAFF BALANCE OPERATIONS =====

/**
 * Create a staff balance document
 */
export async function createStaffBalance(balanceData: Omit<StaffBalanceDocument, '_id' | '_rev'>): Promise<StaffBalanceDocument> {
  try {
    console.log(`💰 Creating staff balance: ${balanceData.balanceType} for ${balanceData.staffId}`);

    const databaseV4 = await getDatabase();

    // Check database initialization
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = await databaseV4.putDoc(balanceData);

    return {
      ...balanceData,
      _id: result.id,
      _rev: result.rev
    } as StaffBalanceDocument;
  } catch (error) {
    console.error('❌ Error creating staff balance:', error);
    throw error;
  }
}

/**
 * Get staff balance by ID
 */
export async function getStaffBalance(balanceId: string): Promise<StaffBalanceDocument | null> {
  try {
    const databaseV4 = await getDatabase();
    const doc = await databaseV4.getDoc(balanceId);
    return doc as StaffBalanceDocument;
  } catch (error) {
    if ((error as any).status === 404) {
      return null;
    }
    console.error('❌ Error getting staff balance:', error);
    throw error;
  }
}

/**
 * Update staff balance document
 */
export async function updateStaffBalance(balance: StaffBalanceDocument): Promise<StaffBalanceDocument> {
  try {
    console.log(`🔄 Updating staff balance: ${balance._id}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.putDoc(balance);

    return {
      ...balance,
      _rev: result.rev
    };
  } catch (error) {
    console.error('❌ Error updating staff balance:', error);
    throw error;
  }
}

/**
 * Delete staff balance document
 */
export async function deleteStaffBalance(balanceId: string): Promise<void> {
  try {
    console.log(`🗑️ Deleting staff balance: ${balanceId}`);

    const balance = await getStaffBalance(balanceId);
    if (!balance) {
      throw new Error(`Staff balance ${balanceId} not found`);
    }

    const databaseV4 = await getDatabase();
    await databaseV4.removeDoc(balance);
    console.log(`✅ Staff balance ${balanceId} deleted successfully`);
  } catch (error) {
    console.error('❌ Error deleting staff balance:', error);
    throw error;
  }
}

/**
 * Get all staff balances for a staff member
 */
export async function getStaffBalances(staffId: string): Promise<StaffBalanceDocument[]> {
  try {
    console.log(`📋 Getting all balances for staff: ${staffId}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { date: 'desc' }]
    });

    return result.docs as StaffBalanceDocument[];
  } catch (error) {
    console.error('❌ Error getting staff balances:', error);
    throw error;
  }
}

/**
 * Get unused staff balances for a staff member
 */
export async function getUnusedStaffBalances(staffId: string): Promise<StaffBalanceDocument[]> {
  try {
    console.log(`📋 Getting unused balances for staff: ${staffId}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        isUsed: false
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { date: 'desc' }]
    });

    return result.docs as StaffBalanceDocument[];
  } catch (error) {
    console.error('❌ Error getting unused staff balances:', error);
    throw error;
  }
}

/**
 * Get staff balances by type
 */
export async function getStaffBalancesByType(staffId: string, balanceType: 'ADVANCE' | 'DEDUCTION' | 'BONUS'): Promise<StaffBalanceDocument[]> {
  try {
    console.log(`📋 Getting ${balanceType} balances for staff: ${staffId}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        balanceType: balanceType
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { balanceType: 'asc' }, { date: 'desc' }]
    });

    return result.docs as StaffBalanceDocument[];
  } catch (error) {
    console.error('❌ Error getting staff balances by type:', error);
    throw error;
  }
}

// ===== PAYMENT SNAPSHOT OPERATIONS =====

/**
 * Create a payment snapshot document
 */
export async function createPaymentSnapshot(snapshotData: Omit<PaymentSnapshotDocument, '_id' | '_rev'>): Promise<PaymentSnapshotDocument> {
  try {
    console.log(`💳 Creating payment snapshot for staff: ${snapshotData.staffId}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.putDoc(snapshotData);
    
    return {
      ...snapshotData,
      _id: result.id,
      _rev: result.rev
    } as PaymentSnapshotDocument;
  } catch (error) {
    console.error('❌ Error creating payment snapshot:', error);
    throw error;
  }
}

/**
 * Get payment snapshot by ID
 */
export async function getPaymentSnapshot(snapshotId: string): Promise<PaymentSnapshotDocument | null> {
  try {
    const databaseV4 = await getDatabase();
    const doc = await databaseV4.getDoc(snapshotId);
    return doc as PaymentSnapshotDocument;
  } catch (error) {
    if ((error as any).status === 404) {
      return null;
    }
    console.error('❌ Error getting payment snapshot:', error);
    throw error;
  }
}

/**
 * Get all payment snapshots for a staff member
 */
export async function getStaffPaymentSnapshots(staffId: string): Promise<PaymentSnapshotDocument[]> {
  try {
    console.log(`📋 Getting payment snapshots for staff: ${staffId}`);

    const databaseV4 = await getDatabase();

    const result = await databaseV4.findDocs({
      selector: {
        type: 'payment_snapshot',
        staffId: staffId
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { paymentDate: 'desc' }]
    });

    return result.docs as PaymentSnapshotDocument[];
  } catch (error) {
    console.error('❌ Error getting staff payment snapshots:', error);
    throw error;
  }
}

/**
 * Get payment snapshots by date range
 */
export async function getPaymentSnapshotsByDateRange(
  startDate: string,
  endDate: string
): Promise<PaymentSnapshotDocument[]> {
  try {
    console.log(`📋 Getting payment snapshots from ${startDate} to ${endDate}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.findDocs({
      selector: {
        type: 'payment_snapshot',
        paymentDate: {
          $gte: startDate,
          $lte: endDate
        }
      },
      sort: [{ type: 'asc' }, { paymentDate: 'desc' }]
    });

    return result.docs as PaymentSnapshotDocument[];
  } catch (error) {
    console.error('❌ Error getting payment snapshots by date range:', error);
    throw error;
  }
}

/**
 * Update payment snapshot document
 */
export async function updatePaymentSnapshot(snapshot: PaymentSnapshotDocument): Promise<PaymentSnapshotDocument> {
  try {
    console.log(`🔄 Updating payment snapshot: ${snapshot._id}`);

    const databaseV4 = await getDatabase();
    const result = await databaseV4.putDoc(snapshot);
    
    return {
      ...snapshot,
      _rev: result.rev
    };
  } catch (error) {
    console.error('❌ Error updating payment snapshot:', error);
    throw error;
  }
}

/**
 * Delete payment snapshot document
 */
export async function deletePaymentSnapshot(snapshotId: string): Promise<void> {
  try {
    console.log(`🗑️ Deleting payment snapshot: ${snapshotId}`);

    const snapshot = await getPaymentSnapshot(snapshotId);
    if (!snapshot) {
      throw new Error(`Payment snapshot ${snapshotId} not found`);
    }

    const databaseV4 = await getDatabase();
    await databaseV4.removeDoc(snapshot);
    console.log(`✅ Payment snapshot ${snapshotId} deleted successfully`);
  } catch (error) {
    console.error('❌ Error deleting payment snapshot:', error);
    throw error;
  }
}

// ===== UTILITY FUNCTIONS =====

/**
 * Mark balance entries as used in a payment
 */
export async function markBalancesAsUsed(
  balanceIds: string[],
  paymentSnapshotId: string
): Promise<void> {
  try {
    console.log(`🔄 Marking ${balanceIds.length} balances as used in payment ${paymentSnapshotId}`);

    const usedDate = new Date().toISOString();

    for (const balanceId of balanceIds) {
      const balance = await getStaffBalance(balanceId);

      if (!balance) {
        console.warn(`⚠️ Balance ${balanceId} not found`);
        continue;
      }

      if (balance.isUsed) {
        console.warn(`⚠️ Balance ${balanceId} is already marked as used`);
        continue;
      }

      const updatedBalance: StaffBalanceDocument = {
        ...balance,
        isUsed: true,
        usedInPaymentId: paymentSnapshotId,
        usedDate: usedDate,
        updatedAt: usedDate
      };

      await updateStaffBalance(updatedBalance);
    }

    console.log(`✅ Successfully marked ${balanceIds.length} balances as used`);
  } catch (error) {
    console.error(`❌ Error marking balances as used:`, error);
    throw error;
  }
}
