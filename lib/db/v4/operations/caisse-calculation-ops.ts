/**
 * Caisse Calculation Operations for V4 Database
 * 
 * 🔧 IMPORTANT FIX: Double Counting Issue Resolved
 * 
 * Previously, the system was adding both:
 * - Orders (when paid) 
 * - Cash Transactions (created from paid orders)
 * 
 * This caused double counting because every paid order creates a cash transaction.
 * 
 * SOLUTION: Use ONLY cash transactions for totals.
 * - Orders are kept for reference and staff tracking
 * - Cash transactions are the single source of truth for money flow
 * - This includes: order payments (sales), manual entries, expenses, etc.
 */

"use client";

import { databaseV4 } from '../core/db-instance';
import {
  CaisseCalculationDocument,
  OrderSummary,
  DEFAULT_CAISSE_CALCULATION_DOCUMENT
} from '../schemas/caisse-calculation-schema';
import { getAllOrders } from './order-ops';
import { getAllCashTransactions } from './cash-ops';
import { OrderDocument } from '../schemas/order-schema';
import { 
  retryWithConflictResolution, 
  safeUpdateDocument 
} from '../core/conflict-resolution';

/**
 * Get all caisse calculations
 */
export async function getAllCaisseCalculations(): Promise<CaisseCalculationDocument[]> {
  try {
    console.log('[getAllCaisseCalculations] Fetching all caisse calculations');
    
    // First try to get all caisse calculations without sorting
    const result = await databaseV4.findDocs({
      selector: {
        type: 'caisse_calculation'
      }
    });

    console.log(`[getAllCaisseCalculations] Found ${result.docs.length} calculations`);
    
    // Sort manually in JavaScript since we don't have an index yet
    const sortedDocs = (result.docs as CaisseCalculationDocument[]).sort((a, b) => 
      new Date(b.calculatedAt).getTime() - new Date(a.calculatedAt).getTime()
    );
    
    return sortedDocs;
  } catch (error) {
    console.error('[getAllCaisseCalculations] Error:', error);
    throw error;
  }
}

/**
 * Get the last caisse calculation
 */
export async function getLastCaisseCalculation(): Promise<CaisseCalculationDocument | null> {
  try {
    console.log('[getLastCaisseCalculation] Fetching last calculation');
    
    const calculations = await getAllCaisseCalculations();
    return calculations.length > 0 ? calculations[0] : null;
  } catch (error) {
    console.error('[getLastCaisseCalculation] Error:', error);
    throw error;
  }
}

/**
 * 🔧 FIXED: Get UNIFIED caisse data (orders + cash transactions)
 * This fixes the schema inconsistency by using ONLY cash transactions for totals
 * Orders are kept for reference but not added to avoid double counting
 */
export interface StaffCollectionTotal {
  staffId: string;
  staffName: string;
  totalCollected: number;
  transactionCount: number;
  orderCount: number;
  deliveryCollections: number;
  manualTransactions: number;
}

export async function getUnifiedCaisseDataSinceLastCalculation(): Promise<{
  orders: OrderDocument[];
  cashTransactions: any[];
  periodStart: string;
  totalExpectedFromOrders: number;
  totalFromCashTransactions: number;
  totalExpected: number;
  mainStaffName: string;
  mainStaffOrderCount: number;
  staffCollectionTotals: StaffCollectionTotal[];
}> {
  try {
    console.log('[getUnifiedCaisseDataSinceLastCalculation] 🔧 Getting UNIFIED caisse data (FIXED: no double counting)');
    
    // Get last calculation
    const lastCalculation = await getLastCaisseCalculation();
    const periodStart = lastCalculation ? lastCalculation.calculatedAt : '1970-01-01T00:00:00.000Z';
    
    console.log(`[getUnifiedCaisseDataSinceLastCalculation] Period start: ${periodStart}`);
    
    // Get all orders
    const allOrders = await getAllOrders();
    
    // Filter orders since last calculation that are paid
    const ordersSinceLastCalc = allOrders.filter(order => {
      const orderTime = new Date(order.createdAt).getTime();
      const periodStartTime = new Date(periodStart).getTime();
      const isPaid = order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid';
      
      return orderTime > periodStartTime && isPaid;
    });
    
    // Get all cash transactions since last calculation
    const allCashTransactions = await getAllCashTransactions();
    const cashTransactionsSinceLastCalc = allCashTransactions.filter(tx => {
      const txTime = new Date(tx.time).getTime();
      const periodStartTime = new Date(periodStart).getTime();
      
      return txTime > periodStartTime;
    });
    
    console.log(`[getUnifiedCaisseDataSinceLastCalculation] Found ${ordersSinceLastCalc.length} paid orders and ${cashTransactionsSinceLastCalc.length} cash transactions since last calculation`);
    
    // Calculate total expected amount from orders (for reference only)
    const totalExpectedFromOrders = ordersSinceLastCalc.reduce((sum, order) => {
      return sum + (order.paymentDetails?.amountPaid || order.total);
    }, 0);
    
    // Calculate total from cash transactions (this is the REAL total)
    const totalFromCashTransactions = cashTransactionsSinceLastCalc.reduce((sum, tx) => {
      return sum + (tx.amount || 0);
    }, 0);
    
    // 🔧 FIXED: Use ONLY cash transactions for total (no double counting)
    // Orders already create transactions when paid, so we don't add them again
    const totalExpected = totalFromCashTransactions;
    
    // Find main staff member (who created the most orders)
    const staffCounts: { [staffName: string]: number } = {};
    ordersSinceLastCalc.forEach(order => {
      const staffName = order.createdByName || 'Personnel Inconnu';
      staffCounts[staffName] = (staffCounts[staffName] || 0) + 1;
    });
    
    // Also count staff from cash transactions
    cashTransactionsSinceLastCalc.forEach(tx => {
      const staffName = tx.performedBy || 'Personnel Inconnu';
      staffCounts[staffName] = (staffCounts[staffName] || 0) + 1;
    });
    
    let mainStaffName = 'Aucun personnel';
    let mainStaffOrderCount = 0;
    
    Object.entries(staffCounts).forEach(([name, count]) => {
      if (count > mainStaffOrderCount) {
        mainStaffName = name;
        mainStaffOrderCount = count;
      }
    });

    // 🚀 NEW: Calculate staff collection totals
    const staffCollectionMap = new Map<string, StaffCollectionTotal>();
    
    // Process orders to get staff collection data
    ordersSinceLastCalc.forEach(order => {
      const staffName = order.createdByName || 'Personnel Inconnu';
      const staffId = order.createdBy || 'unknown';
      const amount = order.paymentDetails?.amountPaid || order.total;
      
      if (!staffCollectionMap.has(staffId)) {
        staffCollectionMap.set(staffId, {
          staffId,
          staffName,
          totalCollected: 0,
          transactionCount: 0,
          orderCount: 0,
          deliveryCollections: 0,
          manualTransactions: 0
        });
      }
      
      const staffData = staffCollectionMap.get(staffId)!;
      staffData.orderCount += 1;
    });
    
    // Process cash transactions to get additional staff collection data
    cashTransactionsSinceLastCalc.forEach(tx => {
      const staffName = tx.performedBy || 'Personnel Inconnu';
      // Try to extract staffId from metadata or use performedBy as fallback
      const staffId = (tx.metadata?.staffId) || tx.performedBy || 'unknown';
      const amount = tx.amount || 0;
      
      if (!staffCollectionMap.has(staffId)) {
        staffCollectionMap.set(staffId, {
          staffId,
          staffName,
          totalCollected: 0,
          transactionCount: 0,
          orderCount: 0,
          deliveryCollections: 0,
          manualTransactions: 0
        });
      }
      
      const staffData = staffCollectionMap.get(staffId)!;
      staffData.transactionCount += 1;
      
      // Only add positive amounts to totalCollected (money coming in)
      if (amount > 0) {
        staffData.totalCollected += amount;
      }
      
      // Categorize transaction types (skip old delivery_collection transactions)
      if (tx.metadata?.transactionCategory === 'delivery_collection') {
        // Skip old delivery collection transactions - system no longer creates these
      } else if (tx.type === 'manual_in' || tx.type === 'manual_out') {
        staffData.manualTransactions += amount;
      }
    });
    
    const staffCollectionTotals = Array.from(staffCollectionMap.values())
      .filter(staff => staff.totalCollected > 0 || staff.transactionCount > 0)
      .sort((a, b) => b.totalCollected - a.totalCollected);
    
    console.log(`[getUnifiedCaisseDataSinceLastCalculation] 🔧 FIXED TOTALS (no double counting):`, {
      totalExpectedFromOrders: `${totalExpectedFromOrders} (reference only)`,
      totalFromCashTransactions: `${totalFromCashTransactions} (actual total)`,
      totalExpected: `${totalExpected} (= cash transactions only)`,
      mainStaffName,
      mainStaffOrderCount,
      staffCollectionTotals: staffCollectionTotals.length
    });
    
    return {
      orders: ordersSinceLastCalc,
      cashTransactions: cashTransactionsSinceLastCalc,
      periodStart,
      totalExpectedFromOrders,
      totalFromCashTransactions,
      totalExpected,
      mainStaffName,
      mainStaffOrderCount,
      staffCollectionTotals
    };
  } catch (error) {
    console.error('[getUnifiedCaisseDataSinceLastCalculation] Error:', error);
    throw error;
  }
}

/**
 * Get orders since last calculation (LEGACY - kept for compatibility)
 */
export async function getOrdersSinceLastCalculation(): Promise<{
  orders: OrderDocument[];
  periodStart: string;
  totalExpected: number;
  mainStaffName: string;
  mainStaffOrderCount: number;
}> {
  try {
    console.log('[getOrdersSinceLastCalculation] Analyzing orders since last calculation');
    
    // Get last calculation
    const lastCalculation = await getLastCaisseCalculation();
    const periodStart = lastCalculation ? lastCalculation.calculatedAt : '1970-01-01T00:00:00.000Z';
    
    console.log(`[getOrdersSinceLastCalculation] Period start: ${periodStart}`);
    
    // Get all orders
    const allOrders = await getAllOrders();
    
    // Filter orders since last calculation that are paid
    const ordersSinceLastCalc = allOrders.filter(order => {
      const orderTime = new Date(order.createdAt).getTime();
      const periodStartTime = new Date(periodStart).getTime();
      const isPaid = order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid';
      
      return orderTime > periodStartTime && isPaid;
    });
    
    console.log(`[getOrdersSinceLastCalculation] Found ${ordersSinceLastCalc.length} paid orders since last calculation`);
    
    // Calculate total expected amount
    const totalExpected = ordersSinceLastCalc.reduce((sum, order) => {
      return sum + (order.paymentDetails?.amountPaid || order.total);
    }, 0);
    
    // Find main staff member (who created the most orders)
    const staffCounts: { [staffName: string]: number } = {};
    ordersSinceLastCalc.forEach(order => {
      const staffName = order.createdByName || 'Personnel Inconnu';
      staffCounts[staffName] = (staffCounts[staffName] || 0) + 1;
    });
    
    let mainStaffName = 'Aucun personnel';
    let mainStaffOrderCount = 0;
    
    Object.entries(staffCounts).forEach(([name, count]) => {
      if (count > mainStaffOrderCount) {
        mainStaffName = name;
        mainStaffOrderCount = count;
      }
    });
    
    console.log(`[getOrdersSinceLastCalculation] Total expected: ${totalExpected}, Main staff: ${mainStaffName} (${mainStaffOrderCount} orders)`);
    
    return {
      orders: ordersSinceLastCalc,
      periodStart,
      totalExpected,
      mainStaffName,
      mainStaffOrderCount
    };
  } catch (error) {
    console.error('[getOrdersSinceLastCalculation] Error:', error);
    throw error;
  }
}

/**
 * Create a new caisse calculation with enhanced delivery collection tracking
 */
export async function createCaisseCalculation(data: {
  calculatedBy: string;
  calculatedByName: string;
  countedAmount: number;
  notes?: string;
}): Promise<CaisseCalculationDocument> {
  try {
    console.log('[createCaisseCalculation] Creating new calculation:', data);
    
    const now = new Date().toISOString();
    const calculationId = `caisse_calculation:${Date.now()}`;
    
    // Get unified analysis since last calculation (orders + cash transactions)
    const analysis = await getUnifiedCaisseDataSinceLastCalculation();
    
    // Calculate variance
    const variance = data.countedAmount - analysis.totalExpected;
    
    // Create order summaries
    const orderSummaries: OrderSummary[] = analysis.orders.map(order => ({
      orderId: order._id,
      amount: order.paymentDetails?.amountPaid || order.total,
      createdAt: order.createdAt,
      createdBy: order.createdBy || 'unknown',
      createdByName: order.createdByName || 'Personnel Inconnu',
      paymentMethod: order.paymentMethod || 'unknown'
    }));
    
    // OLD: Extract delivery collections from cash transactions (REMOVED - now using collection events)
    const deliveryCollections = []; // Empty array - old system removed
    
    // Create calculation document
    const calculation: CaisseCalculationDocument = {
      ...DEFAULT_CAISSE_CALCULATION_DOCUMENT,
      _id: calculationId,
      createdAt: now,
      updatedAt: now,
      calculatedAt: now,
      calculatedBy: data.calculatedBy,
      calculatedByName: data.calculatedByName,
      periodStart: analysis.periodStart,
      periodEnd: now,
      expectedAmount: analysis.totalExpected,
      countedAmount: data.countedAmount,
      variance,
      notes: data.notes,
      orderSummaries,
      // 🚀 NEW: Include delivery collection summaries
      deliveryCollections: deliveryCollections.length > 0 ? deliveryCollections : undefined
    };
    
    console.log('[createCaisseCalculation] Saving calculation document');
    
    // Save to database with conflict resolution
    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(calculation);
    });
    
    console.log(`[createCaisseCalculation] Calculation created successfully: ${calculationId}`);
    
    return calculation;
  } catch (error) {
    console.error('[createCaisseCalculation] Error:', error);
    throw error;
  }
}

/**
 * Get a specific caisse calculation by ID
 */
export async function getCaisseCalculation(calculationId: string): Promise<CaisseCalculationDocument> {
  try {
    console.log(`[getCaisseCalculation] Fetching calculation: ${calculationId}`);
    
    const doc = await databaseV4.getDoc(calculationId);
    
    if (!doc || (doc as any).type !== 'caisse_calculation') {
      throw new Error(`Caisse calculation not found: ${calculationId}`);
    }
    
    return doc as CaisseCalculationDocument;
  } catch (error) {
    console.error(`[getCaisseCalculation] Error fetching calculation ${calculationId}:`, error);
    throw error;
  }
}

/**
 * Update a caisse calculation
 */
export async function updateCaisseCalculation(
  calculationId: string,
  updates: Partial<CaisseCalculationDocument>
): Promise<CaisseCalculationDocument> {
  try {
    console.log(`[updateCaisseCalculation] Updating calculation: ${calculationId}`);
    
    // Get existing calculation
    const existingCalculation = await getCaisseCalculation(calculationId);
    
    // Create updated calculation
    const updatedCalculation: CaisseCalculationDocument = {
      ...existingCalculation,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    // Save to database
    await databaseV4.putDoc(updatedCalculation);
    
    console.log(`[updateCaisseCalculation] Calculation updated successfully: ${calculationId}`);
    
    return updatedCalculation;
  } catch (error) {
    console.error(`[updateCaisseCalculation] Error updating calculation ${calculationId}:`, error);
    throw error;
  }
} 