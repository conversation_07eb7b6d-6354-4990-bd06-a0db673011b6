// knowledge: v4 persistent expense CRUD
import { databaseV4 } from '../core/db-instance';
import { createCashTransaction } from './cash-ops';

const EXPENSES_DOC_ID = 'expenses';

async function getExpensesDoc() {
  try {
    return await databaseV4.getDoc(EXPENSES_DOC_ID) as any;
  } catch (err: any) {
    if (err.status === 404) {
      // Create default expenses doc with fresh timestamps
      const now = new Date().toISOString();
      const doc = {
        _id: EXPENSES_DOC_ID,
        type: 'expense_document',
        schemaVersion: 'v4.0',
        createdAt: now,
        updatedAt: now,
        expenses: []
      };
      await databaseV4.putDoc(doc as any);
      return doc as any;
    }
    throw err;
  }
}

export async function getAllExpenses() {
  const doc = await getExpensesDoc();
  return doc.expenses || [];
}

export async function createExpense(expense: any) {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const doc = await getExpensesDoc();
      const id = `expense_${Date.now()}_${Math.floor(Math.random()*10000)}`;
      const newExpense = {
        id,
        ...expense,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      doc.expenses = [newExpense, ...(doc.expenses || [])];
      doc.updatedAt = new Date().toISOString();
      await databaseV4.putDoc(doc);
      // 🚨 No cash transaction for any expense! Only manual cashout can affect caisse.
      return newExpense;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[createExpense] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      throw error;
    }
  }
  
  throw lastError;
}

export async function updateExpense(id: string, updates: any) {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const doc = await getExpensesDoc();
      const idx = (doc.expenses || []).findIndex((e: any) => e.id === id);
      
      if (idx === -1) {
        throw new Error('Expense not found');
      }
      
      doc.expenses[idx] = {
        ...doc.expenses[idx],
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      doc.updatedAt = new Date().toISOString();
      await databaseV4.putDoc(doc);
      return doc.expenses[idx];
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[updateExpense] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      throw error;
    }
  }
  
  throw lastError;
}

export async function deleteExpense(id: string) {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const doc = await getExpensesDoc();
      const idx = (doc.expenses || []).findIndex((e: any) => e.id === id);
      
      if (idx === -1) {
        throw new Error('Expense not found');
      }
      
      doc.expenses.splice(idx, 1);
      doc.updatedAt = new Date().toISOString();
      await databaseV4.putDoc(doc);
      return true;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[deleteExpense] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      throw error;
    }
  }
  
  throw lastError;
}
// knowledge: v4 persistent expense CRUD end 