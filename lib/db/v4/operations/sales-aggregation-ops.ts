/**
 * 🚀 Sales Aggregation Operations
 * 
 * Core operations for managing pre-computed sales aggregations.
 * These operations provide incremental updates and conflict resolution
 * for lightning-fast dashboard performance.
 */

import { databaseV4 } from '../core/db-instance';
import { OrderDocument } from '../schemas/order-schema';
import {
  DailySalesAggregation,
  WeeklySalesAggregation,
  MonthlySalesAggregation,
  ItemPerformanceAggregation,
  RealTimeDashboardAggregation,
  SalesAggregationDocument,
  generateDailyAggregationId,
  generateWeeklyAggregationId,
  generateMonthlyAggregationId,
  generateItemPerformanceId,
  generateRealTimeDashboardId,
  DEFAULT_DAILY_AGGREGATION,
  DEFAULT_REALTIME_DASHBOARD
} from '../schemas/sales-aggregation-schemas';
// 🚀 Import the new conflict resolution utilities
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument,
  safeEnsureDocument
} from '../core/conflict-resolution';

/**
 * 🔥 Core Aggregation Engine
 * 
 * This is the heart of the sales aggregation system. When an order is created,
 * updated, or deleted, this function incrementally updates all relevant aggregations.
 */
export async function updateAggregationsForOrder(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  previousOrder?: OrderDocument
): Promise<void> {
  console.log(`[updateAggregationsForOrder] Processing ${operation} for order ${order._id}`);
  
  try {
    // Extract date information from order
    const orderDate = new Date(order.createdAt);
    const dateStr = orderDate.toISOString().split('T')[0]; // YYYY-MM-DD
    const hour = orderDate.getHours();
    
    // Update all aggregation levels in parallel for performance
    await Promise.all([
      updateDailyAggregation(order, operation, dateStr, hour, previousOrder),
      updateWeeklyAggregation(order, operation, dateStr, previousOrder),
      updateMonthlyAggregation(order, operation, dateStr, previousOrder),
      updateItemPerformanceAggregations(order, operation, previousOrder),
      updateRealTimeDashboard(order, operation, previousOrder)
    ]);
    
    console.log(`✅ [updateAggregationsForOrder] Successfully updated aggregations for order ${order._id}`);
  } catch (error) {
    console.error(`❌ [updateAggregationsForOrder] Failed to update aggregations for order ${order._id}:`, error);
    throw error;
  }
}

/**
 * Update daily sales aggregation with conflict resolution
 */
async function updateDailyAggregation(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  dateStr: string,
  hour: number,
  previousOrder?: OrderDocument
): Promise<void> {
  const aggregationId = generateDailyAggregationId(dateStr);
  
  await retryWithConflictResolution(async () => {
    // Get or create daily aggregation with conflict resolution
    const dailyAgg = await safeEnsureDocument<DailySalesAggregation>(
      aggregationId,
      () => ({
        ...DEFAULT_DAILY_AGGREGATION,
        _id: aggregationId,
        date: dateStr
      }),
      `getDailyAggregation(${dateStr})`
    );
    
    // Apply the operation
    if (operation === 'create' || operation === 'update') {
      // Remove previous order's impact if this is an update
      if (operation === 'update' && previousOrder) {
        applyOrderToDaily(dailyAgg, previousOrder, hour, 'subtract');
      }
      
      // Add current order's impact
      applyOrderToDaily(dailyAgg, order, hour, 'add');
    } else if (operation === 'delete') {
      // Remove order's impact
      applyOrderToDaily(dailyAgg, order, hour, 'subtract');
    }
    
    // Update metadata
    dailyAgg.updatedAt = new Date().toISOString();
    dailyAgg.version += 1;
    dailyAgg.lastOrderProcessed = order._id;
    
    // Save the updated aggregation with conflict resolution
    await databaseV4.putDoc(dailyAgg);
  }, 3, `updateDailyAggregation(${dateStr})`);
}

/**
 * Apply order changes to daily aggregation
 */
function applyOrderToDaily(
  dailyAgg: DailySalesAggregation,
  order: OrderDocument,
  hour: number,
  operation: 'add' | 'subtract'
): void {
  const multiplier = operation === 'add' ? 1 : -1;
  
  // Only process completed orders for revenue calculations
  if (order.status === 'completed') {
    // Core metrics
    dailyAgg.totalRevenue += order.total * multiplier;
    dailyAgg.totalOrders += 1 * multiplier;
    dailyAgg.totalItems += order.items.reduce((sum, item) => sum + item.quantity, 0) * multiplier;
    
    // Cost & Profit metrics
    const orderCogs = order.totalCogs || 0;
    const orderProfit = order.total - orderCogs;
    dailyAgg.totalCogs += orderCogs * multiplier;
    dailyAgg.grossProfit += orderProfit * multiplier;
    
    // Recalculate derived metrics
    dailyAgg.averageOrderValue = dailyAgg.totalOrders > 0 ? dailyAgg.totalRevenue / dailyAgg.totalOrders : 0;
    dailyAgg.profitMargin = dailyAgg.totalRevenue > 0 ? (dailyAgg.grossProfit / dailyAgg.totalRevenue) * 100 : 0;
    
    // Order type breakdown
    const orderType = order.orderType || 'dine-in';
    if (dailyAgg.orderTypeBreakdown[orderType]) {
      dailyAgg.orderTypeBreakdown[orderType].count += 1 * multiplier;
      dailyAgg.orderTypeBreakdown[orderType].revenue += order.total * multiplier;
    }
    
    // Payment method breakdown
    const paymentMethod = order.paymentMethod || 'cash';
    if (dailyAgg.paymentMethodBreakdown[paymentMethod]) {
      dailyAgg.paymentMethodBreakdown[paymentMethod].count += 1 * multiplier;
      dailyAgg.paymentMethodBreakdown[paymentMethod].amount += order.total * multiplier;
    }
    
    // Hourly breakdown
    if (hour >= 0 && hour < 24) {
      dailyAgg.hourlyBreakdown[hour].orders += 1 * multiplier;
      dailyAgg.hourlyBreakdown[hour].revenue += order.total * multiplier;
      dailyAgg.hourlyBreakdown[hour].averageOrderValue = 
        dailyAgg.hourlyBreakdown[hour].orders > 0 
          ? dailyAgg.hourlyBreakdown[hour].revenue / dailyAgg.hourlyBreakdown[hour].orders 
          : 0;
    }
    
    // Status breakdown - completed
    dailyAgg.statusBreakdown.completed.count += 1 * multiplier;
    dailyAgg.statusBreakdown.completed.revenue += order.total * multiplier;
    
    // Update top items (simplified - full implementation would maintain sorted list)
    updateTopItemsInDaily(dailyAgg, order, multiplier);
  } else if (order.status === 'cancelled') {
    // Track cancelled orders
    dailyAgg.statusBreakdown.cancelled.count += 1 * multiplier;
    dailyAgg.statusBreakdown.cancelled.lostRevenue += order.total * multiplier;
  } else {
    // Track pending orders
    dailyAgg.statusBreakdown.pending.count += 1 * multiplier;
    dailyAgg.statusBreakdown.pending.pendingRevenue += order.total * multiplier;
  }
}

/**
 * Update top items in daily aggregation (simplified version)
 */
function updateTopItemsInDaily(
  dailyAgg: DailySalesAggregation,
  order: OrderDocument,
  multiplier: number
): void {
  // For each item in the order, update or add to top items
  for (const item of order.items) {
    let topItem = dailyAgg.topItems.find(ti => ti.menuItemId === item.menuItemId);
    
    if (!topItem) {
      // Add new item if it doesn't exist
      topItem = {
        menuItemId: item.menuItemId,
        name: item.name,
        category: item.category || 'Unknown',
        quantitySold: 0,
        revenue: 0,
        profit: 0
      };
      dailyAgg.topItems.push(topItem);
    }
    
    // Update metrics
    const itemRevenue = item.price * item.quantity;
    const itemCogs = (item.cogs || 0) * item.quantity;
    const itemProfit = itemRevenue - itemCogs;
    
    topItem.quantitySold += item.quantity * multiplier;
    topItem.revenue += itemRevenue * multiplier;
    topItem.profit += itemProfit * multiplier;
  }
  
  // Keep only top 20 items and sort by revenue
  dailyAgg.topItems = dailyAgg.topItems
    .filter(item => item.quantitySold > 0) // Remove items with zero quantity
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 20);
}

/**
 * Update weekly aggregation (simplified implementation)
 */
async function updateWeeklyAggregation(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  dateStr: string,
  previousOrder?: OrderDocument
): Promise<void> {
  // Get Monday of the week for the order date
  const orderDate = new Date(dateStr);
  const dayOfWeek = orderDate.getDay();
  const mondayDate = new Date(orderDate);
  mondayDate.setDate(orderDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
  const weekStartDate = mondayDate.toISOString().split('T')[0];
  
  const aggregationId = generateWeeklyAggregationId(weekStartDate);
  
  try {
    // Get or create weekly aggregation
    let weeklyAgg: WeeklySalesAggregation;
    try {
      weeklyAgg = await databaseV4.getDoc<WeeklySalesAggregation>(aggregationId);
    } catch (error) {
      // Create new weekly aggregation
      const sundayDate = new Date(mondayDate);
      sundayDate.setDate(mondayDate.getDate() + 6);
      
      weeklyAgg = {
        type: 'weekly_sales_aggregation',
        schemaVersion: 'v4.0',
        _id: aggregationId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1,
        weekStartDate,
        weekEndDate: sundayDate.toISOString().split('T')[0],
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        grossProfit: 0,
        profitMargin: 0,
        dailyBreakdown: [],
        previousWeekComparison: {
          revenueGrowth: 0,
          orderGrowth: 0,
          profitGrowth: 0
        },
        bestDay: {
          date: '',
          dayOfWeek: '',
          revenue: 0,
          orders: 0
        }
      };
    }
    
    // Apply order changes (simplified)
    if (order.status === 'completed') {
      const multiplier = operation === 'delete' ? -1 : 1;
      if (operation === 'update' && previousOrder?.status === 'completed') {
        // Remove previous order impact first
        weeklyAgg.totalRevenue -= previousOrder.total;
        weeklyAgg.totalOrders -= 1;
        weeklyAgg.grossProfit -= (previousOrder.total - (previousOrder.totalCogs || 0));
      }
      
      weeklyAgg.totalRevenue += order.total * multiplier;
      weeklyAgg.totalOrders += 1 * multiplier;
      weeklyAgg.grossProfit += (order.total - (order.totalCogs || 0)) * multiplier;
      
      // Recalculate derived metrics
      weeklyAgg.averageOrderValue = weeklyAgg.totalOrders > 0 ? weeklyAgg.totalRevenue / weeklyAgg.totalOrders : 0;
      weeklyAgg.profitMargin = weeklyAgg.totalRevenue > 0 ? (weeklyAgg.grossProfit / weeklyAgg.totalRevenue) * 100 : 0;
    }
    
    // Update metadata
    weeklyAgg.updatedAt = new Date().toISOString();
    weeklyAgg.version += 1;
    
    // Save the updated aggregation
    await databaseV4.putDoc(weeklyAgg);
    
  } catch (error) {
    console.error(`❌ [updateWeeklyAggregation] Failed for week ${weekStartDate}:`, error);
    throw error;
  }
}

/**
 * Update monthly aggregation (simplified implementation)
 */
async function updateMonthlyAggregation(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  dateStr: string,
  previousOrder?: OrderDocument
): Promise<void> {
  const orderDate = new Date(dateStr);
  const year = orderDate.getFullYear();
  const month = orderDate.getMonth() + 1;
  
  const aggregationId = generateMonthlyAggregationId(year, month);
  
  try {
    // Get or create monthly aggregation
    let monthlyAgg: MonthlySalesAggregation;
    try {
      monthlyAgg = await databaseV4.getDoc<MonthlySalesAggregation>(aggregationId);
    } catch (error) {
      // Create new monthly aggregation
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                         'July', 'August', 'September', 'October', 'November', 'December'];
      
      monthlyAgg = {
        type: 'monthly_sales_aggregation',
        schemaVersion: 'v4.0',
        _id: aggregationId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1,
        year,
        month,
        monthName: monthNames[month - 1],
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        grossProfit: 0,
        profitMargin: 0,
        weeklyBreakdown: [],
        previousMonthComparison: {
          revenueGrowth: 0,
          orderGrowth: 0,
          profitGrowth: 0
        },
        topCategories: []
      };
    }
    
    // Apply order changes (simplified)
    if (order.status === 'completed') {
      const multiplier = operation === 'delete' ? -1 : 1;
      if (operation === 'update' && previousOrder?.status === 'completed') {
        // Remove previous order impact first
        monthlyAgg.totalRevenue -= previousOrder.total;
        monthlyAgg.totalOrders -= 1;
        monthlyAgg.grossProfit -= (previousOrder.total - (previousOrder.totalCogs || 0));
      }
      
      monthlyAgg.totalRevenue += order.total * multiplier;
      monthlyAgg.totalOrders += 1 * multiplier;
      monthlyAgg.grossProfit += (order.total - (order.totalCogs || 0)) * multiplier;
      
      // Recalculate derived metrics
      monthlyAgg.averageOrderValue = monthlyAgg.totalOrders > 0 ? monthlyAgg.totalRevenue / monthlyAgg.totalOrders : 0;
      monthlyAgg.profitMargin = monthlyAgg.totalRevenue > 0 ? (monthlyAgg.grossProfit / monthlyAgg.totalRevenue) * 100 : 0;
    }
    
    // Update metadata
    monthlyAgg.updatedAt = new Date().toISOString();
    monthlyAgg.version += 1;
    
    // Save the updated aggregation
    await databaseV4.putDoc(monthlyAgg);
    
  } catch (error) {
    console.error(`❌ [updateMonthlyAggregation] Failed for ${year}-${month}:`, error);
    throw error;
  }
}

/**
 * Update item performance aggregations
 */
async function updateItemPerformanceAggregations(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  previousOrder?: OrderDocument
): Promise<void> {
  // Get all unique menu items from current and previous orders
  const menuItemIds = new Set<string>();
  
  if (order.items) {
    order.items.forEach(item => menuItemIds.add(item.menuItemId));
  }
  
  if (previousOrder?.items) {
    previousOrder.items.forEach(item => menuItemIds.add(item.menuItemId));
  }
  
  // Update each item's performance aggregation
  for (const menuItemId of menuItemIds) {
    await updateSingleItemPerformance(menuItemId, order, operation, previousOrder);
  }
}

/**
 * Update single item performance aggregation
 */
async function updateSingleItemPerformance(
  menuItemId: string,
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  previousOrder?: OrderDocument
): Promise<void> {
  const aggregationId = generateItemPerformanceId(menuItemId);
  
  try {
    // Get or create item performance aggregation
    let itemAgg: ItemPerformanceAggregation;
    try {
      itemAgg = await databaseV4.getDoc<ItemPerformanceAggregation>(aggregationId);
    } catch (error) {
      // Find the item details from the order
      const orderItem = order.items?.find(item => item.menuItemId === menuItemId) ||
                       previousOrder?.items?.find(item => item.menuItemId === menuItemId);
      
      if (!orderItem) return; // Skip if item not found
      
      // Create new item performance aggregation
      itemAgg = {
        type: 'item_performance_aggregation',
        schemaVersion: 'v4.0',
        _id: aggregationId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: 1,
        menuItemId,
        itemName: orderItem.name,
        category: orderItem.category || 'Unknown',
        totalQuantitySold: 0,
        totalRevenue: 0,
        totalProfit: 0,
        averagePrice: 0,
        profitMargin: 0,
        last7Days: { quantitySold: 0, revenue: 0, profit: 0 },
        last30Days: { quantitySold: 0, revenue: 0, profit: 0 },
        trend: 'stable',
        trendPercentage: 0,
        peakHours: [],
        lastSoldAt: new Date().toISOString(),
        daysSinceLastSold: 0
      };
    }
    
    // Apply changes only for completed orders
    if (order.status === 'completed') {
      // Remove previous order impact if this is an update
      if (operation === 'update' && previousOrder?.status === 'completed') {
        const prevItem = previousOrder.items?.find(item => item.menuItemId === menuItemId);
        if (prevItem) {
          const prevRevenue = prevItem.price * prevItem.quantity;
          const prevCogs = (prevItem.cogs || 0) * prevItem.quantity;
          const prevProfit = prevRevenue - prevCogs;
          
          itemAgg.totalQuantitySold -= prevItem.quantity;
          itemAgg.totalRevenue -= prevRevenue;
          itemAgg.totalProfit -= prevProfit;
        }
      }
      
      // Add current order impact
      const currentItem = order.items?.find(item => item.menuItemId === menuItemId);
      if (currentItem) {
        const multiplier = operation === 'delete' ? -1 : 1;
        const itemRevenue = currentItem.price * currentItem.quantity;
        const itemCogs = (currentItem.cogs || 0) * currentItem.quantity;
        const itemProfit = itemRevenue - itemCogs;
        
        itemAgg.totalQuantitySold += currentItem.quantity * multiplier;
        itemAgg.totalRevenue += itemRevenue * multiplier;
        itemAgg.totalProfit += itemProfit * multiplier;
        
        // Update last sold information
        if (operation !== 'delete') {
          itemAgg.lastSoldAt = order.createdAt;
          itemAgg.daysSinceLastSold = 0;
        }
      }
      
      // Recalculate derived metrics
      itemAgg.averagePrice = itemAgg.totalQuantitySold > 0 ? itemAgg.totalRevenue / itemAgg.totalQuantitySold : 0;
      itemAgg.profitMargin = itemAgg.totalRevenue > 0 ? (itemAgg.totalProfit / itemAgg.totalRevenue) * 100 : 0;
    }
    
    // Update metadata
    itemAgg.updatedAt = new Date().toISOString();
    itemAgg.version += 1;
    
    // Save the updated aggregation
    await databaseV4.putDoc(itemAgg);
    
  } catch (error) {
    console.error(`❌ [updateSingleItemPerformance] Failed for item ${menuItemId}:`, error);
    throw error;
  }
}

/**
 * Update real-time dashboard aggregation
 */
async function updateRealTimeDashboard(
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  previousOrder?: OrderDocument
): Promise<void> {
  const aggregationId = generateRealTimeDashboardId();
  
  try {
    // Get or create real-time dashboard aggregation
    let dashboardAgg: RealTimeDashboardAggregation;
    try {
      dashboardAgg = await databaseV4.getDoc<RealTimeDashboardAggregation>(aggregationId);
    } catch (error) {
      // Create new dashboard aggregation
      dashboardAgg = {
        ...DEFAULT_REALTIME_DASHBOARD,
        _id: aggregationId
      };
    }
    
    // Update current status counts
    updateCurrentStatus(dashboardAgg, order, operation, previousOrder);
    
    // Update today's metrics if order is from today
    const today = new Date().toISOString().split('T')[0];
    const orderDate = order.createdAt.split('T')[0];
    
    if (orderDate === today && order.status === 'completed') {
      const multiplier = operation === 'delete' ? -1 : 1;
      
      // Remove previous order impact if this is an update
      if (operation === 'update' && previousOrder?.status === 'completed') {
        dashboardAgg.today.revenue -= previousOrder.total;
        dashboardAgg.today.orders -= 1;
        dashboardAgg.today.profit -= (previousOrder.total - (previousOrder.totalCogs || 0));
      }
      
      // Add current order impact
      dashboardAgg.today.revenue += order.total * multiplier;
      dashboardAgg.today.orders += 1 * multiplier;
      dashboardAgg.today.profit += (order.total - (order.totalCogs || 0)) * multiplier;
      
      // Recalculate derived metrics
      dashboardAgg.today.averageOrderValue = dashboardAgg.today.orders > 0 ? dashboardAgg.today.revenue / dashboardAgg.today.orders : 0;
      dashboardAgg.today.profitMargin = dashboardAgg.today.revenue > 0 ? (dashboardAgg.today.profit / dashboardAgg.today.revenue) * 100 : 0;
      dashboardAgg.today.lastUpdated = new Date().toISOString();
    }
    
    // Update metadata
    dashboardAgg.updatedAt = new Date().toISOString();
    dashboardAgg.version += 1;
    
    // Save the updated aggregation
    await databaseV4.putDoc(dashboardAgg);
    
  } catch (error) {
    console.error(`❌ [updateRealTimeDashboard] Failed:`, error);
    throw error;
  }
}

/**
 * Update current status counts in dashboard
 */
function updateCurrentStatus(
  dashboardAgg: RealTimeDashboardAggregation,
  order: OrderDocument,
  operation: 'create' | 'update' | 'delete',
  previousOrder?: OrderDocument
): void {
  const today = new Date().toISOString().split('T')[0];
  const orderDate = order.createdAt.split('T')[0];
  
  // Only update status for today's orders
  if (orderDate !== today) return;
  
  // Remove previous status impact if this is an update
  if (operation === 'update' && previousOrder) {
    updateStatusCount(dashboardAgg, previousOrder.status, -1);
  }
  
  // Apply current status impact
  const multiplier = operation === 'delete' ? -1 : 1;
  updateStatusCount(dashboardAgg, order.status, multiplier);
}

/**
 * Helper to update status counts
 */
function updateStatusCount(
  dashboardAgg: RealTimeDashboardAggregation,
  status: string,
  multiplier: number
): void {
  switch (status) {
    case 'pending':
      dashboardAgg.currentStatus.pendingOrders += multiplier;
      break;
    case 'preparing':
      dashboardAgg.currentStatus.preparingOrders += multiplier;
      break;
    case 'completed':
      dashboardAgg.currentStatus.completedOrdersToday += multiplier;
      break;
    case 'cancelled':
      dashboardAgg.currentStatus.cancelledOrdersToday += multiplier;
      break;
  }
}

/**
 * 🚀 Fast Dashboard Data Retrieval
 * 
 * These functions provide lightning-fast access to pre-computed aggregations
 */

export async function getDashboardData(): Promise<RealTimeDashboardAggregation | null> {
  try {
    const aggregationId = generateRealTimeDashboardId();
    return await databaseV4.getDoc<RealTimeDashboardAggregation>(aggregationId);
  } catch (error) {
    console.warn('[getDashboardData] Dashboard aggregation not found, returning null');
    return null;
  }
}

export async function getDailySalesData(date: string): Promise<DailySalesAggregation | null> {
  try {
    const aggregationId = generateDailyAggregationId(date);
    return await databaseV4.getDoc<DailySalesAggregation>(aggregationId);
  } catch (error) {
    console.warn(`[getDailySalesData] Daily aggregation for ${date} not found`);
    return null;
  }
}

export async function getItemPerformanceData(menuItemId: string): Promise<ItemPerformanceAggregation | null> {
  try {
    const aggregationId = generateItemPerformanceId(menuItemId);
    return await databaseV4.getDoc<ItemPerformanceAggregation>(aggregationId);
  } catch (error) {
    console.warn(`[getItemPerformanceData] Item performance for ${menuItemId} not found`);
    return null;
  }
}

/**
 * Get date range of sales data
 */
export async function getSalesDataRange(
  startDate: string,
  endDate: string
): Promise<DailySalesAggregation[]> {
  try {
    const result = await databaseV4.findDocs<DailySalesAggregation>({
      selector: {
        type: 'daily_sales_aggregation',
        date: {
          $gte: startDate,
          $lte: endDate
        }
      },
      sort: [{ type: 'asc' }, { date: 'asc' }]
    });
    
    return result.docs;
  } catch (error) {
    console.error('[getSalesDataRange] Failed to get sales data range:', error);
    return [];
  }
}

/**
 * 🔧 Maintenance Functions
 */

export async function createAggregationIndexes(): Promise<void> {
  console.log('[createAggregationIndexes] Creating indexes for sales aggregations');
  
  try {
    // Index for daily aggregations by date
    await databaseV4.createIndex({
      index: {
        fields: ['type', 'date'],
        name: 'aggregation-daily-date-idx'
      }
    });
    
    // Index for weekly aggregations
    await databaseV4.createIndex({
      index: {
        fields: ['type', 'weekStartDate'],
        name: 'aggregation-weekly-date-idx'
      }
    });
    
    // Index for monthly aggregations
    await databaseV4.createIndex({
      index: {
        fields: ['type', 'year', 'month'],
        name: 'aggregation-monthly-date-idx'
      }
    });
    
    // Index for item performance
    await databaseV4.createIndex({
      index: {
        fields: ['type', 'menuItemId'],
        name: 'aggregation-item-performance-idx'
      }
    });
    
    console.log('✅ [createAggregationIndexes] All aggregation indexes created successfully');
  } catch (error) {
    console.error('❌ [createAggregationIndexes] Failed to create indexes:', error);
    throw error;
  }
} 