import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import { 
  SupplierDocument, 
  Supplier, 
  DEFAULT_SUPPLIER_DOCUMENT, 
  createDefaultSupplierDocument
} from '../schemas/supplier-schema';

export async function getSuppliers(): Promise<SupplierDocument> {
  try {
    return await databaseV4.getDoc<SupplierDocument>('suppliers');
  } catch (error: any) {
    if (error.status === 404) {
      // Create with fresh timestamps
      const defaultDoc = createDefaultSupplierDocument();
      await databaseV4.putDoc(defaultDoc);
      return defaultDoc;
    }
    throw error;
  }
}

export async function updateSuppliers(suppliers: SupplierDocument): Promise<SupplierDocument> {
  try {
    const updatedDoc = {
      ...suppliers,
      updatedAt: new Date().toISOString()
    };
    await databaseV4.putDoc(updatedDoc);
    return updatedDoc;
  } catch (error) {
    console.error('Error updating suppliers document:', error);
    throw error;
  }
}

// Supplier CRUD with conflict resolution
export async function addSupplier(supplier: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>): Promise<Supplier> {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const suppliersDoc = await getSuppliers();
      
      const newSupplier: Supplier = {
        id: `supplier_${uuidv4()}`,
        ...supplier,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      suppliersDoc.suppliers = suppliersDoc.suppliers || [];
      suppliersDoc.suppliers.push(newSupplier);
      await updateSuppliers(suppliersDoc);
      return newSupplier;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[addSupplier] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      console.error('Error adding supplier:', error);
      throw error;
    }
  }
  
  throw lastError;
}

export async function updateSupplier(supplierId: string, updates: Partial<Supplier>): Promise<Supplier> {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const suppliersDoc = await getSuppliers();
      const idx = suppliersDoc.suppliers.findIndex(s => s.id === supplierId);
      
      if (idx === -1) {
        throw new Error(`Supplier with ID ${supplierId} not found`);
      }
      
      const updatedSupplier = { 
        ...suppliersDoc.suppliers[idx], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      };
      
      suppliersDoc.suppliers[idx] = updatedSupplier;
      await updateSuppliers(suppliersDoc);
      return updatedSupplier;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[updateSupplier] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      console.error('Error updating supplier:', error);
      throw error;
    }
  }
  
  throw lastError;
}

export async function deleteSupplier(supplierId: string): Promise<boolean> {
  let retries = 3;
  let lastError: any = null;

  while (retries > 0) {
    try {
      // Get fresh document with latest revision
      const suppliersDoc = await getSuppliers();
      const idx = suppliersDoc.suppliers.findIndex(s => s.id === supplierId);
      
      if (idx === -1) {
        throw new Error(`Supplier with ID ${supplierId} not found`);
      }
      
      suppliersDoc.suppliers.splice(idx, 1);
      await updateSuppliers(suppliersDoc);
      return true;
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with fresh document
      if (error.status === 409 && retries > 1) {
        console.warn(`[deleteSupplier] Document conflict, retrying... (${retries - 1} attempts left)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      console.error('Error deleting supplier:', error);
      throw error;
    }
  }
  
  throw lastError;
} 