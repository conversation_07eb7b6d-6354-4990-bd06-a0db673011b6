"use client";

import { databaseV4 } from '../core/db-instance';
import { OrderDocument } from '../schemas/order-schema';
import { createCashTransaction } from './cash-ops';
import { updateCollectionStatus } from './order-ops';
import { formatCurrency } from '@/lib/utils/currency';

/**
 * 🚀 NEW: Enhanced Collection Operations
 * Handles both staff and freelancer collection-based deliveries
 */

export interface EnhancedPendingCollection {
  orderId: string;
  driverType: 'staff' | 'freelance';
  driverId: string; // staffId or freelancer phone
  driverName: string;
  paymentModel: 'collection' | 'prepaid';
  expectedAmount: number;
  collectionRate?: number; // For freelance collection model (fallback rate)
  deliveryTariff?: number; // Individual order tariff amount
  order: OrderDocument;
  deliveryStatus: string;
  customer: {
    name: string;
    phone?: string;
    address?: string;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  createdAt: string;
}

// Debug function removed - use database monitoring tools or db-debug.ts utilities instead

/**
 * 🔧 MIGRATION: Initialize collection status for existing delivery orders
 */
export async function migrateExistingDeliveryOrders(): Promise<number> {
  try {
    console.log('[migrateExistingDeliveryOrders] Starting migration...');
    
    // Get all completed, paid delivery orders without collection status
    const orders = await databaseV4.findDocs<OrderDocument>({
      selector: {
        type: 'order_document',
        orderType: 'delivery',
        status: 'completed',
        paymentStatus: 'paid',
        deliveryPerson: { $exists: true }
      }
    });

    console.log(`[migrateExistingDeliveryOrders] Found ${orders.docs.length} delivery orders to check`);

    let migratedCount = 0;

    for (const order of orders.docs) {
      // Skip if already has collection status
      if (order.collectionStatus) {
        continue;
      }

      // Skip if no delivery person or prepaid freelancer
      if (!order.deliveryPerson) {
        continue;
      }

      // Skip prepaid freelancers (they don't need collection)
      if (order.deliveryPerson.type === 'freelance' && 
          order.deliveryPerson.paymentModel === 'prepaid') {
        continue;
      }

      try {
        // Initialize collection status for staff or collection-based freelancers
        const { updateDeliveryStatus } = await import('./order-ops');
        
        await updateDeliveryStatus(order._id, 'delivered', {
          attemptedBy: order.deliveryPerson.name,
          notes: 'Migration: Initialized collection status for existing order'
        });

        migratedCount++;
        console.log(`[migrateExistingDeliveryOrders] Migrated order ${order._id}`);
      } catch (error) {
        console.error(`[migrateExistingDeliveryOrders] Error migrating order ${order._id}:`, error);
      }
    }

    console.log(`[migrateExistingDeliveryOrders] ✅ Migration complete. Migrated ${migratedCount} orders`);
    return migratedCount;
  } catch (error) {
    console.error('[migrateExistingDeliveryOrders] Migration error:', error);
    throw error;
  }
}

/**
 * Get all pending collections (staff + freelance collection model)
 */
export async function getEnhancedPendingCollections(): Promise<EnhancedPendingCollection[]> {
  try {
    console.log('[getEnhancedPendingCollections] Fetching pending collections...');
    
    // Debug logging removed from production queries for performance
    // Use debugDeliveryOrders() separately when needed
    
    // Get all delivery orders with pending collections
    const orders = await databaseV4.findDocs<OrderDocument>({
      selector: {
        type: 'order_document',
        orderType: 'delivery',
        status: 'completed',
        paymentStatus: 'paid',
        'collectionStatus.isPending': true,
        deliveryPerson: { $exists: true }
      }
    });

    console.log(`[getEnhancedPendingCollections] Found ${orders.docs.length} orders with pending collections`);

    // Migration logic removed from production queries for performance and reliability
    // If migration is needed, it should be run as a separate admin operation

    const pendingCollections: EnhancedPendingCollection[] = orders.docs.map(order => {
      const deliveryPerson = order.deliveryPerson!;
      
      return {
        orderId: order._id,
        driverType: deliveryPerson.type,
        driverId: deliveryPerson.type === 'staff' 
          ? deliveryPerson.staffId! 
          : (deliveryPerson.freelancerId || deliveryPerson.phone),
        driverName: deliveryPerson.name,
        paymentModel: deliveryPerson.paymentModel || 'collection',
        expectedAmount: order.collectionStatus?.expectedAmount || order.total,
        collectionRate: deliveryPerson.collectionRate,
        deliveryTariff: order.deliveryTariff,
        order: order,
        deliveryStatus: order.deliveryStatus || 'delivered',
        customer: {
          name: order.customer?.name || 'Client',
          phone: order.customer?.phone,
          address: order.customer?.address
        },
        items: order.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price + (item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0)
        })),
        createdAt: order.createdAt
      };
    });

    // Group by driver for easier processing
    const groupedCollections = pendingCollections.reduce((groups, collection) => {
      const key = `${collection.driverType}-${collection.driverId}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(collection);
      return groups;
    }, {} as Record<string, EnhancedPendingCollection[]>);

    console.log(`[getEnhancedPendingCollections] Grouped into ${Object.keys(groupedCollections).length} driver groups`);

    return pendingCollections;
  } catch (error) {
    console.error('[getEnhancedPendingCollections] Error fetching pending collections:', error);
    throw error;
  }
}

/**
 * Process collection for a driver (multiple orders)
 */
export async function processDriverCollection(
  driverCollections: EnhancedPendingCollection[],
  collectionData: {
    actualAmount: number;
    collectedBy: string;
    discrepancyReason?: string;
    manualExpenses?: Array<{
      description: string;
      amount: number;
      reason: string;
    }>;
  }
): Promise<void> {
  try {
    const firstCollection = driverCollections[0];
    const driverName = firstCollection.driverName;
    const driverType = firstCollection.driverType;
    
    console.log(`[processDriverCollection] Processing collection for ${driverType} driver: ${driverName}`);
    console.log(`[processDriverCollection] ${driverCollections.length} orders, expected: ${driverCollections.reduce((sum, c) => sum + c.expectedAmount, 0)}, actual: ${collectionData.actualAmount}`);
    
    if (collectionData.manualExpenses?.length) {
      console.log(`[processDriverCollection] Session expenses: ${collectionData.manualExpenses.map(e => `${e.description}: ${e.amount} DA`).join(', ')}`);
    }

    // Calculate totals
    const totalExpected = driverCollections.reduce((sum, collection) => sum + collection.expectedAmount, 0);
    const manualExpenseTotal = collectionData.manualExpenses?.reduce((sum, exp) => sum + exp.amount, 0) || 0;
    const adjustedExpected = totalExpected - manualExpenseTotal;
    const discrepancy = collectionData.actualAmount - adjustedExpected;

    // Calculate freelancer tariff if applicable - sum individual order tariffs
    const totalTariff = driverType === 'freelance' && firstCollection.paymentModel === 'collection' ? 
                       driverCollections.reduce((sum, collection) => sum + (collection.deliveryTariff || collection.collectionRate || 0), 0) : 0;

    console.log(`[processDriverCollection] Financial breakdown:`, {
      totalExpected,
      actualAmount: collectionData.actualAmount,
      sessionExpenses: manualExpenseTotal,
      freelancerTariff: totalTariff,
      individualTariffs: driverCollections.map(c => ({ orderId: c.orderId, tariff: c.deliveryTariff || c.collectionRate || 0 })),
      netForRestaurant: collectionData.actualAmount - totalTariff,
      discrepancy
    });

    // 1. Create collection EVENT record (not a transaction)
    const collectionEventId = `collection_event:${Date.now()}:${firstCollection.driverId}`;
    const collectionEvent = {
      _id: collectionEventId,
      type: 'collection_event',
      timestamp: new Date().toISOString(),
      driverType,
      driverName,
      driverId: firstCollection.driverId,
      staffId: driverType === 'staff' ? firstCollection.driverId : `freelancer-${firstCollection.driverId}`,
      orderCount: driverCollections.length,
      orderIds: driverCollections.map(c => c.orderId),
      orders: driverCollections.map(c => ({
        orderId: c.orderId,
        expectedAmount: c.expectedAmount,
        customer: c.customer.name,
        items: c.items
      })),
      expectedAmount: totalExpected,
      actualAmount: collectionData.actualAmount,
      freelancerTariff: totalTariff,
      netAmountForRestaurant: collectionData.actualAmount - totalTariff,
      discrepancy,
      discrepancyReason: collectionData.discrepancyReason,
      sessionExpenses: collectionData.manualExpenses || [],
      sessionExpenseTotal: manualExpenseTotal,
      paymentModel: firstCollection.paymentModel,
      collectedBy: collectionData.collectedBy,
      status: 'completed'
    };

    await databaseV4.putDoc(collectionEvent);

    // 2. Create ONLY the net cash transaction for the drawer
    if (collectionData.actualAmount - totalTariff > 0) {
      await createCashTransaction({
        type: 'manual_in',
        amount: collectionData.actualAmount - totalTariff,
        description: `Collection - ${driverName} (${driverCollections.length} livraisons)`,
        time: new Date().toISOString(),
        performedBy: collectionData.collectedBy,
        relatedDocId: collectionEventId,
        metadata: {
          transactionCategory: 'collection_cash_in',
          collectionEventId,
          driverName,
          orderCount: driverCollections.length
        }
      });
    }

    // For freelance collection model, pay the driver their collection rate
    if (driverType === 'freelance' && firstCollection.paymentModel === 'collection' && totalTariff > 0) {
      
      // Create expense for freelancer payment using correct individual tariffs
      const { createExpense } = await import('./expense-ops');
      await createExpense({
        date: new Date().toISOString().split('T')[0],
        category: 'delivery_fees',
        description: `Freelance collection payment - ${driverName} (${driverCollections.length} livraisons)`,
        amount: totalTariff, // Use correct individual tariff total
        paymentMethod: 'cash',
        notes: `Collection-based freelancer payment: ${driverCollections.map(c => `${c.orderId}: ${c.deliveryTariff || c.collectionRate || 0} DA`).join(', ')}`,
        paymentSource: 'cash_register'
      });

      // Deduct from caisse
      await createCashTransaction({
        type: 'expense',
        amount: -totalTariff, // Use correct tariff total
        description: `Freelance collection payment - ${driverName}`,
        time: new Date().toISOString(),
        performedBy: collectionData.collectedBy,
        relatedDocId: `freelance-payment-${Date.now()}`,
        metadata: {
          transactionCategory: 'freelance_collection_payment',
          driverName,
          driverId: firstCollection.driverId,
          staffId: `freelancer-${firstCollection.driverId}`,
          orderCount: driverCollections.length,
          individualTariffs: driverCollections.map(c => ({ orderId: c.orderId, tariff: c.deliveryTariff || c.collectionRate || 0 })),
          totalPayment: totalTariff
        }
      });

      console.log(`[processDriverCollection] Paid freelancer ${driverName}: ${totalTariff} DA (individual tariffs)`);
    }

    // Create formal expenses for session expenses (fuel, parking, etc.)
    if (collectionData.manualExpenses?.length) {
      const { createExpense } = await import('./expense-ops');
      
      for (const expense of collectionData.manualExpenses) {
        await createExpense({
          date: new Date().toISOString().split('T')[0],
          category: 'delivery_expenses', // New category for driver session expenses
          description: `${driverName} - ${expense.description}`,
          amount: expense.amount,
          paymentMethod: 'cash',
          notes: `Session expense: ${expense.reason}`,
          paymentSource: 'cash_register'
        });
      }

      // Create corresponding cash transaction for session expenses
      await createCashTransaction({
        type: 'expense',
        amount: -manualExpenseTotal,
        description: `Driver session expenses - ${driverName}`,
        time: new Date().toISOString(),
        performedBy: collectionData.collectedBy,
        relatedDocId: `session-expenses-${Date.now()}`,
        metadata: {
          transactionCategory: 'driver_session_expenses',
          driverName,
          driverId: firstCollection.driverId,
          staffId: driverType === 'staff' ? firstCollection.driverId : `freelancer-${firstCollection.driverId}`,
          sessionExpenses: collectionData.manualExpenses,
          totalAmount: manualExpenseTotal
        }
      });

      console.log(`[processDriverCollection] Recorded session expenses for ${driverName}: ${manualExpenseTotal} DA`);
    }

    // Update collection status for each order
    for (const collection of driverCollections) {
      await updateCollectionStatus(collection.orderId, {
        actualAmount: collection.expectedAmount, // Individual order amount
        collectedBy: collectionData.collectedBy,
        discrepancyReason: discrepancy !== 0 ? collectionData.discrepancyReason : undefined,
        manualExpenses: collectionData.manualExpenses
      });
    }

    console.log(`[processDriverCollection] ✅ Collection processed successfully for ${driverName}`);
  } catch (error) {
    console.error('[processDriverCollection] Error processing driver collection:', error);
    throw error;
  }
}

/**
 * Get collection summary by driver
 */
export async function getCollectionSummaryByDriver(): Promise<Array<{
  driverType: 'staff' | 'freelance';
  driverId: string;
  driverName: string;
  paymentModel: 'collection' | 'prepaid';
  orderCount: number;
  totalExpected: number;
  collections: EnhancedPendingCollection[];
}>> {
  const pendingCollections = await getEnhancedPendingCollections();
  
  const summary = pendingCollections.reduce((groups, collection) => {
    const key = `${collection.driverType}-${collection.driverId}`;
    
    if (!groups[key]) {
      groups[key] = {
        driverType: collection.driverType,
        driverId: collection.driverId,
        driverName: collection.driverName,
        paymentModel: collection.paymentModel,
        orderCount: 0,
        totalExpected: 0,
        collections: []
      };
    }
    
    groups[key].orderCount++;
    groups[key].totalExpected += collection.expectedAmount;
    groups[key].collections.push(collection);
    
    return groups;
  }, {} as Record<string, any>);
  
  return Object.values(summary);
}

/**
 * Get delivery status color for UI
 */
export function getDeliveryStatusColor(status: string): string {
  switch (status) {
    case 'delivered':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'failed':
      return 'bg-red-50 border-red-200 text-red-800';
    case 'partially_delivered':
      return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    case 'out_for_delivery':
      return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'pending':
      return 'bg-gray-50 border-gray-200 text-gray-800';
    default:
      return 'bg-gray-50 border-gray-200 text-gray-800';
  }
}

/**
 * Get payment model badge color
 */
export function getPaymentModelColor(paymentModel: string): string {
  switch (paymentModel) {
    case 'collection':
      return 'bg-blue-100 text-blue-800';
    case 'prepaid':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
} 