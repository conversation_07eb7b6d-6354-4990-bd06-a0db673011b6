"use client";

/**
 * Queue Operations for V4 Database
 * 
 * This file contains all the database operations related to kitchen queue management.
 * Replaces the in-memory queue system with persistent PouchDB storage.
 */

import { databaseV4 } from '../core/db-instance';
import {
  QueueItem,
  ItemCompletion,
  StationQueueSummary,
  StationQueue,
  ItemCompletionStatus,
  DEFAULT_QUEUE_ITEM,
  DEFAULT_ITEM_COMPLETION,
  DEFAULT_STATION_QUEUE_SUMMARY,
  generateQueueItemId,
  generateItemCompletionId,
  generateStationQueueSummaryId
} from '../schemas/queue-schema';
import { Order, OrderItem } from '../schemas/order-schema';
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument
} from '../core/conflict-resolution';

/**
 * Create queue indexes for optimal performance
 */
export async function createQueueIndexes(): Promise<void> {
  console.log('[createQueueIndexes] Starting queue index creation');
  
  const results: {name: string, success: boolean, error?: any}[] = [];
  
  const indexes = [
    {
      name: 'queue-by-station',
      index: { fields: ['type', 'stationId', 'status', 'createdAt'] }
    },
    {
      name: 'queue-by-order',
      index: { fields: ['type', 'orderId', 'stationId'] }
    },
    {
      name: 'queue-by-status',
      index: { fields: ['type', 'status', 'createdAt'] }
    },
    {
      name: 'item-completion-by-barcode',
      index: { fields: ['type', 'barcode'] }
    },
    {
      name: 'item-completion-by-order',
      index: { fields: ['type', 'orderId', 'stationId'] }
    },
    {
      name: 'station-summary-by-station',
      index: { fields: ['type', 'stationId'] }
    }
  ];

  for (const indexDef of indexes) {
    try {
      await databaseV4.createIndex(indexDef);
      results.push({ name: indexDef.name, success: true });
      console.log(`✅ [createQueueIndexes] Created index: ${indexDef.name}`);
    } catch (error: any) {
      if (error.status === 409) {
        results.push({ name: indexDef.name, success: true });
        console.log(`ℹ️ [createQueueIndexes] Index already exists: ${indexDef.name}`);
      } else {
        results.push({ name: indexDef.name, success: false, error });
        console.error(`❌ [createQueueIndexes] Failed to create index ${indexDef.name}:`, error);
      }
    }
  }

  const successCount = results.filter(r => r.success).length;
  console.log(`[createQueueIndexes] Completed: ${successCount}/${results.length} indexes created successfully`);
}

/**
 * Add order to station queue
 */
export async function addOrderToQueue(
  order: Order, 
  stationId: string, 
  stationItems: OrderItem[]
): Promise<QueueItem> {
  return retryWithConflictResolution(async () => {
    console.log(`📋 [addOrderToQueue] Adding order ${order.id} to station ${stationId} queue`);
    
    const now = new Date().toISOString();
    const queueItemId = generateQueueItemId(stationId, order.id);
    
    // Calculate estimated time
    const totalItems = stationItems.reduce((sum, item) => sum + item.quantity, 0);
    const estimatedTime = calculateEstimatedTime(totalItems);
    
    // Create queue item
    const queueItem: QueueItem = {
      ...DEFAULT_QUEUE_ITEM,
      _id: queueItemId,
      orderId: order.id,
      orderNumber: order.id, // Use order ID as order number for now
      stationId,
      items: stationItems,
      estimatedTime,
      createdAt: now,
      updatedAt: now
    };
    
    // Save queue item
    const result = await databaseV4.putDoc(queueItem);
    const savedQueueItem = { ...queueItem, _rev: result.rev };
    
    // Update station summary
    await updateStationSummary(stationId);
    
    console.log(`✅ [addOrderToQueue] Added order ${order.id} to station ${stationId} queue (${totalItems} items)`);
    return savedQueueItem;
  }, 3, `addOrderToQueue(${order.id}, ${stationId})`);
}

/**
 * Register item for completion tracking
 */
export async function registerItemCompletion(
  barcode: string, 
  orderId: string, 
  stationId: string, 
  itemName: string
): Promise<ItemCompletion> {
  return retryWithConflictResolution(async () => {
    console.log(`🏷️ [registerItemCompletion] Registering barcode ${barcode} for tracking`);
    
    const now = new Date().toISOString();
    const completionId = generateItemCompletionId(barcode);
    
    const itemCompletion: ItemCompletion = {
      ...DEFAULT_ITEM_COMPLETION,
      _id: completionId,
      barcode,
      orderId,
      stationId,
      itemName,
      createdAt: now,
      updatedAt: now
    };
    
    const result = await databaseV4.putDoc(itemCompletion);
    return { ...itemCompletion, _rev: result.rev };
  }, 3, `registerItemCompletion(${barcode})`);
}

/**
 * Complete item when barcode is scanned
 */
export async function completeItem(barcode: string, scannedBy?: string): Promise<{
  success: boolean;
  message: string;
  orderCompleted: boolean;
  queuePosition?: number;
}> {
  return retryWithConflictResolution(async () => {
    console.log(`🔍 [completeItem] Processing barcode scan: ${barcode}`);
    
    // Get item completion record
    const completionId = generateItemCompletionId(barcode);
    let completion: ItemCompletion;
    
    try {
      completion = await safeGetDocument<ItemCompletion>(completionId);
    } catch (error: any) {
      if (error.status === 404) {
        return {
          success: false,
          message: `❌ Invalid barcode: ${barcode}`,
          orderCompleted: false
        };
      }
      throw error;
    }
    
    if (completion.status === 'completed') {
      return {
        success: false,
        message: `⚠️ Item already completed`,
        orderCompleted: false
      };
    }
    
    // Mark item as completed
    const now = new Date().toISOString();
    const updatedCompletion = await safeUpdateDocument<ItemCompletion>(
      completionId,
      (doc) => ({
        ...doc,
        status: 'completed' as const,
        scannedBy,
        completedAt: now,
        updatedAt: now
      }),
      `completeItem(${barcode})`
    );
    
    // Update queue item
    const queueItemId = generateQueueItemId(completion.stationId, completion.orderId);
    let orderCompleted = false;
    let queuePosition: number | undefined;
    
    try {
      const updatedQueueItem = await safeUpdateDocument<QueueItem>(
        queueItemId,
        (queueItem) => {
          // Add completed barcode to the list
          const completedItemIds = [...queueItem.completedItemIds, barcode];
          
          // Check if order is fully completed
          const totalIndividualItems = queueItem.items.reduce((sum, item) => sum + item.quantity, 0);
          const isOrderCompleted = completedItemIds.length >= totalIndividualItems;
          
          let status = queueItem.status;
          let completedAt = queueItem.completedAt;
          
          if (isOrderCompleted) {
            status = 'completed';
            completedAt = now;
            orderCompleted = true;
          } else if (completedItemIds.length > 0) {
            status = 'in-progress';
          }
          
          return {
            ...queueItem,
            completedItemIds,
            status,
            completedAt,
            updatedAt: now
          };
        },
        `completeItem-updateQueue(${barcode})`
      );
      
      // Get queue position
      queuePosition = await getOrderQueuePosition(completion.orderId, completion.stationId);
      
      // Update station summary
      await updateStationSummary(completion.stationId);
      
      // Schedule removal of completed orders after delay
      if (orderCompleted) {
        setTimeout(async () => {
          try {
            await removeCompletedOrderFromQueue(completion.orderId, completion.stationId);
          } catch (error) {
            console.error(`❌ [completeItem] Failed to remove completed order ${completion.orderId}:`, error);
          }
        }, 30000); // Remove after 30 seconds
      }
      
      const totalItems = updatedQueueItem.items.reduce((sum, item) => sum + item.quantity, 0);
      const completedItems = updatedQueueItem.completedItemIds.length;
      
      return {
        success: true,
        message: `✅ Item "${completion.itemName}" completed! (${completedItems}/${totalItems})`,
        orderCompleted,
        queuePosition
      };
      
    } catch (error: any) {
      if (error.status === 404) {
        // Queue item not found, but item completion was successful
        return {
          success: true,
          message: `✅ Item "${completion.itemName}" completed!`,
          orderCompleted: false
        };
      }
      throw error;
    }
  }, 3, `completeItem(${barcode})`);
}

/**
 * Get station queue with all items
 */
export async function getStationQueue(stationId: string): Promise<StationQueue> {
  console.log(`📊 [getStationQueue] Getting queue for station ${stationId}`);
  
  try {
    // Get all queue items for this station
    const result = await databaseV4.findDocs({
      selector: {
        type: 'queue_item',
        stationId: stationId,
        status: { $in: ['pending', 'in-progress'] }
      },
      sort: [{ createdAt: 'asc' }]
    });
    
    const queueItems = result.docs as QueueItem[];
    
    // Get station summary for aggregated data
    let summary: StationQueueSummary;
    try {
      summary = await safeGetDocument<StationQueueSummary>(generateStationQueueSummaryId(stationId));
    } catch (error: any) {
      if (error.status === 404) {
        // Create default summary if not found
        summary = await createDefaultStationSummary(stationId);
      } else {
        throw error;
      }
    }
    
    return {
      stationId: summary.stationId,
      stationName: summary.stationName,
      totalOrders: queueItems.length,
      pendingOrders: queueItems.filter(item => item.status === 'pending').length,
      inProgressOrders: queueItems.filter(item => item.status === 'in-progress').length,
      completedOrders: summary.completedOrders,
      averageTime: summary.averageTime,
      items: queueItems
    };
  } catch (error) {
    console.error(`❌ [getStationQueue] Error getting queue for station ${stationId}:`, error);
    throw error;
  }
}

/**
 * Get all station queues
 */
export async function getAllStationQueues(): Promise<Map<string, StationQueue>> {
  console.log(`📊 [getAllStationQueues] Getting all station queues`);
  
  try {
    // Get all station summaries
    const result = await databaseV4.findDocs({
      selector: {
        type: 'station_queue_summary'
      }
    });
    
    const summaries = result.docs as StationQueueSummary[];
    const queues = new Map<string, StationQueue>();
    
    // Get queue for each station
    for (const summary of summaries) {
      const queue = await getStationQueue(summary.stationId);
      queues.set(summary.stationId, queue);
    }
    
    return queues;
  } catch (error) {
    console.error(`❌ [getAllStationQueues] Error getting all station queues:`, error);
    throw error;
  }
}

/**
 * Cancel an entire order, removing it from all queues
 */
export async function cancelOrder(orderId: string): Promise<void> {
  return retryWithConflictResolution(async () => {
    console.log(`🚫 [cancelOrder] Cancelling order ${orderId} in all queues`);
    
    // Find all queue items for this order
    const queueResult = await databaseV4.findDocs({
      selector: {
        type: 'queue_item',
        orderId: orderId
      }
    });
    
    const queueItems = queueResult.docs as QueueItem[];
    const stationsToUpdate = new Set<string>();
    
    // Delete all queue items for this order
    for (const queueItem of queueItems) {
      await databaseV4.deleteDoc(queueItem._id, queueItem._rev!);
      stationsToUpdate.add(queueItem.stationId);
    }
    
    // Find and delete all item completions for this order
    const completionResult = await databaseV4.findDocs({
      selector: {
        type: 'item_completion',
        orderId: orderId
      }
    });
    
    const completions = completionResult.docs as ItemCompletion[];
    for (const completion of completions) {
      await databaseV4.deleteDoc(completion._id, completion._rev!);
    }
    
    // Update station summaries
    for (const stationId of stationsToUpdate) {
      await updateStationSummary(stationId);
    }
    
    console.log(`✅ [cancelOrder] Order ${orderId} removed from all kitchen queues`);
  }, 3, `cancelOrder(${orderId})`);
}

/**
 * Complete an entire order, marking it as served
 */
export async function completeOrder(orderId: string): Promise<void> {
  return retryWithConflictResolution(async () => {
    console.log(`✅ [completeOrder] Completing order ${orderId} in all queues`);
    
    const now = new Date().toISOString();
    
    // Find all queue items for this order
    const queueResult = await databaseV4.findDocs({
      selector: {
        type: 'queue_item',
        orderId: orderId
      }
    });
    
    const queueItems = queueResult.docs as QueueItem[];
    const stationsToUpdate = new Set<string>();
    
    // Mark all queue items as completed
    for (const queueItem of queueItems) {
      await safeUpdateDocument<QueueItem>(
        queueItem._id,
        (doc) => ({
          ...doc,
          status: 'completed' as const,
          completedAt: now,
          updatedAt: now
        }),
        `completeOrder-updateQueue(${orderId})`
      );
      stationsToUpdate.add(queueItem.stationId);
    }
    
    // Mark all item completions as completed
    const completionResult = await databaseV4.findDocs({
      selector: {
        type: 'item_completion',
        orderId: orderId,
        status: 'pending'
      }
    });
    
    const completions = completionResult.docs as ItemCompletion[];
    for (const completion of completions) {
      await safeUpdateDocument<ItemCompletion>(
        completion._id,
        (doc) => ({
          ...doc,
          status: 'completed' as const,
          completedAt: now,
          updatedAt: now
        }),
        `completeOrder-updateCompletion(${orderId})`
      );
    }
    
    // Update station summaries
    for (const stationId of stationsToUpdate) {
      await updateStationSummary(stationId);
    }
    
    // Schedule removal of completed orders
    setTimeout(async () => {
      for (const stationId of stationsToUpdate) {
        try {
          await removeCompletedOrderFromQueue(orderId, stationId);
        } catch (error) {
          console.error(`❌ [completeOrder] Failed to remove completed order ${orderId} from station ${stationId}:`, error);
        }
      }
    }, 30000); // Remove after 30 seconds
    
    console.log(`✅ [completeOrder] Order ${orderId} marked as completed and scheduled for removal`);
  }, 3, `completeOrder(${orderId})`);
}

/**
 * Get queue position for an order
 */
export async function getOrderQueuePosition(orderId: string, stationId: string): Promise<number> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'queue_item',
        stationId: stationId,
        status: { $in: ['pending', 'in-progress'] }
      },
      sort: [{ createdAt: 'asc' }]
    });
    
    const queueItems = result.docs as QueueItem[];
    const position = queueItems.findIndex(item => item.orderId === orderId);
    return position >= 0 ? position + 1 : 0;
  } catch (error) {
    console.error(`❌ [getOrderQueuePosition] Error getting position for order ${orderId}:`, error);
    return 0;
  }
}

/**
 * Check if an individual item is completed
 */
export async function isItemCompleted(barcode: string): Promise<boolean> {
  try {
    const completion = await safeGetDocument<ItemCompletion>(generateItemCompletionId(barcode));
    return completion.status === 'completed';
  } catch (error: any) {
    if (error.status === 404) {
      return false;
    }
    throw error;
  }
}

/**
 * Clear completed orders from queue (cleanup)
 */
export async function clearCompletedOrders(stationId: string): Promise<void> {
  return retryWithConflictResolution(async () => {
    console.log(`🧹 [clearCompletedOrders] Cleaning up completed orders for station ${stationId}`);
    
    const result = await databaseV4.findDocs({
      selector: {
        type: 'queue_item',
        stationId: stationId,
        status: 'completed'
      }
    });
    
    const completedItems = result.docs as QueueItem[];
    
    for (const item of completedItems) {
      await databaseV4.deleteDoc(item._id, item._rev!);
    }
    
    await updateStationSummary(stationId);
    
    console.log(`✅ [clearCompletedOrders] Removed ${completedItems.length} completed orders from station ${stationId}`);
  }, 3, `clearCompletedOrders(${stationId})`);
}

/**
 * Reset all queues (for testing/debugging)
 */
export async function resetAllQueues(): Promise<void> {
  console.log(`🔄 [resetAllQueues] Resetting all queue data`);
  
  try {
    // Delete all queue items
    const queueResult = await databaseV4.findDocs({
      selector: { type: 'queue_item' }
    });
    
    for (const doc of queueResult.docs) {
      await databaseV4.deleteDoc(doc._id, doc._rev!);
    }
    
    // Delete all item completions
    const completionResult = await databaseV4.findDocs({
      selector: { type: 'item_completion' }
    });
    
    for (const doc of completionResult.docs) {
      await databaseV4.deleteDoc(doc._id, doc._rev!);
    }
    
    // Delete all station summaries
    const summaryResult = await databaseV4.findDocs({
      selector: { type: 'station_queue_summary' }
    });
    
    for (const doc of summaryResult.docs) {
      await databaseV4.deleteDoc(doc._id, doc._rev!);
    }
    
    console.log(`✅ [resetAllQueues] All queue data has been reset`);
  } catch (error) {
    console.error(`❌ [resetAllQueues] Error resetting queues:`, error);
    throw error;
  }
}

// Helper Functions

/**
 * Update station summary with current queue statistics
 */
async function updateStationSummary(stationId: string): Promise<void> {
  const summaryId = generateStationQueueSummaryId(stationId);
  
  try {
    // Get current queue items for this station
    const result = await databaseV4.findDocs({
      selector: {
        type: 'queue_item',
        stationId: stationId
      }
    });
    
    const queueItems = result.docs as QueueItem[];
    
    const pendingOrders = queueItems.filter(item => item.status === 'pending').length;
    const inProgressOrders = queueItems.filter(item => item.status === 'in-progress').length;
    const completedOrders = queueItems.filter(item => item.status === 'completed').length;
    const totalOrders = queueItems.length;
    
    // Calculate average time (simplified)
    const averageTime = calculateAverageTime(queueItems);
    
    const now = new Date().toISOString();
    
    // Try to get existing summary first
    let existingSummary: StationQueueSummary | null = null;
    try {
      existingSummary = await safeGetDocument<StationQueueSummary>(summaryId);
    } catch (error: any) {
      if (error.status !== 404) {
        throw error;
      }
    }
    
    // Create or update station summary
    const summaryToSave: StationQueueSummary = existingSummary ? {
      ...existingSummary,
      stationId,
      stationName: stationId, // Will be resolved by caller
      totalOrders,
      pendingOrders,
      inProgressOrders,
      completedOrders,
      averageTime,
      updatedAt: now
    } : {
      ...DEFAULT_STATION_QUEUE_SUMMARY,
      _id: summaryId,
      stationId,
      stationName: stationId,
      totalOrders,
      pendingOrders,
      inProgressOrders,
      completedOrders,
      averageTime,
      createdAt: now,
      updatedAt: now
    };
    
    await safeUpsertDocument<StationQueueSummary>(
      summaryToSave,
      `updateStationSummary(${stationId})`
    );
  } catch (error) {
    console.error(`❌ [updateStationSummary] Error updating summary for station ${stationId}:`, error);
    throw error;
  }
}

/**
 * Create default station summary
 */
async function createDefaultStationSummary(stationId: string): Promise<StationQueueSummary> {
  const now = new Date().toISOString();
  const summaryId = generateStationQueueSummaryId(stationId);
  
  const summary: StationQueueSummary = {
    ...DEFAULT_STATION_QUEUE_SUMMARY,
    _id: summaryId,
    stationId,
    stationName: stationId,
    createdAt: now,
    updatedAt: now
  };
  
  const result = await databaseV4.putDoc(summary);
  return { ...summary, _rev: result.rev };
}

/**
 * Remove completed order from queue after delay
 */
async function removeCompletedOrderFromQueue(orderId: string, stationId: string): Promise<void> {
  try {
    const queueItemId = generateQueueItemId(stationId, orderId);
    const queueItem = await safeGetDocument<QueueItem>(queueItemId);
    
    if (queueItem.status === 'completed') {
      await databaseV4.deleteDoc(queueItem._id, queueItem._rev!);
      await updateStationSummary(stationId);
      console.log(`🗑️ [removeCompletedOrderFromQueue] Removed completed order ${orderId} from station ${stationId}`);
    }
  } catch (error: any) {
    if (error.status !== 404) {
      console.error(`❌ [removeCompletedOrderFromQueue] Error removing order ${orderId}:`, error);
    }
  }
}

/**
 * Calculate estimated completion time
 */
function calculateEstimatedTime(itemCount: number): number {
  const baseTimePerItem = 2; // 2 minutes per item
  const setupTime = 3; // 3 minutes setup time
  return (itemCount * baseTimePerItem) + setupTime;
}

/**
 * Calculate average completion time for queue items
 */
function calculateAverageTime(queueItems: QueueItem[]): number {
  const completedItems = queueItems.filter(item => 
    item.status === 'completed' && item.completedAt && item.createdAt
  );
  
  if (completedItems.length === 0) return 0;
  
  const totalTime = completedItems.reduce((sum, item) => {
    const created = new Date(item.createdAt).getTime();
    const completed = new Date(item.completedAt!).getTime();
    return sum + (completed - created);
  }, 0);
  
  return Math.round(totalTime / completedItems.length / 60000); // Convert to minutes
} 