"use client";

import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import {
  OrderDocument,
  OrderItem,
  Customer,
  DeliveryPerson,
  VoidEntry,
  DeliveryAttempt,
  DEFAULT_ORDER_DOCUMENT
} from '../schemas/order-schema';
import { getAllMenuItemRecipes } from './menu-item-recipe-ops';
import { getInventory, updateStockItem, createConsumptionLogsForOrder, calculateOrderCOGS, calculateMenuItemCost } from './inventory-ops';
import { getAllSubRecipes, updateSubRecipe } from './sub-recipe-ops';
import { createOrGetFreelancer } from './freelancer-ops';
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument
} from '../core/conflict-resolution';

/**
 * Order Operations for V4 Database
 *
 * This file contains all the database operations related to order management.
 */

const DAILY_RESET_HOUR = 5; // 5 AM

/**
 * Calculates the "business day" date and its start/end timestamps
 * based on a configurable daily reset hour.
 * Orders placed before the reset hour belong to the previous business day.
 */
function getBusinessDateRange() {
  const now = new Date();
  let businessDate = new Date(now);

  // If current hour is before the reset hour, shift to the previous calendar day
  if (now.getHours() < DAILY_RESET_HOUR) {
    businessDate.setDate(now.getDate() - 1);
  }

  // Format the business date as YYYYMMDD
  const year = businessDate.getFullYear();
  const month = (businessDate.getMonth() + 1).toString().padStart(2, '0');
  const day = businessDate.getDate().toString().padStart(2, '0');
  const dateStr = `${year}${month}${day}`;

  // Calculate start of business day (5 AM of the business date)
  const startOfBusinessDay = new Date(businessDate);
  startOfBusinessDay.setHours(DAILY_RESET_HOUR, 0, 0, 0);

  // Calculate end of business day (4:59:59.999 AM of the NEXT calendar day)
  const endOfBusinessDay = new Date(businessDate);
  endOfBusinessDay.setDate(businessDate.getDate() + 1); // Move to next calendar day
  endOfBusinessDay.setHours(DAILY_RESET_HOUR -1, 59, 59, 999); // Set to 4:59:59.999 AM

  return {
    dateStr: dateStr,
    startOfBusinessDay: startOfBusinessDay.toISOString(),
    endOfBusinessDay: endOfBusinessDay.toISOString(),
  };
}

/**
 * Create order indexes
 */
export async function createOrderIndexes(): Promise<void> {
  console.log('[createOrderIndexes] Starting order index creation');
  
  // Track success/failure of each index
  const results: {name: string, success: boolean, error?: any}[] = [];
  
  // Helper function for creating an index with retries
  async function createIndexWithRetry(fields: string[], name?: string, ddoc?: string) {
    const indexName = name || `unnamed-${fields.join('-')}`;
    const maxRetries = 3;
    let lastError: any = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[createOrderIndexes] Creating index '${indexName}' (attempt ${attempt}/${maxRetries})`);
        
        // Prepare index options based on whether name/ddoc are provided
        const indexOptions: PouchDB.Find.CreateIndexOptions = {
          index: { fields }
        };
        
        if (name && ddoc) {
          indexOptions.index.name = name;
          indexOptions.index.ddoc = ddoc;
        }
        
        await databaseV4.createIndex(indexOptions);
        console.log(`[createOrderIndexes] ✅ Successfully created index '${indexName}'`);
        return { name: indexName, success: true };
      } catch (error: any) {
        lastError = error;
        console.warn(`[createOrderIndexes] ⚠️ Failed to create index '${indexName}' (attempt ${attempt}/${maxRetries}): ${error.message}`);
        
        // 409 means index already exists, which is fine
        if (error.status === 409) {
          console.log(`[createOrderIndexes] Index '${indexName}' already exists (409), continuing`);
          return { name: indexName, success: true };
        }
        
        // For other errors, retry after a delay
        if (attempt < maxRetries) {
          const delay = 500 * attempt;
          console.log(`[createOrderIndexes] Retrying after ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // All retries failed
    return { name: indexName, success: false, error: lastError };
  }
  
  try {
    // Define the indexes to create
    const indexes = [
      { fields: ['type', 'status'], name: 'order-type-status-idx', ddoc: 'order-type-status-idx' },
      { fields: ['type', 'createdAt'], name: 'order-type-created-at-idx', ddoc: 'order-type-created-at-idx' },
      { fields: ['type', 'createdAt'] }, // Unnamed version
      { fields: ['type', 'tableId'], name: 'order-type-tableid-idx', ddoc: 'order-type-tableid-idx' },
      { fields: ['type', 'paymentStatus'], name: 'order-type-paymentstatus-idx', ddoc: 'order-type-paymentstatus-idx' },
      { fields: ['createdAt'], name: 'order-pure-created-at-idx', ddoc: 'order-pure-created-at-idx' },
      { fields: ['status', 'createdAt'], name: 'order-status-created-at-idx', ddoc: 'order-status-created-at-idx' },
      { fields: ['type', 'status', 'createdAt'], name: 'order-type-status-created-at-idx', ddoc: 'order-type-status-created-at-idx' }
    ];
    
    // Create all indexes sequentially to avoid overwhelming the database
    for (const index of indexes) {
      results.push(await createIndexWithRetry(index.fields, index.name, index.ddoc));
    }
    
    // Check results and report
    const failedIndexes = results.filter(r => !r.success);
    if (failedIndexes.length > 0) {
      console.error(`[createOrderIndexes] ❌ ${failedIndexes.length}/${results.length} order indexes failed to create:`);
      failedIndexes.forEach(r => console.error(`  - ${r.name}: ${r.error?.message || 'Unknown error'}`));
      console.warn(`[createOrderIndexes] Some order indexes failed, but operations will continue. Performance may be affected.`);
    } else {
      console.log(`[createOrderIndexes] ✅ All ${results.length} order indexes created successfully`);
    }
  } catch (error) {
    console.error('[createOrderIndexes] ❌ Unexpected error during order index creation:', error);
    throw error;
  }
}

/**
 * Generate a new order ID with date + daily sequence format: YYYYMMDD-XXX
 * Example: ************, ************, etc.
 */
async function generateOrderId(): Promise<string> {
  const { dateStr, startOfBusinessDay, endOfBusinessDay } = getBusinessDateRange();
  
  // Find all orders created within the current business day to determine the next sequence number
  const todaysOrders = await databaseV4.findDocs<OrderDocument>({
    selector: {
      type: 'order_document',
      createdAt: {
        $gte: startOfBusinessDay,
        $lte: endOfBusinessDay
      }
    }
  });
  
  // Extract sequence numbers from today's orders
  const sequenceNumbers = todaysOrders.docs
    .map(order => {
      // Extract sequence from order ID format: order:YYYYMMDD-XXX
      const match = order._id.match(/order:\d{8}-(\d+)$/);
      return match ? parseInt(match[1], 10) : 0;
    })
    .filter(seq => seq > 0);
  
  // Find the next available sequence number
  const maxSequence = sequenceNumbers.length > 0 ? Math.max(...sequenceNumbers) : 0;
  const nextSequence = maxSequence + 1;
  
  // Format sequence to exactly 3 digits (001, 002, etc.) to match schema pattern
  const sequenceStr = nextSequence.toString().padStart(3, '0');
  
  return `order:${dateStr}-${sequenceStr}`;
}

/**
 * Extract daily sequence from order ID - NEW FORMAT ONLY
 * Example: "order:************" -> "001"
 */
export function extractDailySequence(orderId: string): string {
  console.log(`🔍 [extractDailySequence] Processing order ID: ${orderId}`);
  
  // Handle new format: order:YYYYMMDD-XXX
  const newFormatMatch = orderId.match(/^order:\d{8}-(\d+)$/);
  if (newFormatMatch) {
    const sequence = newFormatMatch[1].padStart(3, '0');
    console.log(`✅ [extractDailySequence] New format matched, sequence: ${sequence}`);
    return sequence;
  }
  
  // Handle legacy timestamp format for backward compatibility
  const timestampMatch = orderId.match(/^order:(\d+)$/);
  if (timestampMatch) {
    console.log(`⚠️ [extractDailySequence] Legacy timestamp format detected: ${orderId}`);
    // For legacy format, try to extract a meaningful sequence
    const timestamp = parseInt(timestampMatch[1]);
    const date = new Date(timestamp);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    // Create a pseudo-sequence based on time (not perfect but better than 001)
    const pseudoSequence = Math.floor((hours * 60 + minutes) / 10) + 1; // Every 10 minutes = new sequence
    const formattedSequence = pseudoSequence.toString().padStart(3, '0');
    console.log(`🔄 [extractDailySequence] Generated pseudo-sequence: ${formattedSequence}`);
    return formattedSequence;
  }
  
  // Handle any other format variations
  const generalMatch = orderId.match(/(\d+)$/);
  if (generalMatch) {
    const lastNumber = generalMatch[1];
    const sequence = lastNumber.padStart(3, '0');
    console.log(`🔧 [extractDailySequence] General number extraction: ${sequence}`);
    return sequence;
  }
  
  console.warn(`❌ [extractDailySequence] No pattern matched for order ID: ${orderId}, falling back to 001`);
  return '001';
}

/**
 * Extract order date from order ID - NEW FORMAT ONLY  
 * Example: "order:************" -> "20241215"
 */
export function extractOrderDate(orderId: string): string | null {
  const match = orderId.match(/^order:(\d{8})-\d+$/);
  return match ? match[1] : null;
}

/**
 * Create a new order
 */
export async function createOrder(orderData: Omit<OrderDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'>): Promise<OrderDocument> {
  console.log('[createOrder] 🎯 Starting order creation process...');
  console.log('[createOrder] Input data:', {
    customerName: orderData.customer?.name,
    itemCount: orderData.items?.length,
    total: orderData.total,
    orderType: orderData.orderType,
    status: orderData.status
  });
  
  try {
    const now = new Date().toISOString();
    const orderId = await generateOrderId();
    console.log('[createOrder] 📋 Generated order ID:', orderId);

    // Track creation time for indexing detection
    if (typeof window !== 'undefined') {
      localStorage.setItem('lastOrderCreationTime', Date.now().toString());
    }

    // Create order document
    const order: OrderDocument = {
      _id: orderId,
      type: 'order_document',
      schemaVersion: 'v4.0',
      createdAt: now,
      updatedAt: now,
      ...orderData
    };
    console.log('[createOrder] 📄 Constructed order document:', {
      id: order._id,
      type: order.type,
      status: order.status,
      total: order.total,
      itemCount: order.items?.length,
      createdAt: order.createdAt
    });

    // Save to database
    console.log('[createOrder] 💾 Saving order to database...');
    const putResponse = await databaseV4.putDoc(order);
    console.log('[createOrder] ✅ Order saved successfully:', {
      id: putResponse.id,
      rev: putResponse.rev,
      ok: putResponse.ok
    });

    // Verify the order was saved by trying to retrieve it
    console.log('[createOrder] 🔍 Verifying order was saved...');
    try {
      const retrievedOrder = await databaseV4.getDoc<OrderDocument>(orderId);
      console.log('[createOrder] ✅ Order verification successful:', {
        retrievedId: retrievedOrder._id,
        retrievedStatus: retrievedOrder.status,
        retrievedTotal: retrievedOrder.total,
        hasRev: !!retrievedOrder._rev
      });
      
      // Return the retrieved order to ensure we have the latest _rev
      const finalOrder = {
        ...retrievedOrder,
        _rev: putResponse.rev // Use the rev from putDoc response
      };
      
      console.log('[createOrder] 🎉 Order creation completed successfully:', {
        id: finalOrder._id,
        status: finalOrder.status,
        total: finalOrder.total
      });
      
      return finalOrder;
    } catch (verificationError) {
      console.error('[createOrder] ⚠️ Order verification failed, but order was saved:', verificationError);
      console.log('[createOrder] 🔄 Falling back to constructed order with putDoc response...');
      
      // Return the constructed order with the _rev from putDoc
      const fallbackOrder = {
        ...order,
        _rev: putResponse.rev
      };
      
      console.log('[createOrder] 📤 Returning fallback order:', {
        id: fallbackOrder._id,
        hasRev: !!fallbackOrder._rev
      });
      
      return fallbackOrder;
    }
  } catch (error) {
    console.error('[createOrder] ❌ Error creating order:', error);
    console.error('[createOrder] Error details:', {
      message: (error as any)?.message,
      status: (error as any)?.status,
      name: (error as any)?.name,
      stack: (error as any)?.stack
    });
    throw error;
  }
}

/**
 * Get an order by ID
 */
export async function getOrder(orderId: string): Promise<OrderDocument> {
  try {
    return await databaseV4.getDoc<OrderDocument>(orderId);
  } catch (error: any) {
    if (error.status === 404) {
      throw new Error(`Order with ID ${orderId} not found`);
    }
    throw error;
  }
}

/**
 * Get all orders - NEW FORMAT ONLY
 */
export async function getAllOrders(): Promise<OrderDocument[]> {
  console.log('[getAllOrders] Fetching orders (NEW FORMAT ONLY)...');
  
  const { performanceMonitor } = await import('@/lib/utils/performance-monitor');
  
  return performanceMonitor.timeOperation('getAllOrders', async () => {
    const result = await databaseV4.findDocs<OrderDocument>({
      selector: {
        type: 'order_document'
      },
      limit: 10000 // Override default 25-doc limit so we always fetch all orders
    });
    
    console.log(`[getAllOrders] Found ${result.docs.length} orders`);
    
    // Sort by createdAt descending (newest first)
    const sortedOrders = (result.docs || []).sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    
    return sortedOrders;
  }, { orderCount: 0 }); // Will be updated with actual count
}

/**
 * Get active orders (not completed or cancelled)
 */
export async function getActiveOrders(): Promise<OrderDocument[]> {
  try {
    const selector = {
      type: 'order_document',
      status: { $in: ['pending', 'preparing', 'served'] }
    };
    const indexName = 'order-type-status-created-at-idx';

    console.log(`[getActiveOrders] Query with selector: ${JSON.stringify(selector)}, using index: ${indexName}`);
    const result = await databaseV4.findDocs<OrderDocument>({
      selector,
      sort: [{ createdAt: 'desc' }],
      use_index: [indexName, indexName] // Specify the ddoc and index name
    });

    return result.docs;
  } catch (error) {
    console.error('Error getting active orders:', error);
    throw error;
  }
}

/**
 * Get orders by status
 */
export async function getOrdersByStatus(status: OrderDocument['status']): Promise<OrderDocument[]> {
  try {
    const selector = {
      type: 'order_document',
      status: status
    };
    const indexName = 'order-type-status-created-at-idx';

    console.log(`[getOrdersByStatus] Query for status: ${status} with selector: ${JSON.stringify(selector)}, using index: ${indexName}`);
    const result = await databaseV4.findDocs<OrderDocument>({
      selector,
      sort: [{ createdAt: 'desc' }],
      use_index: [indexName, indexName] // Specify the ddoc and index name
    });

    return result.docs;
  } catch (error) {
    console.error(`Error getting orders with status ${status}:`, error);
    throw error;
  }
}

/**
 * Get orders by table ID
 */
export async function getOrdersByTable(tableId: string): Promise<OrderDocument[]> {
  try {
    const originalSelector = {
      type: 'order_document',
      tableId: tableId
    };
    const dbQuerySelector = { createdAt: { $gte: null } }; // Broad selector for DB
    const indexName = 'order-pure-created-at-idx';

    console.log(`[getOrdersByTable] For tableId: ${tableId}. DB query selector: ${JSON.stringify(dbQuerySelector)}, using index: ${indexName}`);
    const result = await databaseV4.findDocs<OrderDocument>({
      selector: dbQuerySelector,
      sort: [{ createdAt: 'desc' }],
      use_index: [indexName, indexName]
    });

    // Manually filter for type and tableId
    const filteredDocs = result.docs.filter(doc => doc.type === 'order_document' && doc.tableId === tableId);
    console.log(`[getOrdersByTable] For tableId: ${tableId}. Raw: ${result.docs.length}, Filtered: ${filteredDocs.length}`);

    return filteredDocs;
  } catch (error) {
    console.error(`Error getting orders for table ${tableId}:`, error);
    throw error;
  }
}

/**
 * Get orders by date range
 */
export async function getOrdersByDateRange(startDate: string, endDate: string): Promise<OrderDocument[]> {
  try {
    const selector = {
      type: 'order_document',
      createdAt: {
        $gte: startDate,
        $lte: endDate
      }
    };

    // Use the createdAt index for sorting
    const result = await databaseV4.findDocs<OrderDocument>({
      selector,
      sort: [{ createdAt: 'desc' }],
    });

    return result.docs;
  } catch (error) {
    console.error(`Error getting orders in date range:`, error);
    throw error;
  }
}

/**
 * Update order status with validation
 */
export async function updateOrderStatus(orderId: string, status: OrderDocument['status']): Promise<OrderDocument> {
  return retryWithConflictResolution(async () => {
    console.log(`[updateOrderStatus] Updating order ${orderId} status to ${status}`);
    
    // Get current order to validate status transition
    const currentOrder = await safeGetDocument<OrderDocument>(orderId);
    
    // Check if order has a valid status field
    if (!currentOrder.status) {
      console.warn(`[updateOrderStatus] Order ${orderId} has no status field, defaulting to 'pending'`);
      currentOrder.status = 'pending';
    }
    
    // Validate status transition
    const { validateStatusTransition } = await import('../utils/order-status-validation');
    validateStatusTransition(currentOrder.status, status, orderId);
    
    // If completing the order, calculate COGS and profit metrics
    if (status === 'completed') {
      console.log(`[updateOrderStatus] Order ${orderId} being completed, calculating COGS and creating consumption logs`);
      
      // Get the current order with fresh revision
      const order = await safeGetDocument<OrderDocument>(orderId);
      
      // Safe stock consumption to prevent double consumption
      const { safeConsumeOrderStock } = await import('../utils/stock-consumption-tracker');
      
      await safeConsumeOrderStock(orderId, async () => {
        // Create consumption logs for all items in the order
        await createConsumptionLogsForOrder(
          orderId,
          order.items.map(item => ({
            menuItemId: item.menuItemId,
            name: item.name,
            quantity: item.quantity,
            size: item.size,
            compositeType: item.compositeType,
            quarters: item.quarters
          })),
          order.createdAt
        );
        
        // Legacy consumption removed - using new consumption log system only
      }, 'new_system');
      
      // COGS calculation moved to simplified-order-finance service for consistency
      
      // Update freelancer stats if this is a delivery order with freelance driver
      if (order.orderType === 'delivery' && 
          order.deliveryPerson?.type === 'freelance' && 
          order.deliveryPerson.phone) {
        
        try {
          console.log('📊 Updating freelancer stats for completed order:', {
            phone: order.deliveryPerson.phone,
            orderValue: order.total
          });
          
          const { updateFreelancerStats } = await import('./freelancer-ops');
          await updateFreelancerStats(order.deliveryPerson.phone, order.total);
          console.log('✅ Freelancer stats updated successfully');
        } catch (freelancerError) {
          console.error('⚠️ Error updating freelancer stats (continuing with order completion):', freelancerError);
          // Don't fail the order completion if freelancer stats update fails
        }
      }
      
      // Update order with status - COGS calculation handled by finance service
      const updatedOrder = await safeUpdateDocument<OrderDocument>(
        orderId,
        (order) => ({
          ...order,
          status,
          updatedAt: new Date().toISOString()
        }),
        `updateOrderStatus(${orderId})`
      );
      
      return updatedOrder;
    } else {
      // For non-completion status updates, just update the status
      const updatedOrder = await safeUpdateDocument<OrderDocument>(
        orderId,
        (order) => ({
          ...order,
          status,
          updatedAt: new Date().toISOString()
        }),
        `updateOrderStatus(${orderId})`
      );
      
      return updatedOrder;
    }
  }, 3, `updateOrderStatus(${orderId})`);
}

/**
 * Update order with conflict resolution and status validation
 */
export async function updateOrder(orderId: string, updates: Partial<OrderDocument>): Promise<OrderDocument> {
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      // If status is being updated, validate the transition
      if (updates.status && updates.status !== order.status) {
        const { validateStatusTransition } = require('../utils/order-status-validation');
        const currentStatus = order.status || 'pending'; // Default to 'pending' if status is undefined
        validateStatusTransition(currentStatus, updates.status, orderId);
      }
      
      return {
        ...order,
        ...updates,
        _id: order._id, // Ensure ID doesn't change
        type: 'order_document', // Ensure type doesn't change
        schemaVersion: 'v4.0', // Ensure schema version doesn't change
        updatedAt: new Date().toISOString()
      };
    },
    `updateOrder(${orderId})`
  );
}

/**
 * Add item to order with conflict resolution
 */
export async function addItemToOrder(orderId: string, item: OrderItem): Promise<OrderDocument> {
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      // Add item to items array
      const updatedItems = [...order.items, item];
      // Recalculate total
      const total = calculateOrderTotal(updatedItems);
      
      return {
        ...order,
        items: updatedItems,
        total,
        updatedAt: new Date().toISOString()
      };
    },
    `addItemToOrder(${orderId})`
  );
}

/**
 * Update item in order with conflict resolution
 */
export async function updateOrderItem(orderId: string, itemId: string, updates: Partial<OrderItem>): Promise<OrderDocument> {
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      // Find item index
      const itemIndex = order.items.findIndex(item => item.id === itemId);
      if (itemIndex === -1) {
        throw new Error(`Item with ID ${itemId} not found in order ${orderId}`);
      }

      // Update item
      const updatedItems = [...order.items];
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        ...updates
      };

      // Recalculate total
      const total = calculateOrderTotal(updatedItems);

      return {
        ...order,
        items: updatedItems,
        total,
        updatedAt: new Date().toISOString()
      };
    },
    `updateOrderItem(${orderId})`
  );
}

/**
 * Remove item from order with conflict resolution
 */
export async function removeItemFromOrder(orderId: string, itemId: string): Promise<OrderDocument> {
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      // Remove item
      const updatedItems = order.items.filter(item => item.id !== itemId);
      // Recalculate total
      const total = calculateOrderTotal(updatedItems);

      return {
        ...order,
        items: updatedItems,
        total,
        updatedAt: new Date().toISOString()
      };
    },
    `removeItemFromOrder(${orderId})`
  );
}

/**
 * Update customer information with conflict resolution
 */
export async function updateOrderCustomer(orderId: string, customer: Customer): Promise<OrderDocument> {
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => ({
      ...order,
      customer,
      updatedAt: new Date().toISOString()
    }),
    `updateOrderCustomer(${orderId})`
  );
}

/**
 * Update delivery person information with conflict resolution
 */
export async function updateOrderDeliveryPerson(orderId: string, deliveryPerson: DeliveryPerson): Promise<OrderDocument> {
  console.log(`[updateOrderDeliveryPerson] Updating order ${orderId} delivery person:`, deliveryPerson);
  
  // Auto-create freelancer if this is a freelance delivery person
  if (deliveryPerson.type === 'freelance' && deliveryPerson.phone) {
    try {
      console.log('🚚 Auto-creating freelancer for delivery person update:', {
        name: deliveryPerson.name,
        phone: deliveryPerson.phone
      });
      
      // Create or get the freelancer - this ensures they exist in the database
      await createOrGetFreelancer(deliveryPerson.name, deliveryPerson.phone);
      console.log('✅ Freelancer auto-created/retrieved successfully');
    } catch (freelancerError) {
      console.error('⚠️ Error auto-creating freelancer (continuing with update):', freelancerError);
      // Don't fail the delivery person update if freelancer creation fails
    }
  }
  
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => ({
      ...order,
      deliveryPerson,
      updatedAt: new Date().toISOString()
    }),
    `updateOrderDeliveryPerson(${orderId})`
  );
}


/**
 * Delete an order
 */
export async function deleteOrder(orderId: string): Promise<boolean> {
  try {
    // Get current order to get the revision
    const order = await getOrder(orderId);

    // Delete the order
    await databaseV4.deleteDoc(orderId, order._rev!);

    return true;
  } catch (error) {
    console.error(`Error deleting order ${orderId}:`, error);
    throw error;
  }
}

/**
 * Helper function to calculate order total
 */
function calculateOrderTotal(items: OrderItem[]): number {
  return items.reduce((total, item) => {
    // Calculate item subtotal (price * quantity)
    let itemTotal = item.price * item.quantity;

    // Add addon prices if any
    if (item.addons && item.addons.length > 0) {
      const addonTotal = item.addons.reduce((sum, addon) => sum + addon.price, 0);
      itemTotal += addonTotal * item.quantity;
    }

    return total + itemTotal;
  }, 0);
}

// Legacy consumeOrderStockV4 function removed - using new consumption log system only

/**
 * Process supplement stock consumption for order addons
 */
export async function processOrderSupplementConsumption(orderId: string, order: OrderDocument): Promise<void> {
  console.log(`[processOrderSupplementConsumption] Start for order: ${orderId}`);
  
  try {
    const { processSupplementConsumption, findSupplementById } = await import('../operations/supplement-ops');
    
    // Process each item in the order
    for (const item of order.items) {
      if (!item.addons || item.addons.length === 0) continue;
      
      // Process each addon to check if it's a supplement
      for (const addon of item.addons) {
        // Check if this addon references a supplement by trying to find it in the supplements collection
        const supplementResult = await findSupplementById(addon.id);
        
        if (supplementResult) {
          const { supplement, categoryId } = supplementResult;
          console.log(`[processOrderSupplementConsumption] Processing supplement: ${supplement.name} for item: ${item.name}`);
          
          try {
            // Process stock consumption for this supplement
            await processSupplementConsumption(
              categoryId,
              supplement.id,
              item.size || 'default',
              item.quantity,
              orderId
            );
            
            console.log(`[processOrderSupplementConsumption] Successfully processed supplement ${supplement.name}`);
          } catch (error) {
            console.error(`[processOrderSupplementConsumption] Error processing supplement ${supplement.name}:`, error);
          }
        } else {
          console.log(`[processOrderSupplementConsumption] Skipping regular addon: ${addon.name}`);
        }
      }
    }
    
    console.log(`[processOrderSupplementConsumption] Done for order: ${orderId}`);
  } catch (error) {
    console.error(`[processOrderSupplementConsumption] Error processing supplement consumption for order ${orderId}:`, error);
  }
}
// --- end knowledge ---

/**
 * Void items from an order with detailed audit trail
 */
export async function voidOrderItems(
  orderId: string, 
  voidItems: Array<{ itemIndex: number; quantity: number }>,
  voidReason: string,
  voidedBy?: string,
  voidedByName?: string
): Promise<OrderDocument> {
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      const now = new Date().toISOString();
      
      // Store original total if this is the first void
      const originalTotal = order.originalTotal || order.total;
      
      // Calculate voided amount for this operation
      let totalVoidedInThisOperation = 0;
      const voidedItemsDetails: Array<{
        itemIndex: number;
        itemId: string;
        itemName: string;
        quantityVoided: number;
        pricePerUnit: number;
        totalVoidedAmount: number;
      }> = [];

      // Update items and calculate voided amounts
      const updatedItems = order.items.map((item, index) => {
        const voidItem = voidItems.find(v => v.itemIndex === index);
        if (voidItem) {
          // Calculate item price including addons
          let itemPrice = item.price;
          if (item.addons && item.addons.length > 0) {
            itemPrice += item.addons.reduce((sum, addon) => sum + addon.price, 0);
          }
          
          const totalVoidedForThisItem = itemPrice * voidItem.quantity;
          totalVoidedInThisOperation += totalVoidedForThisItem;
          
          // Track voided item details
          voidedItemsDetails.push({
            itemIndex: index,
            itemId: item.id,
            itemName: item.name,
            quantityVoided: voidItem.quantity,
            pricePerUnit: itemPrice,
            totalVoidedAmount: totalVoidedForThisItem
          });

          // Update item with void tracking
          const originalQuantity = item.originalQuantity || item.quantity;
          const newQuantity = item.quantity - voidItem.quantity;
          const voidedQuantity = (item.voidedQuantity || 0) + voidItem.quantity;
          
          if (newQuantity > 0) {
            return {
              ...item,
              quantity: newQuantity,
              originalQuantity,
              voidedQuantity,
              isVoided: true
            };
          } else {
            // Item completely voided, but keep it for audit trail
            return {
              ...item,
              quantity: 0,
              originalQuantity,
              voidedQuantity,
              isVoided: true
            };
          }
        }
        return item;
      }).filter(item => item.quantity > 0); // Remove completely voided items

      // Calculate new total
      const newTotal = calculateOrderTotal(updatedItems);
      
      // Create void entry for history
      const voidEntry = {
        voidedAt: now,
        reason: voidReason,
        voidedBy,
        voidedByName,
        voidedItems: voidedItemsDetails,
        totalVoidedAmount: totalVoidedInThisOperation
      };

      // Update void tracking fields
      const currentTotalVoided = (order.totalVoidedAmount || 0) + totalVoidedInThisOperation;
      const voidHistory = [...(order.voidHistory || []), voidEntry];
      
      // Update notes with void information
      const voidNote = `[${now}] Voided items: ${voidReason} (${voidedItemsDetails.length} item(s), -${totalVoidedInThisOperation} DA)`;
      const updatedNotes = order.notes ? `${order.notes}\n${voidNote}` : voidNote;

      return {
        ...order,
        items: updatedItems,
        total: newTotal,
        originalTotal,
        hasVoids: true,
        totalVoidedAmount: currentTotalVoided,
        voidHistory,
        notes: updatedNotes,
        updatedAt: now
      };
    },
    `voidOrderItems(${orderId})`
  );
}

/**
 * Get void history for an order
 */
export async function getOrderVoidHistory(orderId: string): Promise<VoidEntry[]> {
  try {
    const order = await getOrder(orderId);
    return order.voidHistory || [];
  } catch (error) {
    console.error('[getOrderVoidHistory] Error getting void history:', error);
    throw error;
  }
}

/**
 * Get void statistics for a date range
 */
export async function getVoidStatistics(startDate: string, endDate: string): Promise<{
  totalVoids: number;
  totalVoidedAmount: number;
  ordersWithVoids: number;
  mostCommonReasons: Array<{ reason: string; count: number; totalAmount: number }>;
}> {
  try {
    const orders = await getOrdersByDateRange(startDate, endDate);
    const ordersWithVoids = orders.filter(order => order.hasVoids);
    
    let totalVoids = 0;
    let totalVoidedAmount = 0;
    const reasonCounts: Record<string, { count: number; totalAmount: number }> = {};
    
    ordersWithVoids.forEach(order => {
      if (order.voidHistory) {
        order.voidHistory.forEach(voidEntry => {
          totalVoids += voidEntry.voidedItems.length;
          totalVoidedAmount += voidEntry.totalVoidedAmount;
          
          // Track reasons
          const reason = voidEntry.reason.toLowerCase().trim();
          if (!reasonCounts[reason]) {
            reasonCounts[reason] = { count: 0, totalAmount: 0 };
          }
          reasonCounts[reason].count += 1;
          reasonCounts[reason].totalAmount += voidEntry.totalVoidedAmount;
        });
      }
    });
    
    // Sort reasons by frequency
    const mostCommonReasons = Object.entries(reasonCounts)
      .map(([reason, data]) => ({ reason, ...data }))
      .sort((a, b) => b.count - a.count);
    
    return {
      totalVoids,
      totalVoidedAmount,
      ordersWithVoids: ordersWithVoids.length,
      mostCommonReasons
    };
  } catch (error) {
    console.error('[getVoidStatistics] Error getting void statistics:', error);
    throw error;
  }
}

/**
 * 🗑️ DELETE ALL OLD FORMAT ORDERS
 * This removes all orders with timestamp-based IDs (old format)
 * Only keeps orders with date-sequence format (order:YYYYMMDD-XXX)
 */
export async function purgeOldFormatOrders(): Promise<{ deleted: number; kept: number }> {
  console.log('[purgeOldFormatOrders] 🗑️ Starting purge of old format orders...');
  
  try {
    // Get ALL documents to find old format orders
    const result = await databaseV4.findDocs<any>({
      selector: { _id: { $gt: null } }
    });
    
    console.log(`[purgeOldFormatOrders] Found ${result.docs.length} total documents`);
    
    // Filter for old format order documents (timestamp-based IDs)
    const oldFormatOrders = result.docs.filter((doc: any) => {
      return doc.type === 'order_document' && 
             doc._id.startsWith('order:') && 
             !doc._id.match(/^order:\d{8}-\d+$/); // NOT new format
    });
    
    const newFormatOrders = result.docs.filter((doc: any) => {
      return doc.type === 'order_document' && 
             doc._id.match(/^order:\d{8}-\d+$/); // IS new format
    });
    
    console.log(`[purgeOldFormatOrders] Found ${oldFormatOrders.length} old format orders to delete`);
    console.log(`[purgeOldFormatOrders] Found ${newFormatOrders.length} new format orders to keep`);
    
    // Delete all old format orders
    let deletedCount = 0;
    for (const oldOrder of oldFormatOrders) {
      try {
        await databaseV4.deleteDoc(oldOrder._id, oldOrder._rev);
        deletedCount++;
        console.log(`[purgeOldFormatOrders] ✅ Deleted old order: ${oldOrder._id}`);
      } catch (deleteError) {
        console.error(`[purgeOldFormatOrders] ❌ Failed to delete ${oldOrder._id}:`, deleteError);
      }
    }
    
    console.log(`[purgeOldFormatOrders] 🎉 Purge complete: ${deletedCount} deleted, ${newFormatOrders.length} kept`);
    
    return {
      deleted: deletedCount,
      kept: newFormatOrders.length
    };
  } catch (error) {
    console.error('[purgeOldFormatOrders] ❌ Error during purge:', error);
    throw error;
  }
}

/**
 * 🚀 NEW: Update delivery status for an order
 */
export async function updateDeliveryStatus(
  orderId: string, 
  status: 'pending' | 'out_for_delivery' | 'delivered' | 'failed' | 'partially_delivered',
  attemptData?: {
    failureReason?: string;
    deliveredItems?: string[];
    failedItems?: string[];
    attemptedBy?: string;
    notes?: string;
  }
): Promise<OrderDocument> {
  console.log(`[updateDeliveryStatus] Updating delivery status for order ${orderId} to ${status}`);
  
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      // Update delivery status
      order.deliveryStatus = status;
      
      // Add delivery attempt record
      if (!order.deliveryAttempts) {
        order.deliveryAttempts = [];
      }
      
      const attempt: DeliveryAttempt = {
        attemptedAt: new Date().toISOString(),
        status: status === 'out_for_delivery' ? 'delivered' : status as any,
        failureReason: attemptData?.failureReason,
        failedItems: attemptData?.failedItems?.map(itemId => {
          const item = order.items.find(i => i.id === itemId);
          return {
            itemId,
            itemName: item?.name || 'Unknown Item',
            quantity: item?.quantity || 1,
            reason: attemptData?.failureReason || 'Delivery failed'
          };
        }) || [],
        notes: attemptData?.notes
      };
      
      order.deliveryAttempts.push(attempt);
      
      // Initialize collection status for delivery orders that need collection
      if (order.orderType === 'delivery' && 
          order.deliveryPerson?.paymentModel === 'collection' && 
          (status === 'delivered' || status === 'partially_delivered')) {
        
        // Calculate expected amount based on delivery success
        let expectedAmount = order.total;
        if (status === 'partially_delivered' && attemptData?.deliveredItems) {
          // Calculate amount for delivered items only
          expectedAmount = order.items
            .filter(item => attemptData.deliveredItems?.includes(item.id))
            .reduce((sum, item) => {
              const itemPrice = item.price + (item.addons?.reduce((addonSum, addon) => addonSum + addon.price, 0) || 0);
              return sum + (itemPrice * item.quantity);
            }, 0);
        }
        
        order.collectionStatus = {
          isPending: true,
          expectedAmount: expectedAmount,
          manualExpenses: []
        };
      }
      
      order.updatedAt = new Date().toISOString();
      return order;
    },
    `updateDeliveryStatus(${orderId})`
  );
}

/**
 * 🚀 NEW: Process delivery failure and create waste entries
 */
export async function processDeliveryFailure(
  orderId: string,
  failedItems: Array<{
    itemIndex: number;
    quantity: number;
  }>,
  failureReason: string,
  attemptedBy?: string
): Promise<OrderDocument> {
  console.log(`[processDeliveryFailure] Processing delivery failure for order ${orderId}`);
  
  try {
    const order = await getOrder(orderId);
    
    // Prepare waste data for failed items
    const wastedMenuItems = failedItems.map(failedItem => {
      const item = order.items[failedItem.itemIndex];
      return {
        name: item.name,
        quantity: failedItem.quantity,
        originalQuantity: item.quantity,
        price: item.price,
        addons: item.addons,
        menuItemId: item.menuItemId
      };
    });
    
    // Calculate total waste value using COST instead of selling price
    let totalWasteValue = 0;
    for (const failedItem of failedItems) {
      const item = order.items[failedItem.itemIndex];
      // Calculate cost value for this menu item
      const itemCostValue = await calculateMenuItemCost(
        item.menuItemId, 
        failedItem.quantity, 
        item.addons
      );
      totalWasteValue += itemCostValue;
    }
    
    // Process waste using existing system
    const { processMenuItemWaste, processWastedMenuItemStockConsumption } = await import('./inventory-ops');
    
    // Create menu item waste entries
    await processMenuItemWaste(
      orderId,
      wastedMenuItems,
      `Delivery failure: ${failureReason}`,
      totalWasteValue
    );
    
    // Process stock consumption for failed items
    await processWastedMenuItemStockConsumption(
      failedItems,
      order.items.map(item => ({
        menuItemId: item.menuItemId,
        name: item.name,
        quantity: item.quantity,
        size: item.size,
        addons: item.addons
      }))
    );
    
    // Determine delivery status
    const allItemsFailed = failedItems.reduce((sum, f) => sum + f.quantity, 0) === 
                          order.items.reduce((sum, item) => sum + item.quantity, 0);
    
    const deliveryStatus = allItemsFailed ? 'failed' : 'partially_delivered';
    
    // Get delivered items (items not in failed list)
    const deliveredItems = order.items
      .filter((_, index) => !failedItems.some(f => f.itemIndex === index))
      .map(item => item.id);
    
    const failedItemIds = failedItems.map(f => order.items[f.itemIndex].id);
    
    // Update delivery status
    return await updateDeliveryStatus(orderId, deliveryStatus, {
      failureReason,
      deliveredItems,
      failedItems: failedItemIds,
      attemptedBy,
      notes: `Waste processed for ${failedItems.length} failed items`
    });
    
  } catch (error) {
    console.error(`Error processing delivery failure for order ${orderId}:`, error);
    throw error;
  }
}

/**
 * 🚀 NEW: Update collection status for an order
 */
export async function updateCollectionStatus(
  orderId: string,
  collectionData: {
    actualAmount: number;
    collectedBy: string;
    discrepancyReason?: string;
    manualExpenses?: Array<{
      description: string;
      amount: number;
      reason: string;
    }>;
  }
): Promise<OrderDocument> {
  console.log(`[updateCollectionStatus] Updating collection for order ${orderId}`);
  
  return safeUpdateDocument<OrderDocument>(
    orderId,
    (order) => {
      if (!order.collectionStatus) {
        throw new Error('Order does not have collection status initialized');
      }
      
      const manualExpenseTotal = collectionData.manualExpenses?.reduce((sum, exp) => sum + exp.amount, 0) || 0;
      const adjustedExpected = order.collectionStatus.expectedAmount - manualExpenseTotal;
      const discrepancy = collectionData.actualAmount - adjustedExpected;
      
      order.collectionStatus = {
        ...order.collectionStatus,
        isPending: false,
        actualAmount: collectionData.actualAmount,
        collectedAt: new Date().toISOString(),
        collectedBy: collectionData.collectedBy,
        discrepancy,
        discrepancyReason: discrepancy !== 0 ? collectionData.discrepancyReason : undefined,
        manualExpenses: collectionData.manualExpenses?.map(expense => ({
          ...expense,
          addedAt: new Date().toISOString(),
          addedBy: collectionData.collectedBy
        })) || []
      };
      
      order.updatedAt = new Date().toISOString();
      return order;
    },
    `updateCollectionStatus(${orderId})`
  );
}
