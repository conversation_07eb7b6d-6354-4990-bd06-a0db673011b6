/**
 * Cash Session Operations for V4 Database
 * 
 * CORRECTED FLOW: Track actual drawer amount, not expected calculations
 * - Session starts with current drawer amount at that moment
 * - Drawer amount = what's physically in the drawer (reality)
 * - Session tracks: starting amount + sales (excluding delivery) + manual transactions
 * - Current drawer amount is what we calculate and look for
 */

"use client";

import { databaseV4 } from '../core/db-instance';
import { CashSessionDocument, CashCountDocument } from '../schemas/cash-session-schema';
import { getAllCashTransactions } from './cash-ops';
import { retryWithConflictResolution } from '../core/conflict-resolution';

/**
 * Get current drawer state - shows actual physical money in drawer
 * This is the CURRENT REALITY, not an expectation
 */
export async function getCurrentDrawerState(): Promise<{
  currentAmount: number;
  transactionCount: number;
  salesAmount: number;
  manualAmount: number;
}> {
  try {
    console.log('[getCurrentDrawerState] Getting current drawer state...');
    
    // Get all transactions to calculate current drawer amount
    const transactions = await getAllCashTransactions();
    console.log('[getCurrentDrawerState] Total transactions found:', transactions.length);
    
    // Calculate sales amount (including delivery collections made in ordering page)
    const salesAmount = transactions
      .filter(tx => {
        const tType = (tx as any).transactionType ?? (tx as any).type;
        const metadata = (tx as any).metadata;
        
        // Include all sales transactions
        if (tType === 'sales') {
          // Include regular order payments (including prepaid deliveries)
          if (metadata?.transactionCategory === 'order_payment') {
            return true;
          }
          // Include delivery collections made in ordering page
          if (metadata?.transactionCategory === 'delivery_collection' && metadata?.collectedInOrderingPage) {
            return true;
          }
          // Exclude delivery collections from separate collection system
          if (metadata?.transactionCategory === 'delivery_collection' && !metadata?.collectedInOrderingPage) {
            return false;
          }
          // Include other sales transactions for backward compatibility
          return true;
        }
        
        return false;
      })
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Calculate manual transactions (deposits/withdrawals)
    const manualAmount = transactions
      .filter(tx => {
        const tType = (tx as any).transactionType ?? (tx as any).type;
        return tType === 'manual_in' || tType === 'manual_out';
      })
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Current drawer amount = sales + manual transactions
    // This represents what should physically be in the drawer right now
    const currentAmount = salesAmount + manualAmount;

    console.log('[getCurrentDrawerState] Drawer calculation:', {
      salesAmount: `${salesAmount} DA (including delivery collections from ordering page)`,
      manualAmount: `${manualAmount} DA`,
      currentAmount: `${currentAmount} DA (physical drawer)`,
      transactionCount: transactions.length
    });

    return {
      currentAmount,
      transactionCount: transactions.length,
      salesAmount,
      manualAmount
    };
  } catch (error) {
    console.error('[getCurrentDrawerState] Error:', error);
    throw error;
  }
}

/**
 * Get current session (always exists, auto-created if needed)
 */
export async function getCurrentSession(): Promise<CashSessionDocument> {
  try {
    // Look for existing open session
    const result = await databaseV4.findDocs({
      selector: {
        type: 'cash_session',
        status: 'open'
      }
    });

    const openSessions = result.docs as CashSessionDocument[];
    
    if (openSessions.length > 1) {
      console.warn('[getCurrentSession] Multiple open sessions found, using most recent');
    }
    
    if (openSessions.length > 0) {
      return openSessions[0];
    }

    // No session exists, create one automatically with current drawer amount
    console.log('[getCurrentSession] No session found, creating automatic session...');
    
    const drawerState = await getCurrentDrawerState();
    const sessionId = `cash_session:${Date.now()}`;
    
    const session: CashSessionDocument = {
      _id: sessionId,
      type: 'cash_session',
      staffId: 'auto',
      staffName: 'Session Auto',
      openedAt: new Date().toISOString(),
      openedBy: 'System',
      openingAmount: drawerState.currentAmount, // Start with current drawer amount
      status: 'open',
      transactionIds: [],
      notes: 'Session créée automatiquement'
    };

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(session);
    });

    console.log('[getCurrentSession] Auto-created session:', sessionId, 'with opening amount:', drawerState.currentAmount);
    return session;
  } catch (error) {
    console.error('[getCurrentSession] Error:', error);
    throw error;
  }
}

/**
 * Add transaction to current session
 */
export async function addTransactionToSession(transactionId: string): Promise<void> {
  try {
    const session = await getCurrentSession(); // This will auto-create if needed
    
    // Update session with new transaction
    const updatedSession: CashSessionDocument = {
      ...session,
      transactionIds: [...(session.transactionIds || []), transactionId]
    };

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(updatedSession);
    });

    console.log('[addTransactionToSession] Transaction added to session:', transactionId);
  } catch (error) {
    console.error('[addTransactionToSession] Error:', error);
    // Don't throw - transaction linking is not critical
  }
}

/**
 * Get session summary with current state
 * CORRECTED: Focus on actual drawer amount, not expected calculations
 */
export async function getSessionSummary(): Promise<{
  session: CashSessionDocument;
  drawerAmount: number; // What's actually in drawer now
  sessionStartAmount: number;
  sessionSalesAmount: number;
  sessionManualAmount: number;
  sessionTotalAdded: number; // What session added to drawer
  transactionCount: number;
  isActive: boolean; // Active = has sales
}> {
  try {
    console.log('[getSessionSummary] Getting session summary...');
    
    const drawerState = await getCurrentDrawerState();
    const session = await getCurrentSession(); // Always exists now
    
    // Get transactions since session started
    const allTransactions = await getAllCashTransactions();
    const sessionStartTime = new Date(session.openedAt).getTime();
    
    const sessionTransactions = allTransactions.filter(tx => {
      const txTime = new Date(tx.time || tx.createdAt).getTime();
      return txTime >= sessionStartTime;
    });

    // Session sales (including delivery collections made in ordering page)
    const sessionSalesAmount = sessionTransactions
      .filter(tx => {
        const tType = (tx as any).transactionType ?? (tx as any).type;
        const metadata = (tx as any).metadata;
        
        // Include all sales transactions
        if (tType === 'sales') {
          // Include regular order payments (including prepaid deliveries)
          if (metadata?.transactionCategory === 'order_payment') {
            return true;
          }
          // Include delivery collections made in ordering page
          if (metadata?.transactionCategory === 'delivery_collection' && metadata?.collectedInOrderingPage) {
            return true;
          }
          // Exclude delivery collections from separate collection system
          if (metadata?.transactionCategory === 'delivery_collection' && !metadata?.collectedInOrderingPage) {
            return false;
          }
          // Include other sales transactions for backward compatibility
          return true;
        }
        
        return false;
      })
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Session manual transactions
    const sessionManualAmount = sessionTransactions
      .filter(tx => {
        const tType = (tx as any).transactionType ?? (tx as any).type;
        return tType === 'manual_in' || tType === 'manual_out';
      })
      .reduce((sum, tx) => sum + tx.amount, 0);

    // Total amount this session added to drawer
    const sessionTotalAdded = sessionSalesAmount + sessionManualAmount;
    const isActive = sessionSalesAmount > 0; // Active only when there are sales

    console.log('[getSessionSummary] Session summary:', {
      sessionId: session._id,
      sessionStartAmount: session.openingAmount,
      sessionSalesAmount: `${sessionSalesAmount} DA (drawer sales only)`,
      sessionManualAmount: `${sessionManualAmount} DA`,
      sessionTotalAdded: `${sessionTotalAdded} DA (added to drawer)`,
      drawerAmount: `${drawerState.currentAmount} DA (current physical)`,
      isActive
    });

    return {
      session,
      drawerAmount: drawerState.currentAmount,
      sessionStartAmount: session.openingAmount,
      sessionSalesAmount,
      sessionManualAmount,
      sessionTotalAdded,
      transactionCount: sessionTransactions.length,
      isActive
    };
  } catch (error) {
    console.error('[getSessionSummary] Error:', error);
    throw error;
  }
}

/**
 * Close session with counted amount (counted amount becomes new drawer reality)
 */
export async function closeSessionWithCount(data: {
  countedAmount: number;
  countedBy: string;
  notes?: string;
}): Promise<{
  closedSession: CashSessionDocument;
  countRecord: CashCountDocument;
  newSession: CashSessionDocument;
  drawerAdjustment: number;
}> {
  try {
    console.log('[closeSessionWithCount] Closing session with count:', data);
    
    const sessionSummary = await getSessionSummary();
    const session = sessionSummary.session;
    
    if (!sessionSummary.isActive) {
      throw new Error('Cannot close session - no sales in current session');
    }

    const variance = data.countedAmount - sessionSummary.drawerAmount;
    const drawerAdjustment = data.countedAmount - sessionSummary.drawerAmount;

    // Create cash count record
    const countId = `cash_count:${Date.now()}`;
    const countRecord: CashCountDocument = {
      _id: countId,
      type: 'cash_count',
      countedAt: new Date().toISOString(),
      countedBy: data.countedBy,
      expectedAmount: sessionSummary.drawerAmount, // What should be in drawer
      countedAmount: data.countedAmount,
      variance,
      collectedAmount: 0, // No automatic collection
      nextDrawerAmount: data.countedAmount, // Counted amount becomes reality
      notes: data.notes,
      transactionSnapshot: {
        salesAmount: sessionSummary.sessionSalesAmount,
        manualAmount: sessionSummary.sessionManualAmount,
        transactionCount: sessionSummary.transactionCount
      }
    };

    // Close the current session
    const closedSession: CashSessionDocument = {
      ...session,
      status: 'closed',
      closedAt: new Date().toISOString(),
      closedBy: data.countedBy,
      expectedAmount: sessionSummary.drawerAmount, // What should be in drawer
      countedAmount: data.countedAmount,
      variance,
      notes: data.notes
    };

    // Create new session immediately with counted amount as opening
    const newSessionId = `cash_session:${Date.now() + 1}`;
    const newSession: CashSessionDocument = {
      _id: newSessionId,
      type: 'cash_session',
      staffId: 'auto',
      staffName: 'Session Auto',
      openedAt: new Date().toISOString(),
      openedBy: data.countedBy,
      openingAmount: data.countedAmount, // New session starts with counted amount
      status: 'open',
      transactionIds: [],
      notes: `Nouvelle session après comptage de ${data.countedAmount} DA`
    };

    // If there's a drawer adjustment needed, create adjustment transaction
    if (drawerAdjustment !== 0) {
      const { createCashTransaction } = await import('./cash-ops');
      await createCashTransaction({
        type: drawerAdjustment > 0 ? 'manual_in' : 'manual_out',
        amount: drawerAdjustment,
        description: `Ajustement caisse - Comptage: ${data.countedAmount} DA (Écart: ${variance} DA)`,
        time: new Date().toISOString(),
        performedBy: data.countedBy,
        metadata: {
          transactionCategory: 'drawer_adjustment',
          sessionId: session._id,
          countId: countId,
          expectedAmount: sessionSummary.drawerAmount,
          countedAmount: data.countedAmount,
          variance,
          drawerAdjustment
        }
      });
    }

    // Save all records
    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(countRecord);
    });

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(closedSession);
    });

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(newSession);
    });

    console.log('[closeSessionWithCount] Session closed and new session created:', {
      closedSessionId: session._id,
      newSessionId,
      countId,
      variance,
      drawerAdjustment
    });

    return {
      closedSession,
      countRecord,
      newSession,
      drawerAdjustment
    };
  } catch (error) {
    console.error('[closeSessionWithCount] Error:', error);
    throw error;
  }
}

// Legacy compatibility functions (simplified)
export async function getAllCashSessions(): Promise<CashSessionDocument[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'cash_session'
      }
    });

    return (result.docs as CashSessionDocument[]).sort((a, b) => 
      new Date(b.openedAt).getTime() - new Date(a.openedAt).getTime()
    );
  } catch (error) {
    console.error('[getAllCashSessions] Error:', error);
    throw error;
  }
}

export async function getAllCashCounts(): Promise<CashCountDocument[]> {
  try {
    const result = await databaseV4.findDocs({
      selector: {
        type: 'cash_count'
      }
    });

    return (result.docs as CashCountDocument[]).sort((a, b) => 
      new Date(b.countedAt).getTime() - new Date(a.countedAt).getTime()
    );
  } catch (error) {
    console.error('[getAllCashCounts] Error:', error);
    throw error;
  }
}

export async function getRecentCashCounts(limit: number = 5): Promise<CashCountDocument[]> {
  try {
    const allCounts = await getAllCashCounts();
    return allCounts.slice(0, limit);
  } catch (error) {
    console.error('[getRecentCashCounts] Error:', error);
    throw error;
  }
}

/**
 * Create a standalone cash count record
 * For simplified cash counting without session management
 */
export async function createCashCount(data: {
  countedAmount: number;
  expectedAmount: number;
  variance: number;
  countedAt: string;
  countedBy: string;
  notes?: string;
}): Promise<CashCountDocument> {
  try {
    const countId = `cash_count:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
    
    const countRecord: CashCountDocument = {
      _id: countId,
      type: 'cash_count',
      countedAt: data.countedAt,
      countedBy: data.countedBy,
      expectedAmount: data.expectedAmount,
      countedAmount: data.countedAmount,
      variance: data.variance,
      collectedAmount: 0, // No automatic collection for standalone counts
      nextDrawerAmount: data.countedAmount, // Counted amount becomes reality
      notes: data.notes,
      transactionSnapshot: {
        salesAmount: 0, // Standalone count doesn't track session sales
        manualAmount: 0,
        transactionCount: 0
      }
    };

    await retryWithConflictResolution(async () => {
      await databaseV4.putDoc(countRecord);
    });

    console.log('[createCashCount] Standalone cash count created:', countId);
    return countRecord;
  } catch (error) {
    console.error('[createCashCount] Error:', error);
    throw error;
  }
}

// Deprecated functions for backward compatibility
export async function getCurrentActiveSession(): Promise<CashSessionDocument | null> {
  const summary = await getSessionSummary();
  return summary.isActive ? summary.session : null;
}

export async function getCurrentSessionSummary() {
  const summary = await getSessionSummary();
  return {
    hasOpenSession: summary.isActive,
    session: summary.session,
    expectedAmount: summary.drawerAmount,
    transactionCount: summary.transactionCount,
    salesAmount: summary.sessionSalesAmount,
    manualAmount: summary.sessionManualAmount
  };
}

export async function recordCashCount(data: {
  countedAmount: number;
  countedBy: string;
  notes?: string;
}) {
  const result = await closeSessionWithCount(data);
  return {
    countId: result.countRecord._id,
    expectedAmount: result.countRecord.expectedAmount,
    countedAmount: result.countRecord.countedAmount,
    variance: result.countRecord.variance,
    collectedAmount: 0,
    nextDrawerAmount: result.countRecord.nextDrawerAmount
  };
}

export async function initializeDrawer(data: {
  initialAmount: number;
  initializedBy: string;
  notes?: string;
}): Promise<void> {
  // Just create a manual transaction if needed
  if (data.initialAmount > 0) {
    const { createCashTransaction } = await import('./cash-ops');
    await createCashTransaction({
      type: 'manual_in',
      amount: data.initialAmount,
      description: `Initialisation caisse - Montant initial: ${data.initialAmount} DA`,
      time: new Date().toISOString(),
      performedBy: data.initializedBy,
      metadata: {
        transactionCategory: 'drawer_initialization',
        notes: data.notes
      }
    });
  }
  console.log('[initializeDrawer] Drawer initialized with:', data.initialAmount, 'DA');
}

export async function openCashSession(data: {
  staffId: string;
  staffName: string;
  openedBy: string;
  openingAmount: number;
  notes?: string;
}) {
  // Just ensure session exists (will auto-create)
  const session = await getCurrentSession();
  return session;
}

export async function closeCashSession(data: {
  sessionId: string;
  closedBy: string;
  countedAmount: number;
  nextOpeningAmount?: number;
  varianceReason?: string;
  notes?: string;
}) {
  const result = await closeSessionWithCount({
    countedAmount: data.countedAmount,
    countedBy: data.closedBy,
    notes: data.notes
  });

  return {
    closedSession: result.closedSession,
    newSession: result.newSession
  };
}

export async function startNewSession(data: {
  staffId: string;
  staffName: string;
  openedBy: string;
  firstSaleTransactionId: string;
}): Promise<CashSessionDocument> {
  // Just get current session and add the transaction
  const session = await getCurrentSession();
  await addTransactionToSession(data.firstSaleTransactionId);
  return session;
} 