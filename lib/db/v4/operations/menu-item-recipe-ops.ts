// knowledge: v4 menu item recipe CRUD operations
import { databaseV4 } from '../core/db-instance';
import { MenuItemRecipe, DEFAULT_MENU_ITEM_RECIPE } from '../schemas/menu-item-recipe-schema';
// Import SubRecipe type (now without theoreticalCostPerYieldUnit)
import { SubRecipe } from '../../../../types/cogs';
import { getInventory } from './inventory-ops';
import { getAllSubRecipes } from './sub-recipe-ops';
import { 
  retryWithConflictResolution, 
  safeUpdateDocument 
} from '../core/conflict-resolution';

// Omit costPerUnit as it's calculated, theoreticalCostPerServing is removed
export async function createMenuItemRecipe(data: Omit<MenuItemRecipe, '_id' | 'type' | 'createdAt' | 'updatedAt' | 'costPerUnit'>): Promise<MenuItemRecipe> {
  const now = new Date().toISOString();
  const inventory = await getInventory();

  let dynamicTotalCost = 0;
  // const processedIngredients = []; // No longer needed if ingredients aren't modified for standard cost

  // 🚀 FIX: Fetch all sub-recipes once to avoid repeated ID-based fetching
  const allSubRecipes = await getAllSubRecipes();
  console.log('[createMenuItemRecipe] Fetched', allSubRecipes.length, 'sub-recipes for cost calculation');

  for (const ing of data.ingredients) {
    // let standardCostAtRecipeTime: number | undefined = undefined; // Removed
    let currentDynamicIngredientCost = 0;

    if ('stockItemId' in ing) {
      const stockItem = inventory.items.find(i => i.id === ing.stockItemId);
      if (stockItem && typeof stockItem.costPerUnit === 'number') {
        currentDynamicIngredientCost = stockItem.costPerUnit * ing.quantity;
      }
      // processedIngredients.push({ ...ing }); // No change to ing needed
    } else if ('subRecipeId' in ing) {
      // 🚀 FIX: Use allSubRecipes array instead of getSubRecipe(id) to eliminate 404
      const subRecipe = allSubRecipes.find(sr => sr._id === ing.subRecipeId);
      if (subRecipe && typeof subRecipe.costPerUnit === 'number') {
        currentDynamicIngredientCost = subRecipe.costPerUnit * ing.quantity;
        console.log(`[createMenuItemRecipe] Using sub-recipe ${subRecipe.name} cost: ${subRecipe.costPerUnit} for quantity: ${ing.quantity}`);
      } else {
        console.warn(`[createMenuItemRecipe] Sub-recipe not found for ID: ${ing.subRecipeId}`);
      }
      // processedIngredients.push({ ...ing }); // No change to ing needed
    }
    dynamicTotalCost += currentDynamicIngredientCost;
  }

  const costPerUnit = dynamicTotalCost; // This is the dynamic, real-time cost

  const recipe: MenuItemRecipe = {
    ...DEFAULT_MENU_ITEM_RECIPE,
    ...data,
    // ingredients: data.ingredients, // Ingredients from data are used directly
    costPerUnit,
    // theoreticalCostPerServing, // Removed
    _id: `menu-item-recipe:${crypto.randomUUID()}`,
    type: 'menu-item-recipe',
    createdAt: now,
    updatedAt: now,
  };
  
  console.log('[createMenuItemRecipe] Created recipe object:', recipe);
  
  // Use conflict resolution instead of direct putDoc
  await retryWithConflictResolution(async () => {
    await databaseV4.putDoc(recipe);
  });
  
  return recipe;
}

// 🚀 NEW: Get effective cost for a menu item recipe (recipe vs fixed cost logic)
export function getEffectiveCost(recipe: MenuItemRecipe): number {
  // If fixed cost method is selected and has a valid fixed cost, use it
  if (recipe.costingMethod === 'fixed' && typeof recipe.fixedCost === 'number' && recipe.fixedCost > 0) {
    return recipe.fixedCost;
  }
  
  // Otherwise, use calculated cost from recipe ingredients
  return recipe.costPerUnit || 0;
}

// 🚀 NEW: Check if recipe has valid cost data for analytics
export function hasValidCostData(recipe: MenuItemRecipe): boolean {
  if (recipe.costingMethod === 'fixed') {
    return typeof recipe.fixedCost === 'number' && recipe.fixedCost > 0;
  }
  
  // For recipe method, need ingredients and valid cost calculation
  return (recipe.ingredients && recipe.ingredients.length > 0 && typeof recipe.costPerUnit === 'number' && recipe.costPerUnit > 0);
}

// 🚀 FIX: Use getAllMenuItemRecipes + filter pattern to eliminate 404 timing issues
export async function getMenuItemRecipe(id: string): Promise<MenuItemRecipe | null> {
  try {
    console.log(`[getMenuItemRecipe] Fetching menu item recipe by ID: ${id} using getAllMenuItemRecipes + filter pattern`);
    const allRecipes = await getAllMenuItemRecipes();
    const recipe = allRecipes.find(r => r._id === id);
    
    if (recipe) {
      console.log(`[getMenuItemRecipe] Found menu item recipe: ${recipe.menuItemName || 'unnamed'}`);
      return recipe;
    } else {
      console.log(`[getMenuItemRecipe] Menu item recipe not found: ${id}`);
      return null;
    }
  } catch (error: any) {
    console.error(`[getMenuItemRecipe] Error fetching menu item recipe ${id}:`, error);
    return null; // Return null on any error to be consistent
  }
}

export async function getAllMenuItemRecipes(): Promise<MenuItemRecipe[]> {
  try {
    console.log('[getAllMenuItemRecipes] Fetching all menu item recipes...');
    
    // 🚀 FIX: Add retry mechanism for extra reliability
    let result;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        result = await databaseV4.findDocs<MenuItemRecipe>({ selector: { type: 'menu-item-recipe' } });
        break; // Success, exit retry loop
      } catch (queryError: any) {
        attempts++;
        console.warn(`[getAllMenuItemRecipes] Query attempt ${attempts} failed:`, queryError);
        
        if (attempts >= maxAttempts) {
          throw queryError; // Give up after max attempts
        }
        
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 100 * attempts));
      }
    }
    
    if (!result) {
      console.error('[getAllMenuItemRecipes] Failed to get query result after retries');
      return []; // Return empty array as fallback
    }
    
    console.log('[getAllMenuItemRecipes] Found', result.docs?.length || 0, 'menu item recipes');
    
    // 🚀 FIX: Additional validation of returned documents
    const validDocs = (result.docs || []).filter(doc => {
      // Ensure document has required fields
      if (!doc._id || !doc.type || doc.type !== 'menu-item-recipe') {
        console.warn('[getAllMenuItemRecipes] Filtering out invalid document:', doc);
        return false;
      }
      return true;
    });
    
    // Ensure documents are properly structured
    const processedRecipes = validDocs.map(recipe => ({
      ...recipe,
      ingredients: Array.isArray(recipe.ingredients) ? recipe.ingredients : [],
      costPerUnit: typeof recipe.costPerUnit === 'number' ? recipe.costPerUnit : 0
    }));
    
    console.log('[getAllMenuItemRecipes] Returning', processedRecipes.length, 'processed menu item recipes');
    return processedRecipes;
  } catch (error) {
    console.error('[getAllMenuItemRecipes] Error fetching menu item recipes:', error);
    
    // 🚀 FIX: Return empty array instead of throwing to prevent cascading failures
    console.warn('[getAllMenuItemRecipes] Returning empty array as fallback due to error');
    return [];
  }
}

// 🚀 FIX: Use getAllSubRecipes + filter pattern in update operation too
export async function updateMenuItemRecipe(id: string, updates: Partial<MenuItemRecipe>): Promise<MenuItemRecipe | null> {
  try {
    console.log('[updateMenuItemRecipe] Updating recipe:', id, 'with updates:', updates);
    const current = await getMenuItemRecipe(id);
    if (!current) {
      console.error('[updateMenuItemRecipe] Recipe not found for id:', id);
      return null;
    }

    let costPerUnit = current.costPerUnit || 0;
    // let theoreticalCostPerServing = current.theoreticalCostPerServing || 0; // Removed
    const ingredientsToProcess = updates.ingredients || current.ingredients;

    if (updates.ingredients) {
      const inventory = await getInventory();
      let dynamicTotalCost = 0;
      // const processedIngredients = []; // No longer needed

      // 🚀 FIX: Fetch all sub-recipes once to avoid repeated ID-based fetching
      const allSubRecipes = await getAllSubRecipes();
      console.log('[updateMenuItemRecipe] Fetched', allSubRecipes.length, 'sub-recipes for cost calculation');

      for (const ing of ingredientsToProcess) {
        // let standardCostAtRecipeTime: number | undefined = undefined; // Removed
        let currentDynamicIngredientCost = 0;
        
        const quantity = (ing as any).quantity;
        if (typeof quantity !== 'number') {
            console.warn('[updateMenuItemRecipe] Ingredient missing quantity, skipping cost calculation for it:', ing);
            // processedIngredients.push(ing); // Not needed if not modifying ingredients
            continue;
        }

        if ('stockItemId' in ing && ing.stockItemId) {
          const stockItem = inventory.items.find(i => i.id === ing.stockItemId);
          if (stockItem && typeof stockItem.costPerUnit === 'number') {
            currentDynamicIngredientCost = stockItem.costPerUnit * quantity;
          }
          // processedIngredients.push({ ...(ing as any), quantity }); // Not needed
        } else if ('subRecipeId' in ing && ing.subRecipeId) {
          // 🚀 FIX: Use allSubRecipes array instead of getSubRecipe(id) to eliminate 404
          const subRecipe = allSubRecipes.find(sr => sr._id === ing.subRecipeId);
          if (subRecipe && typeof subRecipe.costPerUnit === 'number') {
            currentDynamicIngredientCost = subRecipe.costPerUnit * quantity;
            console.log(`[updateMenuItemRecipe] Using sub-recipe ${subRecipe.name} cost: ${subRecipe.costPerUnit} for quantity: ${quantity}`);
          } else {
            console.warn(`[updateMenuItemRecipe] Sub-recipe not found for ID: ${ing.subRecipeId}`);
          }
          // processedIngredients.push({ ...(ing as any), quantity }); // Not needed
        } else {
          // processedIngredients.push(ing); // Not needed
        }
        dynamicTotalCost += currentDynamicIngredientCost;
      }
      costPerUnit = dynamicTotalCost;
      // theoreticalCostPerServing = theoreticalTotalCost; // Removed
      // updates.ingredients = processedIngredients; // Not needed as ingredients are not modified
    }
    
    const updatedRecipeData: Partial<MenuItemRecipe> = {
        ...updates,
        costPerUnit,
        // theoreticalCostPerServing, // Removed
    };

    // Use safe update with conflict resolution
    return await safeUpdateDocument<MenuItemRecipe>(id, (doc) => ({
      ...doc,
      ...updatedRecipeData,
      updatedAt: new Date().toISOString(),
    }));
  } catch (err) {
    console.error('[updateMenuItemRecipe] Error:', err);
    throw err;
  }
}

export async function deleteMenuItemRecipe(id: string): Promise<void> {
  try {
    // 🚀 FIX: Use getMenuItemRecipe which now uses getAllMenuItemRecipes + filter pattern
    const current = await getMenuItemRecipe(id);
    if (!current || !current._rev) {
        console.warn(`[deleteMenuItemRecipe] Document ${id} not found or missing revision.`);
        return;
    }
    await databaseV4.deleteDoc(id, current._rev);
  } catch (error: any) {
     if (error.status === 404 || error.name === 'not_found') {
          console.warn(`[deleteMenuItemRecipe] Document ${id} already deleted or not found.`);
          return; // Already gone
      }
      console.error(`[deleteMenuItemRecipe] Error deleting document ${id}:`, error);
      throw error; // Re-throw other errors
  }
}
// endknowledge 