"use client";

import { databaseV4 } from '../core/db-instance';
import { validateSchema, ValidationError } from '../core/validation';
import { safeUpdateDocument } from '../core/conflict-resolution';
import {
  freelancerDocumentSchema,
  FreelancerDocument,
  normalizePhoneForId,
  generateFreelancerId,
  createDefaultFreelancer
} from '../schemas/freelancer-schema';

/**
 * Freelancer Operations for PouchDB
 * Simple CRUD operations with phone-based ID and unique name validation
 */

// Create or get freelancer - simplified logic
export async function createOrGetFreelancer(name?: string, phone?: string): Promise<FreelancerDocument> {
  try {
    console.log(`Freelancer - Creating/getting freelancer: ${name || 'No name'} - ${phone || 'No phone'}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Business logic validation: at least one of name or phone must be provided
    if (!name && !phone) {
      throw new Error('At least one of name or phone must be provided');
    }

    // First, try to find existing freelancer by phone (most reliable)
    if (phone) {
      try {
        const existingByPhone = await getFreelancerByPhone(phone);
        if (existingByPhone) {
          console.log(`Freelancer - Found existing by phone: ${existingByPhone._id}`);
          return existingByPhone;
        }
      } catch (error: any) {
        if (error.status !== 404) {
          throw error;
        }
      }
    }

    // If no phone or not found by phone, search by name
    if (name) {
      const existingByName = await getFreelancerByName(name);
      if (existingByName) {
        console.log(`Freelancer - Found existing by name: ${existingByName._id}`);
        return existingByName;
      }
    }

    // Not found - create new freelancer
    console.log('Freelancer - Creating new freelancer');
    
    // Generate unique name if needed
    let finalName = name;
    if (finalName) {
      const isNameUnique = await checkNameUniqueness(finalName.trim());
      if (!isNameUnique) {
        finalName = await generateUniqueName(finalName.trim());
        console.log(`Freelancer - Generated unique name: ${finalName}`);
      }
    }

    // Generate ID - prefer phone-based, fallback to name-based
    let freelancerId: string;
    if (phone) {
      freelancerId = generateFreelancerId(phone);
    } else {
      freelancerId = `freelancer:name-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    // Create the new freelancer
    const freelancerData = createDefaultFreelancer(finalName, phone);
    const newFreelancer: FreelancerDocument = {
      ...freelancerData,
      _id: freelancerId,
      lastActiveAt: new Date().toISOString()
    };
    
    // Validate and save
    const validatedFreelancer = validateSchema<FreelancerDocument>(newFreelancer, freelancerDocumentSchema);
    const result = await databaseV4.putDoc(validatedFreelancer);
    
    console.log(`Freelancer - Created new: ${finalName || 'No name'} (${phone || 'No phone'}) with ID: ${freelancerId}`);
    
    return {
      ...validatedFreelancer,
      _rev: result.rev
    };
  } catch (error) {
    console.error('Freelancer - Error creating/getting freelancer:', error);
    throw error;
  }
}

// Get freelancer by phone
export async function getFreelancerByPhone(phone: string): Promise<FreelancerDocument | null> {
  try {
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const freelancerId = generateFreelancerId(phone);
    const freelancer = await databaseV4.getDoc<FreelancerDocument>(freelancerId);
    return freelancer;
  } catch (error: any) {
    if (error.status === 404) {
      return null;
    }
    console.error('Freelancer - Error getting freelancer by phone:', error);
    throw error;
  }
}

// Get freelancer by exact name match
export async function getFreelancerByName(name: string): Promise<FreelancerDocument | null> {
  try {
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const selector = {
      type: 'freelancer',
      name: { $eq: name.trim() },
      isActive: true
    };

    const result = await databaseV4.findDocs<FreelancerDocument>({
      selector,
      limit: 1
    });

    return result.docs.length > 0 ? result.docs[0] : null;
  } catch (error) {
    console.error('Freelancer - Error getting freelancer by name:', error);
    return null;
  }
}

// Search freelancers by name or phone - simplified and robust
export async function searchFreelancers(query: string): Promise<FreelancerDocument[]> {
  try {
    console.log(`Freelancer - Searching for: ${query}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Clean the query
    const cleanQuery = query.trim();
    if (!cleanQuery) {
      return [];
    }

    // Get all active freelancers first (simple query)
    const selector = {
      type: 'freelancer',
      isActive: true
    };

    const result = await databaseV4.findDocs<FreelancerDocument>({
      selector,
      limit: 100 // Get more to filter in JS
    });

    // Filter and sort in JavaScript to avoid PouchDB issues
    const filteredResults = result.docs.filter(freelancer => {
      const nameMatch = freelancer.name && 
        freelancer.name.toLowerCase().includes(cleanQuery.toLowerCase());
      const phoneMatch = freelancer.phone && 
        freelancer.phone.includes(cleanQuery.replace(/\D/g, ''));
      
      return nameMatch || phoneMatch;
    });

    // Sort by relevance
    const sortedResults = filteredResults.sort((a, b) => {
      // Exact matches first
      const aExactName = a.name?.toLowerCase() === cleanQuery.toLowerCase();
      const bExactName = b.name?.toLowerCase() === cleanQuery.toLowerCase();
      const aExactPhone = a.phone === cleanQuery;
      const bExactPhone = b.phone === cleanQuery;
      
      if (aExactName || aExactPhone) return -1;
      if (bExactName || bExactPhone) return 1;
      
      // Then by total orders (descending)
      if (a.totalOrders !== b.totalOrders) {
        return b.totalOrders - a.totalOrders;
      }
      
      // Then by last active date (most recent first)
      if (a.lastActiveAt && b.lastActiveAt) {
        return new Date(b.lastActiveAt).getTime() - new Date(a.lastActiveAt).getTime();
      }
      
      return 0;
    });

    // Limit results
    const limitedResults = sortedResults.slice(0, 20);

    console.log(`Freelancer - Found ${limitedResults.length} freelancers matching: ${query}`);
    return limitedResults;
  } catch (error) {
    console.error('Freelancer - Error searching freelancers:', error);
    return [];
  }
}

// Get all active freelancers
export async function getAllActiveFreelancers(): Promise<FreelancerDocument[]> {
  try {
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const selector = {
      type: 'freelancer',
      isActive: { $eq: true }
    };

    const result = await databaseV4.findDocs<FreelancerDocument>({
      selector
    });

    // Sort in JavaScript
    const sortedResults = result.docs.sort((a, b) => {
      // Sort by total orders (descending)
      if (a.totalOrders !== b.totalOrders) {
        return b.totalOrders - a.totalOrders;
      }
      
      // Then by last active date (most recent first)
      if (a.lastActiveAt && b.lastActiveAt) {
        return new Date(b.lastActiveAt).getTime() - new Date(a.lastActiveAt).getTime();
      }
      
      return 0;
    });

    return sortedResults;
  } catch (error) {
    console.error('Freelancer - Error getting active freelancers:', error);
    return [];
  }
}

// Update freelancer stats (called when order is completed)
export async function updateFreelancerStats(phone: string, orderValue: number): Promise<FreelancerDocument | null> {
  try {
    console.log(`Freelancer - Updating stats for ${phone}: +${orderValue}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const freelancerId = generateFreelancerId(phone);
    
    try {
      const freelancer = await databaseV4.getDoc<FreelancerDocument>(freelancerId);
      
      const updated = await safeUpdateDocument<FreelancerDocument>(freelancerId, (doc) => {
        doc.totalOrders = doc.totalOrders + 1;
        doc.totalEarnings = doc.totalEarnings + orderValue;
        doc.lastActiveAt = new Date().toISOString();
        doc.updatedAt = new Date().toISOString();
        return doc;
      });
      
      console.log(`Freelancer - Updated stats: ${updated.totalOrders} orders, ${updated.totalEarnings} earnings`);
      return updated;
    } catch (error: any) {
      if (error.status === 404) {
        console.log(`Freelancer - Freelancer ${phone} not found for stats update`);
        return null;
      }
      throw error;
    }
  } catch (error) {
    console.error('Freelancer - Error updating freelancer stats:', error);
    throw error;
  }
}

// Update freelancer details
export async function updateFreelancer(phone: string, updates: Partial<Omit<FreelancerDocument, '_id' | '_rev' | 'type' | 'phone' | 'createdAt'>>): Promise<FreelancerDocument> {
  try {
    console.log(`Freelancer - Updating freelancer: ${phone}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const freelancerId = generateFreelancerId(phone);
    
    // If updating name, check uniqueness
    if (updates.name) {
      const currentFreelancer = await databaseV4.getDoc<FreelancerDocument>(freelancerId);
      if (updates.name.trim() !== currentFreelancer.name) {
        const isNameUnique = await checkNameUniqueness(updates.name.trim(), phone);
        if (!isNameUnique) {
          throw new Error(`Name '${updates.name}' is already taken by another freelancer`);
        }
      }
    }

    const updated = await safeUpdateDocument<FreelancerDocument>(freelancerId, (doc) => {
      Object.assign(doc, updates);
      doc.updatedAt = new Date().toISOString();
      return doc;
    });

    return updated;
  } catch (error) {
    console.error('Freelancer - Error updating freelancer:', error);
    throw error;
  }
}

// Check if name is unique (excluding current freelancer by phone)
export async function checkNameUniqueness(name: string, excludePhone?: string): Promise<boolean> {
  try {
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const selector = {
      type: 'freelancer',
      name: { $eq: name.trim() }
    };

    const result = await databaseV4.findDocs<FreelancerDocument>({
      selector,
      limit: 1
    });

    if (result.docs.length === 0) {
      return true; // Name is unique
    }

    // If excluding a phone, check if the found freelancer is the one we're excluding
    if (excludePhone) {
      const excludeId = generateFreelancerId(excludePhone);
      return result.docs[0]._id === excludeId;
    }

    return false; // Name is not unique
  } catch (error) {
    console.error('Freelancer - Error checking name uniqueness:', error);
    return false; // Assume not unique on error
  }
}

// Generate unique name by appending number
export async function generateUniqueName(baseName: string): Promise<string> {
  let counter = 2;
  let candidateName = `${baseName} (${counter})`;
  
  while (!(await checkNameUniqueness(candidateName))) {
    counter++;
    candidateName = `${baseName} (${counter})`;
    
    // Safety check to prevent infinite loop
    if (counter > 100) {
      candidateName = `${baseName} (${Date.now()})`;
      break;
    }
  }
  
  return candidateName;
}

// Deactivate freelancer
export async function deactivateFreelancer(phone: string): Promise<FreelancerDocument> {
  return updateFreelancer(phone, { isActive: false });
}

// Reactivate freelancer
export async function reactivateFreelancer(phone: string): Promise<FreelancerDocument> {
  return updateFreelancer(phone, { isActive: true });
}

// Test function to verify freelancer operations work
export async function testFreelancerOperations(): Promise<void> {
  try {
    console.log('🧪 Testing freelancer operations...');
    
    // Test 1: Search (should not fail)
    const searchResults = await searchFreelancers('test');
    console.log('✅ Search test passed:', searchResults.length, 'results');
    
    // Test 2: Get all active freelancers
    const activeFreelancers = await getAllActiveFreelancers();
    console.log('✅ Get active freelancers test passed:', activeFreelancers.length, 'results');
    
    // Test 3: Check name uniqueness
    const isUnique = await checkNameUniqueness('Test Name');
    console.log('✅ Name uniqueness test passed:', isUnique);
    
    console.log('🎉 All freelancer operations tests passed!');
  } catch (error) {
    console.error('❌ Freelancer operations test failed:', error);
    throw error;
  }
}