// knowledge: v4 sub-recipe CRUD operations
import { databaseV4 } from '../core/db-instance';
import { SubRecipe, DEFAULT_SUB_RECIPE } from '../schemas/sub-recipe-schema';
import { getInventory } from './inventory-ops';
// 🚀 Import the new conflict resolution utilities
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument,
  safeEnsureDocument
} from '../core/conflict-resolution';

// Omit costPerUnit as it's calculated, theoreticalCostPerYieldUnit is removed
export async function createSubRecipe(data: Omit<SubRecipe, '_id' | 'type' | 'createdAt' | 'updatedAt' | 'costPerUnit'>): Promise<SubRecipe> {
  return retryWithConflictResolution(async () => {
    const now = new Date().toISOString();
    const inventory = await getInventory();
    
    let dynamicTotalCost = 0;

    for (const ing of data.ingredients) {
      const stockItem = inventory.items.find(i => i.id === ing.stockItemId);
      let currentDynamicIngredientCost = 0;

      if (stockItem && typeof stockItem.costPerUnit === 'number') {
        currentDynamicIngredientCost = stockItem.costPerUnit * ing.quantity;
      }
      dynamicTotalCost += currentDynamicIngredientCost;
    }

    const yieldQty = data.yield?.quantity || 1;
    const costPerUnit = yieldQty > 0 ? dynamicTotalCost / yieldQty : 0;

    const { currentStock, ...rest } = data as any;
    const subRecipe: SubRecipe = {
      ...DEFAULT_SUB_RECIPE,
      ...rest,
      ingredients: data.ingredients, // Explicitly set ingredients from data to ensure they're not overwritten
      costPerUnit,
      _id: `sub-recipe:${crypto.randomUUID()}`,
      type: 'sub-recipe',
      createdAt: now,
      updatedAt: now,
    };
    
    console.log('[createSubRecipe] Creating sub-recipe with ingredients:', data.ingredients);
    console.log('[createSubRecipe] Final sub-recipe object:', subRecipe);
    
    await databaseV4.putDoc(subRecipe);
    return subRecipe;
  }, 3, 'createSubRecipe');
}

// 🚀 FIX: Use getAllSubRecipes + filter pattern to eliminate 404 timing issues
export async function getSubRecipe(id: string): Promise<SubRecipe | null> {
  try {
    console.log(`[getSubRecipe] Fetching sub-recipe by ID: ${id} using getAllSubRecipes + filter pattern`);
    const allSubRecipes = await getAllSubRecipes();
    const subRecipe = allSubRecipes.find(sr => sr._id === id);
    
    if (subRecipe) {
      console.log(`[getSubRecipe] Found sub-recipe: ${subRecipe.name}`);
      return subRecipe;
    } else {
      console.log(`[getSubRecipe] Sub-recipe not found: ${id}`);
      return null;
    }
  } catch (error: any) {
    console.error(`[getSubRecipe] Error fetching sub-recipe ${id}:`, error);
    return null; // Return null on any error to be consistent
  }
}

export async function getAllSubRecipes(): Promise<SubRecipe[]> {
  try {
    console.log('[getAllSubRecipes] Fetching all sub-recipes...');
    
    // 🚀 FIX: Add retry mechanism for extra reliability
    let result;
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        result = await databaseV4.findDocs<SubRecipe>({ selector: { type: 'sub-recipe' } });
        break; // Success, exit retry loop
      } catch (queryError: any) {
        attempts++;
        console.warn(`[getAllSubRecipes] Query attempt ${attempts} failed:`, queryError);
        
        if (attempts >= maxAttempts) {
          throw queryError; // Give up after max attempts
        }
        
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 100 * attempts));
      }
    }
    
    if (!result) {
      console.error('[getAllSubRecipes] Failed to get query result after retries');
      return []; // Return empty array as fallback
    }
    
    console.log('[getAllSubRecipes] Raw query result:', result);
    console.log('[getAllSubRecipes] Found', result.docs?.length || 0, 'sub-recipes');
    
    // 🚀 FIX: Additional validation of returned documents
    const validDocs = (result.docs || []).filter(doc => {
      // Ensure document has required fields
      if (!doc._id || !doc.type || doc.type !== 'sub-recipe') {
        console.warn('[getAllSubRecipes] Filtering out invalid document:', doc);
        return false;
      }
      return true;
    });
    
    if (validDocs.length > 0) {
      validDocs.forEach((doc, index) => {
        console.log(`[getAllSubRecipes] Sub-recipe ${index + 1}:`, {
          _id: doc._id,
          name: doc.name,
          ingredients: doc.ingredients?.length || 0,
          type: doc.type
        });
      });
    }

    // Ensure currentStock is always present and documents are properly structured
    const processedSubRecipes = validDocs.map(sub => ({
      ...sub,
      currentStock: typeof sub.currentStock === 'number' ? sub.currentStock : 0,
      ingredients: Array.isArray(sub.ingredients) ? sub.ingredients : [],
      yield: sub.yield || { quantity: 1, unit: 'pcs' as const },
      costPerUnit: typeof sub.costPerUnit === 'number' ? sub.costPerUnit : 0
    }));
    
    console.log('[getAllSubRecipes] Returning', processedSubRecipes.length, 'processed sub-recipes');
    return processedSubRecipes;
  } catch (error) {
    console.error('[getAllSubRecipes] Error fetching sub-recipes:', error);
    
    // 🚀 FIX: Return empty array instead of throwing to prevent cascading failures
    console.warn('[getAllSubRecipes] Returning empty array as fallback due to error');
    return [];
  }
}

// 🚀 FIX: Eliminate refetch after putDoc to prevent 404 timing issues
export async function updateSubRecipe(id: string, updates: Partial<SubRecipe>): Promise<SubRecipe | null> {
  return retryWithConflictResolution(async () => {
    console.log('[updateSubRecipe] id:', id, 'updates:', updates);
    const current = await getSubRecipe(id);
    if (!current) {
      console.error('[updateSubRecipe] SubRecipe not found for id:', id);
      return null;
    }

    let costPerUnit = current.costPerUnit || 0;
    
    const ingredientsToProcess = updates.ingredients || current.ingredients;
    const yieldQty = (updates.yield && typeof updates.yield.quantity === 'number') ? updates.yield.quantity : (current.yield?.quantity || 1);

    if (updates.ingredients || updates.yield) {
      const inventory = await getInventory();
      let dynamicTotalCost = 0;

      for (const ing of ingredientsToProcess) {
        const quantity = (ing as any).quantity;
        const stockItemId = (ing as any).stockItemId;

        if (typeof quantity !== 'number' || typeof stockItemId !== 'string') {
            console.warn('[updateSubRecipe] Ingredient missing quantity or stockItemId, skipping cost calculation for it:', ing);
            continue;
        }

        const stockItem = inventory.items.find(i => i.id === stockItemId);
        let currentDynamicIngredientCost = 0;

        if (stockItem && typeof stockItem.costPerUnit === 'number') {
          currentDynamicIngredientCost = stockItem.costPerUnit * quantity;
        }
        dynamicTotalCost += currentDynamicIngredientCost;
      }
      costPerUnit = yieldQty > 0 ? dynamicTotalCost / yieldQty : 0;
    }

    const updatedRecipeData: Partial<SubRecipe> = {
        ...updates,
        costPerUnit,
    };

    let updated: SubRecipe;
    // Only remove currentStock if not provided
    if (typeof updates.currentStock === 'number') {
      updated = {
        ...current,
        ...updatedRecipeData,
        updatedAt: new Date().toISOString(),
        currentStock: updates.currentStock
      };
    } else {
      const { currentStock, ...rest } = updatedRecipeData as any;
      updated = {
        ...current,
        ...rest,
        updatedAt: new Date().toISOString(),
      };
    }
    
    const response = await databaseV4.putDoc(updated);
    
    // 🚀 FIX: Return the updated object directly instead of refetching to prevent 404
    const finalUpdated = { ...updated, _rev: response.rev };
    console.log('[updateSubRecipe] Successfully updated sub-recipe, returning updated object directly');
    return finalUpdated;
  }, 3, `updateSubRecipe(${id})`);
}

export async function deleteSubRecipe(id: string): Promise<void> {
  try {
    // 🚀 FIX: Use getSubRecipe which now uses getAllSubRecipes + filter pattern
    const current = await getSubRecipe(id);
    if (!current || !current._rev) {
        console.warn(`[deleteSubRecipe] Document ${id} not found or missing revision. Cannot delete.`);
        return; // Can't delete without revision
    }
    
    await databaseV4.deleteDoc(id, current._rev);
  } catch (error: any) {
      // Handle 404 gracefully if needed
      if (error.status === 404 || error.name === 'not_found') {
          console.warn(`[deleteSubRecipe] Document ${id} already deleted or not found.`);
          return; // Already gone, consider it success
      }
      console.error(`[deleteSubRecipe] Error deleting document ${id}:`, error);
      throw error; // Re-throw other errors
  }
}
// endknowledge 