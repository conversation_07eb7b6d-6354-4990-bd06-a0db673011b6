"use client";

import { v4 as uuidv4 } from 'uuid';
import { databaseV4 } from '../core/db-instance';
import { validateSchema, generateId, ValidationError } from '../core/validation';
import {
  staffDocumentSchema,
  scheduleDocumentSchema,
  attendanceDocumentSchema,
  paymentDocumentSchema,
  StaffDocument,
  ScheduleDocument,
  AttendanceDocument,
  PaymentDocument,
  AttendanceRecord,
  staffDesignDoc,
  scheduleDesignDoc,
  attendanceDesignDoc,
  paymentDesignDoc
} from '../schemas/per-staff-schemas';
import { 
  retryWithConflictResolution, 
  safeUpdateDocument 
} from '../core/conflict-resolution';

/**
 * Per-Staff Operations for V4 Database
 * 
 * Each staff member, schedule, and attendance gets their own document
 */

// Default permissions for new staff
export const DEFAULT_STAFF_PERMISSIONS = {
  pages: {
    menu: false,
    orders: false,
    finance: false,
    inventory: false,
    staff: false,
    settings: false,
    suppliers: false,
  },
  tabs: {
    inventory: {
      inventory: false,
      subrecipes: false,
      counts: false,
      waste: false
    },
    staff: {
      shifts_schedule: false,
      attendance: false,
      payments: false
    }
  }
};

export const DEFAULT_ADMIN_STAFF_PERMISSIONS = {
  pages: {
    menu: true,
    orders: true,
    finance: true,
    inventory: true,
    staff: true,
    settings: true,
    suppliers: true,
  },
  tabs: {
    inventory: {
      inventory: true,
      subrecipes: true,
      counts: true,
      waste: true
    },
    staff: {
      shifts_schedule: true,
      attendance: true,
      payments: true
    }
  }
};

/**
 * Initialize per-staff indexes
 */
export async function createPerStaffIndexes(): Promise<void> {
  try {
    console.log('Per-Staff - Creating indexes');

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Create design documents for indexes
    await databaseV4.putDoc(staffDesignDoc);
    await databaseV4.putDoc(scheduleDesignDoc);
    await databaseV4.putDoc(attendanceDesignDoc);
    await databaseV4.putDoc(paymentDesignDoc);

    // Create critical payment indexes for performance and consistency
    console.log('Per-Staff - Creating payment performance indexes');
    
    // Index for getStaffPayments query (type, staffId, paymentDate)
    await databaseV4.createIndex({ 
      index: { fields: ['type', 'staffId', 'paymentDate'] }
    });
    
    // Index for payment status filtering
    await databaseV4.createIndex({ 
      index: { fields: ['type', 'staffId', 'status'] }
    });
    
    // Index for payment type filtering
    await databaseV4.createIndex({ 
      index: { fields: ['type', 'staffId', 'paymentType'] }
    });
    
    // Index for linked payment queries
    await databaseV4.createIndex({ 
      index: { fields: ['type', 'metadata.linkedPayrollId'] }
    });
    
    // Index for period-based queries
    await databaseV4.createIndex({ 
      index: { fields: ['type', 'staffId', 'periodStart', 'periodEnd'] }
    });

    console.log('Per-Staff - Indexes created successfully');
  } catch (error) {
    console.error('Per-Staff - Error creating indexes:', error);
    throw error;
  }
}

/**
 * Get all staff members
 */
export async function getAllStaff(): Promise<StaffDocument[]> {
  try {
    console.log('Per-Staff - Getting all staff members');

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Ensure index on type and name for sorting
    await databaseV4.createIndex({ index: { fields: ['type', 'name'] } });

    // Query all staff documents using the correct method
    // Sort must match the index fields: ['type', 'name']
    const result = await databaseV4.findDocs({
      selector: { type: 'staff' },
      sort: [{ type: 'asc' }, { name: 'asc' }]
    });

    console.log(`Per-Staff - Found ${result.docs.length} staff members`);
    return result.docs as StaffDocument[];
  } catch (error) {
    console.error('Per-Staff - Error getting all staff:', error);
    return [];
  }
}

/**
 * Get staff member by ID
 */
export async function getStaffMember(id: string): Promise<StaffDocument | null> {
  try {
    console.log(`Per-Staff - Getting staff member with ID: ${id}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `staff:${id}`;
    const staff = await databaseV4.getDoc<StaffDocument>(docId);
    return staff;
  } catch (error: any) {
    if (error.status === 404) {
      console.log(`Per-Staff - Staff member ${id} not found`);
      return null;
    }
    console.error(`Per-Staff - Error getting staff member ${id}:`, error);
    throw error;
  }
}

/**
 * Add staff member
 */
export async function addStaffMember(staffData: Partial<StaffDocument>): Promise<StaffDocument> {
  try {
    console.log('Per-Staff - Adding staff member:', staffData.name);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Generate ID if not provided
    const staffId = staffData.id || uuidv4();
    const docId = `staff:${staffId}`;

    // Check if staff member already exists
    try {
      const existing = await databaseV4.getDoc<StaffDocument>(docId);
      console.log(`Per-Staff - Staff member ${staffId} already exists, updating instead`);
      return updateStaffMember(staffId, staffData);
    } catch (error: any) {
      if (error.status !== 404) {
        throw error;
      }
      // Staff doesn't exist, continue with creation
    }

    // Create new staff document
    const newStaff: StaffDocument = {
      _id: docId,
      type: 'staff',
      id: staffId,
      name: staffData.name || 'New Staff',
      role: staffData.role || 'STAFF',
      contact: staffData.contact || '',
      email: staffData.email || '',
      phone: staffData.phone || '',
      status: staffData.status || 'ACTIVE',
      paymentConfig: {
        type: staffData.paymentConfig?.type || 'MONTHLY',
        baseSalary: staffData.paymentConfig?.baseSalary || 0,
        shiftRate: staffData.paymentConfig?.shiftRate,
        shiftRates: staffData.paymentConfig?.shiftRates,
        paymentDay: staffData.paymentConfig?.paymentDay
      },
      userId: staffData.userId,
      hasUserAccount: staffData.hasUserAccount || false,
      username: staffData.username,
      pendingAuth: staffData.pendingAuth || false,
      permissions: staffData.permissions || DEFAULT_STAFF_PERMISSIONS,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Validate the document
    validateSchema(newStaff, staffDocumentSchema);

    // Save to database
    await databaseV4.putDoc(newStaff);

    console.log(`Per-Staff - Staff member ${staffId} created successfully`);
    return newStaff;
  } catch (error) {
    console.error('Per-Staff - Error adding staff member:', error);
    throw error;
  }
}

/**
 * Update staff member
 */
export async function updateStaffMember(id: string, updates: Partial<StaffDocument>): Promise<StaffDocument> {
  try {
    console.log(`Per-Staff - Updating staff member ${id}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `staff:${id}`;
    const existing = await databaseV4.getDoc<StaffDocument>(docId);

    // 👉 Ensure nested paymentConfig fields are preserved when only partial updates are provided
    const updatedPaymentConfig = updates.paymentConfig
      ? { ...existing.paymentConfig, ...updates.paymentConfig }
      : existing.paymentConfig;

    // Remove deprecated field if it exists to avoid schema validation errors
    if ('nextPaymentDate' in (updatedPaymentConfig as any)) {
      delete (updatedPaymentConfig as any).nextPaymentDate;
    }

    // Update the staff member
    const updated: StaffDocument = {
      ...existing,
      ...updates,
      paymentConfig: updatedPaymentConfig,
      _id: docId,
      type: 'staff',
      id: id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    // Validate the updated document
    validateSchema(updated, staffDocumentSchema);

    // Save to database
    await databaseV4.putDoc(updated);

    console.log(`Per-Staff - Staff member ${id} updated successfully`);
    return updated;
  } catch (error) {
    console.error(`Per-Staff - Error updating staff member ${id}:`, error);
    throw error;
  }
}

/**
 * Delete staff member
 */
export async function deleteStaffMember(id: string): Promise<boolean> {
  try {
    console.log(`Per-Staff - Deleting staff member ${id}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `staff:${id}`;
    const staff = await databaseV4.getDoc<StaffDocument>(docId);

    // Delete the staff document
    await databaseV4.deleteDoc(docId, staff._rev!);

    // Also delete related schedule and attendance documents
    try {
      await deleteStaffSchedule(id);
    } catch (error) {
      console.warn(`Per-Staff - Error deleting schedule for staff ${id}:`, error);
    }

    try {
      await deleteStaffAttendance(id);
    } catch (error) {
      console.warn(`Per-Staff - Error deleting attendance for staff ${id}:`, error);
    }

    console.log(`Per-Staff - Staff member ${id} deleted successfully`);
    return true;
  } catch (error) {
    console.error(`Per-Staff - Error deleting staff member ${id}:`, error);
    throw error;
  }
}

/**
 * Get staff schedule
 */
export async function getStaffSchedule(staffId: string): Promise<ScheduleDocument | null> {
  try {
    console.log(`Per-Staff - Getting schedule for staff ${staffId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `schedule:${staffId}`;
    const schedule = await databaseV4.getDoc<ScheduleDocument>(docId);
    return schedule;
  } catch (error: any) {
    if (error.status === 404) {
      console.log(`Per-Staff - Schedule for staff ${staffId} not found`);
      return null;
    }
    console.error(`Per-Staff - Error getting schedule for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Create or update staff schedule
 */
export async function setStaffSchedule(staffId: string, scheduleData: Partial<ScheduleDocument>): Promise<ScheduleDocument> {
  try {
    console.log(`Per-Staff - Setting schedule for staff ${staffId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `schedule:${staffId}`;

    // Try to get existing schedule
    let existing: ScheduleDocument | null = null;
    try {
      existing = await databaseV4.getDoc<ScheduleDocument>(docId);
    } catch (error: any) {
      if (error.status !== 404) {
        throw error;
      }
    }

    // Create or update schedule
    const schedule: ScheduleDocument = {
      _id: docId,
      _rev: existing?._rev,
      type: 'schedule',
      staffId: staffId,
      weeklySchedule: scheduleData.weeklySchedule || {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: []
      },
      effectiveFrom: scheduleData.effectiveFrom || new Date().toISOString(),
      effectiveTo: scheduleData.effectiveTo,
      isActive: scheduleData.isActive !== undefined ? scheduleData.isActive : true,
      createdAt: existing?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Validate the document
    validateSchema(schedule, scheduleDocumentSchema);

    // Save to database
    await databaseV4.putDoc(schedule);

    console.log(`Per-Staff - Schedule for staff ${staffId} saved successfully`);
    return schedule;
  } catch (error) {
    console.error(`Per-Staff - Error setting schedule for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Delete staff schedule
 */
export async function deleteStaffSchedule(staffId: string): Promise<boolean> {
  try {
    console.log(`Per-Staff - Deleting schedule for staff ${staffId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `schedule:${staffId}`;
    const schedule = await databaseV4.getDoc<ScheduleDocument>(docId);

    await databaseV4.deleteDoc(docId, schedule._rev!);

    console.log(`Per-Staff - Schedule for staff ${staffId} deleted successfully`);
    return true;
  } catch (error: any) {
    if (error.status === 404) {
      console.log(`Per-Staff - Schedule for staff ${staffId} not found`);
      return true;
    }
    console.error(`Per-Staff - Error deleting schedule for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Get staff attendance
 */
export async function getStaffAttendance(staffId: string): Promise<AttendanceDocument | null> {
  try {
    console.log(`Per-Staff - Getting attendance for staff ${staffId}`);

    // 🚩 Special bypass for collective staff orders: no attendance document
    if (staffId === 'staff-collective') {
      console.log('Bypassing attendance retrieval for staff-collective ID.');
      return null;
    }

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `attendance:${staffId}`;
    const attendance = await databaseV4.getDoc<AttendanceDocument>(docId);
    return attendance;
  } catch (error: any) {
    if (error.status === 404) {
      console.log(`📋 Per-Staff - No attendance history found for staff ${staffId} (this is normal for new staff members)`);
      return null;
    }
    console.error(`Per-Staff - Error getting attendance for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Record staff attendance
 */
export async function recordStaffAttendance(
  staffId: string,
  date: string,
  status: 'present' | 'late' | 'absent',
  shiftId?: string,
  shiftName?: string,
  notes?: string
): Promise<AttendanceRecord> {
  try {
    console.log(`Per-Staff - Recording attendance for staff ${staffId} on ${date}`);

    if (!databaseV4.isInitialized) {
      console.warn('⚠️ Database not initialized for attendance recording, attempting to wait for initialization...');
      
      // Try to get current restaurant ID and wait for initialization
      const { getCurrentRestaurantId } = await import('../utils/restaurant-id');
      const restaurantId = getCurrentRestaurantId();
      
      if (restaurantId) {
        try {
          console.log('🔄 Attempting to wait for database initialization...');
          await databaseV4.waitForInitialization(restaurantId, 30000); // 30 second timeout
          console.log('✅ Database initialized successfully');
        } catch (waitError) {
          console.error('❌ Database initialization timeout:', waitError);
          throw new Error('Database initialization timeout. Cannot record attendance.');
        }
      } else {
        throw new Error('No restaurant ID found. Cannot initialize database for attendance recording.');
      }
    }

    const docId = `attendance:${staffId}`;

    // Get or create attendance document
    let attendance: AttendanceDocument;
    try {
      attendance = await databaseV4.getDoc<AttendanceDocument>(docId);
    } catch (error: any) {
      if (error.status === 404) {
        // Create new attendance document
        attendance = {
          _id: docId,
          type: 'attendance',
          staffId: staffId,
          records: [],
          schemaVersion: 'v4.0',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      } else {
        throw error;
      }
    }

    // Check if record already exists for this date
    const existingIndex = attendance.records.findIndex(r => r.date === date && r.shiftId === shiftId);

    // Create attendance record
    const record: AttendanceRecord = {
      id: existingIndex !== -1 ? attendance.records[existingIndex].id : uuidv4(),
      date,
      status,
      shiftId,
      shiftName,
      notes,
      isPaid: existingIndex !== -1 ? attendance.records[existingIndex].isPaid : false
    };

    // Update or add record
    if (existingIndex !== -1) {
      attendance.records[existingIndex] = record;
    } else {
      attendance.records.push(record);
    }

    // Update timestamp
    attendance.updatedAt = new Date().toISOString();

    // Validate and save
    validateSchema(attendance, attendanceDocumentSchema);
    await databaseV4.putDoc(attendance);

    console.log(`Per-Staff - Attendance recorded for staff ${staffId} on ${date}`);
    return record;
  } catch (error) {
    console.error(`Per-Staff - Error recording attendance for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Get staff attendance records for date range
 */
export async function getStaffAttendanceRecords(
  staffId: string,
  startDate: string,
  endDate: string
): Promise<AttendanceRecord[]> {
  try {
    console.log(`Per-Staff - Getting attendance records for staff ${staffId} from ${startDate} to ${endDate}`);

    // 🚩 Special bypass for collective staff orders: no attendance records
    if (staffId === 'staff-collective') {
      console.log('Bypassing attendance record retrieval for staff-collective ID.');
      return [];
    }

    if (!databaseV4.isInitialized) {
      console.warn('⚠️ Database not initialized for attendance retrieval, attempting to wait for initialization...');
      
      // Try to get current restaurant ID and wait for initialization
      const { getCurrentRestaurantId } = await import('../utils/restaurant-id');
      const restaurantId = getCurrentRestaurantId();
      
      if (restaurantId) {
        try {
          console.log('🔄 Attempting to wait for database initialization...');
          await databaseV4.waitForInitialization(restaurantId, 30000); // 30 second timeout
          console.log('✅ Database initialized successfully');
        } catch (waitError) {
          console.error('❌ Database initialization timeout:', waitError);
          return []; // Return empty array instead of throwing for retrieval operations
        }
      } else {
        console.warn('No restaurant ID found. Cannot initialize database for attendance retrieval.');
        return []; // Return empty array instead of throwing for retrieval operations
      }
    }

    const attendance = await getStaffAttendance(staffId);
    if (!attendance) {
      return [];
    }

    // Filter records by date range
    return attendance.records.filter(
      record => record.date >= startDate && record.date <= endDate
    );
  } catch (error) {
    console.error(`Per-Staff - Error getting attendance records for staff ${staffId}:`, error);
    return [];
  }
}

/**
 * Delete staff attendance
 */
export async function deleteStaffAttendance(staffId: string): Promise<boolean> {
  try {
    console.log(`Per-Staff - Deleting attendance for staff ${staffId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const docId = `attendance:${staffId}`;
    const attendance = await databaseV4.getDoc<AttendanceDocument>(docId);

    await databaseV4.deleteDoc(docId, attendance._rev!);

    console.log(`Per-Staff - Attendance for staff ${staffId} deleted successfully`);
    return true;
  } catch (error: any) {
    if (error.status === 404) {
      console.log(`📋 Per-Staff - No attendance document to delete for staff ${staffId} (already clean)`);
      return true;
    }
    console.error(`Per-Staff - Error deleting attendance for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Mark attendance records as paid
 */
export async function markAttendanceAsPaid(
  staffId: string,
  recordIds: string[]
): Promise<boolean> {
  try {
    console.log(`Per-Staff - Marking attendance records as paid for staff ${staffId}`);

    const attendance = await getStaffAttendance(staffId);
    if (!attendance) {
      throw new Error(`Attendance document not found for staff ${staffId}`);
    }

    // Update isPaid status for specified records
    let updated = false;
    attendance.records.forEach(record => {
      if (recordIds.includes(record.id)) {
        record.isPaid = true;
        updated = true;
      }
    });

    if (updated) {
      attendance.updatedAt = new Date().toISOString();
      await databaseV4.putDoc(attendance);
      console.log(`Per-Staff - Marked ${recordIds.length} records as paid for staff ${staffId}`);
    }

    return updated;
  } catch (error) {
    console.error(`Per-Staff - Error marking attendance as paid for staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * PAYMENT OPERATIONS
 */

/**
 * Create a payment record
 */
export async function createPayment(paymentData: Omit<PaymentDocument, '_id' | '_rev' | 'type' | 'createdAt' | 'updatedAt'>): Promise<PaymentDocument> {
  try {
    console.log('Per-Staff - Creating payment for staff:', paymentData.staffId);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Generate payment ID with date prefix for better organization
    const paymentDate = new Date(paymentData.paymentDate);
    const datePrefix = paymentDate.toISOString().split('T')[0]; // YYYY-MM-DD
    const paymentId = `payment:${datePrefix}-${uuidv4()}`;

    // Validate attendance IDs if provided
    if (paymentData.metadata?.paidAttendanceIds) {
      const attendanceDoc = await getStaffAttendance(paymentData.staffId);
      if (attendanceDoc) {
        const alreadyPaidIds = paymentData.metadata.paidAttendanceIds.filter(id => {
          const record = attendanceDoc.records.find(r => r.id === id);
          return record?.isPaid === true;
        });
        
        if (alreadyPaidIds.length > 0) {
          throw new Error(`Some attendance records are already paid: ${alreadyPaidIds.join(', ')}`);
        }
      }
    }

    // Create payment document
    const newPayment: PaymentDocument = {
      _id: paymentId,
      type: 'payment',
      ...paymentData,
      status: paymentData.status || 'COMPLETED',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Validate schema
    validateSchema(newPayment, paymentDocumentSchema);

    // Save payment document
    const result = await databaseV4.putDoc(newPayment);
    newPayment._rev = result.rev;

    // Update attendance records as paid if provided
    if (paymentData.metadata?.paidAttendanceIds && paymentData.metadata.paidAttendanceIds.length > 0) {
      await markAttendanceAsPaid(paymentData.staffId, paymentData.metadata.paidAttendanceIds);
    }

    console.log(`Per-Staff - Payment created successfully: ${paymentId}`);
    return newPayment;
  } catch (error) {
    console.error('Per-Staff - Error creating payment:', error);
    throw error;
  }
}

/**
 * Get all payments for a staff member
 */
export async function getStaffPayments(staffId: string): Promise<PaymentDocument[]> {
  try {
    console.log(`Per-Staff - Getting payments for staff: ${staffId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Ensure the payment index exists for optimal performance
    await databaseV4.createIndex({ 
      index: { fields: ['type', 'staffId', 'paymentDate'] }
    });

    // Query payments by staff ID, sorted by date descending
    // This query now uses the optimized index
    const result = await databaseV4.findDocs({
      selector: { 
        type: 'payment',
        staffId: staffId
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { paymentDate: 'desc' }]
    });

    if (!result || !result.docs) {
      console.warn(`Per-Staff - No payment data returned for staff ${staffId}`);
      return [];
    }

    console.log(`Per-Staff - Found ${result.docs.length} payments for staff ${staffId}`);
    return result.docs as PaymentDocument[];
  } catch (error) {
    console.error(`Per-Staff - Error getting payments for staff ${staffId}:`, error);
    // Throw error instead of returning empty array to surface issues
    throw new Error(`Failed to retrieve payments for staff ${staffId}: ${error}`);
  }
}

/**
 * Get payments by date range
 */
export async function getStaffPaymentsByDateRange(
  staffId: string,
  startDate: string,
  endDate: string
): Promise<PaymentDocument[]> {
  try {
    console.log(`Per-Staff - Getting payments for staff ${staffId} from ${startDate} to ${endDate}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = await databaseV4.findDocs({
      selector: {
        type: 'payment',
        staffId: staffId,
        paymentDate: {
          $gte: startDate,
          $lte: endDate
        }
      },
      sort: [{ type: 'asc' }, { paymentDate: 'desc' }]
    });

    console.log(`Per-Staff - Found ${result.docs.length} payments in date range`);
    return result.docs as PaymentDocument[];
  } catch (error) {
    console.error(`Per-Staff - Error getting payments by date range:`, error);
    return [];
  }
}

/**
 * Get payments by period (for salary payments)
 */
export async function getStaffPaymentsByPeriod(
  staffId: string,
  periodStart: string,
  periodEnd: string
): Promise<PaymentDocument[]> {
  try {
    console.log(`Per-Staff - Getting payments for period ${periodStart} to ${periodEnd}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    // Find payments that overlap with the given period
    const result = await databaseV4.findDocs({
      selector: {
        type: 'payment',
        staffId: staffId,
        $or: [
          // Payment period overlaps with query period
          {
            periodStart: { $lte: periodEnd },
            periodEnd: { $gte: periodStart }
          },
          // Payment without period but date within range
          {
            periodStart: { $exists: false },
            paymentDate: {
              $gte: periodStart,
              $lte: periodEnd
            }
          }
        ]
      }
    });

    console.log(`Per-Staff - Found ${result.docs.length} payments for period`);
    return result.docs as PaymentDocument[];
  } catch (error) {
    console.error(`Per-Staff - Error getting payments by period:`, error);
    return [];
  }
}

/**
 * Get advance balance for a staff member
 */
export async function getStaffAdvanceBalance(staffId: string): Promise<number> {
  try {
    console.log(`💰 Getting simplified advance balance for staff: ${staffId}`);

    const payments = await getStaffPayments(staffId);
    
    let balance = 0;
    payments.forEach(payment => {
      if (payment.paymentType === 'ADVANCE') {
        // ✅ Simple: Only count ADVANCE payments
        // Positive = money given to staff (increases debt)
        // Negative = repayment (decreases debt)
        balance += payment.amount;
        console.log(`💰 Advance: ${payment.amount > 0 ? '+' : ''}${payment.amount} (${payment.notes || 'No notes'})`);
      }
    });

    // Ensure balance doesn't go negative
    balance = Math.max(0, balance);

    console.log(`💰 Final advance balance for staff ${staffId}: ${balance}`);
    return balance;
  } catch (error) {
    console.error(`❌ Error getting advance balance:`, error);
    return 0;
  }
}

/**
 * Calculate unpaid balance for per-shift staff
 */
export async function calculateUnpaidBalance(staffId: string): Promise<{
  totalUnpaidShifts: number;
  totalUnpaidAmount: number;
  shiftBreakdown: Array<{
    shiftId: string;
    shiftName: string;
    count: number;
    rate: number;
    amount: number;
  }>;
}> {
  try {
    console.log(`Per-Staff - Calculating unpaid balance for staff: ${staffId}`);

    // Get staff member to access payment config
    const staff = await getStaffMember(staffId);
    if (!staff) {
      throw new Error(`Staff member ${staffId} not found`);
    }

    // Get attendance records
    const attendanceDoc = await getStaffAttendance(staffId);
    if (!attendanceDoc) {
      return { totalUnpaidShifts: 0, totalUnpaidAmount: 0, shiftBreakdown: [] };
    }

    // Filter unpaid records
    const unpaidRecords = attendanceDoc.records.filter(record => 
      record.status === 'present' && !record.isPaid && record.shiftId && record.shiftName
    );

    // Group by shift and calculate amounts
    const shiftGroups: Record<string, {
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
    }> = {};

    unpaidRecords.forEach(record => {
      if (!record.shiftId || !record.shiftName) return;

      const key = `${record.shiftId}-${record.shiftName}`;
      if (!shiftGroups[key]) {
        // Get rate from staff config
        const rate = staff.paymentConfig.shiftRates?.[record.shiftId] || 
                    staff.paymentConfig.shiftRate || 
                    0;

        shiftGroups[key] = {
          shiftId: record.shiftId,
          shiftName: record.shiftName,
          count: 0,
          rate: rate
        };
      }
      shiftGroups[key].count++;
    });

    // Calculate breakdown
    const shiftBreakdown = Object.values(shiftGroups).map(group => ({
      ...group,
      amount: group.count * group.rate
    }));

    const totalUnpaidShifts = unpaidRecords.length;
    const totalUnpaidAmount = shiftBreakdown.reduce((sum, shift) => sum + shift.amount, 0);

    console.log(`Per-Staff - Unpaid balance: ${totalUnpaidShifts} shifts, ${totalUnpaidAmount} amount`);
    return { totalUnpaidShifts, totalUnpaidAmount, shiftBreakdown };
  } catch (error) {
    console.error(`Per-Staff - Error calculating unpaid balance:`, error);
    return { totalUnpaidShifts: 0, totalUnpaidAmount: 0, shiftBreakdown: [] };
  }
}

/**
 * Get period payment status for salary staff
 */
export async function getPeriodPaymentStatus(
  staffId: string,
  periodStart: string,
  periodEnd: string
): Promise<{
  earnedAmount: number;
  paidAmount: number;
  remainingAmount: number;
  isPeriodFullyPaid: boolean;
}> {
  try {
    console.log(`Per-Staff - Getting period payment status for staff ${staffId}`);

    // Get staff member
    const staff = await getStaffMember(staffId);
    if (!staff) {
      throw new Error(`Staff member ${staffId} not found`);
    }

    // Calculate earned amount based on payment type
    let earnedAmount = 0;
    if (staff.paymentConfig.type === 'MONTHLY') {
      earnedAmount = staff.paymentConfig.baseSalary;
    } else if (staff.paymentConfig.type === 'DAILY') {
      const startDate = new Date(periodStart);
      const endDate = new Date(periodEnd);
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      earnedAmount = staff.paymentConfig.baseSalary * days;
    } else if (staff.paymentConfig.type === 'WEEKLY') {
      const startDate = new Date(periodStart);
      const endDate = new Date(periodEnd);
      const weeks = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
      earnedAmount = staff.paymentConfig.baseSalary * weeks;
    }

    // Get payments for this period
    const payments = await getStaffPaymentsByPeriod(staffId, periodStart, periodEnd);
    const paidAmount = payments
      .filter(p => p.paymentType === 'SALARY' && p.status === 'COMPLETED')
      .reduce((sum, p) => sum + p.amount, 0);

    const remainingAmount = earnedAmount - paidAmount;
    const isPeriodFullyPaid = remainingAmount <= 0;

    console.log(`Per-Staff - Period status: earned=${earnedAmount}, paid=${paidAmount}, remaining=${remainingAmount}`);
    return { earnedAmount, paidAmount, remainingAmount, isPeriodFullyPaid };
  } catch (error) {
    console.error(`Per-Staff - Error getting period payment status:`, error);
    return { earnedAmount: 0, paidAmount: 0, remainingAmount: 0, isPeriodFullyPaid: false };
  }
}

/**
 * Update payment status
 */
export async function updatePaymentStatus(
  paymentId: string,
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED'
): Promise<PaymentDocument> {
  try {
    console.log(`Per-Staff - Updating payment status: ${paymentId} to ${status}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const payment = await databaseV4.getDoc<PaymentDocument>(paymentId);
    if (!payment) {
      throw new Error(`Payment ${paymentId} not found`);
    }

    const updatedPayment = {
      ...payment,
      status,
      updatedAt: new Date().toISOString()
    };

    const result = await databaseV4.putDoc(updatedPayment);
    updatedPayment._rev = result.rev;

    console.log(`Per-Staff - Payment status updated: ${paymentId}`);
    return updatedPayment;
  } catch (error) {
    console.error(`Per-Staff - Error updating payment status:`, error);
    throw error;
  }
}

/**
 * Update payment note
 */
export async function updatePaymentNote(
  paymentId: string,
  note: string
): Promise<PaymentDocument> {
  try {
    console.log(`Per-Staff - Updating payment note: ${paymentId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const payment = await databaseV4.getDoc<PaymentDocument>(paymentId);
    if (!payment) {
      throw new Error(`Payment ${paymentId} not found`);
    }

    const updatedPayment = {
      ...payment,
      notes: note,
      updatedAt: new Date().toISOString()
    };

    const result = await databaseV4.putDoc(updatedPayment);
    updatedPayment._rev = result.rev;

    console.log(`Per-Staff - Payment note updated: ${paymentId}`);
    return updatedPayment;
  } catch (error) {
    console.error(`Per-Staff - Error updating payment note:`, error);
    throw error;
  }
}

/**
 * Update payment metadata (for linking adjustments to payroll)
 */
export async function updatePaymentMetadata(
  paymentId: string,
  metadata: Record<string, any>
): Promise<PaymentDocument> {
  try {
    console.log(`Per-Staff - Updating payment metadata: ${paymentId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const payment = await databaseV4.getDoc<PaymentDocument>(paymentId);
    if (!payment) {
      throw new Error(`Payment ${paymentId} not found`);
    }

    const updatedPayment = {
      ...payment,
      metadata: {
        ...payment.metadata,
        ...metadata
      },
      updatedAt: new Date().toISOString()
    };

    const result = await databaseV4.putDoc(updatedPayment);
    updatedPayment._rev = result.rev;

    console.log(`Per-Staff - Payment metadata updated: ${paymentId}`);
    return updatedPayment;
  } catch (error) {
    console.error(`Per-Staff - Error updating payment metadata:`, error);
    throw error;
  }
}

/**
 * Delete payment
 */
export async function deletePayment(paymentId: string): Promise<boolean> {
  try {
    console.log(`Per-Staff - Deleting payment: ${paymentId}`);

    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const payment = await databaseV4.getDoc<PaymentDocument>(paymentId);
    if (!payment) {
      console.log(`Per-Staff - Payment ${paymentId} not found`);
      return false;
    }

    // If payment had paid attendance IDs, unmark them
    if (payment.metadata?.paidAttendanceIds && payment.metadata.paidAttendanceIds.length > 0) {
      await unmarkAttendanceAsPaid(payment.staffId, payment.metadata.paidAttendanceIds);
    }

    await databaseV4.deleteDoc(paymentId, payment._rev!);
    console.log(`Per-Staff - Payment deleted: ${paymentId}`);
    return true;
  } catch (error) {
    console.error(`Per-Staff - Error deleting payment:`, error);
    return false;
  }
}

/**
 * Unmark attendance records as paid (helper for payment deletion)
 */
async function unmarkAttendanceAsPaid(staffId: string, recordIds: string[]): Promise<boolean> {
  try {
    console.log(`Per-Staff - Unmarking attendance as paid for staff ${staffId}:`, recordIds);

    const attendanceDoc = await getStaffAttendance(staffId);
    if (!attendanceDoc) {
      console.log(`Per-Staff - No attendance document found for staff ${staffId}`);
      return false;
    }

    // Update isPaid flag for specified records
    let updated = false;
    attendanceDoc.records.forEach(record => {
      if (recordIds.includes(record.id) && record.isPaid) {
        record.isPaid = false;
        updated = true;
      }
    });

    if (updated) {
      attendanceDoc.updatedAt = new Date().toISOString();
      await databaseV4.putDoc(attendanceDoc);
      console.log(`Per-Staff - Unmarked ${recordIds.length} attendance records as unpaid`);
    }

    return updated;
  } catch (error) {
    console.error(`Per-Staff - Error unmarking attendance as paid:`, error);
    return false;
  }
}