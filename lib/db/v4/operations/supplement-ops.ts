"use client";

import { v4 as uuidv4 } from 'uuid';
import { Supplement } from '../schemas/menu-schema';
import { ConsumptionLog } from '../schemas/inventory-schema';
import { addConsumptionLog } from './inventory-ops';
import { getMenu, updateMenu } from './menu-ops';

// Category-specific supplement configuration interface
export interface CategorySupplementConfig {
  globalPricing: { [sizeName: string]: number };
  isEnabled?: boolean; // Optional since we always use global pricing
}

/**
 * Get category-specific supplement pricing configuration
 */
export async function getCategorySupplementConfig(categoryId: string): Promise<CategorySupplementConfig> {
  const menu = await getMenu();
  const category = menu.categories.find(c => c.id === categoryId);
  return category?.supplementConfig || {
    globalPricing: {},
    isEnabled: true
  };
}

/**
 * Update category-specific supplement pricing configuration
 */
export async function updateCategorySupplementConfig(categoryId: string, config: CategorySupplementConfig): Promise<void> {
  const menu = await getMenu();
  const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
  
  if (categoryIndex === -1) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }
  
  menu.categories[categoryIndex].supplementConfig = config;
  menu.updatedAt = new Date().toISOString();
  await updateMenu(menu);
}

/**
 * Get supplement pricing for a specific size
 */
export async function getSupplementPriceForSize(categoryId: string, sizeName: string): Promise<number> {
  const config = await getCategorySupplementConfig(categoryId);
  return config.globalPricing[sizeName] || 0;
}

/**
 * Get all supplement prices for a category
 */
export async function getAllSupplementPrices(categoryId: string): Promise<{ [sizeName: string]: number }> {
  const config = await getCategorySupplementConfig(categoryId);
  return config.globalPricing;
}

/**
 * Get all supplements for a specific category
 */
export async function getAllSupplements(categoryId: string): Promise<Supplement[]> {
  const menu = await getMenu();
  const category = menu.categories.find(c => c.id === categoryId);
  return category?.supplements || [];
}

/**
 * Get supplement by ID within a specific category
 */
export async function getSupplementById(categoryId: string, supplementId: string): Promise<Supplement | null> {
  const menu = await getMenu();
  const category = menu.categories.find(c => c.id === categoryId);
  const supplements = category?.supplements || [];
  return supplements.find(supplement => supplement.id === supplementId) || null;
}

/**
 * Find a supplement by ID across all categories (for backward compatibility)
 * Returns the supplement and its category ID
 */
export async function findSupplementById(supplementId: string): Promise<{ supplement: Supplement; categoryId: string } | null> {
  const menu = await getMenu();
  
  for (const category of menu.categories) {
    const supplements = category.supplements || [];
    const supplement = supplements.find(s => s.id === supplementId);
    if (supplement) {
      return { supplement, categoryId: category.id };
    }
  }
  
  return null;
}

/**
 * Create a new supplement in a specific category
 */
export async function createSupplement(categoryId: string, supplementData: {
  name: string;
  description?: string;
  stockConsumption?: {
    stockItemId: string;
    quantities: { [sizeName: string]: number };
  };
  color?: string;
  image?: string;
  isActive?: boolean;
}): Promise<Supplement> {
  const menu = await getMenu();
  const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
  
  if (categoryIndex === -1) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }
  
  const supplement: Supplement = {
    id: uuidv4(),
    name: supplementData.name,
    description: supplementData.description,
    stockConsumption: supplementData.stockConsumption,
    color: supplementData.color,
    image: supplementData.image,
    isActive: supplementData.isActive !== false // Default to true
  };

  if (!menu.categories[categoryIndex].supplements) {
    menu.categories[categoryIndex].supplements = [];
  }
  
  menu.categories[categoryIndex].supplements!.push(supplement);
  menu.updatedAt = new Date().toISOString();
  
  await updateMenu(menu);
  return supplement;
}

/**
 * Update a supplement in a specific category
 */
export async function updateSupplement(categoryId: string, supplementId: string, updates: Partial<Supplement>): Promise<void> {
  const menu = await getMenu();
  const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
  
  if (categoryIndex === -1) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }
  
  const supplements = menu.categories[categoryIndex].supplements || [];
  const supplementIndex = supplements.findIndex(s => s.id === supplementId);
  
  if (supplementIndex === -1) {
    throw new Error(`Supplement with ID ${supplementId} not found in category ${categoryId}`);
  }
  
  // Update the supplement with new data
  menu.categories[categoryIndex].supplements![supplementIndex] = {
    ...menu.categories[categoryIndex].supplements![supplementIndex],
    ...updates
  };
  
  menu.updatedAt = new Date().toISOString();
  await updateMenu(menu);
}

/**
 * Delete a supplement from a specific category
 */
export async function deleteSupplement(categoryId: string, supplementId: string): Promise<void> {
  const menu = await getMenu();
  const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
  
  if (categoryIndex === -1) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }
  
  const supplements = menu.categories[categoryIndex].supplements || [];
  const supplementIndex = supplements.findIndex(s => s.id === supplementId);
  
  if (supplementIndex === -1) {
    throw new Error(`Supplement with ID ${supplementId} not found in category ${categoryId}`);
  }
  
  menu.categories[categoryIndex].supplements!.splice(supplementIndex, 1);
  menu.updatedAt = new Date().toISOString();
  
  await updateMenu(menu);
}

/**
 * Process supplement consumption for an order item
 */
export async function processSupplementConsumption(
  categoryId: string,
  supplementId: string,
  sizeName: string,
  quantity: number = 1,
  orderId: string = 'supplement-consumption'
): Promise<void> {
  const supplement = await getSupplementById(categoryId, supplementId);
  
  if (!supplement || !supplement.stockConsumption) {
    console.warn(`Supplement ${supplementId} not found or has no stock consumption configuration`);
    return;
  }

  const consumptionQuantity = supplement.stockConsumption.quantities[sizeName];
  if (!consumptionQuantity || consumptionQuantity <= 0) {
    console.warn(`No consumption quantity defined for supplement ${supplementId} size ${sizeName}`);
    return;
  }

  const totalConsumption = consumptionQuantity * quantity;

  const consumptionLog: ConsumptionLog = {
    id: uuidv4(),
    orderId: orderId,
    stockItemId: supplement.stockConsumption.stockItemId,
    quantity: totalConsumption,
    costPerUnit: 0, // Will be calculated by the system
    totalCost: 0, // Will be calculated by the system
    menuItemId: supplement.id,
    menuItemName: supplement.name,
    date: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  await addConsumptionLog(consumptionLog);
}

/**
 * Helper function to get supplement stock consumption
 */
export function getSupplementStockConsumption(supplement: Supplement): {
  stockItemId: string;
  quantities: { [sizeName: string]: number };
} | null {
  return supplement.stockConsumption || null;
}

/**
 * Get active supplements only for a specific category
 */
export async function getActiveSupplements(categoryId: string): Promise<Supplement[]> {
  const supplements = await getAllSupplements(categoryId);
  return supplements.filter(supplement => supplement.isActive !== false);
}

/**
 * Toggle supplement status (active/inactive) in a specific category
 */
export async function toggleSupplementStatus(categoryId: string, supplementId: string, isActive: boolean): Promise<Supplement> {
  const menu = await getMenu();
  const categoryIndex = menu.categories.findIndex(c => c.id === categoryId);
  
  if (categoryIndex === -1) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }
  
  const supplements = menu.categories[categoryIndex].supplements || [];
  const supplementIndex = supplements.findIndex(s => s.id === supplementId);
  
  if (supplementIndex === -1) {
    throw new Error(`Supplement with ID ${supplementId} not found in category ${categoryId}`);
  }
  
  menu.categories[categoryIndex].supplements![supplementIndex].isActive = isActive;
  menu.updatedAt = new Date().toISOString();
  
  await updateMenu(menu);
  return menu.categories[categoryIndex].supplements![supplementIndex];
} 