"use client";

/**
 * V4 Shifts Operations
 * 
 * Operations for managing shifts in the database
 */

import { databaseV4 } from '../core/db-instance';
import { ShiftsDocument, DEFAULT_SHIFTS_DOCUMENT } from '../schemas/shifts-schema';
import { v4 as uuidv4 } from 'uuid';
import {
  retryWithConflictResolution,
  safeGetDocument,
  safeUpdateDocument,
  safeUpsertDocument,
  safeEnsureDocument
} from '../core/conflict-resolution';

/**
 * Get all shifts with conflict resolution
 */
export async function getShifts(): Promise<ShiftsDocument['shifts']> {
  try {
    console.log('V4 Shifts - Getting all shifts');
    
    // Ensure database is initialized
    if (!databaseV4.isInitialized) {
      console.warn('V4 Shifts - Database not initialized, waiting for initialization');
      const restaurantId = databaseV4.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Database not initialized. No restaurant context. Call initialize() first.');
      }
      await databaseV4.waitForInitialization(restaurantId);
    }
    
    const shiftsDoc = await safeEnsureDocument<ShiftsDocument>(
      'shifts',
      () => ({
        ...DEFAULT_SHIFTS_DOCUMENT,
        _id: 'shifts',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }),
      'getShifts'
    );
    
    return shiftsDoc.shifts;
  } catch (error) {
    console.error('V4 Shifts - Error getting shifts:', error);
    throw error;
  }
}

/**
 * Create a new shift with conflict resolution
 */
export async function createShift(
  name: string,
  startTime: string,
  endTime: string,
  color?: string
): Promise<ShiftsDocument['shifts'][0]> {
  console.log(`V4 Shifts - Creating shift: ${name}`);
  
  // Ensure database is initialized
  if (!databaseV4.isInitialized) {
    console.warn('V4 Shifts - Database not initialized, waiting for initialization');
    const restaurantId = databaseV4.getCurrentRestaurantId();
    if (!restaurantId) {
      throw new Error('Database not initialized. No restaurant context. Call initialize() first.');
    }
    await databaseV4.waitForInitialization(restaurantId);
  }
  
  // Create the new shift
  const newShift = {
    id: uuidv4(),
    name,
    startTime,
    endTime,
    ...(color ? { color } : {})
  };
  
  const updatedDoc = await safeUpdateDocument<ShiftsDocument>(
    'shifts',
    (shiftsDoc) => ({
      ...shiftsDoc,
      shifts: [...shiftsDoc.shifts, newShift],
      updatedAt: new Date().toISOString()
    }),
    `createShift(${name})`
  );
  
  return newShift;
}

/**
 * Update an existing shift
 */
export async function updateShift(
  id: string,
  updates: {
    name?: string;
    startTime?: string;
    endTime?: string;
    color?: string;
  }
): Promise<ShiftsDocument['shifts'][0]> {
  try {
    console.log(`V4 Shifts - Updating shift: ${id}`);
    
    // Ensure database is initialized (wait for initialization if needed)
    if (!databaseV4.isInitialized) {
      console.warn('V4 Shifts - Database not initialized, waiting for initialization');
      const restaurantId = databaseV4.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Database not initialized. No restaurant context. Call initialize() first.');
      }
      await databaseV4.waitForInitialization(restaurantId);
    }
    
    // Get the shifts document
    const shiftsDoc = await databaseV4.getDoc<ShiftsDocument>('shifts');
    
    // Find the shift to update
    const shiftIndex = shiftsDoc.shifts.findIndex(shift => shift.id === id);
    if (shiftIndex === -1) {
      throw new Error(`Shift with ID ${id} not found`);
    }
    
    // Update the shift
    shiftsDoc.shifts[shiftIndex] = {
      ...shiftsDoc.shifts[shiftIndex],
      ...updates
    };
    shiftsDoc.updatedAt = new Date().toISOString();
    
    // Save the document
    await databaseV4.putDoc(shiftsDoc);
    
    return shiftsDoc.shifts[shiftIndex];
  } catch (error) {
    console.error(`V4 Shifts - Error updating shift ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a shift
 */
export async function deleteShift(id: string): Promise<boolean> {
  try {
    console.log(`V4 Shifts - Deleting shift: ${id}`);
    
    // Ensure database is initialized (wait for initialization if needed)
    if (!databaseV4.isInitialized) {
      console.warn('V4 Shifts - Database not initialized, waiting for initialization');
      const restaurantId = databaseV4.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Database not initialized. No restaurant context. Call initialize() first.');
      }
      await databaseV4.waitForInitialization(restaurantId);
    }
    
    // Get the shifts document
    const shiftsDoc = await databaseV4.getDoc<ShiftsDocument>('shifts');
    
    // Find and remove the shift
    const initialLength = shiftsDoc.shifts.length;
    shiftsDoc.shifts = shiftsDoc.shifts.filter(shift => shift.id !== id);
    
    if (shiftsDoc.shifts.length === initialLength) {
      // No shift was removed
      return false;
    }
    
    // Update timestamp
    shiftsDoc.updatedAt = new Date().toISOString();
    
    // Save the document
    await databaseV4.putDoc(shiftsDoc);
    
    return true;
  } catch (error) {
    console.error(`V4 Shifts - Error deleting shift ${id}:`, error);
    throw error;
  }
} 