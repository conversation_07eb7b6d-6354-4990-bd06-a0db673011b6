// knowledge: v4 menu item recipe schema (match v3)
import { RecipeIngredient, SubRecipeIngredient } from '../../../../types/cogs'; // Import enhanced ingredient types

export const menuItemRecipeSchema = {
  type: 'object',
  required: ['_id', 'type', 'menuItemId', 'ingredients', 'createdAt', 'updatedAt'],
  properties: {
    _id: { type: 'string' },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: { type: 'string', enum: ['menu-item-recipe'] },
    menuItemId: { type: 'string' },
    menuItemName: { type: 'string' },
    size: { type: 'string' },
    ingredients: {
      type: 'array',
      items: {
        oneOf: [
          {
            type: 'object',
            required: ['stockItemId', 'quantity'],
            properties: {
              stockItemId: { type: 'string' },
              quantity: { type: 'number' }
            },
            additionalProperties: false // Re-enabled as we are specific now
          },
          {
            type: 'object',
            required: ['subRecipeId', 'quantity'],
            properties: {
              subRecipeId: { type: 'string' },
              quantity: { type: 'number' }
            },
            additionalProperties: false // Re-enabled
          }
        ]
      }
    },
    costPerUnit: { type: 'number' },
    fixedCost: { type: 'number' },
    isRecipeLocked: { type: 'boolean' },
    costingMethod: { type: 'string', enum: ['recipe', 'fixed'] },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

export interface MenuItemRecipe {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'menu-item-recipe';
  menuItemId: string;
  menuItemName?: string;
  size?: string;
  ingredients: (RecipeIngredient | SubRecipeIngredient)[]; // These types are now simpler
  costPerUnit?: number; // Dynamic cost calculated from ingredients
  fixedCost?: number; // User-defined fixed cost override
  isRecipeLocked?: boolean; // Whether the recipe is locked as definitive
  costingMethod?: 'recipe' | 'fixed'; // Which costing method to use for profit calculations
  createdAt: string;
  updatedAt: string;
}

export const DEFAULT_MENU_ITEM_RECIPE: MenuItemRecipe = {
  _id: '',
  type: 'menu-item-recipe',
  menuItemId: '',
  menuItemName: '',
  size: '',
  ingredients: [],
  costPerUnit: 0,
  fixedCost: 0,
  isRecipeLocked: false,
  costingMethod: 'fixed',
  createdAt: '',
  updatedAt: ''
};
// endknowledge 