"use client";

/**
 * Per-staff Document Schemas for PouchDB
 */

// Staff Document Schema
export const staffDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'id', 'name', 'role', 'status', 'paymentConfig', 'permissions'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^staff:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'staff' },
    id: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    name: { type: 'string', minLength: 1 },
    role: {
      type: 'string',
      enum: ['MANAGER', 'CHEF', 'WAITER', 'BARTENDER', 'HOST', 'KITCHEN_HELPER', 'CASHIER', 'CLEANER', 'DELIVERY', 'OWNER', 'ADMIN', 'STAFF', 'USER']
    },
    contact: { type: 'string' },
    email: { type: 'string' },
    phone: { type: 'string' },
    status: { type: 'string', enum: ['ACTIVE', 'INACTIVE'] },
    paymentConfig: {
      type: 'object',
      properties: {
        type: { type: 'string', enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'PER_SHIFT'] },
        baseSalary: { type: 'number' },
        shiftRate: { type: 'number' },
        shiftRates: { type: 'object' },
        paymentDay: { type: 'number' },
        baseLogic: { type: 'string', enum: ['ADDITIVE', 'MINIMUM', 'NONE'] },
        advanceRepayment: {
          type: 'object',
          properties: {
            percentage: { type: 'number' },
            maxAmount: { type: 'number' }
          }
        },
        nextPaymentDueDate: { type: 'string' }
      },
      required: ['type', 'baseSalary']
    },
    userId: { type: 'string' },
    hasUserAccount: { type: 'boolean' },
    username: { type: 'string' },
    pendingAuth: { type: 'boolean' },
    permissions: {
      type: 'object',
      required: ['pages'],
      properties: {
        pages: {
          type: 'object',
          properties: {
            menu: { type: 'boolean' },
            orders: { type: 'boolean' },
            finance: { type: 'boolean' },
            inventory: { type: 'boolean' },
            staff: { type: 'boolean' },
            settings: { type: 'boolean' },
            suppliers: { type: 'boolean' }
          }
        },
        tabs: {
          type: 'object',
          properties: {
            inventory: {
              type: 'object',
              properties: {
                inventory: { type: 'boolean' },
                subrecipes: { type: 'boolean' },
                counts: { type: 'boolean' },
                waste: { type: 'boolean' }
              }
            },
            staff: {
              type: 'object',
              properties: {
                shifts_schedule: { type: 'boolean' },
                attendance: { type: 'boolean' },
                payments: { type: 'boolean' }
              }
            }
          }
        }
      }
    },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// Schedule Document Schema (per staff)
export const scheduleDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'weeklySchedule', 'effectiveFrom', 'isActive'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^schedule:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'schedule' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    weeklySchedule: {
      type: 'object',
      properties: {
        monday: { type: 'array', items: { type: 'string' } },
        tuesday: { type: 'array', items: { type: 'string' } },
        wednesday: { type: 'array', items: { type: 'string' } },
        thursday: { type: 'array', items: { type: 'string' } },
        friday: { type: 'array', items: { type: 'string' } },
        saturday: { type: 'array', items: { type: 'string' } },
        sunday: { type: 'array', items: { type: 'string' } }
      }
    },
    effectiveFrom: { type: 'string' },
    effectiveTo: { type: 'string' },
    isActive: { type: 'boolean' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// Attendance Document Schema (per staff)
export const attendanceDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'records'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^attendance:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'attendance' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    records: {
      type: 'array',
      items: {
        type: 'object',
        required: ['date', 'status'],
        properties: {
          id: { type: 'string' },
          date: { type: 'string', pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' },
          status: { type: 'string', enum: ['present', 'late', 'absent'] },
          shiftId: { type: 'string' },
          shiftName: { type: 'string' },
          notes: { type: 'string' },
          isPaid: { type: 'boolean' }
        }
      }
    },
    schemaVersion: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// Payment Document Schema (NEW)
export const paymentDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'amount', 'paymentType', 'paymentDate', 'status'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^payment:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'payment' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    amount: { type: 'number' },
    paymentType: {
      type: 'string',
      enum: ['SALARY', 'BONUS', 'ADVANCE', 'DEDUCTION', 'SHIFT_PAYMENT']
    },
    paymentDate: { type: 'string' },
    periodStart: { type: 'string' },
    periodEnd: { type: 'string' },
    status: {
      type: 'string',
      enum: ['PENDING', 'COMPLETED', 'CANCELLED'],
      default: 'COMPLETED'
    },
    notes: { type: 'string' },
    metadata: {
      type: 'object',
      properties: {
        paidAttendanceIds: {
          type: 'array',
          items: { type: 'string' }
        },
        baseLogic: {
          type: 'string',
          enum: ['ADDITIVE', 'MINIMUM', 'NONE']
        },
        baseAmount: { type: 'number' },
        shiftAmount: { type: 'number' },
        totalShifts: { type: 'number' },
        totalDays: { type: 'number' },
        shiftBreakdown: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              shiftId: { type: 'string' },
              shiftName: { type: 'string' },
              count: { type: 'number' },
              rate: { type: 'number' },
              amount: { type: 'number' }
            }
          }
        },
        dateRange: {
          type: 'object',
          properties: {
            startDate: { type: 'string' },
            endDate: { type: 'string' }
          }
        },
        advanceRepayment: {
          type: 'object',
          properties: {
            amount: { type: 'number' },
            percentage: { type: 'number' },
            balanceBefore: { type: 'number' },
            balanceAfter: { type: 'number' }
          }
        },
        calculationBreakdown: {
          type: 'object',
          properties: {
            baseSalary: { type: 'number' },
            shiftEarnings: { type: 'number' },
            bonusAmount: { type: 'number' },
            deductionAmount: { type: 'number' },
            advanceDeduction: { type: 'number' },
            finalAmount: { type: 'number' }
          }
        },
        linkedPayrollId: { type: 'string' },
        completedAt: { type: 'string' },
        // Edit tracking
        edited: { type: 'boolean' },
        editedAt: { type: 'string' },
        originalAmount: { type: 'number' },
        editReason: { type: 'string' },
        // Correction tracking
        isCorrection: { type: 'boolean' },
        originalPaymentId: { type: 'string' },
        correctionReason: { type: 'string' },
        hasCorrectionIds: {
          type: 'array',
          items: { type: 'string' }
        },
        // Void tracking
        voided: { type: 'boolean' },
        voidedAt: { type: 'string' },
        voidReason: { type: 'string' },
        // Replacement tracking
        isReplacement: { type: 'boolean' },
        replacesPaymentId: { type: 'string' },
        replacementPaymentId: { type: 'string' },
        // Reversal tracking
        isReversal: { type: 'boolean' },
        reversalOf: { type: 'string' },
        reversalReason: { type: 'string' },
        mirrorPaymentId: { type: 'string' },
        // Adjustment tracking
        isAdjustment: { type: 'boolean' },
        adjustsPaymentId: { type: 'string' },
        adjustmentReason: { type: 'string' },
        adjustmentAmount: { type: 'number' },
        adjustmentIds: {
          type: 'array',
          items: { type: 'string' }
        }
      }
    },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// Payment Design Document for indexing
export const paymentDesignDoc = {
  _id: '_design/payments',
  views: {
    by_staff: {
      map: `function(doc) {
        if (doc.type === 'payment') {
          emit(doc.staffId, doc);
        }
      }`
    },
    by_date: {
      map: `function(doc) {
        if (doc.type === 'payment') {
          emit(doc.paymentDate, doc);
        }
      }`
    },
    by_staff_and_date: {
      map: `function(doc) {
        if (doc.type === 'payment') {
          emit([doc.staffId, doc.paymentDate], doc);
        }
      }`
    },
    by_period: {
      map: `function(doc) {
        if (doc.type === 'payment' && doc.periodStart && doc.periodEnd) {
          emit([doc.staffId, doc.periodStart, doc.periodEnd], doc);
        }
      }`
    }
  }
};

// TypeScript Interfaces
export interface StaffDocument {
  _id: string;
  _rev?: string;
  type: 'staff';
  id: string;
  name: string;
  role: string;
  contact?: string;
  email?: string;
  phone?: string;
  status: 'ACTIVE' | 'INACTIVE';
  paymentConfig: {
    type: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'PER_SHIFT';
    baseSalary: number;
    shiftRate?: number;
    shiftRates?: Record<string, number>;
    paymentDay?: number;
    baseLogic?: 'ADDITIVE' | 'MINIMUM' | 'NONE';
    advanceRepayment?: {
      percentage: number;
      maxAmount: number;
    };
    nextPaymentDueDate?: string;
  };
  userId?: string;
  hasUserAccount: boolean;
  username?: string;
  pendingAuth?: boolean;
  permissions: {
    pages: {
      menu: boolean;
      orders: boolean;
      finance: boolean;
      inventory: boolean;
      staff: boolean;
      settings: boolean;
      suppliers: boolean;
    };
    tabs?: {
      inventory?: {
        inventory?: boolean;
        subrecipes?: boolean;
        counts?: boolean;
        waste?: boolean;
      };
      staff?: {
        shifts_schedule?: boolean;
        attendance?: boolean;
        payments?: boolean;
      };
    };
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface ScheduleDocument {
  _id: string;
  _rev?: string;
  type: 'schedule';
  staffId: string;
  weeklySchedule: {
    monday: string[];
    tuesday: string[];
    wednesday: string[];
    thursday: string[];
    friday: string[];
    saturday: string[];
    sunday: string[];
  };
  effectiveFrom: string;
  effectiveTo?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface AttendanceRecord {
  id: string;
  date: string;
  status: 'present' | 'late' | 'absent';
  shiftId?: string;
  shiftName?: string;
  notes?: string;
  isPaid?: boolean;
}

export interface AttendanceDocument {
  _id: string;
  _rev?: string;
  type: 'attendance';
  staffId: string;
  records: AttendanceRecord[];
  schemaVersion: string;
  createdAt: string;
  updatedAt: string;
}

// Payment Document Interface
export interface PaymentDocument {
  _id: string;
  _rev?: string;
  type: 'payment';
  staffId: string;
  amount: number;
  paymentType: 'SALARY' | 'BONUS' | 'ADVANCE' | 'DEDUCTION' | 'SHIFT_PAYMENT';
  paymentDate: string;
  periodStart?: string;
  periodEnd?: string;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  metadata?: {
    paidAttendanceIds?: string[];
    baseLogic?: 'ADDITIVE' | 'MINIMUM' | 'NONE';
    baseAmount?: number;
    shiftAmount?: number;
    totalShifts?: number;
    totalDays?: number;
    shiftBreakdown?: Array<{
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
      amount: number;
    }>;
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    advanceRepayment?: {
      amount: number;
      percentage: number;
      balanceBefore: number;
      balanceAfter: number;
    };
    calculationBreakdown?: {
      baseSalary: number;
      shiftEarnings: number;
      bonusAmount: number;
      deductionAmount: number;
      advanceDeduction: number;
      finalAmount: number;
    };
    linkedPayrollId?: string;
    completedAt?: string;
    // Edit tracking
    edited?: boolean;
    editedAt?: string;
    originalAmount?: number;
    editReason?: string;
    // Correction tracking
    isCorrection?: boolean;
    originalPaymentId?: string;
    correctionReason?: string;
    hasCorrectionIds?: string[];
    // Void tracking
    voided?: boolean;
    voidedAt?: string;
    voidReason?: string;
    // Replacement tracking
    isReplacement?: boolean;
    replacesPaymentId?: string;
    replacementPaymentId?: string;
    // Reversal tracking
    isReversal?: boolean;
    reversalOf?: string;
    reversalReason?: string;
    mirrorPaymentId?: string;
    // Adjustment tracking
    isAdjustment?: boolean;
    adjustsPaymentId?: string;
    adjustmentReason?: string;
    adjustmentAmount?: number;
    adjustmentIds?: string[];
    // Consolidated advance repayment tracking
    // ✅ Simplified: Advance repayments are now handled as separate ADVANCE payments
    // Advance repayment flag
    isAdvanceRepayment?: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

// Design-doc indexes for PouchDB
export const staffDesignDoc = {
  _id: '_design/staff',
  indexes: {
    by_userId: { index: { fields: ['userId'] } },
    by_name:   { index: { fields: ['type', 'name'] } }
  }
};

export const scheduleDesignDoc = {
  _id: '_design/schedule',
  indexes: {
    by_staffId: { index: { fields: ['staffId'] } }
  }
};

export const attendanceDesignDoc = {
  _id: '_design/attendance',
  indexes: {
    by_staffId: { index: { fields: ['staffId'] } },
    by_date: { index: { fields: ['records.date'] } }
  }
};