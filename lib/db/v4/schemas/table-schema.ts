"use client";

/**
 * Table Schema for V4 Database
 * Defines the schema and interfaces for table-related documents in the v4 database implementation.
 */

// Interface for packaging consumption items
export interface PackagingItem {
  stockItemId: string;
  quantity: number;
}

export const tableDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'tables'],
  properties: {
    _id: {
      type: 'string',
      enum: ['tables']
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['table_document']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    updatedAt: {
      type: 'string',
      format: 'date-time'
    },
    tables: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'name', 'seats', 'position', 'status'],
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          seats: { type: 'number' },
          position: {
            type: 'object',
            required: ['x', 'y'],
            properties: {
              x: { type: 'number' },
              y: { type: 'number' }
            }
          },
          status: { type: 'string', enum: ['free', 'occupied'] },
          packaging: {
            type: 'array',
            items: {
              type: 'object',
              required: ['stockItemId', 'quantity'],
              properties: {
                stockItemId: { type: 'string' },
                quantity: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }
};

export interface Table {
  id: string;
  name: string;
  seats: number;
  position: { x: number; y: number };
  status: 'free' | 'occupied';
  packaging?: PackagingItem[];
}

export interface TableDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'table_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  tables: Table[];
}

export const DEFAULT_TABLE_DOCUMENT: TableDocument = {
  _id: 'tables',
  type: 'table_document',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  tables: []
};

// Helper function to create default document with fresh timestamps
export function createDefaultTableDocument(): TableDocument {
  const now = new Date().toISOString();
  return {
    _id: 'tables',
    type: 'table_document',
    schemaVersion: 'v4.0',
    createdAt: now,
    updatedAt: now,
    tables: []
  };
} 