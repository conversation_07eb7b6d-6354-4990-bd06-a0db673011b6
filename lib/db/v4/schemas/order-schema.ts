"use client";

/**
 * Order Schema for V4 Database
 * 
 * This file defines the schema and interfaces for order-related documents
 * in the v4 database implementation.
 */

import type { AllOrderTypes } from '@/lib/types/order-types';

// Order Document Schema
export const orderDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'status', 'items', 'total'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^order:[0-9]{8}-[0-9]+$'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['order_document']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    updatedAt: {
      type: 'string',
      format: 'date-time'
    },
    tableId: {
      type: 'string'
    },
    status: {
      type: 'string',
      enum: ['pending', 'preparing', 'served', 'completed', 'cancelled']
    },
    orderType: {
      type: 'string',
      enum: ['dine-in', 'takeaway', 'delivery', 'table', 'takeout']
    },
    items: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'menuItemId', 'name', 'quantity', 'price'],
        properties: {
          id: { type: 'string' },
          menuItemId: { type: 'string' },
          name: { type: 'string' },
          quantity: { type: 'number' },
          price: { type: 'number' },
          size: { type: 'string' },
          notes: { type: 'string' },
          category: { type: 'string' },
          categoryId: { type: 'string' },
          // 🍕 NEW: Custom Pizza fields
          compositeType: {
            type: 'string',
            enum: ['pizza_quarters']
          },
          quarters: {
            type: 'array',
            items: {
              type: 'object',
              required: ['menuItemId', 'name', 'price'],
              properties: {
                menuItemId: { type: 'string' },
                name: { type: 'string' },
                price: { type: 'number' },
                size: { type: 'string' }
              }
            }
          },
          addons: {
            type: 'array',
            items: {
              type: 'object',
              required: ['id', 'name', 'price'],
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                price: { type: 'number' },
                type: { 
                  type: 'string',
                  enum: ['regular', 'supplement']
                },
                stockConsumption: {
                  type: 'object',
                  properties: {
                    stockItemId: { type: 'string' },
                    quantities: {
                      type: 'object',
                      additionalProperties: { type: 'number' }
                    }
                  }
                },
                // Legacy stock consumption format - Removed
                // stockConsumptions: {
                //   type: 'object',
                //   additionalProperties: {
                //     type: 'array',
                //     items: {
                //       type: 'object',
                //       properties: {
                //         stockItemId: { type: 'string' },
                //         quantity: { type: 'number' }
                //       }
                //     }
                //   }
                // }
              }
            }
          },
          originalQuantity: { type: 'number' },
          voidedQuantity: { type: 'number' },
          isVoided: { type: 'boolean' }
        }
      }
    },
      total: {
    type: 'number'
  },
  // 💸 NEW: Discount fields
  subtotal: {
    type: 'number'
  },
  discountType: {
    type: 'string',
    enum: ['percentage', 'fixed_amount']
  },
  discountValue: {
    type: 'number'
  },
  discountAmount: {
    type: 'number'
  },
  discountReason: {
    type: 'string'
  },
    notes: {
      type: 'string'
    },
    customer: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        phone: { type: 'string' },
        address: { type: 'string' }
      }
    },
    deliveryPerson: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        phone: { type: 'string' },
        type: {
          type: 'string',
          enum: ['staff', 'freelance']
        },
        staffId: {
          type: 'string'
        },
        freelancerId: {
          type: 'string'
        },
        freelancePayment: {
          type: 'number'
        },
        isPaid: {
          type: 'boolean'
        },
        paymentModel: {
          type: 'string',
          enum: ['collection', 'prepaid']
        },
        collectionRate: {
          type: 'number'
        }
      }
    },
    paymentStatus: {
      type: 'string',
      enum: ['unpaid', 'paid', 'partially_paid']
    },
    paymentMethod: {
      type: 'string',
      enum: ['cash', 'card', 'online', 'mixed']
    },
    paymentDetails: {
      type: 'object',
      properties: {
        amountPaid: { type: 'number' },
        amountDue: { type: 'number' },
        receivedAmount: { type: 'number' },
        change: { type: 'number' },
        paidAt: { type: 'string', format: 'date-time' }
      }
    },
    createdBy: {
      type: 'string'
    },
    createdByName: {
      type: 'string'
    },
    hasVoids: {
      type: 'boolean'
    },
    totalVoidedAmount: {
      type: 'number'
    },
    originalTotal: {
      type: 'number'
    },
    voidHistory: {
      type: 'array',
      items: {
        type: 'object',
        required: ['voidedAt', 'reason', 'voidedItems', 'totalVoidedAmount'],
        properties: {
          voidedAt: { type: 'string', format: 'date-time' },
          reason: { type: 'string' },
          voidedBy: { type: 'string' },
          voidedByName: { type: 'string' },
          voidedItems: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                itemIndex: { type: 'number' },
                itemId: { type: 'string' },
                itemName: { type: 'string' },
                quantityVoided: { type: 'number' },
                pricePerUnit: { type: 'number' },
                totalVoidedAmount: { type: 'number' }
              }
            }
          },
          totalVoidedAmount: { type: 'number' }
        }
      }
    },
    // 🚀 NEW: Collection status validation
    collectionStatus: {
      type: 'object',
      required: ['isPending'],
      properties: {
        isPending: { type: 'boolean' },
        expectedAmount: { type: 'number' },
        actualAmount: { type: 'number' },
        collectedAt: { type: 'string', format: 'date-time' },
        collectedBy: { type: 'string' },
        collectedByName: { type: 'string' },
        discrepancy: { type: 'number' },
        discrepancyReason: { type: 'string' },
        manualExpenses: {
          type: 'array',
          items: {
            type: 'object',
            required: ['description', 'amount', 'reason', 'addedAt'],
            properties: {
              description: { type: 'string' },
              amount: { type: 'number' },
              reason: { type: 'string' },
              addedAt: { type: 'string', format: 'date-time' },
              addedBy: { type: 'string' }
            }
          }
        },
        finalAmount: { type: 'number' },
        notes: { type: 'string' }
      }
    },
    // 🚀 NEW: Delivery status validation
    deliveryStatus: {
      type: 'string',
      enum: ['pending', 'out_for_delivery', 'delivered', 'failed', 'partially_delivered']
    },
    // 🚀 NEW: Delivery attempts validation
    deliveryAttempts: {
      type: 'array',
      items: {
        type: 'object',
        required: ['attemptedAt', 'status'],
        properties: {
          attemptedAt: { type: 'string', format: 'date-time' },
          status: {
            type: 'string',
            enum: ['delivered', 'failed', 'partially_delivered']
          },
          failureReason: { type: 'string' },
          failedItems: {
            type: 'array',
            items: {
              type: 'object',
              required: ['itemId', 'itemName', 'quantity', 'reason'],
              properties: {
                itemId: { type: 'string' },
                itemName: { type: 'string' },
                quantity: { type: 'number' },
                reason: { type: 'string' }
              }
            }
          },
          notes: { type: 'string' }
        }
      }
    },
    // Enhanced delivery person validation
    deliveryTariff: { type: 'number' },
    totalCogs: { type: 'number' },
    grossProfit: { type: 'number' },
    profitMargin: { type: 'number' }
  }
};

// Order Item Interface
export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  quantity: number;
  price: number;
  size?: string;
  notes?: string;
  category?: string;
  categoryId?: string;
  addons?: OrderAddon[];
  cogs?: number; // Cost of goods sold for this item
  // Void tracking
  originalQuantity?: number; // Track original quantity before any voids
  voidedQuantity?: number; // How many were voided
  isVoided?: boolean; // Quick flag to check if item has been voided
  // 🍕 NEW: Custom Pizza Quarter Data
  compositeType?: 'pizza_quarters'; // Signal this is a custom pizza
  quarters?: PizzaQuarter[];         // Quarter configurations
}

// 🍕 Pizza Quarter Interface
export interface PizzaQuarter {
  menuItemId: string;  // Reference to the pizza menu item
  name: string;        // Pizza name for display
  price: number;       // Original pizza price
  size?: string;       // Size if applicable
}

// Order Addon Interface
export interface OrderAddon {
  id: string;
  name: string;
  price: number;
  // Optional fields for supplements
  type?: 'regular' | 'supplement';
  // Stock consumption data for supplements
  stockConsumption?: {
    stockItemId: string;
    quantities: { [sizeName: string]: number };
  };
  // Legacy stock consumption format - Removed
  // stockConsumptions?: {
  //   [sizeName: string]: Array<{
  //     stockItemId: string;
  //     quantity: number;
  //   }>;
  // };
}

// Customer Interface
export interface Customer {
  name: string;
  phone: string;
  address?: string;
}

// Delivery Person Interface
export interface DeliveryPerson {
  name: string;
  phone: string;
  type: 'staff' | 'freelance';
  staffId?: string; // If staff driver - links to staff database
  freelancerId?: string; // 🚀 NEW: If freelance driver - links to freelancer database
  freelancePayment?: number; // Payment amount for freelance driver (set per order)
  isPaid?: boolean; // Track if freelance driver has been paid
  paymentModel?: 'collection' | 'prepaid'; // Payment model for freelancers
  collectionRate?: number; // Rate per delivery for collection model
}

// Payment Details Interface
export interface PaymentDetails {
  amountPaid: number;
  amountDue: number;
  receivedAmount?: number;
  change?: number;
  paidAt: string;
}

export interface DeliveryAttempt {
  attemptedAt: string;
  status: 'delivered' | 'failed' | 'partially_delivered';
  failureReason?: string;
  failedItems?: Array<{
    itemId: string;
    itemName: string;
    quantity: number;
    reason: string;
  }>;
  notes?: string;
}

export interface CollectionStatus {
  isPending: boolean;
  expectedAmount?: number; // Amount expected to be collected
  actualAmount?: number; // Amount actually collected
  collectedAt?: string;
  collectedBy?: string;
  collectedByName?: string;
  discrepancy?: number; // Difference between expected and actual
  discrepancyReason?: string; // Reason for discrepancy
  manualExpenses?: Array<{
    description: string; // e.g., "Carburant", "Parking", "Péage"
    amount: number; // Amount spent by driver
    reason: string; // Additional details about the expense
    addedAt: string; // When the expense was recorded
    addedBy?: string; // Who recorded the expense
  }>; // Session-based driver expenses (fuel, parking, etc.) deducted from collection
  finalAmount?: number;
  notes?: string;
}

// Order Document Interface
export interface OrderDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'order_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  tableId: string;
  status: 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';
  orderType: AllOrderTypes; // 🎯 UPDATED: Use AllOrderTypes for backward compatibility
  items: OrderItem[];
  total: number;
  // 💸 NEW: Discount fields
  subtotal?: number;
  discountType?: 'percentage' | 'fixed_amount';
  discountValue?: number;
  discountAmount?: number;
  discountReason?: string;
  notes?: string;
  customer?: Customer;
  deliveryPerson?: DeliveryPerson;
  deliveryTariff?: number; // Delivery tariff amount for this specific order
  paymentStatus?: 'unpaid' | 'paid' | 'partially_paid';
  paymentMethod?: 'cash' | 'card' | 'online' | 'mixed';
  paymentDetails?: PaymentDetails;
  totalCogs?: number; // Total cost of goods sold for this order
  grossProfit?: number; // total - totalCogs
  profitMargin?: number; // (grossProfit / total) * 100
  createdBy?: string; // User ID of staff who created this order
  createdByName?: string; // Name of staff who created this order
  // Void tracking
  hasVoids?: boolean; // Quick flag to check if order has any voids
  totalVoidedAmount?: number; // Total amount voided from this order
  originalTotal?: number; // Original total before any voids
  voidHistory?: VoidEntry[]; // Complete history of all void operations
  // Enhanced delivery tracking
  deliveryStatus?: 'pending' | 'out_for_delivery' | 'delivered' | 'failed' | 'partially_delivered';
  deliveryAttempts?: DeliveryAttempt[];
  collectionStatus?: CollectionStatus;
}

// Order Interface (for compatibility with existing code)
export interface Order extends OrderDocument {
  id: string; // Alias for _id for compatibility
}

// Default empty order document
export const DEFAULT_ORDER_DOCUMENT: Omit<OrderDocument, '_id'> = {
  type: 'order_document',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  tableId: '',
  status: 'pending',
  orderType: 'dine-in',
  items: [],
  total: 0
};

// Void Entry Interface for tracking individual void operations
export interface VoidEntry {
  voidedAt: string; // ISO timestamp
  reason: string; // Reason for voiding
  voidedBy?: string; // Staff member who performed the void
  voidedByName?: string; // Name of staff member
  voidedItems: Array<{
    itemIndex: number; // Index in the order.items array
    itemId: string; // Item ID
    itemName: string; // Item name for reference
    quantityVoided: number; // How many of this item were voided
    pricePerUnit: number; // Price per unit at time of void
    totalVoidedAmount: number; // Total amount voided for this item
  }>;
  totalVoidedAmount: number; // Total amount voided in this operation
}
