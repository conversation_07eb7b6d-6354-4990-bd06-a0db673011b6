// knowledge: v4 sub-recipe schema (match v3)
import { RecipeIngredient } from '../../../../types/cogs'; // Import enhanced RecipeIngredient type

export const subRecipeSchema = {
  type: 'object',
  required: ['_id', 'type', 'name', 'ingredients', 'yield', 'createdAt', 'updatedAt'],
  properties: {
    _id: { type: 'string' },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: { type: 'string', enum: ['sub-recipe'] },
    name: { type: 'string' },
    ingredients: {
      type: 'array',
      items: {
        type: 'object',
        required: ['stockItemId', 'quantity'],
        properties: {
          stockItemId: { type: 'string' },
          quantity: { type: 'number' }
        },
        additionalProperties: false // Re-enabled
      }
    },
    yield: {
      type: 'object',
      required: ['quantity', 'unit'],
      properties: {
        quantity: { type: 'number' },
        unit: { type: 'string', enum: ['kg', 'L', 'pcs', 'g', 'ml'] }
      },
      additionalProperties: false
    },
    costPerUnit: { type: 'number' },
    lastProduced: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' },
    currentStock: { type: 'number' }
  },
  additionalProperties: false
};

export interface SubRecipe {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  id?: string; // Optional explicit ID for seeding purposes
  type: 'sub-recipe';
  name: string;
  ingredients: RecipeIngredient[]; // Use imported enhanced RecipeIngredient type
  yield: {
    quantity: number;
    unit: 'kg' | 'L' | 'pcs' | 'g' | 'ml';
  };
  costPerUnit?: number;
  lastProduced?: string;
  createdAt: string;
  updatedAt: string;
  currentStock?: number;
}

export const DEFAULT_SUB_RECIPE: SubRecipe = {
  _id: '',
  type: 'sub-recipe',
  name: '',
  ingredients: [],
  yield: { quantity: 1, unit: 'pcs' },
  costPerUnit: 0,
  lastProduced: '',
  createdAt: '',
  updatedAt: '',
  currentStock: 0
};
// endknowledge 