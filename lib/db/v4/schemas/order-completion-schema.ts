// Order completion tracking schema for barcode scanning system

export interface OrderCompletionStatus {
  _id: string; // orderId
  _rev?: string;
  type: 'order-completion';
  schemaVersion: 'v1.0';
  orderId: string;
  stationItems: {
    [stationId: string]: {
      [itemIndex: string]: {
        completed: boolean;
        completedAt?: string;
        scannedBy?: string;
        barcode: string;
      }
    }
  };
  allItemsComplete: boolean;
  expoNotified: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

export interface StationItemStatus {
  completed: boolean;
  completedAt?: string;
  scannedBy?: string;
  barcode: string;
}

export interface BarcodeData {
  orderId: string;
  stationId: string;
  itemIndex: string;
  timestamp: string;
}

// Default completion status for new orders
export const createDefaultCompletionStatus = (orderId: string): Omit<OrderCompletionStatus, '_id' | '_rev'> => {
  const now = new Date().toISOString();
  
  return {
    type: 'order-completion',
    schemaVersion: 'v1.0',
    orderId,
    stationItems: {},
    allItemsComplete: false,
    expoNotified: false,
    createdAt: now,
    updatedAt: now
  };
};

// Type for order completion updates
export type OrderCompletionUpdate = Partial<Omit<OrderCompletionStatus, '_id' | 'type' | 'schemaVersion' | 'orderId'>>;