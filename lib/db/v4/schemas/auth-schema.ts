"use client";

/**
 * V4 Auth Schema
 *
 * Defines the schema for authentication-related documents.
 */

// Auth User Schema
export const authUserSchema = {
  type: 'object',
  required: ['_id', 'type', 'name', 'password', 'role', 'restaurantId'],
  properties: {
    _id: {
      type: 'string'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['user']
    },
    name: {
      type: 'string',
      minLength: 1
    },
    email: {
      type: 'string'
    },
    username: {
      type: 'string',
      minLength: 3
    },
    password: {
      type: 'string',
      minLength: 6
    },
    role: {
      type: 'string',
      enum: ['owner', 'admin', 'manager', 'staff', 'user']
    },
    restaurantId: {
      type: 'string'
    },
    restricted: {
      type: 'boolean'
    },
    permissions: {
      type: 'object',
      properties: {
        pages: {
          type: 'array',
          items: {
            type: 'object',
            required: ['page', 'access'],
            properties: {
              page: { type: 'string' },
              access: { type: 'boolean' }
            }
          }
        },
        tabs: {
          type: 'object',
          properties: {
            inventory: {
              type: 'object',
              properties: {
                inventory: { type: 'boolean' },
                counts: { type: 'boolean' },
                waste: { type: 'boolean' },
                production: { type: 'boolean' },
                recettes: { type: 'boolean' }
              }
            },
            staff: {
              type: 'object',
              properties: {
                staff: { type: 'boolean' },
                shifts_schedule: { type: 'boolean' },
                attendance: { type: 'boolean' },
                payments: { type: 'boolean' }
              }
            }
          }
        },
        components: {
          type: 'array',
          items: {
            type: 'object',
            required: ['page', 'component', 'access'],
            properties: {
              page: { type: 'string' },
              component: { type: 'string' },
              access: { type: 'boolean' }
            }
          }
        }
      }
    },
    metadata: {
      type: 'object',
      properties: {
        position: { type: 'string' },
        status: { type: 'string' },
        staffId: { type: 'string' }
      }
    },
    schemaVersion: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// Restaurant Schema
export const restaurantSchema = {
  type: 'object',
  required: ['_id', 'type', 'phoneNumber', 'password'],
  properties: {
    _id: {
      type: 'string'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['restaurant']
    },
    phoneNumber: {
      type: 'string',
      minLength: 1
    },
    email: {
      type: 'string'
    },
    password: {
      type: 'string',
      minLength: 6
    },
    ownerEmail: {
      type: 'string'
    },
    restricted: {
      type: 'boolean'
    },
    users: {
      type: 'array',
      items: {
        type: 'string'
      }
    },
    schemaVersion: { type: 'string' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// TypeScript interfaces for auth documents
export interface AuthUser {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'user';
  name: string;
  email?: string;
  username?: string;
  password: string;
  role: 'owner' | 'admin' | 'manager' | 'staff' | 'user';
  restaurantId: string;
  restricted?: boolean;
  permissions?: {
    pages?: Array<{
      page: string;
      access: boolean;
    }>;
    tabs?: {
      inventory?: {
        inventory?: boolean;
        counts?: boolean;
        waste?: boolean;
        production?: boolean;
        recettes?: boolean;
      };
      staff?: {
        staff?: boolean;
        shifts_schedule?: boolean;
        attendance?: boolean;
        payments?: boolean;
      };
    };
    components?: Array<{
      page: string;
      component: string;
      access: boolean;
    }>;
  };
  metadata?: {
    position?: string;
    status?: string;
    staffId?: string;
    [key: string]: any;
  };
  schemaVersion: string;
  createdAt: string;
  updatedAt: string;
}

export interface Restaurant {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'restaurant';
  phoneNumber: string;
  email?: string;
  password: string;
  ownerEmail?: string;
  restricted?: boolean;
  users: string[];
  schemaVersion: string;
  createdAt: string;
  updatedAt: string;
}

// Default permissions templates
export const DEFAULT_ADMIN_PERMISSIONS = {
  pages: [
    { page: 'menu', access: true },
    { page: 'orders', access: true },
    { page: 'finance', access: true },
    { page: 'inventory', access: true },
    { page: 'staff', access: true },
    { page: 'settings', access: true },
    { page: 'suppliers', access: true }
  ],
  tabs: {
    inventory: {
      inventory: true,
      counts: true,
      waste: true,
      production: true,
      recettes: true
    },
    staff: {
      staff: true,
      shifts_schedule: true,
      attendance: true,
      payments: true
    }
  },
  components: [
    // Menu components
    { page: 'menu', component: 'add-category', access: true },
    { page: 'menu', component: 'edit-category', access: true },
    { page: 'menu', component: 'delete-category', access: true },
    { page: 'menu', component: 'add-item', access: true },
    { page: 'menu', component: 'edit-item', access: true },
    { page: 'menu', component: 'delete-item', access: true },
    { page: 'menu', component: 'configure-prices', access: true },

    // Order components
    { page: 'orders', component: 'create-order', access: true },
    { page: 'orders', component: 'edit-order', access: true },
    { page: 'orders', component: 'cancel-order', access: true },
    { page: 'orders', component: 'process-payment', access: true },

    // Finance components
    { page: 'finance', component: 'view-reports', access: true },
    { page: 'finance', component: 'add-expense', access: true },
    { page: 'finance', component: 'edit-expense', access: true },
    { page: 'finance', component: 'delete-expense', access: true },

    // Inventory components
    { page: 'inventory', component: 'add-item', access: true },
    { page: 'inventory', component: 'edit-item', access: true },
    { page: 'inventory', component: 'delete-item', access: true },
    { page: 'inventory', component: 'adjust-stock', access: true },

    // Staff components
    { page: 'staff', component: 'add-staff', access: true },
    { page: 'staff', component: 'edit-staff', access: true },
    { page: 'staff', component: 'delete-staff', access: true },
    { page: 'staff', component: 'manage-permissions', access: true },

    // Settings components
    { page: 'settings', component: 'edit-settings', access: true },
    { page: 'settings', component: 'manage-tables', access: true },
    { page: 'settings', component: 'manage-printers', access: true },

    // Suppliers components
    { page: 'suppliers', component: 'add-supplier', access: true },
    { page: 'suppliers', component: 'edit-supplier', access: true },
    { page: 'suppliers', component: 'delete-supplier', access: true }
  ]
};

export const DEFAULT_STAFF_PERMISSIONS = {
  pages: [
    { page: 'menu', access: true },
    { page: 'orders', access: true },
    { page: 'finance', access: false },
    { page: 'inventory', access: false },
    { page: 'staff', access: false },
    { page: 'settings', access: false },
    { page: 'suppliers', access: false }
  ],
  tabs: {
    inventory: {
      inventory: false,
      counts: false,
      waste: false,
      production: false,
      recettes: false
    },
    staff: {
      staff: false,
      shifts_schedule: false,
      attendance: false,
      payments: false
    }
  },
  components: [
    // Menu components - view only
    { page: 'menu', component: 'add-category', access: false },
    { page: 'menu', component: 'edit-category', access: false },
    { page: 'menu', component: 'delete-category', access: false },
    { page: 'menu', component: 'add-item', access: false },
    { page: 'menu', component: 'edit-item', access: false },
    { page: 'menu', component: 'delete-item', access: false },
    { page: 'menu', component: 'configure-prices', access: false },

    // Order components - can create/edit orders
    { page: 'orders', component: 'create-order', access: true },
    { page: 'orders', component: 'edit-order', access: true },
    { page: 'orders', component: 'cancel-order', access: false },
    { page: 'orders', component: 'process-payment', access: true }
  ]
};
