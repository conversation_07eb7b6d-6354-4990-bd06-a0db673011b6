"use client";

/**
 * Caisse Calculation Schema for V4 Database
 * 
 * This file defines the schema and interfaces for caisse calculation documents
 * that track cash register calculations.
 */

// Order Summary for Caisse Calculation
export interface OrderSummary {
  orderId: string;
  amount: number;
  createdAt: string;
  createdBy: string;
  createdByName: string;
  paymentMethod: string;
}

// 🚀 NEW: Delivery Collection Summary for Caisse Calculation
export interface DeliveryCollectionSummary {
  collectionId: string;
  staffId: string;
  staffName: string;
  expectedAmount: number;
  actualAmount: number;
  discrepancy: number;
  orderCount: number;
  orderIds: string[];
  collectedAt: string;
  collectedBy: string;
  discrepancyReason?: string;
}

// Caisse Calculation Document Schema
export const caisseCalculationSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'calculatedAt', 'calculatedBy'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^caisse_calculation:[0-9]+$'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['caisse_calculation']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    updatedAt: {
      type: 'string',
      format: 'date-time'
    },
    calculatedAt: {
      type: 'string',
      format: 'date-time'
    },
    calculatedBy: {
      type: 'string'
    },
    calculatedByName: {
      type: 'string'
    },
    periodStart: {
      type: 'string',
      format: 'date-time'
    },
    periodEnd: {
      type: 'string',
      format: 'date-time'
    },
    expectedAmount: {
      type: 'number'
    },
    countedAmount: {
      type: 'number'
    },
    variance: {
      type: 'number'
    },
    notes: {
      type: 'string'
    },
    orderSummaries: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          orderId: { type: 'string' },
          amount: { type: 'number' },
          createdAt: { type: 'string', format: 'date-time' },
          createdBy: { type: 'string' },
          createdByName: { type: 'string' },
          paymentMethod: { type: 'string' }
        }
      }
    },
    deliveryCollections: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          collectionId: { type: 'string' },
          staffId: { type: 'string' },
          staffName: { type: 'string' },
          expectedAmount: { type: 'number' },
          actualAmount: { type: 'number' },
          discrepancy: { type: 'number' },
          orderCount: { type: 'number' },
          orderIds: { type: 'array', items: { type: 'string' } },
          collectedAt: { type: 'string', format: 'date-time' },
          collectedBy: { type: 'string' },
          discrepancyReason: { type: 'string' }
        }
      }
    }
  }
};

// Caisse Calculation Document Interface
export interface CaisseCalculationDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'caisse_calculation';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  calculatedAt: string;
  calculatedBy: string; // User ID who performed the calculation
  calculatedByName: string; // Name of user who performed the calculation
  periodStart: string; // Start of calculation period (last calculation time)
  periodEnd: string; // End of calculation period (this calculation time)
  expectedAmount: number; // Expected cash amount based on orders
  countedAmount: number; // Actually counted cash amount
  variance: number; // Difference between expected and counted
  notes?: string; // Optional notes about the calculation
  orderSummaries?: OrderSummary[]; // All orders in this period
  // 🚀 NEW: Include delivery collection summaries
  deliveryCollections?: DeliveryCollectionSummary[];
}

// Default empty caisse calculation document
export const DEFAULT_CAISSE_CALCULATION_DOCUMENT: Omit<CaisseCalculationDocument, '_id'> = {
  type: 'caisse_calculation',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  calculatedAt: new Date().toISOString(),
  calculatedBy: '',
  calculatedByName: '',
  periodStart: new Date().toISOString(),
  periodEnd: new Date().toISOString(),
  expectedAmount: 0,
  countedAmount: 0,
  variance: 0,
  orderSummaries: [],
  deliveryCollections: []
}; 