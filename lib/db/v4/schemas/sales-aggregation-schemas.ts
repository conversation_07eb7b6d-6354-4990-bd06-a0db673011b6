/**
 * 🚀 Sales Aggregation Schemas
 * 
 * Pre-computed aggregation documents for lightning-fast sales analytics.
 * These documents are updated incrementally as orders come in, providing
 * instant dashboard performance even with thousands of orders.
 * 
 * Architecture Benefits:
 * - ⚡ Sub-100ms dashboard loading (vs 2-5 seconds)
 * - 📱 Smooth mobile experience 
 * - 🔄 P2P sync friendly (small incremental updates)
 * - 📊 Real-time analytics without heavy computation
 */

import { v4 as uuidv4 } from 'uuid';

// Base aggregation document interface
export interface BaseAggregationDocument {
  _id: string;
  _rev?: string;
  type: string;
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  lastOrderProcessed?: string; // Track which order was last processed
  version: number; // Increment on each update for conflict resolution
}

// Daily Sales Aggregation - Core metrics for each day
export interface DailySalesAggregation extends BaseAggregationDocument {
  type: 'daily_sales_aggregation';
  date: string; // YYYY-MM-DD format
  
  // Core metrics
  totalRevenue: number;
  totalOrders: number;
  totalItems: number;
  averageOrderValue: number;
  
  // Cost & Profit metrics
  totalCogs: number; // Cost of goods sold
  grossProfit: number;
  profitMargin: number; // Percentage
  
  // Order type breakdown
  orderTypeBreakdown: {
    'dine-in': { count: number; revenue: number };
    'takeaway': { count: number; revenue: number };
    'delivery': { count: number; revenue: number };
    'table': { count: number; revenue: number };
    'takeout': { count: number; revenue: number };
  };
  
  // Payment method breakdown
  paymentMethodBreakdown: {
    cash: { count: number; amount: number };
    card: { count: number; amount: number };
    online: { count: number; amount: number };
    mixed: { count: number; amount: number };
  };
  
  // Hourly breakdown for peak time analysis
  hourlyBreakdown: Array<{
    hour: number; // 0-23
    orders: number;
    revenue: number;
    averageOrderValue: number;
  }>;
  
  // Top performing items for the day
  topItems: Array<{
    menuItemId: string;
    name: string;
    category: string;
    quantitySold: number;
    revenue: number;
    profit: number;
  }>;
  
  // Status breakdown
  statusBreakdown: {
    completed: { count: number; revenue: number };
    cancelled: { count: number; lostRevenue: number };
    pending: { count: number; pendingRevenue: number };
  };
}

// Weekly Sales Aggregation - Rolling 7-day metrics
export interface WeeklySalesAggregation extends BaseAggregationDocument {
  type: 'weekly_sales_aggregation';
  weekStartDate: string; // Monday of the week (YYYY-MM-DD)
  weekEndDate: string;   // Sunday of the week (YYYY-MM-DD)
  
  // Core weekly metrics
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  grossProfit: number;
  profitMargin: number;
  
  // Daily breakdown within the week
  dailyBreakdown: Array<{
    date: string;
    dayOfWeek: string; // 'Monday', 'Tuesday', etc.
    orders: number;
    revenue: number;
    profit: number;
  }>;
  
  // Week-over-week comparison
  previousWeekComparison: {
    revenueGrowth: number; // Percentage
    orderGrowth: number;   // Percentage
    profitGrowth: number;  // Percentage
  };
  
  // Best performing day of the week
  bestDay: {
    date: string;
    dayOfWeek: string;
    revenue: number;
    orders: number;
  };
}

// Monthly Sales Aggregation - Monthly overview
export interface MonthlySalesAggregation extends BaseAggregationDocument {
  type: 'monthly_sales_aggregation';
  year: number;
  month: number; // 1-12
  monthName: string; // 'January', 'February', etc.
  
  // Core monthly metrics
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  grossProfit: number;
  profitMargin: number;
  
  // Weekly breakdown within the month
  weeklyBreakdown: Array<{
    weekNumber: number;
    weekStartDate: string;
    orders: number;
    revenue: number;
    profit: number;
  }>;
  
  // Month-over-month comparison
  previousMonthComparison: {
    revenueGrowth: number;
    orderGrowth: number;
    profitGrowth: number;
  };
  
  // Top categories for the month
  topCategories: Array<{
    category: string;
    revenue: number;
    itemsSold: number;
    profit: number;
  }>;
}

// Item Performance Aggregation - Track individual menu item performance
export interface ItemPerformanceAggregation extends BaseAggregationDocument {
  type: 'item_performance_aggregation';
  menuItemId: string;
  itemName: string;
  category: string;
  
  // Performance metrics
  totalQuantitySold: number;
  totalRevenue: number;
  totalProfit: number;
  averagePrice: number;
  profitMargin: number;
  
  // Time-based performance
  last7Days: {
    quantitySold: number;
    revenue: number;
    profit: number;
  };
  
  last30Days: {
    quantitySold: number;
    revenue: number;
    profit: number;
  };
  
  // Trend analysis
  trend: 'increasing' | 'decreasing' | 'stable';
  trendPercentage: number;
  
  // Peak performance times
  peakHours: Array<{
    hour: number;
    averageQuantity: number;
  }>;
  
  // Last sold information
  lastSoldAt: string;
  daysSinceLastSold: number;
}

// Real-time Dashboard Aggregation - Live metrics for dashboard
export interface RealTimeDashboardAggregation extends BaseAggregationDocument {
  type: 'realtime_dashboard_aggregation';
  
  // Today's metrics (updated in real-time)
  today: {
    revenue: number;
    orders: number;
    averageOrderValue: number;
    profit: number;
    profitMargin: number;
    lastUpdated: string;
  };
  
  // This week's metrics
  thisWeek: {
    revenue: number;
    orders: number;
    averageOrderValue: number;
    profit: number;
    profitMargin: number;
  };
  
  // This month's metrics
  thisMonth: {
    revenue: number;
    orders: number;
    averageOrderValue: number;
    profit: number;
    profitMargin: number;
  };
  
  // Quick comparisons
  comparisons: {
    todayVsYesterday: {
      revenueGrowth: number;
      orderGrowth: number;
    };
    thisWeekVsLastWeek: {
      revenueGrowth: number;
      orderGrowth: number;
    };
    thisMonthVsLastMonth: {
      revenueGrowth: number;
      orderGrowth: number;
    };
  };
  
  // Live trending items (updated frequently)
  trendingItems: Array<{
    menuItemId: string;
    name: string;
    quantitySoldToday: number;
    revenueToday: number;
    trend: 'hot' | 'rising' | 'steady';
  }>;
  
  // Current status overview
  currentStatus: {
    pendingOrders: number;
    preparingOrders: number;
    completedOrdersToday: number;
    cancelledOrdersToday: number;
  };
}

// Helper functions for generating aggregation document IDs
export function generateDailyAggregationId(date: string): string {
  return `daily_sales:${date}`; // e.g., "daily_sales:2024-01-15"
}

export function generateWeeklyAggregationId(weekStartDate: string): string {
  return `weekly_sales:${weekStartDate}`; // e.g., "weekly_sales:2024-01-15"
}

export function generateMonthlyAggregationId(year: number, month: number): string {
  const monthStr = month.toString().padStart(2, '0');
  return `monthly_sales:${year}-${monthStr}`; // e.g., "monthly_sales:2024-01"
}

export function generateItemPerformanceId(menuItemId: string): string {
  return `item_performance:${menuItemId}`;
}

export function generateRealTimeDashboardId(): string {
  return 'realtime_dashboard:main';
}

// Default templates for creating new aggregation documents
export const DEFAULT_DAILY_AGGREGATION: Omit<DailySalesAggregation, '_id' | 'date'> = {
  type: 'daily_sales_aggregation',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  version: 1,
  totalRevenue: 0,
  totalOrders: 0,
  totalItems: 0,
  averageOrderValue: 0,
  totalCogs: 0,
  grossProfit: 0,
  profitMargin: 0,
  orderTypeBreakdown: {
    'dine-in': { count: 0, revenue: 0 },
    'takeaway': { count: 0, revenue: 0 },
    'delivery': { count: 0, revenue: 0 },
    'table': { count: 0, revenue: 0 },
    'takeout': { count: 0, revenue: 0 }
  },
  paymentMethodBreakdown: {
    cash: { count: 0, amount: 0 },
    card: { count: 0, amount: 0 },
    online: { count: 0, amount: 0 },
    mixed: { count: 0, amount: 0 }
  },
  hourlyBreakdown: Array.from({ length: 24 }, (_, hour) => ({
    hour,
    orders: 0,
    revenue: 0,
    averageOrderValue: 0
  })),
  topItems: [],
  statusBreakdown: {
    completed: { count: 0, revenue: 0 },
    cancelled: { count: 0, lostRevenue: 0 },
    pending: { count: 0, pendingRevenue: 0 }
  }
};

export const DEFAULT_REALTIME_DASHBOARD: Omit<RealTimeDashboardAggregation, '_id'> = {
  type: 'realtime_dashboard_aggregation',
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  version: 1,
  today: {
    revenue: 0,
    orders: 0,
    averageOrderValue: 0,
    profit: 0,
    profitMargin: 0,
    lastUpdated: new Date().toISOString()
  },
  thisWeek: {
    revenue: 0,
    orders: 0,
    averageOrderValue: 0,
    profit: 0,
    profitMargin: 0
  },
  thisMonth: {
    revenue: 0,
    orders: 0,
    averageOrderValue: 0,
    profit: 0,
    profitMargin: 0
  },
  comparisons: {
    todayVsYesterday: {
      revenueGrowth: 0,
      orderGrowth: 0
    },
    thisWeekVsLastWeek: {
      revenueGrowth: 0,
      orderGrowth: 0
    },
    thisMonthVsLastMonth: {
      revenueGrowth: 0,
      orderGrowth: 0
    }
  },
  trendingItems: [],
  currentStatus: {
    pendingOrders: 0,
    preparingOrders: 0,
    completedOrdersToday: 0,
    cancelledOrdersToday: 0
  }
};

// Union type for all aggregation documents
export type SalesAggregationDocument = 
  | DailySalesAggregation 
  | WeeklySalesAggregation 
  | MonthlySalesAggregation 
  | ItemPerformanceAggregation 
  | RealTimeDashboardAggregation; 