/**
 * Freelancer Schema for PouchDB
 * Simple freelancer tracking with phone-based ID and unique names
 */

// Freelancer Document Schema
export const freelancerDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'totalOrders', 'totalEarnings', 'isActive', 'createdAt', 'updatedAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^freelancer:(.+)$' // freelancer:phone-number or freelancer:name-timestamp-random
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'freelancer' },
    name: { 
      type: 'string', 
      minLength: 1,
      maxLength: 100
    },
    phone: { 
      type: 'string', 
      minLength: 1,
      maxLength: 20
    },
    totalOrders: {
      type: 'number',
      minimum: 0,
      default: 0
    },
    totalEarnings: {
      type: 'number',
      minimum: 0,
      default: 0
    },
    isActive: {
      type: 'boolean',
      default: true
    },
    rating: {
      type: 'number',
      minimum: 0,
      maximum: 5
    },
    notes: {
      type: 'string',
      maxLength: 500
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    updatedAt: {
      type: 'string',
      format: 'date-time'
    },
    lastActiveAt: {
      type: 'string',
      format: 'date-time'
    }
  }
};

// TypeScript Interface
export interface FreelancerDocument {
  _id: string; // "freelancer:phone-number"
  _rev?: string;
  type: 'freelancer';
  name?: string;
  phone?: string;
  totalOrders: number;
  totalEarnings: number;
  isActive: boolean;
  rating?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  lastActiveAt?: string;
}

// Helper function to normalize phone number for ID
export function normalizePhoneForId(phone: string): string {
  // Remove all non-digit characters except + at the beginning
  const cleaned = phone.replace(/[^\d+]/g, '');
  // Ensure + is only at the beginning
  const normalized = cleaned.startsWith('+') ? '+' + cleaned.slice(1).replace(/\+/g, '') : cleaned;
  return normalized || phone; // Fallback to original if cleaning results in empty string
}

// Helper function to generate freelancer document ID
export function generateFreelancerId(phone: string): string {
  const normalizedPhone = normalizePhoneForId(phone);
  return `freelancer:${normalizedPhone}`;
}

// Helper function to create default freelancer document
export function createDefaultFreelancer(name?: string, phone?: string): Omit<FreelancerDocument, '_id' | '_rev'> {
  const now = new Date().toISOString();
  
  const freelancer: Omit<FreelancerDocument, '_id' | '_rev'> = {
    type: 'freelancer',
    totalOrders: 0,
    totalEarnings: 0,
    isActive: true,
    createdAt: now,
    updatedAt: now
  };

  if (name) {
    freelancer.name = name.trim();
  }

  if (phone) {
    freelancer.phone = phone.trim();
  }

  return freelancer;
}