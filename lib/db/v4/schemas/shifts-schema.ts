"use client";

/**
 * Shifts Schema for V4 Database
 */

// Shifts Document Schema
export const shiftsDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'shifts'],
  properties: {
    _id: { type: 'string', enum: ['shifts'] },
    _rev: { 
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: { type: 'string', enum: ['shifts_document'] },
    schemaVersion: { type: 'string', enum: ['v4.0'] },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    shifts: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'name', 'startTime', 'endTime'],
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          startTime: { type: 'string' },
          endTime: { type: 'string' },
          color: { type: 'string' }
        }
      }
    }
  }
};

export interface Shift {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  color?: string;
}

export interface ShiftsDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'shifts_document';
  schemaVersion: 'v4.0';
  shifts: Shift[];
  createdAt: string;
  updatedAt: string;
}

export const DEFAULT_SHIFTS_DOCUMENT: ShiftsDocument = {
  _id: 'shifts',
  type: 'shifts_document',
  shifts: [],
  schemaVersion: 'v4.0',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}; 