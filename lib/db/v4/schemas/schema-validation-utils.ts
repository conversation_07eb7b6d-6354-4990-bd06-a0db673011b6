"use client";

/**
 * Schema Validation Utilities for V4 Database
 * 
 * This utility ensures all schemas have proper PouchDB fields and consistent structure
 * to prevent the document conflicts and "Cannot read properties of undefined (reading 'revs')" errors.
 */

// Import all schemas to validate them
import { menuDocumentSchema } from './menu-schema';
import { tableDocumentSchema } from './table-schema';
import { inventoryDocumentSchema } from './inventory-schema';
import { orderDocumentSchema } from './order-schema';
import { supplierDocumentSchema } from './supplier-schema';
import { shiftsDocumentSchema } from './shifts-schema';

import { restaurantSettingsSchema } from './restaurant-settings-schema';
import { staffDocumentSchema, scheduleDocumentSchema, attendanceDocumentSchema } from './per-staff-schemas';
import { freelancerDocumentSchema } from './freelancer-schema';

/**
 * Validates that a schema has proper PouchDB fields
 */
export function validateSchemaStructure(schema: any, schemaName: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if schema has properties
  if (!schema.properties) {
    errors.push(`${schemaName}: Missing 'properties' object`);
    return { isValid: false, errors, warnings };
  }

  // Check for required PouchDB fields
  const requiredPouchDBFields = ['_id', '_rev'];
  const missingFields: string[] = [];

  requiredPouchDBFields.forEach(field => {
    if (!schema.properties[field]) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    errors.push(`${schemaName}: Missing PouchDB fields in properties: ${missingFields.join(', ')}`);
  }

  // Check _rev field structure
  if (schema.properties._rev) {
    const revField = schema.properties._rev;
    if (revField.type !== 'string') {
      errors.push(`${schemaName}: _rev field must be of type 'string'`);
    }
    if (!revField.description || !revField.description.includes('PouchDB')) {
      warnings.push(`${schemaName}: _rev field should have PouchDB description`);
    }
  }

  // Check if _rev is in required fields (it shouldn't be for new documents)
  if (schema.required && schema.required.includes('_rev')) {
    warnings.push(`${schemaName}: _rev should not be in required fields (optional for new docs)`);
  }

  // Check for standard document fields
  const standardFields = ['type', 'createdAt', 'updatedAt'];
  standardFields.forEach(field => {
    if (!schema.properties[field]) {
      warnings.push(`${schemaName}: Missing standard field '${field}'`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates all schemas in the system
 */
export function validateAllSchemas(): {
  isValid: boolean;
  results: Array<{
    schemaName: string;
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>;
} {
  const schemas = [
    { name: 'menuDocumentSchema', schema: menuDocumentSchema },
    { name: 'tableDocumentSchema', schema: tableDocumentSchema },
    { name: 'inventoryDocumentSchema', schema: inventoryDocumentSchema },
    { name: 'orderDocumentSchema', schema: orderDocumentSchema },
    { name: 'supplierDocumentSchema', schema: supplierDocumentSchema },
    { name: 'shiftsDocumentSchema', schema: shiftsDocumentSchema },
    { name: 'restaurantSettingsSchema', schema: restaurantSettingsSchema },
    { name: 'staffDocumentSchema', schema: staffDocumentSchema },
    { name: 'scheduleDocumentSchema', schema: scheduleDocumentSchema },
    { name: 'attendanceDocumentSchema', schema: attendanceDocumentSchema },
    { name: 'freelancerDocumentSchema', schema: freelancerDocumentSchema }
  ];

  const results = schemas.map(({ name, schema }) => ({
    schemaName: name,
    ...validateSchemaStructure(schema, name)
  }));

  const isValid = results.every(result => result.isValid);

  return { isValid, results };
}

/**
 * Utility to ensure a document has proper PouchDB structure before saving
 */
export function ensurePouchDBStructure<T extends { _id: string; _rev?: string }>(
  doc: T,
  docType: string
): T {
  // Ensure the document has the required structure
  if (!doc._id) {
    throw new Error(`Document of type '${docType}' must have an _id field`);
  }

  // _rev is optional for new documents but required for updates
  // PouchDB will handle this automatically, but we ensure the field exists in the interface

  return doc;
}

/**
 * Logs schema validation results to console
 */
export function logSchemaValidationResults(): void {
  const { isValid, results } = validateAllSchemas();
  
  console.log('🔍 [Schema Validation] Running comprehensive schema validation...');
  
  if (isValid) {
    console.log('✅ [Schema Validation] All schemas are valid!');
  } else {
    console.error('❌ [Schema Validation] Schema validation failed!');
  }

  results.forEach(result => {
    if (result.errors.length > 0) {
      console.error(`❌ [${result.schemaName}] Errors:`, result.errors);
    }
    if (result.warnings.length > 0) {
      console.warn(`⚠️ [${result.schemaName}] Warnings:`, result.warnings);
    }
    if (result.errors.length === 0 && result.warnings.length === 0) {
      console.log(`✅ [${result.schemaName}] Valid`);
    }
  });
}

// Export validation results for runtime checking
export const SCHEMA_VALIDATION_RESULTS = validateAllSchemas();