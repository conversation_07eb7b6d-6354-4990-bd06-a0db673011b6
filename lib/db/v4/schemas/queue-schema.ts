"use client";

/**
 * Queue Schema for PouchDB V4
 * 
 * Defines the schema for persistent kitchen queue management.
 * This replaces the in-memory queue system with PouchDB persistence.
 */

import { OrderItem } from './order-schema';

// Queue Item Schema - Individual queue entries per station
export const queueItemSchema = {
  type: 'object',
  required: ['_id', 'type', 'orderId', 'orderNumber', 'stationId', 'items', 'status', 'createdAt', 'updatedAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^queue-item:[^:]+:[^:]+$' // queue-item:stationId:orderId
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['queue_item']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    orderId: { type: 'string' },
    orderNumber: { type: 'string' },
    stationId: { type: 'string' },
    items: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'menuItemId', 'name', 'quantity', 'price'],
        properties: {
          id: { type: 'string' },
          menuItemId: { type: 'string' },
          name: { type: 'string' },
          quantity: { type: 'number' },
          price: { type: 'number' },
          category: { type: 'string' },
          size: { type: 'string' },
          notes: { type: 'string' },
          cogs: { type: 'number' }
        }
      }
    },
    completedItemIds: {
      type: 'array',
      items: { type: 'string' }
    },
    status: {
      type: 'string',
      enum: ['pending', 'in-progress', 'completed']
    },
    estimatedTime: { type: 'number' }, // minutes
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    completedAt: { type: 'string', format: 'date-time' }
  }
};

// Item Completion Schema - Individual item completion tracking
export const itemCompletionSchema = {
  type: 'object',
  required: ['_id', 'type', 'barcode', 'orderId', 'stationId', 'itemName', 'status', 'createdAt', 'updatedAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^item-completion:[^:]+$' // item-completion:barcode
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['item_completion']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    barcode: { type: 'string' },
    orderId: { type: 'string' },
    stationId: { type: 'string' },
    itemName: { type: 'string' },
    status: {
      type: 'string',
      enum: ['pending', 'completed']
    },
    scannedBy: { type: 'string' },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    completedAt: { type: 'string', format: 'date-time' }
  }
};

// Station Queue Summary Schema - Aggregated queue data per station
export const stationQueueSummarySchema = {
  type: 'object',
  required: ['_id', 'type', 'stationId', 'stationName', 'totalOrders', 'pendingOrders', 'inProgressOrders', 'completedOrders', 'averageTime', 'updatedAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^station-queue:[^:]+$' // station-queue:stationId
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['station_queue_summary']
    },
    schemaVersion: {
      type: 'string',
      enum: ['v4.0']
    },
    stationId: { type: 'string' },
    stationName: { type: 'string' },
    totalOrders: { type: 'number' },
    pendingOrders: { type: 'number' },
    inProgressOrders: { type: 'number' },
    completedOrders: { type: 'number' },
    averageTime: { type: 'number' }, // minutes
    lastOrderProcessed: { type: 'string' },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' }
  }
};

// TypeScript Interfaces
export interface QueueItem {
  _id: string;
  _rev?: string;
  type: 'queue_item';
  schemaVersion: 'v4.0';
  orderId: string;
  orderNumber: string;
  stationId: string;
  items: OrderItem[];
  completedItemIds: string[];
  status: 'pending' | 'in-progress' | 'completed';
  estimatedTime?: number; // minutes
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface ItemCompletion {
  _id: string;
  _rev?: string;
  type: 'item_completion';
  schemaVersion: 'v4.0';
  barcode: string;
  orderId: string;
  stationId: string;
  itemName: string;
  status: 'pending' | 'completed';
  scannedBy?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface StationQueueSummary {
  _id: string;
  _rev?: string;
  type: 'station_queue_summary';
  schemaVersion: 'v4.0';
  stationId: string;
  stationName: string;
  totalOrders: number;
  pendingOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  averageTime: number; // minutes
  lastOrderProcessed?: string;
  createdAt: string;
  updatedAt: string;
}

// Legacy interface for backward compatibility
export interface StationQueue {
  stationId: string;
  stationName: string;
  totalOrders: number;
  pendingOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  averageTime: number;
  items: QueueItem[];
}

export interface ItemCompletionStatus {
  barcode: string;
  orderId: string;
  stationId: string;
  itemName: string;
  status: 'pending' | 'completed';
  completedAt?: Date;
  scannedBy?: string;
}

// Default documents
export const DEFAULT_QUEUE_ITEM: Omit<QueueItem, '_id' | 'orderId' | 'orderNumber' | 'stationId' | 'items' | 'createdAt' | 'updatedAt'> = {
  type: 'queue_item',
  schemaVersion: 'v4.0',
  completedItemIds: [],
  status: 'pending'
};

export const DEFAULT_ITEM_COMPLETION: Omit<ItemCompletion, '_id' | 'barcode' | 'orderId' | 'stationId' | 'itemName' | 'createdAt' | 'updatedAt'> = {
  type: 'item_completion',
  schemaVersion: 'v4.0',
  status: 'pending'
};

export const DEFAULT_STATION_QUEUE_SUMMARY: Omit<StationQueueSummary, '_id' | 'stationId' | 'stationName' | 'createdAt' | 'updatedAt'> = {
  type: 'station_queue_summary',
  schemaVersion: 'v4.0',
  totalOrders: 0,
  pendingOrders: 0,
  inProgressOrders: 0,
  completedOrders: 0,
  averageTime: 0
};

// Helper functions for ID generation
export function generateQueueItemId(stationId: string, orderId: string): string {
  return `queue-item:${stationId}:${orderId}`;
}

export function generateItemCompletionId(barcode: string): string {
  return `item-completion:${barcode}`;
}

export function generateStationQueueSummaryId(stationId: string): string {
  return `station-queue:${stationId}`;
} 