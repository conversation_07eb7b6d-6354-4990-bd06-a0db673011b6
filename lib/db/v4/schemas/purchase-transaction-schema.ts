/**
 * 🚀 Unified Purchase Transaction Schema
 * 
 * Clean, consistent purchase transaction model that replaces all previous
 * purchase implementations. Each transaction represents one purchase event
 * with multiple items and a single receipt.
 */

export interface PurchaseItem {
  stockItemId: string;
  quantity: number;
  unit: string; // The unit used for purchase (e.g., "box", "kg", "L")
  purchaseUnitId?: string; // Reference to purchase unit definition
  baseQuantity: number; // Quantity converted to stock item's base unit
  costPerUnit: number; // Cost per purchase unit
  costPerBaseUnit: number; // Cost per base unit (calculated)
  totalCost: number; // Total cost for this item
}

export interface PurchaseTransaction {
  _id: string;
  _rev?: string;
  type: 'purchase_transaction';
  schemaVersion: 'v1.0';
  
  // Transaction metadata
  date: string; // ISO date when purchase was made
  transactionNumber?: string; // Optional reference number
  
  // Items in this transaction
  items: PurchaseItem[];
  
  // Financial information
  subtotalCost: number; // Sum of all item costs
  taxAmount?: number; // Tax amount if applicable
  totalCost: number; // Final total cost
  amountPaid: number; // Amount actually paid
  
  // Supplier information
  supplierId?: string;
  supplierName?: string; // Cached for performance
  
  // Receipt and notes
  receiptImage?: string; // Attachment filename
  hasReceiptImage: boolean; // Quick flag for UI
  notes?: string;
  
  // Audit trail
  performedBy: string; // User who created the transaction
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseTransactionSummary {
  _id: string;
  date: string;
  itemCount: number;
  totalCost: number;
  supplierId?: string;
  supplierName?: string;
  hasReceiptImage: boolean;
  performedBy: string;
}

// Default template for new transactions
export const DEFAULT_PURCHASE_TRANSACTION: Omit<PurchaseTransaction, '_id' | 'createdAt' | 'updatedAt' | 'performedBy'> = {
  type: 'purchase_transaction',
  schemaVersion: 'v1.0',
  date: '',
  items: [],
  subtotalCost: 0,
  totalCost: 0,
  amountPaid: 0,
  hasReceiptImage: false
};

// Type guards
export function isPurchaseTransaction(doc: any): doc is PurchaseTransaction {
  return doc && doc.type === 'purchase_transaction' && doc.schemaVersion === 'v1.0';
}

// Helper functions
export function generateTransactionId(): string {
  return `purchase_tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function calculateTransactionTotals(items: PurchaseItem[]): {
  subtotalCost: number;
  totalCost: number;
} {
  const subtotalCost = items.reduce((sum, item) => sum + item.totalCost, 0);
  return {
    subtotalCost,
    totalCost: subtotalCost // Can be extended for taxes later
  };
}

export function createTransactionSummary(transaction: PurchaseTransaction): PurchaseTransactionSummary {
  return {
    _id: transaction._id,
    date: transaction.date,
    itemCount: transaction.items.length,
    totalCost: transaction.totalCost,
    supplierId: transaction.supplierId,
    supplierName: transaction.supplierName,
    hasReceiptImage: transaction.hasReceiptImage,
    performedBy: transaction.performedBy
  };
}