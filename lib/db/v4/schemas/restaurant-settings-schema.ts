// knowledge: v4 restaurant settings schema
export const restaurantSettingsSchema = {
  type: 'object',
  required: ['_id', 'type', 'createdAt', 'updatedAt'],
  properties: {
    _id: { type: 'string', pattern: '^restaurant-settings$' },
    _rev: { 
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: { type: 'string', enum: ['restaurant-settings'] },
    cogsSettings: {
      type: 'object',
      properties: {
        targetFoodCostPercentage: { type: 'number', default: 30 },
        defaultProfitMargin: { type: 'number', default: 50 },
        autoUpdatePrices: { type: 'boolean', default: false }
      },
      additionalProperties: false
    },
    currency: { type: 'string', default: 'DZD' },
    taxRate: { type: 'number', default: 0 },
    restaurantPhone: { type: 'string', default: '' },
    restaurantAddress: { type: 'string', default: '' },
    restaurantSecondaryPhone: { type: 'string', default: '' },
    restaurantLogoUrl: { type: 'string', default: '' },
    restaurantFooter: { type: 'string', default: 'Merci de votre visite!' },
    restaurantName: { type: 'string', default: '' },
    googleDrive: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean', default: false },
        type: { type: 'string', enum: ['oauth', 'service_account'] },
        encryptedCredentials: { type: 'string' }, // Encrypted JSON string
        setupBy: { type: 'string' }, // User ID who set it up
        setupDate: { type: 'string' }, // ISO date string
        lastTested: { type: 'string' } // ISO date string
      },
      additionalProperties: false
    },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

export interface RestaurantSettings {
  _id: 'restaurant-settings';
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'restaurant-settings';
  cogsSettings?: {
    targetFoodCostPercentage?: number;
    defaultProfitMargin?: number;
    autoUpdatePrices?: boolean;
  };
  currency?: string;
  taxRate?: number;
  restaurantPhone?: string;
  restaurantAddress?: string;
  restaurantSecondaryPhone?: string;
  restaurantLogoUrl?: string;
  restaurantFooter?: string;
  restaurantName?: string;
  googleDrive?: {
    enabled: boolean;
    type: string;
    encryptedCredentials: string;
    setupBy: string;
    setupDate: string;
    lastTested: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const DEFAULT_RESTAURANT_SETTINGS: RestaurantSettings = {
  _id: 'restaurant-settings',
  type: 'restaurant-settings',
  cogsSettings: {
    targetFoodCostPercentage: 30,
    defaultProfitMargin: 50,
    autoUpdatePrices: false
  },
  currency: 'DZD',
  taxRate: 0,
  restaurantPhone: '',
  restaurantAddress: '',
  restaurantSecondaryPhone: '',
  restaurantLogoUrl: '',
  restaurantFooter: 'Merci de votre visite!',
  restaurantName: '',
  googleDrive: {
    enabled: false,
    type: '',
    encryptedCredentials: '',
    setupBy: '',
    setupDate: '',
    lastTested: ''
  },
  createdAt: '',
  updatedAt: ''
};
// endknowledge