/**
 * Cash Session & Cash Count Schemas for V4 Database
 * 
 * This schema tracks actual cash drawer sessions with proper opening/closing validation
 * Fixes the fundamental flaw where "collection" was just sales instead of physical count
 * 
 * NEW: Added CashCountDocument for business-activity based cash counting
 * NEW: Added CashTransactionDocument for individual cash transactions
 */

"use client";

// Cash Session Document Schema
export const cashSessionDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'staffName', 'openedAt', 'openingAmount', 'status'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^cash_session:[0-9]+$'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['cash_session']
    },
    staffId: {
      type: 'string',
      description: 'ID of the cashier'
    },
    staffName: {
      type: 'string',
      description: 'Name of the cashier'
    },
    openedAt: {
      type: 'string',
      format: 'date-time',
      description: 'When the session was opened'
    },
    openedBy: {
      type: 'string',
      description: 'Who opened the session'
    },
    openingAmount: {
      type: 'number',
      description: 'Starting amount in drawer'
    },
    closedAt: {
      type: 'string',
      format: 'date-time',
      description: 'When the session was closed'
    },
    closedBy: {
      type: 'string',
      description: 'Who closed the session'
    },
    expectedAmount: {
      type: 'number',
      description: 'Expected amount based on opening + transactions'
    },
    countedAmount: {
      type: 'number',
      description: 'Actual physical count'
    },
    collectedAmount: {
      type: 'number',
      description: 'Amount collected from cashier (usually = countedAmount - nextOpeningAmount)'
    },
    nextOpeningAmount: {
      type: 'number',
      description: 'Amount left in drawer for next session'
    },
    variance: {
      type: 'number',
      description: 'Difference between expected and counted'
    },
    varianceReason: {
      type: 'string',
      description: 'Reason for variance if any'
    },
    status: {
      type: 'string',
      enum: ['open', 'closed', 'validated'],
      description: 'Session status'
    },
    notes: {
      type: 'string',
      description: 'Additional notes'
    },
    transactionIds: {
      type: 'array',
      items: {
        type: 'string'
      },
      description: 'IDs of transactions in this session'
    }
  }
};

// Cash Session Interface
export interface CashSessionDocument {
  _id: string;
  _rev?: string;
  type: 'cash_session';
  staffId: string;
  staffName: string;
  openedAt: string;
  openedBy: string;
  openingAmount: number;
  closedAt?: string;
  closedBy?: string;
  expectedAmount?: number;
  countedAmount?: number;
  collectedAmount?: number;
  nextOpeningAmount?: number;
  variance?: number;
  varianceReason?: string;
  status: 'open' | 'closed' | 'validated';
  notes?: string;
  transactionIds?: string[];
}

// Cash Count Document Schema (NEW - for business-activity based counting)
export const cashCountDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'countedAt', 'countedBy', 'expectedAmount', 'countedAmount', 'variance', 'collectedAmount', 'nextDrawerAmount'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^cash_count:[0-9]+$'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['cash_count']
    },
    countedAt: {
      type: 'string',
      format: 'date-time',
      description: 'When the cash count was performed'
    },
    countedBy: {
      type: 'string',
      description: 'Who performed the count'
    },
    expectedAmount: {
      type: 'number',
      description: 'Expected amount based on transactions'
    },
    countedAmount: {
      type: 'number',
      description: 'Actual physical count'
    },
    variance: {
      type: 'number',
      description: 'Difference between expected and counted'
    },
    collectedAmount: {
      type: 'number',
      description: 'Amount collected from drawer'
    },
    nextDrawerAmount: {
      type: 'number',
      description: 'Amount left in drawer after collection'
    },
    notes: {
      type: 'string',
      description: 'Additional notes about the count'
    },
    transactionSnapshot: {
      type: 'object',
      properties: {
        salesAmount: {
          type: 'number',
          description: 'Total sales amount at time of count'
        },
        manualAmount: {
          type: 'number',
          description: 'Total manual transactions at time of count'
        },
        transactionCount: {
          type: 'number',
          description: 'Number of transactions at time of count'
        }
      },
      description: 'Snapshot of transaction state at time of count'
    }
  }
};

// Cash Count Interface
export interface CashCountDocument {
  _id: string;
  _rev?: string;
  type: 'cash_count';
  countedAt: string;
  countedBy: string;
  expectedAmount: number;
  countedAmount: number;
  variance: number;
  collectedAmount: number;
  nextDrawerAmount: number;
  notes?: string;
  transactionSnapshot?: {
    salesAmount: number;
    manualAmount: number;
    transactionCount: number;
  };
}

// Cash Transaction Document Schema
export const cashTransactionDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'transactionType', 'amount', 'description', 'time', 'performedBy'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^cash_transaction:[0-9]+:'
    },
    _rev: {
      type: 'string',
      description: 'PouchDB revision identifier'
    },
    type: {
      type: 'string',
      enum: ['cash_transaction']
    },
    transactionType: {
      type: 'string',
      enum: ['sales', 'manual_in', 'manual_out', 'expense'],
      description: 'Type of cash transaction'
    },
    amount: {
      type: 'number',
      description: 'Transaction amount (positive for in, negative for out)'
    },
    description: {
      type: 'string',
      description: 'Description of the transaction'
    },
    time: {
      type: 'string',
      format: 'date-time',
      description: 'When the transaction occurred'
    },
    performedBy: {
      type: 'string',
      description: 'Who performed the transaction'
    },
    relatedDocId: {
      type: 'string',
      description: 'ID of related document (order, expense, etc.)'
    },
    metadata: {
      type: 'object',
      description: 'Additional transaction metadata'
    }
  }
};

// Cash Transaction Interface
export interface CashTransactionDocument {
  _id: string;
  _rev?: string;
  type: 'cash_transaction';
  transactionType: 'sales' | 'manual_in' | 'manual_out' | 'expense';
  amount: number;
  description: string;
  time: string;
  performedBy: string;
  relatedDocId?: string;
  metadata?: Record<string, any>;
  createdAt?: string; // For compatibility with existing code
} 