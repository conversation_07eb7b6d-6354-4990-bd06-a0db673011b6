"use client";

/**
 * NEW SEPARATE BALANCE PAYMENT SYSTEM SCHEMAS
 * 
 * This file contains the new payment system schemas that implement:
 * - Separate tracking for advances, deductions, and bonuses
 * - Payment snapshots instead of individual transaction history
 * - Clean, accurate balance calculations
 * - Independent balance management
 */

// ===== STAFF BALANCE DOCUMENT SCHEMA =====

export const staffBalanceDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'balanceType', 'amount', 'reason', 'date', 'isUsed', 'createdAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^staff_balance:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'staff_balance' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    balanceType: {
      type: 'string',
      enum: ['ADVANCE', 'DEDUCTION', 'BONUS']
    },
    amount: { 
      type: 'number',
      minimum: 0 // All amounts stored as positive, type determines usage
    },
    reason: { 
      type: 'string',
      minLength: 1,
      maxLength: 500
    },
    date: { 
      type: 'string',
      format: 'date-time'
    },
    isUsed: { 
      type: 'boolean',
      default: false
    },
    usedInPaymentId: { 
      type: 'string',
      pattern: '^payment_snapshot:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    usedDate: { 
      type: 'string',
      format: 'date-time'
    },
    createdAt: { 
      type: 'string',
      format: 'date-time'
    },
    updatedAt: { 
      type: 'string',
      format: 'date-time'
    }
  },
  additionalProperties: false
};

// ===== PAYMENT SNAPSHOT DOCUMENT SCHEMA =====

export const paymentSnapshotDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'staffId', 'paymentDate', 'baseSalary', 'bonusAmount', 'deductionAmount', 'advanceAmount', 'grossAmount', 'totalDeductions', 'netAmount', 'createdAt'],
  properties: {
    _id: {
      type: 'string',
      pattern: '^payment_snapshot:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    _rev: { type: 'string' },
    type: { type: 'string', const: 'payment_snapshot' },
    staffId: {
      type: 'string',
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    },
    paymentDate: { 
      type: 'string',
      format: 'date-time'
    },
    baseSalary: { 
      type: 'number',
      minimum: 0
    },
    bonusAmount: { 
      type: 'number',
      minimum: 0,
      default: 0
    },
    deductionAmount: { 
      type: 'number',
      minimum: 0,
      default: 0
    },
    advanceAmount: { 
      type: 'number',
      minimum: 0,
      default: 0
    },
    grossAmount: { 
      type: 'number',
      description: 'baseSalary + bonusAmount'
    },
    totalDeductions: { 
      type: 'number',
      minimum: 0,
      description: 'deductionAmount + advanceAmount'
    },
    netAmount: { 
      type: 'number',
      description: 'grossAmount - totalDeductions'
    },
    periodStart: { 
      type: 'string',
      format: 'date'
    },
    periodEnd: { 
      type: 'string',
      format: 'date'
    },
    notes: { 
      type: 'string',
      maxLength: 1000
    },
    usedBalanceIds: {
      type: 'array',
      items: {
        type: 'string',
        pattern: '^staff_balance:[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
      },
      description: 'IDs of balance entries used in this payment'
    },
    createdAt: { 
      type: 'string',
      format: 'date-time'
    },
    updatedAt: { 
      type: 'string',
      format: 'date-time'
    }
  },
  additionalProperties: false
};

// ===== DESIGN DOCUMENTS FOR INDEXING =====

export const staffBalanceDesignDoc = {
  _id: '_design/staff_balances',
  views: {
    by_staff: {
      map: `function(doc) {
        if (doc.type === 'staff_balance') {
          emit(doc.staffId, doc);
        }
      }`
    },
    by_staff_and_type: {
      map: `function(doc) {
        if (doc.type === 'staff_balance') {
          emit([doc.staffId, doc.balanceType], doc);
        }
      }`
    },
    by_staff_and_used: {
      map: `function(doc) {
        if (doc.type === 'staff_balance') {
          emit([doc.staffId, doc.isUsed], doc);
        }
      }`
    },
    unused_by_staff: {
      map: `function(doc) {
        if (doc.type === 'staff_balance' && !doc.isUsed) {
          emit(doc.staffId, doc);
        }
      }`
    },
    by_date: {
      map: `function(doc) {
        if (doc.type === 'staff_balance') {
          emit(doc.date, doc);
        }
      }`
    }
  }
};

export const paymentSnapshotDesignDoc = {
  _id: '_design/payment_snapshots',
  views: {
    by_staff: {
      map: `function(doc) {
        if (doc.type === 'payment_snapshot') {
          emit(doc.staffId, doc);
        }
      }`
    },
    by_staff_and_date: {
      map: `function(doc) {
        if (doc.type === 'payment_snapshot') {
          emit([doc.staffId, doc.paymentDate], doc);
        }
      }`
    },
    by_date: {
      map: `function(doc) {
        if (doc.type === 'payment_snapshot') {
          emit(doc.paymentDate, doc);
        }
      }`
    },
    by_period: {
      map: `function(doc) {
        if (doc.type === 'payment_snapshot' && doc.periodStart && doc.periodEnd) {
          emit([doc.staffId, doc.periodStart, doc.periodEnd], doc);
        }
      }`
    }
  }
};

// ===== TYPESCRIPT INTERFACES =====

export interface StaffBalanceDocument {
  _id: string;
  _rev?: string;
  type: 'staff_balance';
  staffId: string;
  balanceType: 'ADVANCE' | 'DEDUCTION' | 'BONUS';
  amount: number;
  reason: string;
  date: string;
  isUsed: boolean;
  usedInPaymentId?: string;
  usedDate?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface PaymentSnapshotDocument {
  _id: string;
  _rev?: string;
  type: 'payment_snapshot';
  staffId: string;
  paymentDate: string;
  paymentType?: 'SALARY' | 'SHIFT_PAYMENT';
  baseSalary: number;
  bonusAmount: number;
  deductionAmount: number;
  advanceAmount: number;
  grossAmount: number;
  totalDeductions: number;
  netAmount: number;
  periodStart?: string;
  periodEnd?: string;
  notes?: string;
  usedBalanceIds?: string[];
  // Per-shift specific data
  shiftData?: {
    attendanceIds: string[];
    shiftBreakdown: Array<{
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
      amount: number;
    }>;
    totalShifts: number;
  };
  createdAt: string;
  updatedAt?: string;
}

// ===== HELPER TYPES =====

export type BalanceType = 'ADVANCE' | 'DEDUCTION' | 'BONUS';

export interface BalanceSummary {
  advances: number;
  deductions: number;
  bonuses: number;
  total: number;
}

export interface PaymentCalculation {
  baseSalary: number;
  bonusAmount: number;
  deductionAmount: number;
  advanceAmount: number;
  grossAmount: number;
  totalDeductions: number;
  netAmount: number;
}
