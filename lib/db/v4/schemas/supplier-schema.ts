"use client";

import { v4 as uuidv4 } from 'uuid';

// Supplier Schema
export const supplierSchema = {
  type: 'object',
  required: ['id', 'name', 'phoneNumber', 'balance', 'isActive', 'createdAt', 'updatedAt'],
  properties: {
    id: { type: 'string' },
    name: { type: 'string' },
    phoneNumber: { type: 'string' },
    category: { type: 'string' },
    items: {
      type: 'array',
      items: { type: 'string' }
    },
    notes: { type: 'string' },
    balance: { type: 'number' },
    isActive: { type: 'boolean' },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' }
  }
};

export interface Supplier {
  id: string;
  name: string;
  phoneNumber: string;
  category?: string;
  items?: string[];
  notes?: string;
  balance: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Supplier Document Schema
export const supplierDocumentSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt', 'suppliers'],
  properties: {
    _id: { type: 'string' },
    _rev: { type: 'string' },
    type: { type: 'string', enum: ['supplier_document'] },
    schemaVersion: { type: 'string', enum: ['v4.0'] },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    suppliers: {
      type: 'array',
      items: supplierSchema
    }
  }
};

export interface SupplierDocument {
  _id: string;
  _rev?: string; // PouchDB revision field - optional for new docs, required for updates
  type: 'supplier_document';
  schemaVersion: 'v4.0';
  createdAt: string;
  updatedAt: string;
  suppliers: Supplier[];
}

// Default supplier document
export const DEFAULT_SUPPLIER_DOCUMENT: Omit<SupplierDocument, '_id' | 'createdAt' | 'updatedAt'> = {
  type: 'supplier_document',
  schemaVersion: 'v4.0',
  suppliers: []
};

export function createDefaultSupplierDocument(): SupplierDocument {
  const now = new Date().toISOString();
  return {
    _id: 'suppliers',
    ...DEFAULT_SUPPLIER_DOCUMENT,
    createdAt: now,
    updatedAt: now
  };
} 