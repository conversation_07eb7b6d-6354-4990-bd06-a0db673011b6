/**
 * Staff Menu Schema - Database structure for staff meal management
 */

// Staff Menu Configuration Document Schema
export const staffMenuConfigSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'createdAt', 'updatedAt'],
  properties: {
    _id: { 
      type: 'string', 
      const: 'staff-menu-config'
    },
    _rev: { type: 'string' },
    type: { 
      type: 'string', 
      const: 'staff_menu_config' 
    },
    schemaVersion: { 
      type: 'string', 
      const: 'v4.0' 
    },
    allowancePerShift: {
      type: 'number',
      minimum: 0,
      default: 1,
      description: 'Number of items staff can order per shift'
    },
    staffMenuItems: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'menuItemId', 'itemName', 'categoryName', 'size', 'originalPrice', 'staffPrice'],
        properties: {
          id: { type: 'string' },
          menuItemId: { type: 'string' },
          itemName: { type: 'string' },
          categoryName: { type: 'string' },
          size: { type: 'string' },
          originalPrice: { type: 'number', minimum: 0 },
          staffPrice: { type: 'number', minimum: 0 },
          isActive: { type: 'boolean', default: true }
        }
      }
    },
    isEnabled: {
      type: 'boolean',
      default: true
    },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// Staff Allowance Tracking Document Schema
export const staffAllowanceSchema = {
  type: 'object',
  required: ['_id', 'type', 'schemaVersion', 'staffId', 'shiftId', 'date', 'createdAt', 'updatedAt'],
  properties: {
    _id: { 
      type: 'string',
      pattern: '^staff-allowance:[0-9a-f-]+:[0-9a-f-]+:[0-9]{4}-[0-9]{2}-[0-9]{2}$'
    },
    _rev: { type: 'string' },
    type: { 
      type: 'string', 
      const: 'staff_allowance' 
    },
    schemaVersion: { 
      type: 'string', 
      const: 'v4.0' 
    },
    staffId: { type: 'string' },
    staffName: { type: 'string' },
    shiftId: { type: 'string' },
    shiftName: { type: 'string' },
    date: { 
      type: 'string',
      pattern: '^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
    },
    maxAllowance: { type: 'number', minimum: 0 },
    usedAllowance: { type: 'number', minimum: 0 },
    remainingAllowance: { type: 'number', minimum: 0 },
    orders: {
      type: 'array',
      items: { type: 'string' }
    },
    isPresent: { type: 'boolean', default: false },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' }
  },
  additionalProperties: false
};

// TypeScript Interfaces
export interface StaffMenuConfigDocument {
  _id: 'staff-menu-config';
  _rev?: string;
  type: 'staff_menu_config';
  schemaVersion: 'v4.0';
  allowancePerShift: number;
  staffMenuItems: StaffMenuItem[];
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StaffMenuItem {
  id: string;
  menuItemId: string;
  itemName: string;
  categoryName: string;
  size: string;
  originalPrice: number;
  staffPrice: number;
  isActive?: boolean;
}

export interface StaffAllowanceDocument {
  _id: string;
  _rev?: string;
  type: 'staff_allowance';
  schemaVersion: 'v4.0';
  staffId: string;
  staffName: string;
  shiftId: string;
  shiftName: string;
  date: string; // YYYY-MM-DD
  maxAllowance: number;
  usedAllowance: number;
  remainingAllowance: number;
  orders: string[];
  isPresent: boolean;
  createdAt: string;
  updatedAt: string;
}

// Default documents
export const DEFAULT_STAFF_MENU_CONFIG: Omit<StaffMenuConfigDocument, '_id'> = {
  type: 'staff_menu_config',
  schemaVersion: 'v4.0',
  allowancePerShift: 1,
  staffMenuItems: [],
  isEnabled: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}; 