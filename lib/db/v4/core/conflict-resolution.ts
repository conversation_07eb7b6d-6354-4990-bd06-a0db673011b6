/**
 * Universal Conflict Resolution Utilities for V4 Database Operations
 * 
 * This module provides robust conflict resolution patterns that can be used
 * across all operation files to handle PouchDB document conflicts.
 */

import { databaseV4 } from './db-instance';

/**
 * Retry operation with exponential backoff for conflict resolution
 */
export async function retryWithConflictResolution<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  operationName: string = 'operation'
): Promise<T> {
  let lastError: any = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // If it's a 409 conflict, retry with exponential backoff
      if (error.status === 409 && attempt < maxRetries) {
        const delay = Math.min(1000, 100 * Math.pow(2, attempt - 1)) + Math.random() * 50;
        console.warn(`[${operationName}] Document conflict, retrying in ${delay.toFixed(0)}ms... (${maxRetries - attempt} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // For other errors or no retries left, throw
      throw error;
    }
  }
  
  throw lastError;
}

/**
 * Get document with automatic retry on conflicts
 */
export async function safeGetDocument<T>(docId: string): Promise<T> {
  return retryWithConflictResolution(
    () => databaseV4.getDoc<T>(docId),
    3,
    `safeGetDocument(${docId})`
  );
}

/**
 * Update document with automatic conflict resolution using get-modify-put pattern
 */
export async function safeUpdateDocument<T extends { _id: string; _rev?: string }>(
  docId: string,
  updateFunction: (doc: T) => T,
  operationName: string = 'safeUpdateDocument'
): Promise<T> {
  return retryWithConflictResolution(async () => {
    // Get fresh document with latest revision
    const currentDoc = await databaseV4.getDoc<T>(docId);
    
    // Apply updates
    const updatedDoc = updateFunction(currentDoc);
    
    // Ensure we preserve the revision
    updatedDoc._rev = currentDoc._rev;
    updatedDoc._id = currentDoc._id;
    
    // Save the updated document
    const result = await databaseV4.putDoc(updatedDoc);
    
    // Return the updated document with new revision
    return { ...updatedDoc, _rev: result.rev };
  }, 3, operationName);
}

/**
 * Create or update document with conflict resolution
 */
export async function safeUpsertDocument<T extends { _id: string; _rev?: string }>(
  doc: T,
  operationName: string = 'safeUpsertDocument'
): Promise<T> {
  return retryWithConflictResolution(async () => {
    try {
      // Try to get existing document to get latest revision
      const existing = await databaseV4.getDoc<T>(doc._id);
      doc._rev = existing._rev;
    } catch (error: any) {
      // If document doesn't exist (404), that's fine - we'll create it
      if (error.status !== 404 && error.name !== 'not_found') {
        throw error;
      }
      // For new documents, ensure no _rev
      delete doc._rev;
    }
    
    // Save the document
    const result = await databaseV4.putDoc(doc);
    
    // Return with new revision
    return { ...doc, _rev: result.rev };
  }, 3, operationName);
}

/**
 * Array-based document update with conflict resolution
 * Useful for documents that contain arrays (like inventory items, menu categories, etc.)
 */
export async function safeUpdateArrayDocument<T extends { _id: string; _rev?: string }, K>(
  docId: string,
  arrayFieldName: keyof T,
  arrayUpdater: (items: K[]) => K[],
  operationName: string = 'safeUpdateArrayDocument'
): Promise<T> {
  return safeUpdateDocument<T>(
    docId,
    (doc) => {
      const currentArray = (doc[arrayFieldName] as K[]) || [];
      const updatedArray = arrayUpdater(currentArray);
      return {
        ...doc,
        [arrayFieldName]: updatedArray,
        updatedAt: new Date().toISOString()
      } as T;
    },
    operationName
  );
}

/**
 * Add item to array in document with conflict resolution
 */
export async function safeAddToArrayDocument<T extends { _id: string; _rev?: string }, K>(
  docId: string,
  arrayFieldName: keyof T,
  newItem: K,
  operationName: string = 'safeAddToArrayDocument'
): Promise<T> {
  return safeUpdateArrayDocument<T, K>(
    docId,
    arrayFieldName,
    (items) => [...items, newItem],
    operationName
  );
}

/**
 * Update item in array within document with conflict resolution
 */
export async function safeUpdateArrayItem<T extends { _id: string; _rev?: string }, K extends { id: string }>(
  docId: string,
  arrayFieldName: keyof T,
  itemId: string,
  itemUpdates: Partial<K>,
  operationName: string = 'safeUpdateArrayItem'
): Promise<T> {
  return safeUpdateArrayDocument<T, K>(
    docId,
    arrayFieldName,
    (items) => items.map(item => 
      item.id === itemId 
        ? { ...item, ...itemUpdates, updatedAt: new Date().toISOString() } as K
        : item
    ),
    operationName
  );
}

/**
 * Remove item from array in document with conflict resolution
 */
export async function safeRemoveFromArrayDocument<T extends { _id: string; _rev?: string }, K extends { id: string }>(
  docId: string,
  arrayFieldName: keyof T,
  itemId: string,
  operationName: string = 'safeRemoveFromArrayDocument'
): Promise<T> {
  return safeUpdateArrayDocument<T, K>(
    docId,
    arrayFieldName,
    (items) => items.filter(item => item.id !== itemId),
    operationName
  );
}

/**
 * Create default document if it doesn't exist, with conflict resolution
 * Now uses safe initialization to prevent concurrent access issues
 */
export async function safeEnsureDocument<T extends { _id: string; _rev?: string }>(
  docId: string,
  defaultDocumentFactory: () => T,
  operationName: string = 'safeEnsureDocument'
): Promise<T> {
  // Use the new safe initialization method to prevent concurrent conflicts
  return await databaseV4.safeInitializeDocument<T>(
    docId,
    defaultDocumentFactory,
    operationName
  );
} 