import { DatabaseV4 } from './db-instance';
import { getCurrentRestaurantId, forceRestaurantId } from '../utils/restaurant-id';

/**
 * 🔧 Proper Mobile PouchDB Main Instance
 *
 * This creates a properly initialized database instance that:
 * 1. Does NOT lie about being initialized
 * 2. <PERSON>perly waits for real PouchDB initialization
 * 3. Handles mobile environments correctly
 * 4. Provides proper error handling and recovery
 */

/**
 * Get restaurant ID from various sources with proper fallbacks
 */
function getRestaurantIdFromStorage(): string | null {
  if (typeof window === 'undefined') return null;

  try {
    // Check cookies first (cross-tab consistency)
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'auth_restaurant_id') {
        const restaurantId = decodeURIComponent(value);
        console.log(`📊 Found restaurant ID in cookies: ${restaurantId}`);
        return restaurantId;
      }
    }

    // Check localStorage
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      const parsedData = JSON.parse(authData);
      if (parsedData.restaurantId && parsedData.restaurantId !== 'restaurant:default') {
        const restaurantId = parsedData.restaurantId;
        console.log(`📊 Found restaurant ID in localStorage: ${restaurantId}`);

        // Set cookie for future cross-tab consistency
        document.cookie = `auth_restaurant_id=${restaurantId}; path=/; max-age=31536000; SameSite=Lax`;
        return restaurantId;
      }
    }
  } catch (e) {
    console.warn('Error getting restaurant ID from storage:', e);
  }

  return null;
}

/**
 * Generate a proper fallback restaurant ID
 */
function generateFallbackRestaurantId(): string {
  try {
    const authData = localStorage.getItem('auth_data');
    if (authData) {
      const parsedData = JSON.parse(authData);
      if (parsedData.name || parsedData.userId) {
        const username = parsedData.name || parsedData.userId;
        const fallbackId = `restaurant:${username.replace(/[^a-z0-9]/gi, '_')}_${Date.now().toString(36)}`;
        console.log(`📊 Generated fallback restaurant ID: ${fallbackId}`);
        forceRestaurantId(fallbackId);
        return fallbackId;
      }
    }
  } catch (e) {
    console.warn('Error generating fallback restaurant ID:', e);
  }

  // Absolute last resort
  const lastResortId = `restaurant:fallback_${Date.now().toString(36)}`;
  console.log(`📊 Using last resort fallback ID: ${lastResortId}`);
  return lastResortId;
}

/**
 * Create a properly initialized database instance
 * NO FAKE INITIALIZATION - waits for real PouchDB to be ready
 */
const createProperlyInitializedDB = (): DatabaseV4 => {
  const db = new DatabaseV4();

  // DO NOT set isInitialized = true here!
  // Let the real initialization process handle this

  // Only initialize if we're in a browser environment
  if (typeof window !== 'undefined') {
    console.log('🔄 Starting proper database initialization...');

    // Get restaurant ID with proper fallbacks
    const restaurantId = getRestaurantIdFromStorage() ||
                        getCurrentRestaurantId() ||
                        generateFallbackRestaurantId();

    if (!restaurantId || restaurantId === 'restaurant:default') {
      console.error('❌ Could not determine valid restaurant ID');
      return db;
    }

    console.log(`📊 Initializing database with ID: ${restaurantId}`);

    // Initialize immediately - no setTimeout delays!
    db.initialize(restaurantId).catch(err => {
      console.error('❌ Database initialization failed:', err);
      // Reset initialization state on failure
      db.isInitialized = false;
    });
  }

  return db;
};

export const mainDbInstance = createProperlyInitializedDB();