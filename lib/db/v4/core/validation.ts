"use client";

import Ajv from 'ajv';
import addFormats from 'ajv-formats';

/**
 * V4 Validation Utilities
 *
 * Provides validation functionality for the v4 database implementation.
 */

// Initialize AJV with format support
const ajv = new Ajv({
  allErrors: true,
  removeAdditional: false,
  useDefaults: true,
  coerceTypes: true,
});

// Add format support (including date-time)
addFormats(ajv);

// Custom error class for validation errors
export class ValidationError extends Error {
  errors: any[];

  constructor(message: string, errors: any[]) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

/**
 * Validate data against a JSON schema
 */
export function validateSchema<T>(data: any, schema: object): T {
  const validate = ajv.compile(schema);
  const valid = validate(data);

  if (!valid) {
    const errorMessage = formatValidationErrors(validate.errors || []);
    throw new ValidationError(`Validation failed: ${errorMessage}`, validate.errors || []);
  }

  return data as T;
}

/**
 * Format validation errors into a readable string
 */
function formatValidationErrors(errors: any[]): string {
  return errors.map(error => {
    const path = error.instancePath || '';
    const property = error.params.missingProperty || error.params.additionalProperty || '';
    const fullPath = path + (property ? `/${property}` : '');

    switch (error.keyword) {
      case 'required':
        return `Missing required property: ${error.params.missingProperty}`;
      case 'type':
        return `${fullPath} should be ${error.params.type}`;
      case 'format':
        return `${fullPath} should match format ${error.params.format}`;
      case 'enum':
        return `${fullPath} should be one of: ${error.params.allowedValues.join(', ')}`;
      case 'additionalProperties':
        return `Additional property ${error.params.additionalProperty} not allowed`;
      default:
        return `${fullPath}: ${error.message}`;
    }
  }).join('; ');
}

/**
 * Generate a UUID
 */
export function generateUuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate a document ID with type prefix
 */
export function generateId(type: string): string {
  return `${type}:${generateUuid()}`;
}

/**
 * Validate ID format
 */
export function validateIdFormat(id: string, type: string): boolean {
  return id.startsWith(`${type}:`);
}

/**
 * Ensure document has required metadata
 */
export function ensureDocumentMetadata<T extends { _id: string }>(
  doc: T,
  type: string
): T & { schemaVersion: string; createdAt: string; updatedAt: string } {
  const now = new Date().toISOString();

  // Validate ID format
  if (!validateIdFormat(doc._id, type)) {
    throw new ValidationError(
      `Invalid document ID format: ${doc._id}. Expected format: ${type}:{uuid}`,
      []
    );
  }

  return {
    ...doc,
    schemaVersion: 'v4.0',
    createdAt: (doc as any).createdAt || now,
    updatedAt: now,
  };
}
