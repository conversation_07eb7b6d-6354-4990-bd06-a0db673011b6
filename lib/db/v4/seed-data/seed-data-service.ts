/**
 * Seed Data Service
 * 
 * Service for initializing the database with universal seed data
 * for new restaurant setups.
 */

import { databaseV4 } from '../core/db-instance';
import {
  prepareStockItems,
  prepareSubRecipes,
  prepareMenuDocument,
  prepareMenuItemRecipes
} from './universal-seed-data';

export interface SeedDataOptions {
  skipIfExists?: boolean;
  categories?: string[]; // Only seed specific categories
  overwrite?: boolean;
}

export interface SeedDataResult {
  success: boolean;
  message: string;
  details: {
    stockItems: number;
    subRecipes: number;
    menuDocument: boolean;
    menuItemRecipes: number;
  };
  errors?: string[];
}

/**
 * Initialize database with universal seed data
 */
export async function initializeUniversalSeedData(
  options: SeedDataOptions = {}
): Promise<SeedDataResult> {
  const {
    skipIfExists = true,
    categories,
    overwrite = false
  } = options;

  console.log('🌱 [SeedDataService] Starting universal seed data initialization...');

  if (!databaseV4.isInitialized) {
    return {
      success: false,
      message: 'Database not initialized',
      details: {
        stockItems: 0,
        subRecipes: 0,
        menuDocument: false,
        menuItemRecipes: 0
      },
      errors: ['Database must be initialized before seeding data']
    };
  }

  const result: SeedDataResult = {
    success: true,
    message: 'Seed data initialization completed',
    details: {
      stockItems: 0,
      subRecipes: 0,
      menuDocument: false,
      menuItemRecipes: 0
    },
    errors: []
  };

  try {
    // 1. Seed Stock Items FIRST and get their actual IDs
    console.log('📦 [SeedDataService] Seeding stock items...');
    const stockItemsResult = await seedStockItems(skipIfExists, overwrite);
    result.details.stockItems = stockItemsResult.count;
    if (stockItemsResult.errors.length > 0) {
      result.errors?.push(...stockItemsResult.errors);
    }

    // 2. Get the actual stock item mapping (name -> real ID)
    console.log('🔗 [SeedDataService] Building stock item ID mapping...');
    const stockItemMapping = await buildStockItemMapping();

    // 3. Seed Sub-Recipes using real stock item IDs
    console.log('🧪 [SeedDataService] Seeding sub-recipes...');
    const subRecipesResult = await seedSubRecipes(skipIfExists, overwrite, stockItemMapping);
    result.details.subRecipes = subRecipesResult.count;
    if (subRecipesResult.errors.length > 0) {
      result.errors?.push(...subRecipesResult.errors);
    }

    // 4. Get the actual sub-recipe mapping (name -> real ID)
    console.log('🔗 [SeedDataService] Building sub-recipe ID mapping...');
    const subRecipeMapping = await buildSubRecipeMapping();

    // 5. Seed Menu Document
    console.log('📋 [SeedDataService] Seeding menu document...');
    const menuResult = await seedMenuDocument(skipIfExists, overwrite, categories);
    result.details.menuDocument = menuResult.success;
    if (menuResult.errors.length > 0) {
      result.errors?.push(...menuResult.errors);
    }

    // 6. Seed Menu Item Recipes using real IDs
    console.log('🍳 [SeedDataService] Seeding menu item recipes...');
    const recipesResult = await seedMenuItemRecipes(skipIfExists, overwrite, categories, stockItemMapping, subRecipeMapping);
    result.details.menuItemRecipes = recipesResult.count;
    if (recipesResult.errors.length > 0) {
      result.errors?.push(...recipesResult.errors);
    }

    // Check if there were any errors
    if (result.errors && result.errors.length > 0) {
      result.success = false;
      result.message = `Seed data initialization completed with ${result.errors.length} errors`;
    }

    console.log('✅ [SeedDataService] Universal seed data initialization completed:', result);
    return result;

  } catch (error) {
    console.error('❌ [SeedDataService] Error during seed data initialization:', error);
    return {
      success: false,
      message: 'Failed to initialize seed data',
      details: {
        stockItems: 0,
        subRecipes: 0,
        menuDocument: false,
        menuItemRecipes: 0
      },
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}

/**
 * Build mapping of stock item names to their actual database IDs
 */
async function buildStockItemMapping(): Promise<Record<string, string>> {
  const mapping: Record<string, string> = {};
  
  try {
    const { getInventory } = await import('../operations/inventory-ops');
    const inventory = await getInventory();
    
    // Create mapping from our seed names to actual database IDs
    const seedItems = prepareStockItems();
    
    for (const seedItem of seedItems) {
      // Find the actual item in inventory by name
      const actualItem = inventory.items?.find(item => item.name === seedItem.name);
      if (actualItem) {
        mapping[seedItem.id] = actualItem.id; // Map seed ID to real ID
        console.log(`🔗 [SeedDataService] Mapped ${seedItem.id} -> ${actualItem.id} (${seedItem.name})`);
      } else {
        console.warn(`⚠️ [SeedDataService] Could not find stock item: ${seedItem.name}`);
      }
    }
    
  } catch (error) {
    console.error('❌ [SeedDataService] Error building stock item mapping:', error);
  }
  
  return mapping;
}

/**
 * Build mapping of sub-recipe names to their actual database IDs
 */
async function buildSubRecipeMapping(): Promise<Record<string, string>> {
  const mapping: Record<string, string> = {};
  
  try {
    const db = databaseV4.getDatabase();
    const subRecipeQuery = await db.find({
      selector: { type: 'sub-recipe' }
    });
    
    // Create mapping from our seed names to actual database IDs
    const seedSubRecipes = prepareSubRecipes();
    
    for (const seedSubRecipe of seedSubRecipes) {
      // Find the actual sub-recipe in database by name
      const actualSubRecipe = subRecipeQuery.docs.find((doc: any) => doc.name === seedSubRecipe.name);
      if (actualSubRecipe && seedSubRecipe.id) {
        // Use the explicit seed ID for mapping
        mapping[seedSubRecipe.id] = actualSubRecipe._id;
        console.log(`🔗 [SeedDataService] Mapped ${seedSubRecipe.id} -> ${actualSubRecipe._id} (${seedSubRecipe.name})`);
      } else {
        console.warn(`⚠️ [SeedDataService] Could not find sub-recipe: ${seedSubRecipe.name}`);
      }
    }
    
  } catch (error) {
    console.error('❌ [SeedDataService] Error building sub-recipe mapping:', error);
  }
  
  return mapping;
}

/**
 * Seed stock items using proper inventory operations
 */
async function seedStockItems(skipIfExists: boolean, overwrite: boolean) {
  const result = { count: 0, errors: [] as string[] };
  
  try {
    const stockItems = prepareStockItems();
    const { getInventory, addStockItem, updateStockItem } = await import('../operations/inventory-ops');

    // Get current inventory
    const inventory = await getInventory();
    const existingItems = inventory.items || [];

    for (const stockItem of stockItems) {
      try {
        // Check if item already exists by name (not by seed ID)
        const existingItem = existingItems.find(item => item.name === stockItem.name);

        if (existingItem && skipIfExists && !overwrite) {
          console.log(`⏭️ [SeedDataService] Stock item ${stockItem.name} already exists, skipping`);
          continue;
        }

        if (existingItem && overwrite) {
          // Update existing item
          await updateStockItem(existingItem.id, {
            ...stockItem,
            updatedAt: new Date().toISOString()
          });
          console.log(`🔄 [SeedDataService] Updated stock item: ${stockItem.name}`);
        } else {
          // Add new item (let the system generate the ID)
          const newItem = await addStockItem({
            name: stockItem.name,
            category: stockItem.category,
            unit: stockItem.unit,
            minLevel: stockItem.minLevel,
            costPerUnit: stockItem.costPerUnit,
            quantity: stockItem.quantity,
            purchaseUnits: stockItem.purchaseUnits
          });
          console.log(`✅ [SeedDataService] Created stock item: ${stockItem.name} with ID: ${newItem.id}`);
        }

        result.count++;

      } catch (error) {
        const errorMsg = `Failed to create stock item ${stockItem.name}`;
        result.errors.push(errorMsg);
        console.error(`❌ [SeedDataService] ${errorMsg}:`, error);
        // Continue with other items
      }
    }
  } catch (error) {
    const errorMsg = 'Failed to prepare or process stock items';
    result.errors.push(errorMsg);
    console.error(`❌ [SeedDataService] ${errorMsg}:`, error);
  }

  return result;
}

/**
 * Seed sub-recipes with better error handling and real stock item IDs
 */
async function seedSubRecipes(skipIfExists: boolean, overwrite: boolean, stockItemMapping: Record<string, string>) {
  const result = { count: 0, errors: [] as string[] };
  
  try {
    const subRecipes = prepareSubRecipes();
    const db = databaseV4.getDatabase();

    for (const subRecipe of subRecipes) {
      try {
        // Update ingredients to use real stock item IDs
        const updatedIngredients = subRecipe.ingredients.map(ingredient => {
          const realStockItemId = stockItemMapping[ingredient.stockItemId];
          if (realStockItemId) {
            console.log(`🔗 [SeedDataService] Mapping ingredient ${ingredient.stockItemId} -> ${realStockItemId}`);
            return { ...ingredient, stockItemId: realStockItemId };
          } else {
            console.warn(`⚠️ [SeedDataService] No mapping found for stock item: ${ingredient.stockItemId}`);
            return ingredient; // Keep original if no mapping found
          }
        });

        const updatedSubRecipe = {
          ...subRecipe,
          ingredients: updatedIngredients,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Use explicit ID if provided in seed data
        if (subRecipe.id) {
          updatedSubRecipe._id = subRecipe.id;
        }

        // Check if exists by name (since IDs are generated)
        if (skipIfExists && !overwrite) {
          try {
            const existingQuery = await db.find({
              selector: {
                type: 'sub-recipe',
                name: subRecipe.name
              },
              limit: 1
            });

            if (existingQuery.docs.length > 0) {
              console.log(`⏭️ [SeedDataService] Sub-recipe ${subRecipe.name} already exists, skipping`);
              continue;
            }
          } catch (error) {
            console.warn(`⚠️ [SeedDataService] Error checking existing sub-recipe ${subRecipe.name}:`, error);
            // Continue anyway
          }
        }

        // If overwriting, try to find and update existing
        if (overwrite) {
          try {
            const existingQuery = await db.find({
              selector: {
                type: 'sub-recipe',
                name: subRecipe.name
              },
              limit: 1
            });

            if (existingQuery.docs.length > 0) {
              const existing = existingQuery.docs[0];
              updatedSubRecipe._id = existing._id;
              updatedSubRecipe._rev = existing._rev;
            }
          } catch (error) {
            console.warn(`⚠️ [SeedDataService] Error finding existing sub-recipe ${subRecipe.name}:`, error);
            // Continue with new document
          }
        }

        await db.put(updatedSubRecipe);
        result.count++;
        console.log(`✅ [SeedDataService] Created sub-recipe: ${subRecipe.name}`);

      } catch (error) {
        const errorMsg = `Failed to create sub-recipe ${subRecipe.name}`;
        result.errors.push(errorMsg);
        console.error(`❌ [SeedDataService] ${errorMsg}:`, error);
        // Continue with other recipes
      }
    }
  } catch (error) {
    const errorMsg = 'Failed to prepare sub-recipes';
    result.errors.push(errorMsg);
    console.error(`❌ [SeedDataService] ${errorMsg}:`, error);
  }

  return result;
}

/**
 * Seed menu document
 */
async function seedMenuDocument(skipIfExists: boolean, overwrite: boolean, categories?: string[]) {
  const result = { success: false, errors: [] as string[] };

  try {
    const db = databaseV4.getDatabase();
    let menuDocument = prepareMenuDocument();

    // Filter categories if specified
    if (categories && categories.length > 0) {
      menuDocument.categories = menuDocument.categories.filter(cat => 
        categories.includes(cat.id)
      );
    }

    // Check if exists
    if (skipIfExists && !overwrite) {
      try {
        await db.get('menu');
        console.log('⏭️ [SeedDataService] Menu document already exists, skipping');
        result.success = true;
        return result;
      } catch (error: any) {
        if (error.status !== 404) {
          throw error;
        }
        // Document doesn't exist, continue with creation
      }
    }

    // 🚨 CRITICAL FIX: Prevent data loss during overwrite
    if (overwrite) {
      try {
        const existing = await db.get('menu');

        // Check if existing menu has user data (non-empty categories)
        if (existing.categories && existing.categories.length > 0) {
          console.warn('⚠️ [SeedDataService] Existing menu has data - merging instead of overwriting');

          // Merge strategy: Keep existing categories, add missing ones from seed
          const existingCategoryIds = new Set(existing.categories.map(c => c.id));
          const newCategories = menuDocument.categories.filter(c => !existingCategoryIds.has(c.id));

          if (newCategories.length > 0) {
            existing.categories.push(...newCategories);
            existing.updatedAt = new Date().toISOString();
            await db.put(existing);
            console.log(`✅ [SeedDataService] Merged ${newCategories.length} new categories into existing menu`);
          } else {
            console.log('ℹ️ [SeedDataService] No new categories to add - existing menu is complete');
          }
          result.success = true;
          return result;
        }

        // If existing menu is empty, safe to overwrite
        menuDocument._rev = existing._rev;
      } catch (error: any) {
        if (error.status !== 404) {
          throw error;
        }
      }
    }

    await db.put(menuDocument);
    result.success = true;
    console.log('✅ [SeedDataService] Created menu document');

  } catch (error) {
    const errorMsg = `Failed to create menu document: ${error}`;
    result.errors.push(errorMsg);
    console.error(`❌ [SeedDataService] ${errorMsg}`);
  }

  return result;
}

/**
 * Seed menu item recipes with real stock item and sub-recipe IDs
 */
async function seedMenuItemRecipes(
  skipIfExists: boolean, 
  overwrite: boolean, 
  categories?: string[], 
  stockItemMapping?: Record<string, string>, 
  subRecipeMapping?: Record<string, string>
) {
  const result = { count: 0, errors: [] as string[] };
  let menuItemRecipes = prepareMenuItemRecipes();

  // Filter recipes by categories if specified
  if (categories && categories.length > 0) {
    const menuDocument = prepareMenuDocument();
    const allowedMenuItems = menuDocument.categories
      .filter(cat => categories.includes(cat.id))
      .flatMap(cat => cat.items.map(item => item.id));
    
    menuItemRecipes = menuItemRecipes.filter(recipe => 
      allowedMenuItems.includes(recipe.menuItemId)
    );
  }

  for (const recipe of menuItemRecipes) {
    try {
      const db = databaseV4.getDatabase();

      // Update ingredients to use real IDs
      const updatedIngredients = recipe.ingredients.map(ingredient => {
        if ('stockItemId' in ingredient && stockItemMapping) {
          const realStockItemId = stockItemMapping[ingredient.stockItemId];
          if (realStockItemId) {
            console.log(`🔗 [SeedDataService] Mapping recipe ingredient ${ingredient.stockItemId} -> ${realStockItemId}`);
            return { ...ingredient, stockItemId: realStockItemId };
          } else {
            console.warn(`⚠️ [SeedDataService] No mapping found for stock item: ${ingredient.stockItemId}`);
            return ingredient;
          }
        } else if ('subRecipeId' in ingredient && subRecipeMapping) {
          const realSubRecipeId = subRecipeMapping[ingredient.subRecipeId];
          if (realSubRecipeId) {
            console.log(`🔗 [SeedDataService] Mapping recipe sub-recipe ${ingredient.subRecipeId} -> ${realSubRecipeId}`);
            return { ...ingredient, subRecipeId: realSubRecipeId };
          } else {
            console.warn(`⚠️ [SeedDataService] No mapping found for sub-recipe: ${ingredient.subRecipeId}`);
            return ingredient;
          }
        }
        return ingredient;
      });

      const updatedRecipe = {
        ...recipe,
        ingredients: updatedIngredients
      };

      // Check if exists by menuItemId and size
      if (skipIfExists && !overwrite) {
        try {
          const existingQuery = await db.find({
            selector: {
              type: 'menu-item-recipe',
              menuItemId: recipe.menuItemId,
              size: recipe.size
            }
          });

          if (existingQuery.docs.length > 0) {
            console.log(`⏭️ [SeedDataService] Recipe for ${recipe.menuItemName} (${recipe.size}) already exists, skipping`);
            continue;
          }
        } catch (error) {
          console.warn(`⚠️ [SeedDataService] Error checking existing recipe: ${error}`);
        }
      }

      await db.put(updatedRecipe);
      result.count++;
      console.log(`✅ [SeedDataService] Created recipe: ${recipe.menuItemName} (${recipe.size})`);

    } catch (error) {
      const errorMsg = `Failed to create recipe ${recipe.menuItemName} (${recipe.size}): ${error}`;
      result.errors.push(errorMsg);
      console.error(`❌ [SeedDataService] ${errorMsg}`);
    }
  }

  return result;
}

/**
 * Check if seed data exists
 */
export async function checkSeedDataExists(): Promise<{
  stockItems: boolean;
  subRecipes: boolean;
  menuDocument: boolean;
  menuItemRecipes: boolean;
}> {
  if (!databaseV4.isInitialized) {
    return {
      stockItems: false,
      subRecipes: false,
      menuDocument: false,
      menuItemRecipes: false
    };
  }

  const db = databaseV4.getDatabase();
  const result = {
    stockItems: false,
    subRecipes: false,
    menuDocument: false,
    menuItemRecipes: false
  };

  try {
    // Check stock items
    const stockQuery = await db.find({
      selector: { type: 'stock-item' },
      limit: 1
    });
    result.stockItems = stockQuery.docs.length > 0;

    // Check sub-recipes
    const subRecipeQuery = await db.find({
      selector: { type: 'sub-recipe' },
      limit: 1
    });
    result.subRecipes = subRecipeQuery.docs.length > 0;

    // Check menu document
    try {
      await db.get('menu');
      result.menuDocument = true;
    } catch (error: any) {
      if (error.status !== 404) {
        console.warn('Error checking menu document:', error);
      }
    }

    // Check menu item recipes
    const recipeQuery = await db.find({
      selector: { type: 'menu-item-recipe' },
      limit: 1
    });
    result.menuItemRecipes = recipeQuery.docs.length > 0;

  } catch (error) {
    console.error('Error checking seed data existence:', error);
  }

  return result;
}

/**
 * Clear all seed data (useful for testing)
 */
export async function clearSeedData(): Promise<void> {
  if (!databaseV4.isInitialized) {
    throw new Error('Database not initialized');
  }

  console.log('🧹 [SeedDataService] Clearing seed data...');
  const db = databaseV4.getDatabase();

  try {
    // Clear inventory items (stock items are now part of inventory document)
    try {
      const { getInventory, updateInventory } = await import('../operations/inventory-ops');
      const inventory = await getInventory();
      
      // Clear all items from inventory
      const clearedInventory = {
        ...inventory,
        items: [],
        updatedAt: new Date().toISOString()
      };
      
      await updateInventory(clearedInventory);
      console.log('✅ [SeedDataService] Cleared inventory items');
    } catch (error) {
      console.warn('⚠️ [SeedDataService] Error clearing inventory:', error);
    }

    // Clear sub-recipes
    const subRecipeQuery = await db.find({
      selector: { type: 'sub-recipe' }
    });
    for (const doc of subRecipeQuery.docs) {
      await db.remove(doc);
    }
    console.log('✅ [SeedDataService] Cleared sub-recipes');

    // Clear menu document
    try {
      const menuDoc = await db.get('menu');
      await db.remove(menuDoc);
      console.log('✅ [SeedDataService] Cleared menu document');
    } catch (error: any) {
      if (error.status !== 404) {
        console.warn('⚠️ [SeedDataService] Error clearing menu:', error);
      }
    }

    // Clear menu item recipes
    const recipeQuery = await db.find({
      selector: { type: 'menu-item-recipe' }
    });
    for (const doc of recipeQuery.docs) {
      await db.remove(doc);
    }
    console.log('✅ [SeedDataService] Cleared menu item recipes');

    console.log('✅ [SeedDataService] Seed data cleared successfully');

  } catch (error) {
    console.error('❌ [SeedDataService] Error clearing seed data:', error);
    throw error;
  }
}