/**
 * Transaction Rollback Utility
 * 
 * Provides rollback mechanisms for failed payment processing
 * to maintain data consistency.
 */

import { updateOrder } from '../operations/order-ops';
import { deleteCashTransaction } from '../operations/cash-ops';
import { OrderDocument } from '../schemas/order-schema';

export interface RollbackOperation {
  type: 'order_update' | 'cash_transaction' | 'stock_consumption';
  orderId?: string;
  transactionId?: string;
  originalData?: any;
  operation: () => Promise<void>;
}

export class TransactionRollback {
  private operations: RollbackOperation[] = [];

  /**
   * Add a rollback operation
   */
  addRollback(operation: RollbackOperation): void {
    this.operations.push(operation);
  }

  /**
   * Execute all rollback operations in reverse order
   */
  async executeRollback(): Promise<void> {
    console.log(`[TransactionRollback] Executing ${this.operations.length} rollback operations`);
    
    // Execute in reverse order (LIFO)
    for (let i = this.operations.length - 1; i >= 0; i--) {
      const operation = this.operations[i];
      
      try {
        console.log(`[TransactionRollback] Rolling back ${operation.type}:`, {
          orderId: operation.orderId,
          transactionId: operation.transactionId
        });
        
        await operation.operation();
        
        console.log(`[TransactionRollback] ✅ Rolled back ${operation.type}`);
      } catch (error) {
        console.error(`[TransactionRollback] ❌ Failed to rollback ${operation.type}:`, error);
        // Continue with other rollbacks even if one fails
      }
    }
    
    this.operations = []; // Clear operations after rollback
  }

  /**
   * Clear all rollback operations (when transaction succeeds)
   */
  clear(): void {
    this.operations = [];
  }
}

/**
 * Safe payment processing with automatic rollback on failure
 */
export async function safePaymentProcessing<T>(
  operation: (rollback: TransactionRollback) => Promise<T>
): Promise<T> {
  const rollback = new TransactionRollback();
  
  try {
    const result = await operation(rollback);
    rollback.clear(); // Success - no rollback needed
    return result;
  } catch (error) {
    console.error('[safePaymentProcessing] Payment processing failed, executing rollback:', error);
    await rollback.executeRollback();
    throw error;
  }
}

/**
 * Helper to create order update rollback
 */
export function createOrderUpdateRollback(
  orderId: string,
  originalOrder: OrderDocument
): RollbackOperation {
  return {
    type: 'order_update',
    orderId,
    originalData: originalOrder,
    operation: async () => {
      await updateOrder(orderId, {
        status: originalOrder.status,
        paymentStatus: originalOrder.paymentStatus,
        paymentMethod: originalOrder.paymentMethod,
        paymentDetails: originalOrder.paymentDetails,
        totalCogs: originalOrder.totalCogs,
        grossProfit: originalOrder.grossProfit,
        profitMargin: originalOrder.profitMargin,
        completedAt: originalOrder.completedAt
      });
    }
  };
}

/**
 * Helper to create cash transaction rollback
 */
export function createCashTransactionRollback(
  transactionId: string
): RollbackOperation {
  return {
    type: 'cash_transaction',
    transactionId,
    operation: async () => {
      await deleteCashTransaction(transactionId);
    }
  };
}