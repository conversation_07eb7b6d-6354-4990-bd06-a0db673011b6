/**
 * Order Status Transition Validation
 * 
 * This module provides validation for order status transitions to prevent
 * invalid state changes that could corrupt business logic.
 * 
 * BUSINESS WORKFLOW CLARIFICATION:
 * - pending: Order is being cooked/prepared (kitchen is working on it)
 * - preparing: Legacy status, treated same as pending for backward compatibility
 * - served: Food is ready and served to customer
 * - completed: Payment processed and order finalized
 * - cancelled: Order was cancelled at any stage
 */

export type OrderStatus = 'pending' | 'preparing' | 'served' | 'completed' | 'cancelled';

/**
 * Valid status transitions map
 * Each status maps to an array of valid next statuses
 * 
 * CORRECTED BUSINESS FLOW:
 * pending (cooking) → served → completed (paid)
 * preparing (legacy, same as pending) → served → completed
 */
const VALID_TRANSITIONS: Record<OrderStatus, OrderStatus[]> = {
  pending: ['served', 'completed', 'cancelled'], // Can go directly to completed for quick payment
  preparing: ['served', 'completed', 'cancelled'], // Legacy compatibility
  served: ['completed', 'cancelled'],
  completed: [], // Terminal state - no further transitions allowed
  cancelled: []  // Terminal state - no further transitions allowed
};

/**
 * Validates if a status transition is allowed
 * 
 * @param currentStatus - The current order status
 * @param newStatus - The desired new status
 * @returns true if transition is valid, false otherwise
 */
export function isValidStatusTransition(
  currentStatus: OrderStatus, 
  newStatus: OrderStatus
): boolean {
  // Allow same status (no-op)
  if (currentStatus === newStatus) {
    return true;
  }

  // Handle undefined or invalid currentStatus
  if (!currentStatus || !(currentStatus in VALID_TRANSITIONS)) {
    console.warn(`[OrderStatusValidation] Invalid currentStatus: "${currentStatus}". Defaulting to 'pending'.`);
    currentStatus = 'pending';
  }

  const validNextStatuses = VALID_TRANSITIONS[currentStatus];
  
  // Additional safety check
  if (!validNextStatuses || !Array.isArray(validNextStatuses)) {
    console.error(`[OrderStatusValidation] No valid transitions found for status: "${currentStatus}"`);
    return false;
  }
  
  return validNextStatuses.includes(newStatus);
}

/**
 * Gets all valid next statuses for a given current status
 * 
 * @param currentStatus - The current order status
 * @returns Array of valid next statuses
 */
export function getValidNextStatuses(currentStatus: OrderStatus): OrderStatus[] {
  return VALID_TRANSITIONS[currentStatus] || [];
}

/**
 * Validates a status transition and throws an error if invalid
 * 
 * @param currentStatus - The current order status
 * @param newStatus - The desired new status
 * @param orderId - Order ID for error context
 * @throws Error if transition is invalid
 */
export function validateStatusTransition(
  currentStatus: OrderStatus,
  newStatus: OrderStatus,
  orderId?: string
): void {
  // Handle undefined or invalid statuses before validation
  const safeCurrentStatus = currentStatus || 'pending';
  
  if (!isValidStatusTransition(safeCurrentStatus, newStatus)) {
    const orderContext = orderId ? ` for order ${orderId}` : '';
    const validStatuses = getValidNextStatuses(safeCurrentStatus);
    
    throw new Error(
      `Invalid status transition${orderContext}: cannot change from '${safeCurrentStatus}' to '${newStatus}'. ` +
      `Valid transitions from '${safeCurrentStatus}' are: [${validStatuses.join(', ')}]`
    );
  }
}

/**
 * Checks if an order status is terminal (no further transitions allowed)
 * 
 * @param status - The order status to check
 * @returns true if status is terminal, false otherwise
 */
export function isTerminalStatus(status: OrderStatus): boolean {
  return VALID_TRANSITIONS[status].length === 0;
}

/**
 * Gets a human-readable description of the status transition rules
 * 
 * @returns Object mapping each status to its transition description
 */
export function getStatusTransitionRules(): Record<OrderStatus, string> {
  return {
    pending: 'Order is cooking - Can move to: served (when ready), completed (when paid), cancelled (if cancelled)',
    preparing: 'Legacy status (same as pending) - Can move to: served (when ready), completed (when paid), cancelled (if cancelled)',
    served: 'Food is ready - Can move to: completed (when payment processed), cancelled (if order is cancelled)',
    completed: 'Terminal state - order is finished and paid',
    cancelled: 'Terminal state - order was cancelled'
  };
}