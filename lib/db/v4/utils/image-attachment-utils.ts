"use client";

export interface CompressedImageResult {
  data: string; // base64 encoded
  size: number; // in bytes
  width: number;
  height: number;
  format: 'jpeg' | 'png' | 'webp';
}

export async function compressImage(
  file: File | string,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
  } = {}
): Promise<CompressedImageResult> {
  const {
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 0.8,
    format = 'jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      const { width: originalWidth, height: originalHeight } = img;
      
      let { width, height } = calculateDimensions(
        originalWidth,
        originalHeight,
        maxWidth,
        maxHeight
      );

      canvas.width = width;
      canvas.height = height;

      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width, height);
      ctx.drawImage(img, 0, 0, width, height);

      const mimeType = `image/${format}`;
      const base64Data = canvas.toDataURL(mimeType, quality);
      
      const sizeInBytes = Math.round((base64Data.length * 3) / 4);

      resolve({
        data: base64Data.split(',')[1], // Remove data:image/jpeg;base64, prefix
        size: sizeInBytes,
        width,
        height,
        format
      });
    };

    img.onerror = () => reject(new Error('Failed to load image'));

    if (typeof file === 'string') {
      img.src = file;
    } else {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          img.src = e.target.result as string;
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    }
  });
}

function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  let width = originalWidth;
  let height = originalHeight;

  if (width > maxWidth) {
    height = (height * maxWidth) / width;
    width = maxWidth;
  }

  if (height > maxHeight) {
    width = (width * maxHeight) / height;
    height = maxHeight;
  }

  return { width: Math.round(width), height: Math.round(height) };
}

export async function addImageAttachment(
  docId: string,
  imageFile: File | string,
  filename: string = 'receipt.jpg',
  compressionOptions?: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
  }
): Promise<void> {
  try {
    console.log(`[addImageAttachment] Adding attachment for docId: ${docId}, filename: ${filename}`);
    const compressed = await compressImage(imageFile, compressionOptions);
    console.log(`[addImageAttachment] Compressed image: ${compressed.size} bytes, ${compressed.width}x${compressed.height}`);
    
    if (typeof window !== 'undefined' && (window as any).electronAPI?.database?.putAttachment) {
      // Get current restaurant ID for proper database identifier
      const restaurantId = localStorage.getItem('currentRestaurantId') || 'resto-2b28a072-d490-40e6-b447-0b56e5610249';
      const dbIdentifier = `inventory-${restaurantId}`;
      console.log(`[addImageAttachment] Using database identifier: ${dbIdentifier}`);
      
      const result = await (window as any).electronAPI.database.putAttachment(
        dbIdentifier,
        docId,
        filename,
        compressed.data,
        `image/${compressed.format}`
      );
      console.log(`[addImageAttachment] Success result:`, result);
    } else {
      throw new Error('Electron attachment API not available');
    }
  } catch (error) {
    console.error('[addImageAttachment] Error adding image attachment:', error);
    throw error;
  }
}

export async function getImageAttachment(
  docId: string,
  filename: string = 'receipt.jpg'
): Promise<string | null> {
  try {
    console.log(`[getImageAttachment] Getting attachment for docId: ${docId}, filename: ${filename}`);
    
    if (typeof window !== 'undefined' && (window as any).electronAPI?.database?.getAttachment) {
      // Get current restaurant ID for proper database identifier
      const restaurantId = localStorage.getItem('currentRestaurantId') || 'resto-2b28a072-d490-40e6-b447-0b56e5610249';
      const dbIdentifier = `inventory-${restaurantId}`;
      console.log(`[getImageAttachment] Using database identifier: ${dbIdentifier}`);
      
      console.log(`[getImageAttachment] Calling electronAPI.database.getAttachment with:`, {
        dbIdentifier,
        docId,
        filename
      });
      
      const attachment = await (window as any).electronAPI.database.getAttachment(
        dbIdentifier,
        docId,
        filename
      );
      console.log(`[getImageAttachment] Raw attachment response:`, attachment);
      
      if (attachment && attachment.data) {
        const dataUrl = `data:${attachment.content_type};base64,${attachment.data}`;
        console.log(`[getImageAttachment] Success - returning data URL of length: ${dataUrl.length}`);
        return dataUrl;
      }
    }
    console.log(`[getImageAttachment] No attachment found or API not available`);
    return null;
  } catch (error) {
    console.error('[getImageAttachment] Error getting image attachment:', error);
    return null;
  }
}

export async function removeImageAttachment(
  docId: string,
  filename: string = 'receipt.jpg'
): Promise<void> {
  try {
    if (typeof window !== 'undefined' && (window as any).electronAPI?.database?.removeAttachment) {
      await (window as any).electronAPI.database.removeAttachment(
        'inventory',
        docId,
        filename
      );
    } else {
      throw new Error('Electron attachment API not available');
    }
  } catch (error) {
    console.error('Error removing image attachment:', error);
    throw error;
  }
}

export async function hasImageAttachment(
  docId: string,
  filename: string = 'receipt.jpg'
): Promise<boolean> {
  try {
    console.log(`[hasImageAttachment] Checking attachment for docId: ${docId}, filename: ${filename}`);
    
    if (typeof window !== 'undefined' && (window as any).electronAPI?.database?.getAttachment) {
      // Get current restaurant ID for proper database identifier
      const restaurantId = localStorage.getItem('currentRestaurantId') || 'resto-2b28a072-d490-40e6-b447-0b56e5610249';
      const dbIdentifier = `inventory-${restaurantId}`;
      console.log(`[hasImageAttachment] Using database identifier: ${dbIdentifier}`);
      
      const attachment = await (window as any).electronAPI.database.getAttachment(
        dbIdentifier,
        docId,
        filename
      );
      const hasAttachment = attachment !== null;
      console.log(`[hasImageAttachment] Has attachment: ${hasAttachment}`);
      return hasAttachment;
    }
    console.log(`[hasImageAttachment] API not available, returning false`);
    return false;
  } catch (error) {
    console.error('[hasImageAttachment] Error checking image attachment:', error);
    return false;
  }
}

export function getAttachmentFilename(purchaseId: string, format: string = 'jpg'): string {
  return `receipt_${purchaseId.replace(/[^a-zA-Z0-9]/g, '_')}.${format}`;
}