/**
 * Stock Consumption Tracker
 * 
 * Prevents double stock consumption by tracking which orders have already
 * had their stock consumed.
 */

interface ConsumptionRecord {
  orderId: string;
  consumedAt: string;
  consumedBy: string;
  method: 'new_system' | 'legacy_system';
}

class StockConsumptionTracker {
  private consumedOrders = new Set<string>();
  private consumptionLog: ConsumptionRecord[] = [];

  /**
   * Check if an order has already had its stock consumed
   */
  isAlreadyConsumed(orderId: string): boolean {
    return this.consumedOrders.has(orderId);
  }

  /**
   * Mark an order as having its stock consumed
   */
  markAsConsumed(orderId: string, method: 'new_system' | 'legacy_system' = 'new_system'): void {
    if (this.consumedOrders.has(orderId)) {
      console.warn(`[StockConsumptionTracker] Order ${orderId} already marked as consumed, skipping duplicate`);
      return;
    }

    this.consumedOrders.add(orderId);
    this.consumptionLog.push({
      orderId,
      consumedAt: new Date().toISOString(),
      consumedBy: method,
      method
    });

    console.log(`[StockConsumptionTracker] Marked order ${orderId} as consumed via ${method}`);
  }

  /**
   * Get consumption log for debugging
   */
  getConsumptionLog(): ConsumptionRecord[] {
    return [...this.consumptionLog];
  }

  /**
   * Clear old consumption records (keep last 1000)
   */
  cleanup(): void {
    if (this.consumptionLog.length > 1000) {
      const toRemove = this.consumptionLog.splice(0, this.consumptionLog.length - 1000);
      toRemove.forEach(record => {
        this.consumedOrders.delete(record.orderId);
      });
      console.log(`[StockConsumptionTracker] Cleaned up ${toRemove.length} old consumption records`);
    }
  }

  /**
   * Reset tracker (for testing)
   */
  reset(): void {
    this.consumedOrders.clear();
    this.consumptionLog = [];
  }
}

// Global instance
export const stockConsumptionTracker = new StockConsumptionTracker();

// Cleanup every hour
if (typeof window !== 'undefined') {
  setInterval(() => {
    stockConsumptionTracker.cleanup();
  }, 60 * 60 * 1000); // 1 hour
}

/**
 * Safe stock consumption wrapper that prevents double consumption
 */
export async function safeConsumeOrderStock(
  orderId: string,
  consumptionFn: () => Promise<void>,
  method: 'new_system' | 'legacy_system' = 'new_system'
): Promise<boolean> {
  if (stockConsumptionTracker.isAlreadyConsumed(orderId)) {
    console.log(`[safeConsumeOrderStock] Order ${orderId} stock already consumed, skipping`);
    return false;
  }

  try {
    await consumptionFn();
    stockConsumptionTracker.markAsConsumed(orderId, method);
    return true;
  } catch (error) {
    console.error(`[safeConsumeOrderStock] Error consuming stock for order ${orderId}:`, error);
    throw error;
  }
}