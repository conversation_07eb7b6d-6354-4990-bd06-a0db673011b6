/**
 * 🚀 Database Debug and Monitoring Utilities
 * 
 * Use these utilities to monitor database health and troubleshoot issues
 */

import { databaseV4 } from '../core/db-instance';

type HealthStatus = 'healthy' | 'degraded' | 'error';

/**
 * Comprehensive database health check
 */
export async function performDatabaseHealthCheck(): Promise<{
  status: HealthStatus;
  details: any;
  recommendations: string[];
}> {
  const results = {
    status: 'healthy' as HealthStatus,
    details: {} as any,
    recommendations: [] as string[]
  };

  try {
    // Get basic health status
    const healthStatus = databaseV4.getHealthStatus();
    results.details.healthStatus = healthStatus;

    // Check if initialized
    if (!healthStatus.isInitialized) {
      results.status = 'error';
      results.recommendations.push('Database is not initialized. Call databaseV4.initialize(restaurantId) first.');
      return results;
    }

    // Check connectivity
    const connectivity = await databaseV4.checkConnectivity();
    results.details.connectivity = connectivity;

    if (!connectivity.success) {
      results.status = 'error';
      results.recommendations.push(`Database connectivity failed: ${connectivity.details.error}`);
      return results;
    }

    // Check if there are any health issues
    if (healthStatus.healthInfo && healthStatus.healthInfo.errors?.length > 0) {
      results.status = 'degraded';
      results.recommendations.push('Recent errors detected in database operations. Check the healthInfo.errors for details.');
    }

    // Test basic operations
    try {
      const testResult = await testBasicOperations();
      results.details.basicOperations = testResult;
      
      if (!testResult.success) {
        results.status = 'degraded';
        results.recommendations.push(`Basic operations test failed: ${testResult.error}`);
      }
    } catch (operationError) {
      results.status = 'degraded';
      results.recommendations.push(`Failed to test basic operations: ${operationError}`);
    }

    if (results.status === 'healthy') {
      results.recommendations.push('Database is operating normally.');
    }

  } catch (error) {
    results.status = 'error';
    results.details.criticalError = error instanceof Error ? error.message : String(error);
    results.recommendations.push('Critical error during health check. Check database configuration.');
  }

  return results;
}

/**
 * Test basic database operations
 */
async function testBasicOperations(): Promise<{ success: boolean; error?: string; timings?: any }> {
  const timings = {} as any;
  
  try {
    // Test document creation
    const start = Date.now();
    const testDoc = {
      _id: `__test_document_${Date.now()}`,
      type: 'test_document',
      created: new Date().toISOString(),
      test: true
    };
    
    const createStart = Date.now();
    await databaseV4.putDoc(testDoc);
    timings.create = Date.now() - createStart;

    // Test document retrieval
    const getStart = Date.now();
    const retrieved = await databaseV4.getDoc(testDoc._id);
    timings.get = Date.now() - getStart;

    // Test document update
    const updateStart = Date.now();
    if (retrieved && typeof retrieved === 'object') {
      const updated = { ...retrieved, updated: new Date().toISOString() };
      await databaseV4.putDoc(updated);
    }
    timings.update = Date.now() - updateStart;

    // Test document deletion
    const deleteStart = Date.now();
    const toDelete = await databaseV4.getDoc<{ _id: string; _rev: string }>(testDoc._id);
    if (toDelete && toDelete._rev) {
      await databaseV4.deleteDoc(testDoc._id, toDelete._rev);
    }
    timings.delete = Date.now() - deleteStart;

    timings.total = Date.now() - start;

    return { success: true, timings };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error),
      timings 
    };
  }
}

/**
 * Get formatted database status for logging
 */
export function getFormattedDatabaseStatus(): string {
  const status = databaseV4.getHealthStatus();
  
  const lines = [
    '🗄️ Database Status Report',
    '========================',
    `📊 Initialized: ${status.isInitialized ? '✅' : '❌'}`,
    `🏪 Restaurant: ${status.currentRestaurantId || 'None'}`,
    `💽 Database: ${status.dbIdentifier || 'None'}`,
    `🖥️ Mode: ${status.isElectron ? 'Electron' : 'Browser'}`,
    `⏰ Last Check: ${status.timestamp}`,
  ];

  if (status.healthInfo) {
    lines.push('', '📈 Health Info:');
    lines.push(`   Status: ${status.healthInfo.status}`);
    lines.push(`   Init Attempts: ${status.healthInfo.initAttempts}`);
    lines.push(`   Last Successful: ${new Date(status.healthInfo.lastSuccessfulInit).toLocaleString()}`);
    
    if (status.healthInfo.errors?.length > 0) {
      lines.push(`   Recent Errors: ${status.healthInfo.errors.length}`);
      status.healthInfo.errors.forEach((err: any, i: number) => {
        lines.push(`     ${i + 1}. ${new Date(err.timestamp).toLocaleTimeString()}: ${err.error}`);
      });
    }
  }

  return lines.join('\n');
}

/**
 * Console commands for debugging (attach to window in development)
 */
export const dbDebugCommands = {
  status: () => {
    console.log(getFormattedDatabaseStatus());
    return databaseV4.getHealthStatus();
  },
  
  healthCheck: async () => {
    console.log('🔍 Performing comprehensive database health check...');
    const result = await performDatabaseHealthCheck();
    console.log(`📊 Health Status: ${result.status}`);
    console.log('📋 Recommendations:');
    result.recommendations.forEach((rec, i) => {
      console.log(`   ${i + 1}. ${rec}`);
    });
    return result;
  },
  
  connectivity: async () => {
    console.log('🔗 Testing database connectivity...');
    const result = await databaseV4.checkConnectivity();
    console.log(result.success ? '✅ Connected' : '❌ Connection Failed');
    console.log('Details:', result.details);
    return result;
  },
  
  help: () => {
    console.log(`
🚀 Database Debug Commands:
  dbDebugCommands.status()       - Get current database status
  dbDebugCommands.healthCheck()  - Perform comprehensive health check  
  dbDebugCommands.connectivity() - Test database connectivity
  dbDebugCommands.help()         - Show this help
    `);
  }
};

// Attach to window in development for easy console access
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).dbDebugCommands = dbDebugCommands;
  console.log('🚀 Database debug commands available as window.dbDebugCommands');
} 