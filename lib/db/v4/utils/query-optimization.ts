/**
 * Database Query Optimization Utilities
 * 
 * This module provides optimized query patterns to reduce memory usage
 * and improve performance by filtering at the database level.
 */

import { databaseV4 } from '../core/db-instance';
import { OrderDocument } from '../schemas/order-schema';

/**
 * Get orders with optimized filtering at database level
 * 
 * @param filters - Query filters to apply
 * @param limit - Maximum number of results (default: 1000)
 * @param sortBy - Field to sort by (default: 'createdAt')
 * @param sortOrder - Sort order (default: 'desc')
 * @returns Promise<OrderDocument[]>
 */
export async function getOrdersOptimized(filters: {
  status?: OrderDocument['status'] | OrderDocument['status'][];
  orderType?: string;
  paymentStatus?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  tableId?: string;
} = {}, options: {
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
} = {}): Promise<OrderDocument[]> {
  
  // Android-specific optimizations: reduce default limit for mobile
  const isAndroid = typeof window !== 'undefined' && 
    (/Android/i.test(navigator.userAgent) || !!(window as any).Capacitor);
  
  const {
    limit = isAndroid ? 500 : 1000, // Reduced limit for Android
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = options;

  try {
    // Build selector dynamically based on filters
    const selector: any = {
      type: 'order_document'
    };

    // Add status filter
    if (filters.status) {
      if (Array.isArray(filters.status)) {
        selector.status = { $in: filters.status };
      } else {
        selector.status = filters.status;
      }
    }

    // Add order type filter
    if (filters.orderType) {
      selector.orderType = filters.orderType;
    }

    // Add payment status filter
    if (filters.paymentStatus) {
      selector.paymentStatus = filters.paymentStatus;
    }

    // Add date range filter
    if (filters.dateRange) {
      selector.createdAt = {
        $gte: filters.dateRange.start,
        $lte: filters.dateRange.end
      };
    }

    // Add table ID filter
    if (filters.tableId) {
      selector.tableId = filters.tableId;
    }

    console.log('[getOrdersOptimized] Query selector:', JSON.stringify(selector));
    if (isAndroid) {
      console.log('🤖 [getOrdersOptimized] Android optimizations applied, limit:', limit);
    }

    // Execute optimized query with Android-safe error handling
    const result = await databaseV4.findDocs<OrderDocument>({
      selector,
      limit,
      sort: [{ [sortBy]: sortOrder }]
    });

    console.log(`[getOrdersOptimized] Found ${result.docs.length} orders (filtered at DB level)`);
    
    // Android-specific: validate results
    if (isAndroid && result.docs) {
      const validDocs = result.docs.filter(doc => doc && typeof doc === 'object' && doc._id);
      if (validDocs.length !== result.docs.length) {
        console.warn(`🤖 [getOrdersOptimized] Android: Filtered out ${result.docs.length - validDocs.length} invalid documents`);
      }
      return validDocs;
    }
    
    return result.docs;

  } catch (error) {
    console.error('[getOrdersOptimized] Error:', error);
    
    // Android-specific error handling
    if (isAndroid) {
      console.error('🤖 [getOrdersOptimized] Android error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
    }
    
    throw error;
  }
}

/**
 * Get active orders (optimized)
 * Only returns orders that are not completed or cancelled
 */
export async function getActiveOrdersOptimized(): Promise<OrderDocument[]> {
  // Android-specific optimizations: further reduce limit for active orders
  const isAndroid = typeof window !== 'undefined' && 
    (/Android/i.test(navigator.userAgent) || !!(window as any).Capacitor);
  
  return getOrdersOptimized({
    status: ['pending', 'preparing', 'served']
  }, {
    limit: isAndroid ? 200 : 500 // Further reduced limit for Android
  });
}

/**
 * Get today's orders (optimized)
 * Returns orders created today based on business day logic
 */
export async function getTodaysOrdersOptimized(): Promise<OrderDocument[]> {
  const now = new Date();
  const startOfDay = new Date(now);
  startOfDay.setHours(5, 0, 0, 0); // 5 AM start of business day
  
  // If current time is before 5 AM, get yesterday's orders
  if (now.getHours() < 5) {
    startOfDay.setDate(startOfDay.getDate() - 1);
  }

  const endOfDay = new Date(startOfDay);
  endOfDay.setDate(endOfDay.getDate() + 1);
  endOfDay.setHours(4, 59, 59, 999); // End at 4:59 AM next day

  return getOrdersOptimized({
    dateRange: {
      start: startOfDay.toISOString(),
      end: endOfDay.toISOString()
    }
  });
}

/**
 * Get orders by table (optimized)
 * Returns orders for a specific table
 */
export async function getOrdersByTableOptimized(tableId: string): Promise<OrderDocument[]> {
  return getOrdersOptimized({
    tableId
  }, {
    limit: 100 // Table orders should be limited
  });
}

/**
 * Get paid orders in date range (optimized)
 * Used for financial reporting
 */
export async function getPaidOrdersInRangeOptimized(
  startDate: string, 
  endDate: string
): Promise<OrderDocument[]> {
  return getOrdersOptimized({
    paymentStatus: 'paid',
    dateRange: {
      start: startDate,
      end: endDate
    }
  });
}