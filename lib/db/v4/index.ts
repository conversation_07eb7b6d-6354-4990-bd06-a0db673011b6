"use client";

/**
 * V4 Database Implementation
 *
 * Main exports for the v4 database implementation.
 */

// Import necessary functions and types
import { DatabaseV4 } from './core/db-instance';

// Initialize and export the database instance
export const databaseV4 = new DatabaseV4();

// Export auth types (these are still valid)
export type { AuthUser, Restaurant } from './schemas/auth-schema';
export { DEFAULT_ADMIN_PERMISSIONS } from './schemas/auth-schema';

// Export new payment system database operations
export {
  createNewPaymentIndexes,
  createStaffBalance,
  getStaffBalance,
  updateStaffBalance,
  deleteStaffBalance,
  getStaffBalances,
  getUnusedStaffBalances,
  getStaffBalancesByType,
  getPaymentSnapshot,
  getStaffPaymentSnapshots,
  getPaymentSnapshotsByDateRange,
  updatePaymentSnapshot,
  deletePaymentSnapshot
} from './operations/new-payment-ops';

// Export new payment system schemas and types
export type {
  StaffBalanceDocument,
  PaymentSnapshotDocument,
  BalanceType,
  BalanceSummary,
  PaymentCalculation
} from './schemas/new-payment-schemas';

// Export new payment system service functions
export {
  addAdvance,
  addDeduction,
  addBonus,
  getAllBalances,
  getUnusedBalances,
  getBalancesByType,
  getBalancesByDateRange,
  updateBalance,
  deleteBalance,
  addMultipleBalances,
  getDetailedBalances,
  calculatePaymentAmounts,
  calculatePaymentWithStrategy,
  previewPayment,
  createPaymentSnapshot,
  createPaymentSnapshotWithStrategy,
  getPaymentHistory,
  getPaymentAnalytics,
  compareStaffPayments,
  markBalancesAsUsed
} from '../../services/new-staff-balance-service';

// Export payment strategy types
export type { PaymentStrategy } from '../../services/new-staff-balance-service';

// Export staff functionality - NEW PER-STAFF APPROACH
export {
  createPerStaffIndexes,
  getAllStaff,
  getStaffMember,
  addStaffMember,
  updateStaffMember,
  deleteStaffMember,
  getStaffSchedule,
  setStaffSchedule,
  deleteStaffSchedule,
  getStaffAttendance,
  recordStaffAttendance,
  getStaffAttendanceRecords,
  deleteStaffAttendance,
  markAttendanceAsPaid,
  // Payment operations
  createPayment,
  getStaffPayments,
  getStaffPaymentsByDateRange,
  getStaffPaymentsByPeriod,
  getStaffAdvanceBalance,
  calculateUnpaidBalance,
  getPeriodPaymentStatus,
  updatePaymentStatus,
  updatePaymentNote,
  updatePaymentMetadata,
  deletePayment,
  DEFAULT_STAFF_PERMISSIONS,
  DEFAULT_ADMIN_STAFF_PERMISSIONS
} from './operations/per-staff-ops';

// Export staff types - NEW PER-STAFF SCHEMAS
export type {
  StaffDocument,
  ScheduleDocument,
  AttendanceDocument,
  AttendanceRecord,
  PaymentDocument
} from './schemas/per-staff-schemas';

// Export menu operations
export {
  getMenu,
  updateMenu,
  addCategory,
  updateCategory,
  removeCategory,
  addItemToCategory,
  updateMenuItem,
  removeMenuItem,
  addSizeToCategory,
  renameSizeInCategory,
  deleteSizeFromCategory,
} from './operations/menu-ops';

// Aliases for backward compatibility
export {
  removeCategory as deleteCategory,
  addItemToCategory as addMenuItem,
  removeMenuItem as deleteMenuItem,
} from './operations/menu-ops';

// Export menu types
export type {
  MenuDocument,
  MenuCategory,
  MenuItem,
} from './schemas/menu-schema';
export { DEFAULT_MENU_DOCUMENT } from './schemas/menu-schema';

// Export seed data functionality
export {
  initializeUniversalSeedData,
  checkSeedDataExists,
  clearSeedData
} from './seed-data/seed-data-service';
export type { SeedDataOptions, SeedDataResult } from './seed-data/seed-data-service';

// Export order functionality
export {
  createOrderIndexes,
  createOrder,
  getOrder,
  getAllOrders,
  getActiveOrders,
  getOrdersByStatus,
  getOrdersByTable,
  getOrdersByDateRange,
  updateOrder,
  updateOrderStatus,
  addItemToOrder,
  updateOrderItem,
  removeItemFromOrder,
  updateOrderCustomer,
  updateOrderDeliveryPerson,
  deleteOrder,
  purgeOldFormatOrders
} from './operations/order-ops';

// Export inventory log functionality - removed legacy inventory log ops

// Export sales aggregation functionality
export {
  createAggregationIndexes
} from './operations/sales-aggregation-ops';

// Import functions for internal use
import { createOrderIndexes } from './operations/order-ops';
import { createAggregationIndexes } from './operations/sales-aggregation-ops';
import { createMenuIndexes } from './operations/menu-ops';
import { getMenu } from './operations/menu-ops';
import { initializeUniversalSeedData, checkSeedDataExists } from './seed-data/seed-data-service';

// Export order types
export type {
  OrderDocument,
  OrderItem,
  OrderAddon,
  Customer,
  DeliveryPerson,
  PaymentDetails,
  Order
} from './schemas/order-schema';
export { DEFAULT_ORDER_DOCUMENT } from './schemas/order-schema';

// knowledge: export table functionality
export {
  createTableIndexes,
  getAllTables,
  getTable,
  createTable,
  updateTable,
  deleteTable,
  updateTableStatus
} from './operations/table-ops';

// knowledge: export table types
export type {
  Table,
  TableDocument
} from './schemas/table-schema';
export { DEFAULT_TABLE_DOCUMENT } from './schemas/table-schema';

// Inventory functionality
export {
  getInventory,
  updateInventory,
  addStockItem,
  updateStockItem,
  deleteStockItem,
  addStockAdjustment,
  updateStockAdjustment,
  deleteStockAdjustment,
  addPurchaseLog,
  updatePurchaseLog,
  deletePurchaseLog,
  addStockCount,
  updateStockCount,
  deleteStockCount,
  addStockCountItem,
  updateStockCountItem,
  deleteStockCountItem,
  addWasteLog,
  updateWasteLog,
  deleteWasteLog
} from './operations/inventory-ops';
export type {
  InventoryDocument,
  StockItem,
  StockAdjustment,
  PurchaseLog,
  StockCount,
  StockCountItem,
  WasteLog
} from './schemas/inventory-schema';
export { DEFAULT_INVENTORY_DOCUMENT } from './schemas/inventory-schema';

// Packaging functionality
export {
  processPackagingConsumption,
  processOrderPackagingConsumption,
  updateTablePackaging,
  updateCategoryPackaging,
  getTablePackaging,
  getCategoryPackaging,
  demoPackagingConsumption
} from './operations/packaging-ops';
export type {
  OrderType,
  OrderItem as PackagingOrderItem,
  PackagingConsumptionOrder,
  PackagingConsumptionResult
} from './operations/packaging-ops';

// Supplier functionality
export {
  getSuppliers,
  updateSuppliers,
  addSupplier,
  updateSupplier,
  deleteSupplier,

} from './operations/supplier-ops';
export type {
  SupplierDocument,
  Supplier,

} from './schemas/supplier-schema';
export { DEFAULT_SUPPLIER_DOCUMENT } from './schemas/supplier-schema';

// Export staff menu functionality
export {
  getStaffMenuConfig,
  updateStaffMenuConfig,
  addStaffMenuItem,
  updateStaffMenuItem,
  removeStaffMenuItem,
  getActiveStaffMenuItems,
  getStaffAllowance,
  markStaffPresent,
  useStaffAllowance,
  canStaffOrder,
  getStaffAllowancesForDate,
  getStaffAllowanceHistory,
  resetAllowancesForNewDay
} from './operations/staff-menu-ops';

export type {
  StaffMenuConfigDocument,
  StaffMenuItem,
  StaffAllowanceDocument
} from './schemas/staff-menu-schema';

// knowledge: export v4 settings operations and types
export {
  getSettings,
  updateSettings,
  ensureDefaultSettings,
  initializeSettings
} from './operations/settings-ops';
export type { RestaurantSettings } from './schemas/restaurant-settings-schema';
export { DEFAULT_RESTAURANT_SETTINGS } from './schemas/restaurant-settings-schema';
// endknowledge

// knowledge: export v4 COGS/production CRUD operations and types
export {
  createSubRecipe,
  getSubRecipe,
  getAllSubRecipes,
  updateSubRecipe,
  deleteSubRecipe
} from './operations/sub-recipe-ops';
export type { SubRecipe } from './schemas/sub-recipe-schema';
export { DEFAULT_SUB_RECIPE } from './schemas/sub-recipe-schema';

export {
  createMenuItemRecipe,
  getMenuItemRecipe,
  getAllMenuItemRecipes,
  updateMenuItemRecipe,
  deleteMenuItemRecipe
} from './operations/menu-item-recipe-ops';
export type { MenuItemRecipe } from './schemas/menu-item-recipe-schema';
export { DEFAULT_MENU_ITEM_RECIPE } from './schemas/menu-item-recipe-schema';

export {
  createProductionBatch,
  getProductionBatch,
  getAllProductionBatches,
  updateProductionBatch,
  deleteProductionBatch
} from './operations/production-batch-ops';
export type { ProductionBatch } from './schemas/production-batch-schema';
export { DEFAULT_PRODUCTION_BATCH } from './schemas/production-batch-schema';
// endknowledge

// knowledge: export v4 cash-ops
export {
  getAllCashTransactions,
  createCashTransaction
} from './operations/cash-ops';

// knowledge: export v4 cash-session-ops (cash counting functions)
export {
  getAllCashCounts,
  createCashCount
} from './operations/cash-session-ops';
// endknowledge

// knowledge: export v4 expense CRUD
export {
  createExpense,
  getAllExpenses,
  updateExpense,
  deleteExpense
} from './operations/expense-ops';
// knowledge: export v4 expense CRUD end

// Export shift operations
export {
  getShifts,
  createShift,
  updateShift,
  deleteShift
} from './operations/shift-ops';

// Export shift types
export type {
  Shift,
  ShiftsDocument
} from './schemas/shifts-schema';
export { DEFAULT_SHIFTS_DOCUMENT } from './schemas/shifts-schema';



// Export queue functionality
export {
  createQueueIndexes,
  addOrderToQueue,
  registerItemCompletion,
  completeItem,
  getStationQueue,
  getAllStationQueues,
  cancelOrder,
  completeOrder,
  getOrderQueuePosition,
  isItemCompleted,
  clearCompletedOrders,
  resetAllQueues
} from './operations/queue-ops';

// Export queue schemas and types
export type {
  QueueItem,
  ItemCompletion,
  StationQueueSummary,
  StationQueue,
  ItemCompletionStatus
} from './schemas/queue-schema';

// Initialize function for v4 database - OPTIMIZED FOR SPEED
export async function initializeV4Database(restaurantId: string): Promise<void> {
  const startTime = Date.now();
  console.log('⚡ [FAST DB Init] Starting for:', restaurantId);

  try {
    const { databaseV4 } = await import('./core/db-instance');

    // Check if already initialized for this restaurant
    if (databaseV4.isInitialized && databaseV4.getCurrentRestaurantId() === restaurantId) {
      console.log('✅ [FAST DB Init] Already initialized, skipping');
      return;
    }

    // Initialize ONLY the core database - no heavy index creation
    await databaseV4.initialize(restaurantId);

    // Initialize settings after database is ready
    try {
      const { initializeSettings } = await import('./operations/settings-ops');
      await initializeSettings();
      console.log('✅ [FAST DB Init] Settings initialized');
    } catch (settingsError) {
      console.warn('⚠️ [FAST DB Init] Settings initialization failed, but continuing:', settingsError);
      // Don't fail the entire initialization if settings fail
    }

    const duration = Date.now() - startTime;
    console.log(`🎉 [FAST DB Init] Completed in ${duration}ms`);

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ [FAST DB Init] Failed after ${duration}ms:`, error);
    throw error;
  }
}

// Create indexes for auth documents (optimized)
export async function createAuthIndexes(): Promise<void> {
  const { databaseV4 } = await import('./core/db-instance');

  try {
    // Create indexes in parallel for better performance
    const indexPromises = [
      databaseV4.createIndex({ index: { fields: ['type', 'email'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'username'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'restaurantId'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'name'] } })
    ];

    await Promise.allSettled(indexPromises);
    console.log('✅ [DB Init] Auth indexes created');
  } catch (error) {
    console.warn('⚠️ [DB Init] Auth indexes creation had issues:', error);
    // Don't throw - these are performance optimizations, not critical
  }
}

// Create indexes for staff documents (optimized)
export async function createStaffIndexes(): Promise<void> {
  const { databaseV4 } = await import('./core/db-instance');

  try {
    // Create indexes in parallel for better performance
    const indexPromises = [
      databaseV4.createIndex({ index: { fields: ['members.id'] } }),
      databaseV4.createIndex({ index: { fields: ['members.name'] } }),
      databaseV4.createIndex({ index: { fields: ['members.role'] } }),
      databaseV4.createIndex({ index: { fields: ['members.status'] } }),
      databaseV4.createIndex({ index: { fields: ['members.userId'] } }),
      databaseV4.createIndex({ index: { fields: ['records.staffId'] } }),
      databaseV4.createIndex({ index: { fields: ['records.date'] } }),
      databaseV4.createIndex({ index: { fields: ['records.isPaid'] } })
    ];

    await Promise.allSettled(indexPromises);
    console.log('✅ [DB Init] Staff indexes created');
  } catch (error) {
    console.warn('⚠️ [DB Init] Staff indexes creation had issues:', error);
    // Don't throw - these are performance optimizations, not critical
  }
}

// Create indexes for freelancer documents (optimized)
export async function createFreelancerIndexes(): Promise<void> {
  const { databaseV4 } = await import('./core/db-instance');

  try {
    // Create indexes in parallel for better performance
    const indexPromises = [
      databaseV4.createIndex({ index: { fields: ['type', 'phone'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'name'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'lastActiveAt'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'createdAt'] } }),
      databaseV4.createIndex({ index: { fields: ['type', 'lastActiveAt', 'createdAt'] } }),
      databaseV4.createIndex({ index: { fields: ['lastActiveAt', 'createdAt'] } })
    ];

    await Promise.allSettled(indexPromises);
    console.log('✅ [DB Init] Freelancer indexes created');
  } catch (error) {
    console.warn('⚠️ [DB Init] Freelancer indexes creation had issues:', error);
    // Don't throw - these are performance optimizations, not critical
  }
}

// Create indexes for menu documents (assuming menu is a single doc 'menu')
// No specific indexes generally needed for a single document lookup by _id.
// If you have complex queries against fields within the menu document, add them here.
// export async function createMenuIndexes(): Promise<void> {
//   // ... existing code ...
// }

/**
 * 🚀 Comprehensive Database Initialization
 * 
 * This function ensures all indexes and default documents are properly created
 * for optimal database performance. Call this after database initialization.
 */
export async function initializeDatabaseV4(): Promise<void> {
  console.log('🚀 [initializeDatabaseV4] Starting comprehensive database initialization...');

  try {
    // Ensure database is initialized first
    if (!databaseV4.isInitialized) {
      console.warn('⚠️ [initializeDatabaseV4] Database not initialized yet, skipping...');
      return;
    }

    // Create all necessary indexes in parallel for better performance
    const indexPromises = [
      // Order indexes
      createOrderIndexes().catch((err: any) => console.warn('⚠️ Order indexes:', err.message)),

      // Sales aggregation indexes
      createAggregationIndexes().catch((err: any) => console.warn('⚠️ Aggregation indexes:', err.message)),

      // Menu indexes
      createMenuIndexes().catch((err: any) => console.warn('⚠️ Menu indexes:', err.message)),

      // Staff indexes
      createStaffIndexes().catch((err: any) => console.warn('⚠️ Staff indexes:', err.message)),

      // Freelancer indexes
      createFreelancerIndexes().catch((err: any) => console.warn('⚠️ Freelancer indexes:', err.message))
    ];

    console.log('🔧 [initializeDatabaseV4] Creating specialized indexes...');
    await Promise.allSettled(indexPromises);

    // Initialize default documents
    console.log('📄 [initializeDatabaseV4] Ensuring default documents exist...');
    await ensureDefaultDocuments();

    console.log('✅ [initializeDatabaseV4] Database initialization completed successfully!');
  } catch (error) {
    console.error('❌ [initializeDatabaseV4] Error during database initialization:', error);
    throw error;
  }
}



/**
 * Ensure all required default documents exist
 * Note: Default documents are now created during database initialization
 * This function is kept for backward compatibility but delegates to the database instance
 */
async function ensureDefaultDocuments(): Promise<void> {
  console.log('📄 [ensureDefaultDocuments] Default documents are created during database initialization');
  // Default documents are now created automatically during database initialization
  // in the DatabaseV4.initializeDefaultDocuments() method to prevent race conditions
}
