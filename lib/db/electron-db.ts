"use client";

/**
 * Helper functions for Electron database integration
 */

/**
 * Check if we're running in an Electron environment
 */
export const isElectronEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;
  return (window as any).IS_DESKTOP_APP === true;
};

// Removed getDbRootPath, ElectronDbProxy, getElectronLevelDBProxy, and getElectronLevelDBInstance
// as they are obsolete with the new PouchDB main process architecture. 