"use client";

import { mainDbInstance } from './v4/core/db-main-instance';

/**
 * 🔧 PouchDB Instance Provider
 * 
 * This file provides a unified interface to access the PouchDB database instance.
 * It bridges the gap between the v4 database system and legacy code that expects
 * a direct PouchDB instance.
 */

/**
 * Get the PouchDB database instance
 * 
 * @returns PouchDB.Database instance from the v4 database system
 * @throws Error if database is not initialized or not available
 */
export function getPouchDB(): PouchDB.Database {
  const db = mainDbInstance.getDatabase();
  
  if (!db) {
    throw new Error('❌ Database not initialized. Please ensure the database is properly set up.');
  }
  
  return db;
}

/**
 * Check if PouchDB is available and initialized
 * 
 * @returns boolean indicating if the database is ready
 */
export function isPouchDBReady(): boolean {
  try {
    const db = mainDbInstance.getDatabase();
    return db !== null;
  } catch {
    return false;
  }
}

/**
 * Get the main database instance (v4)
 * 
 * @returns DatabaseV4 instance for advanced operations
 */
export function getMainDbInstance() {
  return mainDbInstance;
}