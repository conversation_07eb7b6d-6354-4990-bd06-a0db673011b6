/**
 * Database utility functions for working with PouchDB
 * 
 * This replaces the previous CouchDB utilities which are now deprecated.
 */

/**
 * Clean a restaurant ID by removing any prefix
 * 
 * @param id The restaurant ID to clean
 * @returns The cleaned restaurant ID
 */
export function cleanRestaurantId(id: string): string {
  if (!id) return '';
  
  // Store original for debugging
  const originalId = id;
  
  // Handle multiple potential prefix patterns
  // 1. Remove 'restaurant-' or 'restaurant_' prefix (including multiple occurrences)
  // 2. Remove 'restaurant:' prefix that might be used in some IDs
  // 3. Remove 'resto-' or 'resto_' prefix if present
  // 4. Generally clean any non-alphanumeric except dashes and underscores
  
  // First handle the restaurant prefix pattern in various forms
  let cleanedId = id.replace(/^(restaurant[-_:])+/, '');
  
  // Also handle resto prefix
  cleanedId = cleanedId.replace(/^(resto[-_:])+/, '');
  
  // Also handle the case where "restaurant" or "resto" might appear multiple times
  while (cleanedId.match(/^(restaurant|resto)[-_:]/)) {
    cleanedId = cleanedId.replace(/^(restaurant|resto)[-_:]/, '');
  }
  
  // Ensure ID is valid for database use - preserve UUID format
  cleanedId = formatDocId(cleanedId);
  
  // Enhanced logging for debugging sync issues
  console.log(`🔧 [cleanRestaurantId] '${originalId}' -> '${cleanedId}' (${cleanedId.length} chars)`);
  
  return cleanedId;
}

/**
 * Get the database name for a restaurant
 * 
 * @param restaurantId The restaurant ID
 * @returns The database name with 'resto-' prefix
 */
export function getRestaurantDbName(restaurantId: string): string {
  const cleanedId = cleanRestaurantId(restaurantId);
  const dbName = `resto-${cleanedId}`;
  
  // Enhanced logging for debugging sync issues
  console.log(`🗃️ [getRestaurantDbName] Restaurant '${restaurantId}' -> DB name '${dbName}'`);
  
  return dbName;
}

/**
 * Format a document ID to be safe for PouchDB
 * 
 * @param id The document ID to format
 * @returns The formatted document ID
 */
export function formatDocId(id: string): string {
  if (!id) return '';
  // Remove any special characters that could cause issues in PouchDB
  // But preserve UUID format by keeping dashes
  return id.replace(/[^a-zA-Z0-9_-]/g, '');
}

/**
 * Validate that a database name follows expected format
 * 
 * @param dbName The database name to validate
 * @returns true if valid, false otherwise
 */
export function validateDbName(dbName: string): boolean {
  if (!dbName) return false;
  
  // Should start with 'resto-' and contain only valid characters
  const isValid = /^resto-[a-zA-Z0-9_-]+$/.test(dbName);
  
  if (!isValid) {
    console.warn(`⚠️ [validateDbName] Invalid database name format: '${dbName}'`);
  }
  
  return isValid;
}

/**
 * Generate a unique device identifier for sync prevention
 * 
 * @returns A unique device identifier
 */
export function generateDeviceId(): string {
  // Try to get existing device ID from localStorage
  const stored = localStorage.getItem('device_id');
  if (stored) {
    return stored;
  }
  
  // Generate new device ID
  const deviceId = `device-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  localStorage.setItem('device_id', deviceId);
  
  console.log(`🔧 [generateDeviceId] Generated new device ID: ${deviceId}`);
  return deviceId;
}

// Add other common database-related utility functions here if needed. 