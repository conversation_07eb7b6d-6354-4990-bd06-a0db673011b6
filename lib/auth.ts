/**
 * This file previously used next-auth, but has been replaced with a custom JWT auth implementation
 * To use authentication, import from lib/hooks/new-auth instead
 */

import { validateUser, getUserByEmail } from './auth/auth-service';

// Export empty handlers to avoid breaking imports
export const handlers = {};

// These functions are stubs - use the useAuth hook from lib/hooks/new-auth instead
export const auth = async () => null;
export const signIn = async () => false;
export const signOut = async () => {}; 