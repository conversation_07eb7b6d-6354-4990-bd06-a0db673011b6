import { MongoClient, MongoClientOptions, ServerApiVersion } from 'mongodb';

/**
 * Helper to mask credentials in logs
 */
const maskCredentials = (url: string): string => {
  try {
    // Handle SRV and non-SRV URIs
    const isSrvUri = url.includes('+srv') || url.includes('mongodb.net');

    // For standard URIs, use URL parsing
    if (!isSrvUri) {
      try {
        const parsedUrl = new URL(url);
        if (parsedUrl.username || parsedUrl.password) {
          parsedUrl.username = 'USER';
          parsedUrl.password = 'PASSWORD';
        }
        return parsedUrl.toString();
      } catch (parseError) {
        console.warn('[MongoDB] Error parsing URI with URL constructor:', parseError);
      }
    }

    // Fallback to regex replacement for both SRV and standard URIs
    return url.replace(/mongodb(?:[+a-z]*):\/\/[^:]*:[^@]*@/, 'mongodb://USER:PASSWORD@');
  } catch (e) {
    console.warn('[MongoDB] Error in maskCredentials:', e);
    // Ultimate fallback
    return 'mongodb://[MASKED_URI]';
  }
};

// Create and initialize the client promise
let clientPromiseWithTimeout: Promise<MongoClient>;

// Initialize MongoDB client
function initializeMongoClient(): Promise<MongoClient> {
  // Check that the MongoDB URI exists
  if (!process.env.MONGODB_URI) {
    console.error('[MongoDB] CRITICAL: Missing MONGODB_URI environment variable');
    return Promise.reject(new Error('MongoDB URI is not configured. Please set the MONGODB_URI environment variable.'));
  }

  const originalUri = process.env.MONGODB_URI;
  console.log('[MongoDB] Using URI (masked):', maskCredentials(originalUri));

  if (!originalUri.startsWith('mongodb://') && !originalUri.startsWith('mongodb+srv://')) {
    console.error('[MongoDB] CRITICAL: Invalid MongoDB URI format. URI must start with mongodb:// or mongodb+srv://');
    return Promise.reject(new Error('Invalid MongoDB URI format. URI must start with mongodb:// or mongodb+srv://'));
  }

  const options: MongoClientOptions = {
    connectTimeoutMS: 15000,
    socketTimeoutMS: 30000,
    serverSelectionTimeoutMS: 15000,
    maxPoolSize: 10,
    minPoolSize: 1,
    retryWrites: true,
    retryReads: true,
    directConnection: originalUri.includes('directConnection=true'),
    serverApi: {
      version: ServerApiVersion.v1,
      strict: true,
      deprecationErrors: true,
    }
  };
  console.log('[MongoDB] Effective MongoClient options being used:', JSON.stringify(options, null, 2));

  console.log('[MongoDB] Creating MongoDB client instance...');
  let client: MongoClient;
  let clientPromise: Promise<MongoClient>;
  const envType = process.env.NODE_ENV === 'development' ? 'Development' : 'Production';

  if (process.env.NODE_ENV === 'development') {
    const globalWithMongo = global as typeof globalThis & {
      _mongoClientPromise?: Promise<MongoClient>;
    };
    if (!globalWithMongo._mongoClientPromise) {
      client = new MongoClient(originalUri, options);
      console.log(`[MongoDB] ${envType}: Initiating new connection to ${maskCredentials(originalUri)}`);
      globalWithMongo._mongoClientPromise = client.connect().catch(err => {
        console.error(`[MongoDB] ${envType}: Connection attempt failed:`, err);
        if (err.name === 'MongoServerSelectionError' && err.reason) {
          const topologyReason = err.reason as any;
          console.error(`[MongoDB] ${envType}: MongoServerSelectionError Topology type: ${topologyReason.type}`);
          if (topologyReason.servers && typeof topologyReason.servers.values === 'function') {
            try {
              const serverStates = Array.from(topologyReason.servers.values()).map((s: any) => ({ address: s.address, type: s.type, error: s.error ? s.error.message : null }));
              console.error(`[MongoDB] ${envType}: Server states from TopologyDescription:`, JSON.stringify(serverStates, null, 2));
            } catch (e) {
              console.error(`[MongoDB] ${envType}: Error logging server states from TopologyDescription:`, e);
            }
          }
        }
        throw err;
      });
    }
    clientPromise = globalWithMongo._mongoClientPromise;
  } else {
    client = new MongoClient(originalUri, options);
    console.log(`[MongoDB] ${envType}: Initiating new connection to ${maskCredentials(originalUri)}`);
    clientPromise = client.connect().catch(err => {
      console.error(`[MongoDB] ${envType}: Connection attempt failed:`, err);
      if (err.name === 'MongoServerSelectionError' && err.reason) {
        const topologyReason = err.reason as any;
        console.error(`[MongoDB] ${envType}: MongoServerSelectionError Topology type: ${topologyReason.type}`);
        if (topologyReason.servers && typeof topologyReason.servers.values === 'function') {
          try {
            const serverStates = Array.from(topologyReason.servers.values()).map((s: any) => ({ address: s.address, type: s.type, error: s.error ? s.error.message : null }));
            console.error(`[MongoDB] ${envType}: Server states from TopologyDescription:`, JSON.stringify(serverStates, null, 2));
          } catch (e) {
            console.error(`[MongoDB] ${envType}: Error logging server states from TopologyDescription:`, e);
          }
        }
      }
      throw err;
    });
  }

  clientPromise
    .then(connectedClient => {
      console.log(`[MongoDB] Connection to ${connectedClient.db().databaseName} successful.`);
    })
    .catch(err => {
      console.error(`[MongoDB] CRITICAL: Overall connection setup promise rejected. Error: ${err.message}. See previous logs for connection attempt details.`);
    });

  return clientPromise;
}

// Initialize the client promise
clientPromiseWithTimeout = initializeMongoClient();

// Export the promise - it will either be the MongoDB client or a rejected promise with a clear error message
export default clientPromiseWithTimeout;

/**
 * Check if MongoDB is reachable
 * Returns a promise that resolves with a boolean indicating if MongoDB is reachable
 * Will timeout after 5 seconds
 */
export async function isMongoDBReachable(): Promise<{ reachable: boolean; error?: string }> {
  try {
    // Check if navigator is online first (browser-only)
    if (typeof window !== 'undefined' && !navigator.onLine) {
      return { reachable: false, error: 'You are currently offline. Please check your internet connection.' };
    }

    // If MONGODB_URI is not set, MongoDB is not configured
    if (!process.env.MONGODB_URI) {
      return { reachable: false, error: 'MongoDB URI is not configured.' };
    }

    // Create a timeout promise
    const timeoutPromise = new Promise<{ reachable: boolean; error: string }>((resolve) => {
      setTimeout(() => {
        resolve({ reachable: false, error: 'MongoDB connection timed out after 5 seconds' });
      }, 5000);
    });

    // The actual MongoDB check
    const checkMongo = async (): Promise<{ reachable: boolean; error?: string }> => {
      try {
        // Try to get a client
        const client = await clientPromiseWithTimeout;

        // Try a basic admin command to verify connection
        await client.db().admin().ping();

        return { reachable: true };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown MongoDB connection error';
        console.error('[MongoDB] Connection check failed:', errorMessage);

        // Determine if it's an offline-type error
        const isOfflineError =
          errorMessage.includes('timed out') ||
          errorMessage.includes('connect') ||
          errorMessage.includes('network') ||
          errorMessage.includes('ENOTFOUND') ||
          errorMessage.includes('ECONNREFUSED') ||
          errorMessage.includes('offline');

        return {
          reachable: false,
          error: isOfflineError
            ? 'Cannot connect to MongoDB. You may be offline or the database may be unavailable.'
            : `MongoDB connection error: ${errorMessage}`
        };
      }
    };

    // Race between the check and timeout
    return await Promise.race([checkMongo(), timeoutPromise]);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unexpected error checking MongoDB connection';
    console.error('[MongoDB] isMongoDBReachable unexpected error:', errorMessage);
    return { reachable: false, error: errorMessage };
  }
}