// Offline-aware API client for mobile/static builds
import { BUILD_CONFIG, getApiUrl, checkRemoteConnectivity } from '@/lib/build-config';
import { httpClient } from '@/lib/utils/http-client';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  offline?: boolean;
  source?: 'remote' | 'local';
}

class OfflineApiClient {
  private db: any = null;
  private isOnline = true;
  private connectivityChecked = false;

  constructor() {
    this.initializeDb();
  }

  private async initializeDb() {
    if (BUILD_CONFIG.isStatic) {
      // Initialize PouchDB for offline storage
      const PouchDB = (await import('pouchdb')).default;
      
      // Use default adapter (IndexedDB in browser, memory in Electron since we use CouchDB)
      this.db = new PouchDB('bistro-offline');
    }
  }

  private async checkConnectivity() {
    if (this.connectivityChecked) return this.isOnline;
    
    // For web builds, assume always online (they run on server)
    if (!BUILD_CONFIG.isStatic) {
      this.isOnline = true;
      this.connectivityChecked = true;
      return true;
    }

    // For static builds, check remote server connectivity
    try {
      this.isOnline = await checkRemoteConnectivity();
      console.log(`🌐 Remote server ${this.isOnline ? 'reachable' : 'unreachable'}`);
    } catch {
      this.isOnline = false;
    }
    
    this.connectivityChecked = true;
    return this.isOnline;
  }

  // Generic API call with offline fallback
  async apiCall<T>(
    endpoint: string, 
    options: RequestInit = {},
    offlineHandler?: () => Promise<T>
  ): Promise<ApiResponse<T>> {
    // Check connectivity first
    await this.checkConnectivity();
    
    // Get the appropriate API URL
    const apiUrl = getApiUrl(endpoint);
    
    // Try remote API first if online and URL available
    if (apiUrl && this.isOnline) {
      try {
        console.log(`🚀 Calling remote API: ${apiUrl}`);
        
        // Prepare request data
        const requestData = options.body ? JSON.parse(options.body as string) : undefined;
        const method = (options.method || 'GET') as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
        
        // Use platform-aware HTTP client
        const httpResponse = await httpClient.request(apiUrl, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
          body: options.body as string,
          timeout: 10000 // 10 second timeout for remote calls
        });
        
        if (httpResponse.ok) {
          const data = httpResponse.data;
          
          // Save successful response to local storage for offline use
          if (this.db && offlineHandler) {
            this.saveResponseToCache(endpoint, data);
          }
          
          return { 
            success: true, 
            data, 
            source: 'remote' 
          };
        } else {
          throw new Error(`Remote API call failed: ${httpResponse.status}`);
        }
      } catch (error) {
        console.log(`❌ Remote API call to ${endpoint} failed:`, error);
        console.log(`🔄 Falling back to offline mode...`);
        this.isOnline = false; // Mark as offline for subsequent calls
      }
    }

    // Offline fallback
    if (offlineHandler) {
      try {
        const data = await offlineHandler();
        return { 
          success: true, 
          data, 
          offline: true,
          source: 'local'
        };
      } catch (error) {
        return { 
          success: false, 
          error: `Offline operation failed: ${error}`,
          offline: true,
          source: 'local'
        };
      }
    }

    return { 
      success: false, 
      error: `No connection to remote server and no offline handler available`,
      offline: true,
      source: 'local'
    };
  }

  // Save API response to local cache
  private async saveResponseToCache(endpoint: string, data: any) {
    if (this.db) {
      try {
        await this.db.put({
          _id: `cache_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
          endpoint,
          data,
          _cached: true,
          _timestamp: Date.now()
        });
      } catch (error) {
        console.warn('Failed to cache response:', error);
      }
    }
  }

  // Staff authentication with remote server when online
  async authenticateStaff(credentials: { username: string; password: string; restaurantId: string }) {
    return this.apiCall(
      'staff/auth/login',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      },
      async () => {
        // Offline authentication using local storage
        const offlineStaff = localStorage.getItem('offline_staff');
        if (offlineStaff) {
          const staff = JSON.parse(offlineStaff);
          if (staff.username === credentials.username) {
            return {
              user: staff,
              token: 'offline-token',
              offline: true
            };
          }
        }
        throw new Error('Invalid offline credentials - try going online to authenticate');
      }
    );
  }

  // Get staff data from remote server when online
  async getStaff(restaurantId: string) {
    return this.apiCall(
      `staff?restaurantId=${restaurantId}`,
      { method: 'GET' },
      async () => {
        if (this.db) {
          const result = await this.db.allDocs({
            include_docs: true,
            startkey: 'staff_',
            endkey: 'staff_\ufff0'
          });
          return result.rows.map((row: any) => row.doc);
        }
        return [];
      }
    );
  }

  // Force connectivity recheck
  async recheckConnectivity() {
    this.connectivityChecked = false;
    return await this.checkConnectivity();
  }

  // Save data locally for offline access
  async saveOfflineData(type: string, id: string, data: any) {
    if (this.db) {
      try {
        await this.db.put({
          _id: `${type}_${id}`,
          ...data,
          _offline: true,
          _timestamp: Date.now()
        });
        return true;
      } catch (error) {
        console.error('Failed to save offline data:', error);
        return false;
      }
    }
    return false;
  }

  // Get offline data
  async getOfflineData(type: string, id?: string) {
    if (this.db) {
      try {
        if (id) {
          const doc = await this.db.get(`${type}_${id}`);
          return doc;
        } else {
          const result = await this.db.allDocs({
            include_docs: true,
            startkey: `${type}_`,
            endkey: `${type}_\ufff0`
          });
          return result.rows.map((row: any) => row.doc);
        }
      } catch (error) {
        console.error('Failed to get offline data:', error);
        return null;
      }
    }
    return null;
  }

  // Check current connectivity status
  get online() {
    return this.isOnline;
  }

  get offline() {
    return !this.isOnline;
  }
}

// Export singleton instance
export const offlineApi = new OfflineApiClient();

// Helper hook for components
export function useOfflineApi() {
  return offlineApi;
}