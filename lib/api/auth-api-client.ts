/**
 * Client for interacting with the auth database/services
 */
export const authApiClient = {
  /**
   * Get a user document directly from the auth database by user ID
   */
  async getUserDoc(userId: string, restaurantId: string) {
    try {
      // Make API call to get the user document
      const response = await fetch(`/api/auth/user/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`Failed to get user document for ${userId}`, {
          status: response.status,
        });
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error(`Error fetching user document for ${userId}:`, error);
      return null;
    }
  },

  // The updateUserPermissions method has been removed as permissions are now stored in the restaurant database
};