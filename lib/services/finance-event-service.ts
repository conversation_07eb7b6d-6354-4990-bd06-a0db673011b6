'use client';

import { getAllCashTransactions, getAllOrders } from '@/lib/db/v4';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';
import { FinanceEvent } from '@/app/components/finance/EventBasedFinanceHistory';

/**
 * Get all collection events from database
 */
async function getCollectionEvents(): Promise<any[]> {
  try {
    console.log('[getCollectionEvents] Fetching collection events...');
    
    const result = await databaseV4.findDocs({
      selector: {
        type: 'collection_event'
      }
    });

    console.log('[getCollectionEvents] Found collection events:', result.docs?.length || 0);
    
    if (result.docs?.length) {
      console.log('[getCollectionEvents] Sample collection event:', result.docs[0]);
    }

    return (result.docs || []).sort((a: any, b: any) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  } catch (error) {
    console.error('[getCollectionEvents] Error:', error);
    return [];
  }
}

/**
 * Service to convert current transaction data to event-based finance history
 */

export async function getFinanceEvents(): Promise<FinanceEvent[]> {
  const [cashTransactions, orders, collectionEvents] = await Promise.all([
    getAllCashTransactions(),
    getAllOrders(),
    getCollectionEvents()
  ]);

  const events: FinanceEvent[] = [];

  // 1. Process collection events FIRST (they are events, not transactions)
  for (const collectionEvent of collectionEvents) {
    events.push({
      id: collectionEvent._id,
      type: 'collection',
      timestamp: collectionEvent.timestamp,
      netAmount: collectionEvent.netAmountForRestaurant, // What actually went to drawer
      performedBy: collectionEvent.collectedBy,
      description: `Collection - ${collectionEvent.driverName} (${collectionEvent.orderCount} orders)`,
      collection: {
        driverName: collectionEvent.driverName,
        orderCount: collectionEvent.orderCount,
        orders: collectionEvent.orders.map(order => ({
          orderId: order.orderId,
          amount: order.expectedAmount,
          customer: order.customer,
          status: 'collected' // All orders in completed collection are collected
        })),
        grossAmount: collectionEvent.actualAmount,
        driverFee: collectionEvent.freelancerTariff || 0,
        netAmount: collectionEvent.netAmountForRestaurant
      }
    });
  }

  // No mock data - collections will appear when processed through enhanced collection system

  // 2. Process all completed orders as sales events
  const completedOrders = orders.filter(order => 
    order.status === 'completed' && order.paymentStatus === 'paid'
  );

  for (const order of completedOrders) {
    events.push({
      id: `sale_${order._id}`,
      type: 'sale',
      timestamp: order.completedAt || order.updatedAt,
      netAmount: order.total,
      performedBy: order.createdByName || 'System',
      description: `Sale - ${order.customer?.name || 'Customer'}`,
      sale: {
        orderId: order._id,
        orderType: order.orderType,
        customer: order.customer?.name || order.customer?.phone || 'Customer',
        tableId: order.tableId,
        items: order.items?.map(item => item.name).join(', ') || 'Items',
        paymentMethod: order.paymentMethod || 'cash',
        // Only add collection status for delivery orders
        collectionStatus: order.orderType === 'delivery' 
          ? getCollectionStatus(order) 
          : undefined
      }
    });
  }

  // Remove all old delivery_collection transaction handling - now using collection events

  // 3. Process manual transactions (excluding old collection transactions)
  const manualTransactions = cashTransactions.filter(tx => 
    (tx.transactionType === 'manual_in' || tx.transactionType === 'manual_out') &&
    tx.metadata?.transactionCategory !== 'delivery_collection' && // Old system
    tx.metadata?.transactionCategory !== 'collection_cash_in' // New system net transactions
  );

  for (const tx of manualTransactions) {
    events.push(convertToManualEvent(tx));
  }

  return events.sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
}

function getCollectionStatus(order: any): 'not_collected' | 'collected' | 'partial' | 'failed' {
  if (!order.collectionStatus) return 'not_collected';
  
  if (order.collectionStatus.isPending === false) {
    // Check if fully collected
    if (order.collectionStatus.actualAmount >= order.collectionStatus.expectedAmount) {
      return 'collected';
    } else if (order.collectionStatus.actualAmount > 0) {
      return 'partial';
    } else {
      return 'failed';
    }
  }
  
  return 'not_collected';
}


// Old collection conversion functions removed - now using collection events directly

function convertToManualEvent(transaction: any): FinanceEvent {
  return {
    id: transaction._id,
    type: transaction.transactionType === 'manual_in' ? 'manual_in' : 'manual_out',
    timestamp: transaction.time,
    netAmount: transaction.amount,
    performedBy: transaction.performedBy,
    description: transaction.description,
    manualTransaction: {
      reason: transaction.description,
      notes: transaction.metadata?.notes
    }
  };
}

export function useFinanceEvents() {
  const [events, setEvents] = React.useState<FinanceEvent[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const refreshEvents = React.useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const financeEvents = await getFinanceEvents();
      setEvents(financeEvents);
    } catch (err) {
      console.error('[useFinanceEvents] Error:', err);
      setError('Failed to load finance events');
    } finally {
      setLoading(false);
    }
  }, []);

  React.useEffect(() => {
    refreshEvents();
  }, [refreshEvents]);

  return {
    events,
    loading,
    error,
    refreshEvents
  };
}

import React from 'react';
