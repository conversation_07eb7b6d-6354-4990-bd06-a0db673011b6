interface DiscoveredServer {
  ip: string;
  port: number;
  version?: string;
  url: string;
  lastSeen?: Date;
  responseTime?: number;
}

interface DiscoveryOptions {
  timeout?: number;
  maxConcurrent?: number;
}

const DEFAULT_PORTS = [5984, 5985, 5986, 5987];
const PRIMARY_SUBNET = '192.168.1';
const FALLBACK_SUBNETS = ['192.168.0', '10.0.0', '172.16.0'];

// Server cache for faster subsequent discoveries
interface CachedServer extends DiscoveredServer {
  lastVerified: Date;
  failureCount: number;
}

class ServerCache {
  private cache = new Map<string, CachedServer>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_FAILURES = 3;

  set(server: DiscoveredServer): void {
    const key = `${server.ip}:${server.port}`;
    const existing = this.cache.get(key);
    
    this.cache.set(key, {
      ...server,
      lastSeen: new Date(),
      lastVerified: new Date(),
      failureCount: existing?.failureCount || 0
    });
  }

  get(ip: string, port: number): CachedServer | null {
    const key = `${ip}:${port}`;
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Check if cache is still valid
    const now = Date.now();
    const age = now - cached.lastVerified.getTime();
    
    if (age > this.CACHE_DURATION || cached.failureCount >= this.MAX_FAILURES) {
      this.cache.delete(key);
      return null;
    }
    
    return cached;
  }

  getAll(): CachedServer[] {
    const now = Date.now();
    const valid: CachedServer[] = [];
    
    for (const [key, server] of this.cache.entries()) {
      const age = now - server.lastVerified.getTime();
      
      if (age <= this.CACHE_DURATION && server.failureCount < this.MAX_FAILURES) {
        valid.push(server);
      } else {
        this.cache.delete(key);
      }
    }
    
    return valid.sort((a, b) => {
      // Sort by response time (faster first), then by failure count
      if (a.responseTime && b.responseTime) {
        return a.responseTime - b.responseTime;
      }
      return a.failureCount - b.failureCount;
    });
  }

  markFailure(ip: string, port: number): void {
    const key = `${ip}:${port}`;
    const cached = this.cache.get(key);
    
    if (cached) {
      cached.failureCount++;
      if (cached.failureCount >= this.MAX_FAILURES) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

const serverCache = new ServerCache();

async function testCouchDBServer(ip: string, port: number, timeout = 1000): Promise<DiscoveredServer | null> {
  const url = `http://${ip}:${port}`;
  
  // Check cache first for faster response
  const cached = serverCache.get(ip, port);
  if (cached) {
    console.log(`💾 Using cached server: ${url} (${cached.responseTime}ms)`);
    return cached;
  }
  
  const startTime = Date.now();
  
  try {
    let response;
    let data;
    
    // Use CapacitorHttp on mobile, fetch on desktop
    const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
    
    if (isMobile) {
      console.log(`📱 Mobile request to ${url}`);
      const { CapacitorHttp } = await import('@capacitor/core');
      response = await CapacitorHttp.get({
        url,
        headers: { 'Accept': 'application/json' },
        connectTimeout: timeout,
        readTimeout: timeout
      });
      if (response.status !== 200) {
        serverCache.markFailure(ip, port);
        return null;
      }
      data = response.data;
    } else {
      console.log(`🖥️ Desktop request to ${url}`);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        headers: { 'Accept': 'application/json' }
      });
      
      clearTimeout(timeoutId);
      if (!response.ok) {
        serverCache.markFailure(ip, port);
        return null;
      }
      data = await response.json();
    }
    
    const responseTime = Date.now() - startTime;
    
    if (data && data.couchdb && data.version) {
      console.log(`✅ Found CouchDB: ${url} (${responseTime}ms, ${isMobile ? 'mobile' : 'desktop'})`);
      
      const server: DiscoveredServer = {
        ip,
        port,
        version: data.version,
        url,
        lastSeen: new Date(),
        responseTime
      };
      
      // Cache the successful result
      serverCache.set(server);
      
      return server;
    }
    
    serverCache.markFailure(ip, port);
    return null;
  } catch (error) {
    serverCache.markFailure(ip, port);
    return null;
  }
}

async function scanSubnet(subnet: string, ports: number[], options: DiscoveryOptions): Promise<DiscoveredServer[]> {
  const { timeout = 1000, maxConcurrent = 50 } = options;
  const servers: DiscoveredServer[] = [];
  
  for (let i = 1; i <= 254; i += maxConcurrent) {
    const batch = [];
    
    for (let j = 0; j < maxConcurrent && i + j <= 254; j++) {
      const ip = `${subnet}.${i + j}`;
      
      for (const port of ports) {
        batch.push(testCouchDBServer(ip, port, timeout));
      }
    }
    
    const results = await Promise.allSettled(batch);
    
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        servers.push(result.value);
      }
    });
    
    if (servers.length > 0 && subnet === PRIMARY_SUBNET) {
      return servers;
    }
  }
  
  return servers;
}

export async function discoverCouchDBServers(options: DiscoveryOptions = {}): Promise<DiscoveredServer[]> {
  console.log('🔍 Starting CouchDB server discovery...');
  
  // First, return any valid cached servers for immediate response
  const cachedServers = serverCache.getAll();
  if (cachedServers.length > 0) {
    console.log(`💾 Found ${cachedServers.length} cached server(s), returning immediately:`, cachedServers.map(s => s.url));
    
    // Start background refresh but return cached results immediately
    setTimeout(() => refreshCachedServers(options), 100);
    
    return cachedServers;
  }
  
  // No cache, perform full discovery
  return await performFullDiscovery(options);
}

async function refreshCachedServers(options: DiscoveryOptions): Promise<void> {
  console.log('🔄 Background refresh of cached servers...');
  await performFullDiscovery(options);
}

async function performFullDiscovery(options: DiscoveryOptions): Promise<DiscoveredServer[]> {
  // Check primary subnet FIRST (192.168.1.x on port 5984)
  console.log('🔄 Scanning primary subnet first...');
  let servers = await scanSubnet(PRIMARY_SUBNET, [5984], options);
  
  if (servers.length > 0) {
    console.log(`✅ Found ${servers.length} server(s) on primary subnet port 5984:`, servers);
    return servers;
  }
  
  // Then check localhost (127.0.0.1) where the electron CouchDB server runs
  console.log('🏠 Checking localhost...');
  const localhostServers = [];
  for (const port of DEFAULT_PORTS) {
    const server = await testCouchDBServer('127.0.0.1', port, options.timeout || 1000);
    if (server) {
      localhostServers.push(server);
    }
  }
  
  if (localhostServers.length > 0) {
    console.log(`✅ Found ${localhostServers.length} server(s) on localhost:`, localhostServers);
    return localhostServers;
  }
  
  console.log('🔄 Primary subnet scan complete, trying fallback ports...');
  servers = await scanSubnet(PRIMARY_SUBNET, DEFAULT_PORTS.slice(1), options);
  
  if (servers.length > 0) {
    console.log(`✅ Found ${servers.length} server(s) with fallback ports:`, servers);
    return servers;
  }
  
  console.log('🔄 Expanding to fallback subnets...');
  for (const subnet of FALLBACK_SUBNETS) {
    servers = await scanSubnet(subnet, DEFAULT_PORTS, options);
    
    if (servers.length > 0) {
      console.log(`✅ Found ${servers.length} server(s) on ${subnet}:`, servers);
      return servers;
    }
  }
  
  console.log('❌ No CouchDB servers discovered');
  return [];
}

// Export cache management functions
export const serverCacheManager = {
  getAll: () => serverCache.getAll(),
  clear: () => serverCache.clear(),
  size: () => serverCache.size(),
  markFailure: (ip: string, port: number) => serverCache.markFailure(ip, port)
};