'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getAllCaisseCalculations,
  getLastCaisseCalculation,
  getUnifiedCaisseDataSinceLastCalculation,
  createCaisseCalculation,
  getCaisseCalculation,
  updateCaisseCalculation,
  StaffCollectionTotal
} from '@/lib/db/v4/operations/caisse-calculation-ops';
import {
  CaisseCalculationDocument,
  OrderSummary
} from '@/lib/db/v4/schemas/caisse-calculation-schema';

export interface CurrentPeriodAnalysis {
  periodStart: string;
  totalExpected: number;
  orderCount: number;
  cashTransactionCount: number;
  totalFromOrders: number;
  totalFromCashTransactions: number;
  mainStaffName: string;
  mainStaffOrderCount: number;
  staffCollectionTotals: StaffCollectionTotal[];
}



export interface UseCaisseCalculationReturn {
  // Current period data
  currentPeriod: CurrentPeriodAnalysis | null;
  isLoadingCurrentPeriod: boolean;
  
  // Calculation history
  calculations: CaisseCalculationDocument[];
  isLoadingCalculations: boolean;
  
  // Operations
  refreshCurrentPeriod: () => Promise<void>;
  refreshCalculations: () => Promise<void>;
  createCalculation: (data: {
    countedAmount: number;
    notes?: string;
  }) => Promise<CaisseCalculationDocument>;
  
  // State
  error: string | null;
  isReady: boolean;
}

export function useCaisseCalculation(): UseCaisseCalculationReturn {
  const { user, isAuthenticated } = useAuth();
  const { isReady: isDbReady } = useUnifiedDB();
  
  // State
  const [currentPeriod, setCurrentPeriod] = useState<CurrentPeriodAnalysis | null>(null);
  const [calculations, setCalculations] = useState<CaisseCalculationDocument[]>([]);
  const [isLoadingCurrentPeriod, setIsLoadingCurrentPeriod] = useState(false);
  const [isLoadingCalculations, setIsLoadingCalculations] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const isReady = isDbReady && isAuthenticated && !!user;
  
  // Refresh current period analysis - simplified and robust
  const refreshCurrentPeriod = useCallback(async () => {
    if (!isReady) {
      console.log('[CaisseCalculation] Not ready, skipping refresh');
      return;
    }
    
    setIsLoadingCurrentPeriod(true);
    setError(null);
    
    try {
      console.log('[CaisseCalculation] Refreshing current period analysis');
      
      // Simplified database check - let the operations handle initialization
      const unifiedData = await getUnifiedCaisseDataSinceLastCalculation();
      
      const currentPeriodData: CurrentPeriodAnalysis = {
        periodStart: unifiedData.periodStart,
        totalExpected: unifiedData.totalExpected,
        orderCount: unifiedData.orders.length,
        cashTransactionCount: unifiedData.cashTransactions.length,
        totalFromOrders: unifiedData.totalExpectedFromOrders,
        totalFromCashTransactions: unifiedData.totalFromCashTransactions,
        mainStaffName: unifiedData.mainStaffName,
        mainStaffOrderCount: unifiedData.mainStaffOrderCount,
        staffCollectionTotals: unifiedData.staffCollectionTotals
      };
      
      setCurrentPeriod(currentPeriodData);
      
      console.log('[CaisseCalculation] Current period analysis complete:', {
        totalExpected: currentPeriodData.totalExpected,
        orderCount: currentPeriodData.orderCount,
        cashTransactionCount: currentPeriodData.cashTransactionCount
      });
    } catch (err) {
      console.error('[CaisseCalculation] Error refreshing current period:', err);
      setError(err instanceof Error ? err.message : 'Failed to load current period');
    } finally {
      setIsLoadingCurrentPeriod(false);
    }
  }, [isReady]);
  
  // Refresh calculation history - simplified
  const refreshCalculations = useCallback(async () => {
    if (!isReady) return;
    
    setIsLoadingCalculations(true);
    setError(null);
    
    try {
      console.log('[CaisseCalculation] Refreshing calculation history');
      
      const calculationHistory = await getAllCaisseCalculations();
      setCalculations(calculationHistory);
      
      console.log(`[CaisseCalculation] Loaded ${calculationHistory.length} calculations`);
    } catch (err) {
      console.error('[CaisseCalculation] Error refreshing calculations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load calculation history');
      setCalculations([]); // Set empty array on error
    } finally {
      setIsLoadingCalculations(false);
    }
  }, [isReady]);
  
  // Create a new calculation (still uses legacy function for now)
  const createCalculation = useCallback(async (data: {
    countedAmount: number;
    notes?: string;
  }): Promise<CaisseCalculationDocument> => {
    if (!isReady || !user) {
      throw new Error('Not ready to create calculation');
    }
    
    try {
      console.log('[CaisseCalculation] Creating new calculation:', data);
      
      const calculation = await createCaisseCalculation({
        calculatedBy: user.id,
        calculatedByName: user.name || 'Utilisateur Inconnu',
        countedAmount: data.countedAmount,
        notes: data.notes
      });
      
      console.log('[CaisseCalculation] Calculation created successfully:', calculation._id);
      
      // Refresh both current period and calculations
      await Promise.all([
        refreshCurrentPeriod(),
        refreshCalculations()
      ]);
      
      return calculation;
    } catch (err) {
      console.error('[CaisseCalculation] Error creating calculation:', err);
      throw err;
    }
  }, [isReady, user, refreshCurrentPeriod, refreshCalculations]);
  
  // Load data when ready - simplified without race conditions
  useEffect(() => {
    if (isReady) {
      const loadInitialData = async () => {
        try {
          await Promise.all([
            refreshCurrentPeriod(),
            refreshCalculations()
          ]);
        } catch (error) {
          console.error('[CaisseCalculation] Error during initial data load:', error);
          setError('Failed to load initial data');
        }
      };
      
      loadInitialData();
    }
  }, [isReady, refreshCurrentPeriod, refreshCalculations]);
  
  return {
    currentPeriod,
    isLoadingCurrentPeriod,
    calculations,
    isLoadingCalculations,
    refreshCurrentPeriod,
    refreshCalculations,
    createCalculation,
    error,
    isReady
  };
} 