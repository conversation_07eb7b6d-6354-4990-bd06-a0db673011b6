/**
 * Print Status Monitor Service
 * 
 * Provides real-time monitoring of print jobs with:
 * - Status tracking and notifications
 * - Print job history
 * - Performance metrics
 * - Error reporting
 * - Real-time updates via events
 */

export interface PrintJobStatus {
  id: string;
  title: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
  status: 'pending' | 'printing' | 'completed' | 'failed' | 'cancelled';
  printerName?: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  attempts: number;
  maxAttempts: number;
  executionTime?: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

export interface PrintMetrics {
  totalJobs: number;
  successfulJobs: number;
  failedJobs: number;
  averageExecutionTime: number;
  successRate: number;
  jobsByType: Record<string, number>;
  jobsByPrinter: Record<string, number>;
  recentErrors: Array<{ timestamp: string; error: string; jobId: string }>;
}

export interface PrintNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  jobId?: string;
  autoHide?: boolean;
  duration?: number;
}

class PrintStatusMonitor {
  private jobHistory: PrintJobStatus[] = [];
  private activeJobs: Map<string, PrintJobStatus> = new Map();
  private notifications: PrintNotification[] = [];
  private eventListeners: Map<string, Function[]> = new Map();
  private maxHistorySize = 1000;
  private maxNotifications = 50;

  constructor() {
    this.loadHistory();
    this.setupPeriodicCleanup();
  }

  /**
   * Track a new print job
   */
  trackJob(job: {
    id: string;
    title: string;
    type: 'kitchen' | 'receipt' | 'report' | 'expo';
    printerName?: string;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    maxAttempts?: number;
  }): void {
    const jobStatus: PrintJobStatus = {
      id: job.id,
      title: job.title,
      type: job.type,
      status: 'pending',
      printerName: job.printerName,
      createdAt: new Date().toISOString(),
      attempts: 0,
      maxAttempts: job.maxAttempts || 3,
      priority: job.priority || 'normal'
    };

    this.activeJobs.set(job.id, jobStatus);
    this.emit('job-created', jobStatus);
    
    console.log(`📊 [PrintMonitor] Tracking new job: ${job.id} (${job.type})`);
  }

  /**
   * Update job status
   */
  updateJobStatus(
    jobId: string, 
    status: PrintJobStatus['status'], 
    details?: {
      error?: string;
      executionTime?: number;
      printerUsed?: string;
    }
  ): void {
    const job = this.activeJobs.get(jobId);
    if (!job) {
      console.warn(`📊 [PrintMonitor] Job ${jobId} not found for status update`);
      return;
    }

    const previousStatus = job.status;
    job.status = status;

    if (status === 'printing' && !job.startedAt) {
      job.startedAt = new Date().toISOString();
    }

    if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      job.completedAt = new Date().toISOString();
      
      if (details?.executionTime) {
        job.executionTime = details.executionTime;
      } else if (job.startedAt) {
        job.executionTime = new Date().getTime() - new Date(job.startedAt).getTime();
      }

      if (details?.printerUsed) {
        job.printerName = details.printerUsed;
      }

      if (details?.error) {
        job.error = details.error;
      }

      // Move to history and remove from active
      this.addToHistory(job);
      this.activeJobs.delete(jobId);

      // Create notification
      this.createNotification(job, previousStatus);
    }

    if (status === 'printing') {
      job.attempts++;
    }

    this.emit('job-updated', job);
    this.saveHistory();

    console.log(`📊 [PrintMonitor] Job ${jobId} status: ${previousStatus} → ${status}`);
  }

  /**
   * Add job to history
   */
  private addToHistory(job: PrintJobStatus): void {
    this.jobHistory.unshift(job);
    
    // Limit history size
    if (this.jobHistory.length > this.maxHistorySize) {
      this.jobHistory = this.jobHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Create notification for job completion
   */
  private createNotification(job: PrintJobStatus, previousStatus: string): void {
    let notification: PrintNotification;

    switch (job.status) {
      case 'completed':
        notification = {
          id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`,
          type: 'success',
          title: '🖨️ Print Successful',
          message: `${job.title} printed successfully${job.printerName ? ` on ${job.printerName}` : ''}`,
          timestamp: new Date().toISOString(),
          jobId: job.id,
          autoHide: true,
          duration: 3000
        };
        break;

      case 'failed':
        notification = {
          id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`,
          type: 'error',
          title: '❌ Print Failed',
          message: `${job.title} failed to print${job.error ? `: ${job.error}` : ''}`,
          timestamp: new Date().toISOString(),
          jobId: job.id,
          autoHide: false,
          duration: 8000
        };
        break;

      case 'cancelled':
        notification = {
          id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`,
          type: 'warning',
          title: '⚠️ Print Cancelled',
          message: `${job.title} was cancelled`,
          timestamp: new Date().toISOString(),
          jobId: job.id,
          autoHide: true,
          duration: 4000
        };
        break;

      default:
        return; // No notification for other statuses
    }

    this.addNotification(notification);
  }

  /**
   * Add notification
   */
  private addNotification(notification: PrintNotification): void {
    this.notifications.unshift(notification);
    
    // Limit notifications
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }

    this.emit('notification', notification);

    // Auto-hide if specified
    if (notification.autoHide && notification.duration) {
      setTimeout(() => {
        this.removeNotification(notification.id);
      }, notification.duration);
    }

    console.log(`📊 [PrintMonitor] Notification: ${notification.title} - ${notification.message}`);
  }

  /**
   * Remove notification
   */
  removeNotification(notificationId: string): void {
    const index = this.notifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      this.notifications.splice(index, 1);
      this.emit('notification-removed', notificationId);
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): PrintMetrics {
    const allJobs = [...this.jobHistory];
    const totalJobs = allJobs.length;
    const successfulJobs = allJobs.filter(j => j.status === 'completed').length;
    const failedJobs = allJobs.filter(j => j.status === 'failed').length;
    
    const completedJobs = allJobs.filter(j => j.executionTime);
    const averageExecutionTime = completedJobs.length > 0 
      ? completedJobs.reduce((sum, j) => sum + (j.executionTime || 0), 0) / completedJobs.length 
      : 0;

    const successRate = totalJobs > 0 ? (successfulJobs / totalJobs) * 100 : 0;

    const jobsByType = allJobs.reduce((acc, job) => {
      acc[job.type] = (acc[job.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const jobsByPrinter = allJobs.reduce((acc, job) => {
      if (job.printerName) {
        acc[job.printerName] = (acc[job.printerName] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const recentErrors = allJobs
      .filter(j => j.status === 'failed' && j.error)
      .slice(0, 10)
      .map(j => ({
        timestamp: j.completedAt || j.createdAt,
        error: j.error!,
        jobId: j.id
      }));

    return {
      totalJobs,
      successfulJobs,
      failedJobs,
      averageExecutionTime,
      successRate,
      jobsByType,
      jobsByPrinter,
      recentErrors
    };
  }

  /**
   * Get active jobs
   */
  getActiveJobs(): PrintJobStatus[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Get job history
   */
  getJobHistory(limit?: number): PrintJobStatus[] {
    return limit ? this.jobHistory.slice(0, limit) : [...this.jobHistory];
  }

  /**
   * Get notifications
   */
  getNotifications(): PrintNotification[] {
    return [...this.notifications];
  }

  /**
   * Clear all notifications
   */
  clearNotifications(): void {
    this.notifications = [];
    this.emit('notifications-cleared');
  }

  /**
   * Event system
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`📊 [PrintMonitor] Event listener error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Load history from localStorage
   */
  private loadHistory(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('print-job-history');
        if (stored) {
          this.jobHistory = JSON.parse(stored);
          console.log(`📊 [PrintMonitor] Loaded ${this.jobHistory.length} jobs from history`);
        }
      }
    } catch (error) {
      console.error('📊 [PrintMonitor] Failed to load history:', error);
      this.jobHistory = [];
    }
  }

  /**
   * Save history to localStorage
   */
  private saveHistory(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('print-job-history', JSON.stringify(this.jobHistory));
      }
    } catch (error) {
      console.error('📊 [PrintMonitor] Failed to save history:', error);
    }
  }

  /**
   * Setup periodic cleanup
   */
  private setupPeriodicCleanup(): void {
    // Clean up old history entries every hour
    setInterval(() => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7); // Keep 7 days of history
      
      const beforeCount = this.jobHistory.length;
      this.jobHistory = this.jobHistory.filter(job => 
        new Date(job.createdAt) > cutoffDate
      );
      
      if (this.jobHistory.length < beforeCount) {
        console.log(`📊 [PrintMonitor] Cleaned up ${beforeCount - this.jobHistory.length} old history entries`);
        this.saveHistory();
      }
    }, 60 * 60 * 1000); // Every hour
  }
}

// Export singleton instance
export const printStatusMonitor = new PrintStatusMonitor();
