/**
 * Autonomous Sync Manager
 * 
 * Fully autonomous background sync system that:
 * - Starts automatically when app launches
 * - Continuously discovers CouchDB servers
 * - Maintains persistent connections
 * - Auto-reconnects on failures
 * - Runs completely in the background
 */

import { discoverCouchDBServers } from './ip-discovery';
import { nativeSyncService, type SyncServer, type SyncStatus } from './native-sync';
import { multiUserSessionManager } from '@/lib/auth/multi-user-session-manager';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';

// Extend SyncServer interface for autonomous sync
interface EnhancedSyncServer extends SyncServer {
  responseTime?: number;
  lastSeen?: Date;
  isProxy?: boolean;
  deviceId?: string;
}

interface InternetSyncDevice {
  id: string;
  ip: string;
  port: number;
  url: string;
  type: 'desktop' | 'mobile';
  lastSeen: string;
  isOnline: boolean;
}

interface AutonomousConfig {
  discoveryInterval: number;      // How often to scan for servers (ms)
  reconnectInterval: number;      // How often to retry failed connections (ms)
  maxReconnectAttempts: number;   // Max reconnection attempts before giving up
  autoStart: boolean;             // Start automatically on initialization
  preferredServers: string[];     // Preferred server IPs to connect to first
  enableInternetFallback: boolean; // Enable internet sync as fallback
  internetDiscoveryTimeout: number; // Timeout for internet discovery (ms)
  registrationInterval: number;   // How often to register device (ms)
}

interface AutonomousStatus {
  isRunning: boolean;
  isDiscovering: boolean;
  isInternetDiscovering: boolean;
  lastDiscovery: Date | null;
  lastInternetDiscovery: Date | null;
  discoveredServers: EnhancedSyncServer[];
  internetServers: EnhancedSyncServer[];
  connectedServers: EnhancedSyncServer[];
  failedServers: { server: EnhancedSyncServer; attempts: number; lastAttempt: Date }[];
  syncStatus: SyncStatus;
  internetSyncEnabled: boolean;
  deviceRegistered: boolean;
}

class AutonomousSyncManager {
  private config: AutonomousConfig = {
    discoveryInterval: 30000,      // 30 seconds
    reconnectInterval: 60000,      // 1 minute
    maxReconnectAttempts: 5,
    autoStart: true,
    preferredServers: [],
    enableInternetFallback: true,
    internetDiscoveryTimeout: 10000, // 10 seconds
    registrationInterval: 300000   // 5 minutes
  };

  private status: AutonomousStatus = {
    isRunning: false,
    isDiscovering: false,
    isInternetDiscovering: false,
    lastDiscovery: null,
    lastInternetDiscovery: null,
    discoveredServers: [],
    internetServers: [],
    connectedServers: [],
    failedServers: [],
    syncStatus: nativeSyncService.getStatus(),
    internetSyncEnabled: false,
    deviceRegistered: false
  };

  private discoveryTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private registrationTimer: NodeJS.Timeout | null = null;
  private listeners: ((status: AutonomousStatus) => void)[] = [];
  private isInitialized = false;
  private serverBaseUrl: string | null = null;

  constructor() {
    // Listen to native sync status changes
    nativeSyncService.onStatusChange((syncStatus) => {
      this.status.syncStatus = syncStatus;
      this.emitStatusUpdate();
    });

    // Listen to database initialization
    if (typeof window !== 'undefined') {
      document.addEventListener('v4-pouchdb-initialized', this.handleDatabaseReady.bind(this));
    }
  }

  /**
   * Initialize the autonomous sync manager
   */
  async initialize(config?: Partial<AutonomousConfig>): Promise<void> {
    if (this.isInitialized) {
      console.log('🤖 [AutonomousSync] Already initialized');
      return;
    }

    console.log('🤖 [AutonomousSync] Initializing autonomous sync manager...');

    // Merge config
    this.config = { ...this.config, ...config };
    this.isInitialized = true;

    // Initialize internet sync capabilities
    await this.initializeInternetSync();

    // Auto-start if enabled and database is ready
    if (this.config.autoStart) {
      await this.waitForDatabaseAndStart();
    }

    console.log('🤖 [AutonomousSync] Initialized with config:', this.config);
  }

  /**
   * Start autonomous sync operations
   */
  async start(): Promise<void> {
    if (this.status.isRunning) {
      console.log('🤖 [AutonomousSync] Already running');
      return;
    }

    console.log('🤖 [AutonomousSync] Starting autonomous sync...');
    this.status.isRunning = true;
    this.emitStatusUpdate();

    // Start initial discovery
    await this.performDiscovery();

    // Start periodic discovery
    this.startPeriodicDiscovery();

    // Start reconnection monitoring
    this.startReconnectionMonitoring();

    // Start device registration if internet sync is enabled
    if (this.config.enableInternetFallback && this.status.internetSyncEnabled) {
      this.startDeviceRegistration();
    }

    console.log('🤖 [AutonomousSync] Started successfully');
  }

  /**
   * Stop autonomous sync operations
   */
  async stop(): Promise<void> {
    console.log('🤖 [AutonomousSync] Stopping autonomous sync...');

    this.status.isRunning = false;

    // Clear timers
    if (this.discoveryTimer) {
      clearInterval(this.discoveryTimer);
      this.discoveryTimer = null;
    }

    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.registrationTimer) {
      clearInterval(this.registrationTimer);
      this.registrationTimer = null;
    }

    // Disconnect from all servers
    await nativeSyncService.stopSync();

    this.status.connectedServers = [];
    this.emitStatusUpdate();

    console.log('🤖 [AutonomousSync] Stopped');
  }

  /**
   * Get current status
   */
  getStatus(): AutonomousStatus {
    return { ...this.status };
  }

  /**
   * Subscribe to status changes
   */
  onStatusChange(listener: (status: AutonomousStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  /**
   * Manual discovery trigger
   */
  async discover(): Promise<EnhancedSyncServer[]> {
    return await this.performDiscovery();
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<AutonomousConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('🤖 [AutonomousSync] Config updated:', this.config);

    // Update internet sync status if config changed
    if (config.enableInternetFallback !== undefined) {
      if (config.enableInternetFallback && !this.status.internetSyncEnabled) {
        this.initializeInternetSync();
      } else if (!config.enableInternetFallback && this.status.internetSyncEnabled) {
        this.status.internetSyncEnabled = false;
        this.status.deviceRegistered = false;
        if (this.registrationTimer) {
          clearInterval(this.registrationTimer);
          this.registrationTimer = null;
        }
      }
    }

    // Restart timers with new intervals if running
    if (this.status.isRunning) {
      this.restartTimers();
    }
  }

  // Private methods

  private async waitForDatabaseAndStart(): Promise<void> {
    console.log('🤖 [AutonomousSync] Waiting for database initialization...');

    // Wait for database to be ready
    const maxWait = 30000; // 30 seconds
    const startTime = Date.now();

    while (!mainDbInstance.isInitialized && (Date.now() - startTime) < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (mainDbInstance.isInitialized) {
      console.log('🤖 [AutonomousSync] Database ready, starting autonomous sync');
      await this.start();
    } else {
      console.warn('🤖 [AutonomousSync] Database initialization timeout, will start when ready');
    }
  }

  private handleDatabaseReady = async (event: any) => {
    if (event.detail?.success && this.config.autoStart && !this.status.isRunning) {
      console.log('🤖 [AutonomousSync] Database initialized, starting autonomous sync');
      await this.start();
    }
  };

  private async performDiscovery(): Promise<EnhancedSyncServer[]> {
    if (this.status.isDiscovering) {
      console.log('🤖 [AutonomousSync] Discovery already in progress');
      return this.status.discoveredServers;
    }

    console.log('🤖 [AutonomousSync] Starting server discovery...');
    this.status.isDiscovering = true;
    this.emitStatusUpdate();

    try {
      // Use enhanced discovery with caching
      const discovered = await discoverCouchDBServers({
        timeout: 2000,  // Faster timeout since we have caching
        maxConcurrent: 15
      });

      const servers: EnhancedSyncServer[] = discovered.map(server => ({
        ip: server.ip,
        port: server.port,
        url: server.url,
        responseTime: server.responseTime,
        lastSeen: server.lastSeen
      }));

      // Sort servers by performance and preference
      const sortedServers = this.sortServersByPerformance(servers);

      this.status.discoveredServers = sortedServers;
      this.status.lastDiscovery = new Date();

      console.log(`🤖 [AutonomousSync] Discovered ${servers.length} servers (${discovered.filter(s => s.responseTime).length} cached):`,
        servers.map(s => `${s.url} (${s.responseTime || 'new'}ms)`));

      // If no local servers found and internet fallback is enabled, try internet discovery
      if (sortedServers.length === 0 && this.config.enableInternetFallback && this.status.internetSyncEnabled) {
        console.log('🌍 [AutonomousSync] 🚨 LOCAL SYNC UNAVAILABLE - Falling back to internet sync...');
        const internetServers = await this.performInternetDiscovery();
        if (internetServers.length > 0) {
          console.log(`🌍 [AutonomousSync] ✅ Internet fallback available: Found ${internetServers.length} remote server(s)`);
          sortedServers.push(...internetServers);
        } else {
          console.log('🌍 [AutonomousSync] ❌ Internet fallback failed: No remote servers available');
        }
      }

      // Auto-connect to discovered servers
      await this.autoConnectToServers(sortedServers);

      return sortedServers;
    } catch (error) {
      console.error('🤖 [AutonomousSync] Discovery failed:', error);
      return [];
    } finally {
      this.status.isDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private sortServersByPerformance(servers: EnhancedSyncServer[]): EnhancedSyncServer[] {
    return servers.sort((a, b) => {
      // 1. 🏠 LOCAL FIRST: Direct local servers always beat internet proxy servers
      const aIsLocal = !(a as any).isProxy;
      const bIsLocal = !(b as any).isProxy;

      if (aIsLocal && !bIsLocal) return -1; // Local server wins over proxy
      if (!aIsLocal && bIsLocal) return 1;  // Proxy loses to local server

      // 2. Preferred servers first (within same type)
      const aPreferred = this.config.preferredServers.includes(a.ip);
      const bPreferred = this.config.preferredServers.includes(b.ip);

      if (aPreferred && !bPreferred) return -1;
      if (!aPreferred && bPreferred) return 1;

      // 3. Then by response time (faster first)
      if (a.responseTime && b.responseTime) {
        return a.responseTime - b.responseTime;
      }

      // 4. Servers with response time data first
      if (a.responseTime && !b.responseTime) return -1;
      if (!a.responseTime && b.responseTime) return 1;

      // 5. Then by port (5984 first)
      if (a.port === 5984 && b.port !== 5984) return -1;
      if (a.port !== 5984 && b.port === 5984) return 1;

      // 6. Finally by IP (localhost first)
      if (a.ip === '127.0.0.1' && b.ip !== '127.0.0.1') return -1;
      if (a.ip !== '127.0.0.1' && b.ip === '127.0.0.1') return 1;

      return 0;
    });
  }

  private async autoConnectToServers(servers: EnhancedSyncServer[]): Promise<void> {
    if (servers.length === 0) {
      console.log('🤖 [AutonomousSync] No servers to connect to');
      return;
    }

    // Skip if already connected to a server
    if (nativeSyncService.isConnected()) {
      console.log('🤖 [AutonomousSync] Already connected to a server, skipping auto-connect');
      return;
    }

    // Sort servers by preference
    const sortedServers = this.sortServersByPreference(servers);

    // Try to connect to the first available server
    for (const server of sortedServers) {
      // Skip servers that have failed too many times
      const failedServer = this.status.failedServers.find(f => f.server.url === server.url);
      if (failedServer && failedServer.attempts >= this.config.maxReconnectAttempts) {
        console.log(`🤖 [AutonomousSync] Skipping ${server.url} - max attempts reached`);
        continue;
      }

      const isProxy = (server as any).isProxy;
      const syncType = isProxy ? '🌐 INTERNET' : '🏠 LOCAL';
      console.log(`🤖 [AutonomousSync] ${syncType} - Attempting to connect to ${server.url}${isProxy ? ' (via proxy)' : ' (direct)'}...`);

      // For proxy servers, we need to set up special options
      const syncOptions: any = {
        live: true,
        retry: true
      };

      // If this is a proxy server, add authentication headers and proxy flag
      if (isProxy) {
        syncOptions.isInternetProxy = true;
        const activeSession = multiUserSessionManager.getActiveSession();
        if (activeSession?.token) {
          syncOptions.authToken = activeSession.token;
        }
      }

      const success = await nativeSyncService.startSync(server, syncOptions);

      if (success) {
        const syncType = isProxy ? '🌐 INTERNET' : '🏠 LOCAL';
        console.log(`🤖 [AutonomousSync] ✅ ${syncType} SYNC CONNECTED: ${server.url}`);
        this.status.connectedServers = [server];
        this.removeFromFailedServers(server);
        this.emitStatusUpdate();
        break;
      } else {
        const syncType = isProxy ? '🌐 INTERNET' : '🏠 LOCAL';
        console.log(`🤖 [AutonomousSync] ❌ ${syncType} SYNC FAILED: ${server.url}`);
        this.addToFailedServers(server);
      }
    }
  }

  private sortServersByPreference(servers: EnhancedSyncServer[]): EnhancedSyncServer[] {
    return servers.sort((a, b) => {
      // 1. 🏠 LOCAL FIRST: Direct local servers always beat internet proxy servers
      const aIsLocal = !(a as any).isProxy;
      const bIsLocal = !(b as any).isProxy;

      if (aIsLocal && !bIsLocal) return -1; // Local server wins over proxy
      if (!aIsLocal && bIsLocal) return 1;  // Proxy loses to local server

      // 2. Preferred servers first (within same type)
      const aPreferred = this.config.preferredServers.includes(a.ip);
      const bPreferred = this.config.preferredServers.includes(b.ip);

      if (aPreferred && !bPreferred) return -1;
      if (!aPreferred && bPreferred) return 1;

      // 3. Then by port (5984 first)
      if (a.port === 5984 && b.port !== 5984) return -1;
      if (a.port !== 5984 && b.port === 5984) return 1;

      return 0;
    });
  }

  private startPeriodicDiscovery(): void {
    if (this.discoveryTimer) {
      clearInterval(this.discoveryTimer);
    }

    this.discoveryTimer = setInterval(async () => {
      if (this.status.isRunning) {
        console.log('🤖 [AutonomousSync] Periodic discovery...');
        await this.performDiscovery();
      }
    }, this.config.discoveryInterval);
  }

  private startReconnectionMonitoring(): void {
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
    }

    this.reconnectTimer = setInterval(async () => {
      if (this.status.isRunning && !nativeSyncService.isConnected()) {
        console.log('🤖 [AutonomousSync] Connection lost, attempting reconnection...');

        // Try to reconnect to known servers
        if (this.status.discoveredServers.length > 0) {
          await this.autoConnectToServers(this.status.discoveredServers);
        } else {
          // No known servers, perform discovery
          await this.performDiscovery();
        }
      }
    }, this.config.reconnectInterval);
  }

  private restartTimers(): void {
    this.startPeriodicDiscovery();
    this.startReconnectionMonitoring();

    if (this.config.enableInternetFallback && this.status.internetSyncEnabled) {
      this.startDeviceRegistration();
    }
  }

  private addToFailedServers(server: EnhancedSyncServer): void {
    const existing = this.status.failedServers.find(f => f.server.url === server.url);
    if (existing) {
      existing.attempts++;
      existing.lastAttempt = new Date();
    } else {
      this.status.failedServers.push({
        server,
        attempts: 1,
        lastAttempt: new Date()
      });
    }
    this.emitStatusUpdate();
  }

  private removeFromFailedServers(server: EnhancedSyncServer): void {
    this.status.failedServers = this.status.failedServers.filter(f => f.server.url !== server.url);
    this.emitStatusUpdate();
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  // Internet Sync Methods

  private async initializeInternetSync(): Promise<void> {
    if (!this.config.enableInternetFallback) {
      console.log('🌍 [AutonomousSync] Internet fallback disabled');
      return;
    }

    try {
      // Determine if we're running in a mobile or desktop environment
      const isMobile = typeof (window as any).Capacitor !== 'undefined';
      const isElectron = typeof (window as any).electronAPI !== 'undefined';

      if (!isMobile && !isElectron) {
        console.log('🌍 [AutonomousSync] Not in mobile or desktop app, internet sync disabled');
        return;
      }

      // Set server base URL (in production, this would be your actual server)
      this.serverBaseUrl = window.location.origin;

      this.status.internetSyncEnabled = true;
      console.log('🌍 [AutonomousSync] Internet sync initialized, server:', this.serverBaseUrl);
    } catch (error) {
      console.error('🌍 [AutonomousSync] Failed to initialize internet sync:', error);
    }
  }

  private async performInternetDiscovery(): Promise<EnhancedSyncServer[]> {
    if (!this.status.internetSyncEnabled || !this.serverBaseUrl) {
      return [];
    }

    if (this.status.isInternetDiscovering) {
      console.log('🌍 [AutonomousSync] Internet discovery already in progress');
      return this.status.internetServers;
    }

    console.log('🌍 [AutonomousSync] Starting internet peer discovery...');
    this.status.isInternetDiscovering = true;
    this.emitStatusUpdate();

    try {
      const restaurantId = getCurrentRestaurantId();
      const activeSession = multiUserSessionManager.getActiveSession();

      if (!restaurantId || !activeSession?.token) {
        console.warn('🌍 [AutonomousSync] No authenticated session for internet discovery');
        return [];
      }

      const response = await fetch(`${this.serverBaseUrl}/api/sync/discover-peers`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${activeSession.token}`,
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(this.config.internetDiscoveryTimeout)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const devices: InternetSyncDevice[] = data.devices || [];

      const internetServers: EnhancedSyncServer[] = devices
        .filter(device => device.isOnline && device.type === 'desktop')
        .map(device => ({
          ip: device.ip,
          port: device.port,
          url: `${this.serverBaseUrl}/api/sync/proxy/${device.id}`,
          isProxy: true,
          deviceId: device.id,
          responseTime: undefined,
          lastSeen: new Date(device.lastSeen)
        }));

      this.status.internetServers = internetServers;
      this.status.lastInternetDiscovery = new Date();

      console.log(`🌍 [AutonomousSync] Discovered ${internetServers.length} internet servers:`,
        internetServers.map(s => `${s.deviceId} (proxy: ${s.url})`));

      return internetServers;
    } catch (error) {
      console.error('🌍 [AutonomousSync] Internet discovery failed:', error);
      return [];
    } finally {
      this.status.isInternetDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private async registerDevice(): Promise<void> {
    if (!this.status.internetSyncEnabled || !this.serverBaseUrl) {
      return;
    }

    try {
      const restaurantId = getCurrentRestaurantId();
      const activeSession = multiUserSessionManager.getActiveSession();

      if (!restaurantId || !activeSession?.token) {
        console.warn('🌍 [AutonomousSync] No authenticated session for device registration');
        return;
      }

      // Only desktop apps can act as sync servers
      const isElectron = typeof (window as any).electronAPI !== 'undefined';
      if (!isElectron) {
        console.log('🌍 [AutonomousSync] Not a desktop app, skipping device registration');
        return;
      }

      // Get local IP and port from bundled CouchDB
      const localServers = this.status.discoveredServers.filter(s => s.ip === '127.0.0.1' && s.port === 5984);
      if (localServers.length === 0) {
        console.warn('🌍 [AutonomousSync] No local CouchDB server found for registration');
        return;
      }

      const localServer = localServers[0];
      const deviceInfo = {
        type: 'desktop' as const,
        ip: localServer.ip,
        port: localServer.port,
        couchdbUrl: localServer.url
      };

      const response = await fetch(`${this.serverBaseUrl}/api/sync/register-device`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${activeSession.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(deviceInfo)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      this.status.deviceRegistered = true;

      console.log('🌍 [AutonomousSync] Device registered successfully:', result.deviceId);
    } catch (error) {
      console.error('🌍 [AutonomousSync] Device registration failed:', error);
      this.status.deviceRegistered = false;
    }

    this.emitStatusUpdate();
  }

  private startDeviceRegistration(): void {
    if (this.registrationTimer) {
      clearInterval(this.registrationTimer);
    }

    // Register immediately
    this.registerDevice();

    // Then register periodically
    this.registrationTimer = setInterval(() => {
      if (this.status.isRunning && this.status.internetSyncEnabled) {
        this.registerDevice();
      }
    }, this.config.registrationInterval);
  }
}

// Export singleton instance
export const autonomousSyncManager = new AutonomousSyncManager();
export type { AutonomousConfig, AutonomousStatus };