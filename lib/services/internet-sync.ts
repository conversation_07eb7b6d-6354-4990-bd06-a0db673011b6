import { mainDbInstance } from '@/lib/db/v4/core/db-instance';
import { getRestaurantDbName } from '@/lib/db/db-utils';

interface InternetSyncConfig {
  vpsBaseUrl: string;
  authToken: string;
  deviceId: string;
}

interface DiscoveredPeer {
  id: string;
  deviceType: string;
  ipAddress: string;
  couchdbPort: number;
  hostname: string;
  platform: string;
  lastSeen: Date;
}

interface InternetSyncStatus {
  connected: boolean;
  syncing: boolean;
  lastSync?: Date;
  error?: string;
  docsReceived: number;
  docsSent: number;
  proxyUrl?: string;
}

class InternetSyncService {
  private config: InternetSyncConfig | null = null;
  private syncHandler: any = null;
  private currentPeer: DiscoveredPeer | null = null;
  private status: InternetSyncStatus = {
    connected: false,
    syncing: false,
    docsReceived: 0,
    docsSent: 0
  };
  private listeners: ((status: InternetSyncStatus) => void)[] = [];

  configure(config: InternetSyncConfig): void {
    this.config = config;
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  onStatusChange(listener: (status: InternetSyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  async discoverPeers(): Promise<DiscoveredPeer[]> {
    if (!this.config) {
      throw new Error('Internet sync service not configured');
    }

    try {
      const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
      
      if (isMobile) {
        const { CapacitorHttp } = await import('@capacitor/core');
        const response = await CapacitorHttp.get({
          url: `${this.config.vpsBaseUrl}/api/sync/discover-peers`,
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Accept': 'application/json'
          }
        });

        if (response.status !== 200) {
          throw new Error(`Discovery failed: ${response.status}`);
        }

        return response.data.peers.map((peer: any) => ({
          ...peer,
          lastSeen: new Date(peer.lastSeen)
        }));
      } else {
        const response = await fetch(`${this.config.vpsBaseUrl}/api/sync/discover-peers`, {
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Accept': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Discovery failed: ${response.status}`);
        }

        const data = await response.json();
        return data.peers.map((peer: any) => ({
          ...peer,
          lastSeen: new Date(peer.lastSeen)
        }));
      }
    } catch (error: any) {
      console.error('❌ Internet peer discovery failed:', error);
      throw error;
    }
  }

  async startSync(peer: DiscoveredPeer): Promise<boolean> {
    if (!this.config) {
      throw new Error('Internet sync service not configured');
    }

    console.log(`🌐 Starting internet sync with ${peer.hostname}...`);

    try {
      await this.waitForDatabase();
      
      const localDb = mainDbInstance.getPouchDBForSync();
      if (!localDb) {
        throw new Error('Local database not available for sync');
      }

      const restaurantId = mainDbInstance.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Restaurant ID not available');
      }

      const dbName = getRestaurantDbName(restaurantId);

      // Construct proxy URL through VPS
      const proxyUrl = `${this.config.vpsBaseUrl}/api/sync/proxy/${peer.id}/${dbName}`;
      
      console.log(`📡 Internet sync via proxy: ${proxyUrl}`);

      this.currentPeer = peer;
      this.status = {
        connected: true,
        syncing: true,
        docsReceived: 0,
        docsSent: 0,
        proxyUrl
      };
      this.emitStatusUpdate();

      // PouchDB sync with custom headers for proxy authentication
      this.syncHandler = localDb.sync(proxyUrl, {
        live: true,
        retry: true,
        ajax: {
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`
          }
        }
      })
        .on('change', (info: any) => {
          console.log('🌐 Internet sync change:', info);
          if (info.direction === 'pull') {
            this.status.docsReceived += info.change?.docs_read || 0;
            
            // Emit database change events
            if (info.change?.docs && typeof window !== 'undefined') {
              info.change.docs.forEach((doc: any) => {
                window.dispatchEvent(new CustomEvent('pouchdb-change', {
                  detail: {
                    doc,
                    isLocal: false,
                    direction: 'pull',
                    source: 'internet'
                  }
                }));
              });
            }
          } else if (info.direction === 'push') {
            this.status.docsSent += info.change?.docs_written || 0;
          }
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('paused', () => {
          console.log('⏸️ Internet sync paused');
          this.status.syncing = false;
          this.emitStatusUpdate();
        })
        .on('active', () => {
          console.log('▶️ Internet sync active');
          this.status.syncing = true;
          this.emitStatusUpdate();
        })
        .on('denied', (err: any) => {
          console.error('❌ Internet sync denied:', err);
          this.status.error = `Access denied: ${err.message || err.reason || 'Authentication failed'}`;
          this.emitStatusUpdate();
        })
        .on('complete', (info: any) => {
          console.log('✅ Internet sync complete:', info);
          this.status.syncing = false;
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('error', (err: any) => {
          console.error('❌ Internet sync error:', err);
          
          let errorMessage = err.message || err.reason || 'Unknown error';
          if (err.status === 401) {
            errorMessage = 'Authentication failed - check VPS connection';
          } else if (err.status === 403) {
            errorMessage = 'Access forbidden - check permissions';
          } else if (err.status === 404) {
            errorMessage = 'Target device not found or offline';
          } else if (err.status === 502) {
            errorMessage = 'Proxy error - desktop may be offline';
          }

          this.status.error = `Internet sync failed: ${errorMessage}`;
          this.status.connected = false;
          this.status.syncing = false;
          this.emitStatusUpdate();
        });

      return true;
    } catch (error: any) {
      console.error('❌ Failed to start internet sync:', error);
      this.status.error = error.message;
      this.status.connected = false;
      this.status.syncing = false;
      this.emitStatusUpdate();
      return false;
    }
  }

  async stopSync(): Promise<void> {
    if (this.syncHandler) {
      console.log('🛑 Stopping internet sync...');
      this.syncHandler.cancel();
      this.syncHandler = null;
    }

    this.currentPeer = null;
    this.status = {
      connected: false,
      syncing: false,
      docsReceived: this.status.docsReceived,
      docsSent: this.status.docsSent,
      lastSync: this.status.lastSync
    };
    this.emitStatusUpdate();
  }

  private async waitForDatabase(maxWaitMs = 10000): Promise<void> {
    const startTime = Date.now();
    
    while (!mainDbInstance.isInitialized || !mainDbInstance.getPouchDBForSync()) {
      if (Date.now() - startTime > maxWaitMs) {
        throw new Error('Database initialization timeout - not ready for sync operations');
      }
      
      console.log('⏳ Waiting for database initialization...');
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('✅ Database ready for internet sync operations');
  }

  getStatus(): InternetSyncStatus {
    return { ...this.status };
  }

  getCurrentPeer(): DiscoveredPeer | null {
    return this.currentPeer;
  }

  isConnected(): boolean {
    return this.status.connected;
  }

  isSyncing(): boolean {
    return this.status.syncing;
  }

  isConfigured(): boolean {
    return this.config !== null;
  }
}

export const internetSyncService = new InternetSyncService();
export type { InternetSyncConfig, DiscoveredPeer, InternetSyncStatus };