import { getSettings } from '@/lib/db/v4/operations/settings-ops';

export interface RestaurantInfo {
  name: string;
  address: string;
  phone: string;
  secondaryPhone?: string;
  logoUrl: string;
  footer: string;
}

class PrintService {
  private restaurantInfo: RestaurantInfo | null = null;
  private readonly LOCAL_KEY = 'restaurant_info_settings_v4';

  /**
   * 🏪 Get restaurant info from database or localStorage fallback
   */
  async getRestaurantInfo(): Promise<RestaurantInfo> {
    try {
      // Try to get from database first
      const settings = await getSettings();
      this.restaurantInfo = {
        name: settings.restaurantName || '',
        address: settings.restaurantAddress || '',
        phone: settings.restaurantPhone || '',
        secondaryPhone: settings.restaurantSecondaryPhone || undefined,
        logoUrl: settings.restaurantLogoUrl || '',
        footer: settings.restaurantFooter || 'Merci de votre visite!'
      };
      
      // Cache in localStorage for offline access
      localStorage.setItem(this.LOCAL_KEY, JSON.stringify(this.restaurantInfo));
      
      return this.restaurantInfo;
    } catch (error) {
      console.warn('⚠️ Could not load restaurant info from database, using localStorage fallback:', error);
      
      // Fallback to localStorage
      const cached = localStorage.getItem(this.LOCAL_KEY);
      if (cached) {
        try {
          this.restaurantInfo = JSON.parse(cached);
          return this.restaurantInfo!;
        } catch (parseError) {
          console.warn('⚠️ Could not parse cached restaurant info:', parseError);
        }
      }
      
      // Final fallback to empty info
      this.restaurantInfo = {
        name: '',
        address: '',
        phone: '',
        secondaryPhone: undefined,
        logoUrl: '',
        footer: 'Merci de votre visite!'
      };
      
      return this.restaurantInfo;
    }
  }

  /**
   * 🔄 Set restaurant info (used by settings components)
   */
  setRestaurantInfo(name: string, primaryPhone: string, address: string, secondaryPhone: string, logoUrl: string, footer?: string): void {
    this.restaurantInfo = {
      name,
      address,
      phone: primaryPhone,
      secondaryPhone: secondaryPhone || undefined,
      logoUrl,
      footer: footer || 'Merci de votre visite!'
    };
    
    // Update localStorage cache
    localStorage.setItem(this.LOCAL_KEY, JSON.stringify(this.restaurantInfo));
  }

  /**
   * 🧾 Generate restaurant header HTML for receipts
   */
  async generateRestaurantHeader(fontSize: { header: number; normal: number; bold: number }): Promise<string> {
    const info = await this.getRestaurantInfo();
    let headerHtml = '';

    // Create a compact, industry-standard header
    if (info.name || info.address || info.phone || info.logoUrl) {
      headerHtml += `
      <div style="text-align: center; margin-bottom: 8px; padding-bottom: 4px;">`;
      
      // Logo at the top, properly sized for thermal receipts
      if (info.logoUrl) {
        headerHtml += `
        <div style="margin-bottom: 8px;">
          <img src="${info.logoUrl}" alt="Logo" style="max-width: 80px; max-height: 40px; object-fit: contain; display: block; margin: 0 auto;" />
        </div>`;
      }

      // Restaurant name - clean and prominent
      if (info.name) {
        headerHtml += `
        <div style="font-size: ${fontSize.bold + 2}px; font-weight: bold; margin-bottom: 4px; letter-spacing: 0.8px; line-height: 1.1;">
          ${info.name.toUpperCase()}
        </div>`;
      }

      // Address and phone in industry-standard compact format
      if (info.address || info.phone) {
        headerHtml += `
        <div style="font-size: ${fontSize.normal}px; line-height: 1.2; color: #333;">`;
        
        if (info.address) {
          headerHtml += `
          <div style="margin-bottom: 2px;">${info.address}</div>`;
        }
        
        if (info.phone) {
          headerHtml += `
          <div style="font-weight: 500;">Tel: ${info.phone}</div>`;
        }
        
        if (info.secondaryPhone) {
          headerHtml += `
          <div style="font-weight: 500;">Tel: ${info.secondaryPhone}</div>`;
        }
        
        headerHtml += `
        </div>`;
      }
      
      headerHtml += `
      </div>`;
    }

    return headerHtml;
  }

  /**
   * 🦶 Generate restaurant footer HTML for receipts
   */
  async generateRestaurantFooter(fontSize: { normal: number }): Promise<string> {
    const info = await this.getRestaurantInfo();
    
    if (!info.footer) return '';
    
    return `
    <div style="text-align: center; font-size: ${fontSize.normal}px; margin-top: 10px; padding-top: 6px; border-top: 1px dashed #999; color: #555; font-style: italic;">
      ${info.footer}
    </div>`;
  }

  /**
   * 🎯 Check if restaurant info is configured
   */
  async isRestaurantInfoConfigured(): Promise<boolean> {
    const info = await this.getRestaurantInfo();
    return !!(info.name || info.address || info.phone);
  }
}

export const printService = new PrintService();