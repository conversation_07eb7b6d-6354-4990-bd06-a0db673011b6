/**
 * Print Validation Service
 * 
 * Comprehensive testing and validation system for printers:
 * - Printer connectivity tests
 * - Print quality validation
 * - Performance benchmarking
 * - Health monitoring
 * - Automated diagnostics
 */

import { printExecutionService } from './print-execution-service';
import { printStatusMonitor } from './print-status-monitor';

export interface PrintTestResult {
  testId: string;
  printerName: string;
  testType: 'connectivity' | 'quality' | 'performance' | 'full';
  success: boolean;
  score?: number; // 0-100
  duration: number;
  timestamp: string;
  details: {
    connectivity?: boolean;
    printSpeed?: number;
    qualityScore?: number;
    errorRate?: number;
    issues?: string[];
    recommendations?: string[];
  };
  error?: string;
}

export interface PrinterHealthStatus {
  printerName: string;
  overallHealth: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  healthScore: number; // 0-100
  lastTested: string;
  issues: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    recommendation: string;
  }>;
  metrics: {
    successRate: number;
    averageSpeed: number;
    errorCount: number;
    lastError?: string;
  };
}

class PrintValidationService {
  private testHistory: PrintTestResult[] = [];
  private healthStatuses: Map<string, PrinterHealthStatus> = new Map();
  private isRunningTests = false;

  constructor() {
    this.loadTestHistory();
    this.setupPeriodicHealthChecks();
  }

  /**
   * Run comprehensive printer test
   */
  async runFullPrinterTest(printerName: string): Promise<PrintTestResult> {
    const testId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
    const startTime = Date.now();

    console.log(`🧪 [PrintValidation] Starting full test for printer: ${printerName}`);

    try {
      const result: PrintTestResult = {
        testId,
        printerName,
        testType: 'full',
        success: false,
        duration: 0,
        timestamp: new Date().toISOString(),
        details: {
          issues: [],
          recommendations: []
        }
      };

      // Test 1: Connectivity
      const connectivityResult = await this.testConnectivity(printerName);
      result.details.connectivity = connectivityResult.success;

      if (!connectivityResult.success) {
        result.details.issues!.push('Printer connectivity failed');
        result.details.recommendations!.push('Check printer power and connection');
        result.error = connectivityResult.error;
        result.duration = Date.now() - startTime;
        this.addTestResult(result);
        return result;
      }

      // Test 2: Print Speed
      const speedResult = await this.testPrintSpeed(printerName);
      result.details.printSpeed = speedResult.speed;

      // Test 3: Quality Test
      const qualityResult = await this.testPrintQuality(printerName);
      result.details.qualityScore = qualityResult.score;

      // Calculate overall score
      const scores = [
        connectivityResult.success ? 100 : 0,
        Math.min(100, (speedResult.speed / 1000) * 100), // Normalize speed
        qualityResult.score
      ];
      result.score = scores.reduce((sum, score) => sum + score, 0) / scores.length;

      // Determine success
      result.success = result.score >= 70;

      // Add recommendations based on results
      if (result.details.printSpeed! < 500) {
        result.details.issues!.push('Slow print speed detected');
        result.details.recommendations!.push('Check printer drivers and connection quality');
      }

      if (result.details.qualityScore! < 80) {
        result.details.issues!.push('Print quality below optimal');
        result.details.recommendations!.push('Clean printer heads or replace cartridges');
      }

      result.duration = Date.now() - startTime;
      this.addTestResult(result);
      this.updatePrinterHealth(printerName, result);

      console.log(`🧪 [PrintValidation] Full test completed for ${printerName}:`, {
        success: result.success,
        score: result.score,
        duration: result.duration
      });

      return result;

    } catch (error) {
      const result: PrintTestResult = {
        testId,
        printerName,
        testType: 'full',
        success: false,
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown test error',
        details: {
          issues: ['Test execution failed'],
          recommendations: ['Check printer configuration and try again']
        }
      };

      this.addTestResult(result);
      console.error(`🧪 [PrintValidation] Full test failed for ${printerName}:`, error);
      return result;
    }
  }

  /**
   * Test printer connectivity
   */
  async testConnectivity(printerName: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔌 [PrintValidation] Testing connectivity for ${printerName}`);
      
      const result = await printExecutionService.testPrinter(printerName);
      
      return {
        success: result.success,
        error: result.error
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connectivity test failed'
      };
    }
  }

  /**
   * Test print speed
   */
  async testPrintSpeed(printerName: string): Promise<{ speed: number; success: boolean }> {
    try {
      console.log(`⚡ [PrintValidation] Testing print speed for ${printerName}`);
      
      const testContent = this.generateSpeedTestContent();
      const startTime = Date.now();

      const result = await printExecutionService.executePrint({
        id: `speed-test-${Date.now()}`,
        title: 'Print Speed Test',
        content: testContent,
        type: 'report',
        printerName,
        copies: 1,
        priority: 'normal',
        createdAt: new Date().toISOString()
      });

      const speed = result.executionTime || (Date.now() - startTime);

      return {
        speed,
        success: result.success
      };
    } catch (error) {
      console.error(`⚡ [PrintValidation] Speed test failed for ${printerName}:`, error);
      return {
        speed: 0,
        success: false
      };
    }
  }

  /**
   * Test print quality
   */
  async testPrintQuality(printerName: string): Promise<{ score: number; success: boolean }> {
    try {
      console.log(`🎨 [PrintValidation] Testing print quality for ${printerName}`);
      
      const testContent = this.generateQualityTestContent();

      const result = await printExecutionService.executePrint({
        id: `quality-test-${Date.now()}`,
        title: 'Print Quality Test',
        content: testContent,
        type: 'report',
        printerName,
        copies: 1,
        priority: 'normal',
        createdAt: new Date().toISOString()
      });

      // Simulate quality scoring based on print success and speed
      let score = 0;
      if (result.success) {
        score = 85; // Base score for successful print
        
        // Adjust based on execution time
        if (result.executionTime && result.executionTime < 2000) {
          score += 10; // Fast printing bonus
        } else if (result.executionTime && result.executionTime > 5000) {
          score -= 15; // Slow printing penalty
        }
      }

      return {
        score: Math.max(0, Math.min(100, score)),
        success: result.success
      };
    } catch (error) {
      console.error(`🎨 [PrintValidation] Quality test failed for ${printerName}:`, error);
      return {
        score: 0,
        success: false
      };
    }
  }

  /**
   * Generate speed test content
   */
  private generateSpeedTestContent(): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Print Speed Test</title>
          <style>
            body { font-family: 'Courier New', monospace; margin: 20px; }
            .test-header { text-align: center; margin-bottom: 20px; }
            .test-content { line-height: 1.4; }
            @media print { @page { size: A4; margin: 20mm; } }
          </style>
        </head>
        <body>
          <div class="test-header">
            <h2>🚀 PRINT SPEED TEST</h2>
            <p>Timestamp: ${new Date().toLocaleString()}</p>
          </div>
          <div class="test-content">
            <p>This is a print speed test document designed to measure printer performance.</p>
            <p>The test includes various text elements to simulate real-world printing scenarios.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            <hr>
            <p><strong>Test completed at: ${new Date().toISOString()}</strong></p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate quality test content
   */
  private generateQualityTestContent(): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Print Quality Test</title>
          <style>
            body { font-family: 'Courier New', monospace; margin: 20px; }
            .test-header { text-align: center; margin-bottom: 20px; }
            .quality-tests { margin: 20px 0; }
            .test-section { margin: 15px 0; padding: 10px; border: 1px solid #ccc; }
            .font-test { font-size: 8px; }
            .font-test.medium { font-size: 12px; }
            .font-test.large { font-size: 16px; }
            .line-test { border-top: 1px solid #000; margin: 5px 0; }
            @media print { @page { size: A4; margin: 20mm; } }
          </style>
        </head>
        <body>
          <div class="test-header">
            <h2>🎨 PRINT QUALITY TEST</h2>
            <p>Timestamp: ${new Date().toLocaleString()}</p>
          </div>
          
          <div class="quality-tests">
            <div class="test-section">
              <h3>Font Size Test</h3>
              <p class="font-test">Small text (8px) - Check readability</p>
              <p class="font-test medium">Medium text (12px) - Standard size</p>
              <p class="font-test large">Large text (16px) - Headers</p>
            </div>
            
            <div class="test-section">
              <h3>Line Quality Test</h3>
              <div class="line-test"></div>
              <div class="line-test"></div>
              <div class="line-test"></div>
            </div>
            
            <div class="test-section">
              <h3>Character Test</h3>
              <p>ABCDEFGHIJKLMNOPQRSTUVWXYZ</p>
              <p>abcdefghijklmnopqrstuvwxyz</p>
              <p>0123456789 !@#$%^&*()_+-=[]{}|;:,.<>?</p>
            </div>
            
            <div class="test-section">
              <h3>Alignment Test</h3>
              <p style="text-align: left;">Left aligned text</p>
              <p style="text-align: center;">Center aligned text</p>
              <p style="text-align: right;">Right aligned text</p>
            </div>
          </div>
          
          <hr>
          <p><strong>Quality test completed at: ${new Date().toISOString()}</strong></p>
        </body>
      </html>
    `;
  }

  /**
   * Update printer health status
   */
  private updatePrinterHealth(printerName: string, testResult: PrintTestResult): void {
    const existing = this.healthStatuses.get(printerName);
    const metrics = printStatusMonitor.getMetrics();
    
    const health: PrinterHealthStatus = {
      printerName,
      overallHealth: this.calculateHealthLevel(testResult.score || 0),
      healthScore: testResult.score || 0,
      lastTested: testResult.timestamp,
      issues: [],
      metrics: {
        successRate: metrics.successRate,
        averageSpeed: metrics.averageExecutionTime,
        errorCount: metrics.failedJobs,
        lastError: metrics.recentErrors[0]?.error
      }
    };

    // Add issues based on test results
    if (testResult.details.issues) {
      health.issues = testResult.details.issues.map(issue => ({
        severity: this.determineSeverity(issue),
        message: issue,
        recommendation: this.getRecommendation(issue)
      }));
    }

    this.healthStatuses.set(printerName, health);
    console.log(`💊 [PrintValidation] Updated health status for ${printerName}: ${health.overallHealth}`);
  }

  /**
   * Calculate health level from score
   */
  private calculateHealthLevel(score: number): PrinterHealthStatus['overallHealth'] {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 60) return 'fair';
    if (score >= 40) return 'poor';
    return 'critical';
  }

  /**
   * Determine issue severity
   */
  private determineSeverity(issue: string): 'low' | 'medium' | 'high' | 'critical' {
    if (issue.includes('connectivity failed')) return 'critical';
    if (issue.includes('slow') || issue.includes('quality')) return 'medium';
    return 'low';
  }

  /**
   * Get recommendation for issue
   */
  private getRecommendation(issue: string): string {
    if (issue.includes('connectivity')) return 'Check printer power and network connection';
    if (issue.includes('slow')) return 'Update printer drivers or check network speed';
    if (issue.includes('quality')) return 'Clean printer heads or replace consumables';
    return 'Contact technical support if issue persists';
  }

  /**
   * Add test result to history
   */
  private addTestResult(result: PrintTestResult): void {
    this.testHistory.unshift(result);
    
    // Limit history size
    if (this.testHistory.length > 100) {
      this.testHistory = this.testHistory.slice(0, 100);
    }
    
    this.saveTestHistory();
  }

  /**
   * Get test history
   */
  getTestHistory(printerName?: string, limit?: number): PrintTestResult[] {
    let history = printerName 
      ? this.testHistory.filter(test => test.printerName === printerName)
      : this.testHistory;
    
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * Get printer health status
   */
  getPrinterHealth(printerName: string): PrinterHealthStatus | null {
    return this.healthStatuses.get(printerName) || null;
  }

  /**
   * Get all printer health statuses
   */
  getAllPrinterHealth(): PrinterHealthStatus[] {
    return Array.from(this.healthStatuses.values());
  }

  /**
   * Load test history from storage
   */
  private loadTestHistory(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('print-test-history');
        if (stored) {
          this.testHistory = JSON.parse(stored);
        }
      }
    } catch (error) {
      console.error('Failed to load test history:', error);
      this.testHistory = [];
    }
  }

  /**
   * Save test history to storage
   */
  private saveTestHistory(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('print-test-history', JSON.stringify(this.testHistory));
      }
    } catch (error) {
      console.error('Failed to save test history:', error);
    }
  }

  /**
   * Setup periodic health checks
   */
  private setupPeriodicHealthChecks(): void {
    // Run health checks every 6 hours
    setInterval(async () => {
      if (this.isRunningTests) return;
      
      console.log('🏥 [PrintValidation] Running periodic health checks');
      this.isRunningTests = true;
      
      try {
        const printers = await printExecutionService.refreshPrinters();
        
        for (const printer of printers) {
          if (printer.status === 'online') {
            await this.testConnectivity(printer.name);
          }
        }
      } catch (error) {
        console.error('Periodic health check failed:', error);
      } finally {
        this.isRunningTests = false;
      }
    }, 6 * 60 * 60 * 1000); // 6 hours
  }
}

// Export singleton instance
export const printValidationService = new PrintValidationService();
