// Server-side barcode generation service
// Generates actual scannable barcodes that can be printed

import JsBarcode from 'jsbarcode';

// Only import canvas in Node.js environment
let createCanvas: any = null;
if (typeof window === 'undefined') {
  try {
    createCanvas = require('canvas').createCanvas;
  } catch (error) {
    console.warn('Canvas not available, barcode generation will be limited');
  }
}

export interface BarcodeOptions {
  format?: 'CODE128' | 'CODE39' | 'EAN13' | 'EAN8';
  width?: number;
  height?: number;
  displayValue?: boolean;
  fontSize?: number;
  margin?: number;
  background?: string;
  lineColor?: string;
}

export interface GeneratedBarcode {
  dataUrl: string; // Base64 data URL for embedding in HTML
  svg: string;     // SVG string for vector graphics
  width: number;
  height: number;
  value: string;
}

class BarcodeService {
  
  /**
   * Generate a scannable barcode as base64 data URL
   */
  generateBarcode(value: string, options: BarcodeOptions = {}): GeneratedBarcode {
    const defaultOptions: Required<BarcodeOptions> = {
      format: 'CODE128',
      width: 2,
      height: 50,
      displayValue: true,
      fontSize: 12,
      margin: 10,
      background: '#ffffff',
      lineColor: '#000000'
    };

    const opts = { ...defaultOptions, ...options };

    try {
      let dataUrl = '';
      let canvasWidth = 400;
      let canvasHeight = 100;

      // Generate Canvas barcode if canvas is available (server environments)
      if (createCanvas) {
        const canvas = createCanvas(400, 100); // Will be resized by JsBarcode
        JsBarcode(canvas, value, {
          format: opts.format,
          width: opts.width,
          height: opts.height,
          displayValue: opts.displayValue,
          fontSize: opts.fontSize,
          margin: opts.margin,
          background: opts.background,
          lineColor: opts.lineColor
        });
        dataUrl = canvas.toDataURL('image/png');
        canvasWidth = canvas.width;
        canvasHeight = canvas.height;
      } else {
        // Fallback for web builds - generate empty data URL
        console.warn('Canvas not available, returning placeholder barcode');
        dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
      }

      // Generate SVG barcode (always available)
      const svgNode = this.createSVGElement();
      JsBarcode(svgNode, value, {
        format: opts.format,
        width: opts.width,
        height: opts.height,
        displayValue: opts.displayValue,
        fontSize: opts.fontSize,
        margin: opts.margin,
        background: opts.background,
        lineColor: opts.lineColor
      });

      return {
        dataUrl,
        svg: svgNode.outerHTML,
        width: canvasWidth,
        height: canvasHeight,
        value: value
      };

    } catch (error) {
      console.error('❌ Barcode generation failed:', error);
      throw new Error(`Failed to generate barcode for value: ${value}`);
    }
  }

  /**
   * Generate multiple barcodes at once
   */
  generateBarcodes(values: string[], options: BarcodeOptions = {}): GeneratedBarcode[] {
    return values.map(value => this.generateBarcode(value, options));
  }

  /**
   * Generate barcode optimized for kitchen printing
   */
  generateKitchenBarcode(value: string): GeneratedBarcode {
    return this.generateBarcode(value, {
      format: 'CODE128', 
      width: 2,        // Smaller width for individual bars
      height: 70,      // Good height
      displayValue: false,
      fontSize: 20,    // Readable text
      margin: 10,      // Reasonable margin
      background: '#ffffff',
      lineColor: '#000000'
    });
  }

  /**
   * Generate barcode as data URL (simpler approach for HTML embedding)
   */
  generateKitchenBarcodeDataURL(value: string): string {
    try {
      // Added: Detect browser environment and use a DOM canvas if available before falling back to SVG.
      // This improves reliability since most browsers render PNG data-URLs without issues, while some
      // print previews ignore inline SVG encoded as data-URL.
      if (typeof window !== 'undefined' && typeof document !== 'undefined') {
        try {
          const canvasEl = document.createElement('canvas');
          // Slightly larger canvas for better resolution – will be constrained via CSS.
          canvasEl.width = 600;
          canvasEl.height = 150;
          JsBarcode(canvasEl, value, {
            format: 'CODE128',
            width: 2,
            height: 70,
            displayValue: false,
            fontSize: 20,
            margin: 10,
            background: '#ffffff',
            lineColor: '#000000'
          });
          return canvasEl.toDataURL('image/png');
        } catch (domCanvasErr) {
          console.warn('DOM canvas barcode generation failed, will fallback to off-screen methods:', domCanvasErr);
        }
      }

      // 🎯 TRY CANVAS FIRST (for Electron/Node environments)
      if (createCanvas) {
        try {
          // Create a larger canvas for better quality
          const canvas = createCanvas(600, 150);
          JsBarcode(canvas, value, {
            format: 'CODE128',
            width: 2,
            height: 70,
            displayValue: false,
            fontSize: 20,
            margin: 10,
            background: '#ffffff',
            lineColor: '#000000'
          });
          return canvas.toDataURL('image/png');
        } catch (canvasError) {
          console.warn('Node canvas barcode generation failed, falling back to SVG:', canvasError);
        }
      }

      // 🎯 FALLBACK TO SVG (universal compatibility)
      console.log('🎨 Using SVG barcode generation for value:', value);
      const svgElement = this.createSVGElement();
      JsBarcode(svgElement, value, {
        format: 'CODE128', 
        width: 2,        // Smaller width for individual bars
        height: 70,      // Good height
        displayValue: false,
        fontSize: 20,    // Readable text
        margin: 10,      // Reasonable margin
        background: '#ffffff',
        lineColor: '#000000'
      });

      // 🎯 CONVERT SVG TO DATA URL
      const svgString = svgElement.outerHTML;
      
      // 🎯 SAFE BASE64 ENCODING (works in both Node and browsers)
      let svgDataUrl: string;
      try {
        if (typeof Buffer !== 'undefined') {
          // Node.js environment
          svgDataUrl = `data:image/svg+xml;base64,${Buffer.from(svgString).toString('base64')}`;
        } else {
          // Browser environment
          svgDataUrl = `data:image/svg+xml;base64,${btoa(svgString)}`;
        }
      } catch (encodingError) {
        // Final fallback - URL encode the SVG
        svgDataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgString)}`;
      }
      
      console.log('✅ SVG barcode generated successfully, length:', svgDataUrl.length);
      return svgDataUrl;

    } catch (error) {
      console.error('❌ ALL barcode generation methods failed:', error);
      // 🎯 LAST RESORT: Return text-based barcode
      const textBarcode = this.generateTextBarcode(value);
      
      // Safe encoding for text barcode
      try {
        if (typeof Buffer !== 'undefined') {
          return `data:image/svg+xml;base64,${Buffer.from(textBarcode).toString('base64')}`;
        } else {
          return `data:image/svg+xml;base64,${btoa(textBarcode)}`;
        }
      } catch (encodingError) {
        return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(textBarcode)}`;
      }
    }
  }

  /**
   * Generate text-based barcode as fallback
   */
  private generateTextBarcode(value: string): string {
    return `
      <svg xmlns="http://www.w3.org/2000/svg" width="200" height="30" viewBox="0 0 200 30">
        <rect width="200" height="30" fill="#ffffff" stroke="#000000" stroke-width="1"/>
        <text x="100" y="20" text-anchor="middle" font-family="monospace" font-size="12" fill="#000000">${value}</text>
      </svg>
    `;
  }

  /**
   * Test barcode generation
   */
  testBarcodeGeneration(value: string = 'TEST123'): void {
    console.log('🧪 Testing barcode generation...');
    try {
      const result = this.generateKitchenBarcode(value);
      console.log('✅ Barcode generation successful');
      console.log('📏 Dimensions:', result.width, 'x', result.height);
      console.log('📝 Value:', result.value);
      console.log('🖼️ DataURL length:', result.dataUrl.length);
      console.log('📄 SVG length:', result.svg.length);
      console.log('📄 SVG content preview:', result.svg.substring(0, 200) + '...');
    } catch (error) {
      console.error('❌ Barcode test failed:', error);
    }
  }

  /**
   * Create SVG element for server-side rendering
   */
  private createSVGElement(): any {
    // Create a mock SVG element that JsBarcode can work with
    const svgElement: { [key: string]: any } = {
      setAttribute: function(name: string, value: string): void {
        (this as any)[name] = value;
      },
      appendChild: function(child: any): void {
        if (!this.children) this.children = [];
        this.children.push(child);
      },
      get outerHTML(): string {
        const internalProps = ['children', 'outerHTML', 'ownerDocument', 'setAttribute', 'appendChild'];
        const attrs: string = Object.keys(this)
          .filter((key: string) => !internalProps.includes(key)) // Filter out internal mock properties
          .map((key: string) => `${key}="${(this as any)[key]}"`)
          .join(' ');
        
        // Ensure standard SVG attributes are present if not set by JsBarcode
        const xmlnsAttr = attrs.includes('xmlns') ? '' : 'xmlns="http://www.w3.org/2000/svg"';
        const versionAttr = attrs.includes('version') ? '' : 'version="1.1"';

        const children: string = this.children ? this.children.map((child: any) => child.outerHTML || '').join('') : '';
        return `<svg ${xmlnsAttr} ${versionAttr} ${attrs}>${children}</svg>`;
      },
      children: [] as any[]
    };

    // Add createElement method for child elements
    (svgElement as any).ownerDocument = {
      createElementNS: function(namespace: string, tagName: string): { [key: string]: any } {
        const element: { [key: string]: any } = {
          tagName: tagName, // Store tagName for outerHTML
          attributes: {} as Record<string, string>, // Store attributes
          children: [] as any[], // Store children
          setAttribute: function(name: string, value: string): void {
            this.attributes[name] = value;
          },
          appendChild: function(child: any): void {
            this.children.push(child);
          },
          get outerHTML(): string {
            const attrs: string = Object.keys(this.attributes)
              .map((key: string) => `${key}="${this.attributes[key]}"`)
              .join(' ');
            const childrenHtml: string = this.children.map((child: any) => child.outerHTML || '').join('');
            
            // Handle self-closing tags for elements like rect, path, etc.
            if (childrenHtml === '' && !['text'].includes(this.tagName)) { 
              return `<${this.tagName} ${attrs}/>`;
            } else {
              return `<${this.tagName} ${attrs}>${childrenHtml}</${this.tagName}>`;
            }
          }
        };
        return element;
      }
    };

    return svgElement;
  }

  /**
   * Validate barcode value
   */
  validateBarcodeValue(value: string, format: string = 'CODE128'): boolean {
    try {
      // Try to generate a barcode to validate
      if (!createCanvas) {
        console.warn('Canvas not available, barcode validation failed');
        return false;
      }
      const canvas = createCanvas(100, 50);
      JsBarcode(canvas, value, { format: format as any });
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const barcodeService = new BarcodeService();
export default barcodeService; 