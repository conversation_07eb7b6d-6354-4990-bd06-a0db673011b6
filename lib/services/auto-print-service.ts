/**
 * Auto Print Service
 * 
 * Automatically triggers kitchen printing when orders are created or synced
 * Only active on desktop devices with configured printers
 */

import { kitchenPrintService } from './kitchen-print-service';
import { Order } from '@/lib/db/v4/schemas/order-schema';

interface AutoPrintConfig {
  enabled: boolean;
  printOnOrderCreated: boolean;
  printOnOrderSynced: boolean;
  desktopOnly: boolean;
  delayMs: number; // Delay before printing to allow for any UI updates
}

class AutoPrintService {
  private config: AutoPrintConfig = {
    enabled: true,
    printOnOrderCreated: true,
    printOnOrderSynced: true,
    desktopOnly: true,
    delayMs: 1000 // 1 second delay
  };

  private isInitialized = false;
  private listeners: (() => void)[] = [];

  /**
   * Initialize the auto-print service
   */
  initialize(): void {
    if (this.isInitialized) {
      console.log('🖨️ [AutoPrint] Already initialized');
      return;
    }

    // Only initialize on desktop (Electron) environments
    if (this.config.desktopOnly && !this.isElectronDesktop()) {
      console.log('🖨️ [AutoPrint] Skipping initialization - not desktop environment');
      return;
    }

    console.log('🖨️ [AutoPrint] Initializing auto-print service...');
    
    this.loadConfig();
    this.setupEventListeners();
    this.isInitialized = true;

    console.log('🖨️ [AutoPrint] Auto-print service initialized with config:', this.config);
  }

  /**
   * Cleanup event listeners
   */
  destroy(): void {
    console.log('🖨️ [AutoPrint] Destroying auto-print service...');
    
    this.listeners.forEach(cleanup => cleanup());
    this.listeners = [];
    this.isInitialized = false;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AutoPrintConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    
    console.log('🖨️ [AutoPrint] Config updated:', this.config);
  }

  /**
   * Get current configuration
   */
  getConfig(): AutoPrintConfig {
    return { ...this.config };
  }

  /**
   * Check if auto-print is enabled and ready
   */
  isReady(): boolean {
    return this.isInitialized && 
           this.config.enabled && 
           this.isElectronDesktop();
  }

  /**
   * Manually trigger print for an order
   */
  async printOrder(order: Order, source: 'created' | 'synced' = 'created'): Promise<void> {
    if (!this.isReady()) {
      console.log('🖨️ [AutoPrint] Service not ready, skipping print');
      return;
    }

    console.log(`🖨️ [AutoPrint] Printing order ${order.id} (source: ${source})`);

    try {
      // Add delay to allow UI updates to complete
      if (this.config.delayMs > 0) {
        await new Promise(resolve => setTimeout(resolve, this.config.delayMs));
      }

      const result = await kitchenPrintService.printKitchenOrder(order, order.tableId);
      
      if (result.success) {
        console.log(`✅ [AutoPrint] Successfully printed order ${order.id}`);
        
        // Show success notification
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('auto-print-success', {
            detail: { orderId: order.id, source }
          }));
        }
      } else {
        console.error(`❌ [AutoPrint] Failed to print order ${order.id}:`, result.error);
        
        // Show error notification
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('auto-print-error', {
            detail: { orderId: order.id, error: result.error, source }
          }));
        }
      }
    } catch (error) {
      console.error(`❌ [AutoPrint] Error printing order ${order.id}:`, error);
      
      // Show error notification
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('auto-print-error', {
          detail: { orderId: order.id, error: error instanceof Error ? error.message : 'Unknown error', source }
        }));
      }
    }
  }

  // Private methods

  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // Listen for order created events
    if (this.config.printOnOrderCreated) {
      const handleOrderCreated = async (event: CustomEvent) => {
        const order = event.detail?.order;
        if (order) {
          console.log('🖨️ [AutoPrint] Order created event received:', order.id);
          await this.printOrder(order, 'created');
        }
      };

      window.addEventListener('order-created', handleOrderCreated as EventListener);
      this.listeners.push(() => {
        window.removeEventListener('order-created', handleOrderCreated as EventListener);
      });
    }

    // Listen for order synced events (when orders arrive from other devices)
    if (this.config.printOnOrderSynced) {
      const handleOrderSynced = async (event: CustomEvent) => {
        const order = event.detail?.order;
        const source = event.detail?.source;
        
        // Only print if this order came from sync (not local creation)
        if (order && source === 'sync') {
          console.log('🖨️ [AutoPrint] Order synced event received:', order.id);
          await this.printOrder(order, 'synced');
        }
      };

      window.addEventListener('order-synced', handleOrderSynced as EventListener);
      this.listeners.push(() => {
        window.removeEventListener('order-synced', handleOrderSynced as EventListener);
      });
    }

    // Listen for database changes that might indicate synced orders
    const handleDatabaseChange = async (event: CustomEvent) => {
      const change = event.detail;
      
      // Check if this is a new order document from sync
      if (change?.doc?.type === 'order_document' && 
          change?.doc?.status === 'pending' &&
          !change?.isLocal) {
        
        console.log('🖨️ [AutoPrint] Database change detected - new synced order:', change.doc._id);
        await this.printOrder(change.doc, 'synced');
      }
    };

    window.addEventListener('database-change', handleDatabaseChange as EventListener);
    this.listeners.push(() => {
      window.removeEventListener('database-change', handleDatabaseChange as EventListener);
    });
  }

  private isElectronDesktop(): boolean {
    if (typeof window === 'undefined') return false;
    
    return !!(
      (window as any).electronAPI ||
      (window as any).electron?.isElectron ||
      (window as any).IS_DESKTOP_APP ||
      navigator.userAgent.includes('Electron')
    );
  }

  private loadConfig(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('auto_print_config');
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        this.config = { ...this.config, ...parsedConfig };
      }
    } catch (error) {
      console.warn('🖨️ [AutoPrint] Failed to load config from localStorage:', error);
    }
  }

  private saveConfig(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem('auto_print_config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('🖨️ [AutoPrint] Failed to save config to localStorage:', error);
    }
  }
}

// Export singleton instance
export const autoPrintService = new AutoPrintService();
export type { AutoPrintConfig };