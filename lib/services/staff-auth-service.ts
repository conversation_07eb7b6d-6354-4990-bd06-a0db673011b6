/**
 * Staff Auth Service
 *
 * This service handles the creation of MongoDB auth documents for staff members.
 * It's separate from the staff creation process, allowing for optional auth creation
 * after a staff member has been created in PouchDB.
 */

import { StaffDocument } from '@/lib/db/v4/schemas/per-staff-schemas';
import { updateStaffMember } from '@/lib/db/v4';
import { safeFetch, OfflineError } from '@/lib/utils/network';
import { httpClient } from '@/lib/utils/http-client';

/**
 * Creates a MongoDB auth document for an existing staff member
 *
 * @param staffId The ID of the existing staff member in PouchDB
 * @param authData Authentication data (username, password)
 * @returns Result of the operation
 */
export async function createStaffAuth(
  staffMember: StaffDocument,
  authData: {
    username: string;
    password: string;
  }
): Promise<{
  success: boolean;
  userId?: string;
  error?: string;
  details?: Record<string, string[] | string>;
  offlineError?: boolean;
}> {
  try {
    console.log('StaffAuthService: Creating auth for staff member:', staffMember.id);

    // Validate the staff member
    if (!staffMember || !staffMember.id) {
      console.error('StaffAuthService: Invalid staff member');
      return {
        success: false,
        error: 'Invalid staff member. Please try again with a valid staff member.'
      };
    }

    // Check if the staff member already has an auth account
    if (staffMember.hasUserAccount && staffMember.userId) {
      console.error('StaffAuthService: Staff member already has an auth account');
      return {
        success: false,
        error: 'Staff member already has an auth account'
      };
    }

    // Validate auth data
    const errors: Record<string, string[]> = {};
    const username = authData.username?.trim();
    const password = authData.password?.trim();

    if (!username) {
      errors.username = ['Username is required'];
    } else if (username.length < 3) {
      errors.username = ['Username must be at least 3 characters'];
    }

    if (!password) {
      errors.password = ['Password is required'];
    } else if (password.length < 6) {
      errors.password = ['Password must be at least 6 characters'];
    }

    // Handle validation errors
    if (Object.keys(errors).length > 0) {
      console.error('StaffAuthService: Validation errors:', errors);
      return {
        success: false,
        error: 'Please fix the validation errors',
        details: errors
      };
    }

    // Determine the restaurant ID (metadata no longer present on StaffDocument)
    let restaurantId: string | null = null;
    console.log('StaffAuthService: Starting auth creation, restaurantId will be determined from localStorage or database utils');

    // If not found in metadata, try to get from the current authenticated user's context
    if (!restaurantId) {
      try {
        // Try to get from localStorage
        if (typeof window !== 'undefined') {
          restaurantId = localStorage.getItem('restaurantId');
          console.log('StaffAuthService: restaurantId from localStorage:', restaurantId);
        }

        // Try to get from the current database identifier
        if (!restaurantId && typeof window !== 'undefined') {
          // Import the getCurrentRestaurantId function
          const { getCurrentRestaurantId } = await import('@/lib/db/v4/utils/restaurant-id');
          restaurantId = getCurrentRestaurantId();
          console.log('StaffAuthService: restaurantId from getCurrentRestaurantId:', restaurantId);
        }
      } catch (error) {
        console.log('StaffAuthService: Error getting restaurant ID:', error);
      }
    }

    if (!restaurantId) {
      console.error('StaffAuthService: Could not determine restaurant ID');
      return {
        success: false,
        error: 'Could not determine restaurant ID. Please try again or contact support.'
      };
    }

    // Clean the restaurant ID by removing any prefixes (like 'resto_')
    if (restaurantId.startsWith('resto_')) {
      const cleanedRestaurantId = restaurantId.replace('resto_', '');
      console.log(`StaffAuthService: Cleaned restaurant ID from '${restaurantId}' to '${cleanedRestaurantId}'`);
      restaurantId = cleanedRestaurantId;
    }

    // Continue with staff auth creation
    const requestBody = {
      staffMember: {
        id: staffMember.id,
        name: staffMember.name,
        email: staffMember.email,
        role: staffMember.role,
        restaurantId: restaurantId
      },
      auth: {
        username,
        password
      }
    };

    console.log('StaffAuthService: Sending request with body:', {
      ...requestBody,
      password: '[REDACTED]'
    });

    // Use platform-aware HTTP client with offline check
    try {
      const response = await httpClient.post('/api/staff/auth/create', requestBody);

      // httpClient.post() returns parsed data in response.data
      const result = response.data;

      if (!response.ok) {
        return {
          success: false,
          error: result.error || 'Failed to create staff authentication'
        };
      }

      // Update the staff member with auth info
      try {
        await updateStaffMember(staffMember.id, {
          hasUserAccount: true,
          userId: result.userId,
          username: username
        });

        console.log('StaffAuthService: Auth created and staff updated successfully:', staffMember.id);
      } catch (updateError) {
        console.error('StaffAuthService: Error updating staff with auth info:', updateError);
        return {
          success: true,
          userId: result.userId,
          error: 'Auth created but failed to update staff record'
        };
      }

      return {
        success: true,
        userId: result.userId
      };
    } catch (error) {
      // Check for offline error
      if (error instanceof OfflineError) {
        return {
          success: false,
          error: error.message,
          offlineError: true
        };
      }

      console.error('StaffAuthService: Error during fetch:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error creating staff authentication'
      };
    }
  } catch (error) {
    console.error('StaffAuthService: Unexpected error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

/**
 * Checks if a username is available globally
 *
 * @param username The username to check
 * @returns Whether the username is available
 */
export async function isUsernameAvailable(username: string): Promise<boolean> {
  try {
    // Use the public endpoint that checks global username availability
    const response = await httpClient.get(`/api/auth/check-username/public?username=${encodeURIComponent(username)}`);
    return response.data.available;
  } catch (error) {
    console.error('StaffAuthService: Error checking username availability:', error);
    return false;
  }
}
