import { Payment, AttendanceRecord } from '../types/staff';
import { 
  createPayment, 
  getStaffPayments as dbGetStaffPayments, 
  getStaffAdvanceBalance, 
  calculateUnpaidBalance,
  getPeriodPaymentStatus,
  getStaffPaymentsByDateRange,
  deletePayment as dbDeletePayment,
  getStaffMember,
  getStaffAttendanceRecords,
  PaymentDocument, 
  StaffDocument,
  updatePaymentStatus,
  updatePaymentNote as dbUpdatePaymentNote,
  updatePaymentMetadata,
  updateStaffMember
} from '../db/v4';
import { databaseV4 } from '../db/v4';
import { format, addMonths } from 'date-fns';

// Finance system integration
let financeService: any = null;

// Lazy load finance service to avoid circular dependencies
async function getFinanceService() {
  if (!financeService) {
    try {
      const module = await import('./finance-service');
      financeService = module;
    } catch (error) {
      console.warn('Finance service not available:', error);
      financeService = { addExpense: () => Promise.resolve() };
    }
  }
  return financeService;
}

/**
 * PAYMENT CALCULATION SERVICE
 * 
 * Handles all payment calculations, validations, and business logic
 */

// Period tracking interfaces
export interface PaymentPeriod {
  id: string;
  label: string;
  startDate: string;
  endDate: string;
  status: 'paid' | 'overdue' | 'upcoming';
  amount?: number;
}

export interface PeriodStatus {
  paidPeriods: string[];
  duePeriods: string[];
  nextDue?: string;
}

// Payment calculation interfaces
export interface PaymentCalculation {
  baseAmount: number;
  shiftAmount: number;
  bonusAmount: number;
  deductionAmount: number;
  advanceDeduction: number;
  finalAmount: number;
  breakdown: string[];
}

export interface ShiftPaymentData {
  attendanceIds: string[];
  shiftBreakdown: Array<{
    shiftId: string;
    shiftName: string;
    count: number;
    rate: number;
    amount: number;
  }>;
}

/**
 * Calculate earnings for a staff member based on their payment configuration
 */
export async function calculateStaffEarnings(
  staffId: string,
  paymentType: 'SALARY' | 'SHIFT_PAYMENT',
  periodStart?: string,
  periodEnd?: string,
  shiftData?: ShiftPaymentData
): Promise<PaymentCalculation> {
  try {
    console.log(`💰 Calculating earnings for staff ${staffId}, type: ${paymentType}`);

    // Get staff member
    const staff = await getStaffMember(staffId);
    if (!staff) {
      throw new Error(`Staff member ${staffId} not found`);
    }

    let calculation: PaymentCalculation = {
      baseAmount: 0,
      shiftAmount: 0,
      bonusAmount: 0,
      deductionAmount: 0,
      advanceDeduction: 0,
      finalAmount: 0,
      breakdown: []
    };

    if (paymentType === 'SALARY') {
      calculation = await calculateSalaryPayment(staff, periodStart!, periodEnd!);
    } else if (paymentType === 'SHIFT_PAYMENT') {
      calculation = await calculateShiftPayment(staff, shiftData!);
    }

    // Apply advance deduction if configured with enhanced safety checks
    if (staff.paymentConfig.advanceRepayment) {
      const advanceBalance = await getStaffAdvanceBalance(staffId);
      if (advanceBalance > 0) {
        // VULNERABILITY FIX: Enhanced advance repayment calculation with safety limits
        const maxPercentageDeduction = Math.max(0, (calculation.finalAmount * (staff.paymentConfig.advanceRepayment.percentage || 50)) / 100);
        const maxConfiguredAmount = Math.max(0, staff.paymentConfig.advanceRepayment.maxAmount || 10000);
        const maxDeduction = Math.min(
          Math.max(0, advanceBalance), // Ensure advance balance is not negative
          maxConfiguredAmount,
          maxPercentageDeduction,
          Math.max(0, calculation.finalAmount) // Never deduct more than the final amount
        );
        
        // Additional safety check to prevent negative final amounts
        if (maxDeduction > 0 && maxDeduction <= calculation.finalAmount) {
          calculation.advanceDeduction = maxDeduction;
          calculation.finalAmount = Math.max(0, calculation.finalAmount - maxDeduction);
          calculation.breakdown.push(`Advance Repayment: -${maxDeduction.toLocaleString()} DZD`);
        } else if (maxDeduction > calculation.finalAmount) {
          // Log warning but don't apply deduction that would make payment negative
          console.warn(`⚠️ Advance deduction (${maxDeduction}) would exceed final amount (${calculation.finalAmount}) - skipping automatic deduction`);
          calculation.breakdown.push(`Advance Repayment: Skipped (would result in negative payment)`);
        }
      }
    }

    console.log(`💰 Calculation complete:`, calculation);
    return calculation;
  } catch (error) {
    console.error('❌ Error calculating staff earnings:', error);
    throw error;
  }
}

/**
 * Calculate salary payment based on period and base logic
 */
async function calculateSalaryPayment(
  staff: StaffDocument,
  periodStart: string,
  periodEnd: string
): Promise<PaymentCalculation> {
  const calculation: PaymentCalculation = {
    baseAmount: 0,
    shiftAmount: 0,
    bonusAmount: 0,
    deductionAmount: 0,
    advanceDeduction: 0,
    finalAmount: 0,
    breakdown: []
  };

  // Calculate base salary for the period
  if (staff.paymentConfig.type === 'MONTHLY') {
    calculation.baseAmount = staff.paymentConfig.baseSalary;
    calculation.breakdown.push(`Monthly Base Salary: ${calculation.baseAmount.toLocaleString()} DZD`);
  } else if (staff.paymentConfig.type === 'DAILY') {
    const startDate = new Date(periodStart);
    const endDate = new Date(periodEnd);
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    calculation.baseAmount = staff.paymentConfig.baseSalary * days;
    calculation.breakdown.push(`Daily Rate: ${staff.paymentConfig.baseSalary.toLocaleString()} × ${days} days = ${calculation.baseAmount.toLocaleString()} DZD`);
  } else if (staff.paymentConfig.type === 'WEEKLY') {
    const startDate = new Date(periodStart);
    const endDate = new Date(periodEnd);
    const weeks = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
    calculation.baseAmount = staff.paymentConfig.baseSalary * weeks;
    calculation.breakdown.push(`Weekly Rate: ${staff.paymentConfig.baseSalary.toLocaleString()} × ${weeks} weeks = ${calculation.baseAmount.toLocaleString()} DZD`);
  }

  // Calculate shift earnings if applicable
  if (staff.paymentConfig.type !== 'PER_SHIFT' && (staff.paymentConfig.shiftRate || staff.paymentConfig.shiftRates)) {
    const attendanceRecords = await getStaffAttendanceRecords(staff.id, periodStart, periodEnd);
    const workingRecords = attendanceRecords.filter(r => r.status === 'present' && r.shiftId);
    
    let shiftTotal = 0;
    const shiftCounts: Record<string, number> = {};
    
    workingRecords.forEach(record => {
      if (record.shiftId) {
        shiftCounts[record.shiftId] = (shiftCounts[record.shiftId] || 0) + 1;
      }
    });

    Object.entries(shiftCounts).forEach(([shiftId, count]) => {
      const rate = staff.paymentConfig.shiftRates?.[shiftId] || staff.paymentConfig.shiftRate || 0;
      const amount = rate * count;
      shiftTotal += amount;
      calculation.breakdown.push(`Shift ${shiftId}: ${rate.toLocaleString()} × ${count} = ${amount.toLocaleString()} DZD`);
    });

    calculation.shiftAmount = shiftTotal;
  }

  // Apply base logic
  const baseLogic = staff.paymentConfig.baseLogic || 'ADDITIVE';
  if (baseLogic === 'ADDITIVE') {
    calculation.finalAmount = calculation.baseAmount + calculation.shiftAmount;
    if (calculation.shiftAmount > 0) {
      calculation.breakdown.push(`Base Logic: ADDITIVE (${calculation.baseAmount.toLocaleString()} + ${calculation.shiftAmount.toLocaleString()})`);
    }
  } else if (baseLogic === 'MINIMUM') {
    calculation.finalAmount = Math.max(calculation.baseAmount, calculation.shiftAmount);
    calculation.breakdown.push(`Base Logic: MINIMUM (max of ${calculation.baseAmount.toLocaleString()} and ${calculation.shiftAmount.toLocaleString()})`);
  } else if (baseLogic === 'NONE') {
    calculation.finalAmount = calculation.shiftAmount;
    calculation.breakdown.push(`Base Logic: NONE (shift-only payment)`);
  }

  return calculation;
}

/**
 * Calculate shift-based payment
 */
async function calculateShiftPayment(
  staff: StaffDocument,
  shiftData: ShiftPaymentData
): Promise<PaymentCalculation> {
  const calculation: PaymentCalculation = {
    baseAmount: 0,
    shiftAmount: 0,
    bonusAmount: 0,
    deductionAmount: 0,
    advanceDeduction: 0,
    finalAmount: 0,
    breakdown: []
  };

  // Calculate shift amounts
  let shiftTotal = 0;
  shiftData.shiftBreakdown.forEach(shift => {
    shiftTotal += shift.amount;
    calculation.breakdown.push(`${shift.shiftName}: ${shift.rate.toLocaleString()} × ${shift.count} = ${shift.amount.toLocaleString()} DZD`);
  });

  calculation.shiftAmount = shiftTotal;
  calculation.finalAmount = shiftTotal;

  return calculation;
}

// ❌ REMOVED: createStaffPayment() - 180 lines
// This function has been replaced by the new balance system:
// - Use createPaymentSnapshot() from new-staff-balance-service.ts for salary payments
// - Use createPerShiftPaymentSnapshot() for per-shift payments
// - Use addBonus(), addDeduction(), addAdvance() for balance entries

// ❌ REMOVED: getExpenseCategory() - Only used by removed createStaffPayment()

/**
 * Convert PaymentDocument to legacy Payment interface for UI compatibility
 */
function convertPaymentDocument(doc: PaymentDocument): Payment {
  return {
    id: doc._id,
    staffId: doc.staffId,
    staffName: '', // Will be filled by UI component
    amount: doc.amount,
    currency: 'DZD',
    paymentDate: doc.paymentDate,
    paymentType: doc.paymentType.toLowerCase() as 'salary' | 'bonus' | 'advance' | 'deduction',
    period: doc.periodStart && doc.periodEnd ? {
      startDate: doc.periodStart,
      endDate: doc.periodEnd
    } : undefined,
    notes: doc.notes || '',
    createdAt: doc.createdAt
  };
}

/**
 * Get staff payments (converted to legacy format)
 */
export async function getStaffPayments(staffId: string): Promise<Payment[]> {
  try {
    const payments = await dbGetStaffPayments(staffId);
    return payments.map(convertPaymentDocument);
  } catch (error) {
    console.error('Error getting staff payments:', error);
  return [];
}
}

/**
 * Get staff advance balance
 */
export { getStaffAdvanceBalance };

/**
 * Get staff attendance records
 */
export async function getStaffAttendance(
  staffId: string,
  startDate: string,
  endDate: string
): Promise<AttendanceRecord[]> {
  try {
    const dbRecords = await getStaffAttendanceRecords(staffId, startDate, endDate);
    // Convert database records to UI format
    return dbRecords.map(record => ({
      id: record.id,
      staffId: staffId,
      staffName: '', // Will be filled by UI component
      date: record.date,
      shiftId: record.shiftId || '',
      shiftName: record.shiftName || '',
      attended: record.status === 'present',
      status: record.status === 'absent' ? 'late' : record.status as 'present' | 'late',
      notes: record.notes,
      isPaid: record.isPaid
    }));
  } catch (error) {
    console.error('Error getting staff attendance:', error);
  return [];
}
}

/**
 * Calculate unpaid balance
 */
export { calculateUnpaidBalance };

/**
 * Get period payment status
 */
export { getPeriodPaymentStatus };

// ❌ REMOVED: makePayment() - Legacy function that used removed createStaffPayment()
// Use the new balance system functions instead

/**
 * Delete payment
 */
export async function deletePayment(paymentId: string): Promise<void> {
  try {
    const success = await dbDeletePayment(paymentId);
    if (!success) {
      throw new Error(`Failed to delete payment ${paymentId}`);
    }
  } catch (error) {
    console.error('Error deleting payment:', error);
    throw error;
  }
}

/**
 * ADANCED PAYMENT UTILITIES
 */

/**
 * Validate payment request
 */
export async function validatePaymentRequest(
  staffId: string,
  paymentType: string,
  amount: number,
  periodStart?: string,
  periodEnd?: string
): Promise<{ valid: boolean; errors: string[] }> {
  const errors: string[] = [];

  try {
    // VULNERABILITY FIX: Enhanced input validation
    if (!staffId || typeof staffId !== 'string') {
      errors.push('Valid staff ID is required');
    }
    
    if (!paymentType || typeof paymentType !== 'string') {
      errors.push('Valid payment type is required');
    }
    
    if (typeof amount !== 'number' || isNaN(amount) || !Number.isFinite(amount)) {
      errors.push('Amount must be a valid finite number');
    }

    // Basic validations
    if (amount <= 0) {
      errors.push('Payment amount must be greater than 0');
    }
    
    if (amount > 10000000) { // 10 million DA limit
      errors.push('Payment amount exceeds maximum allowed limit');
    }

    // Get staff member
    const staff = await getStaffMember(staffId);
    if (!staff) {
      errors.push(`Staff member ${staffId} not found`);
      return { valid: false, errors };
    }

    // VULNERABILITY FIX: Stricter payment type validation
    const validPaymentTypes = ['SALARY', 'SHIFT_PAYMENT', 'ADVANCE', 'BONUS', 'DEDUCTION'];
    if (!validPaymentTypes.includes(paymentType)) {
      errors.push(`Invalid payment type: ${paymentType}. Must be one of: ${validPaymentTypes.join(', ')}`);
    }

    // Enhanced payment type compatibility checks
    if (paymentType === 'SALARY') {
      if (staff.paymentConfig.type === 'PER_SHIFT') {
        errors.push('Cannot create salary payment for per-shift staff. Use SHIFT_PAYMENT instead.');
      }
      if (!periodStart || !periodEnd) {
        errors.push('Period start and end dates are required for salary payments');
      }
    }

    if (paymentType === 'SHIFT_PAYMENT') {
      if (staff.paymentConfig.type !== 'PER_SHIFT') {
        errors.push('Cannot create shift payment for salary-based staff. Use SALARY instead.');
      }
    }

    // Validate advance limits with enhanced checks
    if (paymentType === 'ADVANCE') {
      try {
        const currentBalance = await getStaffAdvanceBalance(staffId);
        const maxAdvance = staff.paymentConfig.advanceRepayment?.maxAmount || 10000;
        
        if (currentBalance < 0) {
          errors.push('Staff has negative balance - cannot issue additional advances');
        }
        
        if (currentBalance + amount > maxAdvance) {
          errors.push(`Advance would exceed maximum limit of ${maxAdvance.toLocaleString()} DZD (current: ${currentBalance.toLocaleString()} DZD)`);
        }
      } catch (advanceError) {
        errors.push('Error checking advance balance - please try again');
      }
    }

    // Validate period dates if provided
    if (periodStart && periodEnd) {
      const startDate = new Date(periodStart);
      const endDate = new Date(periodEnd);
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        errors.push('Invalid period dates provided');
      } else if (startDate >= endDate) {
        errors.push('Period start date must be before end date');
      }
    }

  } catch (error) {
    errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return { valid: errors.length === 0, errors };
}

/**
 * Get payment summary for a staff member
 */
export async function getStaffPaymentSummary(
  staffId: string,
  year?: number
): Promise<{
  totalPaid: number;
  totalAdvances: number;
  totalDeductions: number;
  advanceBalance: number;
  monthlyBreakdown: Record<string, number>;
  paymentTypeBreakdown: Record<string, number>;
}> {
  try {
    const currentYear = year || new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    const endDate = `${currentYear}-12-31`;
    
    const payments = await getStaffPaymentsByDateRange(staffId, startDate, endDate);
    const advanceBalance = await getStaffAdvanceBalance(staffId);
    
    const summary = {
      totalPaid: 0,
      totalAdvances: 0,
      totalDeductions: 0,
      advanceBalance,
      monthlyBreakdown: {} as Record<string, number>,
      paymentTypeBreakdown: {} as Record<string, number>
    };

    payments.forEach(payment => {
      const month = format(new Date(payment.paymentDate), 'yyyy-MM');
      const type = payment.paymentType;

      // Monthly breakdown
      summary.monthlyBreakdown[month] = (summary.monthlyBreakdown[month] || 0) + payment.amount;
      
      // Type breakdown
      summary.paymentTypeBreakdown[type] = (summary.paymentTypeBreakdown[type] || 0) + payment.amount;
      
      // Totals
      if (type === 'ADVANCE') {
        summary.totalAdvances += payment.amount;
      } else if (type === 'DEDUCTION') {
        summary.totalDeductions += payment.amount;
      } else {
        summary.totalPaid += payment.amount;
      }
    });

    return summary;
  } catch (error) {
    console.error('Error getting payment summary:', error);
    throw error;
  }
}

/**
 * PERIOD TRACKING UTILITIES
 */

/**
 * Generate payment periods for a staff member
 */
export async function getStaffPaymentPeriods(
  staffId: string,
  monthsBack: number = 6,
  monthsForward: number = 3
): Promise<PaymentPeriod[]> {
  try {
    const staff = await getStaffMember(staffId);
    if (!staff) {
      throw new Error(`Staff member ${staffId} not found`);
    }

    const periods: PaymentPeriod[] = [];
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // Generate periods based on payment type
    if (staff.paymentConfig.type === 'MONTHLY') {
      // Generate monthly periods
      for (let i = -monthsBack; i <= monthsForward; i++) {
        const periodDate = new Date(currentYear, currentMonth + i, 1);
        const year = periodDate.getFullYear();
        const month = periodDate.getMonth();
        
        const startDate = new Date(year, month, 1);
        const endDate = new Date(year, month + 1, 0); // Last day of month
        
        const periodId = `${year}-${String(month + 1).padStart(2, '0')}`;
        const periodLabel = startDate.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' });
        
        // Check payment status
        const paymentStatus = await getPeriodPaymentStatus(staffId, 
          startDate.toISOString().split('T')[0], 
          endDate.toISOString().split('T')[0]
        );
        
        let status: 'paid' | 'overdue' | 'upcoming';
        if (paymentStatus.isPeriodFullyPaid) {
          status = 'paid';
        } else if (endDate < today) {
          status = 'overdue';
        } else {
          status = 'upcoming';
        }
        
        periods.push({
          id: periodId,
          label: periodLabel,
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          status,
          amount: paymentStatus.paidAmount || undefined
        });
      }
    } else if (staff.paymentConfig.type === 'WEEKLY') {
      // Generate weekly periods
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Start of current week
      
      for (let i = -monthsBack * 4; i <= monthsForward * 4; i++) {
        const weekStart = new Date(startOfWeek);
        weekStart.setDate(startOfWeek.getDate() + (i * 7));
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        
        const periodId = `${weekStart.getFullYear()}-W${Math.ceil(weekStart.getDate() / 7)}`;
        const periodLabel = `Semaine du ${weekStart.toLocaleDateString('fr-FR')}`;
        
        // Check payment status
        const paymentStatus = await getPeriodPaymentStatus(staffId,
          weekStart.toISOString().split('T')[0],
          weekEnd.toISOString().split('T')[0]
        );
        
        let status: 'paid' | 'overdue' | 'upcoming';
        if (paymentStatus.isPeriodFullyPaid) {
          status = 'paid';
        } else if (weekEnd < today) {
          status = 'overdue';
        } else {
          status = 'upcoming';
        }
        
        periods.push({
          id: periodId,
          label: periodLabel,
          startDate: weekStart.toISOString().split('T')[0],
          endDate: weekEnd.toISOString().split('T')[0],
          status,
          amount: paymentStatus.paidAmount || undefined
        });
      }
    }

    return periods.sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime());
  } catch (error) {
    console.error('Error generating payment periods:', error);
    return [];
  }
}

/**
 * Get period status summary for a staff member
 */
export async function getStaffPeriodStatus(staffId: string): Promise<PeriodStatus> {
  try {
    const periods = await getStaffPaymentPeriods(staffId, 12, 1);
    
    const paidPeriods: string[] = [];
    const duePeriods: string[] = [];
    let nextDue: string | undefined;

    periods.forEach(period => {
      if (period.status === 'paid') {
        paidPeriods.push(period.label);
      } else if (period.status === 'overdue') {
        duePeriods.push(period.label);
      } else if (period.status === 'upcoming' && !nextDue) {
        nextDue = period.label;
      }
    });

    return {
      paidPeriods: paidPeriods.reverse(), // Most recent first
      duePeriods: duePeriods.reverse(),
      nextDue
    };
  } catch (error) {
    console.error('Error getting period status:', error);
    return { paidPeriods: [], duePeriods: [] };
  }
}

/**
 * Get next unpaid period for a staff member
 */
export async function getNextUnpaidPeriod(staffId: string): Promise<PaymentPeriod | null> {
  try {
    const periods = await getStaffPaymentPeriods(staffId, 3, 1);
    
    // Find the earliest unpaid period
    const unpaidPeriods = periods.filter(p => p.status !== 'paid');
    if (unpaidPeriods.length === 0) return null;
    
    // Sort by date and return the earliest
    unpaidPeriods.sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
    return unpaidPeriods[0];
  } catch (error) {
    console.error('Error getting next unpaid period:', error);
    return null;
  }
}

/**
 * Update payment note (always allowed)
 */
export async function updatePaymentNote(paymentId: string, note: string): Promise<void> {
  try {
    console.log(`📝 Updating payment note for payment ${paymentId}`);
    await dbUpdatePaymentNote(paymentId, note);
    console.log(`✅ Payment note updated successfully`);
  } catch (error) {
    console.error('❌ Error updating payment note:', error);
    throw error;
  }
}

/**
 * Reverse a payment (create mirror entry to cancel out original)
 */
export async function reversePayment(
  paymentId: string, 
  reason: string
): Promise<PaymentDocument> {
  try {
    console.log(`↩️ Reversing payment ${paymentId}: ${reason}`);
    
    // Get original payment
    const originalPayment = await getPaymentById(paymentId);
    if (!originalPayment) {
      throw new Error(`Payment ${paymentId} not found`);
    }
    
    if (originalPayment.metadata?.voided) {
      throw new Error('Payment is already voided');
    }
    
    // ❌ DISABLED: Mirror payment creation - requires createStaffPayment() which was removed
    // TODO: Implement void payment functionality in new balance system if needed
    throw new Error('Payment voiding is temporarily disabled - use new balance system for payment management');
    
    // Mark original as voided and link to mirror
    await updatePaymentMetadata(paymentId, {
      voided: true,
      voidedAt: new Date().toISOString(),
      voidReason: reason,
      mirrorPaymentId: mirrorPayment._id
    });
    
    // Mark mirror with reference to original
    await updatePaymentMetadata(mirrorPayment._id, {
      isReversal: true,
      reversalOf: paymentId,
      reversalReason: reason
    });
    
    console.log(`✅ Payment reversed: ${paymentId} → ${mirrorPayment._id}`);
    return mirrorPayment;
    
  } catch (error) {
    console.error('❌ Error reversing payment:', error);
    throw error;
  }
}

/**
 * Add adjustment to a payment (small +/- correction)
 */
export async function addPaymentAdjustment(
  paymentId: string,
  adjustmentAmount: number,
  reason: string
): Promise<PaymentDocument> {
  try {
    console.log(`➕ Adding adjustment to payment ${paymentId}: ${adjustmentAmount} (${reason})`);
    
    // Get original payment
    const originalPayment = await getPaymentById(paymentId);
    if (!originalPayment) {
      throw new Error(`Payment ${paymentId} not found`);
    }
    
    if (originalPayment.metadata?.voided) {
      throw new Error('Cannot adjust a voided payment');
    }
    
    if (adjustmentAmount === 0) {
      throw new Error('Adjustment amount cannot be zero');
    }
    
    // ❌ DISABLED: Adjustment payment creation - requires createStaffPayment() which was removed
    // TODO: Implement payment adjustment functionality in new balance system if needed
    throw new Error('Payment adjustment is temporarily disabled - use new balance system for payment management');
    
    // Mark adjustment with reference to original
    await updatePaymentMetadata(adjustmentPayment._id, {
      isAdjustment: true,
      adjustsPaymentId: paymentId,
      adjustmentReason: reason,
      adjustmentAmount: adjustmentAmount
    });
    
    // Add adjustment reference to original payment
    const originalAdjustments = originalPayment.metadata?.adjustmentIds || [];
    await updatePaymentMetadata(paymentId, {
      adjustmentIds: [...originalAdjustments, adjustmentPayment._id]
    });
    
    console.log(`✅ Adjustment added: ${adjustmentPayment._id} (${adjustmentAmount > 0 ? '+' : ''}${adjustmentAmount})`);
    return adjustmentPayment;
    
  } catch (error) {
    console.error('❌ Error adding payment adjustment:', error);
    throw error;
  }
}

/**
 * Update amount for PENDING payments only (safe direct edit)
 */
export async function updatePendingPaymentAmount(
  paymentId: string,
  newAmount: number,
  reason?: string
): Promise<PaymentDocument> {
  try {
    console.log(`✏️ Updating pending payment amount ${paymentId}: ${newAmount}`);
    
    // Get payment and validate it's pending
    const payment = await getPaymentById(paymentId);
    if (!payment) {
      throw new Error(`Payment ${paymentId} not found`);
    }
    
    if (payment.status !== 'PENDING') {
      throw new Error('Can only edit amounts for PENDING payments. Use adjustment or reversal for completed payments.');
    }
    
    if (payment.metadata?.voided) {
      throw new Error('Cannot edit a voided payment');
    }
    
    // Store original amount for audit
    const originalAmount = payment.amount;
    
    // Update the payment amount directly (safe since it's pending)
    const updatedPayment = {
      ...payment,
      amount: payment.paymentType === 'DEDUCTION' ? -Math.abs(newAmount) : newAmount,
      updatedAt: new Date().toISOString(),
      metadata: {
        ...payment.metadata,
        edited: true,
        editedAt: new Date().toISOString(),
        originalAmount: originalAmount,
        editReason: reason || 'Amount correction before approval'
      }
    };
    
    // Save updated payment
    const result = await databaseV4.putDoc(updatedPayment);
    updatedPayment._rev = result.rev;
    
    console.log(`✅ Pending payment amount updated from ${originalAmount} to ${newAmount}`);
    return updatedPayment;
    
  } catch (error) {
    console.error('❌ Error updating pending payment amount:', error);
    throw error;
  }
}

// Helper function to get payment by ID
async function getPaymentById(paymentId: string): Promise<PaymentDocument | null> {
  try {
    const doc = await databaseV4.getDoc(paymentId);
    return doc as PaymentDocument;
  } catch (error) {
    if (error instanceof Error && 'status' in error && error.status === 404) {
      return null;
    }
    throw error;
  }
}

/**
 * Update the next payment due date for a staff member
 */
export async function updateNextPaymentDueDate(
  staffId: string,
  nextDueDate: string
): Promise<void> {
  try {
    const staff = await getStaffMember(staffId);
    if (!staff) {
      throw new Error(`Staff member with ID ${staffId} not found`);
    }

    // Ensure date is stored consistently without timezone shifts
    // The incoming string should be 'YYYY-MM-DD'
    const parts = nextDueDate.split('-');
    if (parts.length !== 3) {
      throw new Error("Invalid date format. Expected YYYY-MM-DD.");
    }
    const utcDate = new Date(Date.UTC(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2])));
    
    await updateStaffMember(staffId, {
      paymentConfig: {
        ...staff.paymentConfig,
        nextPaymentDueDate: utcDate.toISOString(),
      },
    });

    console.log(`📅 Next payment due date for staff ${staffId} updated to: ${utcDate.toISOString()}`);
  } catch (error) {
    console.error('❌ Error updating next payment due date:', error);
    throw error;
  }
}

/**
 * Get the next payment due date for a staff member
 */
export async function getNextPaymentDueDate(staffId: string): Promise<string | null> {
  console.log('🔍 getNextPaymentDueDate called for staff:', staffId);
  
  try {
    const staff = await getStaffMember(staffId);
    console.log('👤 Staff found:', staff?.name, 'Due date:', staff?.paymentConfig?.nextPaymentDueDate);
    
    const result = staff?.paymentConfig?.nextPaymentDueDate || null;
    console.log('📅 Returning due date:', result);
    
    return result;
  } catch (error) {
    console.error('Error getting next payment due date:', error);
    return null;
  }
}

// ❌ REMOVED: completePendingAdjustments() - 117 lines
// This function has been replaced by the new balance system's automatic balance marking
// The new system handles balance completion when creating payment snapshots

/**
 * Get pending bonuses for a staff member (bonuses not yet applied to salary)
 */
export async function getStaffPendingBonuses(staffId: string): Promise<number> {
  try {
    const { getStaffPayments: dbGetStaffPayments } = await import('../db/v4');
    const payments = await dbGetStaffPayments(staffId);
    
    // Only count PENDING bonuses
    const bonusTotal = payments
      .filter(p => p.paymentType === 'BONUS' && p.status === 'PENDING')
      .reduce((sum, p) => sum + p.amount, 0);
    return bonusTotal;
  } catch (error) {
    console.error('Error getting pending bonuses:', error);
    return 0;
  }
}

/**
 * Get pending deductions for a staff member (deductions not yet applied to salary)
 */
export async function getStaffPendingDeductions(staffId: string): Promise<number> {
  try {
    const { getStaffPayments: dbGetStaffPayments } = await import('../db/v4');
    const payments = await dbGetStaffPayments(staffId);
    
    // Only count PENDING deductions
    const deductionTotal = payments
      .filter(p => p.paymentType === 'DEDUCTION' && p.status === 'PENDING')
      .reduce((sum, p) => sum + Math.abs(p.amount), 0);
    return deductionTotal;
  } catch (error) {
    console.error('Error getting pending deductions:', error);
    return 0;
  }
}

/**
 * Get pending adjustment IDs for a staff member
 */
export async function getStaffPendingAdjustmentIds(staffId: string): Promise<{
  bonusIds: string[];
  deductionIds: string[];
}> {
  try {
    const { getStaffPayments: dbGetStaffPayments } = await import('../db/v4');
    const payments = await dbGetStaffPayments(staffId);
    
    const bonusIds = payments
      .filter(p => p.paymentType === 'BONUS' && p.status === 'PENDING')
      .map(p => p._id);
      
    const deductionIds = payments
      .filter(p => p.paymentType === 'DEDUCTION' && p.status === 'PENDING')
      .map(p => p._id);

    return { bonusIds, deductionIds };
  } catch (error) {
    console.error('Error getting pending adjustment IDs:', error);
    return { bonusIds: [], deductionIds: [] };
  }
}

/**
 * Get complete financial balance for a staff member
 */
export async function getStaffFinancialBalance(staffId: string): Promise<{
  advanceBalance: number;
  pendingBonuses: number;
  pendingDeductions: number;
}> {
  try {
    const [advanceBalance, pendingBonuses, pendingDeductions] = await Promise.all([
      getStaffAdvanceBalance(staffId),
      getStaffPendingBonuses(staffId),
      getStaffPendingDeductions(staffId)
    ]);

    return {
      advanceBalance,
      pendingBonuses,
      pendingDeductions
    };
  } catch (error) {
    console.error('Error getting staff financial balance:', error);
    return {
      advanceBalance: 0,
      pendingBonuses: 0,
      pendingDeductions: 0
    };
  }
}

/**
 * Simplified Payment Snapshot for minimal history display
 */
export interface PaymentSnapshot {
  id: string;
  date: string;
  type: 'Salaire' | 'Prime' | 'Retenue' | 'Avance' | 'Paiement par shift';
  base: number;
  bonus: number;
  deduction: number;
  netPaid: number;
}

export async function getStaffPaymentHistory(staffId: string): Promise<PaymentSnapshot[]> {
  console.log(`Fetching simplified payment history for staff ${staffId}`);
  const payments = await getConsolidatedStaffPayments(staffId);

  const history: PaymentSnapshot[] = payments.map(p => {
    if ('isConsolidated' in p && p.isConsolidated) {
      const consolidated = p as ConsolidatedPayment;
      return {
        id: consolidated._id,
        date: consolidated.paymentDate,
        type: 'Salaire',
        base: consolidated.consolidatedComponents.baseSalary || consolidated.consolidatedComponents.shiftEarnings || 0,
        bonus: consolidated.consolidatedComponents.bonuses.reduce((acc, b) => acc + b.amount, 0),
        deduction: consolidated.consolidatedComponents.deductions.reduce((acc, d) => acc + d.amount, 0),
        netPaid: consolidated.consolidatedComponents.netAmount,
      };
    } else {
      const simple = p as PaymentDocument;
      const amount = simple.amount;
      switch (simple.paymentType) {
        case 'SALARY':
          return {
            id: simple._id!,
            date: simple.paymentDate,
            type: 'Salaire',
            base: amount,
            bonus: 0,
            deduction: 0,
            netPaid: amount,
          };
        case 'BONUS':
          return {
            id: simple._id!,
            date: simple.paymentDate,
            type: 'Prime',
            base: 0,
            bonus: amount,
            deduction: 0,
            netPaid: amount,
          };
        case 'DEDUCTION':
          return {
            id: simple._id!,
            date: simple.paymentDate,
            type: 'Retenue',
            base: 0,
            bonus: 0,
            deduction: Math.abs(amount), // store as positive for display
            netPaid: amount, // already negative in DB
          };
        case 'ADVANCE':
          return {
            id: simple._id!,
            date: simple.paymentDate,
            type: 'Avance',
            base: 0,
            bonus: 0,
            deduction: Math.abs(amount), // treat like deduction
            netPaid: -Math.abs(amount), // cash outflow
          };
        case 'SHIFT_PAYMENT':
          return {
            id: simple._id!,
            date: simple.paymentDate,
            type: 'Paiement par shift',
            base: amount,
            bonus: 0,
            deduction: 0,
            netPaid: amount,
          };
        default:
          return {
            id: simple._id!,
            date: simple.paymentDate,
            type: simple.paymentType,
            base: amount,
            bonus: 0,
            deduction: 0,
            netPaid: amount,
          };
      }
    }
  });

  // Sort by date descending
  history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  console.log(`Simplified history ready for staff ${staffId}`, history);
  return history;
}


/**
 * Consolidated Payment Interface for UI display
 */
export interface ConsolidatedPayment {
  _id: string;
  _rev?: string;
  type: 'payment';
  staffId: string;
  amount: number;
  paymentType: 'SALARY' | 'SHIFT_PAYMENT' | 'ADVANCE';
  paymentDate: string;
  periodStart?: string;
  periodEnd?: string;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  isConsolidated: true;
  consolidatedComponents: {
    baseSalary?: number;
    shiftEarnings?: number;
    bonuses: Array<{ id: string; amount: number; notes?: string }>;
    deductions: Array<{ id: string; amount: number; notes?: string }>;
    netAmount: number;
  };
  metadata?: {
    // ✅ Simplified: Advance repayments are now handled as separate ADVANCE payments
    consolidationDate?: string;
    adjustmentsCleared?: {
      bonuses: number;
      deductions: number;
      totalBonusAmount: number;
      totalDeductionAmount: number;
    };
    [key: string]: any;
  };
}

/**
 * Get consolidated staff payments - merges salary with related bonuses, deductions, and advance repayments
 */
export async function getConsolidatedStaffPayments(staffId: string): Promise<(ConsolidatedPayment | PaymentDocument)[]> {
  try {
    console.log(`🔍 Getting consolidated payments for staff: ${staffId}`);
    
    const { getStaffPayments: dbGetStaffPayments } = await import('../db/v4');
    const allPayments = await dbGetStaffPayments(staffId);
    
    if (!allPayments || allPayments.length === 0) {
      console.log(`📭 No payments found for staff ${staffId}`);
      return [];
    }
    
    console.log(`📊 Processing ${allPayments.length} total payments for consolidation`);
    
    // Sort by date descending for consistent ordering
    const sortedPayments = allPayments.sort((a, b) => {
      const dateA = new Date(a.paymentDate).getTime();
      const dateB = new Date(b.paymentDate).getTime();
      return dateB - dateA;
    });
    
    const consolidatedPayments: (ConsolidatedPayment | PaymentDocument)[] = [];
    const processedIds = new Set<string>();
    
    for (const payment of sortedPayments) {
      if (processedIds.has(payment._id)) {
        console.log(`⏭️ Skipping already processed payment: ${payment._id}`);
        continue;
      }
      
      // Process COMPLETED salary/shift payments for consolidation
      if ((payment.paymentType === 'SALARY' || payment.paymentType === 'SHIFT_PAYMENT') && payment.status === 'COMPLETED') {
        console.log(`🔗 Processing consolidated payment: ${payment._id} (${payment.paymentType})`);
        
        // Find all linked bonuses and deductions
        const linkedPayments = sortedPayments.filter(p => 
          p.metadata?.linkedPayrollId === payment._id && 
          (p.paymentType === 'BONUS' || p.paymentType === 'DEDUCTION') &&
          p.status === 'COMPLETED'
        );
        
        console.log(`   Found ${linkedPayments.length} linked adjustments`);
        
        // Separate bonuses and deductions
        const bonuses = linkedPayments.filter(p => p.paymentType === 'BONUS');
        const deductions = linkedPayments.filter(p => p.paymentType === 'DEDUCTION');
        
        const totalBonuses = bonuses.reduce((sum, p) => sum + (p.amount || 0), 0);
        const totalDeductions = deductions.reduce((sum, p) => sum + Math.abs(p.amount || 0), 0);
        
        // ✅ Simplified: No complex advance repayment metadata
        // Calculate component amounts with fallbacks
        const baseSalary = payment.metadata?.calculationBreakdown?.baseSalary || payment.amount || 0;
        const shiftEarnings = payment.metadata?.calculationBreakdown?.shiftEarnings || 0;
        const netAmount = baseSalary + shiftEarnings + totalBonuses - totalDeductions;
        
        const consolidatedPayment: ConsolidatedPayment = {
          _id: payment._id,
          _rev: payment._rev,
          type: 'payment',
          staffId: payment.staffId,
          amount: netAmount,
          paymentType: payment.paymentType,
          paymentDate: payment.paymentDate,
          periodStart: payment.periodStart,
          periodEnd: payment.periodEnd,
          status: payment.status,
          notes: payment.notes,
          isConsolidated: true,
          consolidatedComponents: {
            baseSalary,
            shiftEarnings,
            bonuses: bonuses.map(p => ({ 
              id: p._id, 
              amount: p.amount || 0, 
              notes: p.notes || 'Bonus' 
            })),
            deductions: deductions.map(p => ({ 
              id: p._id, 
              amount: Math.abs(p.amount || 0), 
              notes: p.notes || 'Deduction' 
            })),
            netAmount
          },
          metadata: payment.metadata
        };
        
        consolidatedPayments.push(consolidatedPayment);
        
        // Mark all processed payments to avoid duplicates
        processedIds.add(payment._id);
        linkedPayments.forEach(p => processedIds.add(p._id));
        
        console.log(`   ✅ Consolidated payment created with net amount: ${netAmount}`);
        
      } else if (payment.status === 'PENDING' && (payment.paymentType === 'SALARY' || payment.paymentType === 'SHIFT_PAYMENT')) {
        // Show pending salary/shift payments (not yet consolidated)
        console.log(`⏳ Adding pending payment: ${payment._id}`);
        consolidatedPayments.push(payment);
        processedIds.add(payment._id);
        
      } else if (payment.paymentType === 'ADVANCE') {
        // ✅ Always show advance payments (both positive and negative)
        console.log(`💰 Adding advance payment: ${payment._id} (${payment.amount > 0 ? 'Given' : 'Repaid'})`);
        consolidatedPayments.push(payment);
        processedIds.add(payment._id);
        
      } else if (!processedIds.has(payment._id)) {
        // Log skipped payments for debugging
        console.log(`⏭️ Skipping payment: ${payment._id} (${payment.paymentType}, ${payment.status})`);
        processedIds.add(payment._id);
      }
    }
    
    console.log(`✅ Consolidated ${consolidatedPayments.length} payments from ${allPayments.length} total payments`);
    return consolidatedPayments;
    
  } catch (error) {
    console.error(`❌ Error getting consolidated payments for staff ${staffId}:`, error);
    // Re-throw with more context
    throw new Error(`Failed to get consolidated payments for staff ${staffId}: ${error}`);
  }
}

// ❌ REMOVED: consolidateAllPendingPayments() - 100 lines
// This function has been replaced by createPaymentSnapshot() in the new balance system
// The new system handles balance consolidation automatically and more efficiently

/**
 * Update payment amount (Quick Fix - always available)
 */
export async function updatePaymentAmount(
  paymentId: string, 
  newAmount: number, 
  reason?: string
): Promise<PaymentDocument> {
  try {
    console.log(`💰 Quick fix: Updating payment amount for ${paymentId} to ${newAmount}`);
    
    // Get the payment
    const payment = await getPaymentById(paymentId);
    if (!payment) {
      throw new Error(`Payment ${paymentId} not found`);
    }

    // Cannot edit cancelled payments
    if (payment.status === 'CANCELLED') {
      throw new Error('Cannot edit cancelled payments');
    }

    // Update amount and add edit note
    const updatedPayment = {
      ...payment,
      amount: payment.paymentType === 'DEDUCTION' ? -Math.abs(newAmount) : newAmount,
      notes: reason ? `${payment.notes || ''}\n[Edited: ${reason}]`.trim() : payment.notes,
      metadata: {
        ...payment.metadata,
        edited: true,
        editedAt: new Date().toISOString(),
        originalAmount: payment.amount,
        editReason: reason
      },
      updatedAt: new Date().toISOString()
    };

    const result = await updatePayment(updatedPayment);
    
    console.log(`✅ Payment amount updated successfully`);
    return result;
  } catch (error) {
    console.error('❌ Error updating payment amount:', error);
    throw error;
  }
}

/**
 * Create payment correction (add/subtract from original)
 */
export async function createPaymentCorrection(
  originalPaymentId: string,
  correctionAmount: number,
  reason: string
): Promise<PaymentDocument> {
  try {
    console.log(`🔧 Creating correction for payment ${originalPaymentId}: ${correctionAmount}`);
    
    // Get original payment
    const originalPayment = await getPaymentById(originalPaymentId);
    if (!originalPayment) {
      throw new Error(`Original payment ${originalPaymentId} not found`);
    }

    // Create correction payment
    const correctionData = {
      staffId: originalPayment.staffId,
      amount: correctionAmount, // Can be positive or negative
      paymentType: correctionAmount > 0 ? originalPayment.paymentType : 'DEDUCTION' as const,
      paymentDate: new Date().toISOString(),
      status: 'COMPLETED' as const,
      notes: `Correction for payment ${originalPaymentId.split(':')[1]}: ${reason}`,
      metadata: {
        isCorrection: true,
        originalPaymentId,
        correctionReason: reason
      }
    };

    const correction = await createPayment(correctionData);

    // Link correction to original payment
    await updatePaymentMetadata(originalPaymentId, {
      hasCorrectionIds: [...(originalPayment.metadata?.hasCorrectionIds || []), correction._id]
    });

    console.log(`✅ Payment correction created: ${correction._id}`);
    return correction;
  } catch (error) {
    console.error('❌ Error creating payment correction:', error);
    throw error;
  }
}

/**
 * Void payment and optionally create replacement
 */
export async function voidPayment(
  paymentId: string,
  reason: string,
  replacementData?: {
    amount: number;
    notes?: string;
  }
): Promise<{ voidedPayment: PaymentDocument; replacementPayment?: PaymentDocument }> {
  try {
    console.log(`🚫 Voiding payment ${paymentId}`);
    
    // Get original payment
    const originalPayment = await getPaymentById(paymentId);
    if (!originalPayment) {
      throw new Error(`Payment ${paymentId} not found`);
    }

    // Update status to CANCELLED
    const voidedPayment = {
      ...originalPayment,
      status: 'CANCELLED' as const,
      notes: `${originalPayment.notes || ''}\n[VOID: ${reason}]`.trim(),
      metadata: {
        ...originalPayment.metadata,
        voided: true,
        voidedAt: new Date().toISOString(),
        voidReason: reason
      },
      updatedAt: new Date().toISOString()
    };

    const updatedVoid = await updatePayment(voidedPayment);

    let replacementPayment: PaymentDocument | undefined;

    // Create replacement if requested
    if (replacementData) {
      const replacementDataFull = {
        staffId: originalPayment.staffId,
        amount: originalPayment.paymentType === 'DEDUCTION' ? -Math.abs(replacementData.amount) : replacementData.amount,
        paymentType: originalPayment.paymentType,
        paymentDate: new Date().toISOString(),
        periodStart: originalPayment.periodStart,
        periodEnd: originalPayment.periodEnd,
        status: originalPayment.status,
        notes: replacementData.notes || `Replacement for voided payment ${paymentId.split(':')[1]}`,
        metadata: {
          isReplacement: true,
          replacesPaymentId: paymentId,
          originalAmount: originalPayment.amount
        }
      };

      replacementPayment = await createPayment(replacementDataFull);

      // Link replacement to voided payment
      await updatePaymentMetadata(paymentId, {
        replacementPaymentId: replacementPayment._id
      });
    }

    console.log(`✅ Payment voided successfully`);
    return { voidedPayment: updatedVoid, replacementPayment };
  } catch (error) {
    console.error('❌ Error voiding payment:', error);
    throw error;
  }
}

/**
 * Update payment document
 */
async function updatePayment(payment: PaymentDocument): Promise<PaymentDocument> {
  try {
    const result = await databaseV4.putDoc(payment);
    return { ...payment, _rev: result.rev };
  } catch (error) {
    console.error('Error updating payment:', error);
    throw error;
  }
}