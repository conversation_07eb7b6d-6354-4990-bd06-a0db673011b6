// Kitchen Queue Management Service
// Handles real-time queue tracking and item completion status
// Now uses PouchDB for persistent storage

import { Order, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import {
  StationQueue,
  ItemCompletionStatus
} from '@/lib/db/v4/schemas/queue-schema';
import {
  addOrderToQueue as dbAddOrderToQueue,
  registerItemCompletion as dbRegisterItemCompletion,
  completeItem as dbCompleteItem,
  getStationQueue as dbGetStationQueue,
  getAllStationQueues as dbGetAllStationQueues,
  cancelOrder as dbCancelOrder,
  completeOrder as dbCompleteOrder,
  getOrderQueuePosition as dbGetOrderQueuePosition,
  isItemCompleted as dbIsItemCompleted,
  clearCompletedOrders as dbClearCompletedOrders,
  resetAllQueues as dbResetAllQueues,
  createQueueIndexes
} from '@/lib/db/v4/operations/queue-ops';

// Legacy interfaces for backward compatibility
export interface QueueItem {
  orderId: string;
  orderNumber: string;
  stationId: string;
  items: OrderItem[];
  completedItemIds: string[];
  status: 'pending' | 'in-progress' | 'completed';
  createdAt: Date;
  estimatedTime?: number;
}

export type { StationQueue, ItemCompletionStatus };

class KitchenQueueService {
  private isInitialized = false;

  /**
   * Initialize the queue service and create indexes
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      console.log('🔧 [KitchenQueueService] Initializing queue indexes...');
      await createQueueIndexes();
      this.isInitialized = true;
      console.log('✅ [KitchenQueueService] Queue service initialized successfully');
    } catch (error) {
      console.error('❌ [KitchenQueueService] Failed to initialize queue service:', error);
      throw error;
    }
  }

  /**
   * Add order to station queue
   */
  async addOrderToQueue(order: Order, stationId: string, stationItems: OrderItem[]): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await dbAddOrderToQueue(order, stationId, stationItems);
      console.log(`📋 Added order ${order.id} to station ${stationId} queue (${stationItems.reduce((sum, item) => sum + item.quantity, 0)} items)`);
    } catch (error) {
      console.error(`❌ [addOrderToQueue] Error adding order ${order.id} to station ${stationId}:`, error);
      throw error;
    }
  }

  /**
   * Mark item as completed when barcode is scanned
   */
  async completeItem(barcode: string, scannedBy?: string): Promise<{
    success: boolean;
    message: string;
    orderCompleted: boolean;
    queuePosition?: number;
  }> {
    await this.ensureInitialized();
    
    try {
      return await dbCompleteItem(barcode, scannedBy);
    } catch (error) {
      console.error(`❌ [completeItem] Error completing item ${barcode}:`, error);
      return {
        success: false,
        message: `❌ Error processing barcode: ${error instanceof Error ? error.message : 'Unknown error'}`,
        orderCompleted: false
      };
    }
  }

  /**
   * Register item for completion tracking (called during print generation)
   */
  async registerItem(barcode: string, orderId: string, stationId: string, itemName: string): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await dbRegisterItemCompletion(barcode, orderId, stationId, itemName);
    } catch (error) {
      console.error(`❌ [registerItem] Error registering item ${barcode}:`, error);
      throw error;
    }
  }

  /**
   * Cancel an entire order, removing it from all queues and clearing item statuses.
   */
  async cancelOrder(orderId: string): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await dbCancelOrder(orderId);
      console.log(`Order ${orderId} removed from all kitchen queues.`);
    } catch (error) {
      console.error(`❌ [cancelOrder] Error cancelling order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Mark an entire order as completed/served, removing it from all queues.
   */
  async completeOrder(orderId: string): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await dbCompleteOrder(orderId);
      console.log(`Order ${orderId} marked as completed and removed from queues.`);
    } catch (error) {
      console.error(`❌ [completeOrder] Error completing order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Get current queue status for a station
   */
  async getStationQueue(stationId: string): Promise<StationQueue> {
    await this.ensureInitialized();
    
    try {
      return await dbGetStationQueue(stationId);
    } catch (error) {
      console.error(`❌ [getStationQueue] Error getting queue for station ${stationId}:`, error);
      throw error;
    }
  }

  /**
   * Get queue position for an order
   */
  async getOrderQueuePosition(orderId: string, stationId: string): Promise<number> {
    await this.ensureInitialized();
    
    try {
      return await dbGetOrderQueuePosition(orderId, stationId);
    } catch (error) {
      console.error(`❌ [getOrderQueuePosition] Error getting position for order ${orderId}:`, error);
      return 0;
    }
  }

  /**
   * Get all station queues
   */
  async getAllStationQueues(): Promise<Map<string, StationQueue>> {
    await this.ensureInitialized();
    
    try {
      return await dbGetAllStationQueues();
    } catch (error) {
      console.error(`❌ [getAllStationQueues] Error getting all station queues:`, error);
      throw error;
    }
  }

  /**
   * Clear completed orders from queue (cleanup)
   */
  async clearCompletedOrders(stationId: string): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await dbClearCompletedOrders(stationId);
    } catch (error) {
      console.error(`❌ [clearCompletedOrders] Error clearing completed orders for station ${stationId}:`, error);
      throw error;
    }
  }

  /**
   * Reset all queues (for testing/debugging)
   */
  async resetAllQueues(): Promise<void> {
    await this.ensureInitialized();
    
    try {
      await dbResetAllQueues();
    } catch (error) {
      console.error(`❌ [resetAllQueues] Error resetting all queues:`, error);
      throw error;
    }
  }

  /**
   * Check if an individual item is completed
   */
  async isItemCompleted(barcode: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      return await dbIsItemCompleted(barcode);
    } catch (error) {
      console.error(`❌ [isItemCompleted] Error checking item completion for ${barcode}:`, error);
      return false;
    }
  }

  /**
   * Ensure the service is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  // Legacy methods for backward compatibility (deprecated but maintained)
  
  /**
   * @deprecated Use async methods instead
   */
  registerItem_LEGACY(barcode: string, orderId: string, stationId: string, itemName: string): void {
    // Fire and forget for backward compatibility
    this.registerItem(barcode, orderId, stationId, itemName).catch(error => {
      console.error('❌ [registerItem_LEGACY] Error:', error);
    });
  }

  /**
   * Calculate estimated completion time
   */
  private calculateEstimatedTime(itemCount: number): number {
    // Base time per item (in minutes) + setup time
    const baseTimePerItem = 2; // 2 minutes per item
    const setupTime = 3; // 3 minutes setup time
    return (itemCount * baseTimePerItem) + setupTime;
  }

  /**
   * Calculate average completion time for a station
   */
  private calculateAverageTime(stationId: string): number {
    // TODO: Implement proper average time calculation based on completed orders
    return 0;
  }
}

// Export singleton instance
export const kitchenQueueService = new KitchenQueueService();
export default kitchenQueueService; 