// Empty stub for @capacitor/http to prevent build errors in static/electron builds
export const CapacitorHttp = {
  get: async () => {
    throw new Error('CapacitorHttp not available in static build');
  },
  post: async () => {
    throw new Error('CapacitorHttp not available in static build');
  },
  put: async () => {
    throw new Error('CapacitorHttp not available in static build');
  },
  delete: async () => {
    throw new Error('CapacitorHttp not available in static build');
  }
};