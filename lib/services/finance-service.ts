'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import {
  getAllCashTransactions,
  createCashTransaction,
  getAllOrders
} from '@/lib/db/v4';

// Type definitions for the finance service
export interface FinancialTransaction {
  id: string;
  type: 'sales' | 'manual_in' | 'manual_out';
  amount: number;
  relatedDocId?: string;
  description: string;
  time: string;
  performedBy: string;
  metadata?: any;
}

/**
 * V4 Cash Register Service
 * Provides cash transaction management with proper validation and error handling
 */
export function useCashRegister() {
  const [transactions, setTransactions] = useState<FinancialTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuth();

  const refreshData = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setTransactions([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Check if database is properly initialized
      const { databaseV4 } = await import('@/lib/db/v4/core/db-instance');
      
      if (!databaseV4.isInitialized || !databaseV4.getCurrentRestaurantId()) {
        console.warn('[FinanceService] Database not properly initialized, waiting...');
        
        try {
          const restaurantId = user.restaurantId;
          if (!restaurantId) {
            throw new Error('No restaurant ID available');
          }
          
          await databaseV4.waitForInitialization(restaurantId, 30000);
          console.log('[FinanceService] Database initialization completed');
        } catch (waitError) {
          console.error('[FinanceService] Database initialization failed:', waitError);
          setError('Database not ready - please refresh the page');
          setTransactions([]);
          return;
        }
      }

      const allTransactions = await getAllCashTransactions();
      console.log('🔍 Finance Service: Raw transactions from database:', allTransactions);
      
      setTransactions(
        Array.isArray(allTransactions)
          ? allTransactions.map((tx: any, i: number) => ({
              id: tx?._id || `tx_${i}_${Date.now()}`,
              type: tx?.transactionType || tx?.type || 'manual_in',
              amount: typeof tx?.amount === 'number' ? tx.amount : 0,
              relatedDocId: tx?.relatedDocId || '',
              description: tx?.description || '',
              time: tx?.time || new Date().toISOString(),
              performedBy: tx?.performedBy || 'Unknown',
              metadata: tx?.metadata
            }))
          : []
      );
      
      console.log('🔍 Finance Service: Processed transactions:', transactions.length);
    } catch (err) {
      console.error('[FinanceService] Error loading cash transactions:', err);
      setError('Failed to load cash transactions');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
      refreshData();
  }, [refreshData]);

  // Add cash transaction with comprehensive validation and sanitization
  const addCashTransaction = async (
    type: 'manual_in' | 'manual_out',
    amount: number,
    description: string,
    performedBy?: string
  ) => {
    if (!isAuthenticated || !user) {
      setError('Not authenticated or user not available');
      return false;
    }

    // Comprehensive amount validation
    if (typeof amount !== 'number' || isNaN(amount) || !isFinite(amount)) {
      setError('Transaction amount must be a valid number');
      return false;
    }
    
    if (amount <= 0) {
      setError('Transaction amount must be greater than zero');
      return false;
    }
    
    if (amount > 999999.99) {
      setError('Transaction amount cannot exceed 999,999.99');
      return false;
    }

    // Comprehensive description validation and sanitization
    if (!description || typeof description !== 'string') {
      setError('Transaction description is required');
      return false;
    }
    
    const sanitizedDescription = description.trim().replace(/[<>]/g, ''); // Basic XSS prevention
    
    if (sanitizedDescription.length === 0) {
      setError('Transaction description cannot be empty');
      return false;
    }
    
    if (sanitizedDescription.length > 500) {
      setError('Transaction description cannot exceed 500 characters');
      return false;
    }

    // Validate transaction type
    if (!['manual_in', 'manual_out'].includes(type)) {
      setError('Invalid transaction type');
      return false;
    }

    // Sanitize performedBy field
    const sanitizedPerformedBy = performedBy?.trim().replace(/[<>]/g, '') || user.name || user.id || 'Unknown User';

    setLoading(true);
    setError(null);

    try {
      const transactionAmount = type === 'manual_out' ? -Math.abs(amount) : Math.abs(amount);
      
      await createCashTransaction({
        type,
        amount: transactionAmount,
        description: sanitizedDescription,
        time: new Date().toISOString(),
        performedBy: sanitizedPerformedBy,
      });
      
      await refreshData();
      
      // Import toast dynamically for success notification
      const { toast } = await import('sonner');
      toast.success('Transaction Added', {
        description: `Cash transaction of ${Math.abs(transactionAmount)} added successfully`,
        duration: 3000,
      });
      
      return true;
    } catch (err) {
      console.error('[FinanceService] Error adding cash transaction:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add transaction';
      setError(errorMessage);
      
      // Import toast dynamically for error notification
      const { toast } = await import('sonner');
      toast.error('Transaction Failed', {
        description: `Failed to add cash transaction: ${errorMessage}`,
        duration: 5000,
      });
      
      return false;
    } finally {
      setLoading(false);
    }
  };



  // Calculate the current cash balance (caisse)
  const calculateCashBalance = useCallback(() => {
    return transactions.reduce((total, tx) => total + (typeof tx.amount === 'number' ? tx.amount : 0), 0);
  }, [transactions]);

  const calculateSessionTotals = () => {
    if (!transactions.length) return { totalIn: 0, totalOut: 0 };
    const totalIn = transactions.filter(tx => tx.amount > 0).reduce((sum, tx) => sum + tx.amount, 0);
    const totalOut = transactions.filter(tx => tx.amount < 0).reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    return { totalIn, totalOut };
  };

  return {
    transactions: Array.isArray(transactions) ? transactions : [],
    loading,
    error,
    addCashTransaction,
    refreshData,
    calculateSessionTotals,
    cashBalance: calculateCashBalance()
  };
}



// Register order payment with comprehensive validation and error handling
export async function registerOrderPayment(
  orderId: string,
  amount: number,
  receivedAmount?: number,
  cashier?: string
): Promise<{ success: boolean; error?: string; userMessage?: string }> {
  // Comprehensive input validation
  if (!orderId || typeof orderId !== 'string' || orderId.trim().length === 0) {
    return { 
      success: false, 
      error: 'Order ID is required',
      userMessage: 'Invalid order ID provided'
    };
  }

  if (typeof amount !== 'number' || isNaN(amount) || !isFinite(amount) || amount <= 0) {
    return { 
      success: false, 
      error: 'Payment amount must be a positive number',
      userMessage: 'Please enter a valid payment amount'
    };
  }
  
  if (amount > 999999.99) {
    return { 
      success: false, 
      error: 'Payment amount exceeds maximum allowed',
      userMessage: 'Payment amount cannot exceed 999,999.99'
    };
  }

  // Validate received amount if provided
  if (receivedAmount !== undefined) {
    if (typeof receivedAmount !== 'number' || isNaN(receivedAmount) || !isFinite(receivedAmount)) {
      return { 
        success: false, 
        error: 'Received amount must be a valid number',
        userMessage: 'Please enter a valid received amount'
      };
    }
    
    if (receivedAmount < amount) {
      return { 
        success: false, 
        error: 'Received amount cannot be less than payment amount',
        userMessage: 'Received amount must be at least the payment amount'
      };
    }
  }

  // Sanitize cashier field
  const sanitizedCashier = cashier?.trim().replace(/[<>]/g, '') || 'System';

  try {
    const sanitizedOrderId = orderId.trim();
    
    await createCashTransaction({
      type: 'sales',
      amount: amount,
      description: `Order payment for order ${sanitizedOrderId}`,
      time: new Date().toISOString(),
      performedBy: sanitizedCashier,
      relatedDocId: sanitizedOrderId
    });
    
    return { success: true };
  } catch (err) {
    console.error('[registerOrderPayment] Error registering payment:', err);
    const errorMessage = err instanceof Error ? err.message : 'Failed to register payment';
    return { 
      success: false, 
      error: errorMessage,
      userMessage: 'Payment registration failed. Please try again or contact support.'
    };
  }
}


// Financial summary functionality removed - use real-time cash calculations only


/**
 * Get the total paid income from orders (v4 only)
 * Optionally accepts a date range (startDate, endDate in ISO format)
 */
export async function getTotalPaidIncomeV4(startDate?: string, endDate?: string): Promise<number> {
  // Fetch all orders
  const allOrders = await getAllOrders();
  // Filter for paid or partially paid orders
  const paidOrders = allOrders.filter(order =>
    (order.paymentStatus === 'paid' || order.paymentStatus === 'partially_paid') &&
    (!startDate || new Date(order.createdAt) >= new Date(startDate)) &&
    (!endDate || new Date(order.createdAt) <= new Date(endDate))
  );
  // Sum the paid amounts (prefer paymentDetails.amountPaid, fallback to order.total)
  const totalIncome = paidOrders.reduce((sum, order) => {
    const paid = order.paymentDetails?.amountPaid ?? order.total ?? 0;
    return sum + paid;
  }, 0);
  return totalIncome;
}


/**
 * Expenses Management Hook
 * Handles expense recording with proper validation
 */
export function useExpenses() {
  const { isAuthenticated, user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addExpense = async (
    amount: number,
    description: string,
    category?: string
  ) => {
    if (!isAuthenticated || !user) {
      setError('Not authenticated');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      await createCashTransaction({
        type: 'manual_out',
        amount: -Math.abs(amount),
        description: `${category ? `[${category}] ` : ''}${description}`,
        time: new Date().toISOString(),
        performedBy: user.name || user.id || 'Unknown User',
      });
      
      return true;
    } catch (err) {
      console.error('[useExpenses] Error adding expense:', err);
      setError('Failed to add expense');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    addExpense,
    loading,
    error
  };
}
