/**
 * Autonomous Sync Auto-Initialization
 * 
 * This module automatically starts the autonomous sync system when the app loads.
 * Import this in your main app component or layout to enable background sync.
 */

import { autonomousSyncManager } from './autonomous-sync-manager';

let isInitialized = false;

/**
 * Initialize autonomous sync with default configuration
 * This should be called once when the app starts
 */
export async function initializeAutonomousSync(): Promise<void> {
  if (isInitialized) {
    console.log('🚀 [SyncAutoInit] Autonomous sync already initialized');
    return;
  }

  console.log('🚀 [SyncAutoInit] Initializing autonomous sync system...');

  try {
    await autonomousSyncManager.initialize({
      autoStart: true,
      discoveryInterval: 30000,      // Discover servers every 30 seconds
      reconnectInterval: 60000,      // Retry connections every minute
      maxReconnectAttempts: 5,       // Max 5 retry attempts per server
      preferredServers: []           // No preferred servers by default
    });

    isInitialized = true;
    console.log('🚀 [SyncAutoInit] Autonomous sync initialized successfully');
  } catch (error) {
    console.error('🚀 [SyncAutoInit] Failed to initialize autonomous sync:', error);
  }
}

/**
 * Auto-initialize when this module is imported (if in browser)
 */
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAutonomousSync);
  } else {
    // DOM is already ready
    setTimeout(initializeAutonomousSync, 100);
  }
}

export { autonomousSyncManager };