import { google } from 'googleapis';
import { getGoogleDriveConfig, saveGoogleDriveConfig, updateGoogleDriveLastTested } from '@/lib/db/mongo/restaurant-settings-ops';

// Types for Google Drive integration
export interface GoogleDriveConfig {
  type: 'oauth' | 'service_account';
  // OAuth config
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  accessToken?: string;
  // Service Account config
  clientEmail?: string;
  privateKey?: string;
  projectId?: string;
}

export interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
  size?: string;
  webViewLink?: string;
  webContentLink?: string;
  parents?: string[];
  createdTime?: string;
  modifiedTime?: string;
}

export interface UploadResult {
  success: boolean;
  fileId?: string;
  webViewLink?: string;
  error?: string;
}

export interface DownloadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface TestResult {
  success: boolean;
  user?: {
    name: string;
    email: string;
  };
  quota?: {
    used: number;
    total: number | null;
  };
  error?: string;
}

export class GoogleDriveService {
  private drive: any = null;
  private auth: any = null;
  private config: GoogleDriveConfig | null = null;
  private restaurantId: string | null = null;
  private isReady = false;

  constructor() {
    console.log('📁 GoogleDriveService initialized');
  }

  /**
   * Initialize Google Drive service with restaurant ID - loads config from MongoDB
   */
  async initializeFromMongoDB(restaurantId: string): Promise<boolean> {
    try {
      console.log('🔧 Initializing Google Drive service from MongoDB for restaurant:', restaurantId);
      this.restaurantId = restaurantId;

      // Get configuration from MongoDB
      const result = await getGoogleDriveConfig(restaurantId);
      
      if (!result.success || !result.config) {
        console.log('📭 No Google Drive configuration found in MongoDB for restaurant:', restaurantId);
        return false;
      }

      const { type, credentials } = result.config;
      this.config = { type, ...credentials };

      // Initialize based on type
      if (type === 'oauth') {
        return await this.initializeOAuth(credentials);
      } else if (type === 'service_account') {
        return await this.initializeServiceAccount(credentials);
      }

      return false;
    } catch (error) {
      console.error('❌ Failed to initialize Google Drive service from MongoDB:', error);
      return false;
    }
  }

  /**
   * Save Google Drive configuration to MongoDB
   */
  async saveConfigToMongoDB(
    restaurantId: string,
    config: {
      type: 'oauth' | 'service_account';
      credentials: any;
      setupBy: string;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await saveGoogleDriveConfig(restaurantId, config);
      if (result.success) {
        // Auto-initialize with the new config
        await this.initializeFromMongoDB(restaurantId);
      }
      return result;
    } catch (error) {
      console.error('❌ Failed to save Google Drive config to MongoDB:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Initialize OAuth authentication
   */
  async initializeOAuth(config: GoogleDriveConfig): Promise<boolean> {
    try {
      console.log('🔐 Initializing OAuth authentication...');
      
      if (!config.clientId || !config.clientSecret || !config.refreshToken) {
        console.error('❌ Missing OAuth configuration parameters');
        return false;
      }

      this.config = config;
      
      // Create OAuth2 client
      this.auth = new google.auth.OAuth2(
        config.clientId,
        config.clientSecret,
        'urn:ietf:wg:oauth:2.0:oob'
      );

      // Set refresh token
      this.auth.setCredentials({
        refresh_token: config.refreshToken,
        access_token: config.accessToken
      });

      // Initialize Drive API
      this.drive = google.drive({ version: 'v3', auth: this.auth });
      this.isReady = true;

      console.log('✅ OAuth authentication initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to initialize OAuth:', error);
      this.isReady = false;
      return false;
    }
  }

  /**
   * Initialize Service Account authentication
   */
  async initializeServiceAccount(config: GoogleDriveConfig): Promise<boolean> {
    try {
      console.log('🔐 Initializing Service Account authentication...');
      
      if (!config.clientEmail || !config.privateKey || !config.projectId) {
        console.error('❌ Missing Service Account configuration parameters');
        return false;
      }

      this.config = config;

      // Create service account client
      this.auth = new google.auth.GoogleAuth({
        credentials: {
          client_email: config.clientEmail,
          private_key: config.privateKey.replace(/\\n/g, '\n'),
          project_id: config.projectId,
        },
        scopes: ['https://www.googleapis.com/auth/drive'],
      });

      // Initialize Drive API
      this.drive = google.drive({ version: 'v3', auth: this.auth });
      this.isReady = true;

      console.log('✅ Service Account authentication initialized successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to initialize Service Account:', error);
      this.isReady = false;
      return false;
    }
  }

  /**
   * Test the connection to Google Drive
   */
  async testConnection(): Promise<TestResult> {
    if (!this.isReady || !this.drive) {
      return { success: false, error: 'Google Drive service not initialized' };
    }

    try {
      console.log('🧪 Testing Google Drive connection...');
      
      // Try to get user info from Drive API
      const response = await this.drive.about.get({
        fields: 'user(displayName,emailAddress),storageQuota(limit,usage)'
      });

      const userInfo = response.data;
      console.log('✅ Google Drive connection test successful');

      // Update last tested timestamp in MongoDB if we have restaurantId
      if (this.restaurantId) {
        try {
          await updateGoogleDriveLastTested(this.restaurantId);
          console.log('⏰ Updated last tested timestamp in MongoDB');
        } catch (error) {
          console.warn('⚠️ Failed to update last tested timestamp:', error);
          // Don't fail the test if we can't update timestamp
        }
      }

      return {
        success: true,
        user: {
          name: userInfo.user?.displayName || 'Unknown',
          email: userInfo.user?.emailAddress || 'Unknown'
        },
        quota: userInfo.storageQuota ? {
          used: parseInt(userInfo.storageQuota.usage || '0'),
          total: userInfo.storageQuota.limit ? parseInt(userInfo.storageQuota.limit) : null
        } : undefined
      };
    } catch (error: any) {
      console.error('❌ Google Drive connection test failed:', error);
      
      // Provide more specific error messages
      if (error.code === 401) {
        return { success: false, error: 'Authentication failed. Please check your credentials.' };
      } else if (error.code === 403) {
        return { success: false, error: 'Access denied. Please check your permissions.' };
      } else if (error.code === 'ENOTFOUND') {
        return { success: false, error: 'Network error. Please check your internet connection.' };
      }
      
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred during connection test' 
      };
    }
  }

  /**
   * Upload a file to Google Drive
   */
  async uploadFile(
    file: File | Buffer,
    fileName: string,
    restaurantId: string,
    metadata?: {
      transactionId?: string;
      fileType?: string;
    }
  ): Promise<UploadResult> {
    if (!this.isReady || !this.drive) {
      return {
        success: false,
        error: 'Google Drive service not initialized'
      };
    }

    try {
      console.log('📤 Uploading file to Google Drive:', fileName);

      // Prepare file metadata
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 8);
      const prefix = metadata?.fileType || 'upload';
      const transactionPart = metadata?.transactionId ? `_${metadata.transactionId}` : '';
      const finalFileName = `${prefix}${transactionPart}_${timestamp}_${randomId}.webp`;

      // Create folder name for restaurant
      const folderName = `Restaurant_${restaurantId}_Images`;

      // Find or create restaurant folder
      let folderId: string;
      try {
        const folderResponse = await this.drive.files.list({
          q: `name='${folderName}' and mimeType='application/vnd.google-apps.folder'`,
          fields: 'files(id, name)'
        });

        if (folderResponse.data.files.length > 0) {
          folderId = folderResponse.data.files[0].id;
          console.log('📁 Using existing folder:', folderId);
        } else {
          // Create folder
          const folderCreate = await this.drive.files.create({
            requestBody: {
              name: folderName,
              mimeType: 'application/vnd.google-apps.folder'
            },
            fields: 'id'
          });
          folderId = folderCreate.data.id;
          console.log('📁 Created new folder:', folderId);
        }
      } catch (error) {
        console.warn('⚠️ Failed to create/find folder, uploading to root');
        folderId = '';
      }

      // Prepare file buffer
      let fileBuffer: Buffer;
      if (file instanceof File) {
        fileBuffer = Buffer.from(await file.arrayBuffer());
      } else {
        fileBuffer = file;
      }

      // Upload file
      const fileMetadata: any = {
        name: finalFileName,
      };

      if (folderId) {
        fileMetadata.parents = [folderId];
      }

      const media = {
        mimeType: 'image/webp',
        body: fileBuffer,
      };

      const response = await this.drive.files.create({
        requestBody: fileMetadata,
        media: media,
        fields: 'id, webViewLink'
      });

      console.log('✅ File uploaded successfully:', response.data.id);

      return {
        success: true,
        fileId: response.data.id,
        webViewLink: response.data.webViewLink
      };

    } catch (error: any) {
      console.error('❌ Failed to upload file:', error);
      return {
        success: false,
        error: error.message || 'Upload failed'
      };
    }
  }

  /**
   * Get download URL for a file
   */
  async getDownloadUrl(fileId: string): Promise<DownloadResult> {
    if (!this.isReady || !this.drive) {
      return {
        success: false,
        error: 'Google Drive service not initialized'
      };
    }

    try {
      console.log('🔗 Getting download URL for file:', fileId);

      // Get file metadata to check if it exists
      const fileResponse = await this.drive.files.get({
        fileId: fileId,
        fields: 'id, name, mimeType, webContentLink'
      });

      // For images, we can use the webContentLink or create a direct download link
      const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;

      console.log('✅ Download URL generated successfully');
      return {
        success: true,
        url: downloadUrl
      };

    } catch (error: any) {
      console.error('❌ Failed to get download URL:', error);
      return {
        success: false,
        error: error.message || 'Failed to get download URL'
      };
    }
  }

  /**
   * Delete a file from Google Drive
   */
  async deleteFile(fileId: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isReady || !this.drive) {
      return {
        success: false,
        error: 'Google Drive service not initialized'
      };
    }

    try {
      console.log('🗑️ Deleting file from Google Drive:', fileId);

      await this.drive.files.delete({
        fileId: fileId
      });

      console.log('✅ File deleted successfully');
      return { success: true };

    } catch (error: any) {
      console.error('❌ Failed to delete file:', error);
      return {
        success: false,
        error: error.message || 'Delete failed'
      };
    }
  }

  /**
   * List files in Google Drive
   */
  async listFiles(query?: string): Promise<{ success: boolean; files?: GoogleDriveFile[]; error?: string }> {
    if (!this.isReady || !this.drive) {
      return {
        success: false,
        error: 'Google Drive service not initialized'
      };
    }

    try {
      console.log('📋 Listing files from Google Drive');

      const response = await this.drive.files.list({
        q: query,
        pageSize: 100,
        fields: 'files(id, name, mimeType, size, webViewLink, webContentLink, parents, createdTime, modifiedTime)'
      });

      return {
        success: true,
        files: response.data.files || []
      };

    } catch (error: any) {
      console.error('❌ Failed to list files:', error);
      return {
        success: false,
        error: error.message || 'List files failed'
      };
    }
  }

  /**
   * Check if service is initialized
   */
  isInitialized(): boolean {
    return this.isReady && this.drive !== null;
  }

  /**
   * Get current configuration (without sensitive data)
   */
  getConfig(): Partial<GoogleDriveConfig> | null {
    if (!this.config) return null;

    return {
      type: this.config.type,
      clientId: this.config.clientId ? `${this.config.clientId.substring(0, 10)}...` : undefined,
      clientEmail: this.config.clientEmail,
      projectId: this.config.projectId
    };
  }

  /**
   * Reset the service
   */
  reset(): void {
    console.log('🔄 Resetting Google Drive service');
    this.drive = null;
    this.auth = null;
    this.config = null;
    this.isReady = false;
  }
} 