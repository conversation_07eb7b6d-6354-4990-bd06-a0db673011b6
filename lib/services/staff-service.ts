// Shim for legacy staff-service compatibility
// Redirects to new-staff-service functions until refs are fully refactored
import {
  getAllStaffMembers,
  getStaffById,
  createStaffLocal,
  updateStaffById,
  deleteStaffById,
} from './new-staff-service';
import { v4 as uuidv4 } from 'uuid';
import { ShiftConfig, StaffMember, StaffPresence, WeeklySchedule, StaffRole, AttendanceRecord, StaffSchedule } from '@/lib/types/staff';
import { getShifts, createShift as dbCreateShift, updateShift as dbUpdateShift, deleteShift as dbDeleteShift } from '@/lib/db/v4';
import { StaffDocument } from '@/lib/db/v4/schemas/per-staff-schemas';
import {
  recordStaffAttendance as dbRecordStaffAttendance,
  getStaffAttendanceRecords as dbGetStaffAttendanceRecords,
  getStaffAttendance as dbGetStaffAttendance
} from '@/lib/db/v4';

// Helper to convert StaffDocument to lib/types/staff StaffMember
const convertV4ToLibStaffMember = (v4Staff: StaffDocument): StaffMember => {
  return {
    id: v4Staff.id,
    name: v4Staff.name,
    role: v4Staff.role as StaffRole,
    email: v4Staff.email || '',
    phone: v4Staff.phone || '',
    status: v4Staff.status,
    startDate: v4Staff.createdAt || new Date().toISOString(),
    paymentConfig: {
      type: v4Staff.paymentConfig.type as any,
      baseSalary: v4Staff.paymentConfig.baseSalary,
      shiftRate: v4Staff.paymentConfig.shiftRate,
      shiftRates: v4Staff.paymentConfig.shiftRates,
      paymentDay: v4Staff.paymentConfig.paymentDay
    },
    presence: {
      staffId: v4Staff.id,
      isPresent: false,
      attendanceHistory: [],
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0
    },
    userId: v4Staff.userId || '',
    hasUserAccount: v4Staff.hasUserAccount || false,
    // Note: Schedule will be loaded separately using per-staff operations
    schedule: undefined,
    type: 'staff' as const
  };
};

// Helper to convert a ShiftConfig to a format compatible with WeeklyScheduleGrid
const convertToGridShift = (shift: any): ShiftConfig => {
  return {
    id: shift.id,
    name: shift.name,
    startTime: shift.startTime,
    endTime: shift.endTime,
    color: shift.color
  };
};

// Local storage key for schedules (only used as fallback)
const SCHEDULES_STORAGE_KEY = 'staff_schedules_v4';

export const staffService = {
  getAllStaff: getAllStaffMembers,
  getStaffById,
  createStaffLocal,
  updateStaffById,
  deleteStaffById,
  
  // 🔧 ADDED: Missing methods for compatibility
  updateStaffMember: updateStaffById,
  deleteStaffMember: deleteStaffById,
  
  // Clear staff local storage - only used as utility
  clearStaffLocalStorage: (): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(SCHEDULES_STORAGE_KEY);
      console.log('Cleared staff local storage');
    } catch (error) {
      console.error('Error clearing staff local storage:', error);
    }
  },
  
  // Get all shifts using v4 database
  getAllShifts: async (): Promise<ShiftConfig[]> => {
    try {
      const shifts = await getShifts();
      return shifts.map(convertToGridShift);
    } catch (error) {
      console.error('Error fetching shifts:', error);
      return [];
    }
  },
  
  // Create shift using v4 database
  createShift: async (shiftData: { name: string; startTime: string; endTime: string; color?: string }): Promise<ShiftConfig> => {
    try {
      const shift = await dbCreateShift(
        shiftData.name,
        shiftData.startTime,
        shiftData.endTime,
        shiftData.color
      );
      
      return convertToGridShift(shift);
    } catch (error) {
      console.error('Error creating shift:', error);
      // Fallback to the stub implementation if database fails
      const id = uuidv4();
      return { id, name: shiftData.name, startTime: shiftData.startTime, endTime: shiftData.endTime, color: shiftData.color };
    }
  },
  
  // Update shift using v4 database
  updateShift: async (id: string, shiftData: { name: string; startTime: string; endTime: string; color?: string }): Promise<ShiftConfig> => {
    try {
      const shift = await dbUpdateShift(id, shiftData);
      return convertToGridShift(shift);
    } catch (error) {
      console.error(`Error updating shift ${id}:`, error);
      // Fallback to the stub implementation if database fails
      return { id, name: shiftData.name, startTime: shiftData.startTime, endTime: shiftData.endTime, color: shiftData.color };
    }
  },
  
  // Delete shift using v4 database
  deleteShift: async (id: string): Promise<void> => {
    try {
      await dbDeleteShift(id);
    } catch (error) {
      console.error(`Error deleting shift ${id}:`, error);
      // No fallback needed, just log the error
    }
  },
  
  // 🔧 FIXED: Get staff with schedules properly loaded from database
  getStaffWithSchedules: async (): Promise<StaffMember[]> => {
    try {
      console.log('📊 Loading staff with schedules from database...');
      
      // Get basic staff documents
      const v4Staff = await getAllStaffMembers();
      console.log(`📋 Found ${v4Staff.length} staff members`);
      
      // Load schedule for each staff member using proper per-staff operations
      const staffWithSchedules = await Promise.all(
        v4Staff.map(async (staff) => {
          const convertedStaff = convertV4ToLibStaffMember(staff);
          
          try {
            // Load schedule from database using per-staff operations
            const { getStaffSchedule } = await import('@/lib/db/v4');
            const scheduleData = await getStaffSchedule(staff.id);
            
            if (scheduleData) {
              convertedStaff.schedule = {
                staffId: scheduleData.staffId,
                weeklySchedule: scheduleData.weeklySchedule,
                effectiveFrom: scheduleData.effectiveFrom,
                effectiveTo: scheduleData.effectiveTo,
                isActive: scheduleData.isActive
              };
              console.log(`✅ Loaded schedule for ${staff.name}:`, convertedStaff.schedule.weeklySchedule);
            } else {
              console.log(`⚠️ No schedule found for ${staff.name}`);
              // Initialize empty schedule to prevent errors
              convertedStaff.schedule = {
                staffId: staff.id,
                weeklySchedule: {
                  monday: [],
                  tuesday: [],
                  wednesday: [],
                  thursday: [],
                  friday: [],
                  saturday: [],
                  sunday: []
                },
                effectiveFrom: new Date().toISOString(),
                isActive: true
              };
            }
          } catch (scheduleError) {
            console.warn(`⚠️ Could not load schedule for ${staff.name}:`, scheduleError);
            // Initialize empty schedule instead of leaving undefined
            convertedStaff.schedule = {
              staffId: staff.id,
              weeklySchedule: {
                monday: [],
                tuesday: [],
                wednesday: [],
                thursday: [],
                friday: [],
                saturday: [],
                sunday: []
              },
              effectiveFrom: new Date().toISOString(),
              isActive: true
            };
          }
          
          /* ------------------ 📌 Load attendance history ------------------ */
          try {
            const attendanceDoc = await dbGetStaffAttendance(staff.id);
            const rawRecords = attendanceDoc?.records || [];
            const convertedRecords = rawRecords.map(r => ({
              id: r.id,
              date: r.date,
              staffId: staff.id,
              staffName: staff.name,
              shiftId: r.shiftId || '',
              shiftName: r.shiftName || '',
              attended: r.status === 'present' || r.status === 'late',
              status: r.status as 'present' | 'late',
              notes: r.notes,
              isPaid: r.isPaid
            }));
            convertedStaff.presence.attendanceHistory = convertedRecords;
            console.log(`✅ Loaded ${convertedRecords.length} attendance records for ${staff.name}`);
          } catch (attendanceError) {
            // This is normal for new staff members who haven't clocked in yet
            console.log(`📋 No attendance history for ${staff.name} yet (normal for new staff)`);
            // Leave attendanceHistory as empty array on failure
          }
          
          return convertedStaff;
        })
      );
      
      console.log(`✅ Successfully loaded ${staffWithSchedules.length} staff members with schedules`);
      return staffWithSchedules;
    } catch (error) {
      console.error('❌ Error getting staff with schedules:', error);
      return [];
    }
  },
  
  // Load schedules from storage - only used as fallback
  loadSchedulesFromStorage: (): Record<string, StaffSchedule> => {
    if (typeof window === 'undefined') return {};
    
    try {
      const schedules = localStorage.getItem(SCHEDULES_STORAGE_KEY);
      return schedules ? JSON.parse(schedules) : {};
    } catch (error) {
      console.error('Error loading schedules from localStorage:', error);
      return {};
    }
  },
  
  // Save schedules to storage - only used as backup
  saveSchedulesToStorage: (schedules: Record<string, StaffSchedule>): void => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(SCHEDULES_STORAGE_KEY, JSON.stringify(schedules));
    } catch (error) {
      console.error('Error saving schedules to localStorage:', error);
    }
  },
  
  // Get a specific staff member's schedule
  getStaffSchedule: async (staffId: string): Promise<StaffSchedule | null> => {
    console.log(`📋 Getting schedule for staff ${staffId}`);
    try {
      // Use the proper per-staff schedule operations
      const { getStaffSchedule } = await import('@/lib/db/v4');
      const schedule = await getStaffSchedule(staffId);
      if (schedule) {
        return {
          staffId: schedule.staffId,
          weeklySchedule: schedule.weeklySchedule,
          effectiveFrom: schedule.effectiveFrom,
          effectiveTo: schedule.effectiveTo,
          isActive: schedule.isActive
        };
      }
      
      // Return null if no schedule found (don't fall back to localStorage)
      return null;
    } catch (error) {
      console.error(`Error getting schedule for staff ${staffId}:`, error);
      return null;
    }
  },
  
  // Get staff for a specific day
  getStaffForDay: async (dayName: string): Promise<StaffMember[]> => {
    try {
      console.log(`📅 Getting staff for day: ${dayName}`);
      const staffMembers = await staffService.getStaffWithSchedules();
      
      // Only return staff who have a schedule for this day
      return staffMembers.filter(staff => {
        if (!staff.schedule?.weeklySchedule) return false;
        const daySchedule = staff.schedule.weeklySchedule[dayName as keyof WeeklySchedule];
        return Array.isArray(daySchedule) && daySchedule.length > 0;
      });
    } catch (error) {
      console.error(`Error getting staff for day ${dayName}:`, error);
      return [];
    }
  },
  
  // Get staff attendance - real implementation
  getStaffAttendance: async (staffId: string, startDate: string, endDate: string): Promise<any[]> => {
    try {
      const records = await dbGetStaffAttendanceRecords(staffId, startDate, endDate);
      // Attach staffId so UI helpers can match correctly
      return records.map(r => ({
        ...r,
        staffId,
        // Ensure shiftName exists for display if missing
        shiftName: r.shiftName || 'Shift',
      }));
    } catch (error) {
      console.error(`Error fetching attendance for ${staffId}:`, error);
      return [];
    }
  },
  
  // Record shift attendance with auto-sync to staff allowance system
  recordShiftAttendance: async (
    staffId: string,
    shiftId: string,
    attendanceData: {
      date: string;
      status: 'present' | 'late' | 'absent';
      shiftName?: string;
      notes?: string;
    }
  ): Promise<AttendanceRecord> => {
    try {
      console.log(`📝 Recording attendance for staff ${staffId} on ${attendanceData.date} for shift ${shiftId}`);
      
      const dbResult = await dbRecordStaffAttendance(
        staffId,
        attendanceData.date,
        attendanceData.status,
        shiftId,
        attendanceData.shiftName,
        attendanceData.notes
      );
      
      // 🔄 AUTO-SYNC: If marking present/late, also mark in staff allowance system
      if (attendanceData.status === 'present' || attendanceData.status === 'late') {
        try {
          const { markStaffPresent } = await import('../db/v4/operations/staff-menu-ops');
          const staffMember = await staffService.getStaffById(staffId);
          if (staffMember) {
            await markStaffPresent(
              staffId, 
              staffMember.name, 
              shiftId, 
              attendanceData.shiftName || 'Shift', 
              attendanceData.date
            );
            console.log(`🔄 Auto-synced: ${staffMember.name} marked present in staff allowance system`);
          }
        } catch (syncError) {
          console.warn('Failed to sync with staff allowance system:', syncError);
          // Don't fail the main operation if sync fails
        }
      }
      
      // Convert database result to service interface
      const staffMember = await staffService.getStaffById(staffId);
      const result: AttendanceRecord = {
        id: dbResult.id,
        date: dbResult.date,
        staffId: staffId,
        staffName: staffMember?.name || 'Unknown Staff',
        shiftId: dbResult.shiftId || shiftId,
        shiftName: dbResult.shiftName || attendanceData.shiftName || 'Shift',
        attended: dbResult.status === 'present' || dbResult.status === 'late',
        status: dbResult.status,
        notes: dbResult.notes,
        isPaid: dbResult.isPaid
      };
      
      return result;
    } catch (error) {
      console.error(`Error recording attendance for staff ${staffId}:`, error);
      throw error;
    }
  },
  
  // Save staff schedule - deprecated, use updateStaffWeeklySchedule instead
  saveStaffSchedule: async (staffId: string, schedule: StaffSchedule): Promise<StaffSchedule> => {
    console.warn('saveStaffSchedule is deprecated, use updateStaffWeeklySchedule instead');
    await staffService.updateStaffWeeklySchedule(staffId, schedule.weeklySchedule);
    return schedule;
  },
  
  // Update staff schedule - primary method for schedule updates
  updateStaffWeeklySchedule: async (staffId: string, weeklySchedule: WeeklySchedule): Promise<any> => {
    console.log(`🔄 Updating weekly schedule for staff ${staffId}:`, weeklySchedule);
    try {
      // Use the proper per-staff schedule operations
      const { setStaffSchedule } = await import('@/lib/db/v4');
      
      // Save using the proper schedule document structure
      const schedule = await setStaffSchedule(staffId, {
        weeklySchedule: weeklySchedule,
        effectiveFrom: new Date().toISOString(),
        isActive: true,
      });
      
      console.log('✅ Schedule updated in database:', schedule);
      
      // Also save to localStorage as backup
      const schedules = staffService.loadSchedulesFromStorage();
      schedules[staffId] = {
        staffId: staffId,
        weeklySchedule: weeklySchedule,
        effectiveFrom: new Date().toISOString(),
        isActive: true,
      };
      staffService.saveSchedulesToStorage(schedules);
      
      return { success: true, staffId, weeklySchedule };
    } catch (error) {
      console.error(`❌ Error updating schedule for staff ${staffId}:`, error);
      
      // Fallback: save to localStorage only
      try {
        const schedules = staffService.loadSchedulesFromStorage();
        schedules[staffId] = {
          staffId: staffId,
          weeklySchedule: weeklySchedule,
          effectiveFrom: new Date().toISOString(),
          isActive: true,
        };
        staffService.saveSchedulesToStorage(schedules);
        console.log('⚠️ Saved schedule to localStorage only due to database error');
        
        return { success: false, staffId, weeklySchedule, error: (error as Error).message };
      } catch (fallbackError) {
        console.error('Even localStorage fallback failed:', fallbackError);
        throw error;
      }
    }
  }
}; 