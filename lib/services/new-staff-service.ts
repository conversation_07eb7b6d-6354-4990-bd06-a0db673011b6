/**
 * Simple Staff Service
 *
 * A simplified service for managing staff members in PouchDB only.
 */

import { v4 as uuidv4 } from 'uuid';
import {
  getAllStaff,
  getStaffMember,
  addStaffMember,
  updateStaffMember,
  deleteStaffMember,
} from '@/lib/db/v4';
import { StaffDocument } from '@/lib/db/v4/schemas/per-staff-schemas';
import { DEFAULT_STAFF_PERMISSIONS } from '@/lib/db/v4/operations/per-staff-ops';

/**
 * Creates a new staff member in the local database (PouchDB only)
 *
 * @param staffData Basic staff data
 * @returns The created staff member
 */
export async function createStaffLocal(staffData: {
  name: string;
  role: string;
  email?: string;
  phone?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  paymentConfig?: StaffDocument['paymentConfig'];
  weeklySchedule?: {
    monday: string[];
    tuesday: string[];
    wednesday: string[];
    thursday: string[];
    friday: string[];
    saturday: string[];
    sunday: string[];
  };
  permissions?: any; // Allow any to match DEFAULT_STAFF_PERMISSIONS
}): Promise<StaffDocument> {
  try {
    console.log('StaffService: Creating staff member locally:', staffData.name);

    // Generate a new UUID for the staff member
    const staffId = uuidv4();

    // Create the staff member object with required fields (excluding schedule)
    const newStaffMember: Partial<StaffDocument> = {
      id: staffId,
      name: staffData.name,
      // Normalize role to uppercase enum or default to 'STAFF'
      role: (staffData.role ? staffData.role.toUpperCase() : 'STAFF') as StaffDocument['role'],
      email: staffData.email || '',
      phone: staffData.phone || '',
      status: staffData.status || 'ACTIVE',
      paymentConfig: {
        type: staffData.paymentConfig?.type || 'MONTHLY',
        baseSalary: staffData.paymentConfig?.baseSalary || 0,
        shiftRate: staffData.paymentConfig?.shiftRate,
        shiftRates: staffData.paymentConfig?.shiftRates
      },
      hasUserAccount: false,
      permissions: staffData.permissions || DEFAULT_STAFF_PERMISSIONS
    };

    // Add the staff member to the database (without schedule)
    const createdStaff = await addStaffMember(newStaffMember);

    // Handle schedule separately if provided
    if (staffData.weeklySchedule) {
      try {
        const { setStaffSchedule } = await import('@/lib/db/v4');
        await setStaffSchedule(staffId, {
          weeklySchedule: staffData.weeklySchedule,
          effectiveFrom: new Date().toISOString(),
          isActive: true,
        });
        console.log('✅ Schedule created separately for staff:', staffId);
      } catch (scheduleError) {
        console.warn('⚠️ Failed to create schedule document, but staff was created:', scheduleError);
        // Don't fail the entire operation if schedule creation fails
      }
    }

    console.log('StaffService: Staff member created successfully:', createdStaff.id);

    return createdStaff;
  } catch (error) {
    console.error('StaffService: Error creating staff member:', error);
    throw error;
  }
}

/**
 * Gets all staff members
 *
 * @returns Array of staff members
 */
export async function getAllStaffMembers(): Promise<StaffDocument[]> {
  try {
    return await getAllStaff();
  } catch (error) {
    console.error('StaffService: Error getting all staff:', error);
    throw error;
  }
}

/**
 * Gets a staff member by ID
 *
 * @param staffId The ID of the staff member
 * @returns The staff member or null if not found
 */
export async function getStaffById(staffId: string): Promise<StaffDocument | null> {
  try {
    return await getStaffMember(staffId);
  } catch (error) {
    console.error(`StaffService: Error getting staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Updates a staff member
 *
 * @param staffId The ID of the staff member
 * @param updates The updates to apply
 * @returns The updated staff member
 */
export async function updateStaffById(
  staffId: string,
  updates: Partial<StaffDocument>
): Promise<StaffDocument> {
  try {
    return await updateStaffMember(staffId, updates);
  } catch (error) {
    console.error(`StaffService: Error updating staff ${staffId}:`, error);
    throw error;
  }
}

/**
 * Deletes a staff member
 *
 * @param staffId The ID of the staff member
 */
export async function deleteStaffById(staffId: string): Promise<void> {
  try {
    await deleteStaffMember(staffId);
  } catch (error) {
    console.error(`StaffService: Error deleting staff ${staffId}:`, error);
    throw error;
  }
}
