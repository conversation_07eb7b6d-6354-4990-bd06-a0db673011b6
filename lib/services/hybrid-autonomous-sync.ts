/**
 * Hybrid Autonomous Sync Manager
 * 
 * Intelligent sync system that:
 * - Prioritizes local network sync (faster, direct CouchDB)
 * - Falls back to internet sync via VPS proxy
 * - Automatically switches between modes based on availability
 * - Maintains persistent connections with auto-reconnection
 */

import { discoverCouchDBServers } from './ip-discovery';
import { internetDiscoveryService, type InternetDiscoveryConfig } from './internet-discovery';
import { internetSyncService, type InternetSyncConfig } from './internet-sync';
import { nativeSyncService, type SyncServer, type SyncStatus } from './native-sync';
import type { DiscoveredPeer } from './internet-sync';

interface HybridSyncServer extends SyncServer {
  responseTime?: number;
  lastSeen?: Date;
  syncType: 'local' | 'internet';
  peerId?: string; // For internet peers
}

interface HybridConfig {
  // Local sync settings
  localDiscoveryInterval: number;
  localReconnectInterval: number;
  
  // Internet sync settings
  internetDiscoveryInterval: number;
  internetReconnectInterval: number;
  vpsBaseUrl: string;
  authToken: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
  
  // General settings
  maxReconnectAttempts: number;
  autoStart: boolean;
  preferLocalSync: boolean; // Prefer local over internet when both available
  
  // Device registration for internet sync
  deviceRegistration?: {
    ipAddress: string;
    couchdbPort?: number;
  };
}

interface HybridStatus {
  isRunning: boolean;
  
  // Local sync status
  localDiscovering: boolean;
  localServers: HybridSyncServer[];
  localConnected: boolean;
  
  // Internet sync status  
  internetDiscovering: boolean;
  internetPeers: HybridSyncServer[];
  internetConnected: boolean;
  internetRegistered: boolean;
  
  // Current connection
  currentConnection: {
    server: HybridSyncServer | null;
    type: 'local' | 'internet' | null;
  };
  
  // Discovery timestamps
  lastLocalDiscovery: Date | null;
  lastInternetDiscovery: Date | null;
  
  // Sync status
  syncStatus: SyncStatus;
  
  // Error states
  error: string | null;
}

class HybridAutonomousSyncManager {
  private config: HybridConfig | null = null;
  private status: HybridStatus = {
    isRunning: false,
    localDiscovering: false,
    localServers: [],
    localConnected: false,
    internetDiscovering: false,
    internetPeers: [],
    internetConnected: false,
    internetRegistered: false,
    currentConnection: { server: null, type: null },
    lastLocalDiscovery: null,
    lastInternetDiscovery: null,
    syncStatus: nativeSyncService.getStatus(),
    error: null
  };

  private localDiscoveryTimer: NodeJS.Timeout | null = null;
  private internetDiscoveryTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private listeners: ((status: HybridStatus) => void)[] = [];

  constructor() {
    // Listen to native sync status changes
    nativeSyncService.onStatusChange((syncStatus) => {
      this.status.syncStatus = syncStatus;
      
      // Update connection status
      if (this.status.currentConnection.type === 'local') {
        this.status.localConnected = syncStatus.connected;
      }
      
      this.emitStatusUpdate();
    });

    // Listen to internet sync status changes
    internetSyncService.onStatusChange((internetStatus) => {
      if (this.status.currentConnection.type === 'internet') {
        this.status.internetConnected = internetStatus.connected;
      }
      this.emitStatusUpdate();
    });
  }

  async initialize(config: HybridConfig): Promise<void> {
    console.log('🔄 [HybridSync] Initializing hybrid autonomous sync...');
    
    this.config = config;

    // Configure internet services
    internetDiscoveryService.configure({
      vpsBaseUrl: config.vpsBaseUrl,
      authToken: config.authToken,
      deviceId: config.deviceId,
      deviceType: config.deviceType
    });

    internetSyncService.configure({
      vpsBaseUrl: config.vpsBaseUrl,
      authToken: config.authToken,
      deviceId: config.deviceId
    });

    // Register device for internet sync if registration info provided
    if (config.deviceRegistration) {
      try {
        await internetDiscoveryService.registerDevice({
          deviceId: config.deviceId,
          deviceType: config.deviceType,
          ipAddress: config.deviceRegistration.ipAddress,
          couchdbPort: config.deviceRegistration.couchdbPort
        });
        this.status.internetRegistered = true;
      } catch (error: any) {
        console.error('🔄 [HybridSync] Device registration failed:', error);
        this.status.error = `Device registration failed: ${error.message}`;
      }
    }

    // Auto-start if enabled
    if (config.autoStart) {
      await this.start();
    }

    console.log('🔄 [HybridSync] Initialized successfully');
  }

  async start(): Promise<void> {
    if (!this.config) {
      throw new Error('HybridSync not configured. Call initialize() first.');
    }

    if (this.status.isRunning) {
      console.log('🔄 [HybridSync] Already running');
      return;
    }

    console.log('🔄 [HybridSync] Starting hybrid sync...');
    this.status.isRunning = true;
    this.status.error = null;
    this.emitStatusUpdate();

    // Start discovery processes
    await this.performLocalDiscovery();
    await this.performInternetDiscovery();

    // Start periodic discovery
    this.startPeriodicDiscovery();

    // Start connection management
    this.startConnectionManagement();

    console.log('🔄 [HybridSync] Started successfully');
  }

  async stop(): Promise<void> {
    console.log('🔄 [HybridSync] Stopping hybrid sync...');
    
    this.status.isRunning = false;
    
    // Clear all timers
    if (this.localDiscoveryTimer) {
      clearInterval(this.localDiscoveryTimer);
      this.localDiscoveryTimer = null;
    }
    
    if (this.internetDiscoveryTimer) {
      clearInterval(this.internetDiscoveryTimer);
      this.internetDiscoveryTimer = null;
    }
    
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Disconnect current sync
    await this.disconnectCurrentSync();

    // Unregister device
    if (this.status.internetRegistered) {
      await internetDiscoveryService.unregisterDevice();
      this.status.internetRegistered = false;
    }

    // Reset status
    this.status.currentConnection = { server: null, type: null };
    this.status.localConnected = false;
    this.status.internetConnected = false;
    
    this.emitStatusUpdate();
    console.log('🔄 [HybridSync] Stopped');
  }

  private async performLocalDiscovery(): Promise<void> {
    if (!this.config || this.status.localDiscovering) return;

    this.status.localDiscovering = true;
    this.emitStatusUpdate();

    try {
      console.log('🏠 [HybridSync] Discovering local servers...');
      const discovered = await discoverCouchDBServers({
        timeout: 2000,
        maxConcurrent: 10
      });

      this.status.localServers = discovered.map(server => ({
        ip: server.ip,
        port: server.port,
        url: server.url,
        syncType: 'local' as const,
        responseTime: server.responseTime,
        lastSeen: server.lastSeen
      }));

      this.status.lastLocalDiscovery = new Date();
      console.log(`🏠 [HybridSync] Found ${this.status.localServers.length} local server(s)`);
    } catch (error: any) {
      console.error('🏠 [HybridSync] Local discovery failed:', error);
    } finally {
      this.status.localDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private async performInternetDiscovery(): Promise<void> {
    if (!this.config || this.status.internetDiscovering || !this.status.internetRegistered) return;

    this.status.internetDiscovering = true;
    this.emitStatusUpdate();

    try {
      console.log('🌐 [HybridSync] Discovering internet peers...');
      const peers = await internetDiscoveryService.discoverPeers();

      this.status.internetPeers = peers.map(peer => ({
        ip: peer.ipAddress,
        port: peer.couchdbPort,
        url: `${this.config!.vpsBaseUrl}/api/sync/proxy/${peer.id}`,
        syncType: 'internet' as const,
        peerId: peer.id,
        lastSeen: peer.lastSeen
      }));

      this.status.lastInternetDiscovery = new Date();
      console.log(`🌐 [HybridSync] Found ${this.status.internetPeers.length} internet peer(s)`);
    } catch (error: any) {
      console.error('🌐 [HybridSync] Internet discovery failed:', error);
    } finally {
      this.status.internetDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private startPeriodicDiscovery(): void {
    if (!this.config) return;

    // Local discovery
    this.localDiscoveryTimer = setInterval(() => {
      this.performLocalDiscovery();
    }, this.config.localDiscoveryInterval);

    // Internet discovery
    this.internetDiscoveryTimer = setInterval(() => {
      this.performInternetDiscovery();
    }, this.config.internetDiscoveryInterval);
  }

  private startConnectionManagement(): void {
    if (!this.config) return;

    // Check and manage connections every 10 seconds
    this.reconnectTimer = setInterval(() => {
      this.manageConnection();
    }, 10000);

    // Initial connection attempt
    setTimeout(() => this.manageConnection(), 1000);
  }

  private async manageConnection(): Promise<void> {
    if (!this.config || !this.status.isRunning) return;

    // If already connected and syncing, don't interfere
    if (this.status.syncStatus.connected && this.status.syncStatus.syncing) {
      return;
    }

    // Determine best connection option
    const bestServer = this.getBestServer();
    
    if (!bestServer) {
      // No servers available, ensure we're disconnected
      if (this.status.currentConnection.server) {
        await this.disconnectCurrentSync();
      }
      return;
    }

    // If already connected to the best server, don't reconnect
    if (this.status.currentConnection.server?.url === bestServer.url) {
      return;
    }

    // Switch to the best server
    console.log(`🔄 [HybridSync] Switching to ${bestServer.syncType} server: ${bestServer.url}`);
    await this.connectToServer(bestServer);
  }

  private getBestServer(): HybridSyncServer | null {
    const { preferLocalSync } = this.config!;
    
    // Prefer local servers if available and preference is set
    if (preferLocalSync && this.status.localServers.length > 0) {
      // Return fastest local server
      return this.status.localServers.sort((a, b) => 
        (a.responseTime || Infinity) - (b.responseTime || Infinity)
      )[0];
    }

    // If no local servers or preference is internet, try internet
    if (this.status.internetPeers.length > 0) {
      // Return most recently seen internet peer
      return this.status.internetPeers.sort((a, b) => 
        b.lastSeen!.getTime() - a.lastSeen!.getTime()
      )[0];
    }

    // Fallback to any local server
    if (this.status.localServers.length > 0) {
      return this.status.localServers.sort((a, b) => 
        (a.responseTime || Infinity) - (b.responseTime || Infinity)
      )[0];
    }

    return null;
  }

  private async connectToServer(server: HybridSyncServer): Promise<void> {
    // Disconnect current connection first
    await this.disconnectCurrentSync();

    try {
      let success = false;

      if (server.syncType === 'local') {
        success = await nativeSyncService.startSync(server, {
          live: true,
          retry: true
        });
        
        if (success) {
          this.status.localConnected = true;
        }
      } else if (server.syncType === 'internet') {
        const peer = this.status.internetPeers.find(p => p.peerId === server.peerId);
        if (peer) {
          success = await nativeSyncService.startSync(server, {
            live: true,
            retry: true,
            isInternetProxy: true,
            authToken: this.config!.authToken
          });
          
          if (success) {
            this.status.internetConnected = true;
          }
        }
      }

      if (success) {
        this.status.currentConnection = { server, type: server.syncType };
        this.status.error = null;
        console.log(`✅ [HybridSync] Connected to ${server.syncType} server: ${server.url}`);
      } else {
        console.error(`❌ [HybridSync] Failed to connect to ${server.syncType} server: ${server.url}`);
      }
    } catch (error: any) {
      console.error(`❌ [HybridSync] Connection error:`, error);
      this.status.error = error.message;
    }

    this.emitStatusUpdate();
  }

  private async disconnectCurrentSync(): Promise<void> {
    if (this.status.currentConnection.server) {
      await nativeSyncService.stopSync();
      this.status.currentConnection = { server: null, type: null };
      this.status.localConnected = false;
      this.status.internetConnected = false;
    }
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  // Public API
  onStatusChange(listener: (status: HybridStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  getStatus(): HybridStatus {
    return { ...this.status };
  }

  async discover(): Promise<void> {
    await Promise.all([
      this.performLocalDiscovery(),
      this.performInternetDiscovery()
    ]);
  }

  updateConfig(config: Partial<HybridConfig>): void {
    if (!this.config) return;
    
    this.config = { ...this.config, ...config };
    
    // Update service configurations
    if (config.vpsBaseUrl || config.authToken || config.deviceId || config.deviceType) {
      internetDiscoveryService.configure({
        vpsBaseUrl: this.config.vpsBaseUrl,
        authToken: this.config.authToken,
        deviceId: this.config.deviceId,
        deviceType: this.config.deviceType
      });

      internetSyncService.configure({
        vpsBaseUrl: this.config.vpsBaseUrl,
        authToken: this.config.authToken,
        deviceId: this.config.deviceId
      });
    }

    // Restart timers if running
    if (this.status.isRunning) {
      this.restartTimers();
    }
  }

  private restartTimers(): void {
    // Clear existing timers
    if (this.localDiscoveryTimer) clearInterval(this.localDiscoveryTimer);
    if (this.internetDiscoveryTimer) clearInterval(this.internetDiscoveryTimer);
    
    // Restart discovery
    this.startPeriodicDiscovery();
  }

  // Utility methods
  isConnected(): boolean {
    return this.status.syncStatus.connected;
  }

  isSyncing(): boolean {
    return this.status.syncStatus.syncing;
  }

  getCurrentConnectionType(): 'local' | 'internet' | null {
    return this.status.currentConnection.type;
  }

  hasLocalServers(): boolean {
    return this.status.localServers.length > 0;
  }

  hasInternetPeers(): boolean {
    return this.status.internetPeers.length > 0;
  }
}

export const hybridAutonomousSyncManager = new HybridAutonomousSyncManager();
export type { HybridConfig, HybridStatus, HybridSyncServer };