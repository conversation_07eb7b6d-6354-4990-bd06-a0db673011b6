/**
 * Staff Menu Service - Integration layer for staff meal management
 */

import {
  getStaffMenuConfig,
  getActiveStaffMenuItems,
  canStaffOrder,
  useStaffAllowance,
  markStaffPresent
} from '../db/v4/operations/staff-menu-ops';
import { StaffMenuItem } from '../db/v4/schemas/staff-menu-schema';
import { OrderItem, OrderAddon } from '../db/v4/schemas/order-schema';
import { createOrder } from '../db/v4/operations/order-ops';

/**
 * Get current date in YYYY-MM-DD format
 */
function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0];
}

/**
 * Check if staff menu is enabled and has items
 */
export async function isStaffMenuEnabled(): Promise<boolean> {
  try {
    const config = await getStaffMenuConfig();
    const activeItems = await getActiveStaffMenuItems();
    return config.isEnabled && activeItems.length > 0;
  } catch (error) {
    console.error('Error checking staff menu status:', error);
    return false;
  }
}

/**
 * Get staff menu items with enhanced data for ordering
 */
export async function getStaffMenuForOrdering(): Promise<StaffMenuItem[]> {
  try {
    const isEnabled = await isStaffMenuEnabled();
    if (!isEnabled) return [];
    
    return await getActiveStaffMenuItems();
  } catch (error) {
    console.error('Error getting staff menu for ordering:', error);
    return [];
  }
}

/**
 * Check if a staff member can order from the staff menu
 */
export async function checkStaffOrderEligibility(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string,
  itemsToOrder: number = 1
): Promise<{
  canOrder: boolean;
  reason?: string;
  allowanceUsed?: number;
  allowanceRemaining?: number;
  maxAllowance?: number;
}> {
  try {
    const date = getCurrentDate();
    const result = await canStaffOrder(staffId, staffName, shiftId, shiftName, date, itemsToOrder);
    
    return {
      canOrder: result.canOrder,
      reason: result.reason,
      allowanceUsed: result.allowance?.usedAllowance,
      allowanceRemaining: result.allowance?.remainingAllowance,
      maxAllowance: result.allowance?.maxAllowance
    };
  } catch (error) {
    console.error('Error checking staff order eligibility:', error);
    return {
      canOrder: false,
      reason: 'System error checking eligibility'
    };
  }
}

/**
 * Mark staff member as present for their current shift
 */
export async function markStaffPresentForShift(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const date = getCurrentDate();
    await markStaffPresent(staffId, staffName, shiftId, shiftName, date);
    return { success: true };
  } catch (error: any) {
    console.error('Error marking staff as present:', error);
    return {
      success: false,
      error: error.message || 'Failed to mark staff as present'
    };
  }
}

/**
 * Create a staff order using special staff prices
 */
export async function createStaffOrder(
  staffId: string,
  staffName: string,
  shiftId: string,
  shiftName: string,
  staffMenuItems: Array<{
    staffMenuItem: StaffMenuItem;
    quantity: number;
    addons?: OrderAddon[];
    notes?: string;
  }>,
  orderMetadata?: {
    tableId?: string;
    notes?: string;
    customerName?: string;
  }
): Promise<{ 
  success: boolean; 
  orderId?: string; 
  error?: string;
  allowanceUsed?: number;
  allowanceRemaining?: number;
}> {
  try {
    const date = getCurrentDate();
    const totalItemsToOrder = staffMenuItems.reduce((sum, item) => sum + item.quantity, 0);
    
    // Check eligibility first
    const eligibility = await checkStaffOrderEligibility(staffId, staffName, shiftId, shiftName, totalItemsToOrder);
    
    if (!eligibility.canOrder) {
      return {
        success: false,
        error: eligibility.reason || 'Staff member cannot order at this time'
      };
    }
    
    // Convert staff menu items to order items with special pricing
    const orderItems: OrderItem[] = staffMenuItems.map(({ staffMenuItem, quantity, addons, notes }, index) => ({
      id: `staff-item-${index}`, // Add required id field
      menuItemId: staffMenuItem.menuItemId,
      name: staffMenuItem.itemName,
      size: staffMenuItem.size,
      price: staffMenuItem.staffPrice, // Use staff price instead of original price
      quantity,
      addons: addons || [],
      notes: notes || ''
    }));
    
    // Calculate total with staff prices
    const total = orderItems.reduce((sum, item) => {
      const itemTotal = item.price * item.quantity;
      const addonsTotal = item.addons.reduce((addonSum, addon) => 
        addonSum + (addon.price * item.quantity), 0);
      return sum + itemTotal + addonsTotal;
    }, 0);
    
    // Create the order
    const order = await createOrder({
      tableId: orderMetadata?.tableId || null,
      items: orderItems,
      orderType: 'dine-in', // Use standard order type instead of 'staff'
      status: 'preparing', // Use valid status
      total,
      paymentStatus: 'paid', // Staff meals are considered "paid" through allowance
      paymentDetails: {
        amountPaid: total,
        amountDue: 0,
        paidAt: new Date().toISOString()
      },
      customer: {
        name: orderMetadata?.customerName || `Commande Équipe - ${staffName}`,
        phone: ''
      },
      notes: `🍽️ Commande Équipe - ${staffName} (${shiftName}) - ${orderMetadata?.notes || `${totalItemsToOrder} items`} - Staff ID: ${staffId}`,
      createdByName: staffName
    });
    
    // Use the staff allowance (skip for collective orders)
    if (staffId !== 'staff-collective') {
      const allowanceResult = await useStaffAllowance(
        staffId, 
        staffName, 
        shiftId, 
        shiftName, 
        date, 
        order._id, 
        totalItemsToOrder
      );
      
      if (!allowanceResult.success) {
        return {
          success: false,
          error: allowanceResult.error || 'Failed to use staff allowance'
        };
      }
      return {
        success: true,
        orderId: order._id,
        allowanceUsed: allowanceResult.allowance?.usedAllowance,
        allowanceRemaining: allowanceResult.allowance?.remainingAllowance
      };
    }
    // For collective orders, no allowance tracking
    return {
      success: true,
      orderId: order._id
    };
    
  } catch (error: any) {
    console.error('Error creating staff order:', error);
    return {
      success: false,
      error: error.message || 'Failed to create staff order'
    };
  }
}

/**
 * Get staff menu summary for display
 */
export async function getStaffMenuSummary(): Promise<{
  isEnabled: boolean;
  itemCount: number;
  allowancePerShift: number;
  categories: Array<{
    name: string;
    items: StaffMenuItem[];
  }>;
}> {
  try {
    const config = await getStaffMenuConfig();
    const items = await getActiveStaffMenuItems();
    
    // Group items by category
    const categoriesMap = new Map<string, StaffMenuItem[]>();
    items.forEach(item => {
      const existing = categoriesMap.get(item.categoryName) || [];
      existing.push(item);
      categoriesMap.set(item.categoryName, existing);
    });
    
    const categories = Array.from(categoriesMap.entries()).map(([name, items]) => ({
      name,
      items
    }));
    
    return {
      isEnabled: config.isEnabled,
      itemCount: items.length,
      allowancePerShift: config.allowancePerShift,
      categories
    };
  } catch (error) {
    console.error('Error getting staff menu summary:', error);
    return {
      isEnabled: false,
      itemCount: 0,
      allowancePerShift: 0,
      categories: []
    };
  }
}

/**
 * Helper to check if an order is a staff order
 */
export function isStaffOrder(order: any): boolean {
  return order.type === 'staff' || order.metadata?.isStaffOrder === true;
}

/**
 * Helper to get staff order details
 */
export function getStaffOrderDetails(order: any): {
  staffId?: string;
  staffName?: string;
  shiftId?: string;
  shiftName?: string;
  allowanceUsed?: number;
} | null {
  if (!isStaffOrder(order)) return null;
  
  return {
    staffId: order.metadata?.staffId,
    staffName: order.metadata?.staffName,
    shiftId: order.metadata?.shiftId,
    shiftName: order.metadata?.shiftName,
    allowanceUsed: order.metadata?.allowanceUsed
  };
} 