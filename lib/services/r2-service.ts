import { S3Client, PutObjectCommand, GetObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';

/**
 * 🚀 Cloudflare R2 Service
 * 
 * Handles file uploads and downloads for Windows releases
 * Uses S3-compatible API to interact with Cloudflare R2
 */

// R2 Configuration
const R2_CONFIG = {
  accessKeyId: process.env.R2_ACCESS_KEY_ID!,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  endpoint: process.env.R2_ENDPOINT!,
  region: process.env.R2_REGION || 'auto',
  bucketName: process.env.R2_BUCKET_NAME!,
};

// Initialize S3 client for R2
const r2Client = new S3Client({
  region: R2_CONFIG.region,
  endpoint: R2_CONFIG.endpoint,
  credentials: {
    accessKeyId: R2_CONFIG.accessKeyId,
    secretAccessKey: R2_CONFIG.secretAccessKey,
  },
  forcePathStyle: true, // Required for R2
});

export class R2Service {
  /**
   * 📤 Upload a file to R2
   */
  static async uploadFile(
    key: string, 
    fileBuffer: Buffer, 
    contentType: string = 'application/octet-stream'
  ): Promise<string> {
    try {
      console.log(`📤 [R2] Uploading file: ${key}`);
      
      const uploader = new Upload({
        client: r2Client,
        params: {
          Bucket: R2_CONFIG.bucketName,
          Key: key,
          Body: fileBuffer,
          ContentType: contentType,
          ACL: 'public-read',
        },
      });

      uploader.on('httpUploadProgress', (progress) => {
        console.log(`📤 [R2] Progress: ${progress.loaded}/${progress.total} (${((progress.loaded / progress.total) * 100).toFixed(2)}%)`);
      });

      await uploader.done();
      
      // Return the public URL
      const publicUrl = `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${key}`;
      console.log(`✅ [R2] File uploaded successfully: ${publicUrl}`);
      
      return publicUrl;
    } catch (error) {
      console.error('❌ [R2] Upload failed:', error);
      throw new Error(`Failed to upload file to R2: ${error}`);
    }
  }

  /**
   * 📥 Get a signed download URL for a file
   */
  static async getDownloadUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      console.log(`📥 [R2] Generating download URL for: ${key}`);
      
      const command = new GetObjectCommand({
        Bucket: R2_CONFIG.bucketName,
        Key: key,
      });

      const signedUrl = await getSignedUrl(r2Client, command, { expiresIn });
      console.log(`✅ [R2] Download URL generated`);
      
      return signedUrl;
    } catch (error) {
      console.error('❌ [R2] Failed to generate download URL:', error);
      throw new Error(`Failed to generate download URL: ${error}`);
    }
  }

  /**
   * 🔍 Check if a file exists in R2
   */
  static async fileExists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: R2_CONFIG.bucketName,
        Key: key,
      });

      await r2Client.send(command);
      return true;
    } catch (error: any) {
      if (error.name === 'NotFound') {
        return false;
      }
      throw error;
    }
  }

  /**
   * 📋 Get file metadata
   */
  static async getFileMetadata(key: string) {
    try {
      const command = new HeadObjectCommand({
        Bucket: R2_CONFIG.bucketName,
        Key: key,
      });

      const response = await r2Client.send(command);
      return {
        size: response.ContentLength,
        lastModified: response.LastModified,
        contentType: response.ContentType,
        etag: response.ETag,
      };
    } catch (error) {
      console.error('❌ [R2] Failed to get file metadata:', error);
      throw new Error(`Failed to get file metadata: ${error}`);
    }
  }

  /**
   * 🌐 Get public URL for a file (if bucket is public)
   */
  static getPublicUrl(key: string): string {
    return `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${key}`;
  }
}

export default R2Service; 