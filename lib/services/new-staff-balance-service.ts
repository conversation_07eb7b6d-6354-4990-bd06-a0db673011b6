"use client";

/**
 * NEW STAFF BALANCE SERVICE
 * 
 * This service implements the new separate balance payment system with:
 * - Independent tracking for advances, deductions, and bonuses
 * - Payment snapshots instead of individual transaction history
 * - Clean, accurate balance calculations
 * - No intertwining of different balance types
 */

import { getCurrentRestaurantId } from '../db/v4/utils/restaurant-id';
import {
  StaffBalanceDocument,
  PaymentSnapshotDocument,
  BalanceType,
  BalanceSummary,
  PaymentCalculation
} from '../db/v4/schemas/new-payment-schemas';
import {
  createPaymentSnapshot as dbCreatePaymentSnapshot,
  getStaffPaymentSnapshots
} from '../db/v4/operations/new-payment-ops';
import { v4 as uuidv4 } from 'uuid';

// ===== DATABASE INITIALIZATION HELPER =====

/**
 * Get the database instance dynamically to ensure we get the initialized instance
 */
async function getDatabase() {
  const { databaseV4 } = await import('../db/v4/core/db-instance');
  return databaseV4;
}

/**
 * Ensure database is properly initialized and wait if necessary
 */
async function ensureDatabaseReady(): Promise<void> {
  try {
    const databaseV4 = await getDatabase();

    // First check if already initialized
    if (databaseV4.isInitialized && databaseV4.getCurrentRestaurantId()) {
      return;
    }

    console.log('🔄 Database not ready, waiting for initialization...');

    // Get restaurant ID
    const restaurantId = getCurrentRestaurantId();
    if (!restaurantId) {
      throw new Error('No restaurant ID available for database operations');
    }

    // Wait for proper initialization
    await databaseV4.waitForInitialization(restaurantId, 10000); // 10 second timeout

    console.log('✅ Database initialization complete');
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw new Error('Database not ready. Please refresh the page and try again.');
  }
}

// ===== VALIDATION FUNCTIONS =====

/**
 * Validate staff ID exists
 */
async function validateStaffExists(staffId: string): Promise<void> {
  try {
    const databaseV4 = await getDatabase();

    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff',
        id: staffId
      },
      limit: 1
    });

    if (result.docs.length === 0) {
      throw new Error(`Staff member with ID ${staffId} not found`);
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes('not found')) {
      throw error;
    }
    console.warn(`Could not validate staff existence for ${staffId}:`, error);
    // Continue anyway - staff validation is optional
  }
}

/**
 * Validate balance amount
 */
function validateAmount(amount: number, context: string): void {
  if (typeof amount !== 'number' || isNaN(amount)) {
    throw new Error(`${context}: Amount must be a valid number`);
  }
  if (amount <= 0) {
    throw new Error(`${context}: Amount must be greater than 0`);
  }
  if (amount > 1000000) {
    throw new Error(`${context}: Amount cannot exceed 1,000,000 DA`);
  }
  if (!Number.isFinite(amount)) {
    throw new Error(`${context}: Amount must be a finite number`);
  }
}

/**
 * Validate reason text
 */
function validateReason(reason: string, context: string): void {
  if (typeof reason !== 'string') {
    throw new Error(`${context}: Reason must be a string`);
  }
  if (!reason || reason.trim().length === 0) {
    throw new Error(`${context}: Reason is required`);
  }
  if (reason.trim().length > 500) {
    throw new Error(`${context}: Reason cannot exceed 500 characters`);
  }
}

/**
 * Validate date string
 */
function validateDate(date: string, context: string): void {
  if (typeof date !== 'string') {
    throw new Error(`${context}: Date must be a string`);
  }

  const parsedDate = new Date(date);
  if (isNaN(parsedDate.getTime())) {
    throw new Error(`${context}: Invalid date format`);
  }

  const now = new Date();
  const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());

  if (parsedDate < oneYearAgo) {
    throw new Error(`${context}: Date cannot be more than 1 year in the past`);
  }
  if (parsedDate > oneYearFromNow) {
    throw new Error(`${context}: Date cannot be more than 1 year in the future`);
  }
}

// ===== BALANCE ADDITION FUNCTIONS =====

/**
 * Add an advance for a staff member
 */
export async function addAdvance(params: {
  staffId: string;
  amount: number;
  reason?: string;
  date?: string;
}): Promise<StaffBalanceDocument> {
  return await addBalance({
    ...params,
    reason: params.reason || 'Avance',
    balanceType: 'ADVANCE'
  });
}

/**
 * Add a deduction for a staff member
 */
export async function addDeduction(params: {
  staffId: string;
  amount: number;
  reason?: string;
  date?: string;
}): Promise<StaffBalanceDocument> {
  return await addBalance({
    ...params,
    reason: params.reason || 'Déduction',
    balanceType: 'DEDUCTION'
  });
}

/**
 * Add a bonus for a staff member
 */
export async function addBonus(params: {
  staffId: string;
  amount: number;
  reason?: string;
  date?: string;
}): Promise<StaffBalanceDocument> {
  return await addBalance({
    ...params,
    reason: params.reason || 'Prime',
    balanceType: 'BONUS'
  });
}

/**
 * Core function to add any type of balance
 */
async function addBalance(params: {
  staffId: string;
  amount: number;
  reason: string;
  balanceType: BalanceType;
  date?: string;
}): Promise<StaffBalanceDocument> {
  try {
    console.log(`💰 Adding ${params.balanceType} for staff ${params.staffId}: ${params.amount}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    // Comprehensive validation
    if (!params.staffId || typeof params.staffId !== 'string') {
      throw new Error('Valid staffId is required');
    }

    validateAmount(params.amount, `Adding ${params.balanceType}`);
    validateReason(params.reason, `Adding ${params.balanceType}`);

    if (params.date) {
      validateDate(params.date, `Adding ${params.balanceType}`);
    }

    // Validate staff exists (optional - continues if validation fails)
    await validateStaffExists(params.staffId);

    const now = new Date().toISOString();
    const dateStr = params.date || now;
    const uuid = uuidv4();
    const datePrefix = dateStr.substring(0, 10); // YYYY-MM-DD

    const balanceDoc: StaffBalanceDocument = {
      _id: `staff_balance:${datePrefix}-${uuid}`,
      type: 'staff_balance',
      staffId: params.staffId,
      balanceType: params.balanceType,
      amount: Math.abs(params.amount), // Always store as positive
      reason: params.reason.trim(),
      date: dateStr,
      isUsed: false,
      createdAt: now
    };

    // Save to database using the same pattern as existing system
    const databaseV4 = await getDatabase();
    const result = await databaseV4.putDoc(balanceDoc);
    console.log(`✅ ${params.balanceType} added successfully:`, result.id);

    // Return the document with the revision
    return {
      ...balanceDoc,
      _rev: result.rev
    };
  } catch (error) {
    console.error(`❌ Error adding ${params.balanceType}:`, error);
    throw error;
  }
}

// ===== BALANCE RETRIEVAL FUNCTIONS =====

/**
 * Get all balances for a staff member (used and unused)
 */
export async function getAllBalances(staffId: string): Promise<BalanceSummary> {
  try {
    console.log(`📊 Getting all balances for staff ${staffId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    if (!staffId) {
      throw new Error('staffId is required');
    }

    const databaseV4 = await getDatabase();

    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        isUsed: false
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { date: 'desc' }]
    });

    console.log(`🔍 Found ${result.docs.length} balance entries for staff ${staffId}`);
    const balances = result.docs as StaffBalanceDocument[];

    if (balances.length > 0) {
      console.log('📋 Balance entries:', balances.map(b => ({
        type: b.balanceType,
        amount: b.amount,
        reason: b.reason,
        isUsed: b.isUsed
      })));
    }

    const summary: BalanceSummary = {
      advances: 0,
      deductions: 0,
      bonuses: 0,
      total: 0
    };

    balances.forEach(balance => {
      switch (balance.balanceType) {
        case 'ADVANCE':
          summary.advances += balance.amount;
          break;
        case 'DEDUCTION':
          summary.deductions += balance.amount;
          break;
        case 'BONUS':
          summary.bonuses += balance.amount;
          break;
      }
    });

    // Calculate net total (bonuses - deductions - advances)
    summary.total = summary.bonuses - summary.deductions - summary.advances;

    console.log(`📊 Balance summary for ${staffId}:`, summary);
    return summary;
  } catch (error) {
    console.error(`❌ Error getting balances for ${staffId}:`, error);
    throw error;
  }
}

/**
 * Get unused balances for a staff member
 */
export async function getUnusedBalances(staffId: string): Promise<StaffBalanceDocument[]> {
  try {
    console.log(`📋 Getting unused balances for staff ${staffId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    if (!staffId) {
      throw new Error('staffId is required');
    }

    const databaseV4 = await getDatabase();
    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        isUsed: false
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { date: 'desc' }]
    });

    const balances = result.docs as StaffBalanceDocument[];
    console.log(`📋 Found ${balances.length} unused balance entries`);
    
    return balances;
  } catch (error) {
    console.error(`❌ Error getting unused balances for ${staffId}:`, error);
    throw error;
  }
}

/**
 * Get balances by type for a staff member
 */
export async function getBalancesByType(staffId: string, balanceType: BalanceType): Promise<StaffBalanceDocument[]> {
  try {
    console.log(`📋 Getting ${balanceType} balances for staff ${staffId}`);

    if (!staffId) {
      throw new Error('staffId is required');
    }

    const databaseV4 = await getDatabase();
    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        balanceType: balanceType
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { balanceType: 'asc' }, { date: 'desc' }]
    });

    const balances = result.docs as StaffBalanceDocument[];
    console.log(`📋 Found ${balances.length} ${balanceType} entries`);
    
    return balances;
  } catch (error) {
    console.error(`❌ Error getting ${balanceType} balances for ${staffId}:`, error);
    throw error;
  }
}

// ===== BALANCE UPDATE FUNCTIONS =====

/**
 * Mark balance entries as used in a payment
 */
export async function markBalancesAsUsed(
  balanceIds: string[], 
  paymentSnapshotId: string
): Promise<void> {
  try {
    console.log(`🔄 Marking ${balanceIds.length} balances as used in payment ${paymentSnapshotId}`);

    // VULNERABILITY FIX: Validate inputs to prevent corruption
    if (!Array.isArray(balanceIds) || balanceIds.length === 0) {
      console.warn('⚠️ No balance IDs provided to mark as used');
      return;
    }
    
    if (!paymentSnapshotId || typeof paymentSnapshotId !== 'string') {
      throw new Error('Invalid payment snapshot ID provided');
    }

    // Remove duplicates to prevent double processing
    const uniqueBalanceIds = [...new Set(balanceIds)];
    if (uniqueBalanceIds.length !== balanceIds.length) {
      console.warn(`⚠️ Removed ${balanceIds.length - uniqueBalanceIds.length} duplicate balance IDs`);
    }

    const databaseV4 = await getDatabase();
    const usedDate = new Date().toISOString();
    let markedCount = 0;
    let alreadyUsedCount = 0;

    for (const balanceId of uniqueBalanceIds) {
      try {
        const balance = await databaseV4.getDoc(balanceId) as StaffBalanceDocument;

        // VULNERABILITY FIX: Enhanced validation for balance state
        if (!balance || balance.type !== 'staff_balance') {
          console.warn(`⚠️ Invalid balance document: ${balanceId}`);
          continue;
        }

        if (balance.isUsed) {
          console.warn(`⚠️ Balance ${balanceId} is already marked as used in payment ${balance.usedInPaymentId}`);
          alreadyUsedCount++;
          continue;
        }

        const updatedBalance: StaffBalanceDocument = {
          ...balance,
          isUsed: true,
          usedInPaymentId: paymentSnapshotId,
          usedDate: usedDate,
          updatedAt: usedDate
        };

        await databaseV4.putDoc(updatedBalance);
        markedCount++;
      } catch (docError) {
        console.error(`❌ Error processing balance ${balanceId}:`, docError);
        // Continue processing other balances rather than failing completely
      }
    }

    console.log(`✅ Successfully marked ${markedCount} balances as used (${alreadyUsedCount} already used)`);
    
    if (markedCount === 0 && alreadyUsedCount === 0) {
      throw new Error('No balances were successfully marked as used - possible data corruption');
    }
  } catch (error) {
    console.error(`❌ Error marking balances as used:`, error);
    throw error;
  }
}

/**
 * Calculate payment amounts from current balances
 */
export async function calculatePaymentAmounts(
  staffId: string,
  baseSalary: number,
  options: {
    useAllBonuses?: boolean;
    useAllDeductions?: boolean;
    useAllAdvances?: boolean;
    specificBalanceIds?: string[];
  } = {}
): Promise<PaymentCalculation> {
  try {
    console.log(`🧮 Calculating payment for staff ${staffId} with base salary ${baseSalary}`);

    if (baseSalary < 0) {
      throw new Error('Base salary cannot be negative');
    }

    let bonusAmount = 0;
    let deductionAmount = 0;
    let advanceAmount = 0;

    if (options.specificBalanceIds && options.specificBalanceIds.length > 0) {
      // Use specific balance IDs
      const databaseV4 = await getDatabase();
      for (const balanceId of options.specificBalanceIds) {
        const balance = await databaseV4.getDoc(balanceId) as StaffBalanceDocument;
        if (balance.isUsed) continue;

        switch (balance.balanceType) {
          case 'BONUS':
            bonusAmount += balance.amount;
            break;
          case 'DEDUCTION':
            deductionAmount += balance.amount;
            break;
          case 'ADVANCE':
            advanceAmount += balance.amount;
            break;
        }
      }
    } else {
      // Use all unused balances based on options
      const unusedBalances = await getUnusedBalances(staffId);

      unusedBalances.forEach(balance => {
        // Validate balance amounts to prevent corruption
        const safeAmount = Math.max(0, balance.amount || 0);
        
        switch (balance.balanceType) {
          case 'BONUS':
            if (options.useAllBonuses !== false) {
              bonusAmount += safeAmount;
            }
            break;
          case 'DEDUCTION':
            if (options.useAllDeductions !== false) {
              deductionAmount += safeAmount;
            }
            break;
          case 'ADVANCE':
            if (options.useAllAdvances !== false) {
              advanceAmount += safeAmount;
            }
            break;
        }
      });
    }

    // VULNERABILITY FIX: Add safety checks for payment calculation
    const grossAmount = Math.max(0, baseSalary + bonusAmount);
    
    // Ensure advance repayment doesn't exceed available funds
    const maxAllowableAdvance = Math.max(0, grossAmount - deductionAmount);
    const safeAdvanceAmount = Math.min(advanceAmount, maxAllowableAdvance);
    const totalDeductions = Math.max(0, deductionAmount + safeAdvanceAmount);
    const netAmount = Math.max(0, grossAmount - totalDeductions);

    // SECURITY VALIDATION: Prevent invalid payment states
    if (netAmount < 0) {
      throw new Error('Calculated net payment is negative - payment calculation error');
    }
    if (totalDeductions > grossAmount + 0.01) {
      throw new Error('Total deductions exceed gross payment - invalid calculation');
    }

    const calculation: PaymentCalculation = {
      baseSalary,
      bonusAmount,
      deductionAmount,
      advanceAmount: safeAdvanceAmount, // Use validated advance amount
      grossAmount,
      totalDeductions,
      netAmount
    };

    console.log(`🧮 Payment calculation:`, calculation);
    return calculation;
  } catch (error) {
    console.error(`❌ Error calculating payment amounts:`, error);
    throw error;
  }
}

// ===== PAYMENT SNAPSHOT FUNCTIONS =====

/**
 * Create a per-shift payment snapshot with shift data and balance integration
 */
export async function createPerShiftPaymentSnapshot(params: {
  staffId: string;
  shiftData: {
    attendanceIds: string[];
    shiftBreakdown: Array<{
      shiftId: string;
      shiftName: string;
      count: number;
      rate: number;
      amount: number;
    }>;
  };
  useAllBonuses?: boolean;
  useAllDeductions?: boolean;
  useAllAdvances?: boolean;
  notes?: string;
}): Promise<PaymentSnapshotDocument> {
  try {
    console.log(`💳 Creating per-shift payment snapshot for staff ${params.staffId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    // VULNERABILITY FIX: Enhanced input validation for type safety
    if (!params.staffId || typeof params.staffId !== 'string') {
      throw new Error('Valid staff ID is required for per-shift payment');
    }

    if (!params.shiftData || typeof params.shiftData !== 'object') {
      throw new Error('Valid shift data is required for per-shift payment');
    }

    // Validate inputs
    await validateStaffExists(params.staffId);
    
    // VULNERABILITY FIX: Validate staff is actually per-shift type
    const databaseV4 = await getDatabase();
    const staffResult = await databaseV4.findDocs({
      selector: {
        type: 'staff',
        id: params.staffId
      },
      limit: 1
    });
    
    if (staffResult.docs.length > 0) {
      const staff = staffResult.docs[0] as any;
      if (staff.paymentConfig?.type !== 'PER_SHIFT') {
        throw new Error(`Staff member ${params.staffId} is not configured for per-shift payments (current type: ${staff.paymentConfig?.type})`);
      }
    }

    if (!params.shiftData.attendanceIds.length) {
      throw new Error('At least one attendance ID is required for per-shift payment');
    }

    if (!params.shiftData.shiftBreakdown.length) {
      throw new Error('Shift breakdown is required for per-shift payment');
    }

    // Calculate base shift amount
    const baseShiftAmount = params.shiftData.shiftBreakdown.reduce((sum, shift) => sum + shift.amount, 0);

    if (baseShiftAmount <= 0) {
      throw new Error('Base shift amount must be greater than 0');
    }

    // Get current balances
    const balances = await getAllBalances(params.staffId);

    // Calculate adjustment amounts based on toggles with proper validation
    const bonusAmount = (params.useAllBonuses && balances.bonuses > 0) ? Math.max(0, balances.bonuses) : 0;
    const deductionAmount = (params.useAllDeductions && balances.deductions > 0) ? Math.max(0, balances.deductions) : 0;
    
    // VULNERABILITY FIX: Calculate advance amount more safely to prevent negative balances
    // Ensure advance repayment never exceeds what can actually be paid back
    const grossAmountBeforeAdvance = baseShiftAmount + bonusAmount - deductionAmount;
    const advanceAmount = (params.useAllAdvances && balances.advances > 0) ?
      Math.min(
        Math.max(0, balances.advances), // Ensure advances is not negative
        Math.max(0, grossAmountBeforeAdvance) // Ensure we don't deduct more than available
      ) : 0;

    // Calculate final amounts with additional safeguards
    const grossAmount = Math.max(0, baseShiftAmount + bonusAmount);
    const totalDeductions = Math.max(0, deductionAmount + advanceAmount);
    const netAmount = Math.max(0, grossAmount - totalDeductions);
    
    // SECURITY CHECK: Validate calculation results
    if (netAmount < 0) {
      throw new Error('Payment calculation resulted in negative net amount - this should not happen');
    }
    if (totalDeductions > grossAmount + 1) { // Allow for small floating point errors
      throw new Error('Total deductions exceed gross amount - payment calculation error');
    }

    console.log(`💰 Per-shift payment calculation:`, {
      baseShiftAmount,
      bonusAmount,
      deductionAmount,
      advanceAmount,
      grossAmount,
      totalDeductions,
      netAmount
    });

    // Create the payment snapshot with shift-specific data
    const snapshot: PaymentSnapshotDocument = {
      _id: uuidv4(),
      type: 'payment_snapshot',
      staffId: params.staffId,
      paymentDate: new Date().toISOString(),
      paymentType: 'SHIFT_PAYMENT',
      baseSalary: baseShiftAmount, // For per-shift, this is the shift earnings
      bonusAmount,
      deductionAmount,
      advanceAmount,
      grossAmount,
      totalDeductions,
      netAmount,
      notes: params.notes || `Per-shift payment: ${params.shiftData.attendanceIds.length} shifts`,
      createdAt: new Date().toISOString(),
      // Per-shift specific metadata
      shiftData: {
        attendanceIds: params.shiftData.attendanceIds,
        shiftBreakdown: params.shiftData.shiftBreakdown,
        totalShifts: params.shiftData.shiftBreakdown.reduce((sum, shift) => sum + shift.count, 0)
      }
    };

    // Save the payment snapshot
    const savedSnapshot = await databaseV4.putDoc(snapshot);
    const snapshotId = savedSnapshot.id;

    // Mark balances as used if they were applied
    const balanceIdsToMark: string[] = [];

    // VULNERABILITY FIX: Prevent double counting by carefully tracking which balances to mark
    if (bonusAmount > 0) {
      const bonusBalances = await getBalancesByType(params.staffId, 'BONUS');
      const unusedBonusBalances = bonusBalances.filter(b => !b.isUsed);
      let remainingBonus = bonusAmount;
      
      for (const bonus of unusedBonusBalances) {
        if (remainingBonus <= 0) break;
        if (!balanceIdsToMark.includes(bonus._id)) { // Prevent double marking
          balanceIdsToMark.push(bonus._id);
          remainingBonus -= bonus.amount;
        }
      }
    }

    if (deductionAmount > 0) {
      const deductionBalances = await getBalancesByType(params.staffId, 'DEDUCTION');
      const unusedDeductionBalances = deductionBalances.filter(b => !b.isUsed);
      let remainingDeduction = deductionAmount;
      
      for (const deduction of unusedDeductionBalances) {
        if (remainingDeduction <= 0) break;
        if (!balanceIdsToMark.includes(deduction._id)) { // Prevent double marking
          balanceIdsToMark.push(deduction._id);
          remainingDeduction -= deduction.amount;
        }
      }
    }

    if (advanceAmount > 0) {
      const advanceBalances = await getBalancesByType(params.staffId, 'ADVANCE');
      const unusedAdvanceBalances = advanceBalances.filter(b => !b.isUsed);
      // Mark advances proportionally based on amount used
      let remainingAdvance = advanceAmount;
      
      for (const advance of unusedAdvanceBalances) {
        if (remainingAdvance <= 0) break;
        if (!balanceIdsToMark.includes(advance._id)) { // Prevent double marking
          balanceIdsToMark.push(advance._id);
          remainingAdvance -= advance.amount;
        }
      }
    }

    // Mark balances as used
    if (balanceIdsToMark.length > 0 && snapshotId) {
      await markBalancesAsUsed(balanceIdsToMark, snapshotId);
    }

    // Mark attendance records as paid
    if (params.shiftData.attendanceIds.length > 0) {
      try {
        const { markAttendanceAsPaid } = await import('../db/v4/operations/per-staff-ops');
        await markAttendanceAsPaid(params.staffId, params.shiftData.attendanceIds);
        console.log(`✅ Marked ${params.shiftData.attendanceIds.length} attendance records as paid`);
      } catch (attendanceError) {
        console.error('⚠️ Failed to mark attendance as paid:', attendanceError);
        // Don't fail the payment if attendance marking fails
      }
    }

    console.log(`✅ Per-shift payment snapshot created successfully:`, {
      snapshotId: snapshotId,
      netAmount,
      balancesMarked: balanceIdsToMark.length,
      attendanceMarked: params.shiftData.attendanceIds.length
    });

    // Return the complete snapshot document with the ID and revision
    return {
      ...snapshot,
      _id: snapshotId,
      _rev: savedSnapshot.rev
    } as PaymentSnapshotDocument;

  } catch (error) {
    console.error(`❌ Error creating per-shift payment snapshot for staff ${params.staffId}:`, error);
    throw error;
  }
}

/**
 * Enhanced payment snapshot creation with strategies
 */
export async function createPaymentSnapshotWithStrategy(params: {
  staffId: string;
  baseSalary: number;
  strategy?: PaymentStrategy;
  strategyOptions?: {
    specificBalanceIds?: string[];
    maxDeductionAmount?: number;
    maxAdvanceAmount?: number;
  };
  notes?: string;
  periodStart?: string;
  periodEnd?: string;
}): Promise<PaymentSnapshotDocument> {
  try {
    console.log(`💳 Creating payment snapshot with strategy for staff ${params.staffId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    // Calculate payment using strategy
    const paymentResult = await calculatePaymentWithStrategy(
      params.staffId,
      params.baseSalary,
      params.strategy || 'USE_ALL_BALANCES',
      params.strategyOptions || {}
    );

    const now = new Date().toISOString();
    const uuid = uuidv4();
    const datePrefix = now.substring(0, 10); // YYYY-MM-DD

    // Create payment snapshot document
    const snapshotData: Omit<PaymentSnapshotDocument, '_id' | '_rev'> = {
      type: 'payment_snapshot',
      staffId: params.staffId,
      paymentDate: now,
      baseSalary: paymentResult.calculation.baseSalary,
      bonusAmount: paymentResult.calculation.bonusAmount,
      deductionAmount: paymentResult.calculation.deductionAmount,
      advanceAmount: paymentResult.calculation.advanceAmount,
      grossAmount: paymentResult.calculation.grossAmount,
      totalDeductions: paymentResult.calculation.totalDeductions,
      netAmount: paymentResult.calculation.netAmount,
      periodStart: params.periodStart,
      periodEnd: params.periodEnd,
      notes: params.notes,
      usedBalanceIds: paymentResult.usedBalanceIds,
      createdAt: now
    };

    // Create the payment snapshot
    const snapshot = await dbCreatePaymentSnapshot(snapshotData);

    // Mark the used balances
    if (paymentResult.usedBalanceIds.length > 0) {
      await markBalancesAsUsed(paymentResult.usedBalanceIds, snapshot._id);
    }

    console.log(`✅ Payment snapshot created successfully: ${snapshot._id}`);
    return snapshot;
  } catch (error) {
    console.error(`❌ Error creating payment snapshot:`, error);
    throw error;
  }
}

/**
 * Delete a payment snapshot and unmark associated attendance records
 */
export async function deletePaymentSnapshot(snapshotId: string): Promise<boolean> {
  try {
    console.log(`🗑️ Deleting payment snapshot: ${snapshotId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    const databaseV4 = await getDatabase();

    // Get the snapshot to check for attendance IDs
    const snapshot = await databaseV4.getDoc(snapshotId) as PaymentSnapshotDocument;
    if (!snapshot) {
      console.warn(`⚠️ Payment snapshot ${snapshotId} not found`);
      return false;
    }

    // Unmark attendance records if this was a per-shift payment
    if (snapshot.shiftData?.attendanceIds?.length > 0) {
      try {
        const { markAttendanceAsPaid } = await import('../db/v4/operations/per-staff-ops');
        // Create a function to unmark attendance (mark as unpaid)
        const attendanceDoc = await databaseV4.findDocs({
          selector: {
            type: 'attendance',
            staffId: snapshot.staffId
          },
          limit: 1
        });

        if (attendanceDoc.docs.length > 0) {
          const attendance = attendanceDoc.docs[0] as any;
          let updated = false;

          attendance.records.forEach((record: any) => {
            if (snapshot.shiftData!.attendanceIds.includes(record.id) && record.isPaid) {
              record.isPaid = false;
              updated = true;
            }
          });

          if (updated) {
            attendance.updatedAt = new Date().toISOString();
            await databaseV4.putDoc(attendance);
            console.log(`✅ Unmarked ${snapshot.shiftData.attendanceIds.length} attendance records as unpaid`);
          }
        }
      } catch (attendanceError) {
        console.error('⚠️ Failed to unmark attendance records:', attendanceError);
        // Continue with snapshot deletion even if attendance unmarking fails
      }
    }

    // Unmark balances as unused if they were marked as used
    if (snapshot.usedBalanceIds?.length > 0) {
      try {
        for (const balanceId of snapshot.usedBalanceIds) {
          const balance = await databaseV4.getDoc(balanceId) as StaffBalanceDocument;
          if (balance && balance.isUsed && balance.usedInPaymentId === snapshotId) {
            const updatedBalance: StaffBalanceDocument = {
              ...balance,
              isUsed: false,
              usedInPaymentId: undefined,
              usedDate: undefined,
              updatedAt: new Date().toISOString()
            };
            await databaseV4.putDoc(updatedBalance);
          }
        }
        console.log(`✅ Unmarked ${snapshot.usedBalanceIds.length} balances as unused`);
      } catch (balanceError) {
        console.error('⚠️ Failed to unmark balances:', balanceError);
        // Continue with snapshot deletion even if balance unmarking fails
      }
    }

    // Delete the snapshot
    await databaseV4.deleteDoc(snapshot._id, snapshot._rev!);
    console.log(`✅ Payment snapshot ${snapshotId} deleted successfully`);

    return true;

  } catch (error) {
    console.error(`❌ Error deleting payment snapshot ${snapshotId}:`, error);
    throw error;
  }
}

/**
 * Create a payment snapshot with complete payment breakdown (legacy method)
 */
export async function createPaymentSnapshot(params: {
  staffId: string;
  baseSalary: number;
  useAllBonuses?: boolean;
  useAllDeductions?: boolean;
  useAllAdvances?: boolean;
  specificBalanceIds?: string[];
  notes?: string;
  periodStart?: string;
  periodEnd?: string;
}): Promise<PaymentSnapshotDocument> {
  try {
    console.log(`💳 Creating payment snapshot for staff ${params.staffId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();
    
    // VULNERABILITY FIX: Enhanced input validation for salary payments
    if (!params.staffId || typeof params.staffId !== 'string') {
      throw new Error('Valid staff ID is required for salary payment');
    }
    
    if (typeof params.baseSalary !== 'number' || isNaN(params.baseSalary) || params.baseSalary < 0) {
      throw new Error('Valid base salary amount is required (must be non-negative)');
    }
    
    // Validate staff is NOT per-shift type for salary payments
    const databaseV4 = await getDatabase();
    const staffResult = await databaseV4.findDocs({
      selector: {
        type: 'staff',
        id: params.staffId
      },
      limit: 1
    });
    
    if (staffResult.docs.length > 0) {
      const staff = staffResult.docs[0] as any;
      if (staff.paymentConfig?.type === 'PER_SHIFT') {
        throw new Error(`Staff member ${params.staffId} is configured for per-shift payments. Use createPerShiftPaymentSnapshot() instead.`);
      }
    }

    // Calculate payment amounts
    const calculation = await calculatePaymentAmounts(
      params.staffId,
      params.baseSalary,
      {
        useAllBonuses: params.useAllBonuses,
        useAllDeductions: params.useAllDeductions,
        useAllAdvances: params.useAllAdvances,
        specificBalanceIds: params.specificBalanceIds
      }
    );

    // Get the balance IDs that will be used
    const unusedBalances = await getUnusedBalances(params.staffId);
    const balanceIdsToUse: string[] = [];

    if (params.specificBalanceIds && params.specificBalanceIds.length > 0) {
      balanceIdsToUse.push(...params.specificBalanceIds);
    } else {
      unusedBalances.forEach(balance => {
        let shouldUse = false;
        switch (balance.balanceType) {
          case 'BONUS':
            shouldUse = params.useAllBonuses !== false;
            break;
          case 'DEDUCTION':
            shouldUse = params.useAllDeductions !== false;
            break;
          case 'ADVANCE':
            shouldUse = params.useAllAdvances !== false;
            break;
        }
        if (shouldUse) {
          balanceIdsToUse.push(balance._id);
        }
      });
    }

    const now = new Date().toISOString();
    const uuid = uuidv4();
    const datePrefix = now.substring(0, 10); // YYYY-MM-DD

    // Create payment snapshot document
    const snapshotData: Omit<PaymentSnapshotDocument, '_id' | '_rev'> = {
      type: 'payment_snapshot',
      staffId: params.staffId,
      paymentDate: now,
      baseSalary: calculation.baseSalary,
      bonusAmount: calculation.bonusAmount,
      deductionAmount: calculation.deductionAmount,
      advanceAmount: calculation.advanceAmount,
      grossAmount: calculation.grossAmount,
      totalDeductions: calculation.totalDeductions,
      netAmount: calculation.netAmount,
      periodStart: params.periodStart,
      periodEnd: params.periodEnd,
      notes: params.notes,
      usedBalanceIds: balanceIdsToUse,
      createdAt: now
    };

    // Create the payment snapshot
    const snapshot = await dbCreatePaymentSnapshot(snapshotData);

    // Mark the used balances
    if (balanceIdsToUse.length > 0) {
      await markBalancesAsUsed(balanceIdsToUse, snapshot._id);
    }

    console.log(`✅ Payment snapshot created successfully: ${snapshot._id}`);
    return snapshot;
  } catch (error) {
    console.error(`❌ Error creating payment snapshot:`, error);
    throw error;
  }
}

/**
 * Get payment history (snapshots only) for a staff member
 */
export async function getPaymentHistory(staffId: string): Promise<PaymentSnapshotDocument[]> {
  try {
    console.log(`📋 Getting payment history for staff ${staffId}`);

    if (!staffId) {
      throw new Error('staffId is required');
    }

    const snapshots = await getStaffPaymentSnapshots(staffId);
    console.log(`📋 Found ${snapshots.length} payment snapshots`);

    return snapshots;
  } catch (error) {
    console.error(`❌ Error getting payment history for ${staffId}:`, error);
    throw error;
  }
}

// ===== ADVANCED BALANCE OPERATIONS =====

/**
 * Get balance summary by date range
 */
export async function getBalancesByDateRange(
  staffId: string,
  startDate: string,
  endDate: string
): Promise<BalanceSummary> {
  try {
    console.log(`📊 Getting balances for staff ${staffId} from ${startDate} to ${endDate}`);

    const databaseV4 = await getDatabase();

    // Check database initialization
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    if (!staffId) {
      throw new Error('staffId is required');
    }

    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        isUsed: false,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { date: 'desc' }]
    });

    const balances = result.docs as StaffBalanceDocument[];

    const summary: BalanceSummary = {
      advances: 0,
      deductions: 0,
      bonuses: 0,
      total: 0
    };

    balances.forEach(balance => {
      switch (balance.balanceType) {
        case 'ADVANCE':
          summary.advances += balance.amount;
          break;
        case 'DEDUCTION':
          summary.deductions += balance.amount;
          break;
        case 'BONUS':
          summary.bonuses += balance.amount;
          break;
      }
    });

    summary.total = summary.bonuses - summary.deductions - summary.advances;

    console.log(`📊 Date range balance summary:`, summary);
    return summary;
  } catch (error) {
    console.error(`❌ Error getting balances by date range:`, error);
    throw error;
  }
}

/**
 * Update an existing balance entry
 */
export async function updateBalance(
  balanceId: string,
  updates: {
    amount?: number;
    reason?: string;
    date?: string;
  }
): Promise<StaffBalanceDocument> {
  try {
    console.log(`🔄 Updating balance ${balanceId}`);

    const databaseV4 = await getDatabase();

    // Check database initialization
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    if (!balanceId) {
      throw new Error('balanceId is required');
    }

    // Get existing balance
    const existingBalance = await databaseV4.getDoc(balanceId) as StaffBalanceDocument;

    if (!existingBalance || existingBalance.type !== 'staff_balance') {
      throw new Error(`Balance ${balanceId} not found`);
    }

    if (existingBalance.isUsed) {
      throw new Error(`Cannot update balance ${balanceId} - it has already been used in a payment`);
    }

    // Validate updates
    if (updates.amount !== undefined) {
      if (updates.amount <= 0) {
        throw new Error('Amount must be greater than 0');
      }
    }

    if (updates.reason !== undefined) {
      if (!updates.reason || updates.reason.trim().length === 0) {
        throw new Error('Reason cannot be empty');
      }
    }

    // Apply updates
    const updatedBalance: StaffBalanceDocument = {
      ...existingBalance,
      ...(updates.amount !== undefined && { amount: Math.abs(updates.amount) }),
      ...(updates.reason !== undefined && { reason: updates.reason.trim() }),
      ...(updates.date !== undefined && { date: updates.date }),
      updatedAt: new Date().toISOString()
    };

    // Save updated balance
    const result = await databaseV4.putDoc(updatedBalance);

    console.log(`✅ Balance ${balanceId} updated successfully`);
    return {
      ...updatedBalance,
      _rev: result.rev
    };
  } catch (error) {
    console.error(`❌ Error updating balance ${balanceId}:`, error);
    throw error;
  }
}

/**
 * Delete an unused balance entry
 */
export async function deleteBalance(balanceId: string): Promise<void> {
  try {
    console.log(`🗑️ Deleting balance ${balanceId}`);

    const databaseV4 = await getDatabase();

    // Check database initialization
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    if (!balanceId) {
      throw new Error('balanceId is required');
    }

    // Get existing balance
    const existingBalance = await databaseV4.getDoc(balanceId) as StaffBalanceDocument;

    if (!existingBalance || existingBalance.type !== 'staff_balance') {
      throw new Error(`Balance ${balanceId} not found`);
    }

    if (existingBalance.isUsed) {
      throw new Error(`Cannot delete balance ${balanceId} - it has already been used in a payment`);
    }

    // Delete the balance
    await databaseV4.deleteDoc(existingBalance._id, existingBalance._rev!);

    console.log(`✅ Balance ${balanceId} deleted successfully`);
  } catch (error) {
    console.error(`❌ Error deleting balance ${balanceId}:`, error);
    throw error;
  }
}

// ===== BULK OPERATIONS =====

/**
 * Add multiple balances in a single operation
 */
export async function addMultipleBalances(
  staffId: string,
  balances: Array<{
    balanceType: BalanceType;
    amount: number;
    reason: string;
    date?: string;
  }>
): Promise<StaffBalanceDocument[]> {
  try {
    console.log(`💰 Adding ${balances.length} balances for staff ${staffId}`);

    const databaseV4 = await getDatabase();

    // Check database initialization
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    if (!staffId || typeof staffId !== 'string') {
      throw new Error('Valid staffId is required');
    }

    if (!Array.isArray(balances) || balances.length === 0) {
      throw new Error('At least one balance entry is required');
    }

    if (balances.length > 50) {
      throw new Error('Cannot add more than 50 balances at once');
    }

    // Validate all balances first
    balances.forEach((balance, index) => {
      const context = `Balance ${index + 1}`;
      validateAmount(balance.amount, context);
      validateReason(balance.reason, context);
      if (balance.date) {
        validateDate(balance.date, context);
      }
      if (!['ADVANCE', 'DEDUCTION', 'BONUS'].includes(balance.balanceType)) {
        throw new Error(`${context}: Invalid balance type ${balance.balanceType}`);
      }
    });

    // Validate staff exists
    await validateStaffExists(staffId);

    // Create all balance documents
    const now = new Date().toISOString();
    const createdBalances: StaffBalanceDocument[] = [];

    for (const balance of balances) {
      const dateStr = balance.date || now;
      const uuid = uuidv4();
      const datePrefix = dateStr.substring(0, 10); // YYYY-MM-DD

      const balanceDoc: StaffBalanceDocument = {
        _id: `staff_balance:${datePrefix}-${uuid}`,
        type: 'staff_balance',
        staffId: staffId,
        balanceType: balance.balanceType,
        amount: Math.abs(balance.amount),
        reason: balance.reason.trim(),
        date: dateStr,
        isUsed: false,
        createdAt: now
      };

      // Save to database
      const result = await databaseV4.putDoc(balanceDoc);
      createdBalances.push({
        ...balanceDoc,
        _rev: result.rev
      });
    }

    console.log(`✅ Successfully added ${createdBalances.length} balances for staff ${staffId}`);
    return createdBalances;
  } catch (error) {
    console.error(`❌ Error adding multiple balances:`, error);
    throw error;
  }
}

/**
 * Get detailed balance breakdown with individual entries
 */
export async function getDetailedBalances(staffId: string): Promise<{
  summary: BalanceSummary;
  entries: {
    advances: StaffBalanceDocument[];
    deductions: StaffBalanceDocument[];
    bonuses: StaffBalanceDocument[];
  };
}> {
  try {
    console.log(`📊 Getting detailed balances for staff ${staffId}`);

    const databaseV4 = await getDatabase();

    // Check database initialization
    if (!databaseV4.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    if (!staffId) {
      throw new Error('staffId is required');
    }

    const result = await databaseV4.findDocs({
      selector: {
        type: 'staff_balance',
        staffId: staffId,
        isUsed: false
      },
      sort: [{ type: 'asc' }, { staffId: 'asc' }, { date: 'desc' }]
    });

    const balances = result.docs as StaffBalanceDocument[];

    const entries = {
      advances: balances.filter(b => b.balanceType === 'ADVANCE'),
      deductions: balances.filter(b => b.balanceType === 'DEDUCTION'),
      bonuses: balances.filter(b => b.balanceType === 'BONUS')
    };

    const summary: BalanceSummary = {
      advances: entries.advances.reduce((sum, b) => sum + b.amount, 0),
      deductions: entries.deductions.reduce((sum, b) => sum + b.amount, 0),
      bonuses: entries.bonuses.reduce((sum, b) => sum + b.amount, 0),
      total: 0
    };

    summary.total = summary.bonuses - summary.deductions - summary.advances;

    console.log(`📊 Detailed balance summary:`, summary);
    return { summary, entries };
  } catch (error) {
    console.error(`❌ Error getting detailed balances:`, error);
    throw error;
  }
}

// ===== ENHANCED PAYMENT CALCULATION SYSTEM =====

/**
 * Payment calculation strategy options
 */
export type PaymentStrategy =
  | 'USE_ALL_BALANCES'           // Use all available balances
  | 'USE_SPECIFIC_BALANCES'      // Use only specified balance IDs
  | 'USE_OLDEST_FIRST'           // Use oldest balances first (FIFO)
  | 'USE_NEWEST_FIRST'           // Use newest balances first (LIFO)
  | 'MAXIMIZE_NET_PAYMENT'       // Prioritize bonuses, minimize deductions
  | 'MINIMIZE_DEDUCTIONS';       // Use minimal deductions/advances

/**
 * Enhanced payment calculation with multiple strategies
 */
export async function calculatePaymentWithStrategy(
  staffId: string,
  baseSalary: number,
  strategy: PaymentStrategy = 'USE_ALL_BALANCES',
  options: {
    specificBalanceIds?: string[];
    maxDeductionAmount?: number;
    maxAdvanceAmount?: number;
    prioritizeBonuses?: boolean;
  } = {}
): Promise<{
  calculation: PaymentCalculation;
  usedBalanceIds: string[];
  unusedBalanceIds: string[];
  strategy: PaymentStrategy;
}> {
  try {
    console.log(`🧮 Calculating payment with strategy: ${strategy}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    validateAmount(baseSalary, 'Base salary');

    // Get all unused balances
    const unusedBalances = await getUnusedBalances(staffId);
    let selectedBalances: StaffBalanceDocument[] = [];

    switch (strategy) {
      case 'USE_ALL_BALANCES':
        selectedBalances = unusedBalances;
        break;

      case 'USE_SPECIFIC_BALANCES':
        if (!options.specificBalanceIds || options.specificBalanceIds.length === 0) {
          throw new Error('specificBalanceIds required for USE_SPECIFIC_BALANCES strategy');
        }
        selectedBalances = unusedBalances.filter(b =>
          options.specificBalanceIds!.includes(b._id)
        );
        break;

      case 'USE_OLDEST_FIRST':
        selectedBalances = [...unusedBalances].sort((a, b) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );
        break;

      case 'USE_NEWEST_FIRST':
        selectedBalances = [...unusedBalances].sort((a, b) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        break;

      case 'MAXIMIZE_NET_PAYMENT':
        // Use all bonuses, minimize deductions and advances
        selectedBalances = unusedBalances.filter(b => {
          if (b.balanceType === 'BONUS') return true;
          if (b.balanceType === 'DEDUCTION' && options.maxDeductionAmount) {
            return b.amount <= options.maxDeductionAmount;
          }
          if (b.balanceType === 'ADVANCE' && options.maxAdvanceAmount) {
            return b.amount <= options.maxAdvanceAmount;
          }
          return false;
        });
        break;

      case 'MINIMIZE_DEDUCTIONS':
        // Use bonuses and minimal deductions/advances
        const bonuses = unusedBalances.filter(b => b.balanceType === 'BONUS');
        const deductions = unusedBalances
          .filter(b => b.balanceType === 'DEDUCTION')
          .sort((a, b) => a.amount - b.amount); // Smallest first
        const advances = unusedBalances
          .filter(b => b.balanceType === 'ADVANCE')
          .sort((a, b) => a.amount - b.amount); // Smallest first

        selectedBalances = [...bonuses];

        // Add minimal deductions if needed
        if (options.maxDeductionAmount) {
          let deductionTotal = 0;
          for (const deduction of deductions) {
            if (deductionTotal + deduction.amount <= options.maxDeductionAmount) {
              selectedBalances.push(deduction);
              deductionTotal += deduction.amount;
            }
          }
        }

        // Add minimal advances if needed
        if (options.maxAdvanceAmount) {
          let advanceTotal = 0;
          for (const advance of advances) {
            if (advanceTotal + advance.amount <= options.maxAdvanceAmount) {
              selectedBalances.push(advance);
              advanceTotal += advance.amount;
            }
          }
        }
        break;

      default:
        throw new Error(`Unknown payment strategy: ${strategy}`);
    }

    // Calculate amounts from selected balances
    let bonusAmount = 0;
    let deductionAmount = 0;
    let advanceAmount = 0;

    selectedBalances.forEach(balance => {
      // Validate balance amounts to prevent negative values
      const safeAmount = Math.max(0, balance.amount || 0);
      
      switch (balance.balanceType) {
        case 'BONUS':
          bonusAmount += safeAmount;
          break;
        case 'DEDUCTION':
          deductionAmount += safeAmount;
          break;
        case 'ADVANCE':
          advanceAmount += safeAmount;
          break;
      }
    });

    // VULNERABILITY FIX: Add comprehensive validation for payment calculations
    const grossAmount = Math.max(0, baseSalary + bonusAmount);
    const totalDeductions = Math.max(0, deductionAmount + advanceAmount);
    
    // Ensure advances don't exceed what can be repaid
    const maxAllowableAdvance = Math.max(0, grossAmount - deductionAmount);
    const safeAdvanceAmount = Math.min(advanceAmount, maxAllowableAdvance);
    const safeTotalDeductions = deductionAmount + safeAdvanceAmount;
    
    const netAmount = Math.max(0, grossAmount - safeTotalDeductions);
    
    // SECURITY CHECK: Validate final calculation
    if (netAmount < 0) {
      throw new Error('Payment calculation resulted in negative net amount');
    }
    if (safeTotalDeductions > grossAmount + 0.01) { // Allow for floating point precision
      throw new Error('Total deductions exceed gross amount - invalid payment calculation');
    }

    const calculation: PaymentCalculation = {
      baseSalary,
      bonusAmount,
      deductionAmount,
      advanceAmount: safeAdvanceAmount, // Use the validated advance amount
      grossAmount,
      totalDeductions: safeTotalDeductions, // Use the validated total deductions
      netAmount
    };

    const usedBalanceIds = selectedBalances.map(b => b._id);
    const unusedBalanceIds = unusedBalances
      .filter(b => !usedBalanceIds.includes(b._id))
      .map(b => b._id);

    console.log(`🧮 Payment calculation with ${strategy}:`, calculation);
    return {
      calculation,
      usedBalanceIds,
      unusedBalanceIds,
      strategy
    };
  } catch (error) {
    console.error(`❌ Error calculating payment with strategy:`, error);
    throw error;
  }
}

/**
 * Preview payment without creating snapshot
 */
export async function previewPayment(
  staffId: string,
  baseSalary: number,
  strategy: PaymentStrategy = 'USE_ALL_BALANCES',
  options: {
    specificBalanceIds?: string[];
    maxDeductionAmount?: number;
    maxAdvanceAmount?: number;
  } = {}
): Promise<{
  calculation: PaymentCalculation;
  balanceBreakdown: {
    bonuses: StaffBalanceDocument[];
    deductions: StaffBalanceDocument[];
    advances: StaffBalanceDocument[];
  };
  strategy: PaymentStrategy;
  willUseBalanceIds: string[];
  willRemainBalanceIds: string[];
}> {
  try {
    console.log(`👁️ Previewing payment for staff ${staffId}`);

    const result = await calculatePaymentWithStrategy(staffId, baseSalary, strategy, options);

    // Get detailed breakdown of balances that will be used
    const unusedBalances = await getUnusedBalances(staffId);
    const balancesToUse = unusedBalances.filter(b =>
      result.usedBalanceIds.includes(b._id)
    );

    const balanceBreakdown = {
      bonuses: balancesToUse.filter(b => b.balanceType === 'BONUS'),
      deductions: balancesToUse.filter(b => b.balanceType === 'DEDUCTION'),
      advances: balancesToUse.filter(b => b.balanceType === 'ADVANCE')
    };

    return {
      calculation: result.calculation,
      balanceBreakdown,
      strategy: result.strategy,
      willUseBalanceIds: result.usedBalanceIds,
      willRemainBalanceIds: result.unusedBalanceIds
    };
  } catch (error) {
    console.error(`❌ Error previewing payment:`, error);
    throw error;
  }
}

// ===== PAYMENT ANALYTICS SYSTEM =====

/**
 * Get payment analytics for a staff member
 */
export async function getPaymentAnalytics(
  staffId: string,
  dateRange?: { startDate: string; endDate: string }
): Promise<{
  totalPayments: number;
  totalNetPaid: number;
  totalBaseSalary: number;
  totalBonuses: number;
  totalDeductions: number;
  totalAdvances: number;
  averageNetPayment: number;
  paymentFrequency: number; // payments per month
  largestPayment: PaymentSnapshotDocument | null;
  smallestPayment: PaymentSnapshotDocument | null;
  monthlyBreakdown: Array<{
    month: string;
    payments: number;
    totalNet: number;
    avgNet: number;
  }>;
}> {
  try {
    console.log(`📊 Getting payment analytics for staff ${staffId}`);

    // Ensure database is properly initialized
    await ensureDatabaseReady();

    let snapshots: PaymentSnapshotDocument[];

    if (dateRange) {
      const databaseV4 = await getDatabase();

      const result = await databaseV4.findDocs({
        selector: {
          type: 'payment_snapshot',
          staffId: staffId,
          paymentDate: {
            $gte: dateRange.startDate,
            $lte: dateRange.endDate
          }
        },
        sort: [{ type: 'asc' }, { staffId: 'asc' }, { paymentDate: 'desc' }]
      });
      snapshots = result.docs as PaymentSnapshotDocument[];
    } else {
      snapshots = await getPaymentHistory(staffId);
    }

    if (snapshots.length === 0) {
      return {
        totalPayments: 0,
        totalNetPaid: 0,
        totalBaseSalary: 0,
        totalBonuses: 0,
        totalDeductions: 0,
        totalAdvances: 0,
        averageNetPayment: 0,
        paymentFrequency: 0,
        largestPayment: null,
        smallestPayment: null,
        monthlyBreakdown: []
      };
    }

    // Calculate totals
    const totalPayments = snapshots.length;
    const totalNetPaid = snapshots.reduce((sum, s) => sum + s.netAmount, 0);
    const totalBaseSalary = snapshots.reduce((sum, s) => sum + s.baseSalary, 0);
    const totalBonuses = snapshots.reduce((sum, s) => sum + s.bonusAmount, 0);
    const totalDeductions = snapshots.reduce((sum, s) => sum + s.deductionAmount, 0);
    const totalAdvances = snapshots.reduce((sum, s) => sum + s.advanceAmount, 0);
    const averageNetPayment = totalNetPaid / totalPayments;

    // Calculate payment frequency (payments per month)
    const firstPayment = new Date(snapshots[snapshots.length - 1].paymentDate);
    const lastPayment = new Date(snapshots[0].paymentDate);
    const monthsDiff = (lastPayment.getTime() - firstPayment.getTime()) / (1000 * 60 * 60 * 24 * 30);
    const paymentFrequency = monthsDiff > 0 ? totalPayments / monthsDiff : totalPayments;

    // Find largest and smallest payments
    const largestPayment = snapshots.reduce((max, s) =>
      s.netAmount > max.netAmount ? s : max
    );
    const smallestPayment = snapshots.reduce((min, s) =>
      s.netAmount < min.netAmount ? s : min
    );

    // Monthly breakdown
    const monthlyMap = new Map<string, { payments: number; totalNet: number }>();

    snapshots.forEach(snapshot => {
      const date = new Date(snapshot.paymentDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!monthlyMap.has(monthKey)) {
        monthlyMap.set(monthKey, { payments: 0, totalNet: 0 });
      }

      const monthData = monthlyMap.get(monthKey)!;
      monthData.payments += 1;
      monthData.totalNet += snapshot.netAmount;
    });

    const monthlyBreakdown = Array.from(monthlyMap.entries())
      .map(([month, data]) => ({
        month,
        payments: data.payments,
        totalNet: data.totalNet,
        avgNet: data.totalNet / data.payments
      }))
      .sort((a, b) => a.month.localeCompare(b.month));

    const analytics = {
      totalPayments,
      totalNetPaid,
      totalBaseSalary,
      totalBonuses,
      totalDeductions,
      totalAdvances,
      averageNetPayment,
      paymentFrequency,
      largestPayment,
      smallestPayment,
      monthlyBreakdown
    };

    console.log(`📊 Payment analytics calculated:`, analytics);
    return analytics;
  } catch (error) {
    console.error(`❌ Error getting payment analytics:`, error);
    throw error;
  }
}

/**
 * Compare payment analytics between two staff members
 */
export async function compareStaffPayments(
  staffId1: string,
  staffId2: string,
  dateRange?: { startDate: string; endDate: string }
): Promise<{
  staff1: Promise<{
    totalPayments: number;
    totalNetPaid: number;
    totalBaseSalary: number;
    totalBonuses: number;
    totalDeductions: number;
    totalAdvances: number;
    averageNetPayment: number;
    paymentFrequency: number;
    largestPayment: PaymentSnapshotDocument | null;
    smallestPayment: PaymentSnapshotDocument | null;
    monthlyBreakdown: Array<{
      month: string;
      payments: number;
      totalNet: number;
      avgNet: number;
    }>;
  }>;
  staff2: Promise<{
    totalPayments: number;
    totalNetPaid: number;
    totalBaseSalary: number;
    totalBonuses: number;
    totalDeductions: number;
    totalAdvances: number;
    averageNetPayment: number;
    paymentFrequency: number;
    largestPayment: PaymentSnapshotDocument | null;
    smallestPayment: PaymentSnapshotDocument | null;
    monthlyBreakdown: Array<{
      month: string;
      payments: number;
      totalNet: number;
      avgNet: number;
    }>;
  }>;
  comparison: {
    netPaymentDifference: number;
    averagePaymentDifference: number;
    paymentFrequencyDifference: number;
    bonusDifference: number;
    deductionDifference: number;
  };
}> {
  try {
    console.log(`📊 Comparing payments between staff ${staffId1} and ${staffId2}`);

    const [analytics1, analytics2] = await Promise.all([
      getPaymentAnalytics(staffId1, dateRange),
      getPaymentAnalytics(staffId2, dateRange)
    ]);

    const comparison = {
      netPaymentDifference: analytics1.totalNetPaid - analytics2.totalNetPaid,
      averagePaymentDifference: analytics1.averageNetPayment - analytics2.averageNetPayment,
      paymentFrequencyDifference: analytics1.paymentFrequency - analytics2.paymentFrequency,
      bonusDifference: analytics1.totalBonuses - analytics2.totalBonuses,
      deductionDifference: analytics1.totalDeductions - analytics2.totalDeductions
    };

    return {
      staff1: Promise.resolve(analytics1),
      staff2: Promise.resolve(analytics2),
      comparison
    };
  } catch (error) {
    console.error(`❌ Error comparing staff payments:`, error);
    throw error;
  }
}
