/**
 * MongoDB Constants
 * 
 * This file contains server-side constants for MongoDB access
 * Used for staff authentication and management
 */

import { MongoClient } from 'mongodb';

// MongoDB connection URL from environment
export const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';

// Database name conventions
export const USERS_COLLECTION = 'users';
export const RESTAURANTS_COLLECTION = 'restaurants';

/**
 * Clean restaurant ID to ensure it's safe for database usage
 */
export function serverCleanRestaurantId(restaurantId: string): string {
  return restaurantId.replace(/[^a-zA-Z0-9-_]/g, '_').toLowerCase();
}

/**
 * Get the restaurant database name based on the restaurant ID
 */
export function getRestaurantDbName(restaurantId: string): string {
  return `resto_${restaurantId}`;
}

/**
 * Get MongoDB client promise
 */
export async function getMongoClient(): Promise<MongoClient> {
  const client = new MongoClient(MONGODB_URI);
  return client.connect();
}

/**
 * Ensure that a collection exists in the MongoDB database
 */
export async function ensureCollection(databaseName: string, collectionName: string): Promise<void> {
  const client = await getMongoClient();
  try {
    const db = client.db(databaseName);
    const collections = await db.listCollections({ name: collectionName }).toArray();
    
    if (collections.length === 0) {
      await db.createCollection(collectionName);
    }
  } finally {
    await client.close();
  }
}

/**
 * Initialize MongoDB staff collection for a restaurant
 */
export async function ensureStaffCollection(restaurantId: string): Promise<void> {
  const cleanedRestaurantId = serverCleanRestaurantId(restaurantId);
  const dbName = getRestaurantDbName(cleanedRestaurantId);
  await ensureCollection(dbName, 'staff');
} 