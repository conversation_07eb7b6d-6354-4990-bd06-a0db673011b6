/**
 * Staff Creator Module
 * 
 * This module provides functions for creating and managing staff members,
 * including adding authentication credentials to existing staff members.
 */

import { v4 as uuidv4 } from 'uuid';
import { getStaffMember, updateStaffMember } from '@/lib/db/v4';
import { StaffDocument } from '@/lib/db/v4/schemas/per-staff-schemas';
import { createMongoUser } from '@/lib/auth/mongo-auth-ops';
import { cleanRestaurantId } from '@/lib/db/db-utils';

/**
 * Adds authentication credentials to an existing staff member
 * 
 * This function:
 * 1. Retrieves the staff member from PouchDB
 * 2. Creates a MongoDB auth user with the same ID
 * 3. Updates the staff member in PouchDB with auth info
 * 
 * @param staffId The ID of the existing staff member
 * @param authData Authentication credentials (username, password)
 * @param restaurantId The restaurant ID
 * @returns The updated staff member
 */
export async function addAuthToStaff(
  staffId: string,
  authData: {
    username: string;
    password: string;
  },
  restaurantId: string
): Promise<StaffDocument> {
  try {
    console.log(`StaffCreator: Adding auth to staff member ${staffId} in restaurant ${restaurantId}`);
    
    // Clean the restaurant ID
    const cleanedRestaurantId = cleanRestaurantId(restaurantId);
    console.log(`StaffCreator: Cleaned restaurant ID: ${cleanedRestaurantId}`);
    
    // Get the staff member from PouchDB
    const staffMember = await getStaffMember(staffId);
    
    if (!staffMember) {
      console.error(`StaffCreator: Staff member with ID ${staffId} not found`);
      throw new Error(`Staff member with ID ${staffId} not found`);
    }
    
    console.log(`StaffCreator: Retrieved staff member:`, {
      id: staffMember.id,
      name: staffMember.name,
      role: staffMember.role,
      hasUserAccount: staffMember.hasUserAccount
    });
    
    // Check if the staff member already has an auth account
    if (staffMember.hasUserAccount && staffMember.userId) {
      console.error(`StaffCreator: Staff member ${staffId} already has an auth account`);
      throw new Error(`Staff member already has an auth account`);
    }
    
    // Create the MongoDB auth user
    console.log(`StaffCreator: Creating MongoDB auth user for staff ${staffId}`);
    
    // Normalize role to a valid AuthUser role
    let normalizedRole: 'owner' | 'admin' | 'manager' | 'staff' | 'user' = 'staff';
    
    if (staffMember.role) {
      const lowerRole = staffMember.role.toLowerCase();
      if (lowerRole === 'owner' || lowerRole === 'admin' || lowerRole === 'manager' || lowerRole === 'user') {
        normalizedRole = lowerRole as 'owner' | 'admin' | 'manager' | 'user';
      } else {
        // Map other roles to 'staff'
        normalizedRole = 'staff';
      }
    }
    
    console.log(`StaffCreator: Normalized role from '${staffMember.role}' to '${normalizedRole}'`);
    
    // Create the MongoDB user with the same ID as the staff member
    const mongoUserResult = await createMongoUser({
      name: staffMember.name || '',
      username: authData.username,
      plaintextPassword: authData.password,
      role: normalizedRole,
      restaurantId: cleanedRestaurantId,
      staffIdToLink: staffId, // Use the same ID for both documents
      email: staffMember.email || '',
      metadata: {
        originalRole: staffMember.role,
        staffId: staffId
      }
    });
    
    if (!mongoUserResult.success || !mongoUserResult.userId) {
      console.error(`StaffCreator: Failed to create MongoDB auth user:`, mongoUserResult.error);
      throw new Error(mongoUserResult.error || 'Failed to create auth user');
    }
    
    console.log(`StaffCreator: MongoDB auth user created successfully with ID ${mongoUserResult.userId}`);
    
    // Update the staff member in PouchDB with auth info
    const updatedStaffMember = await updateStaffMember(staffId, {
      hasUserAccount: true,
      userId: mongoUserResult.userId,
      username: authData.username
    });
    
    console.log(`StaffCreator: Staff member updated with auth info:`, {
      id: updatedStaffMember.id,
      hasUserAccount: updatedStaffMember.hasUserAccount,
      userId: updatedStaffMember.userId,
      username: updatedStaffMember.username
    });
    
    return updatedStaffMember;
  } catch (error) {
    console.error(`StaffCreator: Error adding auth to staff:`, error);
    throw error;
  }
}
