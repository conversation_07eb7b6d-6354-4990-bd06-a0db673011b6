// CouchDB authentication configuration
// Using environment variables for production security
export const getCouchDBCredentials = () => {
  return {
    username: process.env.COUCHDB_ADMIN_USER || 'admin',
    password: process.env.COUCHDB_ADMIN_PASSWORD || 'admin'
  };
};

export const getCouchDBUrl = (ip: string, port: number, dbName?: string) => {
  const creds = getCouchDBCredentials();
  const baseUrl = `http://${creds.username}:${creds.password}@${ip}:${port}`;
  return dbName ? `${baseUrl}/${dbName}` : baseUrl;
};